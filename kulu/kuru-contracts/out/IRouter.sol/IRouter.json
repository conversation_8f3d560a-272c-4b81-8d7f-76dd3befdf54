{"abi": [{"type": "function", "name": "anyToAnySwap", "inputs": [{"name": "_marketAddresses", "type": "address[]", "internalType": "address[]"}, {"name": "_isBuy", "type": "bool[]", "internalType": "bool[]"}, {"name": "_nativeSend", "type": "bool[]", "internalType": "bool[]"}, {"name": "_debitToken", "type": "address", "internalType": "address"}, {"name": "_creditToken", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "deployProxy", "inputs": [{"name": "_type", "type": "uint8", "internalType": "enum IOrderBook.OrderBookType"}, {"name": "_baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "_sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "_pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "_tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "_minSize", "type": "uint96", "internalType": "uint96"}, {"name": "_maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "_takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_makerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "KuruRouterSwap", "inputs": [{"name": "msgSender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "debitToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "creditToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MarketRegistered", "inputs": [{"name": "baseAsset", "type": "address", "indexed": false, "internalType": "address"}, {"name": "quoteAsset", "type": "address", "indexed": false, "internalType": "address"}, {"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": false, "internalType": "address"}, {"name": "pricePrecision", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "sizePrecision", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "tickSize", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "minSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "maxSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "takerFeeBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "makerFeeBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "kuruAmmSpread", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "OBImplementationUpdated", "inputs": [{"name": "previousImplementation", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newImplementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "VaultImplementationUpdated", "inputs": [{"name": "previousImplementation", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newImplementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"anyToAnySwap(address[],bool[],bool[],address,address,uint256,uint256)": "ffa5210a", "deployProxy(uint8,address,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,uint96)": "ce186ec3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"debitToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"creditToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"KuruRouterSwap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"quoteAsset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"vaultAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"pricePrecision\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"sizePrecision\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"tickSize\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"minSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"maxSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"takerFeeBps\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"makerFeeBps\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"kuruAmmSpread\",\"type\":\"uint96\"}],\"name\":\"MarketRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousImplementation\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"OBImplementationUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousImplementation\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"VaultImplementationUpdated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_marketAddresses\",\"type\":\"address[]\"},{\"internalType\":\"bool[]\",\"name\":\"_isBuy\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"_nativeSend\",\"type\":\"bool[]\"},{\"internalType\":\"address\",\"name\":\"_debitToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_creditToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"}],\"name\":\"anyToAnySwap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum IOrderBook.OrderBookType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"_baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"_pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_makerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"}],\"name\":\"deployProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IRouter.sol\":\"IRouter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/interfaces/IRouter.sol\":{\"keccak256\":\"0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340\",\"dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": false}, {"internalType": "address", "name": "debitToken", "type": "address", "indexed": false}, {"internalType": "address", "name": "creditToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "KuruRouterSwap", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "baseAsset", "type": "address", "indexed": false}, {"internalType": "address", "name": "quoteAsset", "type": "address", "indexed": false}, {"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "pricePrecision", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "sizePrecision", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "tickSize", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "minSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "maxSize", "type": "uint96", "indexed": false}, {"internalType": "uint256", "name": "takerFeeBps", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "makerFeeBps", "type": "uint256", "indexed": false}, {"internalType": "uint96", "name": "kuruAmmSpread", "type": "uint96", "indexed": false}], "type": "event", "name": "MarketRegistered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousImplementation", "type": "address", "indexed": false}, {"internalType": "address", "name": "newImplementation", "type": "address", "indexed": false}], "type": "event", "name": "OBImplementationUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousImplementation", "type": "address", "indexed": false}, {"internalType": "address", "name": "newImplementation", "type": "address", "indexed": false}], "type": "event", "name": "VaultImplementationUpdated", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "_marketAddresses", "type": "address[]"}, {"internalType": "bool[]", "name": "_isBuy", "type": "bool[]"}, {"internalType": "bool[]", "name": "_nativeSend", "type": "bool[]"}, {"internalType": "address", "name": "_debitToken", "type": "address"}, {"internalType": "address", "name": "_creditToken", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "anyToAnySwap", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "enum IOrderBook.OrderBookType", "name": "_type", "type": "uint8"}, {"internalType": "address", "name": "_baseAssetAddress", "type": "address"}, {"internalType": "address", "name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint96", "name": "_sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "_pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "_tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "_minSize", "type": "uint96"}, {"internalType": "uint96", "name": "_maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "_takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "_makerFeeBps", "type": "uint256"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "deployProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IRouter.sol": "<PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IRouter.sol": {"keccak256": "0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702", "urls": ["bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340", "dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 9}