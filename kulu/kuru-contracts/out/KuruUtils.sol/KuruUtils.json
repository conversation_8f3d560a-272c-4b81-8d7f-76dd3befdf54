{"abi": [{"type": "function", "name": "calculatePriceOverRoute", "inputs": [{"name": "route", "type": "address[]", "internalType": "address[]"}, {"name": "isBuy", "type": "bool[]", "internalType": "bool[]"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMarginBalances", "inputs": [{"name": "marginA<PERSON>unt<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "users", "type": "address[]", "internalType": "address[]"}, {"name": "tokens", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTokensInfo", "inputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}, {"name": "holder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct KuruUtils.TokenInfo[]", "components": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint8", "internalType": "uint8"}, {"name": "totalSupply", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "324:2639:17:-:0;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "324:2639:17:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;324:2639:17;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;324:2639:17;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;1743:9;;324:2639;;;1738:1195;1773:3;324:2639;;1754:17;;;;;-1:-1:-1;;;;;1830:9:17;;;;:::i;:::-;324:2639;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;2093:12:17;;324:2639;;;;;;;;2093:12;;;324:2639;;2093:12;;;1773:3;2089:101;;;1773:3;-1:-1:-1;324:2639:17;;-1:-1:-1;;;2241:14:17;;324:2639;;;;2241:14;;;324:2639;;2241:14;;;1773:3;2237:109;;;1773:3;-1:-1:-1;324:2639:17;;-1:-1:-1;;;2398:23:17;;324:2639;2398:23;;324:2639;;;2398:23;324:2639;;;2398:23;;;324:2639;;2398:23;;;1773:3;2394:115;;;1773:3;-1:-1:-1;324:2639:17;;-1:-1:-1;;;2562:16:17;;324:2639;;;;;;;;2562:16;;;;324:2639;2562:16;;;1773:3;2558:109;324:2639;2558:109;;;1773:3;-1:-1:-1;324:2639:17;;-1:-1:-1;;;2724:19:17;;324:2639;;;;;2724:19;;;324:2639;2724:19;;;1773:3;324:2639;2720:123;;;;1773:3;324:2639;;;;;;;;;;:::i;:::-;;;2867:55;;;324:2639;;2867:55;;324:2639;;;2867:55;;324:2639;;2867:55;;324:2639;2857:65;;;;:::i;:::-;;;;;;:::i;:::-;;324:2639;1743:9;;2720:123;;-1:-1:-1;324:2639:17;2720:123;;2724:19;;;;;;;;;;;;;;;;:::i;:::-;;;324:2639;;;;;;;2724:19;;;324:2639;;;;2724:19;;;;;2558:109;2623:20;-1:-1:-1;2558:109:17;;;2562:16;;;;;;;;;;;;;;;;;:::i;:::-;;;324:2639;;;;;;;;;;;;;2562:16;;324:2639;2562:16;;;;;;;2394:115;2467:18;;2394:115;;;2398:23;;;;;;;;;;;;;;;;;:::i;:::-;;;324:2639;;;;;2398:23;;;;;;;;;2237:109;2306:16;-1:-1:-1;2237:109:17;;;2241:14;;;;;;;324:2639;2241:14;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;2089:101;2154:12;-1:-1:-1;2089:101:17;;;2093:12;;;;;;;324:2639;2093:12;;;;;;:::i;:::-;;;;;1754:17;324:2639;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1754:17;;324:2639;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;324:2639:17;;;;;;-1:-1:-1;;;;;324:2639:17;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;324:2639:17;;;;;1412:16;;;;;;324:2639;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;324:2639:17;;;;;;;;;;;;;1430:3;1488:8;;;;;;:::i;:::-;;:::i;:::-;1498:9;;;;;;;:::i;:::-;324:2639;;-1:-1:-1;;;1463:45:17;;-1:-1:-1;;;;;324:2639:17;;;;1463:45;;324:2639;;;;;;;;;;;1463:45;;;;;;;324:2639;1463:45;;;1430:3;324:2639;1449:59;;;;;;:::i;:::-;324:2639;;1397:13;;1463:45;;324:2639;1463:45;;;;;;;;;324:2639;1463:45;;;:::i;:::-;;;324:2639;;;;;;;1463:45;;;;;-1:-1:-1;1463:45:17;;;324:2639;;;;;;;;;;;;;;;-1:-1:-1;;324:2639:17;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;621:24;;;637:8;660:13;324:2639;655:374;693:3;324:2639;;675:16;;;;;716:8;;;;:::i;:::-;324:2639;;716:8;;324:2639;;;-1:-1:-1;;;;;778:8:17;;;;:::i;:::-;324:2639;;;;;;;;;;;767:33;;;;;;;;;324:2639;767:33;;;712:307;827:16;324:2639;;;;;;;;;;;;;;637:8;324:2639;;;712:307;;324:2639;660:13;;;324:2639;;;;;;;;;;;;767:33;;;;;324:2639;767:33;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;712:307;324:2639;;-1:-1:-1;;;;;927:8:17;;;;:::i;:::-;324:2639;;;;;;;;;;;916:33;;;;;;;;;324:2639;916:33;;;712:307;324:2639;637:8;324:2639;;;;;;637:8;324:2639;;;;;;;;;;;;;;712:307;;;324:2639;;;;;;;;;;;;916:33;;;;324:2639;916:33;;;;;;;;;:::i;:::-;;;;;675:16;324:2639;675:16;324:2639;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;324:2639:17;;;;;-1:-1:-1;324:2639:17;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;324:2639:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;324:2639:17;;;;;;;;-1:-1:-1;;324:2639:17;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;:::o;:::-;;-1:-1:-1;;;;;324:2639:17;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;324:2639:17;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;324:2639:17;;;;;;;;;;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"calculatePriceOverRoute(address[],bool[])": "0964d85b", "getMarginBalances(address,address[],address[])": "62c402ab", "getTokensInfo(address[],address)": "f7f04a8a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"route\",\"type\":\"address[]\"},{\"internalType\":\"bool[]\",\"name\":\"isBuy\",\"type\":\"bool[]\"}],\"name\":\"calculatePriceOverRoute\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"marginAccountAddress\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"users\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"tokens\",\"type\":\"address[]\"}],\"name\":\"getMarginBalances\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"tokens\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"}],\"name\":\"getTokensInfo\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"decimals\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"totalSupply\",\"type\":\"uint256\"}],\"internalType\":\"struct KuruUtils.TokenInfo[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"title\":\"A periphery contract for Kuru\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/periphery/KuruUtils.sol\":\"KuruUtils\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/periphery/KuruUtils.sol\":{\"keccak256\":\"0xbfab46b5f1454fff4935cc2f094c6aaeaf96a756cb1369de17280e634c22eb4a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://66444242a0f485dd8a5df99f58cab08f4eb2a4288455d7218ac78f1fb0514864\",\"dweb:/ipfs/QmQfXChv3T6Ao5wu8p39Pz3izos5s2w8ZgWwy61QrmbWra\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address[]", "name": "route", "type": "address[]"}, {"internalType": "bool[]", "name": "isBuy", "type": "bool[]"}], "stateMutability": "view", "type": "function", "name": "calculatePriceOverRoute", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "marginA<PERSON>unt<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address[]", "name": "users", "type": "address[]"}, {"internalType": "address[]", "name": "tokens", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getMarginBalances", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "address", "name": "holder", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getTokensInfo", "outputs": [{"internalType": "struct KuruUtils.TokenInfo[]", "name": "", "type": "tuple[]", "components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/periphery/KuruUtils.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/periphery/KuruUtils.sol": {"keccak256": "0xbfab46b5f1454fff4935cc2f094c6aaeaf96a756cb1369de17280e634c22eb4a", "urls": ["bzz-raw://66444242a0f485dd8a5df99f58cab08f4eb2a4288455d7218ac78f1fb0514864", "dweb:/ipfs/QmQfXChv3T6Ao5wu8p39Pz3izos5s2w8ZgWwy61QrmbWra"], "license": "BUSL-1.1"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}}, "version": 1}, "id": 17}