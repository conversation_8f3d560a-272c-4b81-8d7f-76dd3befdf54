{"abi": [{"type": "error", "name": "DivFailed", "inputs": []}, {"type": "error", "name": "DivWadFailed", "inputs": []}, {"type": "error", "name": "ExpOverflow", "inputs": []}, {"type": "error", "name": "FactorialOverflow", "inputs": []}, {"type": "error", "name": "FullMulDivFailed", "inputs": []}, {"type": "error", "name": "LnWadUndefined", "inputs": []}, {"type": "error", "name": "MantissaOverflow", "inputs": []}, {"type": "error", "name": "MulDivFailed", "inputs": []}, {"type": "error", "name": "MulWadFailed", "inputs": []}, {"type": "error", "name": "OutOfDomain", "inputs": []}, {"type": "error", "name": "RPowOverflow", "inputs": []}, {"type": "error", "name": "SDivWadFailed", "inputs": []}, {"type": "error", "name": "SMulWadFailed", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220d821ff3858f7cb142fe1ae2bca6e20770fd4524b3eef70c8890753a8a8ea273364736f6c634300081c0033", "sourceMap": "350:50376:13:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220d821ff3858f7cb142fe1ae2bca6e20770fd4524b3eef70c8890753a8a8ea273364736f6c634300081c0033", "sourceMap": "350:50376:13:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"DivFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DivWadFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FactorialOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FullMulDivFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LnWadUndefined\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MantissaOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MulDivFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MulWadFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OutOfDomain\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RPowOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SDivWadFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SMulWadFailed\",\"type\":\"error\"}],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/utils/FixedPointMathLib.sol)Modified from Solmate (https://github.com/transmissions11/solmate/blob/main/src/utils/FixedPointMathLib.sol)\",\"errors\":{\"DivFailed()\":[{\"details\":\"The division failed, as the denominator is zero.\"}],\"DivWadFailed()\":[{\"details\":\"The operation failed, either due to a multiplication overflow, or a division by a zero.\"}],\"ExpOverflow()\":[{\"details\":\"The operation failed, as the output exceeds the maximum value of uint256.\"}],\"FactorialOverflow()\":[{\"details\":\"The operation failed, as the output exceeds the maximum value of uint256.\"}],\"FullMulDivFailed()\":[{\"details\":\"The full precision multiply-divide operation failed, either due to the result being larger than 256 bits, or a division by a zero.\"}],\"LnWadUndefined()\":[{\"details\":\"The output is undefined, as the input is less-than-or-equal to zero.\"}],\"MantissaOverflow()\":[{\"details\":\"The mantissa is too big to fit.\"}],\"MulDivFailed()\":[{\"details\":\"The operation failed, either due to a multiplication overflow, or a division by a zero.\"}],\"MulWadFailed()\":[{\"details\":\"The operation failed, due to an multiplication overflow.\"}],\"OutOfDomain()\":[{\"details\":\"The input outside the acceptable domain.\"}],\"RPowOverflow()\":[{\"details\":\"The operation failed, due to an overflow.\"}],\"SDivWadFailed()\":[{\"details\":\"The operation failed, either due to a multiplication overflow, or a division by a zero.\"}],\"SMulWadFailed()\":[{\"details\":\"The operation failed, due to an multiplication overflow.\"}]},\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"WAD\":{\"details\":\"The scalar of ETH and most ERC20s.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Arithmetic library with operations for fixed-point numbers.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/FixedPointMathLib.sol\":\"FixedPointMathLib\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "DivFailed"}, {"inputs": [], "type": "error", "name": "DivWadFailed"}, {"inputs": [], "type": "error", "name": "ExpOverflow"}, {"inputs": [], "type": "error", "name": "FactorialOverflow"}, {"inputs": [], "type": "error", "name": "FullMulDivFailed"}, {"inputs": [], "type": "error", "name": "LnWadUndefined"}, {"inputs": [], "type": "error", "name": "MantissaOverflow"}, {"inputs": [], "type": "error", "name": "MulDivFailed"}, {"inputs": [], "type": "error", "name": "MulWadFailed"}, {"inputs": [], "type": "error", "name": "OutOfDomain"}, {"inputs": [], "type": "error", "name": "RPowOverflow"}, {"inputs": [], "type": "error", "name": "SDivWadFailed"}, {"inputs": [], "type": "error", "name": "SMulWadFailed"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/FixedPointMathLib.sol": "FixedPointMathLib"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}}, "version": 1}, "id": 13}