{"abi": [{"type": "function", "name": "deposit", "inputs": [{"name": "amount1", "type": "uint256", "internalType": "uint256"}, {"name": "amount2", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "token1_", "type": "address", "internalType": "address"}, {"name": "token2_", "type": "address", "internalType": "address"}, {"name": "_marginAccount", "type": "address", "internalType": "address"}, {"name": "_market", "type": "address", "internalType": "address"}, {"name": "_spread", "type": "uint96", "internalType": "uint96"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketParams", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "KuruVaultDeposit", "inputs": [{"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount2", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "userAddress", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "KuruVaultWithdraw", "inputs": [{"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount2", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "userAddress", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"deposit(uint256,uint256,address)": "8dbdbe6d", "initialize(address,address,address,address,address,uint96)": "d246ce16", "setMarketParams()": "7660b153"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount2\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"userAddress\",\"type\":\"address\"}],\"name\":\"KuruVaultDeposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount2\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"userAddress\",\"type\":\"address\"}],\"name\":\"KuruVaultWithdraw\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount2\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token2_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_marginAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_spread\",\"type\":\"uint96\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setMarketParams\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IKuruAMMVault.sol\":\"IKuruAMMVault\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IKuruAMMVault.sol\":{\"keccak256\":\"0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460\",\"dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "amount1", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount2", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "userAddress", "type": "address", "indexed": false}], "type": "event", "name": "KuruVaultDeposit", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount1", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount2", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "userAddress", "type": "address", "indexed": false}], "type": "event", "name": "KuruVaultWithdraw", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount1", "type": "uint256"}, {"internalType": "uint256", "name": "amount2", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "token1_", "type": "address"}, {"internalType": "address", "name": "token2_", "type": "address"}, {"internalType": "address", "name": "_marginAccount", "type": "address"}, {"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "uint96", "name": "_spread", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setMarketParams"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IKuruAMMVault.sol": "IKuruAMMVault"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/interfaces/IKuruAMMVault.sol": {"keccak256": "0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7", "urls": ["bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460", "dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 6}