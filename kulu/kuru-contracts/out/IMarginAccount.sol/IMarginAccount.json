{"abi": [{"type": "function", "name": "creditFee", "inputs": [{"name": "_assetA", "type": "address", "internalType": "address"}, {"name": "_feeA", "type": "uint256", "internalType": "uint256"}, {"name": "_assetB", "type": "address", "internalType": "address"}, {"name": "_feeB", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "creditUser", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_useMargin", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "creditUsersEncoded", "inputs": [{"name": "_encodedData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "debitUser", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "getBalance", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateMarkets", "inputs": [{"name": "_marketAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeeCollectorUpdated", "inputs": [{"name": "newFeeCollector", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProtocolStateUpdated", "inputs": [{"name": "newState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"creditFee(address,uint256,address,uint256)": "49f59b62", "creditUser(address,address,uint256,bool)": "75eab98e", "creditUsersEncoded(bytes)": "6f5b601e", "debitUser(address,address,uint256)": "6c139252", "deposit(address,address,uint256)": "8340f549", "getBalance(address,address)": "d4fac45d", "updateMarkets(address)": "01aef349", "withdraw(uint256,address)": "00f714ce"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newFeeCollector\",\"type\":\"address\"}],\"name\":\"FeeCollectorUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"newState\",\"type\":\"bool\"}],\"name\":\"ProtocolStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_assetA\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_feeA\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_assetB\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_feeB\",\"type\":\"uint256\"}],\"name\":\"creditFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_useMargin\",\"type\":\"bool\"}],\"name\":\"creditUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"_encodedData\",\"type\":\"bytes\"}],\"name\":\"creditUsersEncoded\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"debitUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_marketAddress\",\"type\":\"address\"}],\"name\":\"updateMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IMarginAccount.sol\":\"IMarginAccount\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newFeeCollector", "type": "address", "indexed": false}], "type": "event", "name": "FeeCollectorUpdated", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "newState", "type": "bool", "indexed": false}], "type": "event", "name": "ProtocolStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_assetA", "type": "address"}, {"internalType": "uint256", "name": "_feeA", "type": "uint256"}, {"internalType": "address", "name": "_assetB", "type": "address"}, {"internalType": "uint256", "name": "_feeB", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "creditFee"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bool", "name": "_useMargin", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "creditUser"}, {"inputs": [{"internalType": "bytes", "name": "_encodedData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "creditUsersEncoded"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "debitUser"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_marketAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "updateMarkets"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address", "name": "_token", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IMarginAccount.sol": "I<PERSON><PERSON>gin<PERSON><PERSON>unt"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 7}