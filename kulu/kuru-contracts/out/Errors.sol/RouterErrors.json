{"abi": [{"type": "error", "name": "BaseAndQuoteAssetSame", "inputs": []}, {"type": "error", "name": "InvalidMarket", "inputs": []}, {"type": "error", "name": "InvalidPricePrecision", "inputs": []}, {"type": "error", "name": "InvalidSizePrecision", "inputs": []}, {"type": "error", "name": "InvalidTickSize", "inputs": []}, {"type": "error", "name": "LengthMismatch", "inputs": []}, {"type": "error", "name": "MarketTypeMismatch", "inputs": []}, {"type": "error", "name": "NativeAssetTransferFail", "inputs": []}, {"type": "error", "name": "NoMarketsPassed", "inputs": []}, {"type": "error", "name": "SlippageExceeded", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220de8cb83dad1d9b721dd88a8ba02e1345fd11d03cfdcfe44b8b112d6b754e8ad364736f6c634300081c0033", "sourceMap": "4197:1226:12:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220de8cb83dad1d9b721dd88a8ba02e1345fd11d03cfdcfe44b8b112d6b754e8ad364736f6c634300081c0033", "sourceMap": "4197:1226:12:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"BaseAndQuoteAssetSame\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMarket\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPricePrecision\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSizePrecision\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidTickSize\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketTypeMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetTransferFail\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoMarketsPassed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SlippageExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"}],\"devdoc\":{\"errors\":{\"BaseAndQuoteAssetSame()\":[{\"details\":\"Thrown when base and quote asset addresses are the same\"}],\"InvalidMarket()\":[{\"details\":\"Thrown when the market is invalid\"}],\"InvalidPricePrecision()\":[{\"details\":\"Thrown when price precision is not a power of 10\"}],\"InvalidSizePrecision()\":[{\"details\":\"Thrown when size precision is not a power of 10\"}],\"InvalidTickSize()\":[{\"details\":\"Thrown when tick size is 0\"}],\"LengthMismatch()\":[{\"details\":\"Thrown when the length of market addresses, isBuy, and nativeSend arrays are not the same\"}],\"MarketTypeMismatch()\":[{\"details\":\"Thrown when market type given and token addresses are not compatible\"}],\"NativeAssetTransferFail()\":[{\"details\":\"Thrown when the native asset transfer fails\"}],\"NoMarketsPassed()\":[{\"details\":\"Thrown when no markets are passed as input\"}],\"SlippageExceeded()\":[{\"details\":\"Thrown when the slippage exceeds the expected value\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Errors.sol\":\"RouterErrors\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "BaseAndQuoteAssetSame"}, {"inputs": [], "type": "error", "name": "InvalidMarket"}, {"inputs": [], "type": "error", "name": "InvalidPricePrecision"}, {"inputs": [], "type": "error", "name": "InvalidSizePrecision"}, {"inputs": [], "type": "error", "name": "InvalidTickSize"}, {"inputs": [], "type": "error", "name": "LengthMismatch"}, {"inputs": [], "type": "error", "name": "MarketTypeMismatch"}, {"inputs": [], "type": "error", "name": "NativeAssetTransferFail"}, {"inputs": [], "type": "error", "name": "NoMarketsPassed"}, {"inputs": [], "type": "error", "name": "SlippageExceeded"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Errors.sol": "RouterErrors"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}}, "version": 1}, "id": 12}