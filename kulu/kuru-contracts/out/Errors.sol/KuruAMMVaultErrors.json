{"abi": [{"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityMinted", "inputs": []}, {"type": "error", "name": "InsufficientQuoteToken", "inputs": []}, {"type": "error", "name": "NativeAssetMismatch", "inputs": []}, {"type": "error", "name": "NativeAssetTransferFail", "inputs": []}, {"type": "error", "name": "NegativeAmountWithdrawn", "inputs": []}, {"type": "error", "name": "NewSizeExceedsPartiallyFilledSize", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "VaultInitializationPriceCrossesBook", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220a7ba3e53bd394a4e2cd9eecb6160a4cd629148e1ae95a4154aae9797ddd4b3b064736f6c634300081c0033", "sourceMap": "5425:1205:12:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220a7ba3e53bd394a4e2cd9eecb6160a4cd629148e1ae95a4154aae9797ddd4b3b064736f6c634300081c0033", "sourceMap": "5425:1205:12:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityMinted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientQuoteToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetTransferFail\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NegativeAmountWithdrawn\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewSizeExceedsPartiallyFilledSize\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"VaultInitializationPriceCrossesBook\",\"type\":\"error\"}],\"devdoc\":{\"errors\":{\"InsufficientBalance()\":[{\"details\":\"Thrown when balance of owner is too less\"}],\"InsufficientLiquidityMinted()\":[{\"details\":\"Thrown when insufficient liquidity is minted\"}],\"InsufficientQuoteToken()\":[{\"details\":\"Thrown when amount of quote tokens passed is insufficient\"}],\"NativeAssetMismatch()\":[{\"details\":\"Thrown when native token passed as argument and msg.value does not match\"}],\"NativeAssetTransferFail()\":[{\"details\":\"Thrown when native asset transfer fails\"}],\"NegativeAmountWithdrawn()\":[{\"details\":\"Thrown when the amounts withdrawn are negative\"}],\"NewSizeExceedsPartiallyFilledSize()\":[{\"details\":\"Thrown when new size exceeds partially filled size\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}],\"Unauthorized()\":[{\"details\":\"Thrown when a user is not the owner and tries to execute a privileged function\"}],\"VaultInitializationPriceCrossesBook()\":[{\"details\":\"Thrown when vault initialization price crosses the book\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Errors.sol\":\"KuruAMMVaultErrors\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityMinted"}, {"inputs": [], "type": "error", "name": "InsufficientQuoteToken"}, {"inputs": [], "type": "error", "name": "NativeAssetMismatch"}, {"inputs": [], "type": "error", "name": "NativeAssetTransferFail"}, {"inputs": [], "type": "error", "name": "NegativeAmountWithdrawn"}, {"inputs": [], "type": "error", "name": "NewSizeExceedsPartiallyFilledSize"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "VaultInitializationPriceCrossesBook"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Errors.sol": "KuruAMMVaultErrors"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}}, "version": 1}, "id": 12}