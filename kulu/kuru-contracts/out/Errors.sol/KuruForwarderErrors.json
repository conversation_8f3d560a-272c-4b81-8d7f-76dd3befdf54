{"abi": [{"type": "error", "name": "ExecutionFailed", "inputs": []}, {"type": "error", "name": "InsufficientValue", "inputs": []}, {"type": "error", "name": "InterfaceNotAllowed", "inputs": []}, {"type": "error", "name": "NonceAlreadyUsed", "inputs": []}, {"type": "error", "name": "SignatureMismatch", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea26469706673582212205ff6b4986d23133765e8678f164a7f42ff0627b385d7ec89577c803e2e3d9a6764736f6c634300081c0033", "sourceMap": "6632:527:12:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea26469706673582212205ff6b4986d23133765e8678f164a7f42ff0627b385d7ec89577c803e2e3d9a6764736f6c634300081c0033", "sourceMap": "6632:527:12:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ExecutionFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientValue\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InterfaceNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NonceAlreadyUsed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SignatureMismatch\",\"type\":\"error\"}],\"devdoc\":{\"errors\":{\"ExecutionFailed()\":[{\"details\":\"Thrown when the execution fails\"}],\"InsufficientValue()\":[{\"details\":\"Thrown when the value is insufficient\"}],\"InterfaceNotAllowed()\":[{\"details\":\"Thrown when the interface is not allowed\"}],\"NonceAlreadyUsed()\":[{\"details\":\"Thrown when the nonce is already used\"}],\"SignatureMismatch()\":[{\"details\":\"Thrown when the signature does not match the request\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Errors.sol\":\"KuruForwarderErrors\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ExecutionFailed"}, {"inputs": [], "type": "error", "name": "InsufficientValue"}, {"inputs": [], "type": "error", "name": "InterfaceNotAllowed"}, {"inputs": [], "type": "error", "name": "NonceAlreadyUsed"}, {"inputs": [], "type": "error", "name": "SignatureMismatch"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Errors.sol": "KuruForwarderErrors"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}}, "version": 1}, "id": 12}