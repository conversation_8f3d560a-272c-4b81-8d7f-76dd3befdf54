{"abi": [{"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InvalidSpread", "inputs": []}, {"type": "error", "name": "LengthMismatch", "inputs": []}, {"type": "error", "name": "MarketFeeError", "inputs": []}, {"type": "error", "name": "MarketSizeError", "inputs": []}, {"type": "error", "name": "MarketStateError", "inputs": []}, {"type": "error", "name": "NativeAssetInsufficient", "inputs": []}, {"type": "error", "name": "NativeAssetNotRequired", "inputs": []}, {"type": "error", "name": "NativeAssetSurplus", "inputs": []}, {"type": "error", "name": "NativeAssetTransferFail", "inputs": []}, {"type": "error", "name": "OnlyOwnerAllowedError", "inputs": []}, {"type": "error", "name": "Only<PERSON>aultAllowed", "inputs": []}, {"type": "error", "name": "OrderAlreadyFilledOrCancelled", "inputs": []}, {"type": "error", "name": "Post<PERSON>nly<PERSON><PERSON>r", "inputs": []}, {"type": "error", "name": "PriceError", "inputs": []}, {"type": "error", "name": "ProvisionError", "inputs": []}, {"type": "error", "name": "SizeError", "inputs": []}, {"type": "error", "name": "SlippageExceeded", "inputs": []}, {"type": "error", "name": "TickSizeError", "inputs": []}, {"type": "error", "name": "TooMuchSizeFilled", "inputs": []}, {"type": "error", "name": "TransferFromFailed", "inputs": []}, {"type": "error", "name": "Uint32Overflow", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "WrongOrderTypeCancel", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220797300728bcab441d73dc8711cd150b1bf765f0925a8d047eb984d0214245b8f64736f6c634300081c0033", "sourceMap": "62:3093:12:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220797300728bcab441d73dc8711cd150b1bf765f0925a8d047eb984d0214245b8f64736f6c634300081c0033", "sourceMap": "62:3093:12:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSpread\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketFeeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketSizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketStateError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetInsufficient\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetNotRequired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetSurplus\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetTransferFail\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyOwnerAllowedError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyVaultAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OrderAlreadyFilledOrCancelled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PostOnlyError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProvisionError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SlippageExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickSizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TooMuchSizeFilled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TransferFromFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint32Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongOrderTypeCancel\",\"type\":\"error\"}],\"devdoc\":{\"errors\":{\"InsufficientLiquidity()\":[{\"details\":\"Thrown when IOC orders do not get filled by the market\"}],\"InvalidSpread()\":[{\"details\":\"Thrown when Kuru AMM Vault spread passed to initializer is too high or too low or is not a multiple of 10\"}],\"LengthMismatch()\":[{\"details\":\"Thrown when length mismatch occurs between inputted arrays\"}],\"MarketFeeError()\":[{\"details\":\"Thrown when maker fee passed to initializer is too high/invalid\"}],\"MarketSizeError()\":[{\"details\":\"Thrown when minSize = 0 or maxSize < minSize\"}],\"MarketStateError()\":[{\"details\":\"Thrown when a market is paused and a user tries to execute an action or if the owner passes an already existing market state for toggling\"}],\"NativeAssetInsufficient()\":[{\"details\":\"Thrown when msg.value is insufficient in market orders\"}],\"NativeAssetNotRequired()\":[{\"details\":\"Thrown when msg.value is greater than 0 when native assets are not required\"}],\"NativeAssetSurplus()\":[{\"details\":\"Thrown when msg.value is surplus in market orders\"}],\"NativeAssetTransferFail()\":[{\"details\":\"Thrown when native asset transfer fails\"}],\"OnlyOwnerAllowedError()\":[{\"details\":\"Thrown when a non-owner tries to execute a privileged function, i.e, if non owner tries to pause/unpause a market or if a user tries to cancel an order that they did not place\"}],\"OnlyVaultAllowed()\":[{\"details\":\"Thrown when the call is not made by the vault\"}],\"OrderAlreadyFilledOrCancelled()\":[{\"details\":\"Thrown when cancelOrder is called on an order which is already filled or cancelled\"}],\"PostOnlyError()\":[{\"details\":\"Thrown when a post only order gets filled\"}],\"PriceError()\":[{\"details\":\"Thrown when the inputted price while adding an order is invalid\"}],\"ProvisionError()\":[{\"details\":\"Thrown when a flip order matches with a price\"}],\"SizeError()\":[{\"details\":\"Thrown when the size inputted is invalid, i.e, < minSize or > maxSize\"}],\"SlippageExceeded()\":[{\"details\":\"Throws when slippage is exceeded in market orders\"}],\"TickSizeError()\":[{\"details\":\"Thrown when price is not divisible by tick\"}],\"TooMuchSizeFilled()\":[{\"details\":\"Thrown when quote size in uint32 overflows\"}],\"TransferFromFailed()\":[{\"details\":\"Thrown when safe transfer from fails\"}],\"Uint32Overflow()\":[{\"details\":\"Thrown when safe cast to uint32 fails\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}],\"Unauthorized()\":[{\"details\":\"Thrown when a user is not the owner and tries to execute a privileged function\"}],\"WrongOrderTypeCancel()\":[{\"details\":\"Thrown when wrong interface is called for a cancel\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Errors.sol\":\"OrderBookErrors\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InvalidSpread"}, {"inputs": [], "type": "error", "name": "LengthMismatch"}, {"inputs": [], "type": "error", "name": "MarketFeeError"}, {"inputs": [], "type": "error", "name": "MarketSizeError"}, {"inputs": [], "type": "error", "name": "MarketStateError"}, {"inputs": [], "type": "error", "name": "NativeAssetInsufficient"}, {"inputs": [], "type": "error", "name": "NativeAssetNotRequired"}, {"inputs": [], "type": "error", "name": "NativeAssetSurplus"}, {"inputs": [], "type": "error", "name": "NativeAssetTransferFail"}, {"inputs": [], "type": "error", "name": "OnlyOwnerAllowedError"}, {"inputs": [], "type": "error", "name": "Only<PERSON>aultAllowed"}, {"inputs": [], "type": "error", "name": "OrderAlreadyFilledOrCancelled"}, {"inputs": [], "type": "error", "name": "Post<PERSON>nly<PERSON><PERSON>r"}, {"inputs": [], "type": "error", "name": "PriceError"}, {"inputs": [], "type": "error", "name": "ProvisionError"}, {"inputs": [], "type": "error", "name": "SizeError"}, {"inputs": [], "type": "error", "name": "SlippageExceeded"}, {"inputs": [], "type": "error", "name": "TickSizeError"}, {"inputs": [], "type": "error", "name": "TooMuchSizeFilled"}, {"inputs": [], "type": "error", "name": "TransferFromFailed"}, {"inputs": [], "type": "error", "name": "Uint32Overflow"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "WrongOrderTypeCancel"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Errors.sol": "OrderBookErrors"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}}, "version": 1}, "id": 12}