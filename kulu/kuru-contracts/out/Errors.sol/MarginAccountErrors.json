{"abi": [{"type": "error", "name": "FeeCollectorNotChanged", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "NativeAssetMismatch", "inputs": []}, {"type": "error", "name": "NativeAssetTransferFail", "inputs": []}, {"type": "error", "name": "OnlyR<PERSON>erAllowed", "inputs": []}, {"type": "error", "name": "OnlyVerifiedMarketsAllowed", "inputs": []}, {"type": "error", "name": "ProtocolPaused", "inputs": []}, {"type": "error", "name": "ProtocolStateNotChanged", "inputs": []}, {"type": "error", "name": "ZeroAddressNotAllowed", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220c730d953290b505ce9c871c72f411808d262ae7fb9a50fdc985d220da158c41d64736f6c634300081c0033", "sourceMap": "3157:1038:12:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220c730d953290b505ce9c871c72f411808d262ae7fb9a50fdc985d220da158c41d64736f6c634300081c0033", "sourceMap": "3157:1038:12:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"FeeCollectorNotChanged\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetTransferFail\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyRouterAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyVerifiedMarketsAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProtocolPaused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProtocolStateNotChanged\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddressNotAllowed\",\"type\":\"error\"}],\"devdoc\":{\"errors\":{\"FeeCollectorNotChanged()\":[{\"details\":\"Thrown when fee collector is not set\"}],\"InsufficientBalance()\":[{\"details\":\"Thrown when a user has insufficient margin account balance\"}],\"NativeAssetMismatch()\":[{\"details\":\"Thrown when msg.value is not zero when native assets are not required\"}],\"NativeAssetTransferFail()\":[{\"details\":\"Thrown when native asset transfer fails\"}],\"OnlyRouterAllowed()\":[{\"details\":\"Thrown when a non-router tries to update markets\"}],\"OnlyVerifiedMarketsAllowed()\":[{\"details\":\"Thrown when a non-verified market tries to execute a market action\"}],\"ProtocolPaused()\":[{\"details\":\"Thrown when protocol is paused\"}],\"ProtocolStateNotChanged()\":[{\"details\":\"Thrown when protocol state is not changed\"}],\"ZeroAddressNotAllowed()\":[{\"details\":\"Thrown when zero address is passed as a parameter\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Errors.sol\":\"MarginAccountErrors\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "FeeCollectorNotChanged"}, {"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "NativeAssetMismatch"}, {"inputs": [], "type": "error", "name": "NativeAssetTransferFail"}, {"inputs": [], "type": "error", "name": "OnlyR<PERSON>erAllowed"}, {"inputs": [], "type": "error", "name": "OnlyVerifiedMarketsAllowed"}, {"inputs": [], "type": "error", "name": "ProtocolPaused"}, {"inputs": [], "type": "error", "name": "ProtocolStateNotChanged"}, {"inputs": [], "type": "error", "name": "ZeroAddressNotAllowed"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Errors.sol": "MarginAccountErrors"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}}, "version": 1}, "id": 12}