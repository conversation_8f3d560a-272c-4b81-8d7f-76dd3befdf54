{"abi": [{"type": "function", "name": "SPREAD_CONSTANT", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "addBuyOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "size", "type": "uint96", "internalType": "uint96"}, {"name": "_postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addFlipBuyOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "_flippedPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_provisionOrRevert", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addFlipSellOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "_flippedPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_provisionOrRevert", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSellOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "size", "type": "uint96", "internalType": "uint96"}, {"name": "_postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchCancelFlipOrders", "inputs": [{"name": "_orderIds", "type": "uint40[]", "internalType": "uint40[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchCancelOrders", "inputs": [{"name": "_orderIds", "type": "uint40[]", "internalType": "uint40[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchUpdate", "inputs": [{"name": "buyPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "buySizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "sellPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "sellSizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "orderIdsToCancel", "type": "uint40[]", "internalType": "uint40[]"}, {"name": "postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "bestBidAsk", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMarketParams", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getVaultParams", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_factory", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum IOrderBook.OrderBookType"}, {"name": "_baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "_baseAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "_quoteAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "_marginAccountAddress", "type": "address", "internalType": "address"}, {"name": "_sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "_pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "_tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "_minSize", "type": "uint96", "internalType": "uint96"}, {"name": "_maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "_takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_makerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_kuruAmmVault", "type": "address", "internalType": "address"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}, {"name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "kuruAmmVault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "placeAndExecuteMarketBuy", "inputs": [{"name": "_quoteAmount", "type": "uint96", "internalType": "uint96"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "_isMargin", "type": "bool", "internalType": "bool"}, {"name": "_isFillOrKill", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "placeAndExecuteMarketSell", "inputs": [{"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "_isMargin", "type": "bool", "internalType": "bool"}, {"name": "_isFillOrKill", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "toggleMarket", "inputs": [{"name": "_state", "type": "uint8", "internalType": "enum IOrderBook.MarketState"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateVaultOrdSz", "inputs": [{"name": "_vaultAskOrderSize", "type": "uint96", "internalType": "uint96"}, {"name": "_vaultBidOrderSize", "type": "uint96", "internalType": "uint96"}, {"name": "_askPrice", "type": "uint256", "internalType": "uint256"}, {"name": "_bidPrice", "type": "uint256", "internalType": "uint256"}, {"name": "_nullifyPartialFills", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "vaultAskOrderSize", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "vaultBestAsk", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "F<PERSON><PERSON><PERSON><PERSON>Created", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "flippedId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "flippedPrice", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "FlipOrderUpdated", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "FlipOrdersCanceled", "inputs": [{"name": "orderIds", "type": "uint40[]", "indexed": false, "internalType": "uint40[]"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Flipped<PERSON><PERSON><PERSON>Created", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "flippedId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "flippedPrice", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "MarketStateUpdated", "inputs": [{"name": "previousState", "type": "uint8", "indexed": false, "internalType": "enum IOrderBook.MarketState"}, {"name": "newState", "type": "uint8", "indexed": false, "internalType": "enum IOrderBook.MarketState"}], "anonymous": false}, {"type": "event", "name": "OrderCanceled", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OrderCreated", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OrdersCanceled", "inputs": [{"name": "orderId", "type": "uint40[]", "indexed": false, "internalType": "uint40[]"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Trade", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "makerAddress", "type": "address", "indexed": false, "internalType": "address"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "price", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "updatedSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "taker<PERSON><PERSON><PERSON>", "type": "address", "indexed": false, "internalType": "address"}, {"name": "txOrigin", "type": "address", "indexed": false, "internalType": "address"}, {"name": "filledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "VaultParamsUpdated", "inputs": [{"name": "_vaultAskOrderSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultAskPartiallyFilledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultBidOrderSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultBidPartiallyFilledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_askPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "_bidPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "Only<PERSON>aultAllowed", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"SPREAD_CONSTANT()": "a68ee881", "addBuyOrder(uint32,uint96,bool)": "a09e9040", "addFlipBuyOrder(uint32,uint32,uint96,bool)": "4792b6ea", "addFlipSellOrder(uint32,uint32,uint96,bool)": "6d1016b0", "addSellOrder(uint32,uint96,bool)": "40e79b1b", "batchCancelFlipOrders(uint40[])": "ba42d8bd", "batchCancelOrders(uint40[])": "23afbff3", "batchUpdate(uint32[],uint96[],uint32[],uint96[],uint40[],bool)": "5339c59f", "bestBidAsk()": "b4de8b70", "getMarketParams()": "90c9427c", "getVaultParams()": "88bb4f60", "initialize(address,uint8,address,uint256,address,uint256,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,address,uint96,address)": "c827fdcc", "kuruAmmVault()": "a65ddd09", "placeAndExecuteMarketBuy(uint96,uint256,bool,bool)": "7c51d6cf", "placeAndExecuteMarketSell(uint96,uint256,bool,bool)": "532c46db", "toggleMarket(uint8)": "732295ca", "updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": "27688f0f", "vaultAskOrderSize()": "51130ac6", "vaultBestAsk()": "474b848c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"OnlyVaultAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"flippedId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"flippedPrice\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"FlipOrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"}],\"name\":\"FlipOrderUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40[]\",\"name\":\"orderIds\",\"type\":\"uint40[]\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"FlipOrdersCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"flippedId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"flippedPrice\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"FlippedOrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"previousState\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"newState\",\"type\":\"uint8\"}],\"name\":\"MarketStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"OrderCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"OrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40[]\",\"name\":\"orderId\",\"type\":\"uint40[]\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OrdersCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"makerAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"updatedSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"takerAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"txOrigin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"filledSize\",\"type\":\"uint96\"}],\"name\":\"Trade\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultAskOrderSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultAskPartiallyFilledSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultBidOrderSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultBidPartiallyFilledSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_askPrice\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_bidPrice\",\"type\":\"uint256\"}],\"name\":\"VaultParamsUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"SPREAD_CONSTANT\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_postOnly\",\"type\":\"bool\"}],\"name\":\"addBuyOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_flippedPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_provisionOrRevert\",\"type\":\"bool\"}],\"name\":\"addFlipBuyOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_flippedPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_provisionOrRevert\",\"type\":\"bool\"}],\"name\":\"addFlipSellOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_postOnly\",\"type\":\"bool\"}],\"name\":\"addSellOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40[]\",\"name\":\"_orderIds\",\"type\":\"uint40[]\"}],\"name\":\"batchCancelFlipOrders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40[]\",\"name\":\"_orderIds\",\"type\":\"uint40[]\"}],\"name\":\"batchCancelOrders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"buyPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"buySizes\",\"type\":\"uint96[]\"},{\"internalType\":\"uint32[]\",\"name\":\"sellPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"sellSizes\",\"type\":\"uint96[]\"},{\"internalType\":\"uint40[]\",\"name\":\"orderIdsToCancel\",\"type\":\"uint40[]\"},{\"internalType\":\"bool\",\"name\":\"postOnly\",\"type\":\"bool\"}],\"name\":\"batchUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"bestBidAsk\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMarketParams\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getVaultParams\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_factory\",\"type\":\"address\"},{\"internalType\":\"enum IOrderBook.OrderBookType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"_baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_baseAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_quoteAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_marginAccountAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"_pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_makerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_kuruAmmVault\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"__trustedForwarder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruAmmVault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_quoteAmount\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_isMargin\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"_isFillOrKill\",\"type\":\"bool\"}],\"name\":\"placeAndExecuteMarketBuy\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_isMargin\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"_isFillOrKill\",\"type\":\"bool\"}],\"name\":\"placeAndExecuteMarketSell\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"_state\",\"type\":\"uint8\"}],\"name\":\"toggleMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_vaultAskOrderSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_vaultBidOrderSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_askPrice\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_bidPrice\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_nullifyPartialFills\",\"type\":\"bool\"}],\"name\":\"updateVaultOrdSz\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultAskOrderSize\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultBestAsk\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OnlyVaultAllowed()\":[{\"details\":\"Thrown when the call is not made by the vault\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"FlipOrderCreated(uint40,uint40,address,uint96,uint32,uint32,bool)\":{\"details\":\"Emitted when a flip order is created\",\"params\":{\"flippedId\":\"Unique identifier for the flipped order.\",\"flippedPrice\":\"Price point of the flipped order in the specified precision.\",\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"FlipOrderUpdated(uint40,uint96)\":{\"details\":\"Emitted when a flip order is updated\",\"params\":{\"orderId\":\"Unique identifier for the order.\",\"size\":\"Size of the order in the specified precision.\"}},\"FlipOrdersCanceled(uint40[],address)\":{\"details\":\"Emitted when one or more flip orders are canceled\",\"params\":{\"orderIds\":\"Array of order identifiers that were canceled.\",\"owner\":\"Address of the user who canceled the orders.\"}},\"FlippedOrderCreated(uint40,uint40,address,uint96,uint32,uint32,bool)\":{\"details\":\"Emitted when a flip order is partially/completely filled and it results in a new order\",\"params\":{\"flippedId\":\"Unique identifier for the flipped order.\",\"flippedPrice\":\"Price point of the flipped order in the specified precision.\",\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"OrderCanceled(uint40,address,uint32,uint96,bool)\":{\"details\":\"Emitted for each cancel\"},\"OrderCreated(uint40,address,uint96,uint32,bool)\":{\"details\":\"Emitted when a new order is created.\",\"params\":{\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"OrdersCanceled(uint40[],address)\":{\"details\":\"Emitted when one or more orders are completed or canceled.\",\"params\":{\"orderId\":\"Array of order identifiers that were completed or canceled.\"}},\"Trade(uint40,address,bool,uint256,uint96,address,address,uint96)\":{\"details\":\"Emitted when a trade goes through.\",\"params\":{\"filledSize\":\"Size taken by the taker.\",\"orderId\":\"Order Id of the order that was filled. PS. All data regarding the original order can be found out from the order ID\",\"takerAddress\":\"Address of the taker.\",\"updatedSize\":\"New size of the order\"}},\"VaultParamsUpdated(uint96,uint96,uint96,uint96,uint256,uint256)\":{\"details\":\"Emitted when the vault params are updated\",\"params\":{\"_askPrice\":\"The vault best ask price\",\"_bidPrice\":\"The vault best bid price\",\"_vaultAskOrderSize\":\"Size of the vault ask order\",\"_vaultAskPartiallyFilledSize\":\"Size of the vault ask partially filled order\",\"_vaultBidOrderSize\":\"Size of the vault bid order\",\"_vaultBidPartiallyFilledSize\":\"Size of the vault bid partially filled order\"}}},\"kind\":\"dev\",\"methods\":{\"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)\":{\"params\":{\"_askPrice\":\"The new ask price. Note: Only updated on the first deposit\",\"_bidPrice\":\"The new bid price. Note: Only updated on the first deposit\",\"_nullifyPartialFills\":\"Whether to nullify partial fills. Only done during specific withdrawals\",\"_vaultAskOrderSize\":\"The new ask order size\",\"_vaultBidOrderSize\":\"The new bid order size\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)\":{\"notice\":\"Updates the vault order sizes and prices when a user deposits or withdraws\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/AbstractAMM.sol\":\"AbstractAMM\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/AbstractAMM.sol\":{\"keccak256\":\"0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804\",\"dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/BitMath.sol\":{\"keccak256\":\"0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f\",\"dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]},\"contracts/libraries/OrderLinkedList.sol\":{\"keccak256\":\"0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133\",\"dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU\"]},\"contracts/libraries/TreeMath.sol\":{\"keccak256\":\"0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0\",\"dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Only<PERSON>aultAllowed"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint40", "name": "flippedId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "flippedPrice", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "F<PERSON><PERSON><PERSON><PERSON>Created", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}], "type": "event", "name": "FlipOrderUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint40[]", "name": "orderIds", "type": "uint40[]", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}], "type": "event", "name": "FlipOrdersCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint40", "name": "flippedId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "flippedPrice", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "Flipped<PERSON><PERSON><PERSON>Created", "anonymous": false}, {"inputs": [{"internalType": "enum IOrderBook.MarketState", "name": "previousState", "type": "uint8", "indexed": false}, {"internalType": "enum IOrderBook.MarketState", "name": "newState", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "OrderCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "OrderCreated", "anonymous": false}, {"inputs": [{"internalType": "uint40[]", "name": "orderId", "type": "uint40[]", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}], "type": "event", "name": "OrdersCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "makerAddress", "type": "address", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}, {"internalType": "uint256", "name": "price", "type": "uint256", "indexed": false}, {"internalType": "uint96", "name": "updatedSize", "type": "uint96", "indexed": false}, {"internalType": "address", "name": "taker<PERSON><PERSON><PERSON>", "type": "address", "indexed": false}, {"internalType": "address", "name": "txOrigin", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "filledSize", "type": "uint96", "indexed": false}], "type": "event", "name": "Trade", "anonymous": false}, {"inputs": [{"internalType": "uint96", "name": "_vaultAskOrderSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultAskPartiallyFilledSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultBidOrderSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultBidPartiallyFilledSize", "type": "uint96", "indexed": false}, {"internalType": "uint256", "name": "_askPrice", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "_bidPrice", "type": "uint256", "indexed": false}], "type": "event", "name": "VaultParamsUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SPREAD_CONSTANT", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint96", "name": "size", "type": "uint96"}, {"internalType": "bool", "name": "_postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addBuyOrder"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint32", "name": "_flippedPrice", "type": "uint32"}, {"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "bool", "name": "_provisionOrRevert", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addFlipBuyOrder"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint32", "name": "_flippedPrice", "type": "uint32"}, {"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "bool", "name": "_provisionOrRevert", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addFlipSellOrder"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint96", "name": "size", "type": "uint96"}, {"internalType": "bool", "name": "_postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addSellOrder"}, {"inputs": [{"internalType": "uint40[]", "name": "_orderIds", "type": "uint40[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchCancelFlipOrders"}, {"inputs": [{"internalType": "uint40[]", "name": "_orderIds", "type": "uint40[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchCancelOrders"}, {"inputs": [{"internalType": "uint32[]", "name": "buyPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "buySizes", "type": "uint96[]"}, {"internalType": "uint32[]", "name": "sellPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "sellSizes", "type": "uint96[]"}, {"internalType": "uint40[]", "name": "orderIdsToCancel", "type": "uint40[]"}, {"internalType": "bool", "name": "postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "batchUpdate"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "bestBidAsk", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getMarketParams", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getVaultParams", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [{"internalType": "address", "name": "_factory", "type": "address"}, {"internalType": "enum IOrderBook.OrderBookType", "name": "_type", "type": "uint8"}, {"internalType": "address", "name": "_baseAssetAddress", "type": "address"}, {"internalType": "uint256", "name": "_baseAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint256", "name": "_quoteAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "_marginAccountAddress", "type": "address"}, {"internalType": "uint96", "name": "_sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "_pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "_tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "_minSize", "type": "uint96"}, {"internalType": "uint96", "name": "_maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "_takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "_makerFeeBps", "type": "uint256"}, {"internalType": "address", "name": "_kuruAmmVault", "type": "address"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}, {"internalType": "address", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruAmmVault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint96", "name": "_quoteAmount", "type": "uint96"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}, {"internalType": "bool", "name": "_isMargin", "type": "bool"}, {"internalType": "bool", "name": "_isFillOrKill", "type": "bool"}], "stateMutability": "payable", "type": "function", "name": "placeAndExecuteMarketBuy", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}, {"internalType": "bool", "name": "_isMargin", "type": "bool"}, {"internalType": "bool", "name": "_isFillOrKill", "type": "bool"}], "stateMutability": "payable", "type": "function", "name": "placeAndExecuteMarketSell", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "enum IOrderBook.MarketState", "name": "_state", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "toggleMarket"}, {"inputs": [{"internalType": "uint96", "name": "_vaultAskOrderSize", "type": "uint96"}, {"internalType": "uint96", "name": "_vaultBidOrderSize", "type": "uint96"}, {"internalType": "uint256", "name": "_askPrice", "type": "uint256"}, {"internalType": "uint256", "name": "_bidPrice", "type": "uint256"}, {"internalType": "bool", "name": "_nullifyPartialFills", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateVaultOrdSz"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultAskOrderSize", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultBestAsk", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": {"params": {"_askPrice": "The new ask price. Note: Only updated on the first deposit", "_bidPrice": "The new bid price. Note: Only updated on the first deposit", "_nullifyPartialFills": "Whether to nullify partial fills. Only done during specific withdrawals", "_vaultAskOrderSize": "The new ask order size", "_vaultBidOrderSize": "The new bid order size"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": {"notice": "Updates the vault order sizes and prices when a user deposits or withdraws"}}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/AbstractAMM.sol": "AbstractAMM"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/AbstractAMM.sol": {"keccak256": "0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e", "urls": ["bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804", "dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg"], "license": "BUSL-1.1"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/BitMath.sol": {"keccak256": "0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f", "urls": ["bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f", "dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw"], "license": "MIT"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}, "contracts/libraries/OrderLinkedList.sol": {"keccak256": "0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32", "urls": ["bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133", "dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU"], "license": "BUSL-1.1"}, "contracts/libraries/TreeMath.sol": {"keccak256": "0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10", "urls": ["bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0", "dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}}, "version": 1}, "id": 0}