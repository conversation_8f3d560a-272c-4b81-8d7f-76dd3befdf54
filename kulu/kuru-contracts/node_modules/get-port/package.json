{"name": "get-port", "version": "3.2.0", "description": "Get an available port", "license": "MIT", "repository": "sindresorhus/get-port", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["port", "find", "finder", "portfinder", "free", "available", "connection", "connect", "open", "net", "tcp", "scan", "rand", "random", "preferred", "chosen"], "devDependencies": {"ava": "*", "pify": "^3.0.0", "xo": "*"}}