{"name": "binaryen", "description": "JavaScript version of Binaryen, a compiler infrastructure and toolchain library for WebAssembly.", "version": "101.0.0-nightly.20210723", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/AssemblyScript/binaryen.js.git"}, "keywords": ["webassembly", "wasm"], "main": "index.js", "typings": "index.d.ts", "bin": {"wasm-opt": "bin/wasm-opt"}, "scripts": {"readme": "doctoc README.md --github --title \"### Contents\"", "check": "tsc index.d.ts --noEmit --strict --noImplicitAny --strictNullChecks --listFiles --diagnostics", "test": "npm run check && node tests/sanity && node tests/example"}, "files": ["index.js", "index.d.ts", "wasm.js", "wasm.d.ts", "package.json", "package-lock.json", "README.md", "bin/wasm-opt"], "devDependencies": {"dateformat": "^3.0.3", "doctoc": "^1.4.0", "semver": "^7.1.3", "simple-git": "^1.132.0", "typescript": "^3.8.3"}, "dependencies": {}}