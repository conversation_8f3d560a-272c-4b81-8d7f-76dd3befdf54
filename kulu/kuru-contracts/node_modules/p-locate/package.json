{"name": "p-locate", "version": "2.0.0", "description": "Get the first fulfilled promise that satisfies the provided testing function", "license": "MIT", "repository": "sindresorhus/p-locate", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "dependencies": {"p-limit": "^1.1.0"}, "devDependencies": {"ava": "*", "delay": "^1.3.1", "in-range": "^1.0.0", "time-span": "^1.0.0", "xo": "*"}, "xo": {"esnext": true}}