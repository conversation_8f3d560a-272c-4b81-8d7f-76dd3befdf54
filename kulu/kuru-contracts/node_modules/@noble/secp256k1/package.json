{"name": "@noble/secp256k1", "version": "1.7.1", "description": "Fastest JS implementation of secp256k1. Independently audited, high-security, 0-dependency ECDSA & Schnorr signatures", "files": ["lib"], "main": "lib/index.js", "module": "lib/esm/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "build:release": "rollup -c rollup.config.js", "lint": "prettier --print-width 100 --single-quote --check index.ts", "format": "prettier --print-width 100 --single-quote --write index.ts", "test": "jest", "coverage": "jest --coverage", "bench": "node test/benchmark.js"}, "author": "<PERSON> (https://paulmillr.com)", "homepage": "https://paulmillr.com/noble/", "repository": {"type": "git", "url": "https://github.com/paulmillr/noble-secp256k1.git"}, "license": "MIT", "browser": {"crypto": false}, "devDependencies": {"@noble/hashes": "1.1.2", "@rollup/plugin-commonjs": "22.0.0", "@rollup/plugin-node-resolve": "13.3.0", "@types/jest": "28.1.1", "@types/node": "17.0.18", "fast-check": "3.0.0", "jest": "28.1.0", "micro-bmark": "0.2.0", "prettier": "2.6.2", "rollup": "2.75.5", "ts-jest": "28.0.4", "typescript": "4.7.3"}, "keywords": ["secp256k1", "secp", "secp256", "elliptic", "elliptic curve", "curve", "signature", "ecc", "rfc6979", "schnorr", "sig", "bip0340", "bip340", "ecdsa", "endomorphism", "cryptography", "security", "noble"], "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/esm/index.js", "default": "./lib/index.js"}}, "jest": {"testRegex": "/test/.*?\\.ts", "transform": {"^.+\\.ts$": "ts-jest"}}, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}