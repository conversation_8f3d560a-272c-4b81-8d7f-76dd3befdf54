{"name": "@metamask/eth-sig-util", "version": "4.0.1", "description": "A few useful functions for signing ethereum data", "keywords": ["ethereum", "signature"], "homepage": "https://github.com/MetaMask/eth-sig-util#readme", "bugs": {"url": "https://github.com/MetaMask/eth-sig-util/issues"}, "repository": {"type": "git", "url": "https://github.com/MetaMask/eth-sig-util.git"}, "license": "ISC", "author": "<PERSON>", "exports": {".": "./dist/index.js", "./encryption": "./dist/encryption.js", "./personal-sign": "./dist/personal-sign.js", "./sign-typed-data": "./dist/sign-typed-data.js"}, "main": "./dist/index.js", "files": ["dist"], "scripts": {"setup": "yarn install && yarn allow-scripts", "build": "tsc --project .", "build:clean": "rimraf dist && yarn build", "lint:eslint": "eslint . --cache --ext js,ts", "lint:json": "prettier '**/*.json' --ignore-path .gitignore", "lint": "yarn lint:eslint && yarn lint:json --check", "lint:fix": "yarn lint:eslint --fix && yarn lint:json --write", "test": "jest", "test:watch": "jest --watch", "prepublishOnly": "yarn build:clean", "docs": "typedoc", "docs:publish": "typedoc --cleanOutputDir false --gitRevision \"v$(jq -r .version < ./package.json)\""}, "resolutions": {"airtap/engine.io-client/xmlhttprequest-ssl": "^1.6.2"}, "dependencies": {"ethereumjs-abi": "^0.6.8", "ethereumjs-util": "^6.2.1", "ethjs-util": "^0.1.6", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1"}, "devDependencies": {"@gudahtt/typedoc": "^0.23.0", "@lavamoat/allow-scripts": "^1.0.6", "@metamask/auto-changelog": "^2.4.0", "@metamask/eslint-config": "^9.0.0", "@metamask/eslint-config-jest": "^9.0.0", "@metamask/eslint-config-nodejs": "^9.0.0", "@metamask/eslint-config-typescript": "^9.0.1", "@types/jest": "^26.0.24", "@types/node": "^14.14.25", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "ajv": "^8.11.0", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-jsdoc": "^36.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "jest": "^27.0.6", "prettier": "^2.3.2", "prettier-plugin-packagejson": "^2.2.11", "rimraf": "^3.0.2", "ts-jest": "^27.0.3", "typescript": "^4.1.3"}, "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "lavamoat": {"allowScripts": {"@lavamoat/preinstall-always-fail": false, "keccak": true, "secp256k1": true}}}