# SYNOPSIS

[![NPM Package](https://img.shields.io/npm/v/ethereumjs-util.svg?style=flat-square)](https://www.npmjs.org/package/ethereumjs-util)
[![Build Status](https://img.shields.io/travis/ethereumjs/ethereumjs-util.svg?branch=master&style=flat-square)](https://travis-ci.org/ethereumjs/ethereumjs-util)
[![Coverage Status](https://img.shields.io/coveralls/ethereumjs/ethereumjs-util.svg?style=flat-square)](https://coveralls.io/r/ethereumjs/ethereumjs-util)
[![Gitter](https://img.shields.io/gitter/room/ethereum/ethereumjs-lib.svg?style=flat-square)](https://gitter.im/ethereum/ethereumjs-lib) or #ethereumjs on freenode

A collection of utility functions for ethereum. It can be used in node.js or can be in the browser with browserify.

# API

[./docs/](./docs/README.md)

Most of the string manipulation methods are provided by [ethjs-util](https://github.com/ethjs/ethjs-util)

---

Additionally ethereumjs-util re-exports a few commonly-used libraries. These include:

- `BN` ([bn.js](https://github.com/indutny/bn.js))
- `rlp` ([rlp](https://github.com/ethereumjs/rlp))
- `secp256k1` ([secp256k1](https://github.com/cryptocoinjs/secp256k1-node/))

# EthereumJS

See our organizational [documentation](https://ethereumjs.readthedocs.io) for an introduction to `EthereumJS` as well as information on current standards and best practices.

If you want to join for work or do improvements on the libraries have a look at our [contribution guidelines](https://ethereumjs.readthedocs.io/en/latest/contributing.html).

# LICENSE

MPL-2.0
