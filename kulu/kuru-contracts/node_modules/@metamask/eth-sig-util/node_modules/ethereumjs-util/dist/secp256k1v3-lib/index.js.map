{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/secp256k1v3-lib/index.ts"], "names": [], "mappings": ";AAAA,0CAA0C;AAC1C,qEAAqE;;AAErE,0BAA4B;AAC5B,IAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAA;AAEjC,IAAM,EAAE,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,CAAA;AAC9B,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAA;AAOzB,OAAO,CAAC,gBAAgB,GAAG,UAAS,UAAkB,EAAE,UAA0B;IAA1B,2BAAA,EAAA,iBAA0B;IAChF,IAAM,CAAC,GAAG,IAAI,EAAE,CAAC,UAAU,CAAC,CAAA;IAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;KACjD;IAED,IAAM,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACzB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAA;AAC5D,CAAC,CAAA;AAED,OAAO,CAAC,oBAAoB,GAAG,UAAS,UAAkB;IACxD,IAAM,EAAE,GAAG,IAAI,EAAE,CAAC,UAAU,CAAC,CAAA;IAC7B,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;KAChD;IAED,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;AAC1D,CAAC,CAAA;AAED,OAAO,CAAC,eAAe,GAAG,UAAS,MAAc;IAC/C,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACxB,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QAC3B,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;KACd;IAED,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACxB,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QAC3B,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;KACd;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;AAC1F,CAAC,CAAA;AAED,OAAO,CAAC,UAAU,GAAG,UACnB,SAAiB,EACjB,UAAkB,EAClB,UAA0B;IAA1B,2BAAA,EAAA,iBAA0B;IAE1B,IAAM,KAAK,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;IAEzC,IAAM,MAAM,GAAG,IAAI,EAAE,CAAC,UAAU,CAAC,CAAA;IACjC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE;QACnD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;KACzD;IAED,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACpC,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAA;AAC9D,CAAC,CAAA;AAED,IAAM,WAAW,GAAG,UAAS,CAAK,EAAE,CAAK,EAAE,UAAmB;IAC5D,IAAI,SAAS,CAAA;IAEb,IAAI,UAAU,EAAE;QACd,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAC5B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;QACtC,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;KACnD;SAAM;QACL,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAC5B,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACnB,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;QAClD,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;KACpD;IAED,OAAO,SAAS,CAAA;AAClB,CAAC,CAAA"}