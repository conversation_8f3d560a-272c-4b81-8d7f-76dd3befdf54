{"version": 3, "file": "object.js", "sourceRoot": "", "sources": ["../src/object.ts"], "names": [], "mappings": ";;;AAAA,IAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAA;AAChC,IAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;AACvC,yBAA2B;AAC3B,iCAAwD;AAExD;;;;;;;;;;GAUG;AACU,QAAA,gBAAgB,GAAG,UAAS,IAAS,EAAE,MAAW,EAAE,IAAS;IACxE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;IACb,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;IAEjB,sBAAsB;IACtB,IAAI,CAAC,MAAM,GAAG,UAAS,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QAC3C,IAAI,KAAK,EAAE;YAET,IAAM,KAAG,GAAS,EAAE,CAAA;YACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAa;gBACjC,KAAG,CAAC,KAAK,CAAC,GAAG,OAAK,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAG,CAAA;YACjD,CAAC,CAAC,CAAA;YACF,OAAO,KAAG,CAAA;SACX;QACD,OAAO,gBAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3B,CAAC,CAAA;IAED,IAAI,CAAC,SAAS,GAAG,SAAS,SAAS;QACjC,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC,CAAA;IAED,MAAM,CAAC,OAAO,CAAC,UAAC,KAAU,EAAE,CAAS;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC7B,SAAS,MAAM;YACb,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACpB,CAAC;QACD,SAAS,MAAM,CAAC,CAAM;YACpB,CAAC,GAAG,gBAAQ,CAAC,CAAC,CAAC,CAAA;YAEf,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAClD,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;aAC1B;YAED,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,EAAE;gBACnC,CAAC,GAAG,kBAAU,CAAC,CAAC,CAAC,CAAA;gBACjB,MAAM,CACJ,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EACxB,eAAa,KAAK,CAAC,IAAI,4BAAuB,KAAK,CAAC,MAAM,WAAQ,CACnE,CAAA;aACF;iBAAM,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;gBAC/D,MAAM,CACJ,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EACzB,eAAa,KAAK,CAAC,IAAI,kCAA6B,KAAK,CAAC,MAAQ,CACnE,CAAA;aACF;YAED,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC;QAED,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;YACtC,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,GAAG,EAAE,MAAM;YACX,GAAG,EAAE,MAAM;SACZ,CAAC,CAAA;QAEF,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAA;SACjC;QAED,eAAe;QACf,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE;gBACvC,UAAU,EAAE,KAAK;gBACjB,YAAY,EAAE,IAAI;gBAClB,GAAG,EAAE,MAAM;gBACX,GAAG,EAAE,MAAM;aACZ,CAAC,CAAA;SACH;IACH,CAAC,CAAC,CAAA;IAEF,mCAAmC;IACnC,IAAI,IAAI,EAAE;QACR,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAA;SAC1D;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACxB;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;aAClD;YAED,sCAAsC;YACtC,IAAI,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAQ,CAAC,CAAC,CAAC,CAAA;YACrC,CAAC,CAAC,CAAA;SACH;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACnC,IAAM,MAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9B,MAAM,CAAC,OAAO,CAAC,UAAC,KAAU;gBACxB,IAAI,MAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBACxE,IAAI,MAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC7E,CAAC,CAAC,CAAA;SACH;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;AACH,CAAC,CAAA"}