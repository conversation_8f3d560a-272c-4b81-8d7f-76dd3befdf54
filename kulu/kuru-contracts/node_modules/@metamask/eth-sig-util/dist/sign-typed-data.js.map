{"version": 3, "file": "sign-typed-data.js", "sourceRoot": "", "sources": ["../src/sign-typed-data.ts"], "names": [], "mappings": ";;;AAAA,qDAMyB;AACzB,mDAAyD;AAEzD,mCAKiB;AAoBjB;;;;;;;;;;GAUG;AACH,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,iCAAS,CAAA;IACT,iCAAS,CAAA;IACT,iCAAS,CAAA;AACX,CAAC,EAJW,oBAAoB,GAApB,4BAAoB,KAApB,4BAAoB,QAI/B;AA0CY,QAAA,oBAAoB,GAAG;IAClC,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE;QACV,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,oBAAoB,EAAE;gBACpB,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACxB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACzB;oBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;iBAC3B;aACF;SACF;QACD,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC/B,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC1B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;KAC5B;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;CACxD,CAAC;AAEF;;;;;;GAMG;AACH,SAAS,eAAe,CACtB,OAA6B,EAC7B,eAAwC;IAExC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxD,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,GAAG,CAAC,CAAC;KAClD;SAAM,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAChE,MAAM,IAAI,KAAK,CACb,sCAAsC,OAAO,4BAA4B,eAAe,CAAC,IAAI,CAC3F,IAAI,CACL,EAAE,CACJ,CAAC;KACH;AACH,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,WAAW,CAClB,KAA4C,EAC5C,IAAY,EACZ,IAAY,EACZ,KAAU,EACV,OAA0D;IAE1D,eAAe,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,EAAE,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,KAAK,oBAAoB,CAAC,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,iCAAiC;gBACpF,CAAC,CAAC,oEAAoE;gBACtE,CAAC,CAAC,wBAAM,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpD,CAAC;KACH;IAED,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,YAAY,IAAI,EAAE,CAAC,CAAC;KACpE;IAED,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC,SAAS,EAAE,wBAAM,CAAC,KAAK,CAAC,CAAC,CAAC;KACnC;IAED,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,6FAA6F;QAC7F,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,SAAS,EAAE,wBAAM,CAAC,KAAK,CAAC,CAAC,CAAC;KACnC;IAED,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAC7C,IAAI,OAAO,KAAK,oBAAoB,CAAC,EAAE,EAAE;YACvC,MAAM,IAAI,KAAK,CACb,0DAA0D,CAC3D,CAAC;SACH;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACxC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CACpD,CAAC;QACF,OAAO;YACL,SAAS;YACT,wBAAM,CACJ,0BAAS,CACP,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC9B,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACjC,CACF;SACF,CAAC;KACH;IAED,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,UAAU,CACjB,WAAmB,EACnB,IAA6B,EAC7B,KAA4C,EAC5C,OAA0D;IAE1D,eAAe,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,EAAE,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,MAAM,YAAY,GAAG,CAAC,SAAS,CAAC,CAAC;IACjC,MAAM,aAAa,GAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;IAEhE,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QACtC,IAAI,OAAO,KAAK,oBAAoB,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YACzE,SAAS;SACV;QACD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,CAC/B,KAAK,EACL,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,IAAI,EACV,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAChB,OAAO,CACR,CAAC;QACF,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;IAED,OAAO,0BAAS,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AAChD,CAAC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CACjB,WAAmB,EACnB,KAA4C;IAE5C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,YAAY,GAAG,oBAAoB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC9D,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAEjC,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/D,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;SAC1D;QAED,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;aAC7B,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;aAC1C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;KACjB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,oBAAoB,CAC3B,WAAmB,EACnB,KAA4C,EAC5C,UAAuB,IAAI,GAAG,EAAE;IAEhC,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;QAChE,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAEzB,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QACtC,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KAClD;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,UAAU,CACjB,WAAmB,EACnB,IAA6B,EAC7B,KAA4C,EAC5C,OAA0D;IAE1D,eAAe,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,EAAE,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,OAAO,wBAAM,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;;GAMG;AACH,SAAS,QAAQ,CACf,WAAmB,EACnB,KAA4C;IAE5C,OAAO,wBAAM,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;;;;;GAKG;AACH,SAAS,YAAY,CACnB,IAAqB;IAErB,MAAM,aAAa,GAA6B,EAAE,CAAC;IACnD,KAAK,MAAM,GAAG,IAAI,4BAAoB,CAAC,UAAU,EAAE;QACjD,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;YACb,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;SAChC;KACF;IAED,IAAI,OAAO,IAAI,aAAa,EAAE;QAC5B,aAAa,CAAC,KAAK,mBAAK,YAAY,EAAE,EAAE,IAAK,aAAa,CAAC,KAAK,CAAE,CAAC;KACpE;IACD,OAAO,aAA0C,CAAC;AACpD,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAS,UAAU,CACjB,SAA0B,EAC1B,OAA0D;IAE1D,eAAe,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,EAAE,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,MAAM,aAAa,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;IAC9C,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,KAAK,CAAC,IAAI,CACR,UAAU,CACR,cAAc,EACd,aAAa,CAAC,MAAM,EACpB,aAAa,CAAC,KAAK,EACnB,OAAO,CACR,CACF,CAAC;IAEF,IAAI,aAAa,CAAC,WAAW,KAAK,cAAc,EAAE;QAChD,KAAK,CAAC,IAAI,CACR,UAAU;QACR,0EAA0E;QAC1E,aAAa,CAAC,WAAqB,EACnC,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,KAAK,EACnB,OAAO,CACR,CACF,CAAC;KACH;IACD,OAAO,wBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACU,QAAA,cAAc,GAAG;IAC5B,UAAU;IACV,UAAU;IACV,oBAAoB;IACpB,UAAU;IACV,QAAQ;IACR,YAAY;IACZ,UAAU;CACX,CAAC;AAEF;;;;;;;;GAQG;AACH,SAAgB,kBAAkB,CAAC,SAA6B;IAC9D,MAAM,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAClD,OAAO,6BAAW,CAAC,UAAU,CAAC,CAAC;AACjC,CAAC;AAHD,gDAGC;AAED;;;;;;;;GAQG;AACH,SAAS,mBAAmB,CAAC,SAAsB;IACjD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IACjE,IACE,OAAO,SAAS,KAAK,QAAQ;QAC7B,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC;QACxB,CAAC,SAAS,CAAC,MAAM,EACjB;QACA,MAAM,KAAK,CAAC;KACb;IAED,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;YACtB,OAAO,CAAC,CAAC,KAAK,CAAC;SAChB;QAED,OAAO,sBAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IACH,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;QACrC,OAAO,CAAC,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,CAAC;IACH,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YACX,MAAM,KAAK,CAAC;SACb;QACD,OAAO,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,OAAO,6BAAY,CACjB,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB;QACE,6BAAY,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;QAChE,6BAAY,CAAC,KAAK,EAAE,IAAI,CAAC;KAC1B,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,aAAa,CAG3B,EACA,UAAU,EACV,IAAI,EACJ,OAAO,GAKR;IACC,eAAe,CAAC,OAAO,CAAC,CAAC;IACzB,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;KAClD;IAED,MAAM,WAAW,GACf,OAAO,KAAK,oBAAoB,CAAC,EAAE;QACjC,CAAC,CAAC,mBAAmB,CAAC,IAAmB,CAAC;QAC1C,CAAC,CAAC,sBAAc,CAAC,UAAU,CACvB,IAAuB,EACvB,OAA4D,CAC7D,CAAC;IACR,MAAM,GAAG,GAAG,wBAAM,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC5C,OAAO,iBAAS,CAAC,0BAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC;AA5BD,sCA4BC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,qBAAqB,CAGnC,EACA,IAAI,EACJ,SAAS,EACT,OAAO,GAKR;IACC,eAAe,CAAC,OAAO,CAAC,CAAC;IACzB,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,SAAS,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,MAAM,WAAW,GACf,OAAO,KAAK,oBAAoB,CAAC,EAAE;QACjC,CAAC,CAAC,mBAAmB,CAAC,IAAmB,CAAC;QAC1C,CAAC,CAAC,sBAAc,CAAC,UAAU,CACvB,IAAuB,EACvB,OAA4D,CAC7D,CAAC;IACR,MAAM,SAAS,GAAG,wBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC3D,MAAM,MAAM,GAAG,iCAAe,CAAC,SAAS,CAAC,CAAC;IAC1C,OAAO,6BAAW,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AA7BD,sDA6BC", "sourcesContent": ["import {\n  bufferTo<PERSON><PERSON>,\n  ecsign,\n  keccak,\n  publicTo<PERSON><PERSON><PERSON>,\n  to<PERSON><PERSON>er,\n} from 'ethereumjs-util';\nimport { rawEncode, soliditySHA3 } from 'ethereumjs-abi';\n\nimport {\n  concatSig,\n  isNullish,\n  legacyTo<PERSON>uffer,\n  recover<PERSON><PERSON><PERSON><PERSON><PERSON>,\n} from './utils';\n\n/**\n * This is the message format used for `V1` of `signTypedData`.\n */\nexport type TypedDataV1 = TypedDataV1Field[];\n\n/**\n * This represents a single field in a `V1` `signTypedData` message.\n *\n * @property name - The name of the field.\n * @property type - The type of a field (must be a supported Solidity type).\n * @property value - The value of the field.\n */\nexport interface TypedDataV1Field {\n  name: string;\n  type: string;\n  value: any;\n}\n\n/**\n * Represents the version of `signTypedData` being used.\n *\n * V1 is based upon [an early version of EIP-712](https://github.com/ethereum/EIPs/pull/712/commits/21abe254fe0452d8583d5b132b1d7be87c0439ca)\n * that lacked some later security improvements, and should generally be neglected in favor of\n * later versions.\n *\n * V3 is based on EIP-712, except that arrays and recursive data structures are not supported.\n *\n * V4 is based on EIP-712, and includes full support of arrays and recursive data structures.\n */\nexport enum SignTypedDataVersion {\n  V1 = 'V1',\n  V3 = 'V3',\n  V4 = 'V4',\n}\n\nexport interface MessageTypeProperty {\n  name: string;\n  type: string;\n}\n\nexport interface MessageTypes {\n  EIP712Domain: MessageTypeProperty[];\n  [additionalProperties: string]: MessageTypeProperty[];\n}\n\n/**\n * This is the message format used for `signTypeData`, for all versions\n * except `V1`.\n *\n * @template T - The custom types used by this message.\n * @property types - The custom types used by this message.\n * @property primaryType - The type of the message.\n * @property domain - Signing domain metadata. The signing domain is the intended context for the\n * signature (e.g. the dapp, protocol, etc. that it's intended for). This data is used to\n * construct the domain seperator of the message.\n * @property domain.name - The name of the signing domain.\n * @property domain.version - The current major version of the signing domain.\n * @property domain.chainId - The chain ID of the signing domain.\n * @property domain.verifyingContract - The address of the contract that can verify the signature.\n * @property domain.salt - A disambiguating salt for the protocol.\n * @property message - The message to be signed.\n */\nexport interface TypedMessage<T extends MessageTypes> {\n  types: T;\n  primaryType: keyof T;\n  domain: {\n    name?: string;\n    version?: string;\n    chainId?: number;\n    verifyingContract?: string;\n    salt?: ArrayBuffer;\n  };\n  message: Record<string, unknown>;\n}\n\nexport const TYPED_MESSAGE_SCHEMA = {\n  type: 'object',\n  properties: {\n    types: {\n      type: 'object',\n      additionalProperties: {\n        type: 'array',\n        items: {\n          type: 'object',\n          properties: {\n            name: { type: 'string' },\n            type: { type: 'string' },\n          },\n          required: ['name', 'type'],\n        },\n      },\n    },\n    primaryType: { type: 'string' },\n    domain: { type: 'object' },\n    message: { type: 'object' },\n  },\n  required: ['types', 'primaryType', 'domain', 'message'],\n};\n\n/**\n * Validate that the given value is a valid version string.\n *\n * @param version - The version value to validate.\n * @param allowedVersions - A list of allowed versions. If omitted, all versions are assumed to be\n * allowed.\n */\nfunction validateVersion(\n  version: SignTypedDataVersion,\n  allowedVersions?: SignTypedDataVersion[],\n) {\n  if (!Object.keys(SignTypedDataVersion).includes(version)) {\n    throw new Error(`Invalid version: '${version}'`);\n  } else if (allowedVersions && !allowedVersions.includes(version)) {\n    throw new Error(\n      `SignTypedDataVersion not allowed: '${version}'. Allowed versions are: ${allowedVersions.join(\n        ', ',\n      )}`,\n    );\n  }\n}\n\n/**\n * Encode a single field.\n *\n * @param types - All type definitions.\n * @param name - The name of the field to encode.\n * @param type - The type of the field being encoded.\n * @param value - The value to encode.\n * @param version - The EIP-712 version the encoding should comply with.\n * @returns Encoded representation of the field.\n */\nfunction encodeField(\n  types: Record<string, MessageTypeProperty[]>,\n  name: string,\n  type: string,\n  value: any,\n  version: SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n): [type: string, value: any] {\n  validateVersion(version, [SignTypedDataVersion.V3, SignTypedDataVersion.V4]);\n\n  if (types[type] !== undefined) {\n    return [\n      'bytes32',\n      version === SignTypedDataVersion.V4 && value == null // eslint-disable-line no-eq-null\n        ? '0x0000000000000000000000000000000000000000000000000000000000000000'\n        : keccak(encodeData(type, value, types, version)),\n    ];\n  }\n\n  if (value === undefined) {\n    throw new Error(`missing value for field ${name} of type ${type}`);\n  }\n\n  if (type === 'bytes') {\n    return ['bytes32', keccak(value)];\n  }\n\n  if (type === 'string') {\n    // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n    if (typeof value === 'string') {\n      value = Buffer.from(value, 'utf8');\n    }\n    return ['bytes32', keccak(value)];\n  }\n\n  if (type.lastIndexOf(']') === type.length - 1) {\n    if (version === SignTypedDataVersion.V3) {\n      throw new Error(\n        'Arrays are unimplemented in encodeData; use V4 extension',\n      );\n    }\n    const parsedType = type.slice(0, type.lastIndexOf('['));\n    const typeValuePairs = value.map((item) =>\n      encodeField(types, name, parsedType, item, version),\n    );\n    return [\n      'bytes32',\n      keccak(\n        rawEncode(\n          typeValuePairs.map(([t]) => t),\n          typeValuePairs.map(([, v]) => v),\n        ),\n      ),\n    ];\n  }\n\n  return [type, value];\n}\n\n/**\n * Encodes an object by encoding and concatenating each of its members.\n *\n * @param primaryType - The root type.\n * @param data - The object to encode.\n * @param types - Type definitions for all types included in the message.\n * @param version - The EIP-712 version the encoding should comply with.\n * @returns An encoded representation of an object.\n */\nfunction encodeData(\n  primaryType: string,\n  data: Record<string, unknown>,\n  types: Record<string, MessageTypeProperty[]>,\n  version: SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n): Buffer {\n  validateVersion(version, [SignTypedDataVersion.V3, SignTypedDataVersion.V4]);\n\n  const encodedTypes = ['bytes32'];\n  const encodedValues: unknown[] = [hashType(primaryType, types)];\n\n  for (const field of types[primaryType]) {\n    if (version === SignTypedDataVersion.V3 && data[field.name] === undefined) {\n      continue;\n    }\n    const [type, value] = encodeField(\n      types,\n      field.name,\n      field.type,\n      data[field.name],\n      version,\n    );\n    encodedTypes.push(type);\n    encodedValues.push(value);\n  }\n\n  return rawEncode(encodedTypes, encodedValues);\n}\n\n/**\n * Encodes the type of an object by encoding a comma delimited list of its members.\n *\n * @param primaryType - The root type to encode.\n * @param types - Type definitions for all types included in the message.\n * @returns An encoded representation of the primary type.\n */\nfunction encodeType(\n  primaryType: string,\n  types: Record<string, MessageTypeProperty[]>,\n): string {\n  let result = '';\n  const unsortedDeps = findTypeDependencies(primaryType, types);\n  unsortedDeps.delete(primaryType);\n\n  const deps = [primaryType, ...Array.from(unsortedDeps).sort()];\n  for (const type of deps) {\n    const children = types[type];\n    if (!children) {\n      throw new Error(`No type definition specified: ${type}`);\n    }\n\n    result += `${type}(${types[type]\n      .map(({ name, type: t }) => `${t} ${name}`)\n      .join(',')})`;\n  }\n\n  return result;\n}\n\n/**\n * Finds all types within a type definition object.\n *\n * @param primaryType - The root type.\n * @param types - Type definitions for all types included in the message.\n * @param results - The current set of accumulated types.\n * @returns The set of all types found in the type definition.\n */\nfunction findTypeDependencies(\n  primaryType: string,\n  types: Record<string, MessageTypeProperty[]>,\n  results: Set<string> = new Set(),\n): Set<string> {\n  [primaryType] = primaryType.match(/^\\w*/u);\n  if (results.has(primaryType) || types[primaryType] === undefined) {\n    return results;\n  }\n\n  results.add(primaryType);\n\n  for (const field of types[primaryType]) {\n    findTypeDependencies(field.type, types, results);\n  }\n  return results;\n}\n\n/**\n * Hashes an object.\n *\n * @param primaryType - The root type.\n * @param data - The object to hash.\n * @param types - Type definitions for all types included in the message.\n * @param version - The EIP-712 version the encoding should comply with.\n * @returns The hash of the object.\n */\nfunction hashStruct(\n  primaryType: string,\n  data: Record<string, unknown>,\n  types: Record<string, MessageTypeProperty[]>,\n  version: SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n): Buffer {\n  validateVersion(version, [SignTypedDataVersion.V3, SignTypedDataVersion.V4]);\n\n  return keccak(encodeData(primaryType, data, types, version));\n}\n\n/**\n * Hashes the type of an object.\n *\n * @param primaryType - The root type to hash.\n * @param types - Type definitions for all types included in the message.\n * @returns The hash of the object type.\n */\nfunction hashType(\n  primaryType: string,\n  types: Record<string, MessageTypeProperty[]>,\n): Buffer {\n  return keccak(encodeType(primaryType, types));\n}\n\n/**\n * Removes properties from a message object that are not defined per EIP-712.\n *\n * @param data - The typed message object.\n * @returns The typed message object with only allowed fields.\n */\nfunction sanitizeData<T extends MessageTypes>(\n  data: TypedMessage<T>,\n): TypedMessage<T> {\n  const sanitizedData: Partial<TypedMessage<T>> = {};\n  for (const key in TYPED_MESSAGE_SCHEMA.properties) {\n    if (data[key]) {\n      sanitizedData[key] = data[key];\n    }\n  }\n\n  if ('types' in sanitizedData) {\n    sanitizedData.types = { EIP712Domain: [], ...sanitizedData.types };\n  }\n  return sanitizedData as Required<TypedMessage<T>>;\n}\n\n/**\n * Hash a typed message according to EIP-712. The returned message starts with the EIP-712 prefix,\n * which is \"1901\", followed by the hash of the domain separator, then the data (if any).\n * The result is hashed again and returned.\n *\n * This function does not sign the message. The resulting hash must still be signed to create an\n * EIP-712 signature.\n *\n * @param typedData - The typed message to hash.\n * @param version - The EIP-712 version the encoding should comply with.\n * @returns The hash of the typed message.\n */\nfunction eip712Hash<T extends MessageTypes>(\n  typedData: TypedMessage<T>,\n  version: SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n): Buffer {\n  validateVersion(version, [SignTypedDataVersion.V3, SignTypedDataVersion.V4]);\n\n  const sanitizedData = sanitizeData(typedData);\n  const parts = [Buffer.from('1901', 'hex')];\n  parts.push(\n    hashStruct(\n      'EIP712Domain',\n      sanitizedData.domain,\n      sanitizedData.types,\n      version,\n    ),\n  );\n\n  if (sanitizedData.primaryType !== 'EIP712Domain') {\n    parts.push(\n      hashStruct(\n        // TODO: Validate that this is a string, so this type cast can be removed.\n        sanitizedData.primaryType as string,\n        sanitizedData.message,\n        sanitizedData.types,\n        version,\n      ),\n    );\n  }\n  return keccak(Buffer.concat(parts));\n}\n\n/**\n * A collection of utility functions used for signing typed data.\n */\nexport const TypedDataUtils = {\n  encodeData,\n  encodeType,\n  findTypeDependencies,\n  hashStruct,\n  hashType,\n  sanitizeData,\n  eip712Hash,\n};\n\n/**\n * Generate the \"V1\" hash for the provided typed message.\n *\n * The hash will be generated in accordance with an earlier version of the EIP-712\n * specification. This hash is used in `signTypedData_v1`.\n *\n * @param typedData - The typed message.\n * @returns The '0x'-prefixed hex encoded hash representing the type of the provided message.\n */\nexport function typedSignatureHash(typedData: TypedDataV1Field[]): string {\n  const hashBuffer = _typedSignatureHash(typedData);\n  return bufferToHex(hashBuffer);\n}\n\n/**\n * Generate the \"V1\" hash for the provided typed message.\n *\n * The hash will be generated in accordance with an earlier version of the EIP-712\n * specification. This hash is used in `signTypedData_v1`.\n *\n * @param typedData - The typed message.\n * @returns The hash representing the type of the provided message.\n */\nfunction _typedSignatureHash(typedData: TypedDataV1): Buffer {\n  const error = new Error('Expect argument to be non-empty array');\n  if (\n    typeof typedData !== 'object' ||\n    !('length' in typedData) ||\n    !typedData.length\n  ) {\n    throw error;\n  }\n\n  const data = typedData.map(function (e) {\n    if (e.type !== 'bytes') {\n      return e.value;\n    }\n\n    return legacyToBuffer(e.value);\n  });\n  const types = typedData.map(function (e) {\n    return e.type;\n  });\n  const schema = typedData.map(function (e) {\n    if (!e.name) {\n      throw error;\n    }\n    return `${e.type} ${e.name}`;\n  });\n\n  return soliditySHA3(\n    ['bytes32', 'bytes32'],\n    [\n      soliditySHA3(new Array(typedData.length).fill('string'), schema),\n      soliditySHA3(types, data),\n    ],\n  );\n}\n\n/**\n * Sign typed data according to EIP-712. The signing differs based upon the `version`.\n *\n * V1 is based upon [an early version of EIP-712](https://github.com/ethereum/EIPs/pull/712/commits/21abe254fe0452d8583d5b132b1d7be87c0439ca)\n * that lacked some later security improvements, and should generally be neglected in favor of\n * later versions.\n *\n * V3 is based on [EIP-712](https://eips.ethereum.org/EIPS/eip-712), except that arrays and\n * recursive data structures are not supported.\n *\n * V4 is based on [EIP-712](https://eips.ethereum.org/EIPS/eip-712), and includes full support of\n * arrays and recursive data structures.\n *\n * @param options - The signing options.\n * @param options.privateKey - The private key to sign with.\n * @param options.data - The typed data to sign.\n * @param options.version - The signing version to use.\n * @returns The '0x'-prefixed hex encoded signature.\n */\nexport function signTypedData<\n  V extends SignTypedDataVersion,\n  T extends MessageTypes,\n>({\n  privateKey,\n  data,\n  version,\n}: {\n  privateKey: Buffer;\n  data: V extends 'V1' ? TypedDataV1 : TypedMessage<T>;\n  version: V;\n}): string {\n  validateVersion(version);\n  if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(privateKey)) {\n    throw new Error('Missing private key parameter');\n  }\n\n  const messageHash =\n    version === SignTypedDataVersion.V1\n      ? _typedSignatureHash(data as TypedDataV1)\n      : TypedDataUtils.eip712Hash(\n          data as TypedMessage<T>,\n          version as SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n        );\n  const sig = ecsign(messageHash, privateKey);\n  return concatSig(toBuffer(sig.v), sig.r, sig.s);\n}\n\n/**\n * Recover the address of the account that created the given EIP-712\n * signature. The version provided must match the version used to\n * create the signature.\n *\n * @param options - The signature recovery options.\n * @param options.data - The typed data that was signed.\n * @param options.signature - The '0x-prefixed hex encoded message signature.\n * @param options.version - The signing version to use.\n * @returns The '0x'-prefixed hex address of the signer.\n */\nexport function recoverTypedSignature<\n  V extends SignTypedDataVersion,\n  T extends MessageTypes,\n>({\n  data,\n  signature,\n  version,\n}: {\n  data: V extends 'V1' ? TypedDataV1 : TypedMessage<T>;\n  signature: string;\n  version: V;\n}): string {\n  validateVersion(version);\n  if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(signature)) {\n    throw new Error('Missing signature parameter');\n  }\n\n  const messageHash =\n    version === SignTypedDataVersion.V1\n      ? _typedSignatureHash(data as TypedDataV1)\n      : TypedDataUtils.eip712Hash(\n          data as TypedMessage<T>,\n          version as SignTypedDataVersion.V3 | SignTypedDataVersion.V4,\n        );\n  const publicKey = recoverPublicKey(messageHash, signature);\n  const sender = publicToAddress(publicKey);\n  return bufferToHex(sender);\n}\n"]}