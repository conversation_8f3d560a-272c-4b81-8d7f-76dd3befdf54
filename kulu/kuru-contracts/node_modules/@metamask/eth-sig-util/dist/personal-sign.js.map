{"version": 3, "file": "personal-sign.js", "sourceRoot": "", "sources": ["../src/personal-sign.ts"], "names": [], "mappings": ";;;AAAA,qDAMyB;AAEzB,mCAKiB;AAEjB;;;;;;;;;;GAUG;AACH,SAAgB,YAAY,CAAC,EAC3B,UAAU,EACV,IAAI,GAIL;IACC,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;IAED,MAAM,OAAO,GAAG,sBAAc,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,OAAO,GAAG,qCAAmB,CAAC,OAAO,CAAC,CAAC;IAC7C,MAAM,GAAG,GAAG,wBAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACxC,MAAM,UAAU,GAAG,iBAAS,CAAC,0BAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAO,UAAU,CAAC;AACpB,CAAC;AAlBD,oCAkBC;AAED;;;;;;;;GAQG;AACH,SAAgB,wBAAwB,CAAC,EACvC,IAAI,EACJ,SAAS,GAIV;IACC,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,SAAS,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,iCAAe,CAAC,SAAS,CAAC,CAAC;IAC1C,MAAM,SAAS,GAAG,6BAAW,CAAC,MAAM,CAAC,CAAC;IACtC,OAAO,SAAS,CAAC;AACnB,CAAC;AAjBD,4DAiBC;AAED;;;;;;;;GAQG;AACH,SAAgB,gBAAgB,CAAC,EAC/B,IAAI,EACJ,SAAS,GAIV;IACC,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,SAAS,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACnD,OAAO,KAAK,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1C,CAAC;AAfD,4CAeC;AAED;;;;;;GAMG;AACH,SAAS,eAAe,CAAC,OAAgB,EAAE,SAAiB;IAC1D,MAAM,WAAW,GAAG,qCAAmB,CAAC,sBAAc,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,OAAO,wBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAClD,CAAC", "sourcesContent": ["import {\n  bufferToHex,\n  ecsign,\n  hashPersonalMessage,\n  publicToAddress,\n  toBuffer,\n} from 'ethereumjs-util';\n\nimport {\n  concatSig,\n  isNullish,\n  legacyToBuffer,\n  recoverPublic<PERSON>ey,\n} from './utils';\n\n/**\n * Create an Ethereum-specific signature for a message.\n *\n * This function is equivalent to the `eth_sign` Ethereum JSON-RPC method as specified in EIP-1417,\n * as well as the MetaMask's `personal_sign` method.\n *\n * @param options - The personal sign options.\n * @param options.privateKey - The key to sign with.\n * @param options.data - The hex data to sign.\n * @returns The '0x'-prefixed hex encoded signature.\n */\nexport function personalSign({\n  privateKey,\n  data,\n}: {\n  privateKey: Buffer;\n  data: unknown;\n}): string {\n  if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(privateKey)) {\n    throw new Error('Missing privateKey parameter');\n  }\n\n  const message = legacyToBuffer(data);\n  const msgHash = hashPersonalMessage(message);\n  const sig = ecsign(msgHash, privateKey);\n  const serialized = concatSig(toBuffer(sig.v), sig.r, sig.s);\n  return serialized;\n}\n\n/**\n * Recover the address of the account used to create the given Ethereum signature. The message\n * must have been signed using the `personalSign` function, or an equivalent function.\n *\n * @param options - The signature recovery options.\n * @param options.data - The hex data that was signed.\n * @param options.signature - The '0x'-prefixed hex encoded message signature.\n * @returns The '0x'-prefixed hex encoded address of the message signer.\n */\nexport function recoverPersonalSignature({\n  data,\n  signature,\n}: {\n  data: unknown;\n  signature: string;\n}): string {\n  if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(signature)) {\n    throw new Error('Missing signature parameter');\n  }\n\n  const publicKey = getPublicKeyFor(data, signature);\n  const sender = publicToAddress(publicKey);\n  const senderHex = bufferToHex(sender);\n  return senderHex;\n}\n\n/**\n * Recover the public key of the account used to create the given Ethereum signature. The message\n * must have been signed using the `personalSign` function, or an equivalent function.\n *\n * @param options - The public key recovery options.\n * @param options.data - The hex data that was signed.\n * @param options.signature - The '0x'-prefixed hex encoded message signature.\n * @returns The '0x'-prefixed hex encoded public key of the message signer.\n */\nexport function extractPublicKey({\n  data,\n  signature,\n}: {\n  data: unknown;\n  signature: string;\n}): string {\n  if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(signature)) {\n    throw new Error('Missing signature parameter');\n  }\n\n  const publicKey = getPublicKeyFor(data, signature);\n  return `0x${publicKey.toString('hex')}`;\n}\n\n/**\n * Get the public key for the given signature and message.\n *\n * @param message - The message that was signed.\n * @param signature - The '0x'-prefixed hex encoded message signature.\n * @returns The public key of the signer.\n */\nfunction getPublicKeyFor(message: unknown, signature: string): Buffer {\n  const messageHash = hashPersonalMessage(legacyToBuffer(message));\n  return recoverPublicKey(messageHash, signature);\n}\n"]}