{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../src/utils.ts"], "names": [], "mappings": ";;;AAAA,qDASyB;AACzB,2CAAmE;AAEnE;;;;;;;;;;;;GAYG;AACH,SAAgB,aAAa,CAAC,SAAiB,EAAE,YAAoB;IACnE,IAAI,SAAS,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACxD,MAAM,IAAI,KAAK,CACb,gDAAgD,SAAS,EAAE,CAC5D,CAAC;KACH;IAED,IAAI,YAAY,GAAG,CAAC,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,4DAA4D,YAAY,EAAE,CAC3E,CAAC;KACH;IAED,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;AACtE,CAAC;AAdD,sCAcC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,KAAK;IAC7B,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC;AAFD,8BAEC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,KAAc;IAC3C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,wBAAW,CAAC,KAAK,CAAC;QACrD,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC,CAAC,0BAAQ,CAAC,KAAK,CAAC,CAAC;AACtB,CAAC;AAJD,wCAIC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;IACvD,MAAM,IAAI,GAAG,4BAAU,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,IAAI,GAAG,4BAAU,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,IAAI,GAAG,6BAAW,CAAC,CAAC,CAAC,CAAC;IAC5B,MAAM,IAAI,GAAG,aAAa,CAAC,4BAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACjE,MAAM,IAAI,GAAG,aAAa,CAAC,4BAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACjE,MAAM,IAAI,GAAG,2BAAc,CAAC,qBAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5C,OAAO,8BAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C,CAAC;AARD,8BAQC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAC9B,WAAmB,EACnB,SAAiB;IAEjB,MAAM,SAAS,GAAG,4BAAU,CAAC,SAAS,CAAC,CAAC;IACxC,OAAO,2BAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAND,4CAMC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,KAAsB;IAC9C,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,MAAM,GAAG,0BAAQ,CAAC,KAAK,CAAC,CAAC;QAC/B,KAAK,GAAG,6BAAW,CAAC,MAAM,CAAC,CAAC;KAC7B;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,GAAG,GAAG,gEAAgE,CAAC;QAC3E,GAAG,IAAI,aAAa,OAAO,KAAK,KAAK,KAAK,EAAE,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;KACtB;IAED,OAAO,8BAAY,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AAC3C,CAAC;AAjBD,8BAiBC", "sourcesContent": ["import {\n  addHexPrefix,\n  bufferToHex,\n  bufferToInt,\n  ecrecover,\n  fromRpcSig,\n  fromSigned,\n  toBuffer,\n  toUnsigned,\n} from 'ethereumjs-util';\nimport { intToHex, isHexString, stripHexPrefix } from 'ethjs-util';\n\n/**\n * Pads the front of the given hex string with zeroes until it reaches the\n * target length. If the input string is already longer than or equal to the\n * target length, it is returned unmodified.\n *\n * If the input string is \"0x\"-prefixed or not a hex string, an error will be\n * thrown.\n *\n * @param hexString - The hexadecimal string to pad with zeroes.\n * @param targetLength - The target length of the hexadecimal string.\n * @returns The input string front-padded with zeroes, or the original string\n * if it was already greater than or equal to to the target length.\n */\nexport function padWithZeroes(hexString: string, targetLength: number): string {\n  if (hexString !== '' && !/^[a-f0-9]+$/iu.test(hexString)) {\n    throw new Error(\n      `Expected an unprefixed hex string. Received: ${hexString}`,\n    );\n  }\n\n  if (targetLength < 0) {\n    throw new Error(\n      `Expected a non-negative integer target length. Received: ${targetLength}`,\n    );\n  }\n\n  return String.prototype.padStart.call(hexString, targetLength, '0');\n}\n\n/**\n * Returns `true` if the given value is nullish.\n *\n * @param value - The value being checked.\n * @returns Whether the value is nullish.\n */\nexport function isNullish(value) {\n  return value === null || value === undefined;\n}\n\n/**\n * Convert a value to a Buffer. This function should be equivalent to the `toBuffer` function in\n * `ethereumjs-util@5.2.1`.\n *\n * @param value - The value to convert to a Buffer.\n * @returns The given value as a Buffer.\n */\nexport function legacyToBuffer(value: unknown) {\n  return typeof value === 'string' && !isHexString(value)\n    ? Buffer.from(value)\n    : toBuffer(value);\n}\n\n/**\n * Concatenate an extended ECDSA signature into a single '0x'-prefixed hex string.\n *\n * @param v - The 'v' portion of the signature.\n * @param r - The 'r' portion of the signature.\n * @param s - The 's' portion of the signature.\n * @returns The concatenated ECDSA signature as a '0x'-prefixed string.\n */\nexport function concatSig(v: Buffer, r: Buffer, s: Buffer): string {\n  const rSig = fromSigned(r);\n  const sSig = fromSigned(s);\n  const vSig = bufferToInt(v);\n  const rStr = padWithZeroes(toUnsigned(rSig).toString('hex'), 64);\n  const sStr = padWithZeroes(toUnsigned(sSig).toString('hex'), 64);\n  const vStr = stripHexPrefix(intToHex(vSig));\n  return addHexPrefix(rStr.concat(sStr, vStr));\n}\n\n/**\n * Recover the public key from the given signature and message hash.\n *\n * @param messageHash - The hash of the signed message.\n * @param signature - The signature.\n * @returns The public key of the signer.\n */\nexport function recoverPublicKey(\n  messageHash: Buffer,\n  signature: string,\n): Buffer {\n  const sigParams = fromRpcSig(signature);\n  return ecrecover(messageHash, sigParams.v, sigParams.r, sigParams.s);\n}\n\n/**\n * Normalize the input to a lower-cased '0x'-prefixed hex string.\n *\n * @param input - The value to normalize.\n * @returns The normalized value.\n */\nexport function normalize(input: number | string): string {\n  if (!input) {\n    return undefined;\n  }\n\n  if (typeof input === 'number') {\n    const buffer = toBuffer(input);\n    input = bufferToHex(buffer);\n  }\n\n  if (typeof input !== 'string') {\n    let msg = 'eth-sig-util.normalize() requires hex string or integer input.';\n    msg += ` received ${typeof input}: ${input}`;\n    throw new Error(msg);\n  }\n\n  return addHexPrefix(input.toLowerCase());\n}\n"]}