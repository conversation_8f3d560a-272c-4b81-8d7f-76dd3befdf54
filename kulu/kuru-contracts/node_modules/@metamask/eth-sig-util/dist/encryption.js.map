{"version": 3, "file": "encryption.js", "sourceRoot": "", "sources": ["../src/encryption.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,yDAA2C;AAE3C,mCAAoC;AASpC;;;;;;;;GAQG;AACH,SAAgB,OAAO,CAAC,EACtB,SAAS,EACT,IAAI,EACJ,OAAO,GAKR;IACC,IAAI,iBAAS,CAAC,SAAS,CAAC,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;SAAM,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,QAAQ,OAAO,EAAE;QACf,KAAK,0BAA0B,CAAC,CAAC;YAC/B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;aAC3D;YACD,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAE5C,wDAAwD;YACxD,IAAI,gBAAgB,CAAC;YACrB,IAAI;gBACF,gBAAgB,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACrD;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;aACnC;YAED,MAAM,mBAAmB,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAErD,UAAU;YACV,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAC/B,mBAAmB,EACnB,KAAK,EACL,gBAAgB,EAChB,gBAAgB,CAAC,SAAS,CAC3B,CAAC;YAEF,wBAAwB;YACxB,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,0BAA0B;gBACnC,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC;gBACnC,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACjE,UAAU,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC;aACpD,CAAC;YACF,4BAA4B;YAC5B,OAAO,MAAM,CAAC;SACf;QAED;YACE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;KAC5D;AACH,CAAC;AA1DD,0BA0DC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,aAAa,CAAC,EAC5B,SAAS,EACT,IAAI,EACJ,OAAO,GAKR;IACC,IAAI,iBAAS,CAAC,SAAS,CAAC,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;SAAM,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;SAAM,IAAI,iBAAS,CAAC,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,MAAM,sBAAsB,GAAG,CAAC,IAAI,EAAE,CAAC;IACvC,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAE5B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE;QAChD,8BAA8B;QAC9B,oCAAoC;QACpC,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;KACH;IAED,cAAc;IACd,MAAM,eAAe,GAAG;QACtB,IAAI;QACJ,OAAO,EAAE,EAAE;KACZ,CAAC;IAEF,oBAAoB;IACpB,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAClC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,EAC/B,OAAO,CACR,CAAC;IACF,MAAM,MAAM,GAAG,UAAU,GAAG,sBAAsB,CAAC;IACnD,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,wBAAwB;IACxB,IAAI,MAAM,GAAG,CAAC,EAAE;QACd,SAAS,GAAG,sBAAsB,GAAG,MAAM,GAAG,gBAAgB,CAAC,CAAC,mBAAmB;KACpF;IACD,eAAe,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAEhD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IACtD,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;AAC9D,CAAC;AAjDD,sCAiDC;AAED;;;;;;;GAOG;AACH,SAAgB,OAAO,CAAC,EACtB,aAAa,EACb,UAAU,GAIX;IACC,IAAI,iBAAS,CAAC,aAAa,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;KACpD;SAAM,IAAI,iBAAS,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;IAED,QAAQ,aAAa,CAAC,OAAO,EAAE;QAC7B,KAAK,0BAA0B,CAAC,CAAC;YAC/B,iCAAiC;YACjC,MAAM,4BAA4B,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;YAChE,MAAM,4BAA4B,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CACjE,4BAA4B,CAC7B,CAAC,SAAS,CAAC;YAEZ,iCAAiC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACnE,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAC1C,aAAa,CAAC,cAAc,CAC7B,CAAC;YAEF,UAAU;YACV,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CACpC,UAAU,EACV,KAAK,EACL,cAAc,EACd,4BAA4B,CAC7B,CAAC;YAEF,4BAA4B;YAC5B,IAAI,MAAM,CAAC;YACX,IAAI;gBACF,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;aAChD;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACvC;YAED,IAAI,MAAM,EAAE;gBACV,OAAO,MAAM,CAAC;aACf;YACD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACvC;QAED;YACE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC7D;AACH,CAAC;AArDD,0BAqDC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,EAC5B,aAAa,EACb,UAAU,GAIX;IACC,IAAI,iBAAS,CAAC,aAAa,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;KACpD;SAAM,IAAI,iBAAS,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;IAED,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3E,OAAO,eAAe,CAAC,IAAI,CAAC;AAC9B,CAAC;AAfD,sCAeC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,UAAkB;IACvD,MAAM,oBAAoB,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IACxD,MAAM,mBAAmB,GACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC;IACjE,OAAO,QAAQ,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AACpD,CAAC;AALD,wDAKC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,MAAc;IACpC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAChE,OAAO,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["import * as nacl from 'tweetnacl';\nimport * as naclUtil from 'tweetnacl-util';\n\nimport { isNullish } from './utils';\n\nexport interface EthEncryptedData {\n  version: string;\n  nonce: string;\n  ephemPublicKey: string;\n  ciphertext: string;\n}\n\n/**\n * Encrypt a message.\n *\n * @param options - The encryption options.\n * @param options.publicKey - The public key of the message recipient.\n * @param options.data - The message data.\n * @param options.version - The type of encryption to use.\n * @returns The encrypted data.\n */\nexport function encrypt({\n  publicKey,\n  data,\n  version,\n}: {\n  publicKey: string;\n  data: unknown;\n  version: string;\n}): EthEncryptedData {\n  if (isNullish(publicKey)) {\n    throw new Error('Missing publicKey parameter');\n  } else if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(version)) {\n    throw new Error('Missing version parameter');\n  }\n\n  switch (version) {\n    case 'x25519-xsalsa20-poly1305': {\n      if (typeof data !== 'string') {\n        throw new Error('Message data must be given as a string');\n      }\n      // generate ephemeral keypair\n      const ephemeralKeyPair = nacl.box.keyPair();\n\n      // assemble encryption parameters - from string to UInt8\n      let pubKeyUInt8Array;\n      try {\n        pubKeyUInt8Array = naclUtil.decodeBase64(publicKey);\n      } catch (err) {\n        throw new Error('Bad public key');\n      }\n\n      const msgParamsUInt8Array = naclUtil.decodeUTF8(data);\n      const nonce = nacl.randomBytes(nacl.box.nonceLength);\n\n      // encrypt\n      const encryptedMessage = nacl.box(\n        msgParamsUInt8Array,\n        nonce,\n        pubKeyUInt8Array,\n        ephemeralKeyPair.secretKey,\n      );\n\n      // handle encrypted data\n      const output = {\n        version: 'x25519-xsalsa20-poly1305',\n        nonce: naclUtil.encodeBase64(nonce),\n        ephemPublicKey: naclUtil.encodeBase64(ephemeralKeyPair.publicKey),\n        ciphertext: naclUtil.encodeBase64(encryptedMessage),\n      };\n      // return encrypted msg data\n      return output;\n    }\n\n    default:\n      throw new Error('Encryption type/version not supported');\n  }\n}\n\n/**\n * Encrypt a message in a way that obscures the message length.\n *\n * The message is padded to a multiple of 2048 before being encrypted so that the length of the\n * resulting encrypted message can't be used to guess the exact length of the original message.\n *\n * @param options - The encryption options.\n * @param options.publicKey - The public key of the message recipient.\n * @param options.data - The message data.\n * @param options.version - The type of encryption to use.\n * @returns The encrypted data.\n */\nexport function encryptSafely({\n  publicKey,\n  data,\n  version,\n}: {\n  publicKey: string;\n  data: unknown;\n  version: string;\n}): EthEncryptedData {\n  if (isNullish(publicKey)) {\n    throw new Error('Missing publicKey parameter');\n  } else if (isNullish(data)) {\n    throw new Error('Missing data parameter');\n  } else if (isNullish(version)) {\n    throw new Error('Missing version parameter');\n  }\n\n  const DEFAULT_PADDING_LENGTH = 2 ** 11;\n  const NACL_EXTRA_BYTES = 16;\n\n  if (typeof data === 'object' && 'toJSON' in data) {\n    // remove toJSON attack vector\n    // TODO, check all possible children\n    throw new Error(\n      'Cannot encrypt with toJSON property.  Please remove toJSON property',\n    );\n  }\n\n  // add padding\n  const dataWithPadding = {\n    data,\n    padding: '',\n  };\n\n  // calculate padding\n  const dataLength = Buffer.byteLength(\n    JSON.stringify(dataWithPadding),\n    'utf-8',\n  );\n  const modVal = dataLength % DEFAULT_PADDING_LENGTH;\n  let padLength = 0;\n  // Only pad if necessary\n  if (modVal > 0) {\n    padLength = DEFAULT_PADDING_LENGTH - modVal - NACL_EXTRA_BYTES; // nacl extra bytes\n  }\n  dataWithPadding.padding = '0'.repeat(padLength);\n\n  const paddedMessage = JSON.stringify(dataWithPadding);\n  return encrypt({ publicKey, data: paddedMessage, version });\n}\n\n/**\n * Decrypt a message.\n *\n * @param options - The decryption options.\n * @param options.encryptedData - The encrypted data.\n * @param options.privateKey - The private key to decrypt with.\n * @returns The decrypted message.\n */\nexport function decrypt({\n  encryptedData,\n  privateKey,\n}: {\n  encryptedData: EthEncryptedData;\n  privateKey: string;\n}): string {\n  if (isNullish(encryptedData)) {\n    throw new Error('Missing encryptedData parameter');\n  } else if (isNullish(privateKey)) {\n    throw new Error('Missing privateKey parameter');\n  }\n\n  switch (encryptedData.version) {\n    case 'x25519-xsalsa20-poly1305': {\n      // string to buffer to UInt8Array\n      const recieverPrivateKeyUint8Array = nacl_decodeHex(privateKey);\n      const recieverEncryptionPrivateKey = nacl.box.keyPair.fromSecretKey(\n        recieverPrivateKeyUint8Array,\n      ).secretKey;\n\n      // assemble decryption parameters\n      const nonce = naclUtil.decodeBase64(encryptedData.nonce);\n      const ciphertext = naclUtil.decodeBase64(encryptedData.ciphertext);\n      const ephemPublicKey = naclUtil.decodeBase64(\n        encryptedData.ephemPublicKey,\n      );\n\n      // decrypt\n      const decryptedMessage = nacl.box.open(\n        ciphertext,\n        nonce,\n        ephemPublicKey,\n        recieverEncryptionPrivateKey,\n      );\n\n      // return decrypted msg data\n      let output;\n      try {\n        output = naclUtil.encodeUTF8(decryptedMessage);\n      } catch (err) {\n        throw new Error('Decryption failed.');\n      }\n\n      if (output) {\n        return output;\n      }\n      throw new Error('Decryption failed.');\n    }\n\n    default:\n      throw new Error('Encryption type/version not supported.');\n  }\n}\n\n/**\n * Decrypt a message that has been encrypted using `encryptSafely`.\n *\n * @param options - The decryption options.\n * @param options.encryptedData - The encrypted data.\n * @param options.privateKey - The private key to decrypt with.\n * @returns The decrypted message.\n */\nexport function decryptSafely({\n  encryptedData,\n  privateKey,\n}: {\n  encryptedData: EthEncryptedData;\n  privateKey: string;\n}): string {\n  if (isNullish(encryptedData)) {\n    throw new Error('Missing encryptedData parameter');\n  } else if (isNullish(privateKey)) {\n    throw new Error('Missing privateKey parameter');\n  }\n\n  const dataWithPadding = JSON.parse(decrypt({ encryptedData, privateKey }));\n  return dataWithPadding.data;\n}\n\n/**\n * Get the encryption public key for the given key.\n *\n * @param privateKey - The private key to generate the encryption public key with.\n * @returns The encryption public key.\n */\nexport function getEncryptionPublicKey(privateKey: string): string {\n  const privateKeyUint8Array = nacl_decodeHex(privateKey);\n  const encryptionPublicKey =\n    nacl.box.keyPair.fromSecretKey(privateKeyUint8Array).publicKey;\n  return naclUtil.encodeBase64(encryptionPublicKey);\n}\n\n/**\n * Convert a hex string to the UInt8Array format used by nacl.\n *\n * @param msgHex - The string to convert.\n * @returns The converted string.\n */\nfunction nacl_decodeHex(msgHex: string): Uint8Array {\n  const msgBase64 = Buffer.from(msgHex, 'hex').toString('base64');\n  return naclUtil.decodeBase64(msgBase64);\n}\n"]}