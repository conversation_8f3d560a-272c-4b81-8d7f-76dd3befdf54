// @flow
// Generated using flowgen2

import type {HttpVerb} from 'http-basic/lib/HttpVerb';
type IncomingHttpHeaders = Object;
const GenericResponse = require('http-response-object');
import type {Options} from './Options';
import {ResponsePromise} from './ResponsePromise';
import type {RequestFn} from './RequestFn';

type Response = GenericResponse<Buffer | string>;

export type {HttpVerb};
export type {IncomingHttpHeaders as Headers};
export type {Options};
export {ResponsePromise};
export type {Response};
declare var fd: any;
export {fd as FormData};
declare var _default: RequestFn;
export default _default;
