// @flow
// Generated using flowgen2

const GenericResponse = require('http-response-object');
type IncomingHttpHeaders = Object;
import type {Options} from './Options';
import {ResponsePromise} from './ResponsePromise';
import type {RequestFn} from './RequestFn';
import type {HttpVerb} from 'http-basic';
const FormData = require('form-data');

type Response = GenericResponse<Buffer | string>;

export type {HttpVerb};
export type {IncomingHttpHeaders as Headers};
export type {Options};
export {ResponsePromise};
export type {Response};
export {FormData};
declare var _default: RequestFn;
export default _default;
