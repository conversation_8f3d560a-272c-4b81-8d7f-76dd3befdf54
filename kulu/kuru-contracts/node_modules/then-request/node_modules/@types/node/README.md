# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for Node.js (http://nodejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v8.

### Additional Details
 * Last updated: Wed, 28 Oct 2020 18:55:52 GMT
 * Dependencies: none
 * Global values: `<PERSON><PERSON><PERSON>`, `NodeJS`, `SlowBuffer`, `__dirname`, `__filename`, `clearImmediate`, `clearInterval`, `clearTimeout`, `console`, `exports`, `global`, `module`, `process`, `require`, `setImmediate`, `setInterval`, `setTimeout`

# Credits
These definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [DefinitelyTyped](https://github.com/DefinitelyTyped), [Parambir Singh](https://github.com/parambirs), [<PERSON><PERSON><PERSON>](https://github.com/WilcoBakker), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/smac89), [<PERSON>lar<PERSON>](https://github.com/Flarna), [<PERSON><PERSON>](https://github.com/mwiktorczyk), [wwwy3y3](https://github.com/wwwy3y3), [Deividas Bakanas](https://github.com/DeividasBakanas), [Kelvin Jin](https://github.com/kjin), [Alvis HT Tang](https://github.com/alvis), [Sebastian Silbermann](https://github.com/eps1lon), [Hannes Magnusson](https://github.com/Hannes-Magnusson-CK), [Alberto Schiabel](https://github.com/jkomyno), [Huw](https://github.com/hoo29), [Nicolas Even](https://github.com/n-e), [Nikita Galkin](https://github.com/galkin), [Bruno Scheufler](https://github.com/brunoscheufler), [Hoàng Văn Khải](https://github.com/KSXGitHub), [Lishude](https://github.com/islishude), [Andrew Makarov](https://github.com/r3nya), [Jordi Oliveras Rovira](https://github.com/j-oliveras), and [Thanik Bhongbhibhat](https://github.com/bhongy).
