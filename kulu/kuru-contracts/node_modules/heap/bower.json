{"name": "heap", "version": "0.2.6", "description": "binary heap (priority queue) algorithms (ported from Python's heapq module)", "homepage": "https://github.com/qiao/heap.js", "keywords": ["algorithm", "data structure", "heap"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "main": "lib/heap.js", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git://github.com/qiao/heap.js.git"}, "licenses": [{"type": "PSF", "url": "http://docs.python.org/license.html"}]}