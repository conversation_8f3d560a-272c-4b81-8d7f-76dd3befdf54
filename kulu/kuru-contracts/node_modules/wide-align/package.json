{"name": "wide-align", "version": "1.1.3", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js", "version": "perl -pi -e 's/^(  \"version\": $ENV{npm_config_node_version}\").*?\",/$1abc\",/' package-lock.json ; git add package-lock.json"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": "<PERSON> <<EMAIL>> (http://re-becca.org/)", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/iarna/wide-align"}, "dependencies": {"string-width": "^1.0.2 || 2"}, "devDependencies": {"tap": "10 || 11 || 12"}, "files": ["align.js"]}