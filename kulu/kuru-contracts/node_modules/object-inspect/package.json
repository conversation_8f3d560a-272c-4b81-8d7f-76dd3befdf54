{"name": "object-inspect", "version": "1.12.3", "description": "string representations of objects in node and the browser", "main": "index.js", "sideEffects": false, "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "@pkgjs/support": "^0.0.6", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "error-cause": "^1.0.5", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-tostringtag": "^1.0.0", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "string.prototype.repeat": "^1.0.0", "tape": "^5.6.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "npx @pkgjs/support validate", "test": "npm run tests-only && npm run test:corejs", "tests-only": "nyc tape 'test/*.js'", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "support": true}