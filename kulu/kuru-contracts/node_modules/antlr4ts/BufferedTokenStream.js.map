{"version": 3, "file": "BufferedTokenStream.js", "sourceRoot": "", "sources": ["../../src/BufferedTokenStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,iCAAiC;AACjC,+CAA4C;AAC5C,8CAA2C;AAC3C,mCAAgC;AAChC,6CAAiD;AAEjD,mCAAgC;AAKhC;;;;;;;;;;GAUG;AACH,IAAa,mBAAmB,GAAhC,MAAa,mBAAmB;IAwC/B,YAAqB,WAAwB;QAjC7C;;;;WAIG;QACO,WAAM,GAAY,EAAE,CAAC;QAE/B;;;;;;;;;;WAUG;QACO,MAAC,GAAW,CAAC,CAAC,CAAC;QAEzB;;;;;;;;;;WAUG;QACO,eAAU,GAAY,KAAK,CAAC;QAGrC,IAAI,WAAW,IAAI,IAAI,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACjC,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED,2DAA2D;IAC3D,IAAI,WAAW,CAAC,WAAwB;QACvC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACZ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IACzB,CAAC;IAGD,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,CAAC,CAAC;IACf,CAAC;IAGM,IAAI;QACV,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,OAAO,CAAC,MAAc;QAC5B,0BAA0B;IAC3B,CAAC;IAGM,IAAI,CAAC,KAAa;QACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC3B,CAAC;IAGM,OAAO;QACb,IAAI,YAAqB,CAAC;QAC1B,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACpB,+DAA+D;gBAC/D,iCAAiC;gBACjC,YAAY,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;aAC/C;iBAAM;gBACN,mEAAmE;gBACnE,YAAY,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;aAC3C;SACD;aAAM;YACN,sBAAsB;YACtB,YAAY,GAAG,KAAK,CAAC;SACrB;QAED,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;IACF,CAAC;IAED;;;;;OAKG;IACO,IAAI,CAAC,CAAS;QACvB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,GAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,kCAAkC;QAC9E,6CAA6C;QAC7C,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,OAAO,OAAO,IAAI,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,CAAS;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,CAAC;SACT;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAU,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;gBAC5B,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;aAClC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,CAAC;aACb;SACD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,GAAG,CAAC,CAAS;QACnB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrC,MAAM,IAAI,UAAU,CAAC,cAAc,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC1F;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,mDAAmD;IAC5C,QAAQ,CAAC,KAAa,EAAE,IAAY;QAC1C,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;YAC1B,OAAO,EAAE,CAAC;SACV;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,MAAM,GAAY,IAAI,KAAK,EAAS,CAAC;QACzC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC/B,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;SAC9B;QAED,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBACzB,MAAM;aACN;YAED,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACf;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGM,EAAE,CAAC,CAAS;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,EAAE;YACX,OAAO,aAAK,CAAC,YAAY,CAAC;SAC1B;QAED,OAAO,KAAK,CAAC,IAAI,CAAC;IACnB,CAAC;IAES,KAAK,CAAC,CAAS;QACxB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC;IAIM,EAAE,CAAC,CAAS;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,MAAM,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,UAAU,CAAC,uCAAuC,CAAC,CAAC;SAC9D;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,KAAK,CAAC,CAAS;QACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,EAAE;YACZ,MAAM,IAAI,UAAU,CAAC,kCAAkC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,GAAW,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5B,mBAAmB;YACnB,yBAAyB;YACzB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC3C;QAED,6BAA6B;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;OAYG;IACO,eAAe,CAAC,CAAS;QAClC,OAAO,CAAC,CAAC;IACV,CAAC;IAES,QAAQ;QACjB,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAClB,IAAI,CAAC,KAAK,EAAE,CAAC;SACb;IACF,CAAC;IAES,KAAK;QACd,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAUD;;;OAGG;IACI,SAAS,CAAC,KAAc,EAAE,IAAa,EAAE,KAA4B;QAC3E,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,KAAK,KAAK,SAAS,EAAE;YACxB,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC,MAAM,CAAC;SACnB;aAAM,IAAI,IAAI,KAAK,SAAS,EAAE;YAC9B,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;SAC9B;QAED,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACvF,MAAM,IAAI,UAAU,CAAC,QAAQ,GAAG,KAAK,GAAG,WAAW,GAAG,IAAI,GAAG,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SACvG;QAED,IAAI,KAAK,GAAG,IAAI,EAAE;YACjB,OAAO,EAAE,CAAC;SACV;QAED,IAAI,KAAK,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;SAC1C;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SACrC;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,mDAAmD;QACnD,IAAI,cAAc,GAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;QACjE,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5E,OAAO,cAAc,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACO,kBAAkB,CAAC,CAAS,EAAE,OAAe;QACtD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;YACnB,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SACrB;QAED,IAAI,KAAK,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE;YACjC,IAAI,KAAK,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBAC7B,OAAO,CAAC,CAAC;aACT;YAED,CAAC,EAAE,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACb,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACvB;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;OAQG;IACO,sBAAsB,CAAC,CAAS,EAAE,OAAe;QAC1D,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;YACnB,oCAAoC;YACpC,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SACrB;QAED,OAAO,CAAC,IAAI,CAAC,EAAE;YACd,IAAI,KAAK,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,KAAK,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC1D,OAAO,CAAC,CAAC;aACT;YAED,CAAC,EAAE,CAAC;SACJ;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,UAAkB,EAAE,UAAkB,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACvD,MAAM,IAAI,UAAU,CAAC,UAAU,GAAG,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5E;QAED,IAAI,aAAa,GAAW,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,CAAC,EAAE,aAAK,CAAC,qBAAqB,CAAC,CAAC;QACjG,IAAI,EAAU,CAAC;QACf,IAAI,IAAI,GAAW,UAAU,GAAG,CAAC,CAAC;QAClC,sEAAsE;QACtE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACzB,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SACnB;aAAM;YACN,EAAE,GAAG,aAAa,CAAC;SACnB;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,UAAkB,EAAE,UAAkB,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACvD,MAAM,IAAI,UAAU,CAAC,UAAU,GAAG,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5E;QAED,IAAI,UAAU,KAAK,CAAC,EAAE;YACrB,wDAAwD;YACxD,OAAO,EAAE,CAAC;SACV;QAED,IAAI,aAAa,GAAW,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG,CAAC,EAAE,aAAK,CAAC,qBAAqB,CAAC,CAAC;QACrG,IAAI,aAAa,KAAK,UAAU,GAAG,CAAC,EAAE;YACrC,OAAO,EAAE,CAAC;SACV;QAED,0DAA0D;QAC1D,IAAI,IAAI,GAAW,aAAa,GAAG,CAAC,CAAC;QACrC,IAAI,EAAE,GAAW,UAAU,GAAG,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAES,gBAAgB,CAAC,IAAY,EAAE,EAAU,EAAE,OAAe;QACnE,IAAI,MAAM,GAAY,IAAI,KAAK,EAAS,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;gBACnB,IAAI,CAAC,CAAC,OAAO,KAAK,aAAK,CAAC,qBAAqB,EAAE;oBAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACf;aACD;iBAAM;gBACN,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE;oBAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACf;aACD;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IACpC,CAAC;IAQM,OAAO,CAAC,QAAiC;QAC/C,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC3B,QAAQ,GAAG,mBAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;SACzC;aAAM,IAAI,CAAC,CAAC,QAAQ,YAAY,mBAAQ,CAAC,EAAE;YAC3C,qGAAqG;YACrG,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC;SACnC;QAED,IAAI,KAAK,GAAW,QAAQ,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAW,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;YAC1B,OAAO,EAAE,CAAC;SACV;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC/B,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;SAC9B;QAED,IAAI,GAAG,GAAW,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBACzB,MAAM;aACN;YAED,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC;SACd;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;IAIM,gBAAgB,CAAC,KAAU,EAAE,IAAS;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SACpE;QAED,OAAO,EAAE,CAAC;IACX,CAAC;IAED,2CAA2C;IACpC,IAAI;QACV,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,MAAM,SAAS,GAAW,IAAI,CAAC;QAC/B,OAAO,IAAI,EAAE;YACZ,IAAI,OAAO,GAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,OAAO,GAAG,SAAS,EAAE;gBACxB,OAAO;aACP;SACD;IACF,CAAC;IAED,qDAAqD;IAC7C,eAAe,CAAC,CAAQ;QAC/B,OAAO,CAAC,YAAY,yBAAW,CAAC;IACjC,CAAC;IAED,qDAAqD;IAC7C,OAAO,CAAC,CAAM;QACrB,OAAO,CAAC,YAAY,yBAAW,CAAC;IACjC,CAAC;CACD,CAAA;AAxfA;IADC,oBAAO;yDAC0B;AA4ClC;IADC,qBAAQ;sDAGR;AAWD;IADC,qBAAQ;gDAGR;AAGD;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;+CAIR;AAGD;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;kDAwBR;AA8CD;IADC,qBAAQ;8CAOR;AA2BD;IADC,qBAAQ;6CAQR;AAYD;IAFC,oBAAO;IACP,qBAAQ;6CAQR;AA4ND;IADC,qBAAQ;qDAGR;AAQD;IAFC,oBAAO;IACP,qBAAQ;kDA+BR;AAID;IAFC,oBAAO;IACP,qBAAQ;2DAOR;AAteW,mBAAmB;IAwClB,WAAA,oBAAO,CAAA;GAxCR,mBAAmB,CA6f/B;AA7fY,kDAAmB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:49.6074365-07:00\r\n\r\nimport * as assert from \"assert\";\r\nimport { CommonToken } from \"./CommonToken\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Lexer } from \"./Lexer\";\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenSource } from \"./TokenSource\";\r\nimport { TokenStream } from \"./TokenStream\";\r\nimport { WritableToken } from \"./WritableToken\";\r\n\r\n/**\r\n * This implementation of {@link TokenStream} loads tokens from a\r\n * {@link TokenSource} on-demand, and places the tokens in a buffer to provide\r\n * access to any previous token by index.\r\n *\r\n * This token stream ignores the value of {@link Token#getChannel}. If your\r\n * parser requires the token stream filter tokens to only those on a particular\r\n * channel, such as {@link Token#DEFAULT_CHANNEL} or\r\n * {@link Token#HIDDEN_CHANNEL}, use a filtering token stream such a\r\n * {@link CommonTokenStream}.\r\n */\r\nexport class BufferedTokenStream implements TokenStream {\r\n\t/**\r\n\t * The {@link TokenSource} from which tokens for this stream are fetched.\r\n\t */\r\n\t@NotNull\r\n\tprivate _tokenSource: TokenSource;\r\n\r\n\t/**\r\n\t * A collection of all tokens fetched from the token source. The list is\r\n\t * considered a complete view of the input once {@link #fetchedEOF} is set\r\n\t * to `true`.\r\n\t */\r\n\tprotected tokens: Token[] = [];\r\n\r\n\t/**\r\n\t * The index into {@link #tokens} of the current token (next token to\r\n\t * {@link #consume}). {@link #tokens}`[`{@link #p}`]` should be\r\n\t * {@link #LT LT(1)}.\r\n\t *\r\n\t * This field is set to -1 when the stream is first constructed or when\r\n\t * {@link #setTokenSource} is called, indicating that the first token has\r\n\t * not yet been fetched from the token source. For additional information,\r\n\t * see the documentation of {@link IntStream} for a description of\r\n\t * Initializing Methods.\r\n\t */\r\n\tprotected p: number = -1;\r\n\r\n\t/**\r\n\t * Indicates whether the {@link Token#EOF} token has been fetched from\r\n\t * {@link #tokenSource} and added to {@link #tokens}. This field improves\r\n\t * performance for the following cases:\r\n\t *\r\n\t * * {@link #consume}: The lookahead check in {@link #consume} to prevent\r\n\t *   consuming the EOF symbol is optimized by checking the values of\r\n\t *   {@link #fetchedEOF} and {@link #p} instead of calling {@link #LA}.\r\n\t * * {@link #fetch}: The check to prevent adding multiple EOF symbols into\r\n\t *   {@link #tokens} is trivial with this field.\r\n\t */\r\n\tprotected fetchedEOF: boolean = false;\r\n\r\n\tconstructor(@NotNull tokenSource: TokenSource) {\r\n\t\tif (tokenSource == null) {\r\n\t\t\tthrow new Error(\"tokenSource cannot be null\");\r\n\t\t}\r\n\r\n\t\tthis._tokenSource = tokenSource;\r\n\t}\r\n\r\n\t@Override\r\n\tget tokenSource(): TokenSource {\r\n\t\treturn this._tokenSource;\r\n\t}\r\n\r\n\t/** Reset this token stream by setting its token source. */\r\n\tset tokenSource(tokenSource: TokenSource) {\r\n\t\tthis._tokenSource = tokenSource;\r\n\t\tthis.tokens.length = 0;\r\n\t\tthis.p = -1;\r\n\t\tthis.fetchedEOF = false;\r\n\t}\r\n\r\n\t@Override\r\n\tget index(): number {\r\n\t\treturn this.p;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic mark(): number {\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic release(marker: number): void {\r\n\t\t// no resources to release\r\n\t}\r\n\r\n\t@Override\r\n\tpublic seek(index: number): void {\r\n\t\tthis.lazyInit();\r\n\t\tthis.p = this.adjustSeekIndex(index);\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn this.tokens.length;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic consume(): void {\r\n\t\tlet skipEofCheck: boolean;\r\n\t\tif (this.p >= 0) {\r\n\t\t\tif (this.fetchedEOF) {\r\n\t\t\t\t// the last token in tokens is EOF. skip check if p indexes any\r\n\t\t\t\t// fetched token except the last.\r\n\t\t\t\tskipEofCheck = this.p < this.tokens.length - 1;\r\n\t\t\t} else {\r\n\t\t\t\t// no EOF token in tokens. skip check if p indexes a fetched token.\r\n\t\t\t\tskipEofCheck = this.p < this.tokens.length;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// not yet initialized\r\n\t\t\tskipEofCheck = false;\r\n\t\t}\r\n\r\n\t\tif (!skipEofCheck && this.LA(1) === Token.EOF) {\r\n\t\t\tthrow new Error(\"cannot consume EOF\");\r\n\t\t}\r\n\r\n\t\tif (this.sync(this.p + 1)) {\r\n\t\t\tthis.p = this.adjustSeekIndex(this.p + 1);\r\n\t\t}\r\n\t}\r\n\r\n\t/** Make sure index `i` in tokens has a token.\r\n\t *\r\n\t * @returns `true` if a token is located at index `i`, otherwise\r\n\t *    `false`.\r\n\t * @see #get(int i)\r\n\t */\r\n\tprotected sync(i: number): boolean {\r\n\t\tassert(i >= 0);\r\n\t\tlet n: number = i - this.tokens.length + 1; // how many more elements we need?\r\n\t\t//System.out.println(\"sync(\"+i+\") needs \"+n);\r\n\t\tif (n > 0) {\r\n\t\t\tlet fetched: number = this.fetch(n);\r\n\t\t\treturn fetched >= n;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/** Add `n` elements to buffer.\r\n\t *\r\n\t * @returns The actual number of elements added to the buffer.\r\n\t */\r\n\tprotected fetch(n: number): number {\r\n\t\tif (this.fetchedEOF) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet t: Token = this.tokenSource.nextToken();\r\n\t\t\tif (this.isWritableToken(t)) {\r\n\t\t\t\tt.tokenIndex = this.tokens.length;\r\n\t\t\t}\r\n\r\n\t\t\tthis.tokens.push(t);\r\n\t\t\tif (t.type === Token.EOF) {\r\n\t\t\t\tthis.fetchedEOF = true;\r\n\t\t\t\treturn i + 1;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn n;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic get(i: number): Token {\r\n\t\tif (i < 0 || i >= this.tokens.length) {\r\n\t\t\tthrow new RangeError(\"token index \" + i + \" out of range 0..\" + (this.tokens.length - 1));\r\n\t\t}\r\n\r\n\t\treturn this.tokens[i];\r\n\t}\r\n\r\n\t/** Get all tokens from start..stop inclusively. */\r\n\tpublic getRange(start: number, stop: number): Token[] {\r\n\t\tif (start < 0 || stop < 0) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\tthis.lazyInit();\r\n\t\tlet subset: Token[] = new Array<Token>();\r\n\t\tif (stop >= this.tokens.length) {\r\n\t\t\tstop = this.tokens.length - 1;\r\n\t\t}\r\n\r\n\t\tfor (let i = start; i <= stop; i++) {\r\n\t\t\tlet t: Token = this.tokens[i];\r\n\t\t\tif (t.type === Token.EOF) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tsubset.push(t);\r\n\t\t}\r\n\r\n\t\treturn subset;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic LA(i: number): number {\r\n\t\tlet token = this.LT(i);\r\n\t\tif (!token) {\r\n\t\t\treturn Token.INVALID_TYPE;\r\n\t\t}\r\n\r\n\t\treturn token.type;\r\n\t}\r\n\r\n\tprotected tryLB(k: number): Token | undefined {\r\n\t\tif ((this.p - k) < 0) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn this.tokens[this.p - k];\r\n\t}\r\n\r\n\t@NotNull\r\n\t@Override\r\n\tpublic LT(k: number): Token {\r\n\t\tlet result = this.tryLT(k);\r\n\t\tif (result === undefined) {\r\n\t\t\tthrow new RangeError(\"requested lookback index out of range\");\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic tryLT(k: number): Token | undefined {\r\n\t\tthis.lazyInit();\r\n\t\tif (k === 0) {\r\n\t\t\tthrow new RangeError(\"0 is not a valid lookahead index\");\r\n\t\t}\r\n\r\n\t\tif (k < 0) {\r\n\t\t\treturn this.tryLB(-k);\r\n\t\t}\r\n\r\n\t\tlet i: number = this.p + k - 1;\r\n\t\tthis.sync(i);\r\n\t\tif (i >= this.tokens.length) {\r\n\t\t\t// return EOF token\r\n\t\t\t// EOF must be last token\r\n\t\t\treturn this.tokens[this.tokens.length - 1];\r\n\t\t}\r\n\r\n\t\t//\t\tif ( i>range ) range = i;\r\n\t\treturn this.tokens[i];\r\n\t}\r\n\r\n\t/**\r\n\t * Allowed derived classes to modify the behavior of operations which change\r\n\t * the current stream position by adjusting the target token index of a seek\r\n\t * operation. The default implementation simply returns `i`. If an\r\n\t * exception is thrown in this method, the current stream index should not be\r\n\t * changed.\r\n\t *\r\n\t * For example, {@link CommonTokenStream} overrides this method to ensure that\r\n\t * the seek target is always an on-channel token.\r\n\t *\r\n\t * @param i The target token index.\r\n\t * @returns The adjusted target token index.\r\n\t */\r\n\tprotected adjustSeekIndex(i: number): number {\r\n\t\treturn i;\r\n\t}\r\n\r\n\tprotected lazyInit(): void {\r\n\t\tif (this.p === -1) {\r\n\t\t\tthis.setup();\r\n\t\t}\r\n\t}\r\n\r\n\tprotected setup(): void {\r\n\t\tthis.sync(0);\r\n\t\tthis.p = this.adjustSeekIndex(0);\r\n\t}\r\n\r\n\tpublic getTokens(): Token[];\r\n\r\n\tpublic getTokens(start: number, stop: number): Token[];\r\n\r\n\tpublic getTokens(start: number, stop: number, types: Set<number>): Token[];\r\n\r\n\tpublic getTokens(start: number, stop: number, ttype: number): Token[];\r\n\r\n\t/** Given a start and stop index, return a `List` of all tokens in\r\n\t *  the token type `BitSet`.  Return an empty array if no tokens were found.  This\r\n\t *  method looks at both on and off channel tokens.\r\n\t */\r\n\tpublic getTokens(start?: number, stop?: number, types?: Set<number> | number): Token[] {\r\n\t\tthis.lazyInit();\r\n\r\n\t\tif (start === undefined) {\r\n\t\t\tassert(stop === undefined && types === undefined);\r\n\t\t\treturn this.tokens;\r\n\t\t} else if (stop === undefined) {\r\n\t\t\tstop = this.tokens.length - 1;\r\n\t\t}\r\n\r\n\t\tif (start < 0 || stop >= this.tokens.length || stop < 0 || start >= this.tokens.length) {\r\n\t\t\tthrow new RangeError(\"start \" + start + \" or stop \" + stop + \" not in 0..\" + (this.tokens.length - 1));\r\n\t\t}\r\n\r\n\t\tif (start > stop) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\tif (types === undefined) {\r\n\t\t\treturn this.tokens.slice(start, stop + 1);\r\n\t\t} else if (typeof types === \"number\") {\r\n\t\t\ttypes = new Set<number>().add(types);\r\n\t\t}\r\n\r\n\t\tlet typesSet = types;\r\n\r\n\t\t// list = tokens[start:stop]:{T t, t.type in types}\r\n\t\tlet filteredTokens: Token[] = this.tokens.slice(start, stop + 1);\r\n\t\tfilteredTokens = filteredTokens.filter((value) => typesSet.has(value.type));\r\n\r\n\t\treturn filteredTokens;\r\n\t}\r\n\r\n\t/**\r\n\t * Given a starting index, return the index of the next token on channel.\r\n\t * Return `i` if `tokens[i]` is on channel. Return the index of\r\n\t * the EOF token if there are no tokens on channel between `i` and\r\n\t * EOF.\r\n\t */\r\n\tprotected nextTokenOnChannel(i: number, channel: number): number {\r\n\t\tthis.sync(i);\r\n\t\tif (i >= this.size) {\r\n\t\t\treturn this.size - 1;\r\n\t\t}\r\n\r\n\t\tlet token: Token = this.tokens[i];\r\n\t\twhile (token.channel !== channel) {\r\n\t\t\tif (token.type === Token.EOF) {\r\n\t\t\t\treturn i;\r\n\t\t\t}\r\n\r\n\t\t\ti++;\r\n\t\t\tthis.sync(i);\r\n\t\t\ttoken = this.tokens[i];\r\n\t\t}\r\n\r\n\t\treturn i;\r\n\t}\r\n\r\n\t/**\r\n\t * Given a starting index, return the index of the previous token on\r\n\t * channel. Return `i` if `tokens[i]` is on channel. Return -1\r\n\t * if there are no tokens on channel between `i` and 0.\r\n\t *\r\n\t * If `i` specifies an index at or after the EOF token, the EOF token\r\n\t * index is returned. This is due to the fact that the EOF token is treated\r\n\t * as though it were on every channel.\r\n\t */\r\n\tprotected previousTokenOnChannel(i: number, channel: number): number {\r\n\t\tthis.sync(i);\r\n\t\tif (i >= this.size) {\r\n\t\t\t// the EOF token is on every channel\r\n\t\t\treturn this.size - 1;\r\n\t\t}\r\n\r\n\t\twhile (i >= 0) {\r\n\t\t\tlet token: Token = this.tokens[i];\r\n\t\t\tif (token.type === Token.EOF || token.channel === channel) {\r\n\t\t\t\treturn i;\r\n\t\t\t}\r\n\r\n\t\t\ti--;\r\n\t\t}\r\n\r\n\t\treturn i;\r\n\t}\r\n\r\n\t/** Collect all tokens on specified channel to the right of\r\n\t *  the current token up until we see a token on {@link Lexer#DEFAULT_TOKEN_CHANNEL} or\r\n\t *  EOF. If `channel` is `-1`, find any non default channel token.\r\n\t */\r\n\tpublic getHiddenTokensToRight(tokenIndex: number, channel: number = -1): Token[] {\r\n\t\tthis.lazyInit();\r\n\t\tif (tokenIndex < 0 || tokenIndex >= this.tokens.length) {\r\n\t\t\tthrow new RangeError(tokenIndex + \" not in 0..\" + (this.tokens.length - 1));\r\n\t\t}\r\n\r\n\t\tlet nextOnChannel: number = this.nextTokenOnChannel(tokenIndex + 1, Lexer.DEFAULT_TOKEN_CHANNEL);\r\n\t\tlet to: number;\r\n\t\tlet from: number = tokenIndex + 1;\r\n\t\t// if none onchannel to right, nextOnChannel=-1 so set to = last token\r\n\t\tif (nextOnChannel === -1) {\r\n\t\t\tto = this.size - 1;\r\n\t\t} else {\r\n\t\t\tto = nextOnChannel;\r\n\t\t}\r\n\r\n\t\treturn this.filterForChannel(from, to, channel);\r\n\t}\r\n\r\n\t/** Collect all tokens on specified channel to the left of\r\n\t *  the current token up until we see a token on {@link Lexer#DEFAULT_TOKEN_CHANNEL}.\r\n\t *  If `channel` is `-1`, find any non default channel token.\r\n\t */\r\n\tpublic getHiddenTokensToLeft(tokenIndex: number, channel: number = -1): Token[] {\r\n\t\tthis.lazyInit();\r\n\t\tif (tokenIndex < 0 || tokenIndex >= this.tokens.length) {\r\n\t\t\tthrow new RangeError(tokenIndex + \" not in 0..\" + (this.tokens.length - 1));\r\n\t\t}\r\n\r\n\t\tif (tokenIndex === 0) {\r\n\t\t\t// obviously no tokens can appear before the first token\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\tlet prevOnChannel: number = this.previousTokenOnChannel(tokenIndex - 1, Lexer.DEFAULT_TOKEN_CHANNEL);\r\n\t\tif (prevOnChannel === tokenIndex - 1) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\t// if none onchannel to left, prevOnChannel=-1 then from=0\r\n\t\tlet from: number = prevOnChannel + 1;\r\n\t\tlet to: number = tokenIndex - 1;\r\n\r\n\t\treturn this.filterForChannel(from, to, channel);\r\n\t}\r\n\r\n\tprotected filterForChannel(from: number, to: number, channel: number): Token[] {\r\n\t\tlet hidden: Token[] = new Array<Token>();\r\n\t\tfor (let i = from; i <= to; i++) {\r\n\t\t\tlet t: Token = this.tokens[i];\r\n\t\t\tif (channel === -1) {\r\n\t\t\t\tif (t.channel !== Lexer.DEFAULT_TOKEN_CHANNEL) {\r\n\t\t\t\t\thidden.push(t);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (t.channel === channel) {\r\n\t\t\t\t\thidden.push(t);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn hidden;\r\n\t}\r\n\r\n\t@Override\r\n\tget sourceName(): string {\r\n\t\treturn this.tokenSource.sourceName;\r\n\t}\r\n\r\n\t/** Get the text of all tokens in this buffer. */\r\n\tpublic getText(): string;\r\n\tpublic getText(interval: Interval): string;\r\n\tpublic getText(context: RuleContext): string;\r\n\t@NotNull\r\n\t@Override\r\n\tpublic getText(interval?: Interval | RuleContext): string {\r\n\t\tif (interval === undefined) {\r\n\t\t\tinterval = Interval.of(0, this.size - 1);\r\n\t\t} else if (!(interval instanceof Interval)) {\r\n\t\t\t// Note: the more obvious check for 'instanceof RuleContext' results in a circular dependency problem\r\n\t\t\tinterval = interval.sourceInterval;\r\n\t\t}\r\n\r\n\t\tlet start: number = interval.a;\r\n\t\tlet stop: number = interval.b;\r\n\t\tif (start < 0 || stop < 0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\tthis.fill();\r\n\t\tif (stop >= this.tokens.length) {\r\n\t\t\tstop = this.tokens.length - 1;\r\n\t\t}\r\n\r\n\t\tlet buf: string = \"\";\r\n\t\tfor (let i = start; i <= stop; i++) {\r\n\t\t\tlet t: Token = this.tokens[i];\r\n\t\t\tif (t.type === Token.EOF) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tbuf += t.text;\r\n\t\t}\r\n\r\n\t\treturn buf.toString();\r\n\t}\r\n\r\n\t@NotNull\r\n\t@Override\r\n\tpublic getTextFromRange(start: any, stop: any): string {\r\n\t\tif (this.isToken(start) && this.isToken(stop)) {\r\n\t\t\treturn this.getText(Interval.of(start.tokenIndex, stop.tokenIndex));\r\n\t\t}\r\n\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\t/** Get all tokens from lexer until EOF. */\r\n\tpublic fill(): void {\r\n\t\tthis.lazyInit();\r\n\t\tconst blockSize: number = 1000;\r\n\t\twhile (true) {\r\n\t\t\tlet fetched: number = this.fetch(blockSize);\r\n\t\t\tif (fetched < blockSize) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// TODO: Figure out a way to make this more flexible?\r\n\tprivate isWritableToken(t: Token): t is WritableToken {\r\n\t\treturn t instanceof CommonToken;\r\n\t}\r\n\r\n\t// TODO: Figure out a way to make this more flexible?\r\n\tprivate isToken(t: any): t is Token {\r\n\t\treturn t instanceof CommonToken;\r\n\t}\r\n}\r\n"]}