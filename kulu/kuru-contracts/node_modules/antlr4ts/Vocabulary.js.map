{"version": 3, "file": "Vocabulary.js", "sourceRoot": "", "sources": ["../../src/Vocabulary.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:59.4986610-07:00\r\n\r\n/**\r\n * This interface provides information about the vocabulary used by a\r\n * recognizer.\r\n *\r\n * @see Recognizer.vocabulary\r\n * <AUTHOR>\r\n */\r\nexport interface Vocabulary {\r\n\r\n\t/**\r\n\t * Returns the highest token type value. It can be used to iterate from\r\n\t * zero to that number, inclusively, thus querying all stored entries.\r\n\t * @returns the highest token type value\r\n\t */\r\n\treadonly maxTokenType: number;\r\n\r\n\t/**\r\n\t * Gets the string literal associated with a token type. The string returned\r\n\t * by this method, when not `undefined`, can be used unaltered in a parser\r\n\t * grammar to represent this token type.\r\n\t *\r\n\t * The following table shows examples of lexer rules and the literal\r\n\t * names assigned to the corresponding token types.\r\n\t *\r\n\t * <table>\r\n\t *  <tr>\r\n\t *   <th>Rule</th>\r\n\t *   <th>Literal Name</th>\r\n\t *   <th>Java String Literal</th>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`THIS : 'this';`</td>\r\n\t *   <td>`'this'`</td>\r\n\t *   <td>`\"'this'\"`</td>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`SQUOTE : '\\'';`</td>\r\n\t *   <td>`'\\''`</td>\r\n\t *   <td>`\"'\\\\''\"`</td>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`ID : [A-Z]+;`</td>\r\n\t *   <td>n/a</td>\r\n\t *   <td>`undefined`</td>\r\n\t *  </tr>\r\n\t * </table>\r\n\t *\r\n\t * @param tokenType The token type.\r\n\t *\r\n\t * @returns The string literal associated with the specified token type, or\r\n\t * `undefined` if no string literal is associated with the type.\r\n\t */\r\n\tgetLiteralName(tokenType: number): string | undefined;\r\n\r\n\t/**\r\n\t * Gets the symbolic name associated with a token type. The string returned\r\n\t * by this method, when not `undefined`, can be used unaltered in a parser\r\n\t * grammar to represent this token type.\r\n\t *\r\n\t * This method supports token types defined by any of the following\r\n\t * methods:\r\n\t *\r\n\t * * Tokens created by lexer rules.\r\n\t * * Tokens defined in a `tokens{}` block in a lexer or parser\r\n\t *   grammar.\r\n\t * * The implicitly defined `EOF` token, which has the token type\r\n\t *   {@link Token#EOF}.\r\n\t *\r\n\t * The following table shows examples of lexer rules and the literal\r\n\t * names assigned to the corresponding token types.\r\n\t *\r\n\t * <table>\r\n\t *  <tr>\r\n\t *   <th>Rule</th>\r\n\t *   <th>Symbolic Name</th>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`THIS : 'this';`</td>\r\n\t *   <td>`THIS`</td>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`SQUOTE : '\\'';`</td>\r\n\t *   <td>`SQUOTE`</td>\r\n\t *  </tr>\r\n\t *  <tr>\r\n\t *   <td>`ID : [A-Z]+;`</td>\r\n\t *   <td>`ID`</td>\r\n\t *  </tr>\r\n\t * </table>\r\n\t *\r\n\t * @param tokenType The token type.\r\n\t *\r\n\t * @returns The symbolic name associated with the specified token type, or\r\n\t * `undefined` if no symbolic name is associated with the type.\r\n\t */\r\n\tgetSymbolicName(tokenType: number): string | undefined;\r\n\r\n\t/**\r\n\t * Gets the display name of a token type.\r\n\t *\r\n\t * ANTLR provides a default implementation of this method, but\r\n\t * applications are free to override the behavior in any manner which makes\r\n\t * sense for the application. The default implementation returns the first\r\n\t * result from the following list which produces a non-`undefined`\r\n\t * result.\r\n\t *\r\n\t * 1. The result of {@link #getLiteralName}\r\n\t * 1. The result of {@link #getSymbolicName}\r\n\t * 1. The result of {@link Integer#toString}\r\n\t *\r\n\t * @param tokenType The token type.\r\n\t *\r\n\t * @returns The display name of the token type, for use in error reporting or\r\n\t * other user-visible messages which reference specific token types.\r\n\t */\r\n\t//@NotNull\r\n\tgetDisplayName(tokenType: number): string;\r\n\r\n}\r\n"]}