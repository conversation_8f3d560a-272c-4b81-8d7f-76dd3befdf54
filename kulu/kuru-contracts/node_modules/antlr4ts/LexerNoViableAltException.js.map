{"version": 3, "file": "LexerNoViableAltException.js", "sourceRoot": "", "sources": ["../../src/LexerNoViableAltException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,iEAA8D;AAC9D,6CAAiD;AAGjD,8CAA2C;AAC3C,sCAAsC;AAEtC,IAAa,yBAAyB,GAAtC,MAAa,yBAA0B,SAAQ,2CAAoB;IASlE,YACC,KAAwB,EACf,KAAiB,EAC1B,UAAkB,EAClB,cAAwC;QACxC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,KAAK,CAAC,WAAyB,CAAC;IACxC,CAAC;IAGM,QAAQ;QACd,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACtE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACnF,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC/C;QAED,kHAAkH;QAClH,OAAO,8BAA8B,MAAM,IAAI,CAAC;IACjD,CAAC;CACD,CAAA;AAfA;IADC,qBAAQ;4DAGR;AAGD;IADC,qBAAQ;yDAUR;AA1CW,yBAAyB;IAWnC,WAAA,oBAAO,CAAA;GAXG,yBAAyB,CA2CrC;AA3CY,8DAAyB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:52.0961136-07:00\r\n\r\nimport { ATNConfigSet } from \"./atn/ATNConfigSet\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { Lex<PERSON> } from \"./Lexer\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport * as Utils from \"./misc/Utils\";\r\n\r\nexport class LexerNoViableAltException extends RecognitionException {\r\n\t//private static serialVersionUID: number =  -730999203913001726L;\r\n\r\n\t/** Matching attempted at what input index? */\r\n\tprivate _startIndex: number;\r\n\r\n\t/** Which configurations did we try at input.index that couldn't match input.LA(1)? */\r\n\tprivate _deadEndConfigs?: ATNConfigSet;\r\n\r\n\tconstructor(\r\n\t\tlexer: Lexer | undefined,\r\n\t\t@NotNull input: CharStream,\r\n\t\tstartIndex: number,\r\n\t\tdeadEndConfigs: ATNConfigSet | undefined) {\r\n\t\tsuper(lexer, input);\r\n\t\tthis._startIndex = startIndex;\r\n\t\tthis._deadEndConfigs = deadEndConfigs;\r\n\t}\r\n\r\n\tget startIndex(): number {\r\n\t\treturn this._startIndex;\r\n\t}\r\n\r\n\tget deadEndConfigs(): ATNConfigSet | undefined {\r\n\t\treturn this._deadEndConfigs;\r\n\t}\r\n\r\n\t@Override\r\n\tget inputStream(): CharStream {\r\n\t\treturn super.inputStream as CharStream;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tlet symbol = \"\";\r\n\t\tif (this._startIndex >= 0 && this._startIndex < this.inputStream.size) {\r\n\t\t\tsymbol = this.inputStream.getText(Interval.of(this._startIndex, this._startIndex));\r\n\t\t\tsymbol = Utils.escapeWhitespace(symbol, false);\r\n\t\t}\r\n\r\n\t\t// return String.format(Locale.getDefault(), \"%s('%s')\", LexerNoViableAltException.class.getSimpleName(), symbol);\r\n\t\treturn `LexerNoViableAltException('${symbol}')`;\r\n\t}\r\n}\r\n"]}