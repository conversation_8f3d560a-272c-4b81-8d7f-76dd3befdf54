{"version": 3, "file": "ATNType.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNType.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;;GAIG;AACH,IAAkB,OAYjB;AAZD,WAAkB,OAAO;IAExB;;OAEG;IACH,uCAAK,CAAA;IAEL;;OAEG;IACH,yCAAM,CAAA;AAEP,CAAC,EAZiB,OAAO,GAAP,eAAO,KAAP,eAAO,QAYxB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.6094030-07:00\r\n\r\n/**\r\n * Represents the type of recognizer an ATN applies to.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport const enum ATNType {\r\n\r\n\t/**\r\n\t * A lexer grammar.\r\n\t */\r\n\tLEXER,\r\n\r\n\t/**\r\n\t * A parser grammar.\r\n\t */\r\n\tPARSER,\r\n\r\n}\r\n"]}