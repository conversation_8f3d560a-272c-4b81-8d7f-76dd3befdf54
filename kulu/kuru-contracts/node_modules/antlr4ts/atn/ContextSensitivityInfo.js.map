{"version": 3, "file": "ContextSensitivityInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/ContextSensitivityInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,8CAAwC;AAIxC;;;;;;;;;;;;;;;;GAgBG;AACH,IAAa,sBAAsB,GAAnC,MAAa,sBAAuB,SAAQ,qCAAiB;IAC5D;;;;;;;;;;;OAWG;IACH,YACC,QAAgB,EACP,KAAqB,EACrB,KAAkB,EAC3B,UAAkB,EAClB,SAAiB;QAEjB,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;CACD,CAAA;AAtBY,sBAAsB;IAehC,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;GAhBG,sBAAsB,CAsBlC;AAtBY,wDAAsB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.1575933-07:00\r\n\r\nimport { DecisionEventInfo } from \"./DecisionEventInfo\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This class represents profiling event information for a context sensitivity.\r\n * Context sensitivities are decisions where a particular input resulted in an\r\n * SLL conflict, but LL prediction produced a single unique alternative.\r\n *\r\n * In some cases, the unique alternative identified by LL prediction is not\r\n * equal to the minimum represented alternative in the conflicting SLL\r\n * configuration set. Grammars and inputs which result in this scenario are\r\n * unable to use {@link PredictionMode#SLL}, which in turn means they cannot use\r\n * the two-stage parsing strategy to improve parsing performance for that\r\n * input.\r\n *\r\n * @see ParserATNSimulator#reportContextSensitivity\r\n * @see ParserErrorListener#reportContextSensitivity\r\n *\r\n * @since 4.3\r\n */\r\nexport class ContextSensitivityInfo extends DecisionEventInfo {\r\n\t/**\r\n\t * Constructs a new instance of the {@link ContextSensitivityInfo} class\r\n\t * with the specified detailed context sensitivity information.\r\n\t *\r\n\t * @param decision The decision number\r\n\t * @param state The final simulator state containing the unique\r\n\t * alternative identified by full-context prediction\r\n\t * @param input The input token stream\r\n\t * @param startIndex The start index for the current prediction\r\n\t * @param stopIndex The index at which the context sensitivity was\r\n\t * identified during full-context prediction\r\n\t */\r\n\tconstructor(\r\n\t\tdecision: number,\r\n\t\t@NotNull state: SimulatorState,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number) {\r\n\r\n\t\tsuper(decision, state, input, startIndex, stopIndex, true);\r\n\t}\r\n}\r\n"]}