{"version": 3, "file": "OrderedATNConfigSet.js", "sourceRoot": "", "sources": ["../../../src/atn/OrderedATNConfigSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,iDAA8C;AAC9C,8CAAyC;AAEzC;;;GAGG;AACH,MAAa,mBAAoB,SAAQ,2BAAY;IAIpD,YAAY,GAAkB,EAAE,QAAkB;QACjD,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SACrB;aAAM;YACN,KAAK,EAAE,CAAC;SACR;IACF,CAAC;IAGM,KAAK,CAAC,QAAiB;QAC7B,IAAI,IAAI,GAAwB,IAAI,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACxE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAClB;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAGS,MAAM,CAAC,CAAY;QAC5B,6FAA6F;QAC7F,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;IACxC,CAAC;IAGS,QAAQ,CAAC,IAAe,EAAE,OAAuC,EAAE,KAAgB;QAC5F,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;CAED;AApBA;IADC,qBAAQ;gDAQR;AAGD;IADC,qBAAQ;iDAIR;AAGD;IADC,qBAAQ;mDAGR;AA/BF,kDAiCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.9444556-07:00\r\n\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class OrderedATNConfigSet extends ATNConfigSet {\r\n\r\n\tconstructor();\r\n\tconstructor(set: ATNConfigSet, readonly: boolean);\r\n\tconstructor(set?: ATNConfigSet, readonly?: boolean) {\r\n\t\tif (set != null && readonly != null) {\r\n\t\t\tsuper(set, readonly);\r\n\t\t} else {\r\n\t\t\tsuper();\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic clone(readonly: boolean): ATNConfigSet {\r\n\t\tlet copy: OrderedATNConfigSet = new OrderedATNConfigSet(this, readonly);\r\n\t\tif (!readonly && this.isReadOnly) {\r\n\t\t\tcopy.addAll(this);\r\n\t\t}\r\n\r\n\t\treturn copy;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected getKey(e: ATNConfig): { state: number, alt: number } {\r\n\t\t// This is a specially crafted key to ensure configurations are only merged if they are equal\r\n\t\treturn { state: 0, alt: e.hashCode() };\r\n\t}\r\n\r\n\t@Override\r\n\tprotected canMerge(left: ATNConfig, leftKey: { state: number, alt: number }, right: ATNConfig): boolean {\r\n\t\treturn left.equals(right);\r\n\t}\r\n\r\n}\r\n"]}