{"version": 3, "file": "PredictionContext.js", "sourceRoot": "", "sources": ["../../../src/atn/PredictionContext.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAGxD,2DAAwD;AACxD,2DAAwD;AACxD,2CAAwC;AAIxC,mDAAgD;AAChD,8CAAkD;AAElD,qEAAkE;AAKlE,iCAAiC;AAEjC,MAAM,YAAY,GAAW,CAAC,CAAC;AAE/B,MAAsB,iBAAiB;IAwBtC,YAAY,cAAsB;QACjC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACtC,CAAC;IAES,MAAM,CAAC,sBAAsB;QACtC,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACb,CAAC;IAES,MAAM,CAAC,uBAAuB,CAAC,MAAyB,EAAE,WAAmB;QACtF,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACvC,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACb,CAAC;IAES,MAAM,CAAC,iBAAiB,CAAC,OAA4B,EAAE,YAAsB;QACtF,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAEvD,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACvC;QAED,KAAK,IAAI,WAAW,IAAI,YAAY,EAAE;YACrC,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SAC5C;QAED,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACb,CAAC;IAeM,MAAM,CAAC,eAAe,CAAC,GAAQ,EAAE,YAAyB,EAAE,cAAuB,IAAI;QAC7F,IAAI,YAAY,CAAC,OAAO,EAAE;YACzB,OAAO,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC;SAClF;QAED,IAAI,MAAyB,CAAC;QAC9B,IAAI,YAAY,CAAC,OAAO,EAAE;YACzB,MAAM,GAAG,iBAAiB,CAAC,eAAe,CAAC,GAAG,EAAE,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;SACnF;aAAM;YACN,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC;SACpF;QAED,IAAI,KAAK,GAAa,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,UAAU,GAAmB,KAAK,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;QACvE,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,OAA0B;QACxD,OAAO,OAAO,CAAC,eAAe,EAAE,CAAC;IAClC,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,OAA0B;QAC3D,OAAO,OAAO,CAAC,kBAAkB,EAAE,CAAC;IACrC,CAAC;IAEM,MAAM,CAAC,IAAI,CAAU,QAA2B,EAAW,QAA2B,EAAW,eAAuC,+CAAsB,CAAC,QAAQ;QAC7K,IAAI,QAAQ,KAAK,QAAQ,EAAE;YAC1B,OAAO,QAAQ,CAAC;SAChB;QAED,IAAI,QAAQ,CAAC,OAAO,EAAE;YACrB,OAAO,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACzG;aAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;YAC5B,OAAO,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACzG;QAED,IAAI,YAAY,GAAW,QAAQ,CAAC,IAAI,CAAC;QACzC,IAAI,YAAY,GAAW,QAAQ,CAAC,IAAI,CAAC;QACzC,IAAI,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YAC1G,IAAI,MAAM,GAAsB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,IAAI,MAAM,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBACrC,OAAO,QAAQ,CAAC;aAChB;iBAAM,IAAI,MAAM,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC5C,OAAO,QAAQ,CAAC;aAChB;iBAAM;gBACN,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aACnD;SACD;QAED,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,WAAW,GAAwB,IAAI,KAAK,CAAoB,YAAY,GAAG,YAAY,CAAC,CAAC;QACjG,IAAI,gBAAgB,GAAa,IAAI,KAAK,CAAS,WAAW,CAAC,MAAM,CAAC,CAAC;QACvE,IAAI,SAAS,GAAW,CAAC,CAAC;QAC1B,IAAI,UAAU,GAAW,CAAC,CAAC;QAC3B,IAAI,aAAa,GAAY,IAAI,CAAC;QAClC,IAAI,cAAc,GAAY,IAAI,CAAC;QACnC,OAAO,SAAS,GAAG,YAAY,IAAI,UAAU,GAAG,YAAY,EAAE;YAC7D,IAAI,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;gBAC/E,WAAW,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtG,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC7D,aAAa,GAAG,aAAa,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACtF,cAAc,GAAG,cAAc,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACzF,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;aACb;iBAAM,IAAI,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;gBACpF,WAAW,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACnD,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC7D,cAAc,GAAG,KAAK,CAAC;gBACvB,SAAS,EAAE,CAAC;aACZ;iBAAM;gBACN,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;gBACjF,WAAW,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACpD,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAC9D,aAAa,GAAG,KAAK,CAAC;gBACtB,UAAU,EAAE,CAAC;aACb;YAED,KAAK,EAAE,CAAC;SACR;QAED,OAAO,SAAS,GAAG,YAAY,EAAE;YAChC,WAAW,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACnD,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7D,SAAS,EAAE,CAAC;YACZ,cAAc,GAAG,KAAK,CAAC;YACvB,KAAK,EAAE,CAAC;SACR;QAED,OAAO,UAAU,GAAG,YAAY,EAAE;YACjC,WAAW,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACpD,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC9D,UAAU,EAAE,CAAC;YACb,aAAa,GAAG,KAAK,CAAC;YACtB,KAAK,EAAE,CAAC;SACR;QAED,IAAI,aAAa,EAAE;YAClB,OAAO,QAAQ,CAAC;SAChB;aAAM,IAAI,cAAc,EAAE;YAC1B,OAAO,QAAQ,CAAC;SAChB;QAED,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE;YAC/B,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1C,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SACpD;QAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,+FAA+F;YAC/F,OAAO,iBAAiB,CAAC,UAAU,CAAC;SACpC;aAAM,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,IAAI,0BAA0B,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3E;aAAM;YACN,OAAO,IAAI,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;SACjE;IACF,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,OAA0B;QACpD,OAAO,OAAO,KAAK,iBAAiB,CAAC,WAAW,CAAC;IAClD,CAAC;IAEM,MAAM,CAAC,gBAAgB,CACpB,OAA0B,EAC1B,YAAkE,EAClE,OAA0C;QACnD,IAAI,OAAO,CAAC,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC;SACf;QAED,IAAI,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,QAAQ,EAAE;YACb,OAAO,QAAQ,CAAC;SAChB;QAED,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,QAAQ,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC/B,OAAO,QAAQ,CAAC;SAChB;QAED,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,IAAI,OAAO,GAAwB,IAAI,KAAK,CAAoB,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,GAAsB,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAChH,IAAI,OAAO,IAAI,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC/C,IAAI,CAAC,OAAO,EAAE;oBACb,OAAO,GAAG,IAAI,KAAK,CAAoB,OAAO,CAAC,IAAI,CAAC,CAAC;oBACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;wBACtC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;qBAClC;oBAED,OAAO,GAAG,IAAI,CAAC;iBACf;gBAED,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;aACpB;SACD;QAED,IAAI,CAAC,OAAO,EAAE;YACb,QAAQ,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC5D,OAAO,OAAO,CAAC;SACf;QAED,8FAA8F;QAC9F,IAAI,OAA0B,CAAC;QAC/B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,GAAG,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;SAChF;aAAM;YACN,IAAI,YAAY,GAAa,IAAI,KAAK,CAAS,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACtC,YAAY,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;aAC5C;YAED,OAAO,GAAG,IAAI,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChF;QAED,QAAQ,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,IAAI,OAAO,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,IAAI,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IAChB,CAAC;IAEM,mBAAmB,CAAC,aAAqB,EAAE,YAAoC;QACrF,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,YAAY,CAAC,CAAC;IAC/F,CAAC;IAIM,QAAQ,CAAC,WAAmB;QAClC,OAAO,IAAI,0BAA0B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC1D,CAAC;IAOM,QAAQ;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC5B,CAAC;IAKM,SAAS,CAAC,UAA4C,EAAE,YAAoB,EAAE,OAA0B,iBAAiB,CAAC,UAAU;QAC1I,IAAI,MAAM,GAAa,EAAE,CAAC;QAE1B,KAAK,EACL,KAAK,IAAI,IAAI,GAAG,CAAC,GAAI,IAAI,EAAE,EAAE;YAC5B,IAAI,MAAM,GAAW,CAAC,CAAC;YACvB,IAAI,IAAI,GAAY,IAAI,CAAC;YACzB,IAAI,CAAC,GAAsB,IAAI,CAAC;YAChC,IAAI,WAAW,GAAW,YAAY,CAAC;YACvC,IAAI,WAAW,GAAW,EAAE,CAAC;YAC7B,WAAW,IAAI,GAAG,CAAC;YACnB,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;gBAChC,IAAI,KAAK,GAAW,CAAC,CAAC;gBACtB,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE;oBACf,IAAI,IAAI,GAAW,CAAC,CAAC;oBACrB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;wBACpC,IAAI,EAAE,CAAC;qBACP;oBAED,IAAI,IAAI,GAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC3C,KAAK,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC;oBAChC,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;oBACnC,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE;wBACpB,SAAS,KAAK,CAAC;qBACf;oBAED,MAAM,IAAI,IAAI,CAAC;iBACf;gBAED,IAAI,UAAU,EAAE;oBACf,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;wBAC3B,iEAAiE;wBACjE,WAAW,IAAI,GAAG,CAAC;qBACnB;oBAED,IAAI,GAAG,GAAQ,UAAU,CAAC,GAAG,CAAC;oBAC9B,IAAI,CAAC,GAAa,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBAC1C,IAAI,QAAQ,GAAW,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBACzD,WAAW,IAAI,QAAQ,CAAC;iBACxB;qBAAM,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC,oBAAoB,EAAE;oBAC9E,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;wBACf,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC3B,iEAAiE;4BACjE,WAAW,IAAI,GAAG,CAAC;yBACnB;wBAED,WAAW,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;qBACvC;iBACD;gBAED,WAAW,GAAG,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACvB;YAED,WAAW,IAAI,GAAG,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAEzB,IAAI,IAAI,EAAE;gBACT,MAAM;aACN;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAvEA;IADC,qBAAQ;iDAGR;AAhLD;IAAoB,WAAA,oBAAO,CAAA,EAA+B,WAAA,oBAAO,CAAA,EAA+B,WAAA,oBAAO,CAAA;mCA0FtG;AAMD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;+CA0DR;AA5PF,8CAoVC;AAED,MAAM,sBAAuB,SAAQ,iBAAiB;IAGrD,YAAY,WAAoB;QAC/B,KAAK,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAGS,eAAe;QACxB,OAAO,IAAI,CAAC;IACb,CAAC;IAGS,kBAAkB;QAC3B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACjE,CAAC;IAGM,SAAS,CAAC,KAAa;QAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACxC,CAAC;IAGM,cAAc,CAAC,KAAa;QAClC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACxC,CAAC;IAGM,eAAe,CAAC,WAAmB;QACzC,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,mBAAmB,CAAC,aAAqB,EAAE,YAAoC;QACrF,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACnD,CAAC;IAGM,aAAa,CAAC,MAAyB,EAAE,YAAoC;QACnF,OAAO,MAAM,CAAC;IACf,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC;IACb,CAAC;IAGD,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,OAAO,IAAI,KAAK,CAAC,CAAC;IACnB,CAAC;IAGM,SAAS,CAAC,UAAe,EAAE,YAAoB,EAAE,IAAwB;QAC/E,OAAO,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;CAED;AA3DA;IADC,qBAAQ;6DAGR;AAGD;IADC,qBAAQ;gEAGR;AAGD;IADC,qBAAQ;uDAGR;AAGD;IADC,qBAAQ;4DAGR;AAGD;IADC,qBAAQ;6DAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;iEAGR;AAGD;IADC,qBAAQ;2DAGR;AAGD;IADC,qBAAQ;qDAGR;AAGD;IADC,qBAAQ;sDAGR;AAGD;IADC,qBAAQ;oDAGR;AAGD;IADC,qBAAQ;uDAGR;AAIF,IAAM,sBAAsB,GAA5B,MAAM,sBAAuB,SAAQ,iBAAiB;IAOrD,YAAsB,OAA4B,EAAE,YAAsB,EAAE,QAAiB;QAC5F,KAAK,CAAC,QAAQ,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;QAC9E,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,oBAAoB,EAAE,kDAAkD,CAAC,CAAC;QAElJ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;IAGM,SAAS,CAAC,KAAa;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAGM,cAAc,CAAC,KAAa;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAGM,eAAe,CAAC,WAAmB;QACzC,OAAO,eAAM,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACjC,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,KAAK,CAAC;IACd,CAAC;IAGD,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,iBAAiB,CAAC,oBAAoB,CAAC;IACnG,CAAC;IAGS,eAAe;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,QAAQ,GAAwB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,aAAa,GAAa,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzD,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC5C,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;QAC3D,OAAO,IAAI,sBAAsB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC5D,CAAC;IAGS,kBAAkB;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7E;aAAM;YACN,IAAI,QAAQ,GAAwB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnF,IAAI,aAAa,GAAa,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvF,OAAO,IAAI,sBAAsB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;SAC3D;IACF,CAAC;IAGM,aAAa,CAAC,MAAyB,EAAE,YAAoC;QACnF,OAAO,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC,CAAC;IACxG,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,OAA0B,EAAE,MAAyB,EAAE,OAA0C;QACjI,IAAI,MAAM,CAAC,OAAO,EAAE;YACnB,IAAI,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBAC3C,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACrB,OAAO,iBAAiB,CAAC,WAAW,CAAC;iBACrC;gBAED,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aACpC;YAED,OAAO,OAAO,CAAC;SACf;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SACjE;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,MAAM,EAAE;YACZ,IAAI,OAAO,CAAC,OAAO,EAAE;gBACpB,MAAM,GAAG,MAAM,CAAC;aAChB;iBAAM;gBACN,IAAI,WAAW,GAAW,OAAO,CAAC,IAAI,CAAC;gBACvC,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACrB,WAAW,EAAE,CAAC;iBACd;gBAED,IAAI,cAAc,GAAwB,IAAI,KAAK,CAAoB,WAAW,CAAC,CAAC;gBACpF,IAAI,mBAAmB,GAAa,IAAI,KAAK,CAAS,WAAW,CAAC,CAAC;gBACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;oBACrC,mBAAmB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;iBACnD;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;oBACrC,cAAc,CAAC,CAAC,CAAC,GAAG,sBAAsB,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;iBACpG;gBAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;oBAChC,MAAM,GAAG,IAAI,0BAA0B,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;iBACnF;qBAAM;oBACN,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClC,MAAM,GAAG,IAAI,sBAAsB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;iBACzE;gBAED,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACrB,MAAM,GAAG,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;iBAChD;aACD;YAED,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC7B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,CAAC,YAAY,sBAAsB,CAAC,EAAE;YAClD,OAAO,KAAK,CAAC;SACb;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrC,qCAAqC;YACrC,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,GAA2B,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,+BAAc,EAAuE,CAAC,CAAC;IAC1H,CAAC;IAEO,UAAU,CAAC,KAA6B,EAAE,OAAqF;QACtI,IAAI,YAAY,GAAwB,EAAE,CAAC;QAC3C,IAAI,aAAa,GAAwB,EAAE,CAAC;QAC5C,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO,IAAI,EAAE;YACZ,IAAI,WAAW,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;YACrC,IAAI,YAAY,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC;YACvC,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY,EAAE;gBAClC,MAAM;aACN;YAED,IAAI,QAAQ,GAAwE,IAAI,+CAAsB,CAAC,4CAA4C,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YACvL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC3B,SAAS;aACT;YAED,IAAI,QAAQ,GAAW,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,IAAI,QAAQ,KAAK,CAAC,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBACnC,OAAO,KAAK,CAAC;iBACb;gBAED,SAAS;aACT;YAED,IAAI,SAAS,GAAW,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YACxC,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACb;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;oBAClE,OAAO,KAAK,CAAC;iBACb;gBAED,IAAI,UAAU,GAAsB,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,WAAW,GAAsB,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,UAAU,CAAC,QAAQ,EAAE,KAAK,WAAW,CAAC,QAAQ,EAAE,EAAE;oBACrD,OAAO,KAAK,CAAC;iBACb;gBAED,IAAI,UAAU,KAAK,WAAW,EAAE;oBAC/B,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC9B,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAChC;aACD;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;CACD,CAAA;AAxMA;IADC,oBAAO;uDAC4B;AAGpC;IADC,oBAAO;4DACsB;AAY9B;IADC,qBAAQ;uDAGR;AAGD;IADC,qBAAQ;4DAGR;AAGD;IADC,qBAAQ;6DAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;qDAGR;AAGD;IADC,qBAAQ;sDAGR;AAGD;IADC,qBAAQ;6DAWR;AAGD;IADC,qBAAQ;gEAaR;AAGD;IADC,qBAAQ;2DAGR;AA0DD;IADC,qBAAQ;oDAeR;AArJI,sBAAsB;IAOb,WAAA,oBAAO,CAAA;GAPhB,sBAAsB,CA0M3B;AAED,IAAa,0BAA0B,GAAvC,MAAa,0BAA2B,SAAQ,iBAAiB;IAMhE,YAAqB,MAAyB,EAAE,WAAmB;QAClE,KAAK,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;QACtE,2HAA2H;QAC3H,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAGM,SAAS,CAAC,KAAa;QAC7B,sBAAsB;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAGM,cAAc,CAAC,KAAa;QAClC,sBAAsB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAGM,eAAe,CAAC,WAAmB;QACzC,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,CAAC,CAAC;IACV,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,KAAK,CAAC;IACd,CAAC;IAGD,IAAI,QAAQ;QACX,OAAO,KAAK,CAAC;IACd,CAAC;IAGM,aAAa,CAAC,MAAyB,EAAE,YAAoC;QACnF,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACjG,CAAC;IAGS,eAAe;QACxB,IAAI,OAAO,GAAwB,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,YAAY,GAAa,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;QACxF,OAAO,IAAI,sBAAsB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAC1D,CAAC;IAGS,kBAAkB;QAC3B,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,KAAK,IAAI,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,CAAC,YAAY,0BAA0B,CAAC,EAAE;YACtD,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,GAA+B,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,EAAE;YACzC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW;eACzC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;CACD,CAAA;AA3EA;IADC,oBAAO;0DACyB;AAWjC;IADC,qBAAQ;2DAIR;AAGD;IADC,qBAAQ;gEAIR;AAGD;IADC,qBAAQ;iEAGR;AAGD;IADC,qBAAQ;sDAGR;AAGD;IADC,qBAAQ;yDAGR;AAGD;IADC,qBAAQ;0DAGR;AAGD;IADC,qBAAQ;+DAGR;AAGD;IADC,qBAAQ;iEAKR;AAGD;IADC,qBAAQ;oEAGR;AAGD;IADC,qBAAQ;wDAeR;AA7EW,0BAA0B;IAMzB,WAAA,oBAAO,CAAA;GANR,0BAA0B,CA8EtC;AA9EY,gEAA0B;AAgFvC,WAAiB,iBAAiB;IACpB,6BAAW,GAAsB,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC;IACnE,4BAAU,GAAsB,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACjE,uCAAqB,GAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,sCAAoB,GAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAElE,MAAa,eAAgB,SAAQ,+BAAoD;QACxF;YACC,KAAK,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;KACD;IAJY,iCAAe,kBAI3B,CAAA;IAED,MAAa,0BAA0B;QAG9B,0BAA0B;YACjC,sBAAsB;QACvB,CAAC;QAGM,QAAQ,CAAC,GAAsB;YACrC,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC;QAGM,MAAM,CAAC,CAAoB,EAAE,CAAoB;YACvD,OAAO,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;;IAdsB,mCAAQ,GAA+B,IAAI,0BAA0B,EAAE,CAAC;IAO/F;QADC,qBAAQ;8DAGR;IAGD;QADC,qBAAQ;4DAGR;IAfW,4CAA0B,6BAgBtC,CAAA;AACF,CAAC,EA7BgB,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QA6BjC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.3812636-07:00\r\n\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { Arrays } from \"../misc/Arrays\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { EqualityComparator } from \"../misc/EqualityComparator\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { Equatable, JavaSet } from \"../misc/Stubs\";\r\nimport { PredictionContextCache } from \"./PredictionContextCache\";\r\nimport { Recognizer } from \"../Recognizer\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\n\r\nimport * as assert from \"assert\";\r\n\r\nconst INITIAL_HASH: number = 1;\r\n\r\nexport abstract class PredictionContext implements Equatable {\r\n\t/**\r\n\t * Stores the computed hash code of this {@link PredictionContext}. The hash\r\n\t * code is computed in parts to match the following reference algorithm.\r\n\t *\r\n\t * ```\r\n\t * private int referenceHashCode() {\r\n\t *   int hash = {@link MurmurHash#initialize MurmurHash.initialize}({@link #INITIAL_HASH});\r\n\t *\r\n\t *   for (int i = 0; i &lt; this.size; i++) {\r\n\t *     hash = {@link MurmurHash#update MurmurHash.update}(hash, {@link #getParent getParent}(i));\r\n\t *   }\r\n\t *\r\n\t *   for (int i = 0; i &lt; this.size; i++) {\r\n\t *     hash = {@link MurmurHash#update MurmurHash.update}(hash, {@link #getReturnState getReturnState}(i));\r\n\t *   }\r\n\t *\r\n\t *   hash = {@link MurmurHash#finish MurmurHash.finish}(hash, 2 * this.size);\r\n\t *   return hash;\r\n\t * }\r\n\t * ```\r\n\t */\r\n\tprivate readonly cachedHashCode: number;\r\n\r\n\tconstructor(cachedHashCode: number) {\r\n\t\tthis.cachedHashCode = cachedHashCode;\r\n\t}\r\n\r\n\tprotected static calculateEmptyHashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize(INITIAL_HASH);\r\n\t\thash = MurmurHash.finish(hash, 0);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\tprotected static calculateSingleHashCode(parent: PredictionContext, returnState: number): number {\r\n\t\tlet hash: number = MurmurHash.initialize(INITIAL_HASH);\r\n\t\thash = MurmurHash.update(hash, parent);\r\n\t\thash = MurmurHash.update(hash, returnState);\r\n\t\thash = MurmurHash.finish(hash, 2);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\tprotected static calculateHashCode(parents: PredictionContext[], returnStates: number[]): number {\r\n\t\tlet hash: number = MurmurHash.initialize(INITIAL_HASH);\r\n\r\n\t\tfor (let parent of parents) {\r\n\t\t\thash = MurmurHash.update(hash, parent);\r\n\t\t}\r\n\r\n\t\tfor (let returnState of returnStates) {\r\n\t\t\thash = MurmurHash.update(hash, returnState);\r\n\t\t}\r\n\r\n\t\thash = MurmurHash.finish(hash, 2 * parents.length);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\tpublic abstract readonly size: number;\r\n\r\n\tpublic abstract getReturnState(index: number): number;\r\n\r\n\tpublic abstract findReturnState(returnState: number): number;\r\n\r\n\t// @NotNull\r\n\tpublic abstract getParent(index: number): PredictionContext;\r\n\r\n\tprotected abstract addEmptyContext(): PredictionContext;\r\n\r\n\tprotected abstract removeEmptyContext(): PredictionContext;\r\n\r\n\tpublic static fromRuleContext(atn: ATN, outerContext: RuleContext, fullContext: boolean = true): PredictionContext {\r\n\t\tif (outerContext.isEmpty) {\r\n\t\t\treturn fullContext ? PredictionContext.EMPTY_FULL : PredictionContext.EMPTY_LOCAL;\r\n\t\t}\r\n\r\n\t\tlet parent: PredictionContext;\r\n\t\tif (outerContext._parent) {\r\n\t\t\tparent = PredictionContext.fromRuleContext(atn, outerContext._parent, fullContext);\r\n\t\t} else {\r\n\t\t\tparent = fullContext ? PredictionContext.EMPTY_FULL : PredictionContext.EMPTY_LOCAL;\r\n\t\t}\r\n\r\n\t\tlet state: ATNState = atn.states[outerContext.invokingState];\r\n\t\tlet transition: RuleTransition = state.transition(0) as RuleTransition;\r\n\t\treturn parent.getChild(transition.followState.stateNumber);\r\n\t}\r\n\r\n\tprivate static addEmptyContext(context: PredictionContext): PredictionContext {\r\n\t\treturn context.addEmptyContext();\r\n\t}\r\n\r\n\tprivate static removeEmptyContext(context: PredictionContext): PredictionContext {\r\n\t\treturn context.removeEmptyContext();\r\n\t}\r\n\r\n\tpublic static join(@NotNull context0: PredictionContext, @NotNull context1: PredictionContext, @NotNull contextCache: PredictionContextCache = PredictionContextCache.UNCACHED): PredictionContext {\r\n\t\tif (context0 === context1) {\r\n\t\t\treturn context0;\r\n\t\t}\r\n\r\n\t\tif (context0.isEmpty) {\r\n\t\t\treturn PredictionContext.isEmptyLocal(context0) ? context0 : PredictionContext.addEmptyContext(context1);\r\n\t\t} else if (context1.isEmpty) {\r\n\t\t\treturn PredictionContext.isEmptyLocal(context1) ? context1 : PredictionContext.addEmptyContext(context0);\r\n\t\t}\r\n\r\n\t\tlet context0size: number = context0.size;\r\n\t\tlet context1size: number = context1.size;\r\n\t\tif (context0size === 1 && context1size === 1 && context0.getReturnState(0) === context1.getReturnState(0)) {\r\n\t\t\tlet merged: PredictionContext = contextCache.join(context0.getParent(0), context1.getParent(0));\r\n\t\t\tif (merged === context0.getParent(0)) {\r\n\t\t\t\treturn context0;\r\n\t\t\t} else if (merged === context1.getParent(0)) {\r\n\t\t\t\treturn context1;\r\n\t\t\t} else {\r\n\t\t\t\treturn merged.getChild(context0.getReturnState(0));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet count: number = 0;\r\n\t\tlet parentsList: PredictionContext[] = new Array<PredictionContext>(context0size + context1size);\r\n\t\tlet returnStatesList: number[] = new Array<number>(parentsList.length);\r\n\t\tlet leftIndex: number = 0;\r\n\t\tlet rightIndex: number = 0;\r\n\t\tlet canReturnLeft: boolean = true;\r\n\t\tlet canReturnRight: boolean = true;\r\n\t\twhile (leftIndex < context0size && rightIndex < context1size) {\r\n\t\t\tif (context0.getReturnState(leftIndex) === context1.getReturnState(rightIndex)) {\r\n\t\t\t\tparentsList[count] = contextCache.join(context0.getParent(leftIndex), context1.getParent(rightIndex));\r\n\t\t\t\treturnStatesList[count] = context0.getReturnState(leftIndex);\r\n\t\t\t\tcanReturnLeft = canReturnLeft && parentsList[count] === context0.getParent(leftIndex);\r\n\t\t\t\tcanReturnRight = canReturnRight && parentsList[count] === context1.getParent(rightIndex);\r\n\t\t\t\tleftIndex++;\r\n\t\t\t\trightIndex++;\r\n\t\t\t} else if (context0.getReturnState(leftIndex) < context1.getReturnState(rightIndex)) {\r\n\t\t\t\tparentsList[count] = context0.getParent(leftIndex);\r\n\t\t\t\treturnStatesList[count] = context0.getReturnState(leftIndex);\r\n\t\t\t\tcanReturnRight = false;\r\n\t\t\t\tleftIndex++;\r\n\t\t\t} else {\r\n\t\t\t\tassert(context1.getReturnState(rightIndex) < context0.getReturnState(leftIndex));\r\n\t\t\t\tparentsList[count] = context1.getParent(rightIndex);\r\n\t\t\t\treturnStatesList[count] = context1.getReturnState(rightIndex);\r\n\t\t\t\tcanReturnLeft = false;\r\n\t\t\t\trightIndex++;\r\n\t\t\t}\r\n\r\n\t\t\tcount++;\r\n\t\t}\r\n\r\n\t\twhile (leftIndex < context0size) {\r\n\t\t\tparentsList[count] = context0.getParent(leftIndex);\r\n\t\t\treturnStatesList[count] = context0.getReturnState(leftIndex);\r\n\t\t\tleftIndex++;\r\n\t\t\tcanReturnRight = false;\r\n\t\t\tcount++;\r\n\t\t}\r\n\r\n\t\twhile (rightIndex < context1size) {\r\n\t\t\tparentsList[count] = context1.getParent(rightIndex);\r\n\t\t\treturnStatesList[count] = context1.getReturnState(rightIndex);\r\n\t\t\trightIndex++;\r\n\t\t\tcanReturnLeft = false;\r\n\t\t\tcount++;\r\n\t\t}\r\n\r\n\t\tif (canReturnLeft) {\r\n\t\t\treturn context0;\r\n\t\t} else if (canReturnRight) {\r\n\t\t\treturn context1;\r\n\t\t}\r\n\r\n\t\tif (count < parentsList.length) {\r\n\t\t\tparentsList = parentsList.slice(0, count);\r\n\t\t\treturnStatesList = returnStatesList.slice(0, count);\r\n\t\t}\r\n\r\n\t\tif (parentsList.length === 0) {\r\n\t\t\t// if one of them was EMPTY_LOCAL, it would be empty and handled at the beginning of the method\r\n\t\t\treturn PredictionContext.EMPTY_FULL;\r\n\t\t} else if (parentsList.length === 1) {\r\n\t\t\treturn new SingletonPredictionContext(parentsList[0], returnStatesList[0]);\r\n\t\t} else {\r\n\t\t\treturn new ArrayPredictionContext(parentsList, returnStatesList);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic static isEmptyLocal(context: PredictionContext): boolean {\r\n\t\treturn context === PredictionContext.EMPTY_LOCAL;\r\n\t}\r\n\r\n\tpublic static getCachedContext(\r\n\t\t@NotNull context: PredictionContext,\r\n\t\t@NotNull contextCache: Array2DHashMap<PredictionContext, PredictionContext>,\r\n\t\t@NotNull visited: PredictionContext.IdentityHashMap): PredictionContext {\r\n\t\tif (context.isEmpty) {\r\n\t\t\treturn context;\r\n\t\t}\r\n\r\n\t\tlet existing = visited.get(context);\r\n\t\tif (existing) {\r\n\t\t\treturn existing;\r\n\t\t}\r\n\r\n\t\texisting = contextCache.get(context);\r\n\t\tif (existing) {\r\n\t\t\tvisited.put(context, existing);\r\n\t\t\treturn existing;\r\n\t\t}\r\n\r\n\t\tlet changed: boolean = false;\r\n\t\tlet parents: PredictionContext[] = new Array<PredictionContext>(context.size);\r\n\t\tfor (let i = 0; i < parents.length; i++) {\r\n\t\t\tlet parent: PredictionContext = PredictionContext.getCachedContext(context.getParent(i), contextCache, visited);\r\n\t\t\tif (changed || parent !== context.getParent(i)) {\r\n\t\t\t\tif (!changed) {\r\n\t\t\t\t\tparents = new Array<PredictionContext>(context.size);\r\n\t\t\t\t\tfor (let j = 0; j < context.size; j++) {\r\n\t\t\t\t\t\tparents[j] = context.getParent(j);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tchanged = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tparents[i] = parent;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!changed) {\r\n\t\t\texisting = contextCache.putIfAbsent(context, context);\r\n\t\t\tvisited.put(context, existing != null ? existing : context);\r\n\t\t\treturn context;\r\n\t\t}\r\n\r\n\t\t// We know parents.length>0 because context.isEmpty is checked at the beginning of the method.\r\n\t\tlet updated: PredictionContext;\r\n\t\tif (parents.length === 1) {\r\n\t\t\tupdated = new SingletonPredictionContext(parents[0], context.getReturnState(0));\r\n\t\t} else {\r\n\t\t\tlet returnStates: number[] = new Array<number>(context.size);\r\n\t\t\tfor (let i = 0; i < context.size; i++) {\r\n\t\t\t\treturnStates[i] = context.getReturnState(i);\r\n\t\t\t}\r\n\r\n\t\t\tupdated = new ArrayPredictionContext(parents, returnStates, context.hashCode());\r\n\t\t}\r\n\r\n\t\texisting = contextCache.putIfAbsent(updated, updated);\r\n\t\tvisited.put(updated, existing || updated);\r\n\t\tvisited.put(context, existing || updated);\r\n\r\n\t\treturn updated;\r\n\t}\r\n\r\n\tpublic appendSingleContext(returnContext: number, contextCache: PredictionContextCache): PredictionContext {\r\n\t\treturn this.appendContext(PredictionContext.EMPTY_FULL.getChild(returnContext), contextCache);\r\n\t}\r\n\r\n\tpublic abstract appendContext(suffix: PredictionContext, contextCache: PredictionContextCache): PredictionContext;\r\n\r\n\tpublic getChild(returnState: number): PredictionContext {\r\n\t\treturn new SingletonPredictionContext(this, returnState);\r\n\t}\r\n\r\n\tpublic abstract readonly isEmpty: boolean;\r\n\r\n\tpublic abstract readonly hasEmpty: boolean;\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\treturn this.cachedHashCode;\r\n\t}\r\n\r\n\t// @Override\r\n\tpublic abstract equals(o: any): boolean;\r\n\r\n\tpublic toStrings(recognizer: Recognizer<any, any> | undefined, currentState: number, stop: PredictionContext = PredictionContext.EMPTY_FULL): string[] {\r\n\t\tlet result: string[] = [];\r\n\r\n\t\touter:\r\n\t\tfor (let perm = 0; ; perm++) {\r\n\t\t\tlet offset: number = 0;\r\n\t\t\tlet last: boolean = true;\r\n\t\t\tlet p: PredictionContext = this;\r\n\t\t\tlet stateNumber: number = currentState;\r\n\t\t\tlet localBuffer: string = \"\";\r\n\t\t\tlocalBuffer += \"[\";\r\n\t\t\twhile (!p.isEmpty && p !== stop) {\r\n\t\t\t\tlet index: number = 0;\r\n\t\t\t\tif (p.size > 0) {\r\n\t\t\t\t\tlet bits: number = 1;\r\n\t\t\t\t\twhile (((1 << bits) >>> 0) < p.size) {\r\n\t\t\t\t\t\tbits++;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet mask: number = ((1 << bits) >>> 0) - 1;\r\n\t\t\t\t\tindex = (perm >> offset) & mask;\r\n\t\t\t\t\tlast = last && index >= p.size - 1;\r\n\t\t\t\t\tif (index >= p.size) {\r\n\t\t\t\t\t\tcontinue outer;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\toffset += bits;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (recognizer) {\r\n\t\t\t\t\tif (localBuffer.length > 1) {\r\n\t\t\t\t\t\t// first char is '[', if more than that this isn't the first rule\r\n\t\t\t\t\t\tlocalBuffer += \" \";\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet atn: ATN = recognizer.atn;\r\n\t\t\t\t\tlet s: ATNState = atn.states[stateNumber];\r\n\t\t\t\t\tlet ruleName: string = recognizer.ruleNames[s.ruleIndex];\r\n\t\t\t\t\tlocalBuffer += ruleName;\r\n\t\t\t\t} else if (p.getReturnState(index) !== PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\t\tif (!p.isEmpty) {\r\n\t\t\t\t\t\tif (localBuffer.length > 1) {\r\n\t\t\t\t\t\t\t// first char is '[', if more than that this isn't the first rule\r\n\t\t\t\t\t\t\tlocalBuffer += \" \";\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tlocalBuffer += p.getReturnState(index);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tstateNumber = p.getReturnState(index);\r\n\t\t\t\tp = p.getParent(index);\r\n\t\t\t}\r\n\r\n\t\t\tlocalBuffer += \"]\";\r\n\t\t\tresult.push(localBuffer);\r\n\r\n\t\t\tif (last) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n}\r\n\r\nclass EmptyPredictionContext extends PredictionContext {\r\n\tprivate fullContext: boolean;\r\n\r\n\tconstructor(fullContext: boolean) {\r\n\t\tsuper(PredictionContext.calculateEmptyHashCode());\r\n\t\tthis.fullContext = fullContext;\r\n\t}\r\n\r\n\tget isFullContext(): boolean {\r\n\t\treturn this.fullContext;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected addEmptyContext(): PredictionContext {\r\n\t\treturn this;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected removeEmptyContext(): PredictionContext {\r\n\t\tthrow new Error(\"Cannot remove the empty context from itself.\");\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getParent(index: number): PredictionContext {\r\n\t\tthrow new Error(\"index out of bounds\");\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getReturnState(index: number): number {\r\n\t\tthrow new Error(\"index out of bounds\");\r\n\t}\r\n\r\n\t@Override\r\n\tpublic findReturnState(returnState: number): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic appendSingleContext(returnContext: number, contextCache: PredictionContextCache): PredictionContext {\r\n\t\treturn contextCache.getChild(this, returnContext);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic appendContext(suffix: PredictionContext, contextCache: PredictionContextCache): PredictionContext {\r\n\t\treturn suffix;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEmpty(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tget hasEmpty(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\treturn this === o;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toStrings(recognizer: any, currentState: number, stop?: PredictionContext): string[] {\r\n\t\treturn [\"[]\"];\r\n\t}\r\n\r\n}\r\n\r\nclass ArrayPredictionContext extends PredictionContext {\r\n\t@NotNull\r\n\tpublic parents: PredictionContext[];\r\n\r\n\t@NotNull\r\n\tpublic returnStates: number[];\r\n\r\n\tconstructor( @NotNull parents: PredictionContext[], returnStates: number[], hashCode?: number) {\r\n\t\tsuper(hashCode || PredictionContext.calculateHashCode(parents, returnStates));\r\n\t\tassert(parents.length === returnStates.length);\r\n\t\tassert(returnStates.length > 1 || returnStates[0] !== PredictionContext.EMPTY_FULL_STATE_KEY, \"Should be using PredictionContext.EMPTY instead.\");\r\n\r\n\t\tthis.parents = parents;\r\n\t\tthis.returnStates = returnStates;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getParent(index: number): PredictionContext {\r\n\t\treturn this.parents[index];\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getReturnState(index: number): number {\r\n\t\treturn this.returnStates[index];\r\n\t}\r\n\r\n\t@Override\r\n\tpublic findReturnState(returnState: number): number {\r\n\t\treturn Arrays.binarySearch(this.returnStates, returnState);\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn this.returnStates.length;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEmpty(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tget hasEmpty(): boolean {\r\n\t\treturn this.returnStates[this.returnStates.length - 1] === PredictionContext.EMPTY_FULL_STATE_KEY;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected addEmptyContext(): PredictionContext {\r\n\t\tif (this.hasEmpty) {\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\tlet parents2: PredictionContext[] = this.parents.slice(0);\r\n\t\tlet returnStates2: number[] = this.returnStates.slice(0);\r\n\t\tparents2.push(PredictionContext.EMPTY_FULL);\r\n\t\treturnStates2.push(PredictionContext.EMPTY_FULL_STATE_KEY);\r\n\t\treturn new ArrayPredictionContext(parents2, returnStates2);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected removeEmptyContext(): PredictionContext {\r\n\t\tif (!this.hasEmpty) {\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\tif (this.returnStates.length === 2) {\r\n\t\t\treturn new SingletonPredictionContext(this.parents[0], this.returnStates[0]);\r\n\t\t} else {\r\n\t\t\tlet parents2: PredictionContext[] = this.parents.slice(0, this.parents.length - 1);\r\n\t\t\tlet returnStates2: number[] = this.returnStates.slice(0, this.returnStates.length - 1);\r\n\t\t\treturn new ArrayPredictionContext(parents2, returnStates2);\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic appendContext(suffix: PredictionContext, contextCache: PredictionContextCache): PredictionContext {\r\n\t\treturn ArrayPredictionContext.appendContextImpl(this, suffix, new PredictionContext.IdentityHashMap());\r\n\t}\r\n\r\n\tprivate static appendContextImpl(context: PredictionContext, suffix: PredictionContext, visited: PredictionContext.IdentityHashMap): PredictionContext {\r\n\t\tif (suffix.isEmpty) {\r\n\t\t\tif (PredictionContext.isEmptyLocal(suffix)) {\r\n\t\t\t\tif (context.hasEmpty) {\r\n\t\t\t\t\treturn PredictionContext.EMPTY_LOCAL;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthrow new Error(\"what to do here?\");\r\n\t\t\t}\r\n\r\n\t\t\treturn context;\r\n\t\t}\r\n\r\n\t\tif (suffix.size !== 1) {\r\n\t\t\tthrow new Error(\"Appending a tree suffix is not yet supported.\");\r\n\t\t}\r\n\r\n\t\tlet result = visited.get(context);\r\n\t\tif (!result) {\r\n\t\t\tif (context.isEmpty) {\r\n\t\t\t\tresult = suffix;\r\n\t\t\t} else {\r\n\t\t\t\tlet parentCount: number = context.size;\r\n\t\t\t\tif (context.hasEmpty) {\r\n\t\t\t\t\tparentCount--;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet updatedParents: PredictionContext[] = new Array<PredictionContext>(parentCount);\r\n\t\t\t\tlet updatedReturnStates: number[] = new Array<number>(parentCount);\r\n\t\t\t\tfor (let i = 0; i < parentCount; i++) {\r\n\t\t\t\t\tupdatedReturnStates[i] = context.getReturnState(i);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let i = 0; i < parentCount; i++) {\r\n\t\t\t\t\tupdatedParents[i] = ArrayPredictionContext.appendContextImpl(context.getParent(i), suffix, visited);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (updatedParents.length === 1) {\r\n\t\t\t\t\tresult = new SingletonPredictionContext(updatedParents[0], updatedReturnStates[0]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tassert(updatedParents.length > 1);\r\n\t\t\t\t\tresult = new ArrayPredictionContext(updatedParents, updatedReturnStates);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (context.hasEmpty) {\r\n\t\t\t\t\tresult = PredictionContext.join(result, suffix);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tvisited.put(context, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (this === o) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(o instanceof ArrayPredictionContext)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (this.hashCode() !== o.hashCode()) {\r\n\t\t\t// can't be same if hash is different\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tlet other: ArrayPredictionContext = o;\r\n\t\treturn this.equalsImpl(other, new Array2DHashSet<PredictionContextCache.IdentityCommutativePredictionContextOperands>());\r\n\t}\r\n\r\n\tprivate equalsImpl(other: ArrayPredictionContext, visited: JavaSet<PredictionContextCache.IdentityCommutativePredictionContextOperands>): boolean {\r\n\t\tlet selfWorkList: PredictionContext[] = [];\r\n\t\tlet otherWorkList: PredictionContext[] = [];\r\n\t\tselfWorkList.push(this);\r\n\t\totherWorkList.push(other);\r\n\t\twhile (true) {\r\n\t\t\tlet currentSelf = selfWorkList.pop();\r\n\t\t\tlet currentOther = otherWorkList.pop();\r\n\t\t\tif (!currentSelf || !currentOther) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tlet operands: PredictionContextCache.IdentityCommutativePredictionContextOperands = new PredictionContextCache.IdentityCommutativePredictionContextOperands(currentSelf, currentOther);\r\n\t\t\tif (!visited.add(operands)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet selfSize: number = operands.x.size;\r\n\t\t\tif (selfSize === 0) {\r\n\t\t\t\tif (!operands.x.equals(operands.y)) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet otherSize: number = operands.y.size;\r\n\t\t\tif (selfSize !== otherSize) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let i = 0; i < selfSize; i++) {\r\n\t\t\t\tif (operands.x.getReturnState(i) !== operands.y.getReturnState(i)) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet selfParent: PredictionContext = operands.x.getParent(i);\r\n\t\t\t\tlet otherParent: PredictionContext = operands.y.getParent(i);\r\n\t\t\t\tif (selfParent.hashCode() !== otherParent.hashCode()) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (selfParent !== otherParent) {\r\n\t\t\t\t\tselfWorkList.push(selfParent);\r\n\t\t\t\t\totherWorkList.push(otherParent);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\nexport class SingletonPredictionContext extends PredictionContext {\r\n\r\n\t@NotNull\r\n\tpublic parent: PredictionContext;\r\n\tpublic returnState: number;\r\n\r\n\tconstructor(@NotNull parent: PredictionContext, returnState: number) {\r\n\t\tsuper(PredictionContext.calculateSingleHashCode(parent, returnState));\r\n\t\t// assert(returnState != PredictionContext.EMPTY_FULL_STATE_KEY && returnState != PredictionContext.EMPTY_LOCAL_STATE_KEY);\r\n\t\tthis.parent = parent;\r\n\t\tthis.returnState = returnState;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getParent(index: number): PredictionContext {\r\n\t\t// assert(index == 0);\r\n\t\treturn this.parent;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getReturnState(index: number): number {\r\n\t\t// assert(index == 0);\r\n\t\treturn this.returnState;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic findReturnState(returnState: number): number {\r\n\t\treturn this.returnState === returnState ? 0 : -1;\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn 1;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEmpty(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tget hasEmpty(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic appendContext(suffix: PredictionContext, contextCache: PredictionContextCache): PredictionContext {\r\n\t\treturn contextCache.getChild(this.parent.appendContext(suffix, contextCache), this.returnState);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected addEmptyContext(): PredictionContext {\r\n\t\tlet parents: PredictionContext[] = [this.parent, PredictionContext.EMPTY_FULL];\r\n\t\tlet returnStates: number[] = [this.returnState, PredictionContext.EMPTY_FULL_STATE_KEY];\r\n\t\treturn new ArrayPredictionContext(parents, returnStates);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected removeEmptyContext(): PredictionContext {\r\n\t\treturn this;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (o === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(o instanceof SingletonPredictionContext)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tlet other: SingletonPredictionContext = o;\r\n\t\tif (this.hashCode() !== other.hashCode()) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.returnState === other.returnState\r\n\t\t\t&& this.parent.equals(other.parent);\r\n\t}\r\n}\r\n\r\nexport namespace PredictionContext {\r\n\texport const EMPTY_LOCAL: PredictionContext = new EmptyPredictionContext(false);\r\n\texport const EMPTY_FULL: PredictionContext = new EmptyPredictionContext(true);\r\n\texport const EMPTY_LOCAL_STATE_KEY: number = -((1 << 31) >>> 0);\r\n\texport const EMPTY_FULL_STATE_KEY: number = ((1 << 31) >>> 0) - 1;\r\n\r\n\texport class IdentityHashMap extends Array2DHashMap<PredictionContext, PredictionContext> {\r\n\t\tconstructor() {\r\n\t\t\tsuper(IdentityEqualityComparator.INSTANCE);\r\n\t\t}\r\n\t}\r\n\r\n\texport class IdentityEqualityComparator implements EqualityComparator<PredictionContext> {\r\n\t\tpublic static readonly INSTANCE: IdentityEqualityComparator = new IdentityEqualityComparator();\r\n\r\n\t\tprivate IdentityEqualityComparator() {\r\n\t\t\t// intentionally empty\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(obj: PredictionContext): number {\r\n\t\t\treturn obj.hashCode();\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(a: PredictionContext, b: PredictionContext): boolean {\r\n\t\t\treturn a === b;\r\n\t\t}\r\n\t}\r\n}\r\n"]}