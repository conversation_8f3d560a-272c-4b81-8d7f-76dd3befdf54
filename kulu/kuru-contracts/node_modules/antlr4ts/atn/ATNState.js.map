{"version": 3, "file": "ATNState.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAOH,8CAAyC;AAGzC,MAAM,uBAAuB,GAAW,CAAC,CAAC;AAE1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuDG;AACH,MAAsB,QAAQ;IAA9B;QAKQ,gBAAW,GAAW,QAAQ,CAAC,oBAAoB,CAAC;QAEpD,cAAS,GAAW,CAAC,CAAC,CAAE,yCAAyC;QAEjE,2BAAsB,GAAY,KAAK,CAAC;QAE/C,2DAA2D;QACjD,gBAAW,GAAiB,EAAE,CAAC;QAE/B,yBAAoB,GAAiB,IAAI,CAAC,WAAW,CAAC;IA6HjE,CAAC;IAxHA;;;;OAIG;IACI,cAAc;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACH,IAAI,kBAAkB;QACrB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,gCAAgC;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW,CAAC;SAC1C;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,KAAK,CAAC;IACd,CAAC;IAGM,QAAQ;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAEM,cAAc;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAChC,CAAC;IAEM,aAAa,CAAC,CAAa,EAAE,KAAc;QACjD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,SAAS,CAAC;SAC1C;aACI,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,CAAC,SAAS,EAAE;YACrD,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,gDAAgD,CAAC,CAAC;SACpG;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtF,CAAC;IAEM,UAAU,CAAC,CAAS;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,CAAS,EAAE,CAAa;QAC5C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEM,gBAAgB,CAAC,KAAa;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAID,IAAI,yBAAyB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACpC,CAAC;IAEM,YAAY,CAAC,SAAiB;QACpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,WAAW,CAAC;IACvD,CAAC;IAED,IAAI,4BAA4B;QAC/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;IACzC,CAAC;IAEM,sBAAsB,CAAC,CAAS;QACtC,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IAEM,sBAAsB,CAAC,CAAa;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,KAAK,EAAc,CAAC;SACpD;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAEM,sBAAsB,CAAC,CAAS,EAAE,CAAa;QACrD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAEM,yBAAyB,CAAC,CAAS;QACzC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;CACD;AApGA;IADC,qBAAQ;wCAGR;AAGD;IADC,qBAAQ;sCAQR;AAOD;IADC,qBAAQ;wCAGR;AA5DF,4BA2IC;AAED,WAAiB,QAAQ;IACX,6BAAoB,GAAW,CAAC,CAAC,CAAC;AAChD,CAAC,EAFgB,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAExB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.4734328-07:00\r\n\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { Override } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\n\r\nconst INITIAL_NUM_TRANSITIONS: number = 4;\r\n\r\n/**\r\n * The following images show the relation of states and\r\n * {@link ATNState#transitions} for various grammar constructs.\r\n *\r\n * * Solid edges marked with an &#0949; indicate a required\r\n *   {@link EpsilonTransition}.\r\n *\r\n * * Dashed edges indicate locations where any transition derived from\r\n *   {@link Transition} might appear.\r\n *\r\n * * Dashed nodes are place holders for either a sequence of linked\r\n *   {@link BasicState} states or the inclusion of a block representing a nested\r\n *   construct in one of the forms below.\r\n *\r\n * * Nodes showing multiple outgoing alternatives with a `...` support\r\n *   any number of alternatives (one or more). Nodes without the `...` only\r\n *   support the exact number of alternatives shown in the diagram.\r\n *\r\n * <h2>Basic Blocks</h2>\r\n *\r\n * <h3>Rule</h3>\r\n *\r\n * <embed src=\"images/Rule.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h3>Block of 1 or more alternatives</h3>\r\n *\r\n * <embed src=\"images/Block.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h2>Greedy Loops</h2>\r\n *\r\n * <h3>Greedy Closure: `(...)*`</h3>\r\n *\r\n * <embed src=\"images/ClosureGreedy.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h3>Greedy Positive Closure: `(...)+`</h3>\r\n *\r\n * <embed src=\"images/PositiveClosureGreedy.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h3>Greedy Optional: `(...)?`</h3>\r\n *\r\n * <embed src=\"images/OptionalGreedy.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h2>Non-Greedy Loops</h2>\r\n *\r\n * <h3>Non-Greedy Closure: `(...)*?`</h3>\r\n *\r\n * <embed src=\"images/ClosureNonGreedy.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h3>Non-Greedy Positive Closure: `(...)+?`</h3>\r\n *\r\n * <embed src=\"images/PositiveClosureNonGreedy.svg\" type=\"image/svg+xml\"/>\r\n *\r\n * <h3>Non-Greedy Optional: `(...)??`</h3>\r\n *\r\n * <embed src=\"images/OptionalNonGreedy.svg\" type=\"image/svg+xml\"/>\r\n */\r\nexport abstract class ATNState {\r\n\r\n\t/** Which ATN are we in? */\r\n\tpublic atn?: ATN;\r\n\r\n\tpublic stateNumber: number = ATNState.INVALID_STATE_NUMBER;\r\n\r\n\tpublic ruleIndex: number = 0;  // at runtime, we don't have Rule objects\r\n\r\n\tpublic epsilonOnlyTransitions: boolean = false;\r\n\r\n\t/** Track the transitions emanating from this ATN state. */\r\n\tprotected transitions: Transition[] = [];\r\n\r\n\tprotected optimizedTransitions: Transition[] = this.transitions;\r\n\r\n\t/** Used to cache lookahead during parsing, not used during construction */\r\n\tpublic nextTokenWithinRule?: IntervalSet;\r\n\r\n\t/**\r\n\t * Gets the state number.\r\n\t *\r\n\t * @returns the state number\r\n\t */\r\n\tpublic getStateNumber(): number {\r\n\t\treturn this.stateNumber;\r\n\t}\r\n\r\n\t/**\r\n\t * For all states except {@link RuleStopState}, this returns the state\r\n\t * number. Returns -1 for stop states.\r\n\t *\r\n\t * @returns -1 for {@link RuleStopState}, otherwise the state number\r\n\t */\r\n\tget nonStopStateNumber(): number {\r\n\t\treturn this.getStateNumber();\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\treturn this.stateNumber;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\t// are these states same object?\r\n\t\tif (o instanceof ATNState) {\r\n\t\t\treturn this.stateNumber === o.stateNumber;\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget isNonGreedyExitState(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn String(this.stateNumber);\r\n\t}\r\n\r\n\tpublic getTransitions(): Transition[] {\r\n\t\treturn this.transitions.slice(0);\r\n\t}\r\n\r\n\tget numberOfTransitions(): number {\r\n\t\treturn this.transitions.length;\r\n\t}\r\n\r\n\tpublic addTransition(e: Transition, index?: number): void {\r\n\t\tif (this.transitions.length === 0) {\r\n\t\t\tthis.epsilonOnlyTransitions = e.isEpsilon;\r\n\t\t}\r\n\t\telse if (this.epsilonOnlyTransitions !== e.isEpsilon) {\r\n\t\t\tthis.epsilonOnlyTransitions = false;\r\n\t\t\tthrow new Error(\"ATN state \" + this.stateNumber + \" has both epsilon and non-epsilon transitions.\");\r\n\t\t}\r\n\r\n\t\tthis.transitions.splice(index !== undefined ? index : this.transitions.length, 0, e);\r\n\t}\r\n\r\n\tpublic transition(i: number): Transition {\r\n\t\treturn this.transitions[i];\r\n\t}\r\n\r\n\tpublic setTransition(i: number, e: Transition): void {\r\n\t\tthis.transitions[i] = e;\r\n\t}\r\n\r\n\tpublic removeTransition(index: number): Transition {\r\n\t\treturn this.transitions.splice(index, 1)[0];\r\n\t}\r\n\r\n\tpublic abstract readonly stateType: ATNStateType;\r\n\r\n\tget onlyHasEpsilonTransitions(): boolean {\r\n\t\treturn this.epsilonOnlyTransitions;\r\n\t}\r\n\r\n\tpublic setRuleIndex(ruleIndex: number): void {\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t}\r\n\r\n\tget isOptimized(): boolean {\r\n\t\treturn this.optimizedTransitions !== this.transitions;\r\n\t}\r\n\r\n\tget numberOfOptimizedTransitions(): number {\r\n\t\treturn this.optimizedTransitions.length;\r\n\t}\r\n\r\n\tpublic getOptimizedTransition(i: number): Transition {\r\n\t\treturn this.optimizedTransitions[i];\r\n\t}\r\n\r\n\tpublic addOptimizedTransition(e: Transition): void {\r\n\t\tif (!this.isOptimized) {\r\n\t\t\tthis.optimizedTransitions = new Array<Transition>();\r\n\t\t}\r\n\r\n\t\tthis.optimizedTransitions.push(e);\r\n\t}\r\n\r\n\tpublic setOptimizedTransition(i: number, e: Transition): void {\r\n\t\tif (!this.isOptimized) {\r\n\t\t\tthrow new Error(\"This ATNState is not optimized.\");\r\n\t\t}\r\n\r\n\t\tthis.optimizedTransitions[i] = e;\r\n\t}\r\n\r\n\tpublic removeOptimizedTransition(i: number): void {\r\n\t\tif (!this.isOptimized) {\r\n\t\t\tthrow new Error(\"This ATNState is not optimized.\");\r\n\t\t}\r\n\r\n\t\tthis.optimizedTransitions.splice(i, 1);\r\n\t}\r\n}\r\n\r\nexport namespace ATNState {\r\n\texport const INVALID_STATE_NUMBER: number = -1;\r\n}\r\n"]}