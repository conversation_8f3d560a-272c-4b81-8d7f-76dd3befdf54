"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ATNType = void 0;
// ConvertTo-TS run at 2016-10-04T11:26:27.6094030-07:00
/**
 * Represents the type of recognizer an ATN applies to.
 *
 * <AUTHOR>
 */
var ATNType;
(function (ATNType) {
    /**
     * A lexer grammar.
     */
    ATNType[ATNType["LEXER"] = 0] = "LEXER";
    /**
     * A parser grammar.
     */
    ATNType[ATNType["PARSER"] = 1] = "PARSER";
})(ATNType = exports.ATNType || (exports.ATNType = {}));
//# sourceMappingURL=ATNType.js.map