{"version": 3, "file": "RuleStopState.js", "sourceRoot": "", "sources": ["../../../src/atn/RuleStopState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAC9C,8CAAyC;AAEzC;;;;GAIG;AACH,MAAa,aAAc,SAAQ,mBAAQ;IAG1C,IAAI,kBAAkB;QACrB,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,SAAS,CAAC;IAC/B,CAAC;CAED;AATA;IADC,qBAAQ;uDAGR;AAGD;IADC,qBAAQ;8CAGR;AAVF,sCAYC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.7513856-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** The last node in the ATN for a rule, unless that rule is the start symbol.\r\n *  In that case, there is one transition to EOF. Later, we might encode\r\n *  references to all calls to this rule to compute FOLLOW sets for\r\n *  error handling.\r\n */\r\nexport class RuleStopState extends ATNState {\r\n\r\n\t@Override\r\n\tget nonStopStateNumber(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.RULE_STOP;\r\n\t}\r\n\r\n}\r\n"]}