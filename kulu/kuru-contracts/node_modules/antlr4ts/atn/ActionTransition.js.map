{"version": 3, "file": "ActionTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/ActionTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAkD;AAClD,6CAA0C;AAG1C,IAAa,gBAAgB,GAA7B,MAAa,gBAAiB,SAAQ,uBAAU;IAK/C,YAAqB,MAAgB,EAAE,SAAiB,EAAE,cAAsB,CAAC,CAAC,EAAE,iBAA0B,KAAK;QAClH,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACtC,CAAC;IAGD,IAAI,iBAAiB;QACpB,sBAA6B;IAC9B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,CAAC,wDAAwD;IACtE,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,KAAK,CAAC;IACd,CAAC;IAGM,QAAQ;QACd,OAAO,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;IAC5D,CAAC;CACD,CAAA;AAlBA;IADC,qBAAQ;yDAGR;AAGD;IADC,qBAAQ;iDAGR;AAGD;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;gDAGR;AA9BW,gBAAgB;IAKf,WAAA,oBAAO,CAAA;GALR,gBAAgB,CA+B5B;AA/BY,4CAAgB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:24.7363448-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\nexport class ActionTransition extends Transition {\r\n\tpublic ruleIndex: number;\r\n\tpublic actionIndex: number;\r\n\tpublic isCtxDependent: boolean;  // e.g., $i ref in action\r\n\r\n\tconstructor(@NotNull target: ATNState, ruleIndex: number, actionIndex: number = -1, isCtxDependent: boolean = false) {\r\n\t\tsuper(target);\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t\tthis.actionIndex = actionIndex;\r\n\t\tthis.isCtxDependent = isCtxDependent;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.ACTION;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEpsilon(): boolean {\r\n\t\treturn true; // we are to be ignored by analysis 'cept for predicates\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn \"action_\" + this.ruleIndex + \":\" + this.actionIndex;\r\n\t}\r\n}\r\n"]}