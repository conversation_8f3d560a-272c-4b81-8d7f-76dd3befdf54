{"version": 3, "file": "ATN.js", "sourceRoot": "", "sources": ["../../../src/atn/ATN.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AAIxD,oCAAiC;AACjC,qDAAkD;AAClD,iDAA8C;AAE9C,+CAA4C;AAC5C,8CAAwC;AACxC,+EAA4E;AAC5E,2DAAwD;AAKxD,oCAAiC;AAGjC,iCAAiC;AAEjC,MAAM;AACN,IAAa,GAAG,GAAhB,MAAa,GAAG;IA+Df,4DAA4D;IAC5D,YAAqB,WAAoB,EAAE,YAAoB;QA9D/C,WAAM,GAAe,EAAE,CAAC;QAExC;;;WAGG;QAEI,oBAAe,GAAoB,EAAE,CAAC;QAatC,yBAAoB,GAC1B,IAAI,GAAG,EAA4B,CAAC;QA4B9B,qBAAgB,GAAuB,EAAE,CAAC;QAEzC,iBAAY,GACnB,IAAI,+BAAc,CAAuC,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAGtF,kBAAa,GAAU,EAAE,CAAC;QAE1B,cAAS,GAAU,EAAE,CAAC;QAEtB,aAAQ,GAAwB,IAAI,GAAG,EAAkB,CAAC;QAIhE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;IAEM,QAAQ;QACd,IAAI,CAAC,aAAa,GAAG,IAAI,KAAK,CAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,CAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,IAAI,gBAAgB;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAC/B,CAAC;IAEM,gBAAgB,CAAC,OAA0B;QACjD,OAAO,qCAAiB,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,qCAAiB,CAAC,eAAe,EAAE,CAAC,CAAC;IAChH,CAAC;IAEM,gBAAgB;QACtB,MAAM,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAChG,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAmBM,UAAU,CAAC,CAAW,EAAE,GAAuB;QACrD,IAAI,GAAG,EAAE;YACR,IAAI,IAAI,GAAgB,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,IAAI,GAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC;SACZ;aAAM;YACN,IAAI,CAAC,CAAC,mBAAmB,EAAE;gBAC1B,OAAO,CAAC,CAAC,mBAAmB,CAAC;aAC7B;YAED,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,qCAAiB,CAAC,WAAW,CAAC,CAAC;YAC1E,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,CAAC,CAAC,mBAAmB,CAAC;SAC7B;IACF,CAAC;IAEM,QAAQ,CAAC,KAAe;QAC9B,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;QACjB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAEM,WAAW,CAAU,KAAe;QAC1C,qDAAqD;QACrD,IAAI,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACtC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC;QACxB,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;IAC/C,CAAC;IAEM,UAAU,CAAU,IAAY,EAAW,CAAmB;QACpE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,SAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAEM,mBAAmB,CAAU,CAAgB;QACnD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,SAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChD,OAAO,CAAC,CAAC,QAAQ,CAAC;IACnB,CAAC;IAEM,gBAAgB,CAAC,QAAgB;QACvC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACtC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,IAAI,iBAAiB;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IAEI,iBAAiB,CAAC,WAAmB,EAAE,OAAgC;QAC7E,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACzD,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;SAC9C;QAED,IAAI,GAAG,GAA4B,OAAO,CAAC;QAC3C,IAAI,CAAC,GAAa,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,SAAS,GAAgB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,QAAQ,GAAgB,IAAI,yBAAW,EAAE,CAAC;QAC9C,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3B,QAAQ,CAAC,MAAM,CAAC,aAAK,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,aAAa,IAAI,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YAClF,IAAI,aAAa,GAAa,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC7D,IAAI,EAAE,GAAmB,aAAa,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;YACvE,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YAC5C,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3B,QAAQ,CAAC,MAAM,CAAC,aAAK,CAAC,OAAO,CAAC,CAAC;YAC/B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC;SAClB;QAED,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YACtC,QAAQ,CAAC,GAAG,CAAC,aAAK,CAAC,GAAG,CAAC,CAAC;SACxB;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;CACD,CAAA;AA1OA;IADC,oBAAO;mCACgC;AAOxC;IADC,oBAAO;4CACqC;AAa7C;IADC,oBAAO;iDAE6B;AA4BrC;IADC,oBAAO;6CACyC;AAMjD;IADC,oBAAO;0CACyB;AAEjC;IADC,oBAAO;sCACqB;AAuD7B;IADC,oBAAO;qCAeP;AAQD;IAAoB,WAAA,oBAAO,CAAA;sCAM1B;AAED;IAAmB,WAAA,oBAAO,CAAA,EAAgB,WAAA,oBAAO,CAAA;qCAKhD;AAED;IAA4B,WAAA,oBAAO,CAAA;8CAKlC;AAkDD;IADC,oBAAO;4CA8BP;AA3OW,GAAG;IAgEF,WAAA,oBAAO,CAAA;GAhER,GAAG,CA4Of;AA5OY,kBAAG;AA8OhB,WAAiB,GAAG;IACN,sBAAkB,GAAW,CAAC,CAAC;AAC7C,CAAC,EAFgB,GAAG,GAAH,WAAG,KAAH,WAAG,QAEnB;AAhPY,kBAAG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:25.1063510-07:00\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNType } from \"./ATNType\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { InvalidState } from \"./InvalidState\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LL1Analyzer } from \"./LL1Analyzer\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { RuleStartState } from \"./RuleStartState\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\nimport { Token } from \"../Token\";\r\nimport { TokensStartState } from \"./TokensStartState\";\r\n\r\nimport * as assert from \"assert\";\r\n\r\n/** */\r\nexport class ATN {\r\n\t@NotNull\r\n\tpublic readonly states: ATNState[] = [];\r\n\r\n\t/** Each subrule/rule is a decision point and we must track them so we\r\n\t *  can go back later and build DFA predictors for them.  This includes\r\n\t *  all the rules, subrules, optional blocks, ()+, ()* etc...\r\n\t */\r\n\t@NotNull\r\n\tpublic decisionToState: DecisionState[] = [];\r\n\r\n\t/**\r\n\t * Maps from rule index to starting state number.\r\n\t */\r\n\tpublic ruleToStartState!: RuleStartState[];\r\n\r\n\t/**\r\n\t * Maps from rule index to stop state number.\r\n\t */\r\n\tpublic ruleToStopState!: RuleStopState[];\r\n\r\n\t@NotNull\r\n\tpublic modeNameToStartState: Map<string, TokensStartState> =\r\n\t\tnew Map<string, TokensStartState>();\r\n\r\n\t/**\r\n\t * The type of the ATN.\r\n\t */\r\n\tpublic grammarType: ATNType;\r\n\r\n\t/**\r\n\t * The maximum value for any symbol recognized by a transition in the ATN.\r\n\t */\r\n\tpublic maxTokenType: number;\r\n\r\n\t/**\r\n\t * For lexer ATNs, this maps the rule index to the resulting token type.\r\n\t * For parser ATNs, this maps the rule index to the generated bypass token\r\n\t * type if the\r\n\t * {@link ATNDeserializationOptions#isGenerateRuleBypassTransitions}\r\n\t * deserialization option was specified; otherwise, this is `undefined`.\r\n\t */\r\n\tpublic ruleToTokenType!: Int32Array;\r\n\r\n\t/**\r\n\t * For lexer ATNs, this is an array of {@link LexerAction} objects which may\r\n\t * be referenced by action transitions in the ATN.\r\n\t */\r\n\tpublic lexerActions!: LexerAction[];\r\n\r\n\t@NotNull\r\n\tpublic modeToStartState: TokensStartState[] = [];\r\n\r\n\tprivate contextCache: Array2DHashMap<PredictionContext, PredictionContext> =\r\n\t\tnew Array2DHashMap<PredictionContext, PredictionContext>(ObjectEqualityComparator.INSTANCE);\r\n\r\n\t@NotNull\r\n\tpublic decisionToDFA: DFA[] = [];\r\n\t@NotNull\r\n\tpublic modeToDFA: DFA[] = [];\r\n\r\n\tpublic LL1Table: Map<number, number> = new Map<number, number>();\r\n\r\n\t/** Used for runtime deserialization of ATNs from strings */\r\n\tconstructor(@NotNull grammarType: ATNType, maxTokenType: number) {\r\n\t\tthis.grammarType = grammarType;\r\n\t\tthis.maxTokenType = maxTokenType;\r\n\t}\r\n\r\n\tpublic clearDFA(): void {\r\n\t\tthis.decisionToDFA = new Array<DFA>(this.decisionToState.length);\r\n\t\tfor (let i = 0; i < this.decisionToDFA.length; i++) {\r\n\t\t\tthis.decisionToDFA[i] = new DFA(this.decisionToState[i], i);\r\n\t\t}\r\n\r\n\t\tthis.modeToDFA = new Array<DFA>(this.modeToStartState.length);\r\n\t\tfor (let i = 0; i < this.modeToDFA.length; i++) {\r\n\t\t\tthis.modeToDFA[i] = new DFA(this.modeToStartState[i]);\r\n\t\t}\r\n\r\n\t\tthis.contextCache.clear();\r\n\t\tthis.LL1Table.clear();\r\n\t}\r\n\r\n\tget contextCacheSize(): number {\r\n\t\treturn this.contextCache.size;\r\n\t}\r\n\r\n\tpublic getCachedContext(context: PredictionContext): PredictionContext {\r\n\t\treturn PredictionContext.getCachedContext(context, this.contextCache, new PredictionContext.IdentityHashMap());\r\n\t}\r\n\r\n\tpublic getDecisionToDFA(): DFA[] {\r\n\t\tassert(this.decisionToDFA != null && this.decisionToDFA.length === this.decisionToState.length);\r\n\t\treturn this.decisionToDFA;\r\n\t}\r\n\r\n\t/** Compute the set of valid tokens that can occur starting in state `s`.\r\n\t *  If `ctx` is {@link PredictionContext#EMPTY_LOCAL}, the set of tokens will not include what can follow\r\n\t *  the rule surrounding `s`. In other words, the set will be\r\n\t *  restricted to tokens reachable staying within `s`'s rule.\r\n\t */\r\n\t// @NotNull\r\n\tpublic nextTokens(s: ATNState, /*@NotNull*/ ctx: PredictionContext): IntervalSet;\r\n\r\n\t/**\r\n\t * Compute the set of valid tokens that can occur starting in `s` and\r\n\t * staying in same rule. {@link Token#EPSILON} is in set if we reach end of\r\n\t * rule.\r\n\t */\r\n\t// @NotNull\r\n\tpublic nextTokens(/*@NotNull*/ s: ATNState): IntervalSet;\r\n\r\n\t@NotNull\r\n\tpublic nextTokens(s: ATNState, ctx?: PredictionContext): IntervalSet {\r\n\t\tif (ctx) {\r\n\t\t\tlet anal: LL1Analyzer = new LL1Analyzer(this);\r\n\t\t\tlet next: IntervalSet = anal.LOOK(s, ctx);\r\n\t\t\treturn next;\r\n\t\t} else {\r\n\t\t\tif (s.nextTokenWithinRule) {\r\n\t\t\t\treturn s.nextTokenWithinRule;\r\n\t\t\t}\r\n\r\n\t\t\ts.nextTokenWithinRule = this.nextTokens(s, PredictionContext.EMPTY_LOCAL);\r\n\t\t\ts.nextTokenWithinRule.setReadonly(true);\r\n\t\t\treturn s.nextTokenWithinRule;\r\n\t\t}\r\n\t}\r\n\r\n\tpublic addState(state: ATNState): void {\r\n\t\tstate.atn = this;\r\n\t\tstate.stateNumber = this.states.length;\r\n\t\tthis.states.push(state);\r\n\t}\r\n\r\n\tpublic removeState(@NotNull state: ATNState): void {\r\n\t\t// just replace the state, don't shift states in list\r\n\t\tlet invalidState = new InvalidState();\r\n\t\tinvalidState.atn = this;\r\n\t\tinvalidState.stateNumber = state.stateNumber;\r\n\t\tthis.states[state.stateNumber] = invalidState;\r\n\t}\r\n\r\n\tpublic defineMode(@NotNull name: string, @NotNull s: TokensStartState): void {\r\n\t\tthis.modeNameToStartState.set(name, s);\r\n\t\tthis.modeToStartState.push(s);\r\n\t\tthis.modeToDFA.push(new DFA(s));\r\n\t\tthis.defineDecisionState(s);\r\n\t}\r\n\r\n\tpublic defineDecisionState(@NotNull s: DecisionState): number {\r\n\t\tthis.decisionToState.push(s);\r\n\t\ts.decision = this.decisionToState.length - 1;\r\n\t\tthis.decisionToDFA.push(new DFA(s, s.decision));\r\n\t\treturn s.decision;\r\n\t}\r\n\r\n\tpublic getDecisionState(decision: number): DecisionState | undefined {\r\n\t\tif (this.decisionToState.length > 0) {\r\n\t\t\treturn this.decisionToState[decision];\r\n\t\t}\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tget numberOfDecisions(): number {\r\n\t\treturn this.decisionToState.length;\r\n\t}\r\n\r\n\t/**\r\n\t * Computes the set of input symbols which could follow ATN state number\r\n\t * `stateNumber` in the specified full `context`. This method\r\n\t * considers the complete parser context, but does not evaluate semantic\r\n\t * predicates (i.e. all predicates encountered during the calculation are\r\n\t * assumed true). If a path in the ATN exists from the starting state to the\r\n\t * {@link RuleStopState} of the outermost context without matching any\r\n\t * symbols, {@link Token#EOF} is added to the returned set.\r\n\t *\r\n\t * If `context` is `undefined`, it is treated as\r\n\t * {@link ParserRuleContext#EMPTY}.\r\n\t *\r\n\t * Note that this does NOT give you the set of all tokens that could\r\n\t * appear at a given token position in the input phrase.  In other words, it\r\n\t * does not answer:\r\n\t *\r\n\t * > Given a specific partial input phrase, return the set of all\r\n\t * > tokens that can follow the last token in the input phrase.\r\n\t *\r\n\t * The big difference is that with just the input, the parser could land\r\n\t * right in the middle of a lookahead decision. Getting all\r\n\t * *possible* tokens given a partial input stream is a separate\r\n\t * computation. See https://github.com/antlr/antlr4/issues/1428\r\n\t *\r\n\t * For this function, we are specifying an ATN state and call stack to\r\n\t * compute what token(s) can come next and specifically: outside of a\r\n\t * lookahead decision. That is what you want for error reporting and\r\n\t * recovery upon parse error.\r\n\t *\r\n\t * @param stateNumber the ATN state number\r\n\t * @param context the full parse context\r\n\t * @returns The set of potentially valid input symbols which could follow the\r\n\t * specified state in the specified context.\r\n\t * @ if the ATN does not contain a state with\r\n\t * number `stateNumber`\r\n\t */\r\n\t@NotNull\r\n\tpublic getExpectedTokens(stateNumber: number, context: RuleContext | undefined): IntervalSet {\r\n\t\tif (stateNumber < 0 || stateNumber >= this.states.length) {\r\n\t\t\tthrow new RangeError(\"Invalid state number.\");\r\n\t\t}\r\n\r\n\t\tlet ctx: RuleContext | undefined = context;\r\n\t\tlet s: ATNState = this.states[stateNumber];\r\n\t\tlet following: IntervalSet = this.nextTokens(s);\r\n\t\tif (!following.contains(Token.EPSILON)) {\r\n\t\t\treturn following;\r\n\t\t}\r\n\r\n\t\tlet expected: IntervalSet = new IntervalSet();\r\n\t\texpected.addAll(following);\r\n\t\texpected.remove(Token.EPSILON);\r\n\t\twhile (ctx != null && ctx.invokingState >= 0 && following.contains(Token.EPSILON)) {\r\n\t\t\tlet invokingState: ATNState = this.states[ctx.invokingState];\r\n\t\t\tlet rt: RuleTransition = invokingState.transition(0) as RuleTransition;\r\n\t\t\tfollowing = this.nextTokens(rt.followState);\r\n\t\t\texpected.addAll(following);\r\n\t\t\texpected.remove(Token.EPSILON);\r\n\t\t\tctx = ctx._parent;\r\n\t\t}\r\n\r\n\t\tif (following.contains(Token.EPSILON)) {\r\n\t\t\texpected.add(Token.EOF);\r\n\t\t}\r\n\r\n\t\treturn expected;\r\n\t}\r\n}\r\n\r\nexport namespace ATN {\r\n\texport const INVALID_ALT_NUMBER: number = 0;\r\n}\r\n"]}