{"version": 3, "file": "InvalidState.js", "sourceRoot": "", "sources": ["../../../src/atn/InvalidState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAGH,iDAA8C;AAC9C,6CAA0C;AAC1C,8CAAyC;AAEzC;;;GAGG;AACH,MAAa,YAAa,SAAQ,uBAAU;IAG3C,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,YAAY,CAAC;IAClC,CAAC;CAED;AAJA;IADC,qBAAQ;6CAGR;AALF,oCAOC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BasicState } from \"./BasicState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class InvalidState extends BasicState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.INVALID_TYPE;\r\n\t}\r\n\r\n}\r\n"]}