"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockStartState = void 0;
const DecisionState_1 = require("./DecisionState");
/**  The start of a regular `(...)` block. */
class BlockStartState extends DecisionState_1.DecisionState {
}
exports.BlockStartState = BlockStartState;
//# sourceMappingURL=BlockStartState.js.map