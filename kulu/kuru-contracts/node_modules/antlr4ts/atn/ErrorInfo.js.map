{"version": 3, "file": "ErrorInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/ErrorInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,8CAAwC;AAIxC;;;;;;;;;;GAUG;AACH,IAAa,SAAS,GAAtB,MAAa,SAAU,SAAQ,qCAAiB;IAC/C;;;;;;;;;;OAUG;IACH,YACC,QAAgB,EACP,KAAqB,EACrB,KAAkB,EAC3B,UAAkB,EAClB,SAAiB;QAEjB,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;IACxE,CAAC;CACD,CAAA;AArBY,SAAS;IAcnB,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;GAfG,SAAS,CAqBrB;AArBY,8BAAS", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.7213647-07:00\r\n\r\nimport { DecisionEventInfo } from \"./DecisionEventInfo\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This class represents profiling event information for a syntax error\r\n * identified during prediction. Syntax errors occur when the prediction\r\n * algorithm is unable to identify an alternative which would lead to a\r\n * successful parse.\r\n *\r\n * @see Parser#notifyErrorListeners(Token, String, RecognitionException)\r\n * @see ANTLRErrorListener#syntaxError\r\n *\r\n * @since 4.3\r\n */\r\nexport class ErrorInfo extends DecisionEventInfo {\r\n\t/**\r\n\t * Constructs a new instance of the {@link ErrorInfo} class with the\r\n\t * specified detailed syntax error information.\r\n\t *\r\n\t * @param decision The decision number\r\n\t * @param state The final simulator state reached during prediction\r\n\t * prior to reaching the {@link ATNSimulator#ERROR} state\r\n\t * @param input The input token stream\r\n\t * @param startIndex The start index for the current prediction\r\n\t * @param stopIndex The index at which the syntax error was identified\r\n\t */\r\n\tconstructor(\r\n\t\tdecision: number,\r\n\t\t@NotNull state: SimulatorState,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number) {\r\n\r\n\t\tsuper(decision, state, input, startIndex, stopIndex, state.useContext);\r\n\t}\r\n}\r\n"]}