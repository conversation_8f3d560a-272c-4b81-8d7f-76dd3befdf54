{"version": 3, "file": "DecisionState.js", "sourceRoot": "", "sources": ["../../../src/atn/DecisionState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,yCAAsC;AAEtC,MAAsB,aAAc,SAAQ,mBAAQ;IAApD;;QACQ,aAAQ,GAAW,CAAC,CAAC,CAAC;QACtB,cAAS,GAAY,KAAK,CAAC;QAC3B,QAAG,GAAY,KAAK,CAAC;IAC7B,CAAC;CAAA;AAJD,sCAIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.4381103-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\n\r\nexport abstract class DecisionState extends ATNState {\r\n\tpublic decision: number = -1;\r\n\tpublic nonGreedy: boolean = false;\r\n\tpublic sll: boolean = false;\r\n}\r\n"]}