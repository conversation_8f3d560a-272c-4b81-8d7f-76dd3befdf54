{"version": 3, "file": "ConflictInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/ConflictInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,8CAAyC;AACzC,uCAAuC;AAEvC;;;;GAIG;AACH,MAAa,YAAY;IAKxB,YAAY,cAAsB,EAAE,KAAc;QACjD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,YAAY,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO;eAC/B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;IACvC,CAAC;CACD;AAfA;IADC,qBAAQ;0CAUR;AAGD;IADC,qBAAQ;4CAGR;AAjDF,oCAkDC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.0710131-07:00\r\n\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { Override } from \"../Decorators\";\r\nimport * as Utils from \"../misc/Utils\";\r\n\r\n/**\r\n * This class stores information about a configuration conflict.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ConflictInfo {\r\n\tprivate _conflictedAlts: BitSet;\r\n\r\n\tprivate exact: boolean;\r\n\r\n\tconstructor(conflictedAlts: BitSet, exact: boolean) {\r\n\t\tthis._conflictedAlts = conflictedAlts;\r\n\t\tthis.exact = exact;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the set of conflicting alternatives for the configuration set.\r\n\t */\r\n\tget conflictedAlts(): BitSet {\r\n\t\treturn this._conflictedAlts;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets whether or not the configuration conflict is an exact conflict.\r\n\t * An exact conflict occurs when the prediction algorithm determines that\r\n\t * the represented alternatives for a particular configuration set cannot be\r\n\t * further reduced by consuming additional input. After reaching an exact\r\n\t * conflict during an SLL prediction, only switch to full-context prediction\r\n\t * could reduce the set of viable alternatives. In LL prediction, an exact\r\n\t * conflict indicates a true ambiguity in the input.\r\n\t *\r\n\t * For the {@link PredictionMode#LL_EXACT_AMBIG_DETECTION} prediction mode,\r\n\t * accept states are conflicting but not exact are treated as non-accept\r\n\t * states.\r\n\t */\r\n\tget isExact(): boolean {\r\n\t\treturn this.exact;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof ConflictInfo)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.isExact === obj.isExact\r\n\t\t\t&& Utils.equals(this.conflictedAlts, obj.conflictedAlts);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\treturn this.conflictedAlts.hashCode();\r\n\t}\r\n}\r\n"]}