{"version": 3, "file": "LookaheadEventInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/LookaheadEventInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,8CAAwC;AAIxC;;;;;GAKG;AACH,IAAa,kBAAkB,GAA/B,MAAa,kBAAmB,SAAQ,qCAAiB;IAQxD;;;;;;;;;;;;;;OAcG;IACH,YACC,QAAgB,EAChB,KAAiC,EACjC,YAAoB,EACX,KAAkB,EAC3B,UAAkB,EAClB,SAAiB,EACjB,OAAgB;QAEhB,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;CACD,CAAA;AAnCY,kBAAkB;IA2B5B,WAAA,oBAAO,CAAA;GA3BG,kBAAkB,CAmC9B;AAnCY,gDAAkB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.6852565-07:00\r\n\r\nimport { DecisionEventInfo } from \"./DecisionEventInfo\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This class represents profiling event information for tracking the lookahead\r\n * depth required in order to make a prediction.\r\n *\r\n * @since 4.3\r\n */\r\nexport class LookaheadEventInfo extends DecisionEventInfo {\r\n\t/** The alternative chosen by adaptivePredict(), not necessarily\r\n\t *  the outermost alt shown for a rule; left-recursive rules have\r\n\t *  user-level alts that differ from the rewritten rule with a (...) block\r\n\t *  and a (..)* loop.\r\n\t */\r\n\tpublic predictedAlt: number;\r\n\r\n\t/**\r\n\t * Constructs a new instance of the {@link LookaheadEventInfo} class with\r\n\t * the specified detailed lookahead information.\r\n\t *\r\n\t * @param decision The decision number\r\n\t * @param state The final simulator state containing the necessary\r\n\t * information to determine the result of a prediction, or `undefined` if\r\n\t * the final state is not available\r\n\t * @param input The input token stream\r\n\t * @param startIndex The start index for the current prediction\r\n\t * @param stopIndex The index at which the prediction was finally made\r\n\t * @param fullCtx `true` if the current lookahead is part of an LL\r\n\t * prediction; otherwise, `false` if the current lookahead is part of\r\n\t * an SLL prediction\r\n\t */\r\n\tconstructor(\r\n\t\tdecision: number,\r\n\t\tstate: SimulatorState | undefined,\r\n\t\tpredictedAlt: number,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tfullCtx: boolean) {\r\n\r\n\t\tsuper(decision, state, input, startIndex, stopIndex, fullCtx);\r\n\t\tthis.predictedAlt = predictedAlt;\r\n\t}\r\n}\r\n"]}