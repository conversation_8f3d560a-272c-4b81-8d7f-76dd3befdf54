{"version": 3, "file": "SetTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/SetTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,qDAAkD;AAClD,8CAA4D;AAC5D,oCAAiC;AACjC,6CAA0C;AAG1C,+CAA+C;AAC/C,IAAa,aAAa,GAA1B,MAAa,aAAc,SAAQ,uBAAU;IAI5C,qDAAqD;IACrD,YAAqB,MAAgB,EAAY,GAAgB;QAChE,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,GAAG,GAAG,yBAAW,CAAC,EAAE,CAAC,aAAK,CAAC,YAAY,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IAChB,CAAC;IAGD,IAAI,iBAAiB;QACpB,mBAA0B;IAC3B,CAAC;IAID,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,GAAG,CAAC;IACjB,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAIM,QAAQ;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC;CACD,CAAA;AAjCA;IADC,oBAAO;0CACgB;AAaxB;IADC,qBAAQ;sDAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;0CAGP;AAGD;IADC,qBAAQ;4CAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;6CAGP;AAlCW,aAAa;IAKZ,WAAA,oBAAO,CAAA,EAAoB,WAAA,qBAAQ,CAAA;GALpC,aAAa,CAmCzB;AAnCY,sCAAa", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.3060135-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { Override, NotNull, Nullable } from \"../Decorators\";\r\nimport { Token } from \"../Token\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/** A transition containing a set of values. */\r\nexport class SetTransition extends Transition {\r\n\t@NotNull\r\n\tpublic set: IntervalSet;\r\n\r\n\t// TODO (sam): should we really allow undefined here?\r\n\tconstructor(@NotNull target: ATNState, @Nullable set: IntervalSet) {\r\n\t\tsuper(target);\r\n\t\tif (set == null) {\r\n\t\t\tset = IntervalSet.of(Token.INVALID_TYPE);\r\n\t\t}\r\n\r\n\t\tthis.set = set;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.SET;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tget label(): IntervalSet {\r\n\t\treturn this.set;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn this.set.contains(symbol);\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn this.set.toString();\r\n\t}\r\n}\r\n"]}