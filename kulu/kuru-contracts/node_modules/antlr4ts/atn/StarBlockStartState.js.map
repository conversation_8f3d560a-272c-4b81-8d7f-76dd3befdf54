{"version": 3, "file": "StarBlockStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/StarBlockStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,iDAA8C;AAC9C,uDAAoD;AACpD,8CAAyC;AAEzC,4CAA4C;AAC5C,MAAa,mBAAoB,SAAQ,iCAAe;IAGvD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,gBAAgB,CAAC;IACtC,CAAC;CACD;AAHA;IADC,qBAAQ;oDAGR;AALF,kDAMC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.5657409-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BlockStartState } from \"./BlockStartState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** The block that begins a closure loop. */\r\nexport class StarBlockStartState extends BlockStartState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.STAR_BLOCK_START;\r\n\t}\r\n}\r\n"]}