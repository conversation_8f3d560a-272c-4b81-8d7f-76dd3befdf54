{"version": 3, "file": "ParseInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/ParseInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,8CAAwC;AAGxC;;;;;GAKG;AACH,IAAa,SAAS,GAAtB,MAAa,SAAS;IAGrB,YAAqB,YAAmC;QACvD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IAEI,eAAe;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IAEI,cAAc;QACpB,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,EAAE,GAAa,EAAE,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,QAAQ,GAAW,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAChD,IAAI,QAAQ,GAAG,CAAC,EAAE;gBACjB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACX;SACD;QAED,OAAO,EAAE,CAAC;IACX,CAAC;IAED;;;;OAIG;IACI,wBAAwB;QAC9B,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,gBAAgB,CAAC;SAC/B;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;OAIG;IACI,uBAAuB;QAC7B,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,aAAa,CAAC;SAC5B;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;OAIG;IACI,sBAAsB;QAC5B,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,YAAY,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACI,0BAA0B;QAChC,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,kBAAkB,CAAC;SACjC;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACI,yBAAyB;QAC/B,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC;SAChC;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB;QAC7B,IAAI,SAAS,GAAmB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACpE,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,CAAC,IAAI,QAAQ,CAAC,kBAAkB,CAAC;YACjC,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC;SAChC;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAcM,UAAU,CAAC,QAAiB;QAClC,IAAI,QAAQ,EAAE;YACb,IAAI,aAAa,GAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACvE,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;SACjC;aAAM;YACN,IAAI,CAAC,GAAW,CAAC,CAAC;YAClB,IAAI,aAAa,GAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC;YAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC9C,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aACxB;YAED,OAAO,CAAC,CAAC;SACT;IACF,CAAC;CACD,CAAA;AA/IA;IADC,oBAAO;gDAGP;AAWD;IADC,oBAAO;+CAYP;AAvCW,SAAS;IAGR,WAAA,oBAAO,CAAA;GAHR,SAAS,CA8JrB;AA9JY,8BAAS", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:31.0349605-07:00\r\n\r\nimport { DecisionInfo } from \"./DecisionInfo\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { ProfilingATNSimulator } from \"./ProfilingATNSimulator\";\r\n\r\n/**\r\n * This class provides access to specific and aggregate statistics gathered\r\n * during profiling of a parser.\r\n *\r\n * @since 4.3\r\n */\r\nexport class ParseInfo {\r\n\tprotected atnSimulator: ProfilingATNSimulator;\r\n\r\n\tconstructor(@NotNull atnSimulator: ProfilingATNSimulator) {\r\n\t\tthis.atnSimulator = atnSimulator;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets an array of {@link DecisionInfo} instances containing the profiling\r\n\t * information gathered for each decision in the ATN.\r\n\t *\r\n\t * @returns An array of {@link DecisionInfo} instances, indexed by decision\r\n\t * number.\r\n\t */\r\n\t@NotNull\r\n\tpublic getDecisionInfo(): DecisionInfo[] {\r\n\t\treturn this.atnSimulator.getDecisionInfo();\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the decision numbers for decisions that required one or more\r\n\t * full-context predictions during parsing. These are decisions for which\r\n\t * {@link DecisionInfo#LL_Fallback} is non-zero.\r\n\t *\r\n\t * @returns A list of decision numbers which required one or more\r\n\t * full-context predictions during parsing.\r\n\t */\r\n\t@NotNull\r\n\tpublic getLLDecisions(): number[] {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet LL: number[] = [];\r\n\t\tfor (let i = 0; i < decisions.length; i++) {\r\n\t\t\tlet fallBack: number = decisions[i].LL_Fallback;\r\n\t\t\tif (fallBack > 0) {\r\n\t\t\t\tLL.push(i);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn LL;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total time spent during prediction across all decisions made\r\n\t * during parsing. This value is the sum of\r\n\t * {@link DecisionInfo#timeInPrediction} for all decisions.\r\n\t */\r\n\tpublic getTotalTimeInPrediction(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet t: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tt += decision.timeInPrediction;\r\n\t\t}\r\n\r\n\t\treturn t;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of SLL lookahead operations across all decisions\r\n\t * made during parsing. This value is the sum of\r\n\t * {@link DecisionInfo#SLL_TotalLook} for all decisions.\r\n\t */\r\n\tpublic getTotalSLLLookaheadOps(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet k: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tk += decision.SLL_TotalLook;\r\n\t\t}\r\n\r\n\t\treturn k;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of LL lookahead operations across all decisions\r\n\t * made during parsing. This value is the sum of\r\n\t * {@link DecisionInfo#LL_TotalLook} for all decisions.\r\n\t */\r\n\tpublic getTotalLLLookaheadOps(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet k: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tk += decision.LL_TotalLook;\r\n\t\t}\r\n\r\n\t\treturn k;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of ATN lookahead operations for SLL prediction\r\n\t * across all decisions made during parsing.\r\n\t */\r\n\tpublic getTotalSLLATNLookaheadOps(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet k: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tk += decision.SLL_ATNTransitions;\r\n\t\t}\r\n\r\n\t\treturn k;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of ATN lookahead operations for LL prediction\r\n\t * across all decisions made during parsing.\r\n\t */\r\n\tpublic getTotalLLATNLookaheadOps(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet k: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tk += decision.LL_ATNTransitions;\r\n\t\t}\r\n\r\n\t\treturn k;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of ATN lookahead operations for SLL and LL\r\n\t * prediction across all decisions made during parsing.\r\n\t *\r\n\t * This value is the sum of {@link #getTotalSLLATNLookaheadOps} and\r\n\t * {@link #getTotalLLATNLookaheadOps}.\r\n\t */\r\n\tpublic getTotalATNLookaheadOps(): number {\r\n\t\tlet decisions: DecisionInfo[] = this.atnSimulator.getDecisionInfo();\r\n\t\tlet k: number = 0;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tk += decision.SLL_ATNTransitions;\r\n\t\t\tk += decision.LL_ATNTransitions;\r\n\t\t}\r\n\r\n\t\treturn k;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the total number of DFA states stored in the DFA cache for all\r\n\t * decisions in the ATN.\r\n\t */\r\n\tpublic getDFASize(): number;\r\n\r\n\t/**\r\n\t * Gets the total number of DFA states stored in the DFA cache for a\r\n\t * particular decision.\r\n\t */\r\n\tpublic getDFASize(decision: number): number;\r\n\r\n\tpublic getDFASize(decision?: number): number {\r\n\t\tif (decision) {\r\n\t\t\tlet decisionToDFA: DFA = this.atnSimulator.atn.decisionToDFA[decision];\r\n\t\t\treturn decisionToDFA.states.size;\r\n\t\t} else {\r\n\t\t\tlet n: number = 0;\r\n\t\t\tlet decisionToDFA: DFA[] = this.atnSimulator.atn.decisionToDFA;\r\n\t\t\tfor (let i = 0; i < decisionToDFA.length; i++) {\r\n\t\t\t\tn += this.getDFASize(i);\r\n\t\t\t}\r\n\r\n\t\t\treturn n;\r\n\t\t}\r\n\t}\r\n}\r\n"]}