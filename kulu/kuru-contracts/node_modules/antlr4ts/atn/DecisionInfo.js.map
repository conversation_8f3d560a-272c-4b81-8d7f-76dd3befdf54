{"version": 3, "file": "DecisionInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/DecisionInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAQH,8CAAyC;AAGzC;;;;;;;;;;;GAWG;AACH,MAAa,YAAY;IA+LxB;;;;;OAKG;IACH,YAAY,QAAgB;QA/L5B;;;WAGG;QACI,gBAAW,GAAW,CAAC,CAAC;QAE/B;;;;;;;;;;;;WAYG;QACI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;;;;WAKG;QACI,kBAAa,GAAW,CAAC,CAAC;QAEjC;;;;WAIG;QACI,gBAAW,GAAW,CAAC,CAAC;QAE/B;;;;WAIG;QACI,gBAAW,GAAW,CAAC,CAAC;QAQ/B;;;;WAIG;QACI,iBAAY,GAAW,CAAC,CAAC;QAEhC;;;;;;WAMG;QACI,eAAU,GAAW,CAAC,CAAC;QAE9B;;;;;;WAMG;QACI,eAAU,GAAW,CAAC,CAAC;QAQ9B;;;;;WAKG;QACI,yBAAoB,GAA6B,EAAE,CAAC;QAE3D;;;;;;WAMG;QACI,WAAM,GAAgB,EAAE,CAAC;QAEhC;;;;;WAKG;QACI,gBAAW,GAAoB,EAAE,CAAC;QAEzC;;;;;;WAMG;QACI,mBAAc,GAAwB,EAAE,CAAC;QAEhD;;;;;;;;;;;;;;WAcG;QACI,uBAAkB,GAAW,CAAC,CAAC;QAEtC;;;;;;;;;WASG;QACI,uBAAkB,GAAW,CAAC,CAAC;QAEtC;;;;;;;;;;WAUG;QACI,gBAAW,GAAW,CAAC,CAAC;QAE/B;;;;;;;;;;;;;;WAcG;QACI,sBAAiB,GAAW,CAAC,CAAC;QAErC;;;;;;;;;WASG;QACI,sBAAiB,GAAW,CAAC,CAAC;QASpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAGM,QAAQ;QACd,OAAO,GAAG;YACT,WAAW,GAAG,IAAI,CAAC,QAAQ;YAC3B,yBAAyB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM;YAC5D,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;YAChC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;YAC1C,kBAAkB,GAAG,IAAI,CAAC,aAAa;YACvC,uBAAuB,GAAG,IAAI,CAAC,kBAAkB;YACjD,uBAAuB,GAAG,IAAI,CAAC,kBAAkB;YACjD,gBAAgB,GAAG,IAAI,CAAC,WAAW;YACnC,iBAAiB,GAAG,IAAI,CAAC,YAAY;YACrC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB;YAC/C,GAAG,CAAC;IACN,CAAC;CACD;AAdA;IADC,qBAAQ;4CAcR;AAvNF,oCAwNC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.3330673-07:00\r\n\r\nimport { AmbiguityInfo } from \"./AmbiguityInfo\";\r\nimport { ContextSensitivityInfo } from \"./ContextSensitivityInfo\";\r\nimport { ErrorInfo } from \"./ErrorInfo\";\r\nimport { LookaheadEventInfo } from \"./LookaheadEventInfo\";\r\nimport { Override } from \"../Decorators\";\r\nimport { PredicateEvalInfo } from \"./PredicateEvalInfo\";\r\n\r\n/**\r\n * This class contains profiling gathered for a particular decision.\r\n *\r\n * Parsing performance in ANTLR 4 is heavily influenced by both static factors\r\n * (e.g. the form of the rules in the grammar) and dynamic factors (e.g. the\r\n * choice of input and the state of the DFA cache at the time profiling\r\n * operations are started). For best results, gather and use aggregate\r\n * statistics from a large sample of inputs representing the inputs expected in\r\n * production before using the results to make changes in the grammar.\r\n *\r\n * @since 4.3\r\n */\r\nexport class DecisionInfo {\r\n\t/**\r\n\t * The decision number, which is an index into {@link ATN#decisionToState}.\r\n\t */\r\n\tpublic decision: number;\r\n\r\n\t/**\r\n\t * The total number of times {@link ParserATNSimulator#adaptivePredict} was\r\n\t * invoked for this decision.\r\n\t */\r\n\tpublic invocations: number = 0;\r\n\r\n\t/**\r\n\t * The total time spent in {@link ParserATNSimulator#adaptivePredict} for\r\n\t * this decision, in nanoseconds.\r\n\t *\r\n\t * The value of this field contains the sum of differential results obtained\r\n\t * by {@link System#nanoTime()}, and is not adjusted to compensate for JIT\r\n\t * and/or garbage collection overhead. For best accuracy, use a modern JVM\r\n\t * implementation that provides precise results from\r\n\t * {@link System#nanoTime()}, and perform profiling in a separate process\r\n\t * which is warmed up by parsing the input prior to profiling. If desired,\r\n\t * call {@link ATNSimulator#clearDFA} to reset the DFA cache to its initial\r\n\t * state before starting the profiling measurement pass.\r\n\t */\r\n\tpublic timeInPrediction: number = 0;\r\n\r\n\t/**\r\n\t * The sum of the lookahead required for SLL prediction for this decision.\r\n\t * Note that SLL prediction is used before LL prediction for performance\r\n\t * reasons even when {@link PredictionMode#LL} or\r\n\t * {@link PredictionMode#LL_EXACT_AMBIG_DETECTION} is used.\r\n\t */\r\n\tpublic SLL_TotalLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the minimum lookahead required for any single SLL prediction to\r\n\t * complete for this decision, by reaching a unique prediction, reaching an\r\n\t * SLL conflict state, or encountering a syntax error.\r\n\t */\r\n\tpublic SLL_MinLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the maximum lookahead required for any single SLL prediction to\r\n\t * complete for this decision, by reaching a unique prediction, reaching an\r\n\t * SLL conflict state, or encountering a syntax error.\r\n\t */\r\n\tpublic SLL_MaxLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the {@link LookaheadEventInfo} associated with the event where the\r\n\t * {@link #SLL_MaxLook} value was set.\r\n\t */\r\n\tpublic SLL_MaxLookEvent?: LookaheadEventInfo;\r\n\r\n\t/**\r\n\t * The sum of the lookahead required for LL prediction for this decision.\r\n\t * Note that LL prediction is only used when SLL prediction reaches a\r\n\t * conflict state.\r\n\t */\r\n\tpublic LL_TotalLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the minimum lookahead required for any single LL prediction to\r\n\t * complete for this decision. An LL prediction completes when the algorithm\r\n\t * reaches a unique prediction, a conflict state (for\r\n\t * {@link PredictionMode#LL}, an ambiguity state (for\r\n\t * {@link PredictionMode#LL_EXACT_AMBIG_DETECTION}, or a syntax error.\r\n\t */\r\n\tpublic LL_MinLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the maximum lookahead required for any single LL prediction to\r\n\t * complete for this decision. An LL prediction completes when the algorithm\r\n\t * reaches a unique prediction, a conflict state (for\r\n\t * {@link PredictionMode#LL}, an ambiguity state (for\r\n\t * {@link PredictionMode#LL_EXACT_AMBIG_DETECTION}, or a syntax error.\r\n\t */\r\n\tpublic LL_MaxLook: number = 0;\r\n\r\n\t/**\r\n\t * Gets the {@link LookaheadEventInfo} associated with the event where the\r\n\t * {@link #LL_MaxLook} value was set.\r\n\t */\r\n\tpublic LL_MaxLookEvent?: LookaheadEventInfo;\r\n\r\n\t/**\r\n\t * A collection of {@link ContextSensitivityInfo} instances describing the\r\n\t * context sensitivities encountered during LL prediction for this decision.\r\n\t *\r\n\t * @see ContextSensitivityInfo\r\n\t */\r\n\tpublic contextSensitivities: ContextSensitivityInfo[] = [];\r\n\r\n\t/**\r\n\t * A collection of {@link ErrorInfo} instances describing the parse errors\r\n\t * identified during calls to {@link ParserATNSimulator#adaptivePredict} for\r\n\t * this decision.\r\n\t *\r\n\t * @see ErrorInfo\r\n\t */\r\n\tpublic errors: ErrorInfo[] = [];\r\n\r\n\t/**\r\n\t * A collection of {@link AmbiguityInfo} instances describing the\r\n\t * ambiguities encountered during LL prediction for this decision.\r\n\t *\r\n\t * @see AmbiguityInfo\r\n\t */\r\n\tpublic ambiguities: AmbiguityInfo[] = [];\r\n\r\n\t/**\r\n\t * A collection of {@link PredicateEvalInfo} instances describing the\r\n\t * results of evaluating individual predicates during prediction for this\r\n\t * decision.\r\n\t *\r\n\t * @see PredicateEvalInfo\r\n\t */\r\n\tpublic predicateEvals: PredicateEvalInfo[] = [];\r\n\r\n\t/**\r\n\t * The total number of ATN transitions required during SLL prediction for\r\n\t * this decision. An ATN transition is determined by the number of times the\r\n\t * DFA does not contain an edge that is required for prediction, resulting\r\n\t * in on-the-fly computation of that edge.\r\n\t *\r\n\t * If DFA caching of SLL transitions is employed by the implementation, ATN\r\n\t * computation may cache the computed edge for efficient lookup during\r\n\t * future parsing of this decision. Otherwise, the SLL parsing algorithm\r\n\t * will use ATN transitions exclusively.\r\n\t *\r\n\t * @see #SLL_ATNTransitions\r\n\t * @see ParserATNSimulator#computeTargetState\r\n\t * @see LexerATNSimulator#computeTargetState\r\n\t */\r\n\tpublic SLL_ATNTransitions: number = 0;\r\n\r\n\t/**\r\n\t * The total number of DFA transitions required during SLL prediction for\r\n\t * this decision.\r\n\t *\r\n\t * If the ATN simulator implementation does not use DFA caching for SLL\r\n\t * transitions, this value will be 0.\r\n\t *\r\n\t * @see ParserATNSimulator#getExistingTargetState\r\n\t * @see LexerATNSimulator#getExistingTargetState\r\n\t */\r\n\tpublic SLL_DFATransitions: number = 0;\r\n\r\n\t/**\r\n\t * Gets the total number of times SLL prediction completed in a conflict\r\n\t * state, resulting in fallback to LL prediction.\r\n\t *\r\n\t * Note that this value is not related to whether or not\r\n\t * {@link PredictionMode#SLL} may be used successfully with a particular\r\n\t * grammar. If the ambiguity resolution algorithm applied to the SLL\r\n\t * conflicts for this decision produce the same result as LL prediction for\r\n\t * this decision, {@link PredictionMode#SLL} would produce the same overall\r\n\t * parsing result as {@link PredictionMode#LL}.\r\n\t */\r\n\tpublic LL_Fallback: number = 0;\r\n\r\n\t/**\r\n\t * The total number of ATN transitions required during LL prediction for\r\n\t * this decision. An ATN transition is determined by the number of times the\r\n\t * DFA does not contain an edge that is required for prediction, resulting\r\n\t * in on-the-fly computation of that edge.\r\n\t *\r\n\t * If DFA caching of LL transitions is employed by the implementation, ATN\r\n\t * computation may cache the computed edge for efficient lookup during\r\n\t * future parsing of this decision. Otherwise, the LL parsing algorithm will\r\n\t * use ATN transitions exclusively.\r\n\t *\r\n\t * @see #LL_DFATransitions\r\n\t * @see ParserATNSimulator#computeTargetState\r\n\t * @see LexerATNSimulator#computeTargetState\r\n\t */\r\n\tpublic LL_ATNTransitions: number = 0;\r\n\r\n\t/**\r\n\t * The total number of DFA transitions required during LL prediction for\r\n\t * this decision.\r\n\t *\r\n\t * If the ATN simulator implementation does not use DFA caching for LL\r\n\t * transitions, this value will be 0.\r\n\t *\r\n\t * @see ParserATNSimulator#getExistingTargetState\r\n\t * @see LexerATNSimulator#getExistingTargetState\r\n\t */\r\n\tpublic LL_DFATransitions: number = 0;\r\n\r\n\t/**\r\n\t * Constructs a new instance of the {@link DecisionInfo} class to contain\r\n\t * statistics for a particular decision.\r\n\t *\r\n\t * @param decision The decision number\r\n\t */\r\n\tconstructor(decision: number) {\r\n\t\tthis.decision = decision;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn \"{\" +\r\n\t\t\t\"decision=\" + this.decision +\r\n\t\t\t\", contextSensitivities=\" + this.contextSensitivities.length +\r\n\t\t\t\", errors=\" + this.errors.length +\r\n\t\t\t\", ambiguities=\" + this.ambiguities.length +\r\n\t\t\t\", SLL_lookahead=\" + this.SLL_TotalLook +\r\n\t\t\t\", SLL_ATNTransitions=\" + this.SLL_ATNTransitions +\r\n\t\t\t\", SLL_DFATransitions=\" + this.SLL_DFATransitions +\r\n\t\t\t\", LL_Fallback=\" + this.LL_Fallback +\r\n\t\t\t\", LL_lookahead=\" + this.LL_TotalLook +\r\n\t\t\t\", LL_ATNTransitions=\" + this.LL_ATNTransitions +\r\n\t\t\t\"}\";\r\n\t}\r\n}\r\n"]}