{"version": 3, "file": "ProfilingATNSimulator.js", "sourceRoot": "", "sources": ["../../../src/atn/ProfilingATNSimulator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,mDAAgD;AAChD,+BAA4B;AAE5B,iDAA8C;AAE9C,qEAAkE;AAClE,iDAA8C;AAG9C,2CAAwC;AACxC,8CAAkD;AAClD,6DAA0D;AAE1D,6DAA0D;AAE1D,2DAAwD;AAExD,uDAAoD;AACpD,qDAAkD;AAGlD;;GAEG;AACH,MAAa,qBAAsB,SAAQ,uCAAkB;IAyB5D,YAAY,MAAc;QACzB,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QArB7B,gBAAW,GAAW,CAAC,CAAC;QACxB,kBAAa,GAAW,CAAC,CAAC;QAC1B,iBAAY,GAAW,CAAC,CAAC;QAEzB,oBAAe,GAAW,CAAC,CAAC;QAGtC;;;;;;;;;;WAUG;QACO,gCAA2B,GAAW,CAAC,CAAC;QAIjD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,2BAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;IACF,CAAC;IAKM,eAAe,CACZ,KAAkB,EAC3B,QAAgB,EAChB,YAA2C,EAC3C,UAAoB;QACpB,IAAI,UAAU,KAAK,SAAS,EAAE;YAC7B,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SACxE;QAED,IAAI;YACH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;YAC/B,8EAA8E;YAC9E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YAC1C,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,IAAI,CAAC,2BAA2B,GAAG,SAAG,CAAC,kBAAkB,CAAC;YAC1D,IAAI,KAAK,GAAa,OAAO,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,GAAG,GAAW,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YACvE,IAAI,IAAI,GAAa,OAAO,CAAC,MAAM,EAAE,CAAC;YAEtC,IAAI,WAAW,GAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;YAC5D,IAAI,WAAW,KAAK,CAAC,EAAE;gBACtB,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACjC;iBAAM;gBACN,wFAAwF;gBACxF,WAAW,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aACjD;YAED,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,WAAW,CAAC;YACzD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAEvC,IAAI,KAAK,GAAW,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,IAAI,KAAK,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClJ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE;gBACjD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,gBAAgB;oBACxC,IAAI,uCAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACtG;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE;gBAC3B,IAAI,IAAI,GAAW,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;gBAC5D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC;gBAC9C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBAC7I,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE;oBAC/C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC3C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,eAAe;wBACvC,IAAI,uCAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;iBACpG;aACD;YAED,OAAO,GAAG,CAAC;SACX;gBACO;YACP,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;SAC1B;IACF,CAAC;IAGS,aAAa,CAAC,GAAQ,EAAE,KAAkB,EAAE,YAA+B,EAAE,UAAmB;QACzG,IAAI,KAAK,GAA+B,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;QAClG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC;IACd,CAAC;IAGS,iBAAiB,CAAC,GAAQ,EAAE,aAAgC,EAAE,UAAmB;QAC1F,IAAI,KAAK,GAAmB,KAAK,CAAC,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC;IACd,CAAC;IAGS,eAAe,CAAC,GAAQ,EAAE,QAAwB,EAAE,CAAS,EAAE,YAAoC;QAC5G,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,IAAI,UAAU,GAA+B,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QACnG,IAAI,UAAU,IAAI,IAAI,EAAE;YACvB,+CAA+C;YAC/C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,IAAI,CAC/C,IAAI,qBAAS,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAC/F,CAAC;SACF;QAED,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;QAC/B,OAAO,UAAU,CAAC;IACnB,CAAC;IAGS,sBAAsB,CAAC,SAAmB,EAAE,CAAS;QAC9D,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,oEAAoE;QACpE,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;SACtC;aACI;YACJ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;SACvC;QAED,IAAI,mBAAmB,GAAyB,KAAK,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC3F,IAAI,mBAAmB,IAAI,IAAI,EAAE;YAChC,6EAA6E;YAC7E,+CAA+C;YAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,mBAAmB,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;YAEnK,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,iBAAiB,EAAE,CAAC;aACzD;iBACI;gBACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,+CAA+C;aAC1G;YAED,IAAI,mBAAmB,KAAK,2BAAY,CAAC,KAAK,EAAE;gBAC/C,IAAI,KAAK,GAAmB,IAAI,+BAAc,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;gBACjK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,IAAI,CAC/C,IAAI,qBAAS,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAC5F,CAAC;aACF;SACD;QAED,OAAO,mBAAmB,CAAC;IAC5B,CAAC;IAGS,kBAAkB,CAAC,GAAQ,EAAE,CAAW,EAAE,sBAAyC,EAAE,CAAS,EAAE,UAAmB,EAAE,YAAoC;QAClK,IAAI,WAAW,GAA8C,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,EAAE,sBAAsB,EAAE,CAAC,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QAEnJ,IAAI,UAAU,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,iBAAiB,EAAE,CAAC;SACzD;aACI;YACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,kBAAkB,EAAE,CAAC;SAC1D;QAED,OAAO,WAAW,CAAC;IACpB,CAAC;IAGS,uBAAuB,CAAC,IAAqB,EAAE,eAAkC,EAAE,GAAW;QACvG,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,IAAI,MAAM,GAAY,KAAK,CAAC,uBAAuB,CAAC,IAAI,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;QAChF,IAAI,CAAC,CAAC,IAAI,YAAY,iCAAe,CAAC,mBAAmB,CAAC,EAAE;YAC3D,IAAI,WAAW,GAAY,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;YAClD,IAAI,SAAS,GAAW,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;YAC7E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,IAAI,CACvD,IAAI,qCAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAC3H,CAAC;SACF;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGS,wBAAwB,CAAC,GAAQ,EAAE,UAAkB,EAAE,WAA2B,EAAE,UAAkB,EAAE,SAAiB;QAClI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,IAAI,UAAU,KAAK,IAAI,CAAC,2BAA2B,EAAE;YACpD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAC7D,IAAI,+CAAsB,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CACjG,CAAC;SACF;QACD,KAAK,CAAC,wBAAwB,CAAC,GAAG,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACrF,CAAC;IAGS,2BAA2B,CAAC,GAAQ,EAAE,eAAuB,EAAE,aAA6B,EAAE,UAAkB,EAAE,SAAiB;QAC5I,IAAI,eAAe,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,2BAA2B,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACjE;aACI;YACJ,IAAI,CAAC,2BAA2B,GAAG,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACvG;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,KAAK,CAAC,2BAA2B,CAAC,GAAG,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAC/F,CAAC;IAGS,eAAe,CAAU,GAAQ,EAAE,CAAW,EAAE,UAAkB,EAAE,SAAiB,EAAE,KAAc,EAAW,SAAiB,EAAW,OAAqB;QAC1K,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,IAAI,UAAkB,CAAC;QACvB,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACrC;aACI;YACJ,UAAU,GAAG,OAAO,CAAC,0BAA0B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAChE;QACD,IAAI,IAAI,CAAC,2BAA2B,KAAK,SAAG,CAAC,kBAAkB,IAAI,UAAU,KAAK,IAAI,CAAC,2BAA2B,EAAE;YACnH,4DAA4D;YAC5D,4DAA4D;YAC5D,kEAAkE;YAClE,8DAA8D;YAC9D,uBAAuB;YACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAC7D,IAAI,+CAAsB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CACvG,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,IAAI,CACpD,IAAI,6BAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CACzG,CAAC;QACF,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAED,wEAAwE;IAEjE,eAAe;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAEM,eAAe;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;CACD;AAnOA;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;4DA0DR;AAGD;IADC,qBAAQ;0DAKR;AAGD;IADC,qBAAQ;8DAKR;AAGD;IADC,qBAAQ;4DAgBR;AAGD;IADC,qBAAQ;mEAoCR;AAGD;IADC,qBAAQ;+DAYR;AAGD;IADC,qBAAQ;oEAgBR;AAGD;IADC,qBAAQ;qEAYR;AAGD;IADC,qBAAQ;wEAUR;AAGD;IADC,qBAAQ;IACkB,WAAA,oBAAO,CAAA,EAAgF,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;4DA0BpJ;AA/PF,sDA0QC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.4188352-07:00\r\n\r\nimport { AmbiguityInfo } from \"./AmbiguityInfo\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { ATNSimulator } from \"./ATNSimulator\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { ContextSensitivityInfo } from \"./ContextSensitivityInfo\";\r\nimport { DecisionInfo } from \"./DecisionInfo\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { DFAState } from \"../dfa/DFAState\";\r\nimport { ErrorInfo } from \"./ErrorInfo\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { LookaheadEventInfo } from \"./LookaheadEventInfo\";\r\nimport { Parser } from \"../Parser\";\r\nimport { ParserATNSimulator } from \"./ParserATNSimulator\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\nimport { PredicateEvalInfo } from \"./PredicateEvalInfo\";\r\nimport { PredictionContextCache } from \"./PredictionContextCache\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * @since 4.3\r\n */\r\nexport class ProfilingATNSimulator extends ParserATNSimulator {\r\n\tprotected decisions: DecisionInfo[];\r\n\tprotected numDecisions: number;\r\n\r\n\tprotected _input: TokenStream | undefined;\r\n\tprotected _startIndex: number = 0;\r\n\tprotected _sllStopIndex: number = 0;\r\n\tprotected _llStopIndex: number = 0;\r\n\r\n\tprotected currentDecision: number = 0;\r\n\tprotected currentState: SimulatorState | undefined;\r\n\r\n\t/** At the point of LL failover, we record how SLL would resolve the conflict so that\r\n\t *  we can determine whether or not a decision / input pair is context-sensitive.\r\n\t *  If LL gives a different result than SLL's predicted alternative, we have a\r\n\t *  context sensitivity for sure. The converse is not necessarily true, however.\r\n\t *  It's possible that after conflict resolution chooses minimum alternatives,\r\n\t *  SLL could get the same answer as LL. Regardless of whether or not the result indicates\r\n\t *  an ambiguity, it is not treated as a context sensitivity because LL prediction\r\n\t *  was not required in order to produce a correct prediction for this decision and input sequence.\r\n\t *  It may in fact still be a context sensitivity but we don't know by looking at the\r\n\t *  minimum alternatives for the current input.\r\n\t */\r\n\tprotected conflictingAltResolvedBySLL: number = 0;\r\n\r\n\tconstructor(parser: Parser) {\r\n\t\tsuper(parser.interpreter.atn, parser);\r\n\t\tthis.optimize_ll1 = false;\r\n\t\tthis.reportAmbiguities = true;\r\n\t\tthis.numDecisions = this.atn.decisionToState.length;\r\n\t\tthis.decisions = [];\r\n\t\tfor (let i = 0; i < this.numDecisions; i++) {\r\n\t\t\tthis.decisions.push(new DecisionInfo(i));\r\n\t\t}\r\n\t}\r\n\r\n\tpublic adaptivePredict(/*@NotNull*/ input: TokenStream, decision: number, outerContext: ParserRuleContext | undefined): number;\r\n\tpublic adaptivePredict(/*@NotNull*/ input: TokenStream, decision: number, outerContext: ParserRuleContext | undefined, useContext: boolean): number;\r\n\t@Override\r\n\tpublic adaptivePredict(\r\n\t\t@NotNull input: TokenStream,\r\n\t\tdecision: number,\r\n\t\touterContext: ParserRuleContext | undefined,\r\n\t\tuseContext?: boolean): number {\r\n\t\tif (useContext !== undefined) {\r\n\t\t\treturn super.adaptivePredict(input, decision, outerContext, useContext);\r\n\t\t}\r\n\r\n\t\ttry {\r\n\t\t\tthis._input = input;\r\n\t\t\tthis._startIndex = input.index;\r\n\t\t\t// it's possible for SLL to reach a conflict state without consuming any input\r\n\t\t\tthis._sllStopIndex = this._startIndex - 1;\r\n\t\t\tthis._llStopIndex = -1;\r\n\t\t\tthis.currentDecision = decision;\r\n\t\t\tthis.currentState = undefined;\r\n\t\t\tthis.conflictingAltResolvedBySLL = ATN.INVALID_ALT_NUMBER;\r\n\t\t\tlet start: number[] = process.hrtime();\r\n\t\t\tlet alt: number = super.adaptivePredict(input, decision, outerContext);\r\n\t\t\tlet stop: number[] = process.hrtime();\r\n\r\n\t\t\tlet nanoseconds: number = (stop[0] - start[0]) * 1000000000;\r\n\t\t\tif (nanoseconds === 0) {\r\n\t\t\t\tnanoseconds = stop[1] - start[1];\r\n\t\t\t} else {\r\n\t\t\t\t// Add nanoseconds from start to end of that second, plus start of the end second to end\r\n\t\t\t\tnanoseconds += (1000000000 - start[1]) + stop[1];\r\n\t\t\t}\r\n\r\n\t\t\tthis.decisions[decision].timeInPrediction += nanoseconds;\r\n\t\t\tthis.decisions[decision].invocations++;\r\n\r\n\t\t\tlet SLL_k: number = this._sllStopIndex - this._startIndex + 1;\r\n\t\t\tthis.decisions[decision].SLL_TotalLook += SLL_k;\r\n\t\t\tthis.decisions[decision].SLL_MinLook = this.decisions[decision].SLL_MinLook === 0 ? SLL_k : Math.min(this.decisions[decision].SLL_MinLook, SLL_k);\r\n\t\t\tif (SLL_k > this.decisions[decision].SLL_MaxLook) {\r\n\t\t\t\tthis.decisions[decision].SLL_MaxLook = SLL_k;\r\n\t\t\t\tthis.decisions[decision].SLL_MaxLookEvent =\r\n\t\t\t\t\tnew LookaheadEventInfo(decision, undefined, alt, input, this._startIndex, this._sllStopIndex, false);\r\n\t\t\t}\r\n\r\n\t\t\tif (this._llStopIndex >= 0) {\r\n\t\t\t\tlet LL_k: number = this._llStopIndex - this._startIndex + 1;\r\n\t\t\t\tthis.decisions[decision].LL_TotalLook += LL_k;\r\n\t\t\t\tthis.decisions[decision].LL_MinLook = this.decisions[decision].LL_MinLook === 0 ? LL_k : Math.min(this.decisions[decision].LL_MinLook, LL_k);\r\n\t\t\t\tif (LL_k > this.decisions[decision].LL_MaxLook) {\r\n\t\t\t\t\tthis.decisions[decision].LL_MaxLook = LL_k;\r\n\t\t\t\t\tthis.decisions[decision].LL_MaxLookEvent =\r\n\t\t\t\t\t\tnew LookaheadEventInfo(decision, undefined, alt, input, this._startIndex, this._llStopIndex, true);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn alt;\r\n\t\t}\r\n\t\tfinally {\r\n\t\t\tthis._input = undefined;\r\n\t\t\tthis.currentDecision = -1;\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tprotected getStartState(dfa: DFA, input: TokenStream, outerContext: ParserRuleContext, useContext: boolean): SimulatorState | undefined {\r\n\t\tlet state: SimulatorState | undefined = super.getStartState(dfa, input, outerContext, useContext);\r\n\t\tthis.currentState = state;\r\n\t\treturn state;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected computeStartState(dfa: DFA, globalContext: ParserRuleContext, useContext: boolean): SimulatorState {\r\n\t\tlet state: SimulatorState = super.computeStartState(dfa, globalContext, useContext);\r\n\t\tthis.currentState = state;\r\n\t\treturn state;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected computeReachSet(dfa: DFA, previous: SimulatorState, t: number, contextCache: PredictionContextCache): SimulatorState | undefined {\r\n\t\tif (this._input === undefined) {\r\n\t\t\tthrow new Error(\"Invalid state\");\r\n\t\t}\r\n\r\n\t\tlet reachState: SimulatorState | undefined = super.computeReachSet(dfa, previous, t, contextCache);\r\n\t\tif (reachState == null) {\r\n\t\t\t// no reach on current lookahead symbol. ERROR.\r\n\t\t\tthis.decisions[this.currentDecision].errors.push(\r\n\t\t\t\tnew ErrorInfo(this.currentDecision, previous, this._input, this._startIndex, this._input.index),\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tthis.currentState = reachState;\r\n\t\treturn reachState;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected getExistingTargetState(previousD: DFAState, t: number): DFAState | undefined {\r\n\t\tif (this.currentState === undefined || this._input === undefined) {\r\n\t\t\tthrow new Error(\"Invalid state\");\r\n\t\t}\r\n\r\n\t\t// this method is called after each time the input position advances\r\n\t\tif (this.currentState.useContext) {\r\n\t\t\tthis._llStopIndex = this._input.index;\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis._sllStopIndex = this._input.index;\r\n\t\t}\r\n\r\n\t\tlet existingTargetState: DFAState | undefined = super.getExistingTargetState(previousD, t);\r\n\t\tif (existingTargetState != null) {\r\n\t\t\t// this method is directly called by execDFA; must construct a SimulatorState\r\n\t\t\t// to represent the current state for this case\r\n\t\t\tthis.currentState = new SimulatorState(this.currentState.outerContext, existingTargetState, this.currentState.useContext, this.currentState.remainingOuterContext);\r\n\r\n\t\t\tif (this.currentState.useContext) {\r\n\t\t\t\tthis.decisions[this.currentDecision].LL_DFATransitions++;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tthis.decisions[this.currentDecision].SLL_DFATransitions++; // count only if we transition over a DFA state\r\n\t\t\t}\r\n\r\n\t\t\tif (existingTargetState === ATNSimulator.ERROR) {\r\n\t\t\t\tlet state: SimulatorState = new SimulatorState(this.currentState.outerContext, previousD, this.currentState.useContext, this.currentState.remainingOuterContext);\r\n\t\t\t\tthis.decisions[this.currentDecision].errors.push(\r\n\t\t\t\t\tnew ErrorInfo(this.currentDecision, state, this._input, this._startIndex, this._input.index),\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn existingTargetState;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected computeTargetState(dfa: DFA, s: DFAState, remainingGlobalContext: ParserRuleContext, t: number, useContext: boolean, contextCache: PredictionContextCache): [DFAState, ParserRuleContext | undefined] {\r\n\t\tlet targetState: [DFAState, ParserRuleContext | undefined] = super.computeTargetState(dfa, s, remainingGlobalContext, t, useContext, contextCache);\r\n\r\n\t\tif (useContext) {\r\n\t\t\tthis.decisions[this.currentDecision].LL_ATNTransitions++;\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.decisions[this.currentDecision].SLL_ATNTransitions++;\r\n\t\t}\r\n\r\n\t\treturn targetState;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected evalSemanticContextImpl(pred: SemanticContext, parserCallStack: ParserRuleContext, alt: number): boolean {\r\n\t\tif (this.currentState === undefined || this._input === undefined) {\r\n\t\t\tthrow new Error(\"Invalid state\");\r\n\t\t}\r\n\r\n\t\tlet result: boolean = super.evalSemanticContextImpl(pred, parserCallStack, alt);\r\n\t\tif (!(pred instanceof SemanticContext.PrecedencePredicate)) {\r\n\t\t\tlet fullContext: boolean = this._llStopIndex >= 0;\r\n\t\t\tlet stopIndex: number = fullContext ? this._llStopIndex : this._sllStopIndex;\r\n\t\t\tthis.decisions[this.currentDecision].predicateEvals.push(\r\n\t\t\t\tnew PredicateEvalInfo(this.currentState, this.currentDecision, this._input, this._startIndex, stopIndex, pred, result, alt),\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected reportContextSensitivity(dfa: DFA, prediction: number, acceptState: SimulatorState, startIndex: number, stopIndex: number): void {\r\n\t\tif (this._input === undefined) {\r\n\t\t\tthrow new Error(\"Invalid state\");\r\n\t\t}\r\n\r\n\t\tif (prediction !== this.conflictingAltResolvedBySLL) {\r\n\t\t\tthis.decisions[this.currentDecision].contextSensitivities.push(\r\n\t\t\t\tnew ContextSensitivityInfo(this.currentDecision, acceptState, this._input, startIndex, stopIndex),\r\n\t\t\t);\r\n\t\t}\r\n\t\tsuper.reportContextSensitivity(dfa, prediction, acceptState, startIndex, stopIndex);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected reportAttemptingFullContext(dfa: DFA, conflictingAlts: BitSet, conflictState: SimulatorState, startIndex: number, stopIndex: number): void {\r\n\t\tif (conflictingAlts != null) {\r\n\t\t\tthis.conflictingAltResolvedBySLL = conflictingAlts.nextSetBit(0);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.conflictingAltResolvedBySLL = conflictState.s0.configs.getRepresentedAlternatives().nextSetBit(0);\r\n\t\t}\r\n\t\tthis.decisions[this.currentDecision].LL_Fallback++;\r\n\t\tsuper.reportAttemptingFullContext(dfa, conflictingAlts, conflictState, startIndex, stopIndex);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected reportAmbiguity(@NotNull dfa: DFA, D: DFAState, startIndex: number, stopIndex: number, exact: boolean, @NotNull ambigAlts: BitSet, @NotNull configs: ATNConfigSet): void {\r\n\t\tif (this.currentState === undefined || this._input === undefined) {\r\n\t\t\tthrow new Error(\"Invalid state\");\r\n\t\t}\r\n\r\n\t\tlet prediction: number;\r\n\t\tif (ambigAlts != null) {\r\n\t\t\tprediction = ambigAlts.nextSetBit(0);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tprediction = configs.getRepresentedAlternatives().nextSetBit(0);\r\n\t\t}\r\n\t\tif (this.conflictingAltResolvedBySLL !== ATN.INVALID_ALT_NUMBER && prediction !== this.conflictingAltResolvedBySLL) {\r\n\t\t\t// Even though this is an ambiguity we are reporting, we can\r\n\t\t\t// still detect some context sensitivities.  Both SLL and LL\r\n\t\t\t// are showing a conflict, hence an ambiguity, but if they resolve\r\n\t\t\t// to different minimum alternatives we have also identified a\r\n\t\t\t// context sensitivity.\r\n\t\t\tthis.decisions[this.currentDecision].contextSensitivities.push(\r\n\t\t\t\tnew ContextSensitivityInfo(this.currentDecision, this.currentState, this._input, startIndex, stopIndex),\r\n\t\t\t);\r\n\t\t}\r\n\t\tthis.decisions[this.currentDecision].ambiguities.push(\r\n\t\t\tnew AmbiguityInfo(this.currentDecision, this.currentState, ambigAlts, this._input, startIndex, stopIndex),\r\n\t\t);\r\n\t\tsuper.reportAmbiguity(dfa, D, startIndex, stopIndex, exact, ambigAlts, configs);\r\n\t}\r\n\r\n\t// ---------------------------------------------------------------------\r\n\r\n\tpublic getDecisionInfo(): DecisionInfo[] {\r\n\t\treturn this.decisions;\r\n\t}\r\n\r\n\tpublic getCurrentState(): SimulatorState | undefined {\r\n\t\treturn this.currentState;\r\n\t}\r\n}\r\n"]}