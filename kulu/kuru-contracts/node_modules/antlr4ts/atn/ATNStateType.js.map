{"version": 3, "file": "ATNStateType.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNStateType.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,IAAY,YAcX;AAdD,WAAY,YAAY;IACvB,+DAAgB,CAAA;IAChB,iDAAS,CAAA;IACT,2DAAc,CAAA;IACd,6DAAe,CAAA;IACf,uEAAoB,CAAA;IACpB,uEAAoB,CAAA;IACpB,6DAAe,CAAA;IACf,yDAAa,CAAA;IACb,yDAAa,CAAA;IACb,mEAAkB,CAAA;IAClB,sEAAoB,CAAA;IACpB,oEAAmB,CAAA;IACnB,wDAAa,CAAA;AACd,CAAC,EAdW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAcvB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.4734328-07:00\r\n\r\nexport enum ATNStateType {\r\n\tINVALID_TYPE = 0,\r\n\tBASIC = 1,\r\n\tRULE_START = 2,\r\n\tBLOCK_START = 3,\r\n\tPLUS_BLOCK_START = 4,\r\n\tSTAR_BLOCK_START = 5,\r\n\tTOKEN_START = 6,\r\n\tRULE_STOP = 7,\r\n\tBLOCK_END = 8,\r\n\tSTAR_LOOP_BACK = 9,\r\n\tSTAR_LOOP_ENTRY = 10,\r\n\tPLUS_LOOP_BACK = 11,\r\n\tLOOP_END = 12,\r\n}\r\n"]}