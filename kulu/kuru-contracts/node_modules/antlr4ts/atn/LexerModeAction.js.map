{"version": 3, "file": "LexerModeAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerModeAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;GAMG;AACH,MAAa,eAAe;IAG3B;;;OAGG;IACH,YAAY,IAAY;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;;OAGG;IAEH,IAAI,UAAU;QACb,oBAA4B;IAC7B,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,eAAe,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC;IACjC,CAAC;IAGM,QAAQ;QACd,OAAO,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC;IAC9B,CAAC;CACD;AA/CA;IADC,qBAAQ;iDAGR;AAOD;IADC,qBAAQ;0DAGR;AASD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;8CAEtB;AAGD;IADC,qBAAQ;+CAMR;AAGD;IADC,qBAAQ;6CASR;AAGD;IADC,qBAAQ;+CAGR;AAvEF,0CAwEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.8653427-07:00\r\n\r\nimport { <PERSON>er } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Implements the `mode` lexer action by calling {@link Lexer#mode} with\r\n * the assigned mode.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerModeAction implements LexerAction {\r\n\tprivate readonly _mode: number;\r\n\r\n\t/**\r\n\t * Constructs a new `mode` action with the specified mode value.\r\n\t * @param mode The mode value to pass to {@link Lexer#mode}.\r\n\t */\r\n\tconstructor(mode: number) {\r\n\t\tthis._mode = mode;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the lexer mode this action should transition the lexer to.\r\n\t *\r\n\t * @returns The lexer mode for this `mode` command.\r\n\t */\r\n\tget mode(): number {\r\n\t\treturn this._mode;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns {@link LexerActionType#MODE}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.MODE;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `false`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This action is implemented by calling {@link Lexer#mode} with the\r\n\t * value provided by {@link #getMode}.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.mode(this._mode);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\thash = MurmurHash.update(hash, this._mode);\r\n\t\treturn MurmurHash.finish(hash, 2);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerModeAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._mode === obj._mode;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn `mode(${this._mode})`;\r\n\t}\r\n}\r\n"]}