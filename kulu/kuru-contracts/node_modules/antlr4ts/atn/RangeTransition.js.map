{"version": 3, "file": "RangeTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/RangeTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,qDAAkD;AAClD,8CAAkD;AAClD,6CAA0C;AAG1C,IAAa,eAAe,GAA5B,MAAa,eAAgB,SAAQ,uBAAU;IAI9C,YAAqB,MAAgB,EAAE,IAAY,EAAE,EAAU;QAC9D,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACd,CAAC;IAGD,IAAI,iBAAiB;QACpB,qBAA4B;IAC7B,CAAC;IAID,IAAI,KAAK;QACR,OAAO,yBAAW,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC;IACjD,CAAC;IAIM,QAAQ;QACd,OAAO,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;IAC7F,CAAC;CACD,CAAA;AApBA;IADC,qBAAQ;wDAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;4CAGP;AAGD;IADC,qBAAQ;8CAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;+CAGP;AA9BW,eAAe;IAId,WAAA,oBAAO,CAAA;GAJR,eAAe,CA+B3B;AA/BY,0CAAe", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.5959980-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\nexport class RangeTransition extends Transition {\r\n\tpublic from: number;\r\n\tpublic to: number;\r\n\r\n\tconstructor(@NotNull target: ATNState, from: number, to: number) {\r\n\t\tsuper(target);\r\n\t\tthis.from = from;\r\n\t\tthis.to = to;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.RANGE;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tget label(): IntervalSet {\r\n\t\treturn IntervalSet.of(this.from, this.to);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn symbol >= this.from && symbol <= this.to;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn \"'\" + String.fromCodePoint(this.from) + \"'..'\" + String.fromCodePoint(this.to) + \"'\";\r\n\t}\r\n}\r\n"]}