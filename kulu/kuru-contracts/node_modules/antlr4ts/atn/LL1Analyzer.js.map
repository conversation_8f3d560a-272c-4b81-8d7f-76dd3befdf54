{"version": 3, "file": "LL1Analyzer.js", "sourceRoot": "", "sources": ["../../../src/atn/LL1Analyzer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,+EAA4E;AAC5E,2DAAwD;AAExD,2CAAwC;AAExC,2CAAwC;AACxC,qDAAkD;AAClD,8CAAwC;AACxC,yDAAsD;AACtD,+EAA4E;AAC5E,2DAAwD;AACxD,mDAAgD;AAChD,qDAAkD;AAElD,oCAAiC;AAEjC,6DAA0D;AAE1D,IAAa,WAAW,GAAxB,MAAa,WAAW;IASvB,YAAqB,GAAQ,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAElD;;;;;;;;;OASG;IACI,oBAAoB,CAAC,CAAuB;QACpD,kDAAkD;QAChD,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,IAAI,GAAmC,IAAI,KAAK,CAAc,CAAC,CAAC,mBAAmB,CAAC,CAAC;QACzF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,mBAAmB,EAAE,GAAG,EAAE,EAAE;YACrD,IAAI,OAAO,GAA4B,IAAI,yBAAW,EAAE,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;YACpB,IAAI,QAAQ,GAA8B,IAAI,+BAAc,CAAY,mDAAwB,CAAC,QAAQ,CAAC,CAAC;YAC3G,IAAI,YAAY,GAAY,KAAK,CAAC,CAAC,kCAAkC;YACrE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,qCAAiB,CAAC,WAAW,EAC5E,OAAO,EAAE,QAAQ,EAAE,IAAI,eAAM,EAAE,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;YACvD,8DAA8D;YAC9D,8CAA8C;YAC9C,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;gBACjE,OAAO,GAAG,SAAS,CAAC;gBACpB,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;aACpB;SACD;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IA2CM,IAAI,CAAU,CAAW,EAAW,GAAsB,EAAE,SAA2B;QAC7F,IAAI,SAAS,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;aACjC;YAED,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;SAC/C;aAAM,IAAI,SAAS,KAAK,IAAI,EAAE;YAC9B,2GAA2G;YAC3G,8DAA8D;YAC9D,SAAS,GAAG,SAAS,CAAC;SACtB;QAED,IAAI,CAAC,GAAgB,IAAI,yBAAW,EAAE,CAAC;QACvC,IAAI,YAAY,GAAY,IAAI,CAAC,CAAC,kCAAkC;QACpE,IAAI,MAAM,GAAY,IAAI,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,+BAAc,EAAa,EAAE,IAAI,eAAM,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACO,KAAK,CACL,CAAW,EACpB,SAA+B,EACtB,GAAsB,EACtB,IAAiB,EACjB,QAAmC,EACnC,eAAuB,EAChC,YAAqB,EACrB,MAAe;QACjB,4DAA4D;QAC1D,IAAI,CAAC,GAAc,qBAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACrB,OAAO;SACP;QAED,IAAI,CAAC,KAAK,SAAS,EAAE;YACpB,IAAI,qCAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;gBACxC,IAAI,CAAC,GAAG,CAAC,aAAK,CAAC,OAAO,CAAC,CAAC;gBACxB,OAAO;aACP;iBAAM,IAAI,GAAG,CAAC,OAAO,EAAE;gBACvB,IAAI,MAAM,EAAE;oBACX,IAAI,CAAC,GAAG,CAAC,aAAK,CAAC,GAAG,CAAC,CAAC;iBACpB;gBAED,OAAO;aACP;SACD;QAED,IAAI,CAAC,YAAY,6BAAa,EAAE;YAC/B,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,qCAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;gBACxD,IAAI,MAAM,EAAE;oBACX,IAAI,CAAC,GAAG,CAAC,aAAK,CAAC,GAAG,CAAC,CAAC;iBACpB;gBAED,OAAO;aACP;YAED,IAAI,OAAO,GAAY,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI;gBACH,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;oBAClC,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;wBACrE,SAAS;qBACT;oBAED,IAAI,WAAW,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxE,uDAAuD;oBAClD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;iBAC5G;aACD;oBACO;gBACP,IAAI,OAAO,EAAE;oBACZ,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACjC;aACD;SACD;QAED,IAAI,CAAC,GAAW,CAAC,CAAC,mBAAmB,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAe,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,YAAY,+BAAc,EAAE;gBAChC,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;oBACrC,SAAS;iBACT;gBAED,IAAI,UAAU,GAAsB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAE5E,IAAI;oBACH,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;iBACnG;wBACO;oBACP,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACnC;aACD;iBACI,IAAI,CAAC,YAAY,yDAA2B,EAAE;gBAClD,IAAI,YAAY,EAAE;oBACjB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;iBAC5F;qBACI;oBACJ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;iBAC/B;aACD;iBACI,IAAI,CAAC,CAAC,SAAS,EAAE;gBACrB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;aAC5F;iBACI,IAAI,CAAC,YAAY,uCAAkB,EAAE;gBACzC,IAAI,CAAC,MAAM,CAAC,yBAAW,CAAC,EAAE,CAAC,aAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;aAC9E;iBACI;gBACR,uCAAuC;gBACnC,IAAI,GAAG,GAA4B,CAAC,CAAC,KAAK,CAAC;gBAC3C,IAAI,GAAG,IAAI,IAAI,EAAE;oBAChB,IAAI,CAAC,YAAY,mCAAgB,EAAE;wBAClC,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,yBAAW,CAAC,EAAE,CAAC,aAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;qBACvF;oBACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBACjB;aACD;SACD;IACF,CAAC;CACD,CAAA;AA7OA;;GAEG;AACoB,oBAAQ,GAAW,aAAK,CAAC,YAAY,CAAC;AAG7D;IADC,oBAAO;wCACQ;AA+EhB;IADC,oBAAO;IACK,WAAA,oBAAO,CAAA,EAAe,WAAA,oBAAO,CAAA;uCAkBzC;AAiCD;IACE,WAAA,oBAAO,CAAA;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;wCA8FR;AA7OW,WAAW;IASV,WAAA,oBAAO,CAAA;GATR,WAAW,CA8OvB;AA9OY,kCAAW", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.4445360-07:00\r\n\r\nimport { AbstractPredicateTransition } from \"./AbstractPredicateTransition\";\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { NotSetTransition } from \"./NotSetTransition\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\nimport { SetTransition } from \"./SetTransition\";\r\nimport { Token } from \"../Token\";\r\nimport { Transition } from \"./Transition\";\r\nimport { WildcardTransition } from \"./WildcardTransition\";\r\n\r\nexport class LL1Analyzer {\r\n\t/** Special value added to the lookahead sets to indicate that we hit\r\n\t *  a predicate during analysis if `seeThruPreds==false`.\r\n\t */\r\n\tpublic static readonly HIT_PRED: number = Token.INVALID_TYPE;\r\n\r\n\t@NotNull\r\n\tpublic atn: ATN;\r\n\r\n\tconstructor(@NotNull atn: ATN) { this.atn = atn; }\r\n\r\n\t/**\r\n\t * Calculates the SLL(1) expected lookahead set for each outgoing transition\r\n\t * of an {@link ATNState}. The returned array has one element for each\r\n\t * outgoing transition in `s`. If the closure from transition\r\n\t * *i* leads to a semantic predicate before matching a symbol, the\r\n\t * element at index *i* of the result will be `undefined`.\r\n\t *\r\n\t * @param s the ATN state\r\n\t * @returns the expected symbols for each outgoing transition of `s`.\r\n\t */\r\n\tpublic getDecisionLookahead(s: ATNState | undefined): Array<IntervalSet | undefined> | undefined {\r\n//\t\tSystem.out.println(\"LOOK(\"+s.stateNumber+\")\");\r\n\t\tif (s == null) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet look: Array<IntervalSet | undefined> = new Array<IntervalSet>(s.numberOfTransitions);\r\n\t\tfor (let alt = 0; alt < s.numberOfTransitions; alt++) {\r\n\t\t\tlet current: IntervalSet | undefined = new IntervalSet();\r\n\t\t\tlook[alt] = current;\r\n\t\t\tlet lookBusy: Array2DHashSet<ATNConfig> = new Array2DHashSet<ATNConfig>(ObjectEqualityComparator.INSTANCE);\r\n\t\t\tlet seeThruPreds: boolean = false; // fail to get lookahead upon pred\r\n\t\t\tthis._LOOK(s.transition(alt).target, undefined, PredictionContext.EMPTY_LOCAL,\r\n\t\t\t\tcurrent, lookBusy, new BitSet(), seeThruPreds, false);\r\n\t\t\t// Wipe out lookahead for this alternative if we found nothing\r\n\t\t\t// or we had a predicate when we !seeThruPreds\r\n\t\t\tif (current.size === 0 || current.contains(LL1Analyzer.HIT_PRED)) {\r\n\t\t\t\tcurrent = undefined;\r\n\t\t\t\tlook[alt] = current;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn look;\r\n\t}\r\n\r\n\t/**\r\n\t * Compute set of tokens that can follow `s` in the ATN in the\r\n\t * specified `ctx`.\r\n\t *\r\n\t * If `ctx` is `undefined` and the end of the rule containing\r\n\t * `s` is reached, {@link Token#EPSILON} is added to the result set.\r\n\t * If `ctx` is not `undefined` and the end of the outermost rule is\r\n\t * reached, {@link Token#EOF} is added to the result set.\r\n\t *\r\n\t * @param s the ATN state\r\n\t * @param ctx the complete parser context, or `undefined` if the context\r\n\t * should be ignored\r\n\t *\r\n\t * @returns The set of tokens that can follow `s` in the ATN in the\r\n\t * specified `ctx`.\r\n\t */\r\n\t// @NotNull\r\n\tpublic LOOK(/*@NotNull*/ s: ATNState, /*@NotNull*/ ctx: PredictionContext): IntervalSet;\r\n\r\n\t/**\r\n\t * Compute set of tokens that can follow `s` in the ATN in the\r\n\t * specified `ctx`.\r\n\t *\r\n\t * If `ctx` is `undefined` and the end of the rule containing\r\n\t * `s` is reached, {@link Token#EPSILON} is added to the result set.\r\n\t * If `ctx` is not `PredictionContext#EMPTY_LOCAL` and the end of the outermost rule is\r\n\t * reached, {@link Token#EOF} is added to the result set.\r\n\t *\r\n\t * @param s the ATN state\r\n\t * @param stopState the ATN state to stop at. This can be a\r\n\t * {@link BlockEndState} to detect epsilon paths through a closure.\r\n\t * @param ctx the complete parser context, or `undefined` if the context\r\n\t * should be ignored\r\n\t *\r\n\t * @returns The set of tokens that can follow `s` in the ATN in the\r\n\t * specified `ctx`.\r\n\t */\r\n\t// @NotNull\r\n\tpublic LOOK(/*@NotNull*/ s: ATNState, /*@NotNull*/ ctx: PredictionContext, stopState: ATNState | null): IntervalSet;\r\n\r\n\t@NotNull\r\n\tpublic LOOK(@NotNull s: ATNState, @NotNull ctx: PredictionContext, stopState?: ATNState | null): IntervalSet {\r\n\t\tif (stopState === undefined) {\r\n\t\t\tif (s.atn == null) {\r\n\t\t\t\tthrow new Error(\"Illegal state\");\r\n\t\t\t}\r\n\r\n\t\t\tstopState = s.atn.ruleToStopState[s.ruleIndex];\r\n\t\t} else if (stopState === null) {\r\n\t\t\t// This is an explicit request to pass undefined as the stopState to _LOOK. Used to distinguish an overload\r\n\t\t\t// from the method which simply omits the stopState parameter.\r\n\t\t\tstopState = undefined;\r\n\t\t}\r\n\r\n\t\tlet r: IntervalSet = new IntervalSet();\r\n\t\tlet seeThruPreds: boolean = true; // ignore preds; get all lookahead\r\n\t\tlet addEOF: boolean = true;\r\n\t\tthis._LOOK(s, stopState, ctx, r, new Array2DHashSet<ATNConfig>(), new BitSet(), seeThruPreds, addEOF);\r\n\t\treturn r;\r\n\t}\r\n\r\n\t/**\r\n\t * Compute set of tokens that can follow `s` in the ATN in the\r\n\t * specified `ctx`.\r\n\t * <p/>\r\n\t * If `ctx` is {@link PredictionContext#EMPTY_LOCAL} and\r\n\t * `stopState` or the end of the rule containing `s` is reached,\r\n\t * {@link Token#EPSILON} is added to the result set. If `ctx` is not\r\n\t * {@link PredictionContext#EMPTY_LOCAL} and `addEOF` is `true`\r\n\t * and `stopState` or the end of the outermost rule is reached,\r\n\t * {@link Token#EOF} is added to the result set.\r\n\t *\r\n\t * @param s the ATN state.\r\n\t * @param stopState the ATN state to stop at. This can be a\r\n\t * {@link BlockEndState} to detect epsilon paths through a closure.\r\n\t * @param ctx The outer context, or {@link PredictionContext#EMPTY_LOCAL} if\r\n\t * the outer context should not be used.\r\n\t * @param look The result lookahead set.\r\n\t * @param lookBusy A set used for preventing epsilon closures in the ATN\r\n\t * from causing a stack overflow. Outside code should pass\r\n\t * `new HashSet<ATNConfig>` for this argument.\r\n\t * @param calledRuleStack A set used for preventing left recursion in the\r\n\t * ATN from causing a stack overflow. Outside code should pass\r\n\t * `new BitSet()` for this argument.\r\n\t * @param seeThruPreds `true` to true semantic predicates as\r\n\t * implicitly `true` and \"see through them\", otherwise `false`\r\n\t * to treat semantic predicates as opaque and add {@link #HIT_PRED} to the\r\n\t * result if one is encountered.\r\n\t * @param addEOF Add {@link Token#EOF} to the result if the end of the\r\n\t * outermost context is reached. This parameter has no effect if `ctx`\r\n\t * is {@link PredictionContext#EMPTY_LOCAL}.\r\n\t */\r\n\tprotected _LOOK(\r\n\t\t@NotNull s: ATNState,\r\n\t\tstopState: ATNState | undefined,\r\n\t\t@NotNull ctx: PredictionContext,\r\n\t\t@NotNull look: IntervalSet,\r\n\t\t@NotNull lookBusy: Array2DHashSet<ATNConfig>,\r\n\t\t@NotNull calledRuleStack: BitSet,\r\n\t\tseeThruPreds: boolean,\r\n\t\taddEOF: boolean): void {\r\n//\t\tSystem.out.println(\"_LOOK(\"+s.stateNumber+\", ctx=\"+ctx);\r\n\t\tlet c: ATNConfig = ATNConfig.create(s, 0, ctx);\r\n\t\tif (!lookBusy.add(c)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (s === stopState) {\r\n\t\t\tif (PredictionContext.isEmptyLocal(ctx)) {\r\n\t\t\t\tlook.add(Token.EPSILON);\r\n\t\t\t\treturn;\r\n\t\t\t} else if (ctx.isEmpty) {\r\n\t\t\t\tif (addEOF) {\r\n\t\t\t\t\tlook.add(Token.EOF);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (s instanceof RuleStopState) {\r\n\t\t\tif (ctx.isEmpty && !PredictionContext.isEmptyLocal(ctx)) {\r\n\t\t\t\tif (addEOF) {\r\n\t\t\t\t\tlook.add(Token.EOF);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tlet removed: boolean = calledRuleStack.get(s.ruleIndex);\r\n\t\t\ttry {\r\n\t\t\t\tcalledRuleStack.clear(s.ruleIndex);\r\n\t\t\t\tfor (let i = 0; i < ctx.size; i++) {\r\n\t\t\t\t\tif (ctx.getReturnState(i) === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet returnState: ATNState = this.atn.states[ctx.getReturnState(i)];\r\n//\t\t\t\t\tSystem.out.println(\"popping back to \"+retState);\r\n\t\t\t\t\tthis._LOOK(returnState, stopState, ctx.getParent(i), look, lookBusy, calledRuleStack, seeThruPreds, addEOF);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tfinally {\r\n\t\t\t\tif (removed) {\r\n\t\t\t\t\tcalledRuleStack.set(s.ruleIndex);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet n: number = s.numberOfTransitions;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tlet t: Transition = s.transition(i);\r\n\t\t\tif (t instanceof RuleTransition) {\r\n\t\t\t\tif (calledRuleStack.get(t.ruleIndex)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet newContext: PredictionContext = ctx.getChild(t.followState.stateNumber);\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tcalledRuleStack.set(t.ruleIndex);\r\n\t\t\t\t\tthis._LOOK(t.target, stopState, newContext, look, lookBusy, calledRuleStack, seeThruPreds, addEOF);\r\n\t\t\t\t}\r\n\t\t\t\tfinally {\r\n\t\t\t\t\tcalledRuleStack.clear(t.ruleIndex);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse if (t instanceof AbstractPredicateTransition) {\r\n\t\t\t\tif (seeThruPreds) {\r\n\t\t\t\t\tthis._LOOK(t.target, stopState, ctx, look, lookBusy, calledRuleStack, seeThruPreds, addEOF);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tlook.add(LL1Analyzer.HIT_PRED);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse if (t.isEpsilon) {\r\n\t\t\t\tthis._LOOK(t.target, stopState, ctx, look, lookBusy, calledRuleStack, seeThruPreds, addEOF);\r\n\t\t\t}\r\n\t\t\telse if (t instanceof WildcardTransition) {\r\n\t\t\t\tlook.addAll(IntervalSet.of(Token.MIN_USER_TOKEN_TYPE, this.atn.maxTokenType));\r\n\t\t\t}\r\n\t\t\telse {\r\n//\t\t\t\tSystem.out.println(\"adding \"+ t);\r\n\t\t\t\tlet set: IntervalSet | undefined = t.label;\r\n\t\t\t\tif (set != null) {\r\n\t\t\t\t\tif (t instanceof NotSetTransition) {\r\n\t\t\t\t\t\tset = set.complement(IntervalSet.of(Token.MIN_USER_TOKEN_TYPE, this.atn.maxTokenType));\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlook.addAll(set);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n"]}