{"version": 3, "file": "DecisionEventInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/DecisionEventInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,8CAAwC;AAIxC;;;;;;;;;;;;;;GAcG;AACH,IAAa,iBAAiB,GAA9B,MAAa,iBAAiB;IAsC7B,YACC,QAAgB,EAChB,KAAiC,EACxB,KAAkB,EAC3B,UAAkB,EAClB,SAAiB,EACjB,OAAgB;QAEhB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;CACD,CAAA;AAlCA;IADC,oBAAO;gDACkB;AAnBd,iBAAiB;IAyC3B,WAAA,oBAAO,CAAA;GAzCG,iBAAiB,CAqD7B;AArDY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.2401032-07:00\r\n\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This is the base class for gathering detailed information about prediction\r\n * events which occur during parsing.\r\n *\r\n * Note that we could record the parser call stack at the time this event\r\n * occurred but in the presence of left recursive rules, the stack is kind of\r\n * meaningless. It's better to look at the individual configurations for their\r\n * individual stacks. Of course that is a {@link PredictionContext} object\r\n * not a parse tree node and so it does not have information about the extent\r\n * (start...stop) of the various subtrees. Examining the stack tops of all\r\n * configurations provide the return states for the rule invocations.\r\n * From there you can get the enclosing rule.\r\n *\r\n * @since 4.3\r\n */\r\nexport class DecisionEventInfo {\r\n\t/**\r\n\t * The invoked decision number which this event is related to.\r\n\t *\r\n\t * @see ATN#decisionToState\r\n\t */\r\n\tpublic decision: number;\r\n\r\n\t/**\r\n\t * The simulator state containing additional information relevant to the\r\n\t * prediction state when the current event occurred, or `undefined` if no\r\n\t * additional information is relevant or available.\r\n\t */\r\n\tpublic state: SimulatorState | undefined;\r\n\r\n\t/**\r\n\t * The input token stream which is being parsed.\r\n\t */\r\n\t@NotNull\r\n\tpublic input: TokenStream;\r\n\r\n\t/**\r\n\t * The token index in the input stream at which the current prediction was\r\n\t * originally invoked.\r\n\t */\r\n\tpublic startIndex: number;\r\n\r\n\t/**\r\n\t * The token index in the input stream at which the current event occurred.\r\n\t */\r\n\tpublic stopIndex: number;\r\n\r\n\t/**\r\n\t * `true` if the current event occurred during LL prediction;\r\n\t * otherwise, `false` if the input occurred during SLL prediction.\r\n\t */\r\n\tpublic fullCtx: boolean;\r\n\r\n\tconstructor(\r\n\t\tdecision: number,\r\n\t\tstate: SimulatorState | undefined,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tfullCtx: boolean) {\r\n\r\n\t\tthis.decision = decision;\r\n\t\tthis.fullCtx = fullCtx;\r\n\t\tthis.stopIndex = stopIndex;\r\n\t\tthis.input = input;\r\n\t\tthis.startIndex = startIndex;\r\n\t\tthis.state = state;\r\n\t}\r\n}\r\n"]}