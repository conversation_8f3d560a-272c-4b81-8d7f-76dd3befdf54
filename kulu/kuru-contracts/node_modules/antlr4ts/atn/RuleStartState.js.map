{"version": 3, "file": "RuleStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/RuleStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAC9C,8CAAyC;AAGzC,MAAa,cAAe,SAAQ,mBAAQ;IAA5C;;QAGQ,qBAAgB,GAAY,KAAK,CAAC;QAClC,iBAAY,GAAY,KAAK,CAAC;IAMtC,CAAC;IAHA,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,UAAU,CAAC;IAChC,CAAC;CACD;AAHA;IADC,qBAAQ;+CAGR;AATF,wCAUC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.6806851-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { Override } from \"../Decorators\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\n\r\nexport class RuleStartState extends ATNState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic stopState!: RuleStopState;\r\n\tpublic isPrecedenceRule: boolean = false;\r\n\tpublic leftFactored: boolean = false;\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.RULE_START;\r\n\t}\r\n}\r\n"]}