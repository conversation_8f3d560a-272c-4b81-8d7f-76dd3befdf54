{"version": 3, "file": "BlockEndState.js", "sourceRoot": "", "sources": ["../../../src/atn/BlockEndState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAE9C,8CAAyC;AAEzC,iDAAiD;AACjD,MAAa,aAAc,SAAQ,mBAAQ;IAK1C,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,SAAS,CAAC;IAC/B,CAAC;CACD;AAHA;IADC,qBAAQ;8CAGR;AAPF,sCAQC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.9125304-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BlockStartState } from \"./BlockStartState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** Terminal node of a simple `(a|b|c)` block. */\r\nexport class BlockEndState extends ATNState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic startState!: BlockStartState;\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.BLOCK_END;\r\n\t}\r\n}\r\n"]}