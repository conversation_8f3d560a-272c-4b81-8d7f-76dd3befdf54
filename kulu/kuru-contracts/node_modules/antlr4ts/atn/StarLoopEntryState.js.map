{"version": 3, "file": "StarLoopEntryState.js", "sourceRoot": "", "sources": ["../../../src/atn/StarLoopEntryState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iDAA8C;AAC9C,2CAAwC;AACxC,mDAAgD;AAChD,8CAAyC;AAGzC,MAAa,kBAAmB,SAAQ,6BAAa;IAArD;;QAIC;;;;;;;;;WASG;QACI,2BAAsB,GAAY,KAAK,CAAC;QAE/C;;;;;;;;;;;;WAYG;QACI,6BAAwB,GAAW,IAAI,eAAM,EAAE,CAAC;IAMxD,CAAC;IAHA,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,eAAe,CAAC;IACrC,CAAC;CACD;AAHA;IADC,qBAAQ;mDAGR;AAlCF,gDAmCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.7099201-07:00\r\n\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { Override } from \"../Decorators\";\r\nimport { StarLoopbackState } from \"./StarLoopbackState\";\r\n\r\nexport class StarLoopEntryState extends DecisionState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic loopBackState!: StarLoopbackState;\r\n\r\n\t/**\r\n\t * Indicates whether this state can benefit from a precedence DFA during SLL\r\n\t * decision making.\r\n\t *\r\n\t * This is a computed property that is calculated during ATN deserialization\r\n\t * and stored for use in {@link ParserATNSimulator} and\r\n\t * {@link ParserInterpreter}.\r\n\t *\r\n\t * @see `DFA.isPrecedenceDfa`\r\n\t */\r\n\tpublic precedenceRuleDecision: boolean = false;\r\n\r\n\t/**\r\n\t * For precedence decisions, this set marks states *S* which have all\r\n\t * of the following characteristics:\r\n\t *\r\n\t * * One or more invocation sites of the current rule returns to\r\n\t *   *S*.\r\n\t * * The closure from *S* includes the current decision without\r\n\t *   passing through any rule invocations or stepping out of the current\r\n\t *   rule.\r\n\t *\r\n\t * This field is not used when {@link #precedenceRuleDecision} is\r\n\t * `false`.\r\n\t */\r\n\tpublic precedenceLoopbackStates: BitSet = new BitSet();\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.STAR_LOOP_ENTRY;\r\n\t}\r\n}\r\n"]}