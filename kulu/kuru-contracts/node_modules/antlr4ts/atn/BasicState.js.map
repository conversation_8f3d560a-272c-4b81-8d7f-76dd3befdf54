{"version": 3, "file": "BasicState.js", "sourceRoot": "", "sources": ["../../../src/atn/BasicState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAC9C,8CAAyC;AAEzC;;;GAGG;AACH,MAAa,UAAW,SAAQ,mBAAQ;IAGvC,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,KAAK,CAAC;IAC3B,CAAC;CAED;AAJA;IADC,qBAAQ;2CAGR;AALF,gCAOC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.8389930-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class BasicState extends ATNState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.BASIC;\r\n\t}\r\n\r\n}\r\n"]}