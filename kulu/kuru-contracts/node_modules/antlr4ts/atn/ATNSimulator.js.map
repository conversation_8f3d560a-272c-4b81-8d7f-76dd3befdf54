{"version": 3, "file": "ATNSimulator.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNSimulator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,iDAA8C;AAC9C,8CAA2C;AAC3C,8CAAwC;AACxC,2DAAwD;AAExD,IAAsB,YAAY,GAAlC,MAAsB,YAAY;IAgBjC,YAAqB,GAAQ;QAC5B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IAChB,CAAC;IAdD,MAAM,KAAK,KAAK;QACf,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACzB,YAAY,CAAC,MAAM,GAAG,IAAI,mBAAQ,CAAC,IAAI,2BAAY,EAAE,CAAC,CAAC;YACvD,YAAY,CAAC,MAAM,CAAC,WAAW,GAAG,qCAAiB,CAAC,oBAAoB,CAAC;SACzE;QAED,OAAO,YAAY,CAAC,MAAM,CAAC;IAC5B,CAAC;IAWD;;;;;;;;;;OAUG;IACI,QAAQ;QACd,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IACrB,CAAC;CACD,CAAA;AAtBA;IADC,oBAAO;yCACQ;AAVhB;IADC,oBAAO;+BAQP;AAXoB,YAAY;IAgBpB,WAAA,oBAAO,CAAA;GAhBC,YAAY,CAoCjC;AApCqB,oCAAY;AAsClC,WAAiB,YAAY;IAC5B,MAAM,sBAAsB,GAAW,GAAG,CAAC;IAC3C,MAAM,sBAAsB,GAAW,MAAM,CAAC;IAC9C,MAAM,wBAAwB,GAAW,QAAQ,CAAC;AACnD,CAAC,EAJgB,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAI5B;AA1CqB,oCAAY", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.3184311-07:00\r\n\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { DFAState } from \"../dfa/DFAState\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\n\r\nexport abstract class ATNSimulator {\r\n\t/** Must distinguish between missing edge and edge we know leads nowhere */\r\n\tprivate static _ERROR: DFAState;\r\n\t@NotNull\r\n\tstatic get ERROR(): DFAState {\r\n\t\tif (!ATNSimulator._ERROR) {\r\n\t\t\tATNSimulator._ERROR = new DFAState(new ATNConfigSet());\r\n\t\t\tATNSimulator._ERROR.stateNumber = PredictionContext.EMPTY_FULL_STATE_KEY;\r\n\t\t}\r\n\r\n\t\treturn ATNSimulator._ERROR;\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic atn: ATN;\r\n\r\n\tconstructor(@NotNull atn: ATN) {\r\n\t\tthis.atn = atn;\r\n\t}\r\n\r\n\tpublic abstract reset(): void;\r\n\r\n\t/**\r\n\t * Clear the DFA cache used by the current instance. Since the DFA cache may\r\n\t * be shared by multiple ATN simulators, this method may affect the\r\n\t * performance (but not accuracy) of other parsers which are being used\r\n\t * concurrently.\r\n\t *\r\n\t * @ if the current instance does not\r\n\t * support clearing the DFA.\r\n\t *\r\n\t * @since 4.3\r\n\t */\r\n\tpublic clearDFA(): void {\r\n\t\tthis.atn.clearDFA();\r\n\t}\r\n}\r\n\r\nexport namespace ATNSimulator {\r\n\tconst RULE_VARIANT_DELIMITER: string = \"$\";\r\n\tconst RULE_LF_VARIANT_MARKER: string = \"$lf$\";\r\n\tconst RULE_NOLF_VARIANT_MARKER: string = \"$nolf$\";\r\n}\r\n"]}