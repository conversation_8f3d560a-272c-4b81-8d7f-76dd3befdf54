{"version": 3, "file": "PredicateTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/PredicateTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,+EAA4E;AAE5E,8CAAkD;AAClD,uDAAoD;AAGpD;;;;;GAKG;AACH,IAAa,mBAAmB,GAAhC,MAAa,mBAAoB,SAAQ,yDAA2B;IAKnE,YAAqB,MAAgB,EAAE,SAAiB,EAAE,SAAiB,EAAE,cAAuB;QACnG,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACtC,CAAC;IAGD,IAAI,iBAAiB;QACpB,yBAAgC;IACjC,CAAC;IAGD,IAAI,SAAS,KAAc,OAAO,IAAI,CAAC,CAAC,CAAC;IAGlC,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,iCAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3F,CAAC;IAIM,QAAQ;QACd,OAAO,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;IACxD,CAAC;CACD,CAAA;AArBA;IADC,qBAAQ;4DAGR;AAGD;IADC,qBAAQ;oDACgC;AAGzC;IADC,qBAAQ;kDAGR;AAQD;IAFC,qBAAQ;IACR,oBAAO;mDAGP;AAjCW,mBAAmB;IAKlB,WAAA,oBAAO,CAAA;GALR,mBAAmB,CAkC/B;AAlCY,kDAAmB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.2826960-07:00\r\n\r\nimport { AbstractPredicateTransition } from \"./AbstractPredicateTransition\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/** TODO: this is old comment:\r\n *  A tree of semantic predicates from the grammar AST if label==SEMPRED.\r\n *  In the ATN, labels will always be exactly one predicate, but the DFA\r\n *  may have to combine a bunch of them as it collects predicates from\r\n *  multiple ATN configurations into a single DFA state.\r\n */\r\nexport class PredicateTransition extends AbstractPredicateTransition {\r\n\tpublic ruleIndex: number;\r\n\tpublic predIndex: number;\r\n\tpublic isCtxDependent: boolean;   // e.g., $i ref in pred\r\n\r\n\tconstructor(@NotNull target: ATNState, ruleIndex: number, predIndex: number, isCtxDependent: boolean) {\r\n\t\tsuper(target);\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t\tthis.predIndex = predIndex;\r\n\t\tthis.isCtxDependent = isCtxDependent;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.PREDICATE;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEpsilon(): boolean { return true; }\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget predicate(): SemanticContext.Predicate {\r\n\t\treturn new SemanticContext.Predicate(this.ruleIndex, this.predIndex, this.isCtxDependent);\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn \"pred_\" + this.ruleIndex + \":\" + this.predIndex;\r\n\t}\r\n}\r\n"]}