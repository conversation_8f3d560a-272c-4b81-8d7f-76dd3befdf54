{"version": 3, "file": "LexerPushModeAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerPushModeAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;GAMG;AACH,MAAa,mBAAmB;IAG/B;;;OAGG;IACH,YAAY,IAAY;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;;OAGG;IAEH,IAAI,UAAU;QACb,yBAAiC;IAClC,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,mBAAmB,CAAC,EAAE;YACjD,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC;IACjC,CAAC;IAGM,QAAQ;QACd,OAAO,YAAY,IAAI,CAAC,KAAK,GAAG,CAAC;IAClC,CAAC;CACD;AA/CA;IADC,qBAAQ;qDAGR;AAOD;IADC,qBAAQ;8DAGR;AASD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;kDAEtB;AAGD;IADC,qBAAQ;mDAMR;AAGD;IADC,qBAAQ;iDASR;AAGD;IADC,qBAAQ;mDAGR;AAvEF,kDAwEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.1378801-07:00\r\n\r\nimport { <PERSON>er } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Implements the `pushMode` lexer action by calling\r\n * {@link Lexer#pushMode} with the assigned mode.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerPushModeAction implements LexerAction {\r\n\tprivate readonly _mode: number;\r\n\r\n\t/**\r\n\t * Constructs a new `pushMode` action with the specified mode value.\r\n\t * @param mode The mode value to pass to {@link Lexer#pushMode}.\r\n\t */\r\n\tconstructor(mode: number) {\r\n\t\tthis._mode = mode;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the lexer mode this action should transition the lexer to.\r\n\t *\r\n\t * @returns The lexer mode for this `pushMode` command.\r\n\t */\r\n\tget mode(): number {\r\n\t\treturn this._mode;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns {@link LexerActionType#PUSH_MODE}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.PUSH_MODE;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `false`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This action is implemented by calling {@link Lexer#pushMode} with the\r\n\t * value provided by {@link #getMode}.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.pushMode(this._mode);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\thash = MurmurHash.update(hash, this._mode);\r\n\t\treturn MurmurHash.finish(hash, 2);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerPushModeAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._mode === obj._mode;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn `pushMode(${this._mode})`;\r\n\t}\r\n}\r\n"]}