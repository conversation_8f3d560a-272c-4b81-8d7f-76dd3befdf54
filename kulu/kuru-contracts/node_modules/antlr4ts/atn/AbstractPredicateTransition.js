"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbstractPredicateTransition = void 0;
const Transition_1 = require("./Transition");
/**
 *
 * <AUTHOR>
 */
class AbstractPredicateTransition extends Transition_1.Transition {
    constructor(target) {
        super(target);
    }
}
exports.AbstractPredicateTransition = AbstractPredicateTransition;
//# sourceMappingURL=AbstractPredicateTransition.js.map