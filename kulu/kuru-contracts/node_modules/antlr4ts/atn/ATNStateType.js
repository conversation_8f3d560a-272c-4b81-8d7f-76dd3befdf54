"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ATNStateType = void 0;
// ConvertTo-TS run at 2016-10-04T11:26:27.4734328-07:00
var ATNStateType;
(function (ATNStateType) {
    ATNStateType[ATNStateType["INVALID_TYPE"] = 0] = "INVALID_TYPE";
    ATNStateType[ATNStateType["BASIC"] = 1] = "BASIC";
    ATNStateType[ATNStateType["RULE_START"] = 2] = "RULE_START";
    ATNStateType[ATNStateType["BLOCK_START"] = 3] = "BLOCK_START";
    ATNStateType[ATNStateType["PLUS_BLOCK_START"] = 4] = "PLUS_BLOCK_START";
    ATNStateType[ATNStateType["STAR_BLOCK_START"] = 5] = "STAR_BLOCK_START";
    ATNStateType[ATNStateType["TOKEN_START"] = 6] = "TOKEN_START";
    ATNStateType[ATNStateType["RULE_STOP"] = 7] = "RULE_STOP";
    ATNStateType[ATNStateType["BLOCK_END"] = 8] = "BLOCK_END";
    ATNStateType[ATNStateType["STAR_LOOP_BACK"] = 9] = "STAR_LOOP_BACK";
    ATNStateType[ATNStateType["STAR_LOOP_ENTRY"] = 10] = "STAR_LOOP_ENTRY";
    ATNStateType[ATNStateType["PLUS_LOOP_BACK"] = 11] = "PLUS_LOOP_BACK";
    ATNStateType[ATNStateType["LOOP_END"] = 12] = "LOOP_END";
})(ATNStateType = exports.ATNStateType || (exports.ATNStateType = {}));
//# sourceMappingURL=ATNStateType.js.map