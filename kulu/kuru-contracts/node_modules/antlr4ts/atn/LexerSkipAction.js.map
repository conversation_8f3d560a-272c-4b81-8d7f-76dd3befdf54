{"version": 3, "file": "LexerSkipAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerSkipAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;;;GAQG;AACH,MAAa,eAAe;IAC3B;;OAEG;IACH;QACC,sBAAsB;IACvB,CAAC;IAED;;;OAGG;IAEH,IAAI,UAAU;QACb,oBAA4B;IAC7B,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;OAIG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,OAAO,GAAG,KAAK,IAAI,CAAC;IACrB,CAAC;IAGM,QAAQ;QACd,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAvCA;IADC,qBAAQ;iDAGR;AAOD;IADC,qBAAQ;0DAGR;AAQD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;8CAEtB;AAGD;IADC,qBAAQ;+CAKR;AAGD;IADC,qBAAQ;6CAGR;AAGD;IADC,qBAAQ;+CAGR;AAnDF,0CAoDC;AAED,WAAiB,eAAe;IAC/B;;OAEG;IACU,wBAAQ,GAAoB,IAAI,eAAe,EAAE,CAAC;AAChE,CAAC,EALgB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAK/B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.2324460-07:00\r\n\r\nimport { <PERSON><PERSON> } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Implements the `skip` lexer action by calling {@link Lexer#skip}.\r\n *\r\n * The `skip` command does not have any parameters, so this action is\r\n * implemented as a singleton instance exposed by {@link #INSTANCE}.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerSkipAction implements LexerAction {\r\n\t/**\r\n\t * Constructs the singleton instance of the lexer `skip` command.\r\n\t */\r\n\tconstructor() {\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns {@link LexerActionType#SKIP}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.SKIP;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `false`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This action is implemented by calling {@link Lexer#skip}.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.skip();\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\treturn MurmurHash.finish(hash, 1);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\treturn obj === this;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn \"skip\";\r\n\t}\r\n}\r\n\r\nexport namespace LexerSkipAction {\r\n\t/**\r\n\t * Provides a singleton instance of this parameterless lexer action.\r\n\t */\r\n\texport const INSTANCE: LexerSkipAction = new LexerSkipAction();\r\n}\r\n"]}