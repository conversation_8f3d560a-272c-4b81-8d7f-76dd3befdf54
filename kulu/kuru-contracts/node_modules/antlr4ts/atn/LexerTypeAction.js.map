{"version": 3, "file": "LexerTypeAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerTypeAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;GAMG;AACH,MAAa,eAAe;IAG3B;;;OAGG;IACH,YAAY,IAAY;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;;OAGG;IAEH,IAAI,UAAU;QACb,oBAA4B;IAC7B,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,eAAe,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC;IACjC,CAAC;IAGM,QAAQ;QACd,OAAO,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC;IAC9B,CAAC;CACD;AA/CA;IADC,qBAAQ;iDAGR;AAOD;IADC,qBAAQ;0DAGR;AASD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;8CAEtB;AAGD;IADC,qBAAQ;+CAMR;AAGD;IADC,qBAAQ;6CASR;AAGD;IADC,qBAAQ;+CAGR;AAtEF,0CAuEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.3204839-07:00\r\n\r\nimport { <PERSON>er } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Implements the `type` lexer action by setting `Lexer.type`\r\n * with the assigned type.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerTypeAction implements LexerAction {\r\n\tprivate readonly _type: number;\r\n\r\n\t/**\r\n\t * Constructs a new `type` action with the specified token type value.\r\n\t * @param type The type to assign to the token using `Lexer.type`.\r\n\t */\r\n\tconstructor(type: number) {\r\n\t\tthis._type = type;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the type to assign to a token created by the lexer.\r\n\t * @returns The type to assign to a token created by the lexer.\r\n\t */\r\n\tget type(): number {\r\n\t\treturn this._type;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns {@link LexerActionType#TYPE}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.TYPE;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `false`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This action is implemented by setting `Lexer.type` with the\r\n\t * value provided by `type`.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.type = this._type;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\thash = MurmurHash.update(hash, this._type);\r\n\t\treturn MurmurHash.finish(hash, 2);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerTypeAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._type === obj._type;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn `type(${this._type})`;\r\n\t}\r\n}\r\n"]}