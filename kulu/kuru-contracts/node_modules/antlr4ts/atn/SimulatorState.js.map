{"version": 3, "file": "SimulatorState.js", "sourceRoot": "", "sources": ["../../../src/atn/SimulatorState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAwC;AACxC,4DAAyD;AAEzD;;;GAGG;AACH,IAAa,cAAc,GAA3B,MAAa,cAAc;IAQ1B,YAAY,YAA+B,EAAW,EAAY,EAAE,UAAmB,EAAE,qBAAoD;QAC5I,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,qCAAiB,CAAC,YAAY,EAAE,CAAC;QAC3F,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;IACpD,CAAC;CACD,CAAA;AAdY,cAAc;IAQoB,WAAA,oBAAO,CAAA;GARzC,cAAc,CAc1B;AAdY,wCAAc", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.3871984-07:00\r\n\r\nimport { DFAState } from \"../dfa/DFAState\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class SimulatorState {\r\n\tpublic outerContext: ParserRuleContext;\r\n\r\n\tpublic s0: DFAState;\r\n\r\n\tpublic useContext: boolean;\r\n\tpublic remainingOuterContext: ParserRuleContext | undefined;\r\n\r\n\tconstructor(outerContext: ParserRuleContext, @NotNull s0: DFAState, useContext: boolean, remainingOuterContext: ParserRuleContext | undefined) {\r\n\t\tthis.outerContext = outerContext != null ? outerContext : ParserRuleContext.emptyContext();\r\n\t\tthis.s0 = s0;\r\n\t\tthis.useContext = useContext;\r\n\t\tthis.remainingOuterContext = remainingOuterContext;\r\n\t}\r\n}\r\n"]}