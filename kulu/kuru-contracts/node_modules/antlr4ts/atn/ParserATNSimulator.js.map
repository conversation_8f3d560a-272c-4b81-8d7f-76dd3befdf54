{"version": 3, "file": "ParserATNSimulator.js", "sourceRoot": "", "sources": ["../../../src/atn/ParserATNSimulator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,4DAAyD;AACzD,yDAAsD;AACtD,2DAAwD;AACxD,2CAAwC;AACxC,+BAA4B;AAC5B,2CAAwC;AACxC,iDAA8C;AAC9C,iDAA8C;AAE9C,iDAA8C;AAC9C,qDAAkD;AAClD,2CAAwC;AACxC,iDAA8C;AAC9C,mDAAgD;AAEhD,8CAA2C;AAE3C,qDAAkD;AAClD,+CAA4C;AAC5C,4CAAyC;AACzC,8CAA4D;AAC5D,yDAAsD;AACtD,kEAA+D;AAC/D,+EAA4E;AAE5E,4DAAyD;AAGzD,2DAAwD;AACxD,qEAAkE;AAClE,qDAAkD;AAElD,mDAAgD;AAChD,qDAAkD;AAClD,uDAAoD;AACpD,mDAAgD;AAChD,qDAAkD;AAElD,oCAAiC;AAKjC,sDAAmD;AAEnD,iCAAiC;AAEjC,MAAM,eAAe,GAAG,MAAM,CAAC;AAC/B,MAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAE7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsMG;AACH,IAAa,kBAAkB,GAA/B,MAAa,kBAAmB,SAAQ,2BAAY;IAqDnD,YAAqB,GAAQ,EAAE,MAAc;QAC5C,KAAK,CAAC,GAAG,CAAC,CAAC;QAhDJ,mBAAc,GAAmB,+BAAc,CAAC,EAAE,CAAC;QACpD,yBAAoB,GAAY,KAAK,CAAC;QACtC,6BAAwB,GAAY,IAAI,CAAC;QAEhD;;;;;;;;;;;;WAYG;QACI,8BAAyB,GAAY,KAAK,CAAC;QAC3C,4BAAuB,GAAY,IAAI,CAAC;QACxC,iBAAY,GAAY,IAAI,CAAC;QAC7B,wBAAmB,GAAY,IAAI,CAAC;QACpC,4BAAuB,GAAY,IAAI,CAAC;QACxC,sCAAiC,GAAY,KAAK,CAAC;QAI1D;;;;;;;;WAQG;QACI,sBAAiB,GAAY,KAAK,CAAC;QAE1C;;;;;WAKG;QACO,0BAAqB,GAAY,IAAI,CAAC;QAM/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAGM,iBAAiB;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC5B,CAAC;IAEM,iBAAiB,CAAU,cAA8B;QAC/D,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACtC,CAAC;IAGM,KAAK;QACX,sBAAsB;IACvB,CAAC;IAIM,eAAe,CACZ,KAAkB,EAC3B,QAAgB,EAChB,YAA2C,EAC3C,UAAoB;QACpB,IAAI,UAAU,KAAK,SAAS,EAAE;YAC7B,UAAU,GAAG,KAAK,CAAC;SACnB;QAED,IAAI,GAAG,GAAQ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;YAC9D,IAAI,IAAI,GAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,MAAM,EAAE;gBAChC,IAAI,GAAG,GAAW,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;gBAClD,IAAI,GAAG,GAAuB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzD,IAAI,GAAG,IAAI,IAAI,EAAE;oBAChB,OAAO,GAAG,CAAC;iBACX;aACD;SACD;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC9B,UAAU,GAAG,IAAI,CAAC;SAClB;aACI,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACxC,UAAU,GAAG,UAAU,IAAI,GAAG,CAAC,kBAAkB,CAAC;SAClD;QAED,IAAI,CAAC,qBAAqB,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,+BAAc,CAAC,GAAG,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3J,IAAI,YAAY,IAAI,IAAI,EAAE;YACzB,YAAY,GAAG,qCAAiB,CAAC,YAAY,EAAE,CAAC;SAChD;QAED,IAAI,KAAiC,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;YACjB,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SACjE;QAED,IAAI,KAAK,IAAI,IAAI,EAAE;YAClB,IAAI,YAAY,IAAI,IAAI,EAAE;gBACzB,YAAY,GAAG,qCAAiB,CAAC,YAAY,EAAE,CAAC;aAChD;YACD,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ;oBACzC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBAC9C,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;aAC1D;YAED,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,GAAW,KAAK,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,KAAK,GAAW,KAAK,CAAC,KAAK,CAAC;QAChC,IAAI;YACH,IAAI,GAAG,GAAW,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;aACtG;YACD,OAAO,GAAG,CAAC;SACX;gBACO;YACP,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACjB;IACF,CAAC;IAES,aAAa,CACb,GAAQ,EACR,KAAkB,EAClB,YAA+B,EACxC,UAAmB;QAEnB,IAAI,CAAC,UAAU,EAAE;YAChB,IAAI,GAAG,CAAC,eAAe,EAAE;gBACxB,8DAA8D;gBAC9D,sDAAsD;gBACtD,IAAI,KAAK,GAAyB,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC9F,IAAI,KAAK,IAAI,IAAI,EAAE;oBAClB,OAAO,SAAS,CAAC;iBACjB;gBAED,OAAO,IAAI,+BAAc,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;aACpE;iBACI;gBACJ,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,EAAE;oBACnB,OAAO,SAAS,CAAC;iBACjB;gBAED,OAAO,IAAI,+BAAc,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;aACrE;SACD;QAED,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACpC,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,gBAAgB,GAAkC,YAAY,CAAC;QACnE,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC;QAC7B,IAAI,EAAwB,CAAC;QAC7B,IAAI,GAAG,CAAC,eAAe,EAAE;YACxB,EAAE,GAAG,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAChE;aACI;YACJ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;SAChB;QAED,OAAO,gBAAgB,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;YACvE,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;YACxD,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChE,IAAI,gBAAgB,CAAC,OAAO,EAAE;gBAC7B,MAAM,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;aAC7C;iBACI;gBACJ,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC;aAC3C;SACD;QAED,IAAI,EAAE,IAAI,IAAI,EAAE;YACf,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,IAAI,+BAAc,CAAC,YAAY,EAAE,EAAE,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IAC3E,CAAC;IAES,OAAO,CACP,GAAQ,EACR,KAAkB,EAAE,UAAkB,EACtC,KAAqB;QAC9B,IAAI,YAAY,GAAsB,KAAK,CAAC,YAAY,CAAC;QACzD,IAAI,kBAAkB,CAAC,SAAS,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ;gBACzC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBAC9C,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,kBAAkB,CAAC,SAAS,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,GAAa,KAAK,CAAC,EAAE,CAAC;QAE3B,IAAI,CAAC,GAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,qBAAqB,GAAkC,KAAK,CAAC,qBAAqB,CAAC;QAEvF,OAAO,IAAI,EAAE;YACZ,IAAI,kBAAkB,CAAC,SAAS,EAAE;gBACjC,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;aACtF;YACD,IAAI,KAAK,CAAC,UAAU,EAAE;gBACrB,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;oBAC5B,IAAI,IAA0B,CAAC;oBAC/B,IAAI,qBAAqB,IAAI,IAAI,EAAE;wBAClC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;wBAClE,IAAI,GAAG,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,CAAC;qBACtE;oBAED,IAAI,IAAI,IAAI,IAAI,EAAE;wBACjB,mBAAmB;wBACnB,IAAI,YAAY,GAAmB,IAAI,+BAAc,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;wBACtH,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;qBAC1D;oBAED,MAAM,CAAC,qBAAqB,IAAI,IAAI,CAAC,CAAC;oBACtC,qBAAqB,GAAG,qBAAqB,CAAC,MAAM,CAAC;oBACrD,CAAC,GAAG,IAAI,CAAC;iBACT;aACD;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE;gBAC5C,IAAI,CAAC,CAAC,UAAU,IAAI,IAAI,EAAE;oBACzB,IAAI,kBAAkB,CAAC,SAAS,EAAE;wBACjC,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;qBAC3B;iBACD;qBACI;oBACJ,IAAI,kBAAkB,CAAC,SAAS,EAAE;wBACjC,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;qBAC9E;iBACD;gBAED,kEAAkE;gBAClE,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8BAA8B;gBAC9B,MAAM;aACN;YAED,qDAAqD;YACrD,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;YAEjD,iEAAiE;YACjE,IAAI,MAAM,GAAyB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,IAAI,kBAAkB,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC3C,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;iBACxE;gBACD,IAAI,GAAW,CAAC;gBAChB,IAAI,kBAAkB,CAAC,SAAS,EAAE;oBACjC,IAAI,QAAQ,GAAa,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBACjF,OAAO,CAAC,GAAG,CAAC,gBAAgB;wBAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC;wBAC1C,gBAAgB,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;iBACnC;gBAED,IAAI,YAAY,GAAmB,IAAI,+BAAc,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;gBAChH,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;gBACzD,IAAI,kBAAkB,CAAC,SAAS,EAAE;oBACjC,OAAO,CAAC,GAAG,CAAC,4BAA4B,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC7H,YAAY;iBACZ;gBACD,0BAA0B;gBAC1B,IAAI,kBAAkB,CAAC,SAAS,EAAE;oBACjC,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ;wBACzC,YAAY,GAAG,GAAG,CAAC,CAAC;iBACrB;gBACD,OAAO,GAAG,CAAC,CAAC,gEAAgE;aAC5E;iBACI,IAAI,MAAM,KAAK,2BAAY,CAAC,KAAK,EAAE;gBACvC,IAAI,UAAU,GAAmB,IAAI,+BAAc,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;gBAC9G,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;aAC7D;YACD,CAAC,GAAG,MAAM,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;gBACpE,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAChB;SACD;QACH,8BAA8B;QAC9B,iEAAiE;QACjE,eAAe;QACf,KAAK;QAEH,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;YACxD,IAAI,GAAG,CAAC,aAAa,YAAY,6BAAa,EAAE;gBAC/C,IAAI,CAAC,IAAI,CAAC,qBAAqB;oBAC9B,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,IAAI,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;oBAC9D,CAAC,IAAI,CAAC,iCAAiC,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,CAAC,EAAE;oBACxE,sCAAsC;oBACtC,yDAAyD;oBACzD,kIAAkI;oBAClI,GAAG;iBACH;qBACI;oBACJ,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;oBAE1B,uEAAuE;oBACvE,qEAAqE;oBACrE,qBAAqB;oBACrB,IAAI,eAAmC,CAAC;oBACxC,IAAI,UAAU,GAA0C,CAAC,CAAC,UAAU,CAAC;oBACrE,IAAI,UAAU,IAAI,IAAI,EAAE;wBACvB,IAAI,aAAa,GAAW,KAAK,CAAC,KAAK,CAAC;wBACxC,IAAI,aAAa,KAAK,UAAU,EAAE;4BACjC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;yBACvB;wBAED,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;wBAC3E,IAAI,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;4BACxC,OAAO,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;yBACrC;wBAED,IAAI,aAAa,KAAK,UAAU,EAAE;4BACjC,sDAAsD;4BACtD,oDAAoD;4BACpD,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;yBAC1B;qBACD;oBAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;wBAC3B,IAAI,aAAa,GAAmB,IAAI,+BAAc,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;wBACjH,IAAI,CAAC,2BAA2B,CAAC,GAAG,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;qBAC/F;oBAED,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACvB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;iBACrE;aACD;SACD;QAED,0DAA0D;QAC1D,sDAAsD;QACtD,IAAI,UAAU,GAA0C,CAAC,CAAC,UAAU,CAAC;QACrE,IAAI,UAAU,IAAI,IAAI,EAAE;YACvB,IAAI,SAAS,GAAW,KAAK,CAAC,KAAK,CAAC;YACpC,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC7B,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACvB;YAED,IAAI,IAAI,GAAW,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,KAAK,+BAAc,CAAC,wBAAwB,CAAC,CAAC;YACjK,QAAQ,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC5B,KAAK,CAAC;oBACL,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAEpE,KAAK,CAAC;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAE3B;oBACC,uEAAuE;oBACvE,iCAAiC;oBACjC,IAAI,UAAU,KAAK,SAAS,EAAE;wBAC7B,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBACtB;oBAED,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;oBAChG,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAC1B;SACD;QAED,IAAI,kBAAkB,CAAC,SAAS,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ;gBACzC,YAAY,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;SAC9B;QACD,OAAO,CAAC,CAAC,UAAU,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACO,aAAa,CAAC,KAAe,EAAE,UAAmB;QAC3D,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YACzB,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,EAAE;YAC1C,cAAc;YACd,OAAO,IAAI,CAAC;SACZ;QAED,0CAA0C;QAC1C,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,KAAK,+BAAc,CAAC,wBAAwB,EAAE;YAClF,OAAO,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACO,OAAO,CACP,GAAQ,EACR,KAAkB,EAAE,UAAkB,EACtC,YAA4B;QACrC,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,mBAAmB,GAAG,GAAG,CAAC,QAAQ,GAAG,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;SACjG;QAED,IAAI,YAAY,GAAsB,YAAY,CAAC,YAAY,CAAC;QAChE,IAAI,UAAU,GAAY,YAAY,CAAC,UAAU,CAAC;QAElD,IAAI,CAAC,GAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,QAAQ,GAAmB,YAAY,CAAC;QAE5C,IAAI,YAAY,GAA2B,IAAI,+CAAsB,EAAE,CAAC;QACxE,OAAO,IAAI,EAAE,EAAE,kBAAkB;YAChC,IAAI,SAAS,GAA+B,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YACjG,IAAI,SAAS,IAAI,IAAI,EAAE;gBACtB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,2BAAY,CAAC,KAAK,CAAC,CAAC;gBAC9D,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3D;YAED,IAAI,CAAC,GAAa,SAAS,CAAC,EAAE,CAAC;YAE/B,gCAAgC;YAChC,MAAM,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,UAAU,KAAK,SAAG,CAAC,kBAAkB,CAAC,CAAC;YACnE,6BAA6B;YAC7B,MAAM,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC;YAE1D,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE;gBACtC,IAAI,eAAe,GAAuB,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;gBACpE,IAAI,YAAY,GAAW,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAG,CAAC,kBAAkB,CAAC;gBAC3F,IAAI,YAAY,KAAK,SAAG,CAAC,kBAAkB,EAAE;oBAC5C,IAAI,IAAI,CAAC,YAAY;2BACjB,KAAK,CAAC,KAAK,KAAK,UAAU;2BAC1B,CAAC,GAAG,CAAC,eAAe;2BACpB,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,qBAAqB;2BAC1D,GAAG,CAAC,QAAQ,IAAI,CAAC;2BACjB,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE;wBAClC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,EAAE;4BACnC,IAAI,GAAG,GAAW,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;4BACnD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;yBACzC;qBACD;oBAED,IAAI,UAAU,IAAI,IAAI,CAAC,wBAAwB,EAAE;wBAChD,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;qBACrF;iBACD;gBAED,YAAY,GAAG,CAAC,CAAC,UAAU,CAAC;gBAChC,qEAAqE;gBACrE,sCAAsC;gBAClC,IAAI,kBAAkB,GAAY,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC;gBACxF,IAAI,kBAAkB,EAAE;oBACvB,4DAA4D;oBAC5D,qDAAqD;oBACrD,kBAAkB,GAAG,CAAC,UAAU;2BAC5B,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;2BAC9D,CAAC,CAAC,IAAI,CAAC,iCAAiC,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC;iBAC5E;gBAED,IAAI,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE;oBACjC,IAAI,eAAe,GAA0C,CAAC,CAAC,UAAU,CAAC;oBAC1E,IAAI,eAAe,IAAI,IAAI,EAAE;wBAC5B,IAAI,aAAa,GAAW,KAAK,CAAC,KAAK,CAAC;wBACxC,IAAI,aAAa,KAAK,UAAU,EAAE;4BACjC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;yBACvB;wBAED,2FAA2F;wBAC3F,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,YAAY,EAAE,kBAAkB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACxH,QAAQ,eAAe,CAAC,WAAW,EAAE,EAAE;4BACvC,KAAK,CAAC;gCACL,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;4BAEpE,KAAK,CAAC;gCACL,OAAO,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;4BAEtC;gCACC,MAAM;yBACN;wBAED,IAAI,aAAa,KAAK,UAAU,EAAE;4BACjC,sDAAsD;4BACtD,oDAAoD;4BACpD,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;yBAC1B;qBACD;iBACD;gBAED,IAAI,CAAC,kBAAkB,EAAE;oBACxB,IAAI,eAAe,IAAI,IAAI,EAAE;wBAC5B,IAAI,IAAI,CAAC,iBAAiB,IAAI,eAAe,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;4BAChE,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;yBAC7G;wBAED,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBAC7C;oBAED,OAAO,YAAY,CAAC;iBACpB;qBACI;oBACJ,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC;oBACpB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;oBAErC,IAAI,kBAAkB,CAAC,KAAK,EAAE;wBAC7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,YAAY,CAAC,CAAC;qBACvD;oBACD,IAAI,gBAAgB,GAAmB,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;oBACvF,IAAI,IAAI,CAAC,iBAAiB,EAAE;wBAC3B,IAAI,CAAC,2BAA2B,CAAC,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;qBAC3F;oBAED,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACvB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;iBAC9D;aACD;YAED,QAAQ,GAAG,SAAS,CAAC;YAErB,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;gBACxB,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAChB;SACD;IACF,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkDG;IACO,iBAAiB,CAAU,KAAkB,EAAE,UAAkB,EAAW,QAAwB;QAC7G,IAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,EAAE;YACxB,IAAI,IAAI,GAAW,IAAI,eAAM,EAAE,CAAC;YAChC,IAAI,MAAM,GAAW,CAAC,CAAC;YACvB,KAAK,IAAI,MAAM,IAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE;gBACvC,IAAI,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,KAAK,YAAY,6BAAa,EAAE;oBAC5E,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACrB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;iBACtC;aACD;YAED,QAAQ,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC5B,KAAK,CAAC;oBACL,MAAM;gBAEP,KAAK,CAAC;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAE3B;oBACC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB,EAAE;wBAC5C,2DAA2D;wBAC3D,0CAA0C;wBAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBAC1B;oBAED;;;uBAGG;oBACH,IAAI,eAAe,GAAiB,IAAI,2BAAY,EAAE,CAAC;oBACvD,KAAK,IAAI,MAAM,IAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE;wBACvC,IAAI,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,KAAK,YAAY,6BAAa,EAAE;4BAC5E,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;yBAC5B;qBACD;oBAED;;;;;;;uBAOG;oBACH,IAAI,SAAS,GAAkC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;oBACxG,IAAI,SAAS,IAAI,IAAI,EAAE;wBACtB,IAAI,UAAU,GAA0C,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;wBACtG,IAAI,UAAU,IAAI,IAAI,EAAE;4BACvB,IAAI,SAAS,GAAW,KAAK,CAAC,KAAK,CAAC;4BACpC,IAAI;gCACH,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gCACvB,IAAI,YAAY,GAAW,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gCAC9F,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;oCAC1B,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iCAClC;6BACD;oCACO;gCACP,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;6BACtB;yBACD;qBACD;oBAED,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAC1B;SACD;QAED,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAES,eAAe,CAAC,GAAQ,EAAE,QAAwB,EAAE,CAAS,EAAE,YAAoC;QAC5G,IAAI,UAAU,GAAY,QAAQ,CAAC,UAAU,CAAC;QAC9C,IAAI,sBAAsB,GAAkC,QAAQ,CAAC,qBAAqB,CAAC;QAE3F,IAAI,CAAC,GAAa,QAAQ,CAAC,EAAE,CAAC;QAC9B,IAAI,UAAU,EAAE;YACf,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;gBAC5B,IAAI,IAA0B,CAAC;gBAC/B,IAAI,sBAAsB,IAAI,IAAI,EAAE;oBACnC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;oBACpE,IAAI,GAAG,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC;iBACvE;gBAED,IAAI,IAAI,IAAI,IAAI,EAAE;oBACjB,MAAM;iBACN;gBAED,MAAM,CAAC,sBAAsB,IAAI,IAAI,CAAC,CAAC;gBACvC,sBAAsB,GAAG,sBAAsB,CAAC,MAAM,CAAC;gBACvD,CAAC,GAAG,IAAI,CAAC;aACT;SACD;QAED,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE;YACtC,OAAO,IAAI,+BAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;SACxF;QAED,IAAI,EAAE,GAAa,CAAC,CAAC;QAErB,IAAI,MAAM,GAAyB,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACtE,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,IAAI,MAAM,GAA8C,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,EAAE,EAAE,sBAAsB,EAAE,CAAC,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;YAC9I,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACnC;QAED,IAAI,MAAM,KAAK,2BAAY,CAAC,KAAK,EAAE;YAClC,OAAO,SAAS,CAAC;SACjB;QAED,MAAM,CAAC,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC5D,OAAO,IAAI,+BAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;;;;;;OAUG;IACO,sBAAsB,CAAU,CAAW,EAAE,CAAS;QAC/D,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IAEO,kBAAkB,CAAU,GAAQ,EAAW,CAAW,EAAE,sBAAqD,EAAE,CAAS,EAAE,UAAmB,EAAE,YAAoC;QAChM,IAAI,cAAc,GAAgB,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtD,IAAI,eAAwC,CAAC;QAC7C,IAAI,KAAK,GAAiB,IAAI,2BAAY,EAAE,CAAC;QAC7C,IAAI,cAAuB,CAAC;QAC5B,GAAG;YACF,IAAI,cAAc,GAAY,CAAC,UAAU,IAAI,sBAAsB,IAAI,IAAI,CAAC;YAC5E,IAAI,CAAC,cAAc,EAAE;gBACpB,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;aAClC;YAED,IAAI,iBAAiB,GAAiB,IAAI,2BAAY,EAAE,CAAC;YAEzD;;;;;;;;;eASG;YACH,IAAI,iBAA0C,CAAC;YAE/C,KAAK,IAAI,CAAC,IAAI,cAAc,EAAE;gBAC7B,IAAI,kBAAkB,CAAC,KAAK,EAAE;oBAC7B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;iBACvE;gBAED,IAAI,CAAC,CAAC,KAAK,YAAY,6BAAa,EAAE;oBACrC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC1B,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC,uBAAuB,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;wBACpE,IAAI,iBAAiB,IAAI,IAAI,EAAE;4BAC9B,iBAAiB,GAAG,EAAE,CAAC;yBACvB;wBAED,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBAC1B;oBAED,SAAS;iBACT;gBAED,IAAI,CAAC,GAAW,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC;gBACrD,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAgB,gCAAgC;oBAC9E,IAAI,KAAK,GAAe,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;oBAC3D,IAAI,MAAM,GAAyB,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACxE,IAAI,MAAM,IAAI,IAAI,EAAE;wBACnB,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC;qBAChE;iBACD;aACD;YAED;;;;;;;;eAQG;YACH,IAAI,IAAI,CAAC,uBAAuB,IAAI,iBAAiB,IAAI,IAAI,IAAI,CAAC,KAAK,aAAK,CAAC,GAAG,IAAI,iBAAiB,CAAC,SAAS,KAAK,SAAG,CAAC,kBAAkB,EAAE;gBAC3I,iBAAiB,CAAC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC;gBACpE,KAAK,GAAG,iBAAiB,CAAC;gBAC1B,MAAM;aACN;YAED;;eAEG;YACH,IAAI,iBAAiB,GAAY,KAAK,CAAC;YACvC,IAAI,iBAAiB,GAAY,CAAC,KAAK,aAAK,CAAC,GAAG,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;YAC3G,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC;YAE5C,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;gBACxB;;;;;;;;;;;mBAWG;gBACH,KAAK,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;aACrE;YAED;;;;;;;eAOG;YACH,IAAI,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,+BAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClG,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrC,KAAK,IAAI,CAAC,IAAI,iBAAiB,EAAE;oBAChC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;iBAC3B;aACD;YAED,IAAI,UAAU,IAAI,cAAc,EAAE;gBACjC,KAAK,CAAC,KAAK,EAAE,CAAC;gBAEd,uEAAuE;gBACvE,sBAAsB,GAAG,sBAA2C,CAAC;gBAErE,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;gBACpE,IAAI,kBAAkB,GAAW,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;gBAC7E,IAAI,eAAe,IAAI,IAAI,EAAE;oBAC5B,eAAe,GAAG,IAAI,yBAAW,EAAE,CAAC;iBACpC;gBAED,IAAI,sBAAsB,CAAC,OAAO,EAAE;oBACnC,sBAAsB,GAAG,SAAS,CAAC;iBACnC;qBAAM;oBACN,sBAAsB,GAAG,sBAAsB,CAAC,MAAM,CAAC;iBACvD;gBAED,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBACxC,IAAI,kBAAkB,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;oBAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC/C,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;qBACtF;iBACD;aACD;SACD,QAAQ,UAAU,IAAI,cAAc,EAAE;QAEvC,IAAI,KAAK,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,2BAAY,CAAC,KAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,2BAAY,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;SACpD;QAED,IAAI,MAAM,GAAa,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QACxF,OAAO,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;OAYG;IAEO,kCAAkC,CAAU,OAAqB,EAAE,YAAoC;QAChH,IAAI,+BAAc,CAAC,0BAA0B,CAAC,OAAO,CAAC,EAAE;YACvD,OAAO,OAAO,CAAC;SACf;QAED,IAAI,MAAM,GAAiB,IAAI,2BAAY,EAAE,CAAC;QAC9C,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,YAAY,6BAAa,CAAC,EAAE;gBAC7C,SAAS;aACT;YAED,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SACjC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGS,iBAAiB,CAC1B,GAAQ,EACR,aAAgC,EAChC,UAAmB;QACnB,IAAI,EAAE,GACL,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;YACvF,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACxB,GAAG,CAAC,EAAE,CAAC;QAEV,IAAI,EAAE,IAAI,IAAI,EAAE;YACf,IAAI,CAAC,UAAU,EAAE;gBAChB,OAAO,IAAI,+BAAc,CAAC,aAAa,EAAE,EAAE,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;aACxE;YAED,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,IAAI,QAAQ,GAAW,GAAG,CAAC,QAAQ,CAAC;QACpC,WAAW;QACX,IAAI,CAAC,GAAa,GAAG,CAAC,aAAa,CAAC;QAEpC,IAAI,eAAe,GAAW,CAAC,CAAC;QAChC,IAAI,sBAAsB,GAAkC,aAAa,CAAC;QAC1E,IAAI,cAAc,GAAsB,UAAU,CAAC,CAAC,CAAC,qCAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,qCAAiB,CAAC,WAAW,CAAC,CAAC,kDAAkD;QACrK,IAAI,YAAY,GAA2B,IAAI,+CAAsB,EAAE,CAAC;QACxE,IAAI,UAAU,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACpC,OAAO,sBAAsB,IAAI,IAAI,EAAE;oBACtC,IAAI,sBAAsB,CAAC,OAAO,EAAE;wBACnC,eAAe,GAAG,qCAAiB,CAAC,oBAAoB,CAAC;wBACzD,sBAAsB,GAAG,SAAS,CAAC;qBACnC;yBACI;wBACJ,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;wBAC9D,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;wBACnF,sBAAsB,GAAG,sBAAsB,CAAC,MAAM,CAAC;qBACvD;iBACD;aACD;YAED,OAAO,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,kBAAkB,IAAI,sBAAsB,IAAI,IAAI,EAAE;gBAC7E,IAAI,IAA0B,CAAC;gBAC/B,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;gBACpE,IAAI,sBAAsB,CAAC,OAAO,EAAE;oBACnC,IAAI,GAAG,EAAE,CAAC,gBAAgB,CAAC,qCAAiB,CAAC,oBAAoB,CAAC,CAAC;oBACnE,eAAe,GAAG,qCAAiB,CAAC,oBAAoB,CAAC;oBACzD,sBAAsB,GAAG,SAAS,CAAC;iBACnC;qBACI;oBACJ,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;oBAC9D,IAAI,GAAG,EAAE,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;oBAC5C,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;oBACnF,sBAAsB,GAAG,sBAAsB,CAAC,MAAM,CAAC;iBACvD;gBAED,IAAI,IAAI,IAAI,IAAI,EAAE;oBACjB,MAAM;iBACN;gBAED,EAAE,GAAG,IAAI,CAAC;aACV;SACD;QAED,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE;YACzC,OAAO,IAAI,+BAAc,CAAC,aAAa,EAAE,EAAE,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;SACjF;QAED,IAAI,OAAO,GAAiB,IAAI,2BAAY,EAAE,CAAC;QAC/C,OAAO,IAAI,EAAE;YACZ,IAAI,iBAAiB,GAAiB,IAAI,2BAAY,EAAE,CAAC;YACzD,IAAI,CAAC,GAAW,CAAC,CAAC,mBAAmB,CAAC;YACtC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE;gBAC9B,sBAAsB;gBACtB,IAAI,MAAM,GAAa,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;gBAC/C,iBAAiB,CAAC,GAAG,CAAC,qBAAS,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;aACxE;YAED,IAAI,cAAc,GAAY,sBAAsB,IAAI,IAAI,CAAC;YAC7D,IAAI,CAAC,cAAc,EAAE;gBACpB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;aACpC;YAED,IAAI,iBAAiB,GAAY,IAAI,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;YACjG,IAAI,cAAc,GAAY,OAAO,CAAC,oBAAoB,CAAC;YAE3D,IAAI,IAAc,CAAC;YACnB,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAClD,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;gBAClD,MAAM;aACN;iBACI,IAAI,EAAE,IAAI,IAAI,EAAE;gBACpB,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE;oBACzB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;oBACpD,IAAI,UAAU,EAAE;wBACf,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;4BAChB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;yBAClB;6BAAM;4BACN,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC;yBAClB;qBACD;yBAAM;wBACN,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;4BACZ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;yBACd;6BAAM;4BACN,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;yBACd;qBACD;iBACD;qBACI;oBACJ;;;;;uBAKG;oBACH,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;oBAC3E,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;oBACpD,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;iBACvE;aACD;iBACI;gBACJ,IAAI,GAAG,CAAC,eAAe,EAAE;oBACxB,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;iBAC3E;gBAED,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;gBACpD,EAAE,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;aAC3C;YAED,EAAE,GAAG,IAAI,CAAC;YAEV,IAAI,CAAC,UAAU,IAAI,CAAC,cAAc,EAAE;gBACnC,MAAM;aACN;YAED,sDAAsD;YACtD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEnC,uEAAuE;YACvE,sBAAsB,GAAG,sBAA2C,CAAC;YAErE,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;YACpE,IAAI,kBAAkB,GAAW,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;YAE7E,IAAI,sBAAsB,CAAC,OAAO,EAAE;gBACnC,sBAAsB,GAAG,SAAS,CAAC;aACnC;iBAAM;gBACN,sBAAsB,GAAG,sBAAsB,CAAC,MAAM,CAAC;aACvD;YAED,IAAI,kBAAkB,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;gBAClE,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;aACtF;YAED,eAAe,GAAG,kBAAkB,CAAC;SACrC;QAED,OAAO,IAAI,+BAAc,CAAC,aAAa,EAAE,EAAE,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;IAClF,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG;IAEO,qBAAqB,CAAU,OAAqB,EAAE,aAAgC,EAAE,YAAoC;QACrI,IAAI,cAAc,GAAmC,IAAI,GAAG,EAA6B,CAAC;QAC1F,IAAI,SAAS,GAAiB,IAAI,2BAAY,EAAE,CAAC;QACjD,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,qBAAqB;YACrB,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE;gBACrB,SAAS;aACT;YAED,IAAI,cAAc,GAAgC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACrH,IAAI,cAAc,IAAI,IAAI,EAAE;gBAC3B,mCAAmC;gBACnC,SAAS;aACT;YAED,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,cAAc,KAAK,MAAM,CAAC,eAAe,EAAE;gBAC9C,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC,EAAE,YAAY,CAAC,CAAC;aACnF;iBACI;gBACJ,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;aACpC;SACD;QAED,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE;gBACrB,kBAAkB;gBAClB,SAAS;aACT;YAED,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAE;gBACzC;;;mBAGG;gBACH,IAAI,OAAO,GAAkC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC1F,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBACtD,aAAa;oBACb,SAAS;iBACT;aACD;YAED,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SACpC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAES,kBAAkB,CAAU,MAAiB,EAAW,KAAiB,EAAE,KAAa;QACjG,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACnD,OAAO,KAAK,CAAC,MAAM,CAAC;SACpB;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,2CAA2C;IACjC,iBAAiB,CAC1B,CAAW,EACX,OAAqB,EACrB,KAAa;QACb,IAAI,eAAe,GAAuB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACxF,IAAI,CAAC,eAAe,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,mHAAmH,CAAC,CAAC;SACrI;QAED,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;SACtC;QACD,IAAI,SAAS,GAAkC,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1G,gEAAgE;QAChE,IAAI,eAAsD,CAAC;QAC3D,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,0CAA0C;YAC1C,0DAA0D;YAC1D,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAC3E,CAAC,CAAC,UAAU,GAAG,eAAe,CAAC;SAC/B;QACD,OAAO,eAAe,CAAC;IACxB,CAAC;IAES,oBAAoB,CACpB,SAAiB,EACjB,OAAqB,EAC9B,KAAa;QACb,iCAAiC;QAEjC;;;;;;;;;;WAUG;QACH,IAAI,SAAS,GAAmD,IAAI,KAAK,CAAkB,KAAK,GAAG,CAAC,CAAC,CAAC;QACtG,IAAI,CAAC,GAAW,SAAS,CAAC,MAAM,CAAC;QACjC,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,iCAAe,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC;aAC3E;SACD;QAED,IAAI,SAAS,GAAW,CAAC,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG,iCAAe,CAAC,IAAI,CAAC;aACpC;iBACI,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,iCAAe,CAAC,IAAI,EAAE;gBAC/C,SAAS,EAAE,CAAC;aACZ;SACD;QAED,0EAA0E;QAC1E,IAAI,MAAM,GAAkC,SAA8B,CAAC;QAE3E,wCAAwC;QACxC,IAAI,SAAS,KAAK,CAAC,EAAE;YACpB,MAAM,GAAG,SAAS,CAAC;SACnB;QACD,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,8BAA8B,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,eAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;SAC/F;QACD,OAAO,MAAM,CAAC;IACf,CAAC;IAES,uBAAuB,CAAC,SAA6B,EAAE,SAA4B;QAC5F,IAAI,KAAK,GAA8B,EAAE,CAAC;QAC1C,IAAI,iBAAiB,GAAY,KAAK,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,IAAI,GAAoB,SAAS,CAAC,CAAC,CAAC,CAAC;YAEzC,oDAAoD;YACpD,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;YAErB,yDAAyD;YACzD,8DAA8D;YAC9D,2EAA2E;YAC3E,uDAAuD;YACvD,oDAAoD;YACpD,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,iCAAe,CAAC,IAAI,EAAE;gBAC3E,KAAK,CAAC,IAAI,CAAC,IAAI,mBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aACjD;iBACI,IAAI,IAAI,KAAK,iCAAe,CAAC,IAAI,EAAE;gBACvC,iBAAiB,GAAG,IAAI,CAAC;gBACzB,KAAK,CAAC,IAAI,CAAC,IAAI,mBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aACjD;SACD;QAED,IAAI,CAAC,iBAAiB,EAAE;YACvB,OAAO,SAAS,CAAC;SACjB;QAEH,8DAA8D;QAC5D,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;OAGG;IACO,mBAAmB,CACnB,eAA0C,EACnD,YAA+B,EAC/B,QAAiB;QACjB,IAAI,WAAW,GAAW,IAAI,eAAM,EAAE,CAAC;QACvC,KAAK,IAAI,IAAI,IAAI,eAAe,EAAE;YACjC,IAAI,IAAI,CAAC,IAAI,KAAK,iCAAe,CAAC,IAAI,EAAE;gBACvC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,QAAQ,EAAE;oBACd,MAAM;iBACN;gBAED,SAAS;aACT;YAED,IAAI,eAAe,GAAY,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/F,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,SAAS,EAAE;gBAC7D,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,GAAG,GAAG,GAAG,eAAe,CAAC,CAAC;aACzD;YAED,IAAI,eAAe,EAAE;gBACpB,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,SAAS,EAAE;oBAC7D,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;iBACnC;gBACD,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,QAAQ,EAAE;oBACd,MAAM;iBACN;aACD;SACD;QAED,OAAO,WAAW,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACO,uBAAuB,CAAU,IAAqB,EAAE,eAAkC,EAAE,GAAW;QAChH,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACjD,CAAC;IAED;;;;;YAKK;IAEK,OAAO,CAChB,aAA2B,EAClB,OAAqB,EAC9B,iBAA0B,EAC1B,cAAuB,EACb,YAAoC,EAC9C,iBAA0B;QAC1B,IAAI,YAAY,IAAI,IAAI,EAAE;YACzB,YAAY,GAAG,+CAAsB,CAAC,QAAQ,CAAC;SAC/C;QAED,IAAI,cAAc,GAAiB,aAAa,CAAC;QACjD,IAAI,WAAW,GAA8B,IAAI,+BAAc,CAAY,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,OAAO,cAAc,CAAC,IAAI,GAAG,CAAC,EAAE;YAC/B,IAAI,YAAY,GAAiB,IAAI,2BAAY,EAAE,CAAC;YACpD,KAAK,IAAI,MAAM,IAAI,cAAc,EAAE;gBAClC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC;aACpI;YAED,cAAc,GAAG,YAAY,CAAC;SAC9B;IACF,CAAC;IAES,WAAW,CACX,MAAiB,EACjB,OAAqB,EACpB,YAA0B,EAC3B,WAAsC,EAC/C,iBAA0B,EAC1B,eAAwB,EACf,YAAoC,EAC7C,KAAa,EACb,iBAA0B;QAC1B,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;SACpE;QAED,IAAI,MAAM,CAAC,KAAK,YAAY,6BAAa,EAAE;YAC1C,mDAAmD;YACnD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;gBAC5B,IAAI,QAAQ,GAAY,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAChD,IAAI,YAAY,GAAW,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;oBACtC,IAAI,UAAU,GAAsB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;oBACtF,IAAI,WAAW,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,IAAI,CAAC,GAAc,qBAAS,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;oBACjG,sDAAsD;oBACtD,sDAAsD;oBACtD,qDAAqD;oBACrD,CAAC,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;oBAC/C,CAAC,CAAC,4BAA4B,GAAG,MAAM,CAAC,4BAA4B,CAAC;oBACrE,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC,CAAC;oBAClC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,EAAE,YAAY,EAAE,KAAK,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;iBACxI;gBAED,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,EAAE;oBAClC,OAAO;iBACP;gBAED,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,qCAAiB,CAAC,WAAW,CAAC,CAAC;aAC9E;iBACI,IAAI,CAAC,eAAe,EAAE;gBAC1B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBAClC,OAAO;aACP;iBACI;gBACJ,uEAAuE;gBACvE,IAAI,kBAAkB,CAAC,KAAK,EAAE;oBAC7B,OAAO,CAAC,GAAG,CAAC,mBAAmB;wBAC9B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;iBAC3C;gBAED,IAAI,MAAM,CAAC,OAAO,KAAK,qCAAiB,CAAC,UAAU,EAAE;oBACpD,yDAAyD;oBACzD,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,qCAAiB,CAAC,WAAW,CAAC,CAAC;iBAC9E;qBACI,IAAI,CAAC,MAAM,CAAC,uBAAuB,IAAI,qCAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBAC3F,+DAA+D;oBAC/D,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;iBAClC;aACD;SACD;QAED,IAAI,CAAC,GAAa,MAAM,CAAC,KAAK,CAAC;QAC/B,eAAe;QACf,IAAI,CAAC,CAAC,CAAC,yBAAyB,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAClC,mEAAmE;YACnE,wDAAwD;YACxD,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,CAAC;aACvC;SACD;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;YACxD,+DAA+D;YAC/D,oEAAoE;YACpE,yBAAyB;YACzB,IAAI,CAAC,KAAK,CAAC;mBACP,CAAC,CAAC,SAAS,KAAK,2BAAY,CAAC,eAAe;mBAC3C,CAAwB,CAAC,sBAAsB;mBAChD,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAE7B,IAAI,kBAAkB,GAAG,CAAuB,CAAC;gBAEjD,4DAA4D;gBAC5D,8DAA8D;gBAC9D,6DAA6D;gBAC7D,qCAAqC;gBACrC,IAAI,QAAQ,GAAY,IAAI,CAAC;gBAC7B,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;oBACrD,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;wBACvF,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;qBACN;iBACD;gBAED,IAAI,QAAQ,EAAE;oBACb,SAAS;iBACT;aACD;YAED,IAAI,CAAC,GAAe,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,kBAAkB,GACrB,CAAC,CAAC,CAAC,YAAY,mCAAgB,CAAC,IAAI,iBAAiB,CAAC;YACvD,IAAI,CAAC,GAA0B,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,kBAAkB,EAAE,KAAK,KAAK,CAAC,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;YAClI,IAAI,CAAC,IAAI,IAAI,EAAE;gBACd,IAAI,CAAC,YAAY,+BAAc,EAAE;oBAChC,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;wBAC/C,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;wBAClC,SAAS;qBACT;iBACD;gBAED,IAAI,QAAQ,GAAW,KAAK,CAAC;gBAC7B,IAAI,MAAM,CAAC,KAAK,YAAY,6BAAa,EAAE;oBAC1C,oFAAoF;oBACpF,wEAAwE;oBACxE,kDAAkD;oBAClD,0DAA0D;oBAC1D,wBAAwB;oBAExB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE;wBACjD,IAAI,yBAAyB,GAAY,CAAuB,CAAC,yBAAyB,CAAC;wBAC3F,IAAI,yBAAyB,KAAK,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE;4BACnE,CAAC,CAAC,4BAA4B,GAAG,IAAI,CAAC;yBACtC;qBACD;oBAED,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAE9C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;wBACxB,qDAAqD;wBACrD,SAAS;qBACT;oBAED,MAAM,CAAC,QAAQ,GAAG,iBAAiB,CAAC,CAAC;oBACrC,QAAQ,EAAE,CAAC;oBACX,IAAI,kBAAkB,CAAC,KAAK,EAAE;wBAC7B,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC;qBACzC;iBACD;qBACI,IAAI,CAAC,YAAY,+BAAc,EAAE;oBACrC,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,qCAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE;wBAC1I,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC;wBACrC,IAAI,QAAQ,KAAK,CAAC,EAAE;4BACnB,mDAAmD;4BACnD,gDAAgD;4BAChD,QAAQ,EAAE,CAAC;4BACX,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,qCAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gCACpF,2GAA2G;gCAC3G,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC;6BAC9C;yBACD;qBACD;yBACI;wBACJ,4FAA4F;wBAC5F,IAAI,QAAQ,IAAI,CAAC,EAAE;4BAClB,QAAQ,EAAE,CAAC;yBACX;qBACD;iBACD;qBACI;oBACJ,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;wBACxC,6CAA6C;wBAC7C,SAAS;qBACT;iBACD;gBAED,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,kBAAkB,EAAE,eAAe,EAAE,YAAY,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;aACxI;SACD;IACF,CAAC;IAGM,WAAW,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,OAAO,QAAQ,GAAG,KAAK,GAAG,GAAG,CAAC;IAC/B,CAAC;IAES,gBAAgB,CAAU,MAAiB,EAAW,CAAa,EAAE,iBAA0B,EAAE,SAAkB,EAAE,YAAoC,EAAE,iBAA0B;QAC9L,QAAQ,CAAC,CAAC,iBAAiB,EAAE;YAC7B;gBACC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAmB,EAAE,YAAY,CAAC,CAAC;YAEvE;gBACC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAkC,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;YAE5G;gBACC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAwB,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;YAE5F;gBACC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAqB,CAAC,CAAC;YAE7D;gBACC,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAE1C,kBAAyB;YACzB,mBAA0B;YAC1B;gBACC,mEAAmE;gBACnE,0BAA0B;gBAC1B,IAAI,iBAAiB,EAAE;oBACtB,IAAI,CAAC,CAAC,OAAO,CAAC,aAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;wBAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;qBACzC;iBACD;gBAED,OAAO,SAAS,CAAC;YAElB;gBACC,OAAO,SAAS,CAAC;SACjB;IACF,CAAC;IAGS,gBAAgB,CAAU,MAAiB,EAAW,CAAmB;QAClF,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;SAChE;QACD,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAGS,oBAAoB,CACpB,MAAiB,EACjB,EAAiC,EAC1C,iBAA0B,EAC1B,SAAkB;QAClB,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,iBAAiB,GAAG,IAAI;gBAChE,EAAE,CAAC,UAAU,GAAG,MAAM;gBACtB,sBAAsB,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;gBACzB,OAAO,CAAC,GAAG,CAAC,8BAA8B;oBACzC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC;aACxC;SACD;QAED,IAAI,CAAY,CAAC;QACjB,IAAI,iBAAiB,IAAI,SAAS,EAAE;YACnC,IAAI,SAAS,GAAoB,iCAAe,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;YAC3F,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SAClD;aACI;YACJ,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,8BAA8B,GAAG,CAAC,CAAC,CAAC;SAChD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAGS,cAAc,CACd,MAAiB,EACjB,EAAuB,EAChC,iBAA0B,EAC1B,SAAkB;QAClB,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,iBAAiB,GAAG,IAAI;gBAChE,EAAE,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC,SAAS;gBACjC,kBAAkB,GAAG,EAAE,CAAC,cAAc,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;gBACzB,OAAO,CAAC,GAAG,CAAC,8BAA8B;oBACzC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC;aACxC;SACD;QAED,IAAI,CAAY,CAAC;QACjB,IAAI,iBAAiB;YACpB,CAAC,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC,cAAc,IAAI,SAAS,CAAC,CAAC,EAAE;YAC1D,IAAI,SAAS,GAAoB,iCAAe,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;YAC3F,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SAClD;aACI;YACJ,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,8BAA8B,GAAG,CAAC,CAAC,CAAC;SAChD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAGS,cAAc,CAAU,MAAiB,EAAW,CAAiB,EAAY,YAAoC;QAC9H,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC9D,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;SAC5B;QAED,IAAI,WAAW,GAAa,CAAC,CAAC,WAAW,CAAC;QAC1C,IAAI,UAA6B,CAAC;QAElC,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,qCAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE;YAC1I,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;SAC5B;aACI,IAAI,YAAY,IAAI,IAAI,EAAE;YAC9B,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;SAC5E;aACI;YACJ,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAC9D;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAiBO,YAAY,CAAU,SAAuB,EAAE,YAAoC;QAC1F,IAAI,SAAS,CAAC,SAAS,KAAK,SAAG,CAAC,kBAAkB,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,EAAE;YAC1E,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,OAAO,GAAgB,SAAS,CAAC,OAAO,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAE3D,IAAI,KAAK,GAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC;QACrD,IAAI,IAAI,GAAW,IAAI,eAAM,EAAE,CAAC;QAChC,IAAI,MAAM,GAAW,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjB;;;;;;;WAOG;QAEH,sEAAsE;QACtE,kEAAkE;QAClE,YAAY;QACZ,IAAI,YAAY,GAAW,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAC/D,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,IAAI,WAAW,GAAW,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAC1D,IAAI,WAAW,KAAK,YAAY,EAAE;gBACjC,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,EAAE;oBAC1B,OAAO,SAAS,CAAC;iBACjB;gBAED,YAAY,GAAG,WAAW,CAAC;aAC3B;SACD;QAED,IAAI,eAAuB,CAAC;QAC5B,IAAI,KAAK,EAAE;YACV,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAEnD,sDAAsD;YACtD,eAAe,GAAG,IAAI,eAAM,EAAE,CAAC;YAC/B,IAAI,MAAM,GAAW,MAAM,CAAC;YAC5B,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;gBAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,YAAY,EAAE;oBACrD,MAAM;iBACN;gBAED,IAAI,GAAG,GAAW,MAAM,CAAC,GAAG,CAAC;gBAC7B,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,GAAG,GAAG,CAAC;aACb;YAED,kBAAkB;YAClB,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACnD,IAAI,UAAU,GAAW,MAAM,CAAC;YAChC,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;gBAC3B,IAAI,WAAW,GAAW,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBAC1D,IAAI,GAAG,GAAW,MAAM,CAAC,GAAG,CAAC;gBAC7B,IAAI,WAAW,KAAK,YAAY,EAAE;oBACjC,IAAI,UAAU,KAAK,MAAM,EAAE;wBAC1B,KAAK,GAAG,KAAK,CAAC;wBACd,MAAM;qBACN;oBAED,YAAY,GAAG,WAAW,CAAC;oBAC3B,UAAU,GAAG,MAAM,CAAC;iBACpB;qBACI,IAAI,GAAG,KAAK,UAAU,EAAE;oBAC5B,IAAI,GAAG,KAAK,eAAe,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE;wBACvD,KAAK,GAAG,KAAK,CAAC;wBACd,MAAM;qBACN;oBAED,UAAU,GAAG,GAAG,CAAC;iBACjB;aACD;SACD;QAED,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC;QACnD,IAAI,sBAAsB,GAAW,CAAC,CAAC;QACvC,IAAI,2BAA2B,GAAW,CAAC,CAAC;QAC5C,IAAI,kBAAkB,GAAsB,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,GAAc,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,EAAE;gBAC1B,MAAM;aACN;YAED,IAAI,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,YAAY,EAAE;gBACrD,MAAM;aACN;YAED,2BAA2B,GAAG,CAAC,CAAC;YAChC,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SAC/E;QAED,KAAK,IAAI,CAAC,GAAG,2BAA2B,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtE,IAAI,MAAM,GAAc,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,KAAK,GAAa,MAAM,CAAC,KAAK,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,KAAK,CAAC,kBAAkB,KAAK,YAAY,EAAE;gBAC9C,YAAY,GAAG,KAAK,CAAC,kBAAkB,CAAC;gBACxC,sBAAsB,GAAG,CAAC,CAAC;gBAC3B,2BAA2B,GAAG,CAAC,CAAC;gBAChC,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC;gBACpC,KAAK,IAAI,CAAC,GAAG,sBAAsB,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACjE,IAAI,OAAO,GAAc,OAAO,CAAC,CAAC,CAAC,CAAC;oBACpC,IAAI,OAAO,CAAC,GAAG,KAAK,MAAM,EAAE;wBAC3B,MAAM;qBACN;oBAED,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,KAAK,YAAY,EAAE;wBACtD,MAAM;qBACN;oBAED,2BAA2B,GAAG,CAAC,CAAC;oBAChC,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC5E;gBAED,CAAC,GAAG,2BAA2B,CAAC;gBAChC,SAAS;aACT;YAED,IAAI,mBAAmB,GAAsB,MAAM,CAAC,OAAO,CAAC;YAC5D,IAAI,UAAU,GAAW,MAAM,CAAC,GAAG,CAAC;YACpC,IAAI,+BAA+B,GAAW,CAAC,CAAC;YAChD,KAAK,IAAI,CAAC,GAAG,+BAA+B,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1E,IAAI,OAAO,GAAc,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,OAAO,CAAC,GAAG,KAAK,UAAU,EAAE;oBAC/B,MAAM;iBACN;gBAED,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,KAAK,YAAY,EAAE;oBACtD,MAAM;iBACN;gBAED,+BAA+B,GAAG,CAAC,CAAC;gBACpC,mBAAmB,GAAG,YAAY,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;aAC9E;YAED,CAAC,GAAG,+BAA+B,CAAC;YAEpC,IAAI,KAAK,GAAsB,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;YAC1F,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtC,OAAO,SAAS,CAAC;aACjB;YAED,4BAA4B;YAC5B,KAAK,GAAG,KAAK,IAAI,kBAAkB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;SAChE;QAED,OAAO,IAAI,2BAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAES,+BAA+B,CAAC,OAAqB;QAC9D,IAAI,eAAe,GAAuB,OAAO,CAAC,eAAe,CAAC;QAClE,IAAI,eAAe,IAAI,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,SAAG,CAAC,kBAAkB,EAAE;YAC5E,eAAe,GAAG,IAAI,eAAM,EAAE,CAAC;YAC/B,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,eAAe,CAAC;IACxB,CAAC;IAGM,YAAY,CAAC,CAAS;QAC5B,IAAI,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;YACpB,OAAO,KAAK,CAAC;SACb;QAED,IAAI,UAAU,GAAe,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,+BAAc,CAAC,gBAAgB,CAAC;QAC9G,IAAI,WAAW,GAAW,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;YAC9B,OAAO,WAAW,CAAC;SACnB;QAED,OAAO,WAAW,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,CAAC;IAEM,gBAAgB,CAAC,KAAkB;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IAEM,kBAAkB,CAAU,IAA0B;QAC5D,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzC,IAAI,CAAC,cAAc,EAAE;YACpB,OAAO;SACP;QAED,KAAK,IAAI,CAAC,IAAI,cAAc,EAAE;YAC7B,IAAI,KAAK,GAAW,UAAU,CAAC;YAC/B,IAAI,CAAC,CAAC,KAAK,CAAC,4BAA4B,GAAG,CAAC,EAAE;gBAC7C,IAAI,CAAC,GAAe,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,YAAY,+BAAc,EAAE;oBAChC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;iBAC9C;qBACI,IAAI,CAAC,YAAY,6BAAa,EAAE;oBACpC,IAAI,GAAG,GAAY,CAAC,YAAY,mCAAgB,CAAC;oBACjD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBACrD;aACD;YACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;SAC1D;IACF,CAAC;IAGS,WAAW,CACX,KAAkB,EAClB,YAA+B,EAC/B,OAAqB,EAC9B,UAAkB;QAClB,OAAO,IAAI,2CAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAClD,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EACrB,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EACX,OAAO,EAAE,YAAY,CAAC,CAAC;IACzB,CAAC;IAES,YAAY,CAAU,OAA4B;QAC3D,IAAI,GAAG,GAAW,SAAG,CAAC,kBAAkB,CAAC;QACzC,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,GAAG,KAAK,SAAG,CAAC,kBAAkB,EAAE;gBACnC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,kBAAkB;aAC/B;iBACI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBACvB,OAAO,SAAG,CAAC,kBAAkB,CAAC;aAC9B;SACD;QACD,OAAO,GAAG,CAAC;IACZ,CAAC;IAES,wBAAwB,CAAU,OAA4B,EAAE,GAAW;QACpF,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBAClB,IAAI,CAAC,CAAC,KAAK,YAAY,6BAAa,EAAE;oBACrC,OAAO,IAAI,CAAC;iBACZ;aACD;SACD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAGS,UAAU,CACV,GAAQ,EACR,SAAmB,EAC5B,CAAS,EACT,kBAA2C,EAClC,SAAuB,EAChC,YAAoC;QACpC,MAAM,CAAC,kBAAkB,IAAI,IAAI,IAAI,kBAAkB,CAAC,OAAO,IAAI,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAE3F,IAAI,IAAI,GAAa,SAAS,CAAC;QAC/B,IAAI,EAAE,GAAa,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAElE,IAAI,kBAAkB,IAAI,IAAI,EAAE;YAC/B,KAAK,IAAI,OAAO,IAAI,kBAAkB,CAAC,OAAO,EAAE,EAAE;gBACjD,IAAI,OAAO,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;oBACvD,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;wBACtC,SAAS;qBACT;iBACD;gBAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,IAAI,GAAyB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,IAAI,IAAI,IAAI,EAAE;oBACjB,IAAI,GAAG,IAAI,CAAC;oBACZ,SAAS;iBACT;gBAED,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;gBACzE,MAAM,CAAC,OAAO,KAAK,qCAAiB,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;gBAChG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBACrC,IAAI,GAAG,IAAI,CAAC;aACZ;SACD;QAED,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5E;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7B,IAAI,kBAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,+BAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;SAClL;QACD,OAAO,EAAE,CAAC;IACX,CAAC;IAES,UAAU,CAAW,CAAW,EAAE,CAAS,EAAY,CAAW;QAC3E,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAClB;IACF,CAAC;IAED,mDAAmD;IAEzC,kBAAkB,CAAU,GAAQ,EAAW,OAAqB,EAAE,aAAqB,EAAE,YAAoC;QAC1I,IAAI,aAAa,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;YAC7D,IAAI,cAAc,GAAiB,IAAI,2BAAY,EAAE,CAAC;YACtD,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;gBAC3B,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAC;aACtE;YAED,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;SAC3D;aACI;YACJ,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAAE,uCAAuC,CAAC,CAAC;YAC/E,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACpC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SACpD;IACF,CAAC;IAED,mDAAmD;IAEzC,WAAW,CAAU,GAAQ,EAAW,OAAqB,EAAE,YAAoC;QAC5G,IAAI,SAAS,GAAY,IAAI,CAAC,yBAAyB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;QACzF,IAAI,SAAS,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBACxB,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,IAAI,QAAQ,GAAa,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,QAAQ,GAAyB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACrB,OAAO,QAAQ,CAAC;aAChB;SACD;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YACxB,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;gBACjC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;aAChE;SACD;QAED,IAAI,QAAQ,GAAa,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,iFAAiF;QACjF,IAAI,aAAa,GAAkB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAkB,CAAC;QAC5F,IAAI,YAAY,GAAW,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,YAAY,KAAK,SAAG,CAAC,kBAAkB,EAAE;YAC5C,QAAQ,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,YAAY,CAAC,CAAC;SAC7D;aAAM,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,EAAE;YAC3C,IAAI,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAC9C,IAAI,eAAe,EAAE;gBACpB,QAAQ,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9E;SACD;QAED,IAAI,QAAQ,CAAC,aAAa,IAAI,OAAO,CAAC,kBAAkB,EAAE;YACzD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,SAAS,EAAE;YACf,OAAO,QAAQ,CAAC;SAChB;QAED,IAAI,KAAK,GAAa,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,kBAAkB,CAAC,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE;YACnD,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,QAAQ,CAAC,CAAC;SACjD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAGS,cAAc,CAAU,GAAQ,EAAW,OAAqB;QACzE,OAAO,IAAI,mBAAQ,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAES,2BAA2B,CAAU,GAAQ,EAAE,eAAmC,EAAW,aAA6B,EAAE,UAAkB,EAAE,SAAiB;QAC1K,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,WAAW,EAAE;YAC/D,IAAI,QAAQ,GAAa,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,uCAAuC,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,OAAO;gBAClG,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,QAAQ,CAAC,2BAA2B,EAAE;gBACzC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;aAC/G;SACD;IACF,CAAC;IAES,wBAAwB,CAAU,GAAQ,EAAE,UAAkB,EAAW,WAA2B,EAAE,UAAkB,EAAE,SAAiB;QACpJ,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,WAAW,EAAE;YAC/D,IAAI,QAAQ,GAAa,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,oCAAoC,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC,EAAE,CAAC,OAAO;gBAC7F,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,QAAQ,CAAC,wBAAwB,EAAE;gBACtC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;aACrG;SACD;IACF,CAAC;IAED,wEAAwE;IAC9D,eAAe,CACf,GAAQ,EACjB,CAAW,EAAG,4DAA4D;IAC1E,UAAkB,EAClB,SAAiB,EACjB,KAAc,EACL,SAAiB,EACjB,OAAqB;QAE9B,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,WAAW,EAAE;YAC/D,IAAI,QAAQ,GAAa,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,kBAAkB;gBAC7B,SAAS,GAAG,GAAG,GAAG,OAAO;gBACzB,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,QAAQ,CAAC,eAAe,EAAE;gBAC7B,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;aAC9F;SACD;IACF,CAAC;IAES,cAAc,CAAC,OAAoB;QAC5C,IAAI,OAAO,CAAC,OAAO,EAAE;YACpB,OAAO,qCAAiB,CAAC,oBAAoB,CAAC;SAC9C;QAED,IAAI,KAAK,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,UAAU,GAAmB,KAAK,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;QACvE,OAAO,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC;IAC3C,CAAC;IAES,aAAa,CAAC,OAA0B;QACjD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC9B,OAAO,OAAO,CAAC;SACf;QAED,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,IAAI,KAAK,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,iBAAwB,CAAC,CAAC;YACzG,IAAI,UAAU,GAAmB,KAAK,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;YACvE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;gBACzB,MAAM;aACN;YAED,uGAAuG;YACvG,kFAAkF;YAClF,OAAO,GAAG,OAAO,CAAC,MAA2B,CAAC;SAC9C;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;CACD,CAAA;AAjrEc,wBAAK,GAAY,KAAK,CAAC;AACvB,4BAAS,GAAY,KAAK,CAAC;AAC3B,8BAAW,GAAY,KAAK,CAAC;AAqtD5B,4CAAyB,GACvC,CAAC,EAAa,EAAE,EAAa,EAAU,EAAE;IACxC,IAAI,IAAI,GAAW,EAAE,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC;IAC7E,IAAI,IAAI,KAAK,CAAC,EAAE;QACf,OAAO,IAAI,CAAC;KACZ;IAED,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;IACvB,IAAI,IAAI,KAAK,CAAC,EAAE;QACf,OAAO,IAAI,CAAC;KACZ;IAED,OAAO,CAAC,CAAC;AACV,CAAC,CAAA;AA/tDF;IADC,oBAAO;0DACmD;AAqD3D;IADC,oBAAO;2DAGP;AAED;IAA0B,WAAA,oBAAO,CAAA;2DAEhC;AAGD;IADC,qBAAQ;+CAGR;AAID;IACE,WAAA,oBAAO,CAAA;yDAmER;AAED;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;uDAqDR;AAED;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;iDAwLR;AAqFD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;iDA4HR;AAqDD;IAA6B,WAAA,oBAAO,CAAA,EAA0C,WAAA,oBAAO,CAAA;2DAmEpF;AA0DD;IAAkC,WAAA,oBAAO,CAAA;gEAExC;AAkBD;IADC,oBAAO;IACsB,WAAA,oBAAO,CAAA,EAAY,WAAA,oBAAO,CAAA;4DA6IvD;AAgBD;IADC,oBAAO;IACsC,WAAA,oBAAO,CAAA;4EAepD;AAGD;IADC,oBAAO;2DAgKP;AAuDD;IADC,oBAAO;IACyB,WAAA,oBAAO,CAAA;+DA8CvC;AAED;IAA8B,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;4DAMhE;AA2BD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;8DA4CR;AAqCD;IACE,WAAA,oBAAO,CAAA;6DA+BR;AA0BD;IAAmC,WAAA,oBAAO,CAAA;iEAEzC;AASD;IAEE,WAAA,oBAAO,CAAA;IAGP,WAAA,qBAAQ,CAAA;iDAgBT;AAED;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,qBAAQ,CAAA;IACR,WAAA,oBAAO,CAAA;IAGP,WAAA,oBAAO,CAAA;qDAkKR;AAGD;IADC,oBAAO;qDAMP;AAED;IAA4B,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;0DAiC9D;AAGD;IADC,oBAAO;IACoB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;0DAK9D;AAGD;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;8DA0BR;AAGD;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;wDA2BR;AAGD;IADC,oBAAO;IACkB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA,EAAqB,WAAA,qBAAQ,CAAA;wDAoBzF;AAiBD;IAAsB,WAAA,oBAAO,CAAA;sDA0J5B;AAaD;IADC,oBAAO;sDAaP;AAMD;IAA2B,WAAA,oBAAO,CAAA;4DAqBjC;AAGD;IADC,oBAAO;IAEN,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;qDAMR;AAED;IAAwB,WAAA,oBAAO,CAAA;sDAW9B;AAED;IAAoC,WAAA,oBAAO,CAAA;kEAS1C;AAGD;IADC,oBAAO;IAEN,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IAGP,WAAA,oBAAO,CAAA;oDAsCR;AAED;IAAsB,WAAA,qBAAQ,CAAA,EAA0B,WAAA,qBAAQ,CAAA;oDAI/D;AAID;IADC,oBAAO;IACsB,WAAA,oBAAO,CAAA,EAAY,WAAA,oBAAO,CAAA;4DAevD;AAID;IADC,oBAAO;IACe,WAAA,oBAAO,CAAA,EAAY,WAAA,oBAAO,CAAA;qDA8ChD;AAGD;IADC,oBAAO;IACkB,WAAA,oBAAO,CAAA,EAAY,WAAA,oBAAO,CAAA;wDAEnD;AAED;IAAuC,WAAA,oBAAO,CAAA,EAAiD,WAAA,oBAAO,CAAA;qEAYrG;AAED;IAAoC,WAAA,oBAAO,CAAA,EAAgC,WAAA,oBAAO,CAAA;kEAYjF;AAGD;IACE,WAAA,oBAAO,CAAA;IAKP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;yDAcR;AA3oEW,kBAAkB;IAqDjB,WAAA,oBAAO,CAAA;GArDR,kBAAkB,CAkrE9B;AAlrEY,gDAAkB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:31.1989835-07:00\r\n\r\nimport { AcceptStateInfo } from \"../dfa/AcceptStateInfo\";\r\nimport { ActionTransition } from \"./ActionTransition\";\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { Arrays } from \"../misc/Arrays\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { ATNSimulator } from \"./ATNSimulator\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { AtomTransition } from \"./AtomTransition\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { ConflictInfo } from \"./ConflictInfo\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { DFAState } from \"../dfa/DFAState\";\r\nimport { EpsilonTransition } from \"./EpsilonTransition\";\r\nimport { IntegerList } from \"../misc/IntegerList\";\r\nimport { Interval } from \"../misc/Interval\";\r\nimport { IntStream } from \"../IntStream\";\r\nimport { NotNull, Nullable, Override } from \"../Decorators\";\r\nimport { NotSetTransition } from \"./NotSetTransition\";\r\nimport { NoViableAltException } from \"../NoViableAltException\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { Parser } from \"../Parser\";\r\nimport { ParserRuleContext } from \"../ParserRuleContext\";\r\nimport { PrecedencePredicateTransition } from \"./PrecedencePredicateTransition\";\r\nimport { PredicateTransition } from \"./PredicateTransition\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { PredictionContextCache } from \"./PredictionContextCache\";\r\nimport { PredictionMode } from \"./PredictionMode\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\nimport { SetTransition } from \"./SetTransition\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { StarLoopEntryState } from \"./StarLoopEntryState\";\r\nimport { Token } from \"../Token\";\r\nimport { TokenStream } from \"../TokenStream\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\nimport { Vocabulary } from \"../Vocabulary\";\r\nimport { VocabularyImpl } from \"../VocabularyImpl\";\r\n\r\nimport * as assert from \"assert\";\r\n\r\nconst MAX_SHORT_VALUE = 0xFFFF;\r\nconst MIN_INTEGER_VALUE = -((1 << 31) >>> 0);\r\n\r\n/**\r\n * The embodiment of the adaptive LL(*), ALL(*), parsing strategy.\r\n *\r\n * The basic complexity of the adaptive strategy makes it harder to understand.\r\n * We begin with ATN simulation to build paths in a DFA. Subsequent prediction\r\n * requests go through the DFA first. If they reach a state without an edge for\r\n * the current symbol, the algorithm fails over to the ATN simulation to\r\n * complete the DFA path for the current input (until it finds a conflict state\r\n * or uniquely predicting state).\r\n *\r\n * All of that is done without using the outer context because we want to create\r\n * a DFA that is not dependent upon the rule invocation stack when we do a\r\n * prediction. One DFA works in all contexts. We avoid using context not\r\n * necessarily because it's slower, although it can be, but because of the DFA\r\n * caching problem. The closure routine only considers the rule invocation stack\r\n * created during prediction beginning in the decision rule. For example, if\r\n * prediction occurs without invoking another rule's ATN, there are no context\r\n * stacks in the configurations. When lack of context leads to a conflict, we\r\n * don't know if it's an ambiguity or a weakness in the strong LL(*) parsing\r\n * strategy (versus full LL(*)).\r\n *\r\n * When SLL yields a configuration set with conflict, we rewind the input and\r\n * retry the ATN simulation, this time using full outer context without adding\r\n * to the DFA. Configuration context stacks will be the full invocation stacks\r\n * from the start rule. If we get a conflict using full context, then we can\r\n * definitively say we have a true ambiguity for that input sequence. If we\r\n * don't get a conflict, it implies that the decision is sensitive to the outer\r\n * context. (It is not context-sensitive in the sense of context-sensitive\r\n * grammars.)\r\n *\r\n * The next time we reach this DFA state with an SLL conflict, through DFA\r\n * simulation, we will again retry the ATN simulation using full context mode.\r\n * This is slow because we can't save the results and have to \"interpret\" the\r\n * ATN each time we get that input.\r\n *\r\n * **CACHING FULL CONTEXT PREDICTIONS**\r\n *\r\n * We could cache results from full context to predicted alternative easily and\r\n * that saves a lot of time but doesn't work in presence of predicates. The set\r\n * of visible predicates from the ATN start state changes depending on the\r\n * context, because closure can fall off the end of a rule. I tried to cache\r\n * tuples (stack context, semantic context, predicted alt) but it was slower\r\n * than interpreting and much more complicated. Also required a huge amount of\r\n * memory. The goal is not to create the world's fastest parser anyway. I'd like\r\n * to keep this algorithm simple. By launching multiple threads, we can improve\r\n * the speed of parsing across a large number of files.\r\n *\r\n * There is no strict ordering between the amount of input used by SLL vs LL,\r\n * which makes it really hard to build a cache for full context. Let's say that\r\n * we have input A B C that leads to an SLL conflict with full context X. That\r\n * implies that using X we might only use A B but we could also use A B C D to\r\n * resolve conflict. Input A B C D could predict alternative 1 in one position\r\n * in the input and A B C E could predict alternative 2 in another position in\r\n * input. The conflicting SLL configurations could still be non-unique in the\r\n * full context prediction, which would lead us to requiring more input than the\r\n * original A B C.\tTo make a\tprediction cache work, we have to track\tthe exact\r\n * input\tused during the previous prediction. That amounts to a cache that maps\r\n * X to a specific DFA for that context.\r\n *\r\n * Something should be done for left-recursive expression predictions. They are\r\n * likely LL(1) + pred eval. Easier to do the whole SLL unless error and retry\r\n * with full LL thing Sam does.\r\n *\r\n * **AVOIDING FULL CONTEXT PREDICTION**\r\n *\r\n * We avoid doing full context retry when the outer context is empty, we did not\r\n * dip into the outer context by falling off the end of the decision state rule,\r\n * or when we force SLL mode.\r\n *\r\n * As an example of the not dip into outer context case, consider as super\r\n * constructor calls versus function calls. One grammar might look like\r\n * this:\r\n *\r\n * ```antlr\r\n * ctorBody\r\n *   : '{' superCall? stat* '}'\r\n *   ;\r\n * ```\r\n *\r\n * Or, you might see something like\r\n *\r\n * ```antlr\r\n * stat\r\n *   : superCall ';'\r\n *   | expression ';'\r\n *   | ...\r\n *   ;\r\n * ```\r\n *\r\n * In both cases I believe that no closure operations will dip into the outer\r\n * context. In the first case ctorBody in the worst case will stop at the '}'.\r\n * In the 2nd case it should stop at the ';'. Both cases should stay within the\r\n * entry rule and not dip into the outer context.\r\n *\r\n * **PREDICATES**\r\n *\r\n * Predicates are always evaluated if present in either SLL or LL both. SLL and\r\n * LL simulation deals with predicates differently. SLL collects predicates as\r\n * it performs closure operations like ANTLR v3 did. It delays predicate\r\n * evaluation until it reaches and accept state. This allows us to cache the SLL\r\n * ATN simulation whereas, if we had evaluated predicates on-the-fly during\r\n * closure, the DFA state configuration sets would be different and we couldn't\r\n * build up a suitable DFA.\r\n *\r\n * When building a DFA accept state during ATN simulation, we evaluate any\r\n * predicates and return the sole semantically valid alternative. If there is\r\n * more than 1 alternative, we report an ambiguity. If there are 0 alternatives,\r\n * we throw an exception. Alternatives without predicates act like they have\r\n * true predicates. The simple way to think about it is to strip away all\r\n * alternatives with false predicates and choose the minimum alternative that\r\n * remains.\r\n *\r\n * When we start in the DFA and reach an accept state that's predicated, we test\r\n * those and return the minimum semantically viable alternative. If no\r\n * alternatives are viable, we throw an exception.\r\n *\r\n * During full LL ATN simulation, closure always evaluates predicates and\r\n * on-the-fly. This is crucial to reducing the configuration set size during\r\n * closure. It hits a landmine when parsing with the Java grammar, for example,\r\n * without this on-the-fly evaluation.\r\n *\r\n * **SHARING DFA**\r\n *\r\n * All instances of the same parser share the same decision DFAs through a\r\n * static field. Each instance gets its own ATN simulator but they share the\r\n * same {@link ATN#decisionToDFA} field. They also share a\r\n * {@link PredictionContextCache} object that makes sure that all\r\n * {@link PredictionContext} objects are shared among the DFA states. This makes\r\n * a big size difference.\r\n *\r\n * **THREAD SAFETY**\r\n *\r\n * The {@link ParserATNSimulator} locks on the {@link ATN#decisionToDFA} field when\r\n * it adds a new DFA object to that array. {@link #addDFAEdge}\r\n * locks on the DFA for the current decision when setting the\r\n * {@link DFAState#edges} field. {@link #addDFAState} locks on\r\n * the DFA for the current decision when looking up a DFA state to see if it\r\n * already exists. We must make sure that all requests to add DFA states that\r\n * are equivalent result in the same shared DFA object. This is because lots of\r\n * threads will be trying to update the DFA at once. The\r\n * {@link #addDFAState} method also locks inside the DFA lock\r\n * but this time on the shared context cache when it rebuilds the\r\n * configurations' {@link PredictionContext} objects using cached\r\n * subgraphs/nodes. No other locking occurs, even during DFA simulation. This is\r\n * safe as long as we can guarantee that all threads referencing\r\n * `s.edge[t]` get the same physical target {@link DFAState}, or\r\n * `undefined`. Once into the DFA, the DFA simulation does not reference the\r\n * {@link DFA#states} map. It follows the {@link DFAState#edges} field to new\r\n * targets. The DFA simulator will either find {@link DFAState#edges} to be\r\n * `undefined`, to be non-`undefined` and `dfa.edges[t]` undefined, or\r\n * `dfa.edges[t]` to be non-undefined. The\r\n * {@link #addDFAEdge} method could be racing to set the field\r\n * but in either case the DFA simulator works; if `undefined`, and requests ATN\r\n * simulation. It could also race trying to get `dfa.edges[t]`, but either\r\n * way it will work because it's not doing a test and set operation.\r\n *\r\n * **Starting with SLL then failing to combined SLL/LL (Two-Stage\r\n * Parsing)**\r\n *\r\n * Sam pointed out that if SLL does not give a syntax error, then there is no\r\n * point in doing full LL, which is slower. We only have to try LL if we get a\r\n * syntax error. For maximum speed, Sam starts the parser set to pure SLL\r\n * mode with the {@link BailErrorStrategy}:\r\n *\r\n * ```\r\n * parser.interpreter.{@link #setPredictionMode setPredictionMode}`(`{@link PredictionMode#SLL}`)`;\r\n * parser.{@link Parser#setErrorHandler setErrorHandler}(new {@link BailErrorStrategy}());\r\n * ```\r\n *\r\n * If it does not get a syntax error, then we're done. If it does get a syntax\r\n * error, we need to retry with the combined SLL/LL strategy.\r\n *\r\n * The reason this works is as follows. If there are no SLL conflicts, then the\r\n * grammar is SLL (at least for that input set). If there is an SLL conflict,\r\n * the full LL analysis must yield a set of viable alternatives which is a\r\n * subset of the alternatives reported by SLL. If the LL set is a singleton,\r\n * then the grammar is LL but not SLL. If the LL set is the same size as the SLL\r\n * set, the decision is SLL. If the LL set has size &gt; 1, then that decision\r\n * is truly ambiguous on the current input. If the LL set is smaller, then the\r\n * SLL conflict resolution might choose an alternative that the full LL would\r\n * rule out as a possibility based upon better context information. If that's\r\n * the case, then the SLL parse will definitely get an error because the full LL\r\n * analysis says it's not viable. If SLL conflict resolution chooses an\r\n * alternative within the LL set, them both SLL and LL would choose the same\r\n * alternative because they both choose the minimum of multiple conflicting\r\n * alternatives.\r\n *\r\n * Let's say we have a set of SLL conflicting alternatives `{1, 2, 3}` and\r\n * a smaller LL set called *s*. If *s* is `{2, 3}`, then SLL\r\n * parsing will get an error because SLL will pursue alternative 1. If\r\n * *s* is `{1, 2}` or `{1, 3}` then both SLL and LL will\r\n * choose the same alternative because alternative one is the minimum of either\r\n * set. If *s* is `{2}` or `{3}` then SLL will get a syntax\r\n * error. If *s* is `{1}` then SLL will succeed.\r\n *\r\n * Of course, if the input is invalid, then we will get an error for sure in\r\n * both SLL and LL parsing. Erroneous input will therefore require 2 passes over\r\n * the input.\r\n */\r\nexport class ParserATNSimulator extends ATNSimulator {\r\n\tpublic static debug: boolean = false;\r\n\tpublic static dfa_debug: boolean = false;\r\n\tpublic static retry_debug: boolean = false;\r\n\r\n\t@NotNull\r\n\tprivate predictionMode: PredictionMode = PredictionMode.LL;\r\n\tpublic force_global_context: boolean = false;\r\n\tpublic always_try_local_context: boolean = true;\r\n\r\n\t/**\r\n\t * Determines whether the DFA is used for full-context predictions. When\r\n\t * `true`, the DFA stores transition information for both full-context\r\n\t * and SLL parsing; otherwise, the DFA only stores SLL transition\r\n\t * information.\r\n\t *\r\n\t * For some grammars, enabling the full-context DFA can result in a\r\n\t * substantial performance improvement. However, this improvement typically\r\n\t * comes at the expense of memory used for storing the cached DFA states,\r\n\t * configuration sets, and prediction contexts.\r\n\t *\r\n\t * The default value is `false`.\r\n\t */\r\n\tpublic enable_global_context_dfa: boolean = false;\r\n\tpublic optimize_unique_closure: boolean = true;\r\n\tpublic optimize_ll1: boolean = true;\r\n\tpublic optimize_tail_calls: boolean = true;\r\n\tpublic tail_call_preserves_sll: boolean = true;\r\n\tpublic treat_sllk1_conflict_as_ambiguity: boolean = false;\r\n\r\n\tprotected _parser: Parser;\r\n\r\n\t/**\r\n\t * When `true`, ambiguous alternatives are reported when they are\r\n\t * encountered within {@link #execATN}. When `false`, these messages\r\n\t * are suppressed. The default is `false`.\r\n\t *\r\n\t * When messages about ambiguous alternatives are not required, setting this\r\n\t * to `false` enables additional internal optimizations which may lose\r\n\t * this information.\r\n\t */\r\n\tpublic reportAmbiguities: boolean = false;\r\n\r\n\t/** By default we do full context-sensitive LL(*) parsing not\r\n\t *  Strong LL(*) parsing. If we fail with Strong LL(*) we\r\n\t *  try full LL(*). That means we rewind and use context information\r\n\t *  when closure operations fall off the end of the rule that\r\n\t *  holds the decision were evaluating.\r\n\t */\r\n\tprotected userWantsCtxSensitive: boolean = true;\r\n\r\n\tprivate dfa?: DFA;\r\n\r\n\tconstructor(@NotNull atn: ATN, parser: Parser) {\r\n\t\tsuper(atn);\r\n\t\tthis._parser = parser;\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getPredictionMode(): PredictionMode {\r\n\t\treturn this.predictionMode;\r\n\t}\r\n\r\n\tpublic setPredictionMode(@NotNull predictionMode: PredictionMode): void {\r\n\t\tthis.predictionMode = predictionMode;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reset(): void {\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\tpublic adaptivePredict(/*@NotNull*/ input: TokenStream, decision: number, outerContext: ParserRuleContext | undefined): number;\r\n\tpublic adaptivePredict(/*@NotNull*/ input: TokenStream, decision: number, outerContext: ParserRuleContext | undefined, useContext: boolean): number;\r\n\tpublic adaptivePredict(\r\n\t\t@NotNull input: TokenStream,\r\n\t\tdecision: number,\r\n\t\touterContext: ParserRuleContext | undefined,\r\n\t\tuseContext?: boolean): number {\r\n\t\tif (useContext === undefined) {\r\n\t\t\tuseContext = false;\r\n\t\t}\r\n\r\n\t\tlet dfa: DFA = this.atn.decisionToDFA[decision];\r\n\t\tassert(dfa != null);\r\n\t\tif (this.optimize_ll1 && !dfa.isPrecedenceDfa && !dfa.isEmpty) {\r\n\t\t\tlet ll_1: number = input.LA(1);\r\n\t\t\tif (ll_1 >= 0 && ll_1 <= 0xFFFF) {\r\n\t\t\t\tlet key: number = ((decision << 16) >>> 0) + ll_1;\r\n\t\t\t\tlet alt: number | undefined = this.atn.LL1Table.get(key);\r\n\t\t\t\tif (alt != null) {\r\n\t\t\t\t\treturn alt;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.dfa = dfa;\r\n\r\n\t\tif (this.force_global_context) {\r\n\t\t\tuseContext = true;\r\n\t\t}\r\n\t\telse if (!this.always_try_local_context) {\r\n\t\t\tuseContext = useContext || dfa.isContextSensitive;\r\n\t\t}\r\n\r\n\t\tthis.userWantsCtxSensitive = useContext || (this.predictionMode !== PredictionMode.SLL && outerContext != null && !this.atn.decisionToState[decision].sll);\r\n\t\tif (outerContext == null) {\r\n\t\t\touterContext = ParserRuleContext.emptyContext();\r\n\t\t}\r\n\r\n\t\tlet state: SimulatorState | undefined;\r\n\t\tif (!dfa.isEmpty) {\r\n\t\t\tstate = this.getStartState(dfa, input, outerContext, useContext);\r\n\t\t}\r\n\r\n\t\tif (state == null) {\r\n\t\t\tif (outerContext == null) {\r\n\t\t\t\touterContext = ParserRuleContext.emptyContext();\r\n\t\t\t}\r\n\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\tconsole.log(\"ATN decision \" + dfa.decision +\r\n\t\t\t\t\t\" exec LA(1)==\" + this.getLookaheadName(input) +\r\n\t\t\t\t\t\", outerContext=\" + outerContext.toString(this._parser));\r\n\t\t\t}\r\n\r\n\t\t\tstate = this.computeStartState(dfa, outerContext, useContext);\r\n\t\t}\r\n\r\n\t\tlet m: number = input.mark();\r\n\t\tlet index: number = input.index;\r\n\t\ttry {\r\n\t\t\tlet alt: number = this.execDFA(dfa, input, index, state);\r\n\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\tconsole.log(\"DFA after predictATN: \" + dfa.toString(this._parser.vocabulary, this._parser.ruleNames));\r\n\t\t\t}\r\n\t\t\treturn alt;\r\n\t\t}\r\n\t\tfinally {\r\n\t\t\tthis.dfa = undefined;\r\n\t\t\tinput.seek(index);\r\n\t\t\tinput.release(m);\r\n\t\t}\r\n\t}\r\n\r\n\tprotected getStartState(\r\n\t\t@NotNull dfa: DFA,\r\n\t\t@NotNull input: TokenStream,\r\n\t\t@NotNull outerContext: ParserRuleContext,\r\n\t\tuseContext: boolean): SimulatorState | undefined {\r\n\r\n\t\tif (!useContext) {\r\n\t\t\tif (dfa.isPrecedenceDfa) {\r\n\t\t\t\t// the start state for a precedence DFA depends on the current\r\n\t\t\t\t// parser precedence, and is provided by a DFA method.\r\n\t\t\t\tlet state: DFAState | undefined = dfa.getPrecedenceStartState(this._parser.precedence, false);\r\n\t\t\t\tif (state == null) {\r\n\t\t\t\t\treturn undefined;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn new SimulatorState(outerContext, state, false, outerContext);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tif (dfa.s0 == null) {\r\n\t\t\t\t\treturn undefined;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn new SimulatorState(outerContext, dfa.s0, false, outerContext);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!this.enable_global_context_dfa) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet remainingContext: ParserRuleContext | undefined = outerContext;\r\n\t\tassert(outerContext != null);\r\n\t\tlet s0: DFAState | undefined;\r\n\t\tif (dfa.isPrecedenceDfa) {\r\n\t\t\ts0 = dfa.getPrecedenceStartState(this._parser.precedence, true);\r\n\t\t}\r\n\t\telse {\r\n\t\t\ts0 = dfa.s0full;\r\n\t\t}\r\n\r\n\t\twhile (remainingContext != null && s0 != null && s0.isContextSensitive) {\r\n\t\t\tremainingContext = this.skipTailCalls(remainingContext);\r\n\t\t\ts0 = s0.getContextTarget(this.getReturnState(remainingContext));\r\n\t\t\tif (remainingContext.isEmpty) {\r\n\t\t\t\tassert(s0 == null || !s0.isContextSensitive);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tremainingContext = remainingContext.parent;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (s0 == null) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn new SimulatorState(outerContext, s0, useContext, remainingContext);\r\n\t}\r\n\r\n\tprotected execDFA(\r\n\t\t@NotNull dfa: DFA,\r\n\t\t@NotNull input: TokenStream, startIndex: number,\r\n\t\t@NotNull state: SimulatorState): number {\r\n\t\tlet outerContext: ParserRuleContext = state.outerContext;\r\n\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\tconsole.log(\"DFA decision \" + dfa.decision +\r\n\t\t\t\t\" exec LA(1)==\" + this.getLookaheadName(input) +\r\n\t\t\t\t\", outerContext=\" + outerContext.toString(this._parser));\r\n\t\t}\r\n\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\tconsole.log(dfa.toString(this._parser.vocabulary, this._parser.ruleNames));\r\n\t\t}\r\n\t\tlet s: DFAState = state.s0;\r\n\r\n\t\tlet t: number = input.LA(1);\r\n\t\tlet remainingOuterContext: ParserRuleContext | undefined = state.remainingOuterContext;\r\n\r\n\t\twhile (true) {\r\n\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\tconsole.log(\"DFA state \" + s.stateNumber + \" LA(1)==\" + this.getLookaheadName(input));\r\n\t\t\t}\r\n\t\t\tif (state.useContext) {\r\n\t\t\t\twhile (s.isContextSymbol(t)) {\r\n\t\t\t\t\tlet next: DFAState | undefined;\r\n\t\t\t\t\tif (remainingOuterContext != null) {\r\n\t\t\t\t\t\tremainingOuterContext = this.skipTailCalls(remainingOuterContext);\r\n\t\t\t\t\t\tnext = s.getContextTarget(this.getReturnState(remainingOuterContext));\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (next == null) {\r\n\t\t\t\t\t\t// fail over to ATN\r\n\t\t\t\t\t\tlet initialState: SimulatorState = new SimulatorState(state.outerContext, s, state.useContext, remainingOuterContext);\r\n\t\t\t\t\t\treturn this.execATN(dfa, input, startIndex, initialState);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tassert(remainingOuterContext != null);\r\n\t\t\t\t\tremainingOuterContext = remainingOuterContext.parent;\r\n\t\t\t\t\ts = next;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isAcceptState(s, state.useContext)) {\r\n\t\t\t\tif (s.predicates != null) {\r\n\t\t\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\t\tconsole.log(\"accept \" + s);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\t\tconsole.log(\"accept; predict \" + s.prediction + \" in state \" + s.stateNumber);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// keep going unless we're at EOF or state only has one alt number\r\n\t\t\t\t// mentioned in configs; check if something else could match\r\n\t\t\t\t// TODO: don't we always stop? only lexer would keep going\r\n\t\t\t\t// TODO: v3 dfa don't do this.\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\t// t is not updated if one of these states is reached\r\n\t\t\tassert(!this.isAcceptState(s, state.useContext));\r\n\r\n\t\t\t// if no edge, pop over to ATN interpreter, update DFA and return\r\n\t\t\tlet target: DFAState | undefined = this.getExistingTargetState(s, t);\r\n\t\t\tif (target == null) {\r\n\t\t\t\tif (ParserATNSimulator.dfa_debug && t >= 0) {\r\n\t\t\t\t\tconsole.log(\"no edge for \" + this._parser.vocabulary.getDisplayName(t));\r\n\t\t\t\t}\r\n\t\t\t\tlet alt: number;\r\n\t\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\tlet interval: Interval = Interval.of(startIndex, this._parser.inputStream.index);\r\n\t\t\t\t\tconsole.log(\"ATN exec upon \" +\r\n\t\t\t\t\t\tthis._parser.inputStream.getText(interval) +\r\n\t\t\t\t\t\t\" at DFA state \" + s.stateNumber);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet initialState: SimulatorState = new SimulatorState(outerContext, s, state.useContext, remainingOuterContext);\r\n\t\t\t\talt = this.execATN(dfa, input, startIndex, initialState);\r\n\t\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\tconsole.log(\"back from DFA update, alt=\" + alt + \", dfa=\\n\" + dfa.toString(this._parser.vocabulary, this._parser.ruleNames));\r\n\t\t\t\t\t//dump(dfa);\r\n\t\t\t\t}\r\n\t\t\t\t// action already executed\r\n\t\t\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\tconsole.log(\"DFA decision \" + dfa.decision +\r\n\t\t\t\t\t\t\" predicts \" + alt);\r\n\t\t\t\t}\r\n\t\t\t\treturn alt; // we've updated DFA, exec'd action, and have our deepest answer\r\n\t\t\t}\r\n\t\t\telse if (target === ATNSimulator.ERROR) {\r\n\t\t\t\tlet errorState: SimulatorState = new SimulatorState(outerContext, s, state.useContext, remainingOuterContext);\r\n\t\t\t\treturn this.handleNoViableAlt(input, startIndex, errorState);\r\n\t\t\t}\r\n\t\t\ts = target;\r\n\t\t\tif (!this.isAcceptState(s, state.useContext) && t !== IntStream.EOF) {\r\n\t\t\t\tinput.consume();\r\n\t\t\t\tt = input.LA(1);\r\n\t\t\t}\r\n\t\t}\r\n//\t\tif ( acceptState==null ) {\r\n//\t\t\tif ( debug ) System.out.println(\"!!! no viable alt in dfa\");\r\n//\t\t\treturn -1;\r\n//\t\t}\r\n\r\n\t\tif (!state.useContext && s.configs.conflictInfo != null) {\r\n\t\t\tif (dfa.atnStartState instanceof DecisionState) {\r\n\t\t\t\tif (!this.userWantsCtxSensitive ||\r\n\t\t\t\t\t(!s.configs.dipsIntoOuterContext && s.configs.isExactConflict) ||\r\n\t\t\t\t\t(this.treat_sllk1_conflict_as_ambiguity && input.index === startIndex)) {\r\n\t\t\t\t\t// we don't report the ambiguity again\r\n\t\t\t\t\t//if ( !this.acceptState.configset.hasSemanticContext ) {\r\n\t\t\t\t\t// \tthis.reportAmbiguity(dfa, acceptState, startIndex, input.index, acceptState.configset.conflictingAlts, acceptState.configset);\r\n\t\t\t\t\t//}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tassert(!state.useContext);\r\n\r\n\t\t\t\t\t// Before attempting full context prediction, check to see if there are\r\n\t\t\t\t\t// disambiguating or validating predicates to evaluate which allow an\r\n\t\t\t\t\t// immediate decision\r\n\t\t\t\t\tlet conflictingAlts: BitSet | undefined;\r\n\t\t\t\t\tlet predicates: DFAState.PredPrediction[] | undefined = s.predicates;\r\n\t\t\t\t\tif (predicates != null) {\r\n\t\t\t\t\t\tlet conflictIndex: number = input.index;\r\n\t\t\t\t\t\tif (conflictIndex !== startIndex) {\r\n\t\t\t\t\t\t\tinput.seek(startIndex);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconflictingAlts = this.evalSemanticContext(predicates, outerContext, true);\r\n\t\t\t\t\t\tif (conflictingAlts.cardinality() === 1) {\r\n\t\t\t\t\t\t\treturn conflictingAlts.nextSetBit(0);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (conflictIndex !== startIndex) {\r\n\t\t\t\t\t\t\t// restore the index so reporting the fallback to full\r\n\t\t\t\t\t\t\t// context occurs with the index at the correct spot\r\n\t\t\t\t\t\t\tinput.seek(conflictIndex);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (this.reportAmbiguities) {\r\n\t\t\t\t\t\tlet conflictState: SimulatorState = new SimulatorState(outerContext, s, state.useContext, remainingOuterContext);\r\n\t\t\t\t\t\tthis.reportAttemptingFullContext(dfa, conflictingAlts, conflictState, startIndex, input.index);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tinput.seek(startIndex);\r\n\t\t\t\t\treturn this.adaptivePredict(input, dfa.decision, outerContext, true);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Before jumping to prediction, check to see if there are\r\n\t\t// disambiguating or validating predicates to evaluate\r\n\t\tlet predicates: DFAState.PredPrediction[] | undefined = s.predicates;\r\n\t\tif (predicates != null) {\r\n\t\t\tlet stopIndex: number = input.index;\r\n\t\t\tif (startIndex !== stopIndex) {\r\n\t\t\t\tinput.seek(startIndex);\r\n\t\t\t}\r\n\r\n\t\t\tlet alts: BitSet = this.evalSemanticContext(predicates, outerContext, this.reportAmbiguities && this.predictionMode === PredictionMode.LL_EXACT_AMBIG_DETECTION);\r\n\t\t\tswitch (alts.cardinality()) {\r\n\t\t\tcase 0:\r\n\t\t\t\tthrow this.noViableAlt(input, outerContext, s.configs, startIndex);\r\n\r\n\t\t\tcase 1:\r\n\t\t\t\treturn alts.nextSetBit(0);\r\n\r\n\t\t\tdefault:\r\n\t\t\t\t// report ambiguity after predicate evaluation to make sure the correct\r\n\t\t\t\t// set of ambig alts is reported.\r\n\t\t\t\tif (startIndex !== stopIndex) {\r\n\t\t\t\t\tinput.seek(stopIndex);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.reportAmbiguity(dfa, s, startIndex, stopIndex, s.configs.isExactConflict, alts, s.configs);\r\n\t\t\t\treturn alts.nextSetBit(0);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.dfa_debug) {\r\n\t\t\tconsole.log(\"DFA decision \" + dfa.decision +\r\n\t\t\t\t\" predicts \" + s.prediction);\r\n\t\t}\r\n\t\treturn s.prediction;\r\n\t}\r\n\r\n\t/**\r\n\t * Determines if a particular DFA state should be treated as an accept state\r\n\t * for the current prediction mode. In addition to the `useContext`\r\n\t * parameter, the {@link #getPredictionMode()} method provides the\r\n\t * prediction mode controlling the prediction algorithm as a whole.\r\n\t *\r\n\t * The default implementation simply returns the value of\r\n\t * `DFAState.isAcceptState` except for conflict states when\r\n\t * `useContext` is `true` and {@link #getPredictionMode()} is\r\n\t * {@link PredictionMode#LL_EXACT_AMBIG_DETECTION}. In that case, only\r\n\t * conflict states where {@link ATNConfigSet#isExactConflict} is\r\n\t * `true` are considered accept states.\r\n\t *\r\n\t * @param state The DFA state to check.\r\n\t * @param useContext `true` if the prediction algorithm is currently\r\n\t * considering the full parser context; otherwise, `false` if the\r\n\t * algorithm is currently performing a local context prediction.\r\n\t *\r\n\t * @returns `true` if the specified `state` is an accept state;\r\n\t * otherwise, `false`.\r\n\t */\r\n\tprotected isAcceptState(state: DFAState, useContext: boolean): boolean {\r\n\t\tif (!state.isAcceptState) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (state.configs.conflictingAlts == null) {\r\n\t\t\t// unambiguous\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\t// More picky when we need exact conflicts\r\n\t\tif (useContext && this.predictionMode === PredictionMode.LL_EXACT_AMBIG_DETECTION) {\r\n\t\t\treturn state.configs.isExactConflict;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/** Performs ATN simulation to compute a predicted alternative based\r\n\t *  upon the remaining input, but also updates the DFA cache to avoid\r\n\t *  having to traverse the ATN again for the same input sequence.\r\n\t *\r\n\t * There are some key conditions we're looking for after computing a new\r\n\t * set of ATN configs (proposed DFA state):\r\n\t *\r\n\t * * if the set is empty, there is no viable alternative for current symbol\r\n\t * * does the state uniquely predict an alternative?\r\n\t * * does the state have a conflict that would prevent us from\r\n\t *   putting it on the work list?\r\n\t * * if in non-greedy decision is there a config at a rule stop state?\r\n\t *\r\n\t * We also have some key operations to do:\r\n\t *\r\n\t * * add an edge from previous DFA state to potentially new DFA state, D,\r\n\t *   upon current symbol but only if adding to work list, which means in all\r\n\t *   cases except no viable alternative (and possibly non-greedy decisions?)\r\n\t * * collecting predicates and adding semantic context to DFA accept states\r\n\t * * adding rule context to context-sensitive DFA accept states\r\n\t * * consuming an input symbol\r\n\t * * reporting a conflict\r\n\t * * reporting an ambiguity\r\n\t * * reporting a context sensitivity\r\n\t * * reporting insufficient predicates\r\n\t *\r\n\t * We should isolate those operations, which are side-effecting, to the\r\n\t * main work loop. We can isolate lots of code into other functions, but\r\n\t * they should be side effect free. They can return package that\r\n\t * indicates whether we should report something, whether we need to add a\r\n\t * DFA edge, whether we need to augment accept state with semantic\r\n\t * context or rule invocation context. Actually, it seems like we always\r\n\t * add predicates if they exist, so that can simply be done in the main\r\n\t * loop for any accept state creation or modification request.\r\n\t *\r\n\t * cover these cases:\r\n\t *   dead end\r\n\t *   single alt\r\n\t *   single alt + preds\r\n\t *   conflict\r\n\t *   conflict + preds\r\n\t *\r\n\t * TODO: greedy + those\r\n\t */\r\n\tprotected execATN(\r\n\t\t@NotNull dfa: DFA,\r\n\t\t@NotNull input: TokenStream, startIndex: number,\r\n\t\t@NotNull initialState: SimulatorState): number {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"execATN decision \" + dfa.decision + \" exec LA(1)==\" + this.getLookaheadName(input));\r\n\t\t}\r\n\r\n\t\tlet outerContext: ParserRuleContext = initialState.outerContext;\r\n\t\tlet useContext: boolean = initialState.useContext;\r\n\r\n\t\tlet t: number = input.LA(1);\r\n\r\n\t\tlet previous: SimulatorState = initialState;\r\n\r\n\t\tlet contextCache: PredictionContextCache = new PredictionContextCache();\r\n\t\twhile (true) { // while more work\r\n\t\t\tlet nextState: SimulatorState | undefined = this.computeReachSet(dfa, previous, t, contextCache);\r\n\t\t\tif (nextState == null) {\r\n\t\t\t\tthis.setDFAEdge(previous.s0, input.LA(1), ATNSimulator.ERROR);\r\n\t\t\t\treturn this.handleNoViableAlt(input, startIndex, previous);\r\n\t\t\t}\r\n\r\n\t\t\tlet D: DFAState = nextState.s0;\r\n\r\n\t\t\t// predicted alt => accept state\r\n\t\t\tassert(D.isAcceptState || D.prediction === ATN.INVALID_ALT_NUMBER);\r\n\t\t\t// conflicted => accept state\r\n\t\t\tassert(D.isAcceptState || D.configs.conflictInfo == null);\r\n\r\n\t\t\tif (this.isAcceptState(D, useContext)) {\r\n\t\t\t\tlet conflictingAlts: BitSet | undefined = D.configs.conflictingAlts;\r\n\t\t\t\tlet predictedAlt: number = conflictingAlts == null ? D.prediction : ATN.INVALID_ALT_NUMBER;\r\n\t\t\t\tif (predictedAlt !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\t\t\tif (this.optimize_ll1\r\n\t\t\t\t\t\t&& input.index === startIndex\r\n\t\t\t\t\t\t&& !dfa.isPrecedenceDfa\r\n\t\t\t\t\t\t&& nextState.outerContext === nextState.remainingOuterContext\r\n\t\t\t\t\t\t&& dfa.decision >= 0\r\n\t\t\t\t\t\t&& !D.configs.hasSemanticContext) {\r\n\t\t\t\t\t\tif (t >= 0 && t <= MAX_SHORT_VALUE) {\r\n\t\t\t\t\t\t\tlet key: number = ((dfa.decision << 16) >>> 0) + t;\r\n\t\t\t\t\t\t\tthis.atn.LL1Table.set(key, predictedAlt);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (useContext && this.always_try_local_context) {\r\n\t\t\t\t\t\tthis.reportContextSensitivity(dfa, predictedAlt, nextState, startIndex, input.index);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tpredictedAlt = D.prediction;\r\n//\t\t\t\tint k = input.index - startIndex + 1; // how much input we used\r\n//\t\t\t\tSystem.out.println(\"used k=\"+k);\r\n\t\t\t\tlet attemptFullContext: boolean = conflictingAlts != null && this.userWantsCtxSensitive;\r\n\t\t\t\tif (attemptFullContext) {\r\n\t\t\t\t\t// Only exact conflicts are known to be ambiguous when local\r\n\t\t\t\t\t// prediction does not step out of the decision rule.\r\n\t\t\t\t\tattemptFullContext = !useContext\r\n\t\t\t\t\t\t&& (D.configs.dipsIntoOuterContext || !D.configs.isExactConflict)\r\n\t\t\t\t\t\t&& (!this.treat_sllk1_conflict_as_ambiguity || input.index !== startIndex);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (D.configs.hasSemanticContext) {\r\n\t\t\t\t\tlet predPredictions: DFAState.PredPrediction[] | undefined = D.predicates;\r\n\t\t\t\t\tif (predPredictions != null) {\r\n\t\t\t\t\t\tlet conflictIndex: number = input.index;\r\n\t\t\t\t\t\tif (conflictIndex !== startIndex) {\r\n\t\t\t\t\t\t\tinput.seek(startIndex);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// use complete evaluation here if we'll want to retry with full context if still ambiguous\r\n\t\t\t\t\t\tconflictingAlts = this.evalSemanticContext(predPredictions, outerContext, attemptFullContext || this.reportAmbiguities);\r\n\t\t\t\t\t\tswitch (conflictingAlts.cardinality()) {\r\n\t\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t\tthrow this.noViableAlt(input, outerContext, D.configs, startIndex);\r\n\r\n\t\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t\treturn conflictingAlts.nextSetBit(0);\r\n\r\n\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (conflictIndex !== startIndex) {\r\n\t\t\t\t\t\t\t// restore the index so reporting the fallback to full\r\n\t\t\t\t\t\t\t// context occurs with the index at the correct spot\r\n\t\t\t\t\t\t\tinput.seek(conflictIndex);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!attemptFullContext) {\r\n\t\t\t\t\tif (conflictingAlts != null) {\r\n\t\t\t\t\t\tif (this.reportAmbiguities && conflictingAlts.cardinality() > 1) {\r\n\t\t\t\t\t\t\tthis.reportAmbiguity(dfa, D, startIndex, input.index, D.configs.isExactConflict, conflictingAlts, D.configs);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tpredictedAlt = conflictingAlts.nextSetBit(0);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn predictedAlt;\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tassert(!useContext);\r\n\t\t\t\t\tassert(this.isAcceptState(D, false));\r\n\r\n\t\t\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\t\t\tconsole.log(\"RETRY with outerContext=\" + outerContext);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet fullContextState: SimulatorState = this.computeStartState(dfa, outerContext, true);\r\n\t\t\t\t\tif (this.reportAmbiguities) {\r\n\t\t\t\t\t\tthis.reportAttemptingFullContext(dfa, conflictingAlts, nextState, startIndex, input.index);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tinput.seek(startIndex);\r\n\t\t\t\t\treturn this.execATN(dfa, input, startIndex, fullContextState);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tprevious = nextState;\r\n\r\n\t\t\tif (t !== IntStream.EOF) {\r\n\t\t\t\tinput.consume();\r\n\t\t\t\tt = input.LA(1);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * This method is used to improve the localization of error messages by\r\n\t * choosing an alternative rather than throwing a\r\n\t * {@link NoViableAltException} in particular prediction scenarios where the\r\n\t * {@link #ERROR} state was reached during ATN simulation.\r\n\t *\r\n\t * The default implementation of this method uses the following\r\n\t * algorithm to identify an ATN configuration which successfully parsed the\r\n\t * decision entry rule. Choosing such an alternative ensures that the\r\n\t * {@link ParserRuleContext} returned by the calling rule will be complete\r\n\t * and valid, and the syntax error will be reported later at a more\r\n\t * localized location.\r\n\t *\r\n\t * * If no configuration in `configs` reached the end of the\r\n\t *   decision rule, return {@link ATN#INVALID_ALT_NUMBER}.\r\n\t * * If all configurations in `configs` which reached the end of the\r\n\t *   decision rule predict the same alternative, return that alternative.\r\n\t * * If the configurations in `configs` which reached the end of the\r\n\t *   decision rule predict multiple alternatives (call this *S*),\r\n\t *   choose an alternative in the following order.\r\n\t *\r\n\t *     1. Filter the configurations in `configs` to only those\r\n\t *        configurations which remain viable after evaluating semantic predicates.\r\n\t *        If the set of these filtered configurations which also reached the end of\r\n\t *        the decision rule is not empty, return the minimum alternative\r\n\t *        represented in this set.\r\n\t *     1. Otherwise, choose the minimum alternative in *S*.\r\n\t *\r\n\t * In some scenarios, the algorithm described above could predict an\r\n\t * alternative which will result in a {@link FailedPredicateException} in\r\n\t * parser. Specifically, this could occur if the *only* configuration\r\n\t * capable of successfully parsing to the end of the decision rule is\r\n\t * blocked by a semantic predicate. By choosing this alternative within\r\n\t * {@link #adaptivePredict} instead of throwing a\r\n\t * {@link NoViableAltException}, the resulting\r\n\t * {@link FailedPredicateException} in the parser will identify the specific\r\n\t * predicate which is preventing the parser from successfully parsing the\r\n\t * decision rule, which helps developers identify and correct logic errors\r\n\t * in semantic predicates.\r\n\t *\r\n\t * @param input The input {@link TokenStream}\r\n\t * @param startIndex The start index for the current prediction, which is\r\n\t * the input index where any semantic context in `configs` should be\r\n\t * evaluated\r\n\t * @param previous The ATN simulation state immediately before the\r\n\t * {@link #ERROR} state was reached\r\n\t *\r\n\t * @returns The value to return from {@link #adaptivePredict}, or\r\n\t * {@link ATN#INVALID_ALT_NUMBER} if a suitable alternative was not\r\n\t * identified and {@link #adaptivePredict} should report an error instead.\r\n\t */\r\n\tprotected handleNoViableAlt(@NotNull input: TokenStream, startIndex: number, @NotNull previous: SimulatorState): number {\r\n\t\tif (previous.s0 != null) {\r\n\t\t\tlet alts: BitSet = new BitSet();\r\n\t\t\tlet maxAlt: number = 0;\r\n\t\t\tfor (let config of previous.s0.configs) {\r\n\t\t\t\tif (config.reachesIntoOuterContext || config.state instanceof RuleStopState) {\r\n\t\t\t\t\talts.set(config.alt);\r\n\t\t\t\t\tmaxAlt = Math.max(maxAlt, config.alt);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tswitch (alts.cardinality()) {\r\n\t\t\tcase 0:\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase 1:\r\n\t\t\t\treturn alts.nextSetBit(0);\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tif (!previous.s0.configs.hasSemanticContext) {\r\n\t\t\t\t\t// configs doesn't contain any predicates, so the predicate\r\n\t\t\t\t\t// filtering code below would be pointless\r\n\t\t\t\t\treturn alts.nextSetBit(0);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*\r\n\t\t\t\t * Try to find a configuration set that not only dipped into the outer\r\n\t\t\t\t * context, but also isn't eliminated by a predicate.\r\n\t\t\t\t */\r\n\t\t\t\tlet filteredConfigs: ATNConfigSet = new ATNConfigSet();\r\n\t\t\t\tfor (let config of previous.s0.configs) {\r\n\t\t\t\t\tif (config.reachesIntoOuterContext || config.state instanceof RuleStopState) {\r\n\t\t\t\t\t\tfilteredConfigs.add(config);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* The following code blocks are adapted from predicateDFAState with\r\n\t\t\t\t * the following key changes.\r\n\t\t\t\t *\r\n\t\t\t\t *  1. The code operates on an ATNConfigSet rather than a DFAState.\r\n\t\t\t\t *  2. Predicates are collected for all alternatives represented in\r\n\t\t\t\t *     filteredConfigs, rather than restricting the evaluation to\r\n\t\t\t\t *     conflicting and/or unique configurations.\r\n\t\t\t\t */\r\n\t\t\t\tlet altToPred: SemanticContext[] | undefined = this.getPredsForAmbigAlts(alts, filteredConfigs, maxAlt);\r\n\t\t\t\tif (altToPred != null) {\r\n\t\t\t\t\tlet predicates: DFAState.PredPrediction[] | undefined = this.getPredicatePredictions(alts, altToPred);\r\n\t\t\t\t\tif (predicates != null) {\r\n\t\t\t\t\t\tlet stopIndex: number = input.index;\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tinput.seek(startIndex);\r\n\t\t\t\t\t\t\tlet filteredAlts: BitSet = this.evalSemanticContext(predicates, previous.outerContext, false);\r\n\t\t\t\t\t\t\tif (!filteredAlts.isEmpty) {\r\n\t\t\t\t\t\t\t\treturn filteredAlts.nextSetBit(0);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfinally {\r\n\t\t\t\t\t\t\tinput.seek(stopIndex);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn alts.nextSetBit(0);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthrow this.noViableAlt(input, previous.outerContext, previous.s0.configs, startIndex);\r\n\t}\r\n\r\n\tprotected computeReachSet(dfa: DFA, previous: SimulatorState, t: number, contextCache: PredictionContextCache): SimulatorState | undefined {\r\n\t\tlet useContext: boolean = previous.useContext;\r\n\t\tlet remainingGlobalContext: ParserRuleContext | undefined = previous.remainingOuterContext;\r\n\r\n\t\tlet s: DFAState = previous.s0;\r\n\t\tif (useContext) {\r\n\t\t\twhile (s.isContextSymbol(t)) {\r\n\t\t\t\tlet next: DFAState | undefined;\r\n\t\t\t\tif (remainingGlobalContext != null) {\r\n\t\t\t\t\tremainingGlobalContext = this.skipTailCalls(remainingGlobalContext);\r\n\t\t\t\t\tnext = s.getContextTarget(this.getReturnState(remainingGlobalContext));\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (next == null) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tassert(remainingGlobalContext != null);\r\n\t\t\t\tremainingGlobalContext = remainingGlobalContext.parent;\r\n\t\t\t\ts = next;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tassert(!this.isAcceptState(s, useContext));\r\n\t\tif (this.isAcceptState(s, useContext)) {\r\n\t\t\treturn new SimulatorState(previous.outerContext, s, useContext, remainingGlobalContext);\r\n\t\t}\r\n\r\n\t\tlet s0: DFAState = s;\r\n\r\n\t\tlet target: DFAState | undefined = this.getExistingTargetState(s0, t);\r\n\t\tif (target == null) {\r\n\t\t\tlet result: [DFAState, ParserRuleContext | undefined] = this.computeTargetState(dfa, s0, remainingGlobalContext, t, useContext, contextCache);\r\n\t\t\ttarget = result[0];\r\n\t\t\tremainingGlobalContext = result[1];\r\n\t\t}\r\n\r\n\t\tif (target === ATNSimulator.ERROR) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tassert(!useContext || !target.configs.dipsIntoOuterContext);\r\n\t\treturn new SimulatorState(previous.outerContext, target, useContext, remainingGlobalContext);\r\n\t}\r\n\r\n\t/**\r\n\t * Get an existing target state for an edge in the DFA. If the target state\r\n\t * for the edge has not yet been computed or is otherwise not available,\r\n\t * this method returns `undefined`.\r\n\t *\r\n\t * @param s The current DFA state\r\n\t * @param t The next input symbol\r\n\t * @returns The existing target DFA state for the given input symbol\r\n\t * `t`, or `undefined` if the target state for this edge is not\r\n\t * already cached\r\n\t */\r\n\tprotected getExistingTargetState(@NotNull s: DFAState, t: number): DFAState | undefined {\r\n\t\treturn s.getTarget(t);\r\n\t}\r\n\r\n\t/**\r\n\t * Compute a target state for an edge in the DFA, and attempt to add the\r\n\t * computed state and corresponding edge to the DFA.\r\n\t *\r\n\t * @param dfa\r\n\t * @param s The current DFA state\r\n\t * @param remainingGlobalContext\r\n\t * @param t The next input symbol\r\n\t * @param useContext\r\n\t * @param contextCache\r\n\t *\r\n\t * @returns The computed target DFA state for the given input symbol\r\n\t * `t`. If `t` does not lead to a valid DFA state, this method\r\n\t * returns {@link #ERROR}.\r\n\t */\r\n\t@NotNull\r\n\tprotected computeTargetState(@NotNull dfa: DFA, @NotNull s: DFAState, remainingGlobalContext: ParserRuleContext | undefined, t: number, useContext: boolean, contextCache: PredictionContextCache): [DFAState, ParserRuleContext | undefined] {\r\n\t\tlet closureConfigs: ATNConfig[] = s.configs.toArray();\r\n\t\tlet contextElements: IntegerList | undefined;\r\n\t\tlet reach: ATNConfigSet = new ATNConfigSet();\r\n\t\tlet stepIntoGlobal: boolean;\r\n\t\tdo {\r\n\t\t\tlet hasMoreContext: boolean = !useContext || remainingGlobalContext != null;\r\n\t\t\tif (!hasMoreContext) {\r\n\t\t\t\treach.isOutermostConfigSet = true;\r\n\t\t\t}\r\n\r\n\t\t\tlet reachIntermediate: ATNConfigSet = new ATNConfigSet();\r\n\r\n\t\t\t/* Configurations already in a rule stop state indicate reaching the end\r\n\t\t\t * of the decision rule (local context) or end of the start rule (full\r\n\t\t\t * context). Once reached, these configurations are never updated by a\r\n\t\t\t * closure operation, so they are handled separately for the performance\r\n\t\t\t * advantage of having a smaller intermediate set when calling closure.\r\n\t\t\t *\r\n\t\t\t * For full-context reach operations, separate handling is required to\r\n\t\t\t * ensure that the alternative matching the longest overall sequence is\r\n\t\t\t * chosen when multiple such configurations can match the input.\r\n\t\t\t */\r\n\t\t\tlet skippedStopStates: ATNConfig[] | undefined;\r\n\r\n\t\t\tfor (let c of closureConfigs) {\r\n\t\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\t\tconsole.log(\"testing \" + this.getTokenName(t) + \" at \" + c.toString());\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.state instanceof RuleStopState) {\r\n\t\t\t\t\tassert(c.context.isEmpty);\r\n\t\t\t\t\tif (useContext && !c.reachesIntoOuterContext || t === IntStream.EOF) {\r\n\t\t\t\t\t\tif (skippedStopStates == null) {\r\n\t\t\t\t\t\t\tskippedStopStates = [];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tskippedStopStates.push(c);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet n: number = c.state.numberOfOptimizedTransitions;\r\n\t\t\t\tfor (let ti = 0; ti < n; ti++) {               // for each optimized transition\r\n\t\t\t\t\tlet trans: Transition = c.state.getOptimizedTransition(ti);\r\n\t\t\t\t\tlet target: ATNState | undefined = this.getReachableTarget(c, trans, t);\r\n\t\t\t\t\tif (target != null) {\r\n\t\t\t\t\t\treachIntermediate.add(c.transform(target, false), contextCache);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* This block optimizes the reach operation for intermediate sets which\r\n\t\t\t * trivially indicate a termination state for the overall\r\n\t\t\t * adaptivePredict operation.\r\n\t\t\t *\r\n\t\t\t * The conditions assume that intermediate\r\n\t\t\t * contains all configurations relevant to the reach set, but this\r\n\t\t\t * condition is not true when one or more configurations have been\r\n\t\t\t * withheld in skippedStopStates, or when the current symbol is EOF.\r\n\t\t\t */\r\n\t\t\tif (this.optimize_unique_closure && skippedStopStates == null && t !== Token.EOF && reachIntermediate.uniqueAlt !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\t\treachIntermediate.isOutermostConfigSet = reach.isOutermostConfigSet;\r\n\t\t\t\treach = reachIntermediate;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\t/* If the reach set could not be trivially determined, perform a closure\r\n\t\t\t * operation on the intermediate set to compute its initial value.\r\n\t\t\t */\r\n\t\t\tlet collectPredicates: boolean = false;\r\n\t\t\tlet treatEofAsEpsilon: boolean = t === Token.EOF;\r\n\t\t\tthis.closure(reachIntermediate, reach, collectPredicates, hasMoreContext, contextCache, treatEofAsEpsilon);\r\n\t\t\tstepIntoGlobal = reach.dipsIntoOuterContext;\r\n\r\n\t\t\tif (t === IntStream.EOF) {\r\n\t\t\t\t/* After consuming EOF no additional input is possible, so we are\r\n\t\t\t\t * only interested in configurations which reached the end of the\r\n\t\t\t\t * decision rule (local context) or end of the start rule (full\r\n\t\t\t\t * context). Update reach to contain only these configurations. This\r\n\t\t\t\t * handles both explicit EOF transitions in the grammar and implicit\r\n\t\t\t\t * EOF transitions following the end of the decision or start rule.\r\n\t\t\t\t *\r\n\t\t\t\t * This is handled before the configurations in skippedStopStates,\r\n\t\t\t\t * because any configurations potentially added from that list are\r\n\t\t\t\t * already guaranteed to meet this condition whether or not it's\r\n\t\t\t\t * required.\r\n\t\t\t\t */\r\n\t\t\t\treach = this.removeAllConfigsNotInRuleStopState(reach, contextCache);\r\n\t\t\t}\r\n\r\n\t\t\t/* If skippedStopStates is not undefined, then it contains at least one\r\n\t\t\t * configuration. For full-context reach operations, these\r\n\t\t\t * configurations reached the end of the start rule, in which case we\r\n\t\t\t * only add them back to reach if no configuration during the current\r\n\t\t\t * closure operation reached such a state. This ensures adaptivePredict\r\n\t\t\t * chooses an alternative matching the longest overall sequence when\r\n\t\t\t * multiple alternatives are viable.\r\n\t\t\t */\r\n\t\t\tif (skippedStopStates != null && (!useContext || !PredictionMode.hasConfigInRuleStopState(reach))) {\r\n\t\t\t\tassert(skippedStopStates.length > 0);\r\n\t\t\t\tfor (let c of skippedStopStates) {\r\n\t\t\t\t\treach.add(c, contextCache);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (useContext && stepIntoGlobal) {\r\n\t\t\t\treach.clear();\r\n\r\n\t\t\t\t// We know remainingGlobalContext is not undefined at this point (why?)\r\n\t\t\t\tremainingGlobalContext = remainingGlobalContext as ParserRuleContext;\r\n\r\n\t\t\t\tremainingGlobalContext = this.skipTailCalls(remainingGlobalContext);\r\n\t\t\t\tlet nextContextElement: number = this.getReturnState(remainingGlobalContext);\r\n\t\t\t\tif (contextElements == null) {\r\n\t\t\t\t\tcontextElements = new IntegerList();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (remainingGlobalContext.isEmpty) {\r\n\t\t\t\t\tremainingGlobalContext = undefined;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tremainingGlobalContext = remainingGlobalContext.parent;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tcontextElements.add(nextContextElement);\r\n\t\t\t\tif (nextContextElement !== PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\t\tfor (let i = 0; i < closureConfigs.length; i++) {\r\n\t\t\t\t\t\tclosureConfigs[i] = closureConfigs[i].appendContext(nextContextElement, contextCache);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} while (useContext && stepIntoGlobal);\r\n\r\n\t\tif (reach.isEmpty) {\r\n\t\t\tthis.setDFAEdge(s, t, ATNSimulator.ERROR);\r\n\t\t\treturn [ATNSimulator.ERROR, remainingGlobalContext];\r\n\t\t}\r\n\r\n\t\tlet result: DFAState = this.addDFAEdge(dfa, s, t, contextElements, reach, contextCache);\r\n\t\treturn [result, remainingGlobalContext];\r\n\t}\r\n\r\n\t/**\r\n\t * Return a configuration set containing only the configurations from\r\n\t * `configs` which are in a {@link RuleStopState}. If all\r\n\t * configurations in `configs` are already in a rule stop state, this\r\n\t * method simply returns `configs`.\r\n\t *\r\n\t * @param configs the configuration set to update\r\n\t * @param contextCache the {@link PredictionContext} cache\r\n\t *\r\n\t * @returns `configs` if all configurations in `configs` are in a\r\n\t * rule stop state, otherwise return a new configuration set containing only\r\n\t * the configurations from `configs` which are in a rule stop state\r\n\t */\r\n\t@NotNull\r\n\tprotected removeAllConfigsNotInRuleStopState(@NotNull configs: ATNConfigSet, contextCache: PredictionContextCache): ATNConfigSet {\r\n\t\tif (PredictionMode.allConfigsInRuleStopStates(configs)) {\r\n\t\t\treturn configs;\r\n\t\t}\r\n\r\n\t\tlet result: ATNConfigSet = new ATNConfigSet();\r\n\t\tfor (let config of configs) {\r\n\t\t\tif (!(config.state instanceof RuleStopState)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tresult.add(config, contextCache);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected computeStartState(\r\n\t\tdfa: DFA,\r\n\t\tglobalContext: ParserRuleContext,\r\n\t\tuseContext: boolean): SimulatorState {\r\n\t\tlet s0: DFAState | undefined =\r\n\t\t\tdfa.isPrecedenceDfa ? dfa.getPrecedenceStartState(this._parser.precedence, useContext) :\r\n\t\t\t\tuseContext ? dfa.s0full :\r\n\t\t\t\t\tdfa.s0;\r\n\r\n\t\tif (s0 != null) {\r\n\t\t\tif (!useContext) {\r\n\t\t\t\treturn new SimulatorState(globalContext, s0, useContext, globalContext);\r\n\t\t\t}\r\n\r\n\t\t\ts0.setContextSensitive(this.atn);\r\n\t\t}\r\n\r\n\t\tlet decision: number = dfa.decision;\r\n\t\t// @NotNull\r\n\t\tlet p: ATNState = dfa.atnStartState;\r\n\r\n\t\tlet previousContext: number = 0;\r\n\t\tlet remainingGlobalContext: ParserRuleContext | undefined = globalContext;\r\n\t\tlet initialContext: PredictionContext = useContext ? PredictionContext.EMPTY_FULL : PredictionContext.EMPTY_LOCAL; // always at least the implicit call to start rule\r\n\t\tlet contextCache: PredictionContextCache = new PredictionContextCache();\r\n\t\tif (useContext) {\r\n\t\t\tif (!this.enable_global_context_dfa) {\r\n\t\t\t\twhile (remainingGlobalContext != null) {\r\n\t\t\t\t\tif (remainingGlobalContext.isEmpty) {\r\n\t\t\t\t\t\tpreviousContext = PredictionContext.EMPTY_FULL_STATE_KEY;\r\n\t\t\t\t\t\tremainingGlobalContext = undefined;\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tpreviousContext = this.getReturnState(remainingGlobalContext);\r\n\t\t\t\t\t\tinitialContext = initialContext.appendSingleContext(previousContext, contextCache);\r\n\t\t\t\t\t\tremainingGlobalContext = remainingGlobalContext.parent;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\twhile (s0 != null && s0.isContextSensitive && remainingGlobalContext != null) {\r\n\t\t\t\tlet next: DFAState | undefined;\r\n\t\t\t\tremainingGlobalContext = this.skipTailCalls(remainingGlobalContext);\r\n\t\t\t\tif (remainingGlobalContext.isEmpty) {\r\n\t\t\t\t\tnext = s0.getContextTarget(PredictionContext.EMPTY_FULL_STATE_KEY);\r\n\t\t\t\t\tpreviousContext = PredictionContext.EMPTY_FULL_STATE_KEY;\r\n\t\t\t\t\tremainingGlobalContext = undefined;\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tpreviousContext = this.getReturnState(remainingGlobalContext);\r\n\t\t\t\t\tnext = s0.getContextTarget(previousContext);\r\n\t\t\t\t\tinitialContext = initialContext.appendSingleContext(previousContext, contextCache);\r\n\t\t\t\t\tremainingGlobalContext = remainingGlobalContext.parent;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (next == null) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ts0 = next;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (s0 != null && !s0.isContextSensitive) {\r\n\t\t\treturn new SimulatorState(globalContext, s0, useContext, remainingGlobalContext);\r\n\t\t}\r\n\r\n\t\tlet configs: ATNConfigSet = new ATNConfigSet();\r\n\t\twhile (true) {\r\n\t\t\tlet reachIntermediate: ATNConfigSet = new ATNConfigSet();\r\n\t\t\tlet n: number = p.numberOfTransitions;\r\n\t\t\tfor (let ti = 0; ti < n; ti++) {\r\n\t\t\t\t// for each transition\r\n\t\t\t\tlet target: ATNState = p.transition(ti).target;\r\n\t\t\t\treachIntermediate.add(ATNConfig.create(target, ti + 1, initialContext));\r\n\t\t\t}\r\n\r\n\t\t\tlet hasMoreContext: boolean = remainingGlobalContext != null;\r\n\t\t\tif (!hasMoreContext) {\r\n\t\t\t\tconfigs.isOutermostConfigSet = true;\r\n\t\t\t}\r\n\r\n\t\t\tlet collectPredicates: boolean = true;\r\n\t\t\tthis.closure(reachIntermediate, configs, collectPredicates, hasMoreContext, contextCache, false);\r\n\t\t\tlet stepIntoGlobal: boolean = configs.dipsIntoOuterContext;\r\n\r\n\t\t\tlet next: DFAState;\r\n\t\t\tif (useContext && !this.enable_global_context_dfa) {\r\n\t\t\t\ts0 = this.addDFAState(dfa, configs, contextCache);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\telse if (s0 == null) {\r\n\t\t\t\tif (!dfa.isPrecedenceDfa) {\r\n\t\t\t\t\tnext = this.addDFAState(dfa, configs, contextCache);\r\n\t\t\t\t\tif (useContext) {\r\n\t\t\t\t\t\tif (!dfa.s0full) {\r\n\t\t\t\t\t\t\tdfa.s0full = next;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tnext = dfa.s0full;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (!dfa.s0) {\r\n\t\t\t\t\t\t\tdfa.s0 = next;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tnext = dfa.s0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\t/* If this is a precedence DFA, we use applyPrecedenceFilter\r\n\t\t\t\t\t * to convert the computed start state to a precedence start\r\n\t\t\t\t\t * state. We then use DFA.setPrecedenceStartState to set the\r\n\t\t\t\t\t * appropriate start state for the precedence level rather\r\n\t\t\t\t\t * than simply setting DFA.s0.\r\n\t\t\t\t\t */\r\n\t\t\t\t\tconfigs = this.applyPrecedenceFilter(configs, globalContext, contextCache);\r\n\t\t\t\t\tnext = this.addDFAState(dfa, configs, contextCache);\r\n\t\t\t\t\tdfa.setPrecedenceStartState(this._parser.precedence, useContext, next);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tif (dfa.isPrecedenceDfa) {\r\n\t\t\t\t\tconfigs = this.applyPrecedenceFilter(configs, globalContext, contextCache);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnext = this.addDFAState(dfa, configs, contextCache);\r\n\t\t\t\ts0.setContextTarget(previousContext, next);\r\n\t\t\t}\r\n\r\n\t\t\ts0 = next;\r\n\r\n\t\t\tif (!useContext || !stepIntoGlobal) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\t// TODO: make sure it distinguishes empty stack states\r\n\t\t\tnext.setContextSensitive(this.atn);\r\n\r\n\t\t\t// We know remainingGlobalContext is not undefined at this point (why?)\r\n\t\t\tremainingGlobalContext = remainingGlobalContext as ParserRuleContext;\r\n\r\n\t\t\tconfigs.clear();\r\n\t\t\tremainingGlobalContext = this.skipTailCalls(remainingGlobalContext);\r\n\t\t\tlet nextContextElement: number = this.getReturnState(remainingGlobalContext);\r\n\r\n\t\t\tif (remainingGlobalContext.isEmpty) {\r\n\t\t\t\tremainingGlobalContext = undefined;\r\n\t\t\t} else {\r\n\t\t\t\tremainingGlobalContext = remainingGlobalContext.parent;\r\n\t\t\t}\r\n\r\n\t\t\tif (nextContextElement !== PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\tinitialContext = initialContext.appendSingleContext(nextContextElement, contextCache);\r\n\t\t\t}\r\n\r\n\t\t\tpreviousContext = nextContextElement;\r\n\t\t}\r\n\r\n\t\treturn new SimulatorState(globalContext, s0, useContext, remainingGlobalContext);\r\n\t}\r\n\r\n\t/**\r\n\t * This method transforms the start state computed by\r\n\t * {@link #computeStartState} to the special start state used by a\r\n\t * precedence DFA for a particular precedence value. The transformation\r\n\t * process applies the following changes to the start state's configuration\r\n\t * set.\r\n\t *\r\n\t * 1. Evaluate the precedence predicates for each configuration using\r\n\t *    {@link SemanticContext#evalPrecedence}.\r\n\t * 1. When {@link ATNConfig#isPrecedenceFilterSuppressed} is `false`,\r\n\t *    remove all configurations which predict an alternative greater than 1,\r\n\t *    for which another configuration that predicts alternative 1 is in the\r\n\t *    same ATN state with the same prediction context. This transformation is\r\n\t *    valid for the following reasons:\r\n\t *\r\n\t *     * The closure block cannot contain any epsilon transitions which bypass\r\n\t *       the body of the closure, so all states reachable via alternative 1 are\r\n\t *       part of the precedence alternatives of the transformed left-recursive\r\n\t *       rule.\r\n\t *     * The \"primary\" portion of a left recursive rule cannot contain an\r\n\t *       epsilon transition, so the only way an alternative other than 1 can exist\r\n\t *       in a state that is also reachable via alternative 1 is by nesting calls\r\n\t *       to the left-recursive rule, with the outer calls not being at the\r\n\t *       preferred precedence level. The\r\n\t *       {@link ATNConfig#isPrecedenceFilterSuppressed} property marks ATN\r\n\t *       configurations which do not meet this condition, and therefore are not\r\n\t *       eligible for elimination during the filtering process.\r\n\t *\r\n\t * The prediction context must be considered by this filter to address\r\n\t * situations like the following.\r\n\t *\r\n\t * ```antlr\r\n\t * grammar TA;\r\n\t * prog: statement* EOF;\r\n\t * statement: letterA | statement letterA 'b' ;\r\n\t * letterA: 'a';\r\n\t * ```\r\n\t *\r\n\t * If the above grammar, the ATN state immediately before the token\r\n\t * reference `'a'` in `letterA` is reachable from the left edge\r\n\t * of both the primary and closure blocks of the left-recursive rule\r\n\t * `statement`. The prediction context associated with each of these\r\n\t * configurations distinguishes between them, and prevents the alternative\r\n\t * which stepped out to `prog` (and then back in to `statement`\r\n\t * from being eliminated by the filter.\r\n\t *\r\n\t * @param configs The configuration set computed by\r\n\t * {@link #computeStartState} as the start state for the DFA.\r\n\t * @returns The transformed configuration set representing the start state\r\n\t * for a precedence DFA at a particular precedence level (determined by\r\n\t * calling {@link Parser#getPrecedence}).\r\n\t */\r\n\t@NotNull\r\n\tprotected applyPrecedenceFilter(@NotNull configs: ATNConfigSet, globalContext: ParserRuleContext, contextCache: PredictionContextCache): ATNConfigSet {\r\n\t\tlet statesFromAlt1: Map<number, PredictionContext> = new Map<number, PredictionContext>();\r\n\t\tlet configSet: ATNConfigSet = new ATNConfigSet();\r\n\t\tfor (let config of configs) {\r\n\t\t\t// handle alt 1 first\r\n\t\t\tif (config.alt !== 1) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet updatedContext: SemanticContext | undefined = config.semanticContext.evalPrecedence(this._parser, globalContext);\r\n\t\t\tif (updatedContext == null) {\r\n\t\t\t\t// the configuration was eliminated\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tstatesFromAlt1.set(config.state.stateNumber, config.context);\r\n\t\t\tif (updatedContext !== config.semanticContext) {\r\n\t\t\t\tconfigSet.add(config.transform(config.state, false, updatedContext), contextCache);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tconfigSet.add(config, contextCache);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfor (let config of configs) {\r\n\t\t\tif (config.alt === 1) {\r\n\t\t\t\t// already handled\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (!config.isPrecedenceFilterSuppressed) {\r\n\t\t\t\t/* In the future, this elimination step could be updated to also\r\n\t\t\t\t * filter the prediction context for alternatives predicting alt>1\r\n\t\t\t\t * (basically a graph subtraction algorithm).\r\n\t\t\t\t */\r\n\t\t\t\tlet context: PredictionContext | undefined = statesFromAlt1.get(config.state.stateNumber);\r\n\t\t\t\tif (context != null && context.equals(config.context)) {\r\n\t\t\t\t\t// eliminated\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tconfigSet.add(config, contextCache);\r\n\t\t}\r\n\r\n\t\treturn configSet;\r\n\t}\r\n\r\n\tprotected getReachableTarget(@NotNull source: ATNConfig, @NotNull trans: Transition, ttype: number): ATNState | undefined {\r\n\t\tif (trans.matches(ttype, 0, this.atn.maxTokenType)) {\r\n\t\t\treturn trans.target;\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/** collect and set D's semantic context */\r\n\tprotected predicateDFAState(\r\n\t\tD: DFAState,\r\n\t\tconfigs: ATNConfigSet,\r\n\t\tnalts: number): DFAState.PredPrediction[] | undefined {\r\n\t\tlet conflictingAlts: BitSet | undefined = this.getConflictingAltsFromConfigSet(configs);\r\n\t\tif (!conflictingAlts) {\r\n\t\t\tthrow new Error(\"This unhandled scenario is intended to be unreachable, but I'm currently not sure of why we know that's the case.\");\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"predicateDFAState \" + D);\r\n\t\t}\r\n\t\tlet altToPred: SemanticContext[] | undefined = this.getPredsForAmbigAlts(conflictingAlts, configs, nalts);\r\n\t\t// altToPred[uniqueAlt] is now our validating predicate (if any)\r\n\t\tlet predPredictions: DFAState.PredPrediction[] | undefined;\r\n\t\tif (altToPred != null) {\r\n\t\t\t// we have a validating predicate; test it\r\n\t\t\t// Update DFA so reach becomes accept state with predicate\r\n\t\t\tpredPredictions = this.getPredicatePredictions(conflictingAlts, altToPred);\r\n\t\t\tD.predicates = predPredictions;\r\n\t\t}\r\n\t\treturn predPredictions;\r\n\t}\r\n\r\n\tprotected getPredsForAmbigAlts(\r\n\t\t@NotNull ambigAlts: BitSet,\r\n\t\t@NotNull configs: ATNConfigSet,\r\n\t\tnalts: number): SemanticContext[] | undefined {\r\n\t\t// REACH=[1|1|[]|0:0, 1|2|[]|0:1]\r\n\r\n\t\t/* altToPred starts as an array of all undefined contexts. The entry at index i\r\n\t\t * corresponds to alternative i. altToPred[i] may have one of three values:\r\n\t\t *   1. undefined: no ATNConfig c is found such that c.alt===i\r\n\t\t *   2. SemanticContext.NONE: At least one ATNConfig c exists such that\r\n\t\t *      c.alt===i and c.semanticContext===SemanticContext.NONE. In other words,\r\n\t\t *      alt i has at least one unpredicated config.\r\n\t\t *   3. Non-NONE Semantic Context: There exists at least one, and for all\r\n\t\t *      ATNConfig c such that c.alt===i, c.semanticContext!==SemanticContext.NONE.\r\n\t\t *\r\n\t\t * From this, it is clear that NONE||anything==NONE.\r\n\t\t */\r\n\t\tlet altToPred: Array<SemanticContext | undefined> | undefined = new Array<SemanticContext>(nalts + 1);\r\n\t\tlet n: number = altToPred.length;\r\n\t\tfor (let c of configs) {\r\n\t\t\tif (ambigAlts.get(c.alt)) {\r\n\t\t\t\taltToPred[c.alt] = SemanticContext.or(altToPred[c.alt], c.semanticContext);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet nPredAlts: number = 0;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tif (altToPred[i] == null) {\r\n\t\t\t\taltToPred[i] = SemanticContext.NONE;\r\n\t\t\t}\r\n\t\t\telse if (altToPred[i] !== SemanticContext.NONE) {\r\n\t\t\t\tnPredAlts++;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// At this point we know `altToPred` doesn't contain any undefined entries\r\n\t\tlet result: SemanticContext[] | undefined = altToPred as SemanticContext[];\r\n\r\n\t\t// nonambig alts are undefined in result\r\n\t\tif (nPredAlts === 0) {\r\n\t\t\tresult = undefined;\r\n\t\t}\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"getPredsForAmbigAlts result \" + (result ? Arrays.toString(result) : \"undefined\"));\r\n\t\t}\r\n\t\treturn result;\r\n\t}\r\n\r\n\tprotected getPredicatePredictions(ambigAlts: BitSet | undefined, altToPred: SemanticContext[]): DFAState.PredPrediction[] | undefined {\r\n\t\tlet pairs: DFAState.PredPrediction[] = [];\r\n\t\tlet containsPredicate: boolean = false;\r\n\t\tfor (let i = 1; i < altToPred.length; i++) {\r\n\t\t\tlet pred: SemanticContext = altToPred[i];\r\n\r\n\t\t\t// unpredicated is indicated by SemanticContext.NONE\r\n\t\t\tassert(pred != null);\r\n\r\n\t\t\t// find first unpredicated but ambig alternative, if any.\r\n\t\t\t// Only ambiguous alternatives will have SemanticContext.NONE.\r\n\t\t\t// Any unambig alts or ambig naked alts after first ambig naked are ignored\r\n\t\t\t// (undefined, i) means alt i is the default prediction\r\n\t\t\t// if no (undefined, i), then no default prediction.\r\n\t\t\tif (ambigAlts != null && ambigAlts.get(i) && pred === SemanticContext.NONE) {\r\n\t\t\t\tpairs.push(new DFAState.PredPrediction(pred, i));\r\n\t\t\t}\r\n\t\t\telse if (pred !== SemanticContext.NONE) {\r\n\t\t\t\tcontainsPredicate = true;\r\n\t\t\t\tpairs.push(new DFAState.PredPrediction(pred, i));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!containsPredicate) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n//\t\tSystem.out.println(Arrays.toString(altToPred)+\"->\"+pairs);\r\n\t\treturn pairs;\r\n\t}\r\n\r\n\t/** Look through a list of predicate/alt pairs, returning alts for the\r\n\t *  pairs that win. An `undefined` predicate indicates an alt containing an\r\n\t *  unpredicated config which behaves as \"always true.\"\r\n\t */\r\n\tprotected evalSemanticContext(\r\n\t\t@NotNull predPredictions: DFAState.PredPrediction[],\r\n\t\touterContext: ParserRuleContext,\r\n\t\tcomplete: boolean): BitSet {\r\n\t\tlet predictions: BitSet = new BitSet();\r\n\t\tfor (let pair of predPredictions) {\r\n\t\t\tif (pair.pred === SemanticContext.NONE) {\r\n\t\t\t\tpredictions.set(pair.alt);\r\n\t\t\t\tif (!complete) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet evaluatedResult: boolean = this.evalSemanticContextImpl(pair.pred, outerContext, pair.alt);\r\n\t\t\tif (ParserATNSimulator.debug || ParserATNSimulator.dfa_debug) {\r\n\t\t\t\tconsole.log(\"eval pred \" + pair + \"=\" + evaluatedResult);\r\n\t\t\t}\r\n\r\n\t\t\tif (evaluatedResult) {\r\n\t\t\t\tif (ParserATNSimulator.debug || ParserATNSimulator.dfa_debug) {\r\n\t\t\t\t\tconsole.log(\"PREDICT \" + pair.alt);\r\n\t\t\t\t}\r\n\t\t\t\tpredictions.set(pair.alt);\r\n\t\t\t\tif (!complete) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn predictions;\r\n\t}\r\n\r\n\t/**\r\n\t * Evaluate a semantic context within a specific parser context.\r\n\t *\r\n\t * This method might not be called for every semantic context evaluated\r\n\t * during the prediction process. In particular, we currently do not\r\n\t * evaluate the following but it may change in the future:\r\n\t *\r\n\t * * Precedence predicates (represented by\r\n\t *   {@link SemanticContext.PrecedencePredicate}) are not currently evaluated\r\n\t *   through this method.\r\n\t * * Operator predicates (represented by {@link SemanticContext.AND} and\r\n\t *   {@link SemanticContext.OR}) are evaluated as a single semantic\r\n\t *   context, rather than evaluating the operands individually.\r\n\t *   Implementations which require evaluation results from individual\r\n\t *   predicates should override this method to explicitly handle evaluation of\r\n\t *   the operands within operator predicates.\r\n\t *\r\n\t * @param pred The semantic context to evaluate\r\n\t * @param parserCallStack The parser context in which to evaluate the\r\n\t * semantic context\r\n\t * @param alt The alternative which is guarded by `pred`\r\n\t *\r\n\t * @since 4.3\r\n\t */\r\n\tprotected evalSemanticContextImpl(@NotNull pred: SemanticContext, parserCallStack: ParserRuleContext, alt: number): boolean {\r\n\t\treturn pred.eval(this._parser, parserCallStack);\r\n\t}\r\n\r\n\t/* TODO: If we are doing predicates, there is no point in pursuing\r\n\t\t closure operations if we reach a DFA state that uniquely predicts\r\n\t\t alternative. We will not be caching that DFA state and it is a\r\n\t\t waste to pursue the closure. Might have to advance when we do\r\n\t\t ambig detection thought :(\r\n\t\t  */\r\n\r\n\tprotected closure(\r\n\t\tsourceConfigs: ATNConfigSet,\r\n\t\t@NotNull configs: ATNConfigSet,\r\n\t\tcollectPredicates: boolean,\r\n\t\thasMoreContext: boolean,\r\n\t\t@Nullable contextCache: PredictionContextCache,\r\n\t\ttreatEofAsEpsilon: boolean): void {\r\n\t\tif (contextCache == null) {\r\n\t\t\tcontextCache = PredictionContextCache.UNCACHED;\r\n\t\t}\r\n\r\n\t\tlet currentConfigs: ATNConfigSet = sourceConfigs;\r\n\t\tlet closureBusy: Array2DHashSet<ATNConfig> = new Array2DHashSet<ATNConfig>(ObjectEqualityComparator.INSTANCE);\r\n\t\twhile (currentConfigs.size > 0) {\r\n\t\t\tlet intermediate: ATNConfigSet = new ATNConfigSet();\r\n\t\t\tfor (let config of currentConfigs) {\r\n\t\t\t\tthis.closureImpl(config, configs, intermediate, closureBusy, collectPredicates, hasMoreContext, contextCache, 0, treatEofAsEpsilon);\r\n\t\t\t}\r\n\r\n\t\t\tcurrentConfigs = intermediate;\r\n\t\t}\r\n\t}\r\n\r\n\tprotected closureImpl(\r\n\t\t@NotNull config: ATNConfig,\r\n\t\t@NotNull configs: ATNConfigSet,\r\n\t\t@Nullable intermediate: ATNConfigSet,\r\n\t\t@NotNull closureBusy: Array2DHashSet<ATNConfig>,\r\n\t\tcollectPredicates: boolean,\r\n\t\thasMoreContexts: boolean,\r\n\t\t@NotNull contextCache: PredictionContextCache,\r\n\t\tdepth: number,\r\n\t\ttreatEofAsEpsilon: boolean): void {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"closure(\" + config.toString(this._parser, true) + \")\");\r\n\t\t}\r\n\r\n\t\tif (config.state instanceof RuleStopState) {\r\n\t\t\t// We hit rule end. If we have context info, use it\r\n\t\t\tif (!config.context.isEmpty) {\r\n\t\t\t\tlet hasEmpty: boolean = config.context.hasEmpty;\r\n\t\t\t\tlet nonEmptySize: number = config.context.size - (hasEmpty ? 1 : 0);\r\n\t\t\t\tfor (let i = 0; i < nonEmptySize; i++) {\r\n\t\t\t\t\tlet newContext: PredictionContext = config.context.getParent(i); // \"pop\" return state\r\n\t\t\t\t\tlet returnState: ATNState = this.atn.states[config.context.getReturnState(i)];\r\n\t\t\t\t\tlet c: ATNConfig = ATNConfig.create(returnState, config.alt, newContext, config.semanticContext);\r\n\t\t\t\t\t// While we have context to pop back from, we may have\r\n\t\t\t\t\t// gotten that context AFTER having fallen off a rule.\r\n\t\t\t\t\t// Make sure we track that we are now out of context.\r\n\t\t\t\t\tc.outerContextDepth = config.outerContextDepth;\r\n\t\t\t\t\tc.isPrecedenceFilterSuppressed = config.isPrecedenceFilterSuppressed;\r\n\t\t\t\t\tassert(depth > MIN_INTEGER_VALUE);\r\n\t\t\t\t\tthis.closureImpl(c, configs, intermediate, closureBusy, collectPredicates, hasMoreContexts, contextCache, depth - 1, treatEofAsEpsilon);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!hasEmpty || !hasMoreContexts) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconfig = config.transform(config.state, false, PredictionContext.EMPTY_LOCAL);\r\n\t\t\t}\r\n\t\t\telse if (!hasMoreContexts) {\r\n\t\t\t\tconfigs.add(config, contextCache);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\t// else if we have no context info, just chase follow links (if greedy)\r\n\t\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\t\tconsole.log(\"FALLING off rule \" +\r\n\t\t\t\t\t\tthis.getRuleName(config.state.ruleIndex));\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (config.context === PredictionContext.EMPTY_FULL) {\r\n\t\t\t\t\t// no need to keep full context overhead when we step out\r\n\t\t\t\t\tconfig = config.transform(config.state, false, PredictionContext.EMPTY_LOCAL);\r\n\t\t\t\t}\r\n\t\t\t\telse if (!config.reachesIntoOuterContext && PredictionContext.isEmptyLocal(config.context)) {\r\n\t\t\t\t\t// add stop state when leaving decision rule for the first time\r\n\t\t\t\t\tconfigs.add(config, contextCache);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet p: ATNState = config.state;\r\n\t\t// optimization\r\n\t\tif (!p.onlyHasEpsilonTransitions) {\r\n\t\t\tconfigs.add(config, contextCache);\r\n\t\t\t// make sure to not return here, because EOF transitions can act as\r\n\t\t\t// both epsilon transitions and non-epsilon transitions.\r\n\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\tconsole.log(\"added config \" + configs);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < p.numberOfOptimizedTransitions; i++) {\r\n\t\t\t// This block implements first-edge elimination of ambiguous LR\r\n\t\t\t// alternatives as part of dynamic disambiguation during prediction.\r\n\t\t\t// See antlr/antlr4#1398.\r\n\t\t\tif (i === 0\r\n\t\t\t\t&& p.stateType === ATNStateType.STAR_LOOP_ENTRY\r\n\t\t\t\t&& (p as StarLoopEntryState).precedenceRuleDecision\r\n\t\t\t\t&& !config.context.hasEmpty) {\r\n\r\n\t\t\t\tlet precedenceDecision = p as StarLoopEntryState;\r\n\r\n\t\t\t\t// When suppress is true, it means the outgoing edge i==0 is\r\n\t\t\t\t// ambiguous with the outgoing edge i==1, and thus the closure\r\n\t\t\t\t// operation can dynamically disambiguate by suppressing this\r\n\t\t\t\t// edge during the closure operation.\r\n\t\t\t\tlet suppress: boolean = true;\r\n\t\t\t\tfor (let j: number = 0; j < config.context.size; j++) {\r\n\t\t\t\t\tif (!precedenceDecision.precedenceLoopbackStates.get(config.context.getReturnState(j))) {\r\n\t\t\t\t\t\tsuppress = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (suppress) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet t: Transition = p.getOptimizedTransition(i);\r\n\t\t\tlet continueCollecting: boolean =\r\n\t\t\t\t!(t instanceof ActionTransition) && collectPredicates;\r\n\t\t\tlet c: ATNConfig | undefined = this.getEpsilonTarget(config, t, continueCollecting, depth === 0, contextCache, treatEofAsEpsilon);\r\n\t\t\tif (c != null) {\r\n\t\t\t\tif (t instanceof RuleTransition) {\r\n\t\t\t\t\tif (intermediate != null && !collectPredicates) {\r\n\t\t\t\t\t\tintermediate.add(c, contextCache);\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet newDepth: number = depth;\r\n\t\t\t\tif (config.state instanceof RuleStopState) {\r\n\t\t\t\t\t// target fell off end of rule; mark resulting c as having dipped into outer context\r\n\t\t\t\t\t// We can't get here if incoming config was rule stop and we had context\r\n\t\t\t\t\t// track how far we dip into outer context.  Might\r\n\t\t\t\t\t// come in handy and we avoid evaluating context dependent\r\n\t\t\t\t\t// preds if this is > 0.\r\n\r\n\t\t\t\t\tif (this.dfa != null && this.dfa.isPrecedenceDfa) {\r\n\t\t\t\t\t\tlet outermostPrecedenceReturn: number = (t as EpsilonTransition).outermostPrecedenceReturn;\r\n\t\t\t\t\t\tif (outermostPrecedenceReturn === this.dfa.atnStartState.ruleIndex) {\r\n\t\t\t\t\t\t\tc.isPrecedenceFilterSuppressed = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tc.outerContextDepth = c.outerContextDepth + 1;\r\n\r\n\t\t\t\t\tif (!closureBusy.add(c)) {\r\n\t\t\t\t\t\t// avoid infinite recursion for right-recursive rules\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tassert(newDepth > MIN_INTEGER_VALUE);\r\n\t\t\t\t\tnewDepth--;\r\n\t\t\t\t\tif (ParserATNSimulator.debug) {\r\n\t\t\t\t\t\tconsole.log(\"dips into outer ctx: \" + c);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse if (t instanceof RuleTransition) {\r\n\t\t\t\t\tif (this.optimize_tail_calls && t.optimizedTailCall && (!this.tail_call_preserves_sll || !PredictionContext.isEmptyLocal(config.context))) {\r\n\t\t\t\t\t\tassert(c.context === config.context);\r\n\t\t\t\t\t\tif (newDepth === 0) {\r\n\t\t\t\t\t\t\t// the pop/push of a tail call would keep the depth\r\n\t\t\t\t\t\t\t// constant, except we latch if it goes negative\r\n\t\t\t\t\t\t\tnewDepth--;\r\n\t\t\t\t\t\t\tif (!this.tail_call_preserves_sll && PredictionContext.isEmptyLocal(config.context)) {\r\n\t\t\t\t\t\t\t\t// make sure the SLL config \"dips into the outer context\" or prediction may not fall back to LL on conflict\r\n\t\t\t\t\t\t\t\tc.outerContextDepth = c.outerContextDepth + 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\t// latch when newDepth goes negative - once we step out of the entry context we can't return\r\n\t\t\t\t\t\tif (newDepth >= 0) {\r\n\t\t\t\t\t\t\tnewDepth++;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tif (!t.isEpsilon && !closureBusy.add(c)) {\r\n\t\t\t\t\t\t// avoid infinite recursion for EOF* and EOF+\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.closureImpl(c, configs, intermediate, closureBusy, continueCollecting, hasMoreContexts, contextCache, newDepth, treatEofAsEpsilon);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getRuleName(index: number): string {\r\n\t\tif (this._parser != null && index >= 0) {\r\n\t\t\treturn this._parser.ruleNames[index];\r\n\t\t}\r\n\t\treturn \"<rule \" + index + \">\";\r\n\t}\r\n\r\n\tprotected getEpsilonTarget(@NotNull config: ATNConfig, @NotNull t: Transition, collectPredicates: boolean, inContext: boolean, contextCache: PredictionContextCache, treatEofAsEpsilon: boolean): ATNConfig | undefined {\r\n\t\tswitch (t.serializationType) {\r\n\t\tcase TransitionType.RULE:\r\n\t\t\treturn this.ruleTransition(config, t as RuleTransition, contextCache);\r\n\r\n\t\tcase TransitionType.PRECEDENCE:\r\n\t\t\treturn this.precedenceTransition(config, t as PrecedencePredicateTransition, collectPredicates, inContext);\r\n\r\n\t\tcase TransitionType.PREDICATE:\r\n\t\t\treturn this.predTransition(config, t as PredicateTransition, collectPredicates, inContext);\r\n\r\n\t\tcase TransitionType.ACTION:\r\n\t\t\treturn this.actionTransition(config, t as ActionTransition);\r\n\r\n\t\tcase TransitionType.EPSILON:\r\n\t\t\treturn config.transform(t.target, false);\r\n\r\n\t\tcase TransitionType.ATOM:\r\n\t\tcase TransitionType.RANGE:\r\n\t\tcase TransitionType.SET:\r\n\t\t\t// EOF transitions act like epsilon transitions after the first EOF\r\n\t\t\t// transition is traversed\r\n\t\t\tif (treatEofAsEpsilon) {\r\n\t\t\t\tif (t.matches(Token.EOF, 0, 1)) {\r\n\t\t\t\t\treturn config.transform(t.target, false);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn undefined;\r\n\r\n\t\tdefault:\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected actionTransition(@NotNull config: ATNConfig, @NotNull t: ActionTransition): ATNConfig {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"ACTION edge \" + t.ruleIndex + \":\" + t.actionIndex);\r\n\t\t}\r\n\t\treturn config.transform(t.target, false);\r\n\t}\r\n\r\n\t@Nullable\r\n\tprotected precedenceTransition(\r\n\t\t@NotNull config: ATNConfig,\r\n\t\t@NotNull pt: PrecedencePredicateTransition,\r\n\t\tcollectPredicates: boolean,\r\n\t\tinContext: boolean): ATNConfig {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"PRED (collectPredicates=\" + collectPredicates + \") \" +\r\n\t\t\t\tpt.precedence + \">=_p\" +\r\n\t\t\t\t\", ctx dependent=true\");\r\n\t\t\tif (this._parser != null) {\r\n\t\t\t\tconsole.log(\"context surrounding pred is \" +\r\n\t\t\t\t\tthis._parser.getRuleInvocationStack());\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet c: ATNConfig;\r\n\t\tif (collectPredicates && inContext) {\r\n\t\t\tlet newSemCtx: SemanticContext = SemanticContext.and(config.semanticContext, pt.predicate);\r\n\t\t\tc = config.transform(pt.target, false, newSemCtx);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tc = config.transform(pt.target, false);\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"config from pred transition=\" + c);\r\n\t\t}\r\n\t\treturn c;\r\n\t}\r\n\r\n\t@Nullable\r\n\tprotected predTransition(\r\n\t\t@NotNull config: ATNConfig,\r\n\t\t@NotNull pt: PredicateTransition,\r\n\t\tcollectPredicates: boolean,\r\n\t\tinContext: boolean): ATNConfig {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"PRED (collectPredicates=\" + collectPredicates + \") \" +\r\n\t\t\t\tpt.ruleIndex + \":\" + pt.predIndex +\r\n\t\t\t\t\", ctx dependent=\" + pt.isCtxDependent);\r\n\t\t\tif (this._parser != null) {\r\n\t\t\t\tconsole.log(\"context surrounding pred is \" +\r\n\t\t\t\t\tthis._parser.getRuleInvocationStack());\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet c: ATNConfig;\r\n\t\tif (collectPredicates &&\r\n\t\t\t(!pt.isCtxDependent || (pt.isCtxDependent && inContext))) {\r\n\t\t\tlet newSemCtx: SemanticContext = SemanticContext.and(config.semanticContext, pt.predicate);\r\n\t\t\tc = config.transform(pt.target, false, newSemCtx);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tc = config.transform(pt.target, false);\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"config from pred transition=\" + c);\r\n\t\t}\r\n\t\treturn c;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected ruleTransition(@NotNull config: ATNConfig, @NotNull t: RuleTransition, @Nullable contextCache: PredictionContextCache): ATNConfig {\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"CALL rule \" + this.getRuleName(t.target.ruleIndex) +\r\n\t\t\t\t\", ctx=\" + config.context);\r\n\t\t}\r\n\r\n\t\tlet returnState: ATNState = t.followState;\r\n\t\tlet newContext: PredictionContext;\r\n\r\n\t\tif (this.optimize_tail_calls && t.optimizedTailCall && (!this.tail_call_preserves_sll || !PredictionContext.isEmptyLocal(config.context))) {\r\n\t\t\tnewContext = config.context;\r\n\t\t}\r\n\t\telse if (contextCache != null) {\r\n\t\t\tnewContext = contextCache.getChild(config.context, returnState.stateNumber);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tnewContext = config.context.getChild(returnState.stateNumber);\r\n\t\t}\r\n\r\n\t\treturn config.transform(t.target, false, newContext);\r\n\t}\r\n\r\n\tprivate static STATE_ALT_SORT_COMPARATOR: (o1: ATNConfig, o2: ATNConfig) => number =\r\n\t\t(o1: ATNConfig, o2: ATNConfig): number => {\r\n\t\t\tlet diff: number = o1.state.nonStopStateNumber - o2.state.nonStopStateNumber;\r\n\t\t\tif (diff !== 0) {\r\n\t\t\t\treturn diff;\r\n\t\t\t}\r\n\r\n\t\t\tdiff = o1.alt - o2.alt;\r\n\t\t\tif (diff !== 0) {\r\n\t\t\t\treturn diff;\r\n\t\t\t}\r\n\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\tprivate isConflicted(@NotNull configset: ATNConfigSet, contextCache: PredictionContextCache): ConflictInfo | undefined {\r\n\t\tif (configset.uniqueAlt !== ATN.INVALID_ALT_NUMBER || configset.size <= 1) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet configs: ATNConfig[] = configset.toArray();\r\n\t\tconfigs.sort(ParserATNSimulator.STATE_ALT_SORT_COMPARATOR);\r\n\r\n\t\tlet exact: boolean = !configset.dipsIntoOuterContext;\r\n\t\tlet alts: BitSet = new BitSet();\r\n\t\tlet minAlt: number = configs[0].alt;\r\n\t\talts.set(minAlt);\r\n\r\n\t\t/* Quick checks come first (single pass, no context joining):\r\n\t\t *  1. Make sure first config in the sorted list predicts the minimum\r\n\t\t *     represented alternative.\r\n\t\t *  2. Make sure every represented state has at least one configuration\r\n\t\t *     which predicts the minimum represented alternative.\r\n\t\t *  3. (exact only) make sure every represented state has at least one\r\n\t\t *     configuration which predicts each represented alternative.\r\n\t\t */\r\n\r\n\t\t// quick check 1 & 2 => if we assume #1 holds and check #2 against the\r\n\t\t// minAlt from the first state, #2 will fail if the assumption was\r\n\t\t// incorrect\r\n\t\tlet currentState: number = configs[0].state.nonStopStateNumber;\r\n\t\tfor (let config of configs) {\r\n\t\t\tlet stateNumber: number = config.state.nonStopStateNumber;\r\n\t\t\tif (stateNumber !== currentState) {\r\n\t\t\t\tif (config.alt !== minAlt) {\r\n\t\t\t\t\treturn undefined;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tcurrentState = stateNumber;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet representedAlts: BitSet;\r\n\t\tif (exact) {\r\n\t\t\tcurrentState = configs[0].state.nonStopStateNumber;\r\n\r\n\t\t\t// get the represented alternatives of the first state\r\n\t\t\trepresentedAlts = new BitSet();\r\n\t\t\tlet maxAlt: number = minAlt;\r\n\t\t\tfor (let config of configs) {\r\n\t\t\t\tif (config.state.nonStopStateNumber !== currentState) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet alt: number = config.alt;\r\n\t\t\t\trepresentedAlts.set(alt);\r\n\t\t\t\tmaxAlt = alt;\r\n\t\t\t}\r\n\r\n\t\t\t// quick check #3:\r\n\t\t\tcurrentState = configs[0].state.nonStopStateNumber;\r\n\t\t\tlet currentAlt: number = minAlt;\r\n\t\t\tfor (let config of configs) {\r\n\t\t\t\tlet stateNumber: number = config.state.nonStopStateNumber;\r\n\t\t\t\tlet alt: number = config.alt;\r\n\t\t\t\tif (stateNumber !== currentState) {\r\n\t\t\t\t\tif (currentAlt !== maxAlt) {\r\n\t\t\t\t\t\texact = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcurrentState = stateNumber;\r\n\t\t\t\t\tcurrentAlt = minAlt;\r\n\t\t\t\t}\r\n\t\t\t\telse if (alt !== currentAlt) {\r\n\t\t\t\t\tif (alt !== representedAlts.nextSetBit(currentAlt + 1)) {\r\n\t\t\t\t\t\texact = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcurrentAlt = alt;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tcurrentState = configs[0].state.nonStopStateNumber;\r\n\t\tlet firstIndexCurrentState: number = 0;\r\n\t\tlet lastIndexCurrentStateMinAlt: number = 0;\r\n\t\tlet joinedCheckContext: PredictionContext = configs[0].context;\r\n\t\tfor (let i = 1; i < configs.length; i++) {\r\n\t\t\tlet config: ATNConfig = configs[i];\r\n\t\t\tif (config.alt !== minAlt) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tif (config.state.nonStopStateNumber !== currentState) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tlastIndexCurrentStateMinAlt = i;\r\n\t\t\tjoinedCheckContext = contextCache.join(joinedCheckContext, configs[i].context);\r\n\t\t}\r\n\r\n\t\tfor (let i = lastIndexCurrentStateMinAlt + 1; i < configs.length; i++) {\r\n\t\t\tlet config: ATNConfig = configs[i];\r\n\t\t\tlet state: ATNState = config.state;\r\n\t\t\talts.set(config.alt);\r\n\t\t\tif (state.nonStopStateNumber !== currentState) {\r\n\t\t\t\tcurrentState = state.nonStopStateNumber;\r\n\t\t\t\tfirstIndexCurrentState = i;\r\n\t\t\t\tlastIndexCurrentStateMinAlt = i;\r\n\t\t\t\tjoinedCheckContext = config.context;\r\n\t\t\t\tfor (let j = firstIndexCurrentState + 1; j < configs.length; j++) {\r\n\t\t\t\t\tlet config2: ATNConfig = configs[j];\r\n\t\t\t\t\tif (config2.alt !== minAlt) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (config2.state.nonStopStateNumber !== currentState) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlastIndexCurrentStateMinAlt = j;\r\n\t\t\t\t\tjoinedCheckContext = contextCache.join(joinedCheckContext, config2.context);\r\n\t\t\t\t}\r\n\r\n\t\t\t\ti = lastIndexCurrentStateMinAlt;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet joinedCheckContext2: PredictionContext = config.context;\r\n\t\t\tlet currentAlt: number = config.alt;\r\n\t\t\tlet lastIndexCurrentStateCurrentAlt: number = i;\r\n\t\t\tfor (let j = lastIndexCurrentStateCurrentAlt + 1; j < configs.length; j++) {\r\n\t\t\t\tlet config2: ATNConfig = configs[j];\r\n\t\t\t\tif (config2.alt !== currentAlt) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (config2.state.nonStopStateNumber !== currentState) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlastIndexCurrentStateCurrentAlt = j;\r\n\t\t\t\tjoinedCheckContext2 = contextCache.join(joinedCheckContext2, config2.context);\r\n\t\t\t}\r\n\r\n\t\t\ti = lastIndexCurrentStateCurrentAlt;\r\n\r\n\t\t\tlet check: PredictionContext = contextCache.join(joinedCheckContext, joinedCheckContext2);\r\n\t\t\tif (!joinedCheckContext.equals(check)) {\r\n\t\t\t\treturn undefined;\r\n\t\t\t}\r\n\r\n\t\t\t// update exact if necessary\r\n\t\t\texact = exact && joinedCheckContext.equals(joinedCheckContext2);\r\n\t\t}\r\n\r\n\t\treturn new ConflictInfo(alts, exact);\r\n\t}\r\n\r\n\tprotected getConflictingAltsFromConfigSet(configs: ATNConfigSet): BitSet | undefined {\r\n\t\tlet conflictingAlts: BitSet | undefined = configs.conflictingAlts;\r\n\t\tif (conflictingAlts == null && configs.uniqueAlt !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\tconflictingAlts = new BitSet();\r\n\t\t\tconflictingAlts.set(configs.uniqueAlt);\r\n\t\t}\r\n\r\n\t\treturn conflictingAlts;\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getTokenName(t: number): string {\r\n\t\tif (t === Token.EOF) {\r\n\t\t\treturn \"EOF\";\r\n\t\t}\r\n\r\n\t\tlet vocabulary: Vocabulary = this._parser != null ? this._parser.vocabulary : VocabularyImpl.EMPTY_VOCABULARY;\r\n\t\tlet displayName: string = vocabulary.getDisplayName(t);\r\n\t\tif (displayName === String(t)) {\r\n\t\t\treturn displayName;\r\n\t\t}\r\n\r\n\t\treturn displayName + \"<\" + t + \">\";\r\n\t}\r\n\r\n\tpublic getLookaheadName(input: TokenStream): string {\r\n\t\treturn this.getTokenName(input.LA(1));\r\n\t}\r\n\r\n\tpublic dumpDeadEndConfigs(@NotNull nvae: NoViableAltException): void {\r\n\t\tconsole.log(\"dead end configs: \");\r\n\t\tlet deadEndConfigs = nvae.deadEndConfigs;\r\n\t\tif (!deadEndConfigs) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tfor (let c of deadEndConfigs) {\r\n\t\t\tlet trans: string = \"no edges\";\r\n\t\t\tif (c.state.numberOfOptimizedTransitions > 0) {\r\n\t\t\t\tlet t: Transition = c.state.getOptimizedTransition(0);\r\n\t\t\t\tif (t instanceof AtomTransition) {\r\n\t\t\t\t\ttrans = \"Atom \" + this.getTokenName(t._label);\r\n\t\t\t\t}\r\n\t\t\t\telse if (t instanceof SetTransition) {\r\n\t\t\t\t\tlet not: boolean = t instanceof NotSetTransition;\r\n\t\t\t\t\ttrans = (not ? \"~\" : \"\") + \"Set \" + t.set.toString();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(c.toString(this._parser, true) + \":\" + trans);\r\n\t\t}\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected noViableAlt(\r\n\t\t@NotNull input: TokenStream,\r\n\t\t@NotNull outerContext: ParserRuleContext,\r\n\t\t@NotNull configs: ATNConfigSet,\r\n\t\tstartIndex: number): NoViableAltException {\r\n\t\treturn new NoViableAltException(this._parser, input,\r\n\t\t\tinput.get(startIndex),\r\n\t\t\tinput.LT(1),\r\n\t\t\tconfigs, outerContext);\r\n\t}\r\n\r\n\tprotected getUniqueAlt(@NotNull configs: Iterable<ATNConfig>): number {\r\n\t\tlet alt: number = ATN.INVALID_ALT_NUMBER;\r\n\t\tfor (let c of configs) {\r\n\t\t\tif (alt === ATN.INVALID_ALT_NUMBER) {\r\n\t\t\t\talt = c.alt; // found first alt\r\n\t\t\t}\r\n\t\t\telse if (c.alt !== alt) {\r\n\t\t\t\treturn ATN.INVALID_ALT_NUMBER;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn alt;\r\n\t}\r\n\r\n\tprotected configWithAltAtStopState(@NotNull configs: Iterable<ATNConfig>, alt: number): boolean {\r\n\t\tfor (let c of configs) {\r\n\t\t\tif (c.alt === alt) {\r\n\t\t\t\tif (c.state instanceof RuleStopState) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected addDFAEdge(\r\n\t\t@NotNull dfa: DFA,\r\n\t\t@NotNull fromState: DFAState,\r\n\t\tt: number,\r\n\t\tcontextTransitions: IntegerList | undefined,\r\n\t\t@NotNull toConfigs: ATNConfigSet,\r\n\t\tcontextCache: PredictionContextCache): DFAState {\r\n\t\tassert(contextTransitions == null || contextTransitions.isEmpty || dfa.isContextSensitive);\r\n\r\n\t\tlet from: DFAState = fromState;\r\n\t\tlet to: DFAState = this.addDFAState(dfa, toConfigs, contextCache);\r\n\r\n\t\tif (contextTransitions != null) {\r\n\t\t\tfor (let context of contextTransitions.toArray()) {\r\n\t\t\t\tif (context === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\t\tif (from.configs.isOutermostConfigSet) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfrom.setContextSensitive(this.atn);\r\n\t\t\t\tfrom.setContextSymbol(t);\r\n\t\t\t\tlet next: DFAState | undefined = from.getContextTarget(context);\r\n\t\t\t\tif (next != null) {\r\n\t\t\t\t\tfrom = next;\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnext = this.addDFAContextState(dfa, from.configs, context, contextCache);\r\n\t\t\t\tassert(context !== PredictionContext.EMPTY_FULL_STATE_KEY || next.configs.isOutermostConfigSet);\r\n\t\t\t\tfrom.setContextTarget(context, next);\r\n\t\t\t\tfrom = next;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"EDGE \" + from + \" -> \" + to + \" upon \" + this.getTokenName(t));\r\n\t\t}\r\n\t\tthis.setDFAEdge(from, t, to);\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"DFA=\\n\" + dfa.toString(this._parser != null ? this._parser.vocabulary : VocabularyImpl.EMPTY_VOCABULARY, this._parser != null ? this._parser.ruleNames : undefined));\r\n\t\t}\r\n\t\treturn to;\r\n\t}\r\n\r\n\tprotected setDFAEdge(@Nullable p: DFAState, t: number, @Nullable q: DFAState): void {\r\n\t\tif (p != null) {\r\n\t\t\tp.setTarget(t, q);\r\n\t\t}\r\n\t}\r\n\r\n\t/** See comment on LexerInterpreter.addDFAState. */\r\n\t@NotNull\r\n\tprotected addDFAContextState(@NotNull dfa: DFA, @NotNull configs: ATNConfigSet, returnContext: number, contextCache: PredictionContextCache): DFAState {\r\n\t\tif (returnContext !== PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\tlet contextConfigs: ATNConfigSet = new ATNConfigSet();\r\n\t\t\tfor (let config of configs) {\r\n\t\t\t\tcontextConfigs.add(config.appendContext(returnContext, contextCache));\r\n\t\t\t}\r\n\r\n\t\t\treturn this.addDFAState(dfa, contextConfigs, contextCache);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tassert(!configs.isOutermostConfigSet, \"Shouldn't be adding a duplicate edge.\");\r\n\t\t\tconfigs = configs.clone(true);\r\n\t\t\tconfigs.isOutermostConfigSet = true;\r\n\t\t\treturn this.addDFAState(dfa, configs, contextCache);\r\n\t\t}\r\n\t}\r\n\r\n\t/** See comment on LexerInterpreter.addDFAState. */\r\n\t@NotNull\r\n\tprotected addDFAState(@NotNull dfa: DFA, @NotNull configs: ATNConfigSet, contextCache: PredictionContextCache): DFAState {\r\n\t\tlet enableDfa: boolean = this.enable_global_context_dfa || !configs.isOutermostConfigSet;\r\n\t\tif (enableDfa) {\r\n\t\t\tif (!configs.isReadOnly) {\r\n\t\t\t\tconfigs.optimizeConfigs(this);\r\n\t\t\t}\r\n\r\n\t\t\tlet proposed: DFAState = this.createDFAState(dfa, configs);\r\n\t\t\tlet existing: DFAState | undefined = dfa.states.get(proposed);\r\n\t\t\tif (existing != null) {\r\n\t\t\t\treturn existing;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!configs.isReadOnly) {\r\n\t\t\tif (configs.conflictInfo == null) {\r\n\t\t\t\tconfigs.conflictInfo = this.isConflicted(configs, contextCache);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet newState: DFAState = this.createDFAState(dfa, configs.clone(true));\r\n\t\t// getDecisionState won't return undefined when we request a known valid decision\r\n\t\tlet decisionState: DecisionState = this.atn.getDecisionState(dfa.decision) as DecisionState;\r\n\t\tlet predictedAlt: number = this.getUniqueAlt(configs);\r\n\t\tif (predictedAlt !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\tnewState.acceptStateInfo = new AcceptStateInfo(predictedAlt);\r\n\t\t} else if (configs.conflictingAlts != null) {\r\n\t\t\tlet conflictingAlts = configs.conflictingAlts;\r\n\t\t\tif (conflictingAlts) {\r\n\t\t\t\tnewState.acceptStateInfo = new AcceptStateInfo(conflictingAlts.nextSetBit(0));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (newState.isAcceptState && configs.hasSemanticContext) {\r\n\t\t\tthis.predicateDFAState(newState, configs, decisionState.numberOfTransitions);\r\n\t\t}\r\n\r\n\t\tif (!enableDfa) {\r\n\t\t\treturn newState;\r\n\t\t}\r\n\r\n\t\tlet added: DFAState = dfa.addState(newState);\r\n\t\tif (ParserATNSimulator.debug && added === newState) {\r\n\t\t\tconsole.log(\"adding new DFA state: \" + newState);\r\n\t\t}\r\n\t\treturn added;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected createDFAState(@NotNull dfa: DFA, @NotNull configs: ATNConfigSet): DFAState {\r\n\t\treturn new DFAState(configs);\r\n\t}\r\n\r\n\tprotected reportAttemptingFullContext(@NotNull dfa: DFA, conflictingAlts: BitSet | undefined, @NotNull conflictState: SimulatorState, startIndex: number, stopIndex: number): void {\r\n\t\tif (ParserATNSimulator.debug || ParserATNSimulator.retry_debug) {\r\n\t\t\tlet interval: Interval = Interval.of(startIndex, stopIndex);\r\n\t\t\tconsole.log(\"reportAttemptingFullContext decision=\" + dfa.decision + \":\" + conflictState.s0.configs +\r\n\t\t\t\t\", input=\" + this._parser.inputStream.getText(interval));\r\n\t\t}\r\n\t\tif (this._parser != null) {\r\n\t\t\tlet listener = this._parser.getErrorListenerDispatch();\r\n\t\t\tif (listener.reportAttemptingFullContext) {\r\n\t\t\t\tlistener.reportAttemptingFullContext(this._parser, dfa, startIndex, stopIndex, conflictingAlts, conflictState);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected reportContextSensitivity(@NotNull dfa: DFA, prediction: number, @NotNull acceptState: SimulatorState, startIndex: number, stopIndex: number): void {\r\n\t\tif (ParserATNSimulator.debug || ParserATNSimulator.retry_debug) {\r\n\t\t\tlet interval: Interval = Interval.of(startIndex, stopIndex);\r\n\t\t\tconsole.log(\"reportContextSensitivity decision=\" + dfa.decision + \":\" + acceptState.s0.configs +\r\n\t\t\t\t\", input=\" + this._parser.inputStream.getText(interval));\r\n\t\t}\r\n\t\tif (this._parser != null) {\r\n\t\t\tlet listener = this._parser.getErrorListenerDispatch();\r\n\t\t\tif (listener.reportContextSensitivity) {\r\n\t\t\t\tlistener.reportContextSensitivity(this._parser, dfa, startIndex, stopIndex, prediction, acceptState);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/** If context sensitive parsing, we know it's ambiguity not conflict */\r\n\tprotected reportAmbiguity(\r\n\t\t@NotNull dfa: DFA,\r\n\t\tD: DFAState,  // the DFA state from execATN(): void that had SLL conflicts\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\texact: boolean,\r\n\t\t@NotNull ambigAlts: BitSet,\r\n\t\t@NotNull configs: ATNConfigSet) // configs that LL not SLL considered conflicting\r\n\t{\r\n\t\tif (ParserATNSimulator.debug || ParserATNSimulator.retry_debug) {\r\n\t\t\tlet interval: Interval = Interval.of(startIndex, stopIndex);\r\n\t\t\tconsole.log(\"reportAmbiguity \" +\r\n\t\t\t\tambigAlts + \":\" + configs +\r\n\t\t\t\t\", input=\" + this._parser.inputStream.getText(interval));\r\n\t\t}\r\n\t\tif (this._parser != null) {\r\n\t\t\tlet listener = this._parser.getErrorListenerDispatch();\r\n\t\t\tif (listener.reportAmbiguity) {\r\n\t\t\t\tlistener.reportAmbiguity(this._parser, dfa, startIndex, stopIndex, exact, ambigAlts, configs);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected getReturnState(context: RuleContext): number {\r\n\t\tif (context.isEmpty) {\r\n\t\t\treturn PredictionContext.EMPTY_FULL_STATE_KEY;\r\n\t\t}\r\n\r\n\t\tlet state: ATNState = this.atn.states[context.invokingState];\r\n\t\tlet transition: RuleTransition = state.transition(0) as RuleTransition;\r\n\t\treturn transition.followState.stateNumber;\r\n\t}\r\n\r\n\tprotected skipTailCalls(context: ParserRuleContext): ParserRuleContext {\r\n\t\tif (!this.optimize_tail_calls) {\r\n\t\t\treturn context;\r\n\t\t}\r\n\r\n\t\twhile (!context.isEmpty) {\r\n\t\t\tlet state: ATNState = this.atn.states[context.invokingState];\r\n\t\t\tassert(state.numberOfTransitions === 1 && state.transition(0).serializationType === TransitionType.RULE);\r\n\t\t\tlet transition: RuleTransition = state.transition(0) as RuleTransition;\r\n\t\t\tif (!transition.tailCall) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\t// This method requires that the root ancestor of the ParserRuleContext be empty. If we make it to this\r\n\t\t\t// line, we know the current node is not empty, which means it does have a parent.\r\n\t\t\tcontext = context.parent as ParserRuleContext;\r\n\t\t}\r\n\r\n\t\treturn context;\r\n\t}\r\n\r\n\t/**\r\n\t * @since 4.3\r\n\t */\r\n\tget parser(): Parser {\r\n\t\treturn this._parser;\r\n\t}\r\n}\r\n"]}