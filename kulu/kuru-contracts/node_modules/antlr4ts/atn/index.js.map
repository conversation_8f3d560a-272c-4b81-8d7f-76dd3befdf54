{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/atn/index.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,gEAA8C;AAC9C,qDAAmC;AACnC,kDAAgC;AAChC,wCAAsB;AACtB,8CAA4B;AAC5B,iDAA+B;AAC/B,8DAA4C;AAC5C,oDAAkC;AAClC,mCAAmC;AACnC,iDAA+B;AAC/B,6CAA2B;AAC3B,iDAA+B;AAC/B,4CAA0B;AAC1B,mDAAiC;AACjC,yDAAuC;AACvC,+CAA6B;AAC7B,kDAAgC;AAChC,oDAAkC;AAClC,yDAAuC;AACvC,iDAA+B;AAC/B,2DAAyC;AACzC,sDAAoC;AACpC,iDAA+B;AAC/B,kDAAgC;AAChC,sDAAoC;AACpC,8CAA4B;AAC5B,iDAA+B;AAC/B,gDAA8B;AAC9B,wDAAsC;AACtC,oDAAkC;AAClC,sDAAoC;AACpC,uDAAqC;AACrC,sDAAoC;AACpC,6DAA2C;AAC3C,oDAAkC;AAClC,oDAAkC;AAClC,uDAAqC;AACrC,wDAAsC;AACtC,oDAAkC;AAClC,oDAAkC;AAClC,gDAA8B;AAC9B,uDAAqC;AACrC,iDAA+B;AAC/B,qDAAmC;AACnC,wDAAsC;AACtC,8CAA4B;AAC5B,uDAAqC;AACrC,wDAAsC;AACtC,sDAAoC;AACpC,kEAAgD;AAChD,sDAAoC;AACpC,wDAAsC;AACtC,sDAAoC;AACpC,2DAAyC;AACzC,mDAAiC;AACjC,0DAAwC;AACxC,oDAAkC;AAClC,mDAAiC;AACjC,kDAAgC;AAChC,mDAAiC;AACjC,oDAAkC;AAClC,kDAAgC;AAChC,mDAAiC;AACjC,wDAAsC;AACtC,sDAAoC;AACpC,uDAAqC;AACrC,qDAAmC;AACnC,+CAA6B;AAC7B,mDAAiC;AACjC,uDAAqC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport * from \"./AbstractPredicateTransition\";\r\nexport * from \"./ActionTransition\";\r\nexport * from \"./AmbiguityInfo\";\r\nexport * from \"./ATN\";\r\nexport * from \"./ATNConfig\";\r\nexport * from \"./ATNConfigSet\";\r\nexport * from \"./ATNDeserializationOptions\";\r\nexport * from \"./ATNDeserializer\";\r\n// export * from \"./ATNSerializer\";\r\nexport * from \"./ATNSimulator\";\r\nexport * from \"./ATNState\";\r\nexport * from \"./ATNStateType\";\r\nexport * from \"./ATNType\";\r\nexport * from \"./AtomTransition\";\r\nexport * from \"./BasicBlockStartState\";\r\nexport * from \"./BasicState\";\r\nexport * from \"./BlockEndState\";\r\nexport * from \"./BlockStartState\";\r\nexport * from \"./CodePointTransitions\";\r\nexport * from \"./ConflictInfo\";\r\nexport * from \"./ContextSensitivityInfo\";\r\nexport * from \"./DecisionEventInfo\";\r\nexport * from \"./DecisionInfo\";\r\nexport * from \"./DecisionState\";\r\nexport * from \"./EpsilonTransition\";\r\nexport * from \"./ErrorInfo\";\r\nexport * from \"./InvalidState\";\r\nexport * from \"./LexerAction\";\r\nexport * from \"./LexerActionExecutor\";\r\nexport * from \"./LexerActionType\";\r\nexport * from \"./LexerATNSimulator\";\r\nexport * from \"./LexerChannelAction\";\r\nexport * from \"./LexerCustomAction\";\r\nexport * from \"./LexerIndexedCustomAction\";\r\nexport * from \"./LexerModeAction\";\r\nexport * from \"./LexerMoreAction\";\r\nexport * from \"./LexerPopModeAction\";\r\nexport * from \"./LexerPushModeAction\";\r\nexport * from \"./LexerSkipAction\";\r\nexport * from \"./LexerTypeAction\";\r\nexport * from \"./LL1Analyzer\";\r\nexport * from \"./LookaheadEventInfo\";\r\nexport * from \"./LoopEndState\";\r\nexport * from \"./NotSetTransition\";\r\nexport * from \"./OrderedATNConfigSet\";\r\nexport * from \"./ParseInfo\";\r\nexport * from \"./ParserATNSimulator\";\r\nexport * from \"./PlusBlockStartState\";\r\nexport * from \"./PlusLoopbackState\";\r\nexport * from \"./PrecedencePredicateTransition\";\r\nexport * from \"./PredicateEvalInfo\";\r\nexport * from \"./PredicateTransition\";\r\nexport * from \"./PredictionContext\";\r\nexport * from \"./PredictionContextCache\";\r\nexport * from \"./PredictionMode\";\r\nexport * from \"./ProfilingATNSimulator\";\r\nexport * from \"./RangeTransition\";\r\nexport * from \"./RuleStartState\";\r\nexport * from \"./RuleStopState\";\r\nexport * from \"./RuleTransition\";\r\nexport * from \"./SemanticContext\";\r\nexport * from \"./SetTransition\";\r\nexport * from \"./SimulatorState\";\r\nexport * from \"./StarBlockStartState\";\r\nexport * from \"./StarLoopbackState\";\r\nexport * from \"./StarLoopEntryState\";\r\nexport * from \"./TokensStartState\";\r\nexport * from \"./Transition\";\r\nexport * from \"./TransitionType\";\r\nexport * from \"./WildcardTransition\";\r\n"]}