{"version": 3, "file": "TokensStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/TokensStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iDAA8C;AAC9C,mDAAgD;AAChD,8CAAyC;AAEzC,yEAAyE;AACzE,MAAa,gBAAiB,SAAQ,6BAAa;IAGlD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,WAAW,CAAC;IACjC,CAAC;CACD;AAHA;IADC,qBAAQ;iDAGR;AALF,4CAMC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.7814046-07:00\r\n\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** The Tokens rule start state linking to each lexer rule start state */\r\nexport class TokensStartState extends DecisionState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.TOKEN_START;\r\n\t}\r\n}\r\n"]}