{"version": 3, "file": "PredicateEvalInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/PredicateEvalInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,8CAAwC;AAKxC;;;;;;;GAOG;AACH,IAAa,iBAAiB,GAA9B,MAAa,iBAAkB,SAAQ,qCAAiB;IAiBvD;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YACU,KAAqB,EAC9B,QAAgB,EACP,KAAkB,EAC3B,UAAkB,EAClB,SAAiB,EACR,MAAuB,EAChC,UAAmB,EACnB,YAAoB;QAEpB,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;CACD,CAAA;AApDY,iBAAiB;IAsC3B,WAAA,oBAAO,CAAA;IAEP,WAAA,oBAAO,CAAA;IAGP,WAAA,oBAAO,CAAA;GA3CG,iBAAiB,CAoD7B;AApDY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.1914305-07:00\r\n\r\nimport { DecisionEventInfo } from \"./DecisionEventInfo\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This class represents profiling event information for semantic predicate\r\n * evaluations which occur during prediction.\r\n *\r\n * @see ParserATNSimulator#evalSemanticContext\r\n *\r\n * @since 4.3\r\n */\r\nexport class PredicateEvalInfo extends DecisionEventInfo {\r\n\t/**\r\n\t * The semantic context which was evaluated.\r\n\t */\r\n\tpublic semctx: SemanticContext;\r\n\t/**\r\n\t * The alternative number for the decision which is guarded by the semantic\r\n\t * context {@link #semctx}. Note that other ATN\r\n\t * configurations may predict the same alternative which are guarded by\r\n\t * other semantic contexts and/or {@link SemanticContext#NONE}.\r\n\t */\r\n\tpublic predictedAlt: number;\r\n\t/**\r\n\t * The result of evaluating the semantic context {@link #semctx}.\r\n\t */\r\n\tpublic evalResult: boolean;\r\n\r\n\t/**\r\n\t * Constructs a new instance of the {@link PredicateEvalInfo} class with the\r\n\t * specified detailed predicate evaluation information.\r\n\t *\r\n\t * @param state The simulator state\r\n\t * @param decision The decision number\r\n\t * @param input The input token stream\r\n\t * @param startIndex The start index for the current prediction\r\n\t * @param stopIndex The index at which the predicate evaluation was\r\n\t * triggered. Note that the input stream may be reset to other positions for\r\n\t * the actual evaluation of individual predicates.\r\n\t * @param semctx The semantic context which was evaluated\r\n\t * @param evalResult The results of evaluating the semantic context\r\n\t * @param predictedAlt The alternative number for the decision which is\r\n\t * guarded by the semantic context `semctx`. See {@link #predictedAlt}\r\n\t * for more information.\r\n\t *\r\n\t * @see ParserATNSimulator#evalSemanticContext(SemanticContext, ParserRuleContext, int)\r\n\t * @see SemanticContext#eval(Recognizer, RuleContext)\r\n\t */\r\n\tconstructor(\r\n\t\t@NotNull state: SimulatorState,\r\n\t\tdecision: number,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\t@NotNull semctx: SemanticContext,\r\n\t\tevalResult: boolean,\r\n\t\tpredictedAlt: number) {\r\n\r\n\t\tsuper(decision, state, input, startIndex, stopIndex, state.useContext);\r\n\t\tthis.semctx = semctx;\r\n\t\tthis.evalResult = evalResult;\r\n\t\tthis.predictedAlt = predictedAlt;\r\n\t}\r\n}\r\n"]}