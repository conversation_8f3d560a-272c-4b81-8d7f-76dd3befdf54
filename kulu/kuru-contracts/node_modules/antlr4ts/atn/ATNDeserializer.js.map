{"version": 3, "file": "ATNDeserializer.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNDeserializer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,yDAAsD;AACtD,2DAAwD;AACxD,+BAA4B;AAC5B,2EAAwE;AAExE,iDAA8C;AAE9C,qDAAkD;AAClD,iEAA8D;AAC9D,6CAA0C;AAC1C,2CAAwC;AACxC,mDAAgD;AAChD,uDAAoD;AACpD,mDAAgD;AAChD,oCAAiC;AACjC,2DAAwD;AAExD,qDAAkD;AAClD,iDAA8C;AAG9C,6DAA0D;AAC1D,2DAAwD;AACxD,uDAAoD;AACpD,uDAAoD;AACpD,6DAA0D;AAC1D,+DAA4D;AAC5D,uDAAoD;AACpD,uDAAoD;AACpD,iDAA8C;AAC9C,8CAAwC;AACxC,yDAAsD;AACtD,6DAA0D;AAC1D,+DAA4D;AAC5D,2DAAwD;AACxD,mFAAgF;AAChF,+DAA4D;AAC5D,uDAAoD;AACpD,qDAAkD;AAClD,mDAAgD;AAChD,qDAAkD;AAClD,mDAAgD;AAChD,+DAA4D;AAC5D,2DAAwD;AACxD,6DAA0D;AAC1D,oCAAiC;AACjC,yDAAsD;AAGtD,uCAAoC;AACpC,6DAA0D;AAW1D,IAAW,wBAGV;AAHD,WAAW,wBAAwB;IAClC,qFAAW,CAAA;IACX,qFAAW,CAAA;AACZ,CAAC,EAHU,wBAAwB,KAAxB,wBAAwB,QAGlC;AAED;;;GAGG;AACH,MAAa,eAAe;IA8C3B,YAAY,sBAAkD;QAC7D,IAAI,sBAAsB,KAAK,SAAS,EAAE;YACzC,sBAAsB,GAAG,qDAAyB,CAAC,cAAc,CAAC;SAClE;QAED,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACtD,CAAC;IAnDD,MAAM,KAAK,kBAAkB;QAC5B;;WAEG;QACH,OAAO,CAAC,CAAC;IACV,CAAC;IAgDD;;;;;;;;;;;;OAYG;IACO,MAAM,CAAC,kBAAkB,CAAC,OAAa,EAAE,UAAgB;QAClE,IAAI,YAAY,GAAW,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/F,IAAI,YAAY,GAAG,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC;SACb;QAED,OAAO,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,YAAY,CAAC;IAC/F,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,IAA8B;QACnE,IAAI,IAAI,wBAAyC,EAAE;YAClD,OAAO;gBACN,WAAW,EAAE,CAAC,IAAiB,EAAE,CAAS,EAAU,EAAE;oBACrD,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,EAAE,CAAC;aACP,CAAC;SACF;aAAM;YACN,OAAO;gBACN,WAAW,EAAE,CAAC,IAAiB,EAAE,CAAS,EAAU,EAAE;oBACrD,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzC,CAAC;gBACD,IAAI,EAAE,CAAC;aACP,CAAC;SACF;IACF,CAAC;IAEM,WAAW,CAAU,IAAiB;QAC5C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAErB,2GAA2G;QAC3G,wGAAwG;QACxG,2GAA2G;QAC3G,gHAAgH;QAChH,gHAAgH;QAChH,iGAAiG;QACjG,EAAE;QACF,+GAA+G;QAC/G,qGAAqG;QACrG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;SACjC;QAED,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,OAAO,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,IAAI,OAAO,KAAK,eAAe,CAAC,kBAAkB,EAAE;YACnD,IAAI,MAAM,GAAG,0CAA0C,OAAO,cAAc,eAAe,CAAC,kBAAkB,IAAI,CAAC;YACnH,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;QAED,IAAI,IAAI,GAAS,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;YACzE,IAAI,MAAM,GAAG,uCAAuC,IAAI,cAAc,eAAe,CAAC,eAAe,qBAAqB,CAAC;YAC3H,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;QAED,IAAI,oBAAoB,GAAY,eAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAElH,IAAI,WAAW,GAAY,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,YAAY,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,GAAG,GAAQ,IAAI,SAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAElD,EAAE;QACF,SAAS;QACT,EAAE;QACF,IAAI,oBAAoB,GAAkC,EAAE,CAAC;QAC7D,IAAI,eAAe,GAAqC,EAAE,CAAC;QAC3D,IAAI,OAAO,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YACjC,IAAI,KAAK,GAAiB,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3D,4BAA4B;YAC5B,IAAI,KAAK,KAAK,2BAAY,CAAC,YAAY,EAAE;gBACxC,GAAG,CAAC,QAAQ,CAAC,IAAI,2BAAY,EAAE,CAAC,CAAC;gBACjC,SAAS;aACT;YAED,IAAI,SAAS,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzD,IAAI,SAAS,KAAK,MAAM,EAAE;gBACzB,SAAS,GAAG,CAAC,CAAC,CAAC;aACf;YAED,IAAI,CAAC,GAAa,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,2BAAY,CAAC,QAAQ,EAAE,EAAE,eAAe;gBACrD,IAAI,mBAAmB,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAiB,EAAE,mBAAmB,CAAC,CAAC,CAAC;aACpE;iBACI,IAAI,CAAC,YAAY,iCAAe,EAAE;gBACtC,IAAI,cAAc,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9D,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;aAC1C;YACD,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAChB;QAED,+GAA+G;QAC/G,KAAK,IAAI,IAAI,IAAI,oBAAoB,EAAE;YACtC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5C;QAED,KAAK,IAAI,IAAI,IAAI,eAAe,EAAE;YACjC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;SACxD;QAED,IAAI,kBAAkB,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,WAAW,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,WAAW,CAAmB,CAAC,SAAS,GAAG,IAAI,CAAC;SAC5D;QAED,IAAI,eAAe,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,WAAW,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,WAAW,CAAmB,CAAC,GAAG,GAAG,IAAI,CAAC;SACtD;QAED,IAAI,mBAAmB,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,WAAW,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,WAAW,CAAoB,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACpE;QAED,EAAE;QACF,QAAQ;QACR,EAAE;QACF,IAAI,MAAM,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,IAAI,GAAG,CAAC,WAAW,kBAAkB,EAAE;YACtC,GAAG,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;SAC7C;QAED,GAAG,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAiB,MAAM,CAAC,CAAC;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,IAAI,UAAU,GAAmB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAmB,CAAC;YACjE,UAAU,CAAC,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;YACjE,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;YACrC,IAAI,GAAG,CAAC,WAAW,kBAAkB,EAAE;gBACtC,IAAI,SAAS,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzD,IAAI,SAAS,KAAK,MAAM,EAAE;oBACzB,SAAS,GAAG,aAAK,CAAC,GAAG,CAAC;iBACtB;gBAED,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gBAEnC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,mBAAmB,EAAE,IAAI,CAAC,EAAE;oBACnF,4DAA4D;oBAC5D,0BAA0B;oBAC1B,IAAI,kBAAkB,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClE,IAAI,kBAAkB,KAAK,MAAM,EAAE;wBAClC,kBAAkB,GAAG,CAAC,CAAC,CAAC;qBACxB;iBACD;aACD;SACD;QAED,GAAG,CAAC,eAAe,GAAG,IAAI,KAAK,CAAgB,MAAM,CAAC,CAAC;QACvD,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,CAAC,KAAK,YAAY,6BAAa,CAAC,EAAE;gBACtC,SAAS;aACT;YAED,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;YAC7C,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;SACxD;QAED,EAAE;QACF,QAAQ;QACR,EAAE;QACF,IAAI,MAAM,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAqB,CAAC,CAAC;SAC7D;QAED,GAAG,CAAC,SAAS,GAAG,IAAI,KAAK,CAAM,MAAM,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SACpD;QAED,EAAE;QACF,OAAO;QACP,EAAE;QACF,IAAI,IAAI,GAAkB,EAAE,CAAC;QAE7B,kEAAkE;QAClE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,sBAAsB,qBAAsC,CAAC,CAAC;QAEtH,gEAAgE;QAChE,sDAAsD;QACtD,IAAI,eAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;YAChF,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,sBAAsB,qBAAsC,CAAC,CAAC;SACtH;QAED,EAAE;QACF,QAAQ;QACR,EAAE;QACF,IAAI,MAAM,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,GAAG,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACrD,IAAI,KAAK,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvD,IAAI,IAAI,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,IAAI,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,IAAI,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,KAAK,GAAe,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACvF,gIAAgI;YAChI,IAAI,QAAQ,GAAa,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACzC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC,IAAI,CAAC,CAAC;SACP;QAID,IAAI,oBAAoB,GAAG,IAAI,+BAAc,CAAI;YAChD,QAAQ,EAAE,CAAC,CAAI,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,yBAAyB;YAE7E,MAAM,EAAE,CAAC,CAAI,EAAE,CAAI,EAAW,EAAE;gBAC/B,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;uBAC9B,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW;uBAC/B,CAAC,CAAC,yBAAyB,KAAK,CAAC,CAAC,yBAAyB,CAAC;YACjE,CAAC;SACD,CAAC,CAAC;QACH,IAAI,iBAAiB,GAAQ,EAAE,CAAC;QAChC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,uBAAuB,GAAY,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;YAClH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,CAAC,GAAe,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,CAAC,CAAC,YAAY,+BAAc,CAAC,EAAE;oBACnC,SAAS;iBACT;gBAED,IAAI,cAAc,GAAmB,CAAC,CAAC;gBACvC,IAAI,yBAAyB,GAAY,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;gBAC5G,IAAI,CAAC,yBAAyB,IAAI,uBAAuB,EAAE;oBAC1D,SAAS;iBACT;gBAED,IAAI,yBAAyB,GAAW,CAAC,CAAC,CAAC;gBAC3C,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,gBAAgB,EAAE;oBAC3E,IAAI,cAAc,CAAC,UAAU,KAAK,CAAC,EAAE;wBACpC,yBAAyB,GAAG,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC;qBAC5D;iBACD;gBAED,IAAI,OAAO,GAAG,EAAE,SAAS,EAAE,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,yBAAyB,EAAE,CAAC;gBAC7I,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACtC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAChC;aACD;SACD;QAED,qDAAqD;QACrD,KAAK,IAAI,gBAAgB,IAAI,iBAAiB,EAAE;YAC/C,IAAI,UAAU,GAAG,IAAI,qCAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;YAC7H,GAAG,CAAC,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SAC1E;QAED,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,KAAK,YAAY,iCAAe,EAAE;gBACrC,uDAAuD;gBACvD,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBACzC;gBAED,wEAAwE;gBACxE,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;oBAC5C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBACzC;gBAED,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;aAClC;YAED,IAAI,KAAK,YAAY,qCAAiB,EAAE;gBACvC,IAAI,aAAa,GAAsB,KAAK,CAAC;gBAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;oBAC3D,IAAI,MAAM,GAAa,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC1D,IAAI,MAAM,YAAY,yCAAmB,EAAE;wBAC1C,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;qBACrC;iBACD;aACD;iBACI,IAAI,KAAK,YAAY,qCAAiB,EAAE;gBAC5C,IAAI,aAAa,GAAsB,KAAK,CAAC;gBAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;oBAC3D,IAAI,MAAM,GAAa,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC1D,IAAI,MAAM,YAAY,uCAAkB,EAAE;wBACzC,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;qBACrC;iBACD;aACD;SACD;QAED,EAAE;QACF,YAAY;QACZ,EAAE;QACF,IAAI,UAAU,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,IAAI,QAAQ,GAAkB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAkB,CAAC;YAC7D,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,QAAQ,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;SAC1B;QAED,EAAE;QACF,gBAAgB;QAChB,EAAE;QACF,IAAI,GAAG,CAAC,WAAW,kBAAkB,EAAE;YACtC,IAAI,oBAAoB,EAAE;gBACzB,GAAG,CAAC,YAAY,GAAG,IAAI,KAAK,CAAc,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACjD,IAAI,UAAU,GAAoB,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACnE,IAAI,KAAK,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrD,IAAI,KAAK,KAAK,MAAM,EAAE;wBACrB,KAAK,GAAG,CAAC,CAAC,CAAC;qBACX;oBAED,IAAI,KAAK,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrD,IAAI,KAAK,KAAK,MAAM,EAAE;wBACrB,KAAK,GAAG,CAAC,CAAC,CAAC;qBACX;oBAED,IAAI,WAAW,GAAgB,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBAEjF,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;iBAClC;aACD;iBACI;gBACJ,gEAAgE;gBAChE,4DAA4D;gBAC5D,kDAAkD;gBAClD,IAAI,kBAAkB,GAAkB,EAAE,CAAC;gBAC3C,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;oBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;wBACnD,IAAI,UAAU,GAAe,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;wBACjD,IAAI,CAAC,CAAC,UAAU,YAAY,mCAAgB,CAAC,EAAE;4BAC9C,SAAS;yBACT;wBAED,IAAI,SAAS,GAAW,UAAU,CAAC,SAAS,CAAC;wBAC7C,IAAI,WAAW,GAAW,UAAU,CAAC,WAAW,CAAC;wBACjD,IAAI,WAAW,GAAsB,IAAI,qCAAiB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;wBACnF,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,mCAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;wBAC7G,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBACrC;iBACD;gBAED,GAAG,CAAC,YAAY,GAAG,kBAAkB,CAAC;aACtC;SACD;QAED,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAElC,GAAG,CAAC,aAAa,GAAG,IAAI,KAAK,CAAM,UAAU,CAAC,CAAC;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACpC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1D;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;YAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SACpB;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,+BAA+B,IAAI,GAAG,CAAC,WAAW,mBAAmB,EAAE;YACtG,GAAG,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;aAClD;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,IAAI,WAAW,GAAyB,IAAI,2CAAoB,EAAE,CAAC;gBACnE,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE1B,IAAI,UAAU,GAAkB,IAAI,6BAAa,EAAE,CAAC;gBACpD,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;gBACzB,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEzB,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAClC,GAAG,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBAErC,UAAU,CAAC,UAAU,GAAG,WAAW,CAAC;gBAEpC,IAAI,QAA8B,CAAC;gBACnC,IAAI,iBAAyC,CAAC;gBAC9C,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE;oBAC7C,gEAAgE;oBAChE,QAAQ,GAAG,SAAS,CAAC;oBACrB,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;wBAC7B,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE;4BAC1B,SAAS;yBACT;wBAED,IAAI,CAAC,CAAC,KAAK,YAAY,uCAAkB,CAAC,EAAE;4BAC3C,SAAS;yBACT;wBAED,IAAI,iBAAiB,GAAa,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;wBACzF,IAAI,CAAC,CAAC,iBAAiB,YAAY,2BAAY,CAAC,EAAE;4BACjD,SAAS;yBACT;wBAED,IAAI,iBAAiB,CAAC,sBAAsB,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,6BAAa,EAAE;4BAChH,QAAQ,GAAG,KAAK,CAAC;4BACjB,MAAM;yBACN;qBACD;oBAED,IAAI,CAAC,QAAQ,EAAE;wBACd,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;qBACxF;oBAED,iBAAiB,GAAI,QAA+B,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBACjF;qBACI;oBACJ,QAAQ,GAAG,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;iBAClC;gBAED,+FAA+F;gBAC/F,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;oBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;wBACnD,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;wBACrC,IAAI,UAAU,KAAK,iBAAiB,EAAE;4BACrC,SAAS;yBACT;wBAED,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE;4BACnC,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;yBAC/B;qBACD;iBACD;gBAED,gFAAgF;gBAChF,OAAO,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,EAAE;oBACvD,IAAI,UAAU,GAAe,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;oBACvH,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;iBACtC;gBAED,sBAAsB;gBACtB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,qCAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC1E,UAAU,CAAC,aAAa,CAAC,IAAI,qCAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAE1D,IAAI,UAAU,GAAa,IAAI,uBAAU,EAAE,CAAC;gBAC5C,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACzB,UAAU,CAAC,aAAa,CAAC,IAAI,+BAAc,CAAC,UAAU,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjF,WAAW,CAAC,aAAa,CAAC,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;aAC7D;YAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;gBAC5C,8BAA8B;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACpB;SACD;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE;YAC3C,OAAO,IAAI,EAAE;gBACZ,IAAI,iBAAiB,GAAW,CAAC,CAAC;gBAClC,iBAAiB,IAAI,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACzD,iBAAiB,IAAI,eAAe,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;gBACjE,IAAI,aAAa,GAAY,GAAG,CAAC,WAAW,kBAAkB,CAAC;gBAC/D,iBAAiB,IAAI,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBACtE,IAAI,iBAAiB,KAAK,CAAC,EAAE;oBAC5B,MAAM;iBACN;aACD;YAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;gBAC5C,8BAA8B;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACpB;SACD;QAED,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAEvC,OAAO,GAAG,CAAC;IACZ,CAAC;IAEO,eAAe,CAAC,IAAiB,EAAE,CAAS,EAAE,IAAmB,EAAE,mBAAwC;QAClH,IAAI,KAAK,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,UAAU,GAAW,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,EAAE,CAAC;YACJ,IAAI,GAAG,GAAgB,IAAI,yBAAW,EAAE,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEf,IAAI,WAAW,GAAY,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;YAClE,IAAI,WAAW,EAAE;gBAChB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACZ;YAED,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,CAAC,GAAW,mBAAmB,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzD,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC;gBAC9B,IAAI,CAAC,GAAW,mBAAmB,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzD,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC;gBAC9B,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACd;SACD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;OAMG;IACO,uBAAuB,CAAU,GAAQ;QAClD,sDAAsD;QACtD,IAAI,uBAAuB,GAAG,IAAI,GAAG,EAA8B,CAAC;QAEpE,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,CAAC,KAAK,YAAY,uCAAkB,CAAC,EAAE;gBAC3C,SAAS;aACT;YAED;;;eAGG;YACH,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,gBAAgB,EAAE;gBAC3D,IAAI,iBAAiB,GAAa,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;gBACzF,IAAI,iBAAiB,YAAY,2BAAY,EAAE;oBAC9C,IAAI,iBAAiB,CAAC,sBAAsB,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,6BAAa,EAAE;wBAChH,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;wBACpD,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC;wBACpC,KAAK,CAAC,wBAAwB,GAAG,IAAI,eAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;qBAC/D;iBACD;aACD;SACD;QAED,qEAAqE;QACrE,+CAA+C;QAC/C,KAAK,IAAI,kBAAkB,IAAI,uBAAuB,EAAE;YACvD,KAAK,IAAI,UAAU,IAAI,GAAG,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,EAAE;gBACnF,IAAI,UAAU,CAAC,iBAAiB,oBAA2B,EAAE;oBAC5D,SAAS;iBACT;gBAED,IAAI,iBAAiB,GAAG,UAA+B,CAAC;gBACxD,IAAI,iBAAiB,CAAC,yBAAyB,KAAK,CAAC,CAAC,EAAE;oBACvD,SAAS;iBACT;gBAED,kBAAkB,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;aAClF;SACD;IACF,CAAC;IAES,SAAS,CAAC,GAAQ;QAC3B,qBAAqB;QACrB,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE,qCAAqC,CAAC,CAAC;YAChF,IAAI,KAAK,CAAC,SAAS,KAAK,2BAAY,CAAC,YAAY,EAAE;gBAClD,SAAS;aACT;YAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,yBAAyB,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC,CAAC;YAEvF,IAAI,KAAK,YAAY,yCAAmB,EAAE;gBACzC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC;aACvD;YAED,IAAI,KAAK,YAAY,uCAAkB,EAAE;gBACxC,IAAI,kBAAkB,GAAuB,KAAK,CAAC;gBACnD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC;gBACpE,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,mBAAmB,KAAK,CAAC,CAAC,CAAC;gBAElE,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,yCAAmB,EAAE;oBAC3E,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,2BAAY,CAAC,CAAC;oBACrF,IAAI,CAAC,cAAc,CAAC,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;iBACnD;qBACI,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,2BAAY,EAAE;oBACzE,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,yCAAmB,CAAC,CAAC;oBAC5F,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;iBAClD;qBACI;oBACJ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBACzC;aACD;YAED,IAAI,KAAK,YAAY,qCAAiB,EAAE;gBACvC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,uCAAkB,CAAC,CAAC;aAC9E;YAED,IAAI,KAAK,YAAY,2BAAY,EAAE;gBAClC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC;aACvD;YAED,IAAI,KAAK,YAAY,+BAAc,EAAE;gBACpC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;aACnD;YAED,IAAI,KAAK,YAAY,iCAAe,EAAE;gBACrC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;aAClD;YAED,IAAI,KAAK,YAAY,6BAAa,EAAE;gBACnC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;aACpD;YAED,IAAI,KAAK,YAAY,6BAAa,EAAE;gBACnC,IAAI,aAAa,GAAkB,KAAK,CAAC;gBACzC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,mBAAmB,IAAI,CAAC,IAAI,aAAa,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;aAC3F;iBACI;gBACJ,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,IAAI,KAAK,YAAY,6BAAa,CAAC,CAAC;aACtF;SACD;IACF,CAAC;IAES,cAAc,CAAC,SAAkB,EAAE,OAAgB;QAC5D,IAAI,CAAC,SAAS,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,OAAO,CAAC,CAAC;SACrD;IACF,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,GAAQ;QACrC,IAAI,YAAY,GAAW,CAAC,CAAC;QAE7B,IAAI,sBAAsB,GAAG,IAAI,KAAK,CAAyB,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC5F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,UAAU,GAAmB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,WAAW,GAAa,UAAU,CAAC;YACvC,OAAO,WAAW,CAAC,yBAAyB;mBACxC,WAAW,CAAC,4BAA4B,KAAK,CAAC;mBAC9C,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,iBAAiB,oBAA2B,EAAE;gBACvF,WAAW,GAAG,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;aAC3D;YAED,IAAI,WAAW,CAAC,4BAA4B,KAAK,CAAC,EAAE;gBACnD,SAAS;aACT;YAED,IAAI,eAAe,GAAe,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACxE,IAAI,WAAW,GAAa,eAAe,CAAC,MAAM,CAAC;YACnD,IAAI,eAAe,CAAC,SAAS;mBACzB,CAAC,WAAW,CAAC,yBAAyB;mBACtC,WAAW,CAAC,4BAA4B,KAAK,CAAC;mBAC9C,CAAC,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,6BAAa,CAAC,EAAE;gBAC7E,SAAS;aACT;YAED,QAAQ,eAAe,CAAC,iBAAiB,EAAE;gBAC3C,kBAAyB;gBACzB,mBAA0B;gBAC1B;oBACC,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;oBAC5C,MAAM;gBAEP,qBAA4B;gBAC5B;oBACC,sBAAsB;oBACtB,SAAS;gBAEV;oBACC,SAAS;aACT;SACD;QAED,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE;gBACxB,SAAS;aACT;YAED,IAAI,oBAA8C,CAAC;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;gBAC5D,IAAI,UAAU,GAAe,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,CAAC,CAAC,UAAU,YAAY,+BAAc,CAAC,EAAE;oBAC5C,IAAI,oBAAoB,KAAK,SAAS,EAAE;wBACvC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACtC;oBAED,SAAS;iBACT;gBAED,IAAI,cAAc,GAAmB,UAAU,CAAC;gBAChD,IAAI,SAAS,GAA2B,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChG,IAAI,SAAS,KAAK,SAAS,EAAE;oBAC5B,IAAI,oBAAoB,KAAK,SAAS,EAAE;wBACvC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACtC;oBAED,SAAS;iBACT;gBAED,IAAI,oBAAoB,KAAK,SAAS,EAAE;oBACvC,oBAAoB,GAAG,EAAE,CAAC;oBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC3B,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3D;iBACD;gBAED,YAAY,EAAE,CAAC;gBACf,IAAI,MAAM,GAAa,cAAc,CAAC,WAAW,CAAC;gBAClD,IAAI,iBAAiB,GAAa,IAAI,uBAAU,EAAE,CAAC;gBACnD,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACjD,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;gBAChC,oBAAoB,CAAC,IAAI,CAAC,IAAI,qCAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBAEpE,QAAQ,SAAS,CAAC,iBAAiB,EAAE;oBACrC;wBACC,iBAAiB,CAAC,aAAa,CAAC,IAAI,+BAAc,CAAC,MAAM,EAAG,SAA4B,CAAC,MAAM,CAAC,CAAC,CAAC;wBAClG,MAAM;oBAEP;wBACC,iBAAiB,CAAC,aAAa,CAAC,IAAI,iCAAe,CAAC,MAAM,EAAG,SAA6B,CAAC,IAAI,EAAG,SAA6B,CAAC,EAAE,CAAC,CAAC,CAAC;wBACrI,MAAM;oBAEP;wBACC,iBAAiB,CAAC,aAAa,CAAC,IAAI,6BAAa,CAAC,MAAM,EAAG,SAA2B,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC/F,MAAM;oBAEP;wBACC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;iBACjD;aACD;YAED,IAAI,oBAAoB,KAAK,SAAS,EAAE;gBACvC,IAAI,KAAK,CAAC,WAAW,EAAE;oBACtB,OAAO,KAAK,CAAC,4BAA4B,GAAG,CAAC,EAAE;wBAC9C,KAAK,CAAC,yBAAyB,CAAC,KAAK,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC;qBACxE;iBACD;gBAED,KAAK,IAAI,UAAU,IAAI,oBAAoB,EAAE;oBAC5C,KAAK,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;iBACzC;aACD;SACD;QAED,IAAI,uCAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,YAAY,GAAG,qCAAqC,CAAC,CAAC;SACrG;QAED,OAAO,YAAY,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,GAAQ;QAC7C,IAAI,YAAY,GAAW,CAAC,CAAC;QAE7B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,yBAAyB,IAAI,KAAK,YAAY,6BAAa,EAAE;gBACvE,SAAS;aACT;YAED,IAAI,oBAA8C,CAAC;YACnD,cAAc,EACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;gBAC5D,IAAI,UAAU,GAAe,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,YAAY,GAAa,UAAU,CAAC,MAAM,CAAC;gBAC/C,IAAI,UAAU,CAAC,iBAAiB,oBAA2B;uBACtD,UAAgC,CAAC,yBAAyB,KAAK,CAAC,CAAC;uBAClE,YAAY,CAAC,SAAS,KAAK,2BAAY,CAAC,KAAK;uBAC7C,CAAC,YAAY,CAAC,yBAAyB,EAAE;oBAC5C,IAAI,oBAAoB,KAAK,SAAS,EAAE;wBACvC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACtC;oBAED,SAAS,cAAc,CAAC;iBACxB;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;oBACnE,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,iBAAiB,oBAA2B;2BAClF,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAuB,CAAC,yBAAyB,KAAK,CAAC,CAAC,EAAE;wBACnG,IAAI,oBAAoB,KAAK,SAAS,EAAE;4BACvC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;yBACtC;wBAED,SAAS,cAAc,CAAC;qBACxB;iBACD;gBAED,YAAY,EAAE,CAAC;gBACf,IAAI,oBAAoB,KAAK,SAAS,EAAE;oBACvC,oBAAoB,GAAG,EAAE,CAAC;oBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC3B,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3D;iBACD;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;oBACnE,IAAI,MAAM,GAAa,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBACrE,oBAAoB,CAAC,IAAI,CAAC,IAAI,qCAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;iBACzD;aACD;YAED,IAAI,oBAAoB,KAAK,SAAS,EAAE;gBACvC,IAAI,KAAK,CAAC,WAAW,EAAE;oBACtB,OAAO,KAAK,CAAC,4BAA4B,GAAG,CAAC,EAAE;wBAC9C,KAAK,CAAC,yBAAyB,CAAC,KAAK,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC;qBACxE;iBACD;gBAED,KAAK,IAAI,UAAU,IAAI,oBAAoB,EAAE;oBAC5C,KAAK,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;iBACzC;aACD;SACD;QAED,IAAI,uCAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,YAAY,GAAG,wDAAwD,CAAC,CAAC;SACxH;QAED,OAAO,YAAY,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,GAAQ,EAAE,aAAsB;QAC3D,IAAI,aAAa,EAAE;YAClB,2DAA2D;YAC3D,OAAO,CAAC,CAAC;SACT;QAED,IAAI,YAAY,GAAW,CAAC,CAAC;QAC7B,IAAI,SAAS,GAAoB,GAAG,CAAC,eAAe,CAAC;QACrD,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC/B,IAAI,cAAc,GAAgB,IAAI,yBAAW,EAAE,CAAC;YACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;gBAC/D,IAAI,aAAa,GAAe,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACnE,IAAI,CAAC,CAAC,aAAa,YAAY,qCAAiB,CAAC,EAAE;oBAClD,SAAS;iBACT;gBAED,IAAI,aAAa,CAAC,MAAM,CAAC,4BAA4B,KAAK,CAAC,EAAE;oBAC5D,SAAS;iBACT;gBAED,IAAI,UAAU,GAAe,aAAa,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC5E,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,YAAY,6BAAa,CAAC,EAAE;oBAClD,SAAS;iBACT;gBAED,IAAI,UAAU,YAAY,mCAAgB,EAAE;oBAC3C,4BAA4B;oBAC5B,SAAS;iBACT;gBAED,IAAI,UAAU,YAAY,+BAAc;uBACpC,UAAU,YAAY,iCAAe;uBACrC,UAAU,YAAY,6BAAa,EAAE;oBACxC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACtB;aACD;YAED,IAAI,cAAc,CAAC,IAAI,IAAI,CAAC,EAAE;gBAC7B,SAAS;aACT;YAED,IAAI,oBAAoB,GAAiB,EAAE,CAAC;YAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;gBAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAChC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9D;aACD;YAED,IAAI,aAAa,GAAa,QAAQ,CAAC,sBAAsB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACjI,IAAI,QAAQ,GAAgB,IAAI,yBAAW,EAAE,CAAC;YAC9C,KAAK,IAAI,QAAQ,IAAI,cAAc,CAAC,SAAS,EAAE;gBAC9C,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC9C,IAAI,eAAe,GAAe,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBACtG,IAAI,eAAe,YAAY,mCAAgB,EAAE;wBAChD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;qBACxC;yBAAM;wBACN,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,KAAoB,CAAC,CAAC;qBACtD;iBACD;aACD;YAED,IAAI,aAAyB,CAAC;YAC9B,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpC,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;oBACxB,aAAa,GAAG,IAAI,+BAAc,CAAC,aAAa,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;iBACvE;qBAAM;oBACN,IAAI,aAAa,GAAa,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACpD,aAAa,GAAG,IAAI,iCAAe,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;iBACrF;aACD;iBAAM;gBACN,aAAa,GAAG,IAAI,6BAAa,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;aAC3D;YAED,IAAI,iBAAiB,GAAa,IAAI,uBAAU,EAAE,CAAC;YACnD,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACnD,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YAEhC,iBAAiB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAC/C,oBAAoB,CAAC,IAAI,CAAC,IAAI,qCAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAEpE,YAAY,IAAI,QAAQ,CAAC,4BAA4B,GAAG,oBAAoB,CAAC,MAAM,CAAC;YAEpF,IAAI,QAAQ,CAAC,WAAW,EAAE;gBACzB,OAAO,QAAQ,CAAC,4BAA4B,GAAG,CAAC,EAAE;oBACjD,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC;iBAC9E;aACD;YAED,KAAK,IAAI,UAAU,IAAI,oBAAoB,EAAE;gBAC5C,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;aAC5C;SACD;QAED,IAAI,uCAAkB,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,YAAY,GAAG,4BAA4B,CAAC,CAAC;SAC5F;QAED,OAAO,YAAY,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,GAAQ;QACxC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,CAAC,UAAU,YAAY,+BAAc,CAAC,EAAE;oBAC5C,SAAS;iBACT;gBAED,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gBAChE,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;aACxE;YAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACvB,SAAS;aACT;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;gBAC5D,IAAI,UAAU,GAAG,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,CAAC,UAAU,YAAY,+BAAc,CAAC,EAAE;oBAC5C,SAAS;iBACT;gBAED,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gBAChE,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;aACxE;SACD;IACF,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,GAAQ,EAAE,UAA0B,EAAE,aAAsB;QACvF,IAAI,CAAC,aAAa,IAAI,UAAU,CAAC,QAAQ,EAAE;YAC1C,OAAO,IAAI,CAAC;SACZ;QACD,IAAI,aAAa,IAAI,UAAU,CAAC,iBAAiB,EAAE;YAClD,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,SAAS,GAAW,IAAI,eAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,QAAQ,GAAe,EAAE,CAAC;QAC9B,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACtC,OAAO,IAAI,EAAE;YACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,EAAE;gBACX,MAAM;aACN;YAED,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gBACrC,SAAS;aACT;YAED,IAAI,KAAK,YAAY,6BAAa,EAAE;gBACnC,SAAS;aACT;YAED,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBACrC,OAAO,KAAK,CAAC;aACb;YAED,IAAI,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC;YACrG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC9E,IAAI,CAAC,CAAC,iBAAiB,oBAA2B,EAAE;oBACnD,OAAO,KAAK,CAAC;iBACb;gBAED,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;aACxB;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAES,MAAM,CAAC,KAAK,CAAC,CAAS;QAC/B,OAAO,CAAC,CAAC;IACV,CAAC;IAES,MAAM,CAAC,OAAO,CAAC,IAAiB,EAAE,MAAc;QACzD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACxD,CAAC;IAES,MAAM,CAAC,MAAM,CAAC,IAAiB,EAAE,MAAc;QACxD,IAAI,YAAY,GAAW,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACjE,IAAI,WAAW,GAAW,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,WAAW,GAAW,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,WAAW,GAAW,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,WAAI,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAGS,WAAW,CACX,GAAQ,EACjB,IAAoB,EAAE,GAAW,EAAE,GAAW,EAC9C,IAAY,EAAE,IAAY,EAAE,IAAY,EACxC,IAAmB;QACnB,IAAI,MAAM,GAAa,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvC,QAAQ,IAAI,EAAE;YACb,oBAA2B,CAAC,CAAC,OAAO,IAAI,qCAAiB,CAAC,MAAM,CAAC,CAAC;YAClE;gBACC,IAAI,IAAI,KAAK,CAAC,EAAE;oBACf,OAAO,IAAI,iCAAe,CAAC,MAAM,EAAE,aAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;iBACpD;qBACI;oBACJ,OAAO,IAAI,iCAAe,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBAC/C;YACF;gBACC,IAAI,EAAE,GAAmB,IAAI,+BAAc,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAmB,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gBACpG,OAAO,EAAE,CAAC;YACX;gBACC,IAAI,EAAE,GAAwB,IAAI,yCAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;gBACtF,OAAO,EAAE,CAAC;YACX;gBACC,OAAO,IAAI,6DAA6B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACxD;gBACC,IAAI,IAAI,KAAK,CAAC,EAAE;oBACf,OAAO,IAAI,+BAAc,CAAC,MAAM,EAAE,aAAK,CAAC,GAAG,CAAC,CAAC;iBAC7C;qBACI;oBACJ,OAAO,IAAI,+BAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACxC;YACF;gBACC,IAAI,CAAC,GAAqB,IAAI,mCAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC/E,OAAO,CAAC,CAAC;YACV,gBAAuB,CAAC,CAAC,OAAO,IAAI,6BAAa,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACtE,oBAA2B,CAAC,CAAC,OAAO,IAAI,mCAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7E,qBAA4B,CAAC,CAAC,OAAO,IAAI,uCAAkB,CAAC,MAAM,CAAC,CAAC;SACpE;QAED,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IAChE,CAAC;IAES,YAAY,CAAC,IAAkB,EAAE,SAAiB;QAC3D,IAAI,CAAW,CAAC;QAChB,QAAQ,IAAI,EAAE;YACb,KAAK,2BAAY,CAAC,YAAY,CAAC,CAAC,OAAO,IAAI,2BAAY,EAAE,CAAC;YAC1D,KAAK,2BAAY,CAAC,KAAK;gBAAE,CAAC,GAAG,IAAI,uBAAU,EAAE,CAAC;gBAAC,MAAM;YACrD,KAAK,2BAAY,CAAC,UAAU;gBAAE,CAAC,GAAG,IAAI,+BAAc,EAAE,CAAC;gBAAC,MAAM;YAC9D,KAAK,2BAAY,CAAC,WAAW;gBAAE,CAAC,GAAG,IAAI,2CAAoB,EAAE,CAAC;gBAAC,MAAM;YACrE,KAAK,2BAAY,CAAC,gBAAgB;gBAAE,CAAC,GAAG,IAAI,yCAAmB,EAAE,CAAC;gBAAC,MAAM;YACzE,KAAK,2BAAY,CAAC,gBAAgB;gBAAE,CAAC,GAAG,IAAI,yCAAmB,EAAE,CAAC;gBAAC,MAAM;YACzE,KAAK,2BAAY,CAAC,WAAW;gBAAE,CAAC,GAAG,IAAI,mCAAgB,EAAE,CAAC;gBAAC,MAAM;YACjE,KAAK,2BAAY,CAAC,SAAS;gBAAE,CAAC,GAAG,IAAI,6BAAa,EAAE,CAAC;gBAAC,MAAM;YAC5D,KAAK,2BAAY,CAAC,SAAS;gBAAE,CAAC,GAAG,IAAI,6BAAa,EAAE,CAAC;gBAAC,MAAM;YAC5D,KAAK,2BAAY,CAAC,cAAc;gBAAE,CAAC,GAAG,IAAI,qCAAiB,EAAE,CAAC;gBAAC,MAAM;YACrE,KAAK,2BAAY,CAAC,eAAe;gBAAE,CAAC,GAAG,IAAI,uCAAkB,EAAE,CAAC;gBAAC,MAAM;YACvE,KAAK,2BAAY,CAAC,cAAc;gBAAE,CAAC,GAAG,IAAI,qCAAiB,EAAE,CAAC;gBAAC,MAAM;YACrE,KAAK,2BAAY,CAAC,QAAQ;gBAAE,CAAC,GAAG,IAAI,2BAAY,EAAE,CAAC;gBAAC,MAAM;YAC1D;gBACC,IAAI,OAAO,GAAW,4BAA4B,IAAI,gBAAgB,CAAC;gBACvE,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QAED,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;QACxB,OAAO,CAAC,CAAC;IACV,CAAC;IAES,kBAAkB,CAAC,IAAqB,EAAE,KAAa,EAAE,KAAa;QAC/E,QAAQ,IAAI,EAAE;YACd;gBACC,OAAO,IAAI,uCAAkB,CAAC,KAAK,CAAC,CAAC;YAEtC;gBACC,OAAO,IAAI,qCAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE5C;gBACC,OAAO,IAAI,iCAAe,CAAC,KAAK,CAAC,CAAC;YAEnC;gBACC,OAAO,iCAAe,CAAC,QAAQ,CAAC;YAEjC;gBACC,OAAO,uCAAkB,CAAC,QAAQ,CAAC;YAEpC;gBACC,OAAO,IAAI,yCAAmB,CAAC,KAAK,CAAC,CAAC;YAEvC;gBACC,OAAO,iCAAe,CAAC,QAAQ,CAAC;YAEjC;gBACC,OAAO,IAAI,iCAAe,CAAC,KAAK,CAAC,CAAC;YAEnC;gBACC,IAAI,OAAO,GAAW,mCAAmC,IAAI,gBAAgB,CAAC;gBAC9E,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SACzB;IACF,CAAC;;AA/nCD;;GAEG;AAEH;;GAEG;AACqB,oCAAoB,GAAS,WAAI,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;AAC7G;;;;GAIG;AACqB,mCAAmB,GAAS,WAAI,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;AAC5G;;;;GAIG;AACqB,iCAAiB,GAAS,WAAI,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;AAC1G;;;GAGG;AACqB,+BAAe,GAAW;IACjD,eAAe,CAAC,oBAAoB;IACpC,eAAe,CAAC,mBAAmB;IACnC,eAAe,CAAC,iBAAiB;CACjC,CAAC;AAEF;;GAEG;AACqB,+BAAe,GAAS,eAAe,CAAC,iBAAiB,CAAC;AAGlF;IADC,oBAAO;+DAC2D;AAkDnE;IAAoB,WAAA,oBAAO,CAAA;kDA6b1B;AAkCD;IAAmC,WAAA,oBAAO,CAAA;8DAyCzC;AAicD;IADC,oBAAO;IAEN,WAAA,oBAAO,CAAA;kDAsCR;AA9kCF,0CAwoCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:25.9683447-07:00\r\n\r\nimport { ActionTransition } from \"./ActionTransition\";\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNDeserializationOptions } from \"./ATNDeserializationOptions\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { ATNType } from \"./ATNType\";\r\nimport { AtomTransition } from \"./AtomTransition\";\r\nimport { BasicBlockStartState } from \"./BasicBlockStartState\";\r\nimport { BasicState } from \"./BasicState\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { BlockEndState } from \"./BlockEndState\";\r\nimport { BlockStartState } from \"./BlockStartState\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { EpsilonTransition } from \"./EpsilonTransition\";\r\nimport { Interval } from \"../misc/Interval\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { InvalidState } from \"./InvalidState\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { LexerChannelAction } from \"./LexerChannelAction\";\r\nimport { LexerCustomAction } from \"./LexerCustomAction\";\r\nimport { LexerModeAction } from \"./LexerModeAction\";\r\nimport { LexerMoreAction } from \"./LexerMoreAction\";\r\nimport { LexerPopModeAction } from \"./LexerPopModeAction\";\r\nimport { LexerPushModeAction } from \"./LexerPushModeAction\";\r\nimport { LexerSkipAction } from \"./LexerSkipAction\";\r\nimport { LexerTypeAction } from \"./LexerTypeAction\";\r\nimport { LoopEndState } from \"./LoopEndState\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { NotSetTransition } from \"./NotSetTransition\";\r\nimport { ParserATNSimulator } from \"./ParserATNSimulator\";\r\nimport { PlusBlockStartState } from \"./PlusBlockStartState\";\r\nimport { PlusLoopbackState } from \"./PlusLoopbackState\";\r\nimport { PrecedencePredicateTransition } from \"./PrecedencePredicateTransition\";\r\nimport { PredicateTransition } from \"./PredicateTransition\";\r\nimport { RangeTransition } from \"./RangeTransition\";\r\nimport { RuleStartState } from \"./RuleStartState\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\nimport { SetTransition } from \"./SetTransition\";\r\nimport { StarBlockStartState } from \"./StarBlockStartState\";\r\nimport { StarLoopbackState } from \"./StarLoopbackState\";\r\nimport { StarLoopEntryState } from \"./StarLoopEntryState\";\r\nimport { Token } from \"../Token\";\r\nimport { TokensStartState } from \"./TokensStartState\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\nimport { UUID } from \"../misc/UUID\";\r\nimport { WildcardTransition } from \"./WildcardTransition\";\r\n\r\ninterface UnicodeDeserializer {\r\n\t// Wrapper for readInt() or readInt32()\r\n\treadUnicode(data: Uint16Array, p: number): number;\r\n\r\n\t// Work around Java not allowing mutation of captured variables\r\n\t// by returning amount by which to increment p after each read\r\n\treadonly size: number;\r\n}\r\n\r\nconst enum UnicodeDeserializingMode {\r\n\tUNICODE_BMP,\r\n\tUNICODE_SMP,\r\n}\r\n\r\n/**\r\n *\r\n * <AUTHOR> Harwell\r\n */\r\nexport class ATNDeserializer {\r\n\tstatic get SERIALIZED_VERSION(): number {\r\n\t\t/* This value should never change. Updates following this version are\r\n\t\t * reflected as change in the unique ID SERIALIZED_UUID.\r\n\t\t */\r\n\t\treturn 3;\r\n\t}\r\n\r\n\t/* WARNING: DO NOT MERGE THESE LINES. If UUIDs differ during a merge,\r\n\t * resolve the conflict by generating a new ID!\r\n\t */\r\n\r\n\t/**\r\n\t * This is the earliest supported serialized UUID.\r\n\t */\r\n\tprivate static readonly BASE_SERIALIZED_UUID: UUID = UUID.fromString(\"E4178468-DF95-44D0-AD87-F22A5D5FB6D3\");\r\n\t/**\r\n\t * This UUID indicates an extension of {@link #ADDED_PRECEDENCE_TRANSITIONS}\r\n\t * for the addition of lexer actions encoded as a sequence of\r\n\t * {@link LexerAction} instances.\r\n\t */\r\n\tprivate static readonly ADDED_LEXER_ACTIONS: UUID = UUID.fromString(\"AB35191A-1603-487E-B75A-479B831EAF6D\");\r\n\t/**\r\n\t * This UUID indicates the serialized ATN contains two sets of\r\n\t * IntervalSets, where the second set's values are encoded as\r\n\t * 32-bit integers to support the full Unicode SMP range up to U+10FFFF.\r\n\t */\r\n\tprivate static readonly ADDED_UNICODE_SMP: UUID = UUID.fromString(\"C23FEA89-0605-4f51-AFB8-058BCAB8C91B\");\r\n\t/**\r\n\t * This list contains all of the currently supported UUIDs, ordered by when\r\n\t * the feature first appeared in this branch.\r\n\t */\r\n\tprivate static readonly SUPPORTED_UUIDS: UUID[] = [\r\n\t\tATNDeserializer.BASE_SERIALIZED_UUID,\r\n\t\tATNDeserializer.ADDED_LEXER_ACTIONS,\r\n\t\tATNDeserializer.ADDED_UNICODE_SMP,\r\n\t];\r\n\r\n\t/**\r\n\t * This is the current serialized UUID.\r\n\t */\r\n\tprivate static readonly SERIALIZED_UUID: UUID = ATNDeserializer.ADDED_UNICODE_SMP;\r\n\r\n\t@NotNull\r\n\tprivate readonly deserializationOptions: ATNDeserializationOptions;\r\n\r\n\tconstructor(deserializationOptions?: ATNDeserializationOptions) {\r\n\t\tif (deserializationOptions === undefined) {\r\n\t\t\tdeserializationOptions = ATNDeserializationOptions.defaultOptions;\r\n\t\t}\r\n\r\n\t\tthis.deserializationOptions = deserializationOptions;\r\n\t}\r\n\r\n\t/**\r\n\t * Determines if a particular serialized representation of an ATN supports\r\n\t * a particular feature, identified by the {@link UUID} used for serializing\r\n\t * the ATN at the time the feature was first introduced.\r\n\t *\r\n\t * @param feature The {@link UUID} marking the first time the feature was\r\n\t * supported in the serialized ATN.\r\n\t * @param actualUuid The {@link UUID} of the actual serialized ATN which is\r\n\t * currently being deserialized.\r\n\t * @returns `true` if the `actualUuid` value represents a\r\n\t * serialized ATN at or after the feature identified by `feature` was\r\n\t * introduced; otherwise, `false`.\r\n\t */\r\n\tprotected static isFeatureSupported(feature: UUID, actualUuid: UUID): boolean {\r\n\t\tlet featureIndex: number = ATNDeserializer.SUPPORTED_UUIDS.findIndex((e) => e.equals(feature));\r\n\t\tif (featureIndex < 0) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn ATNDeserializer.SUPPORTED_UUIDS.findIndex((e) => e.equals(actualUuid)) >= featureIndex;\r\n\t}\r\n\r\n\tprivate static getUnicodeDeserializer(mode: UnicodeDeserializingMode): UnicodeDeserializer {\r\n\t\tif (mode === UnicodeDeserializingMode.UNICODE_BMP) {\r\n\t\t\treturn {\r\n\t\t\t\treadUnicode: (data: Uint16Array, p: number): number => {\r\n\t\t\t\t\treturn ATNDeserializer.toInt(data[p]);\r\n\t\t\t\t},\r\n\t\t\t\tsize: 1,\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\treturn {\r\n\t\t\t\treadUnicode: (data: Uint16Array, p: number): number => {\r\n\t\t\t\t\treturn ATNDeserializer.toInt32(data, p);\r\n\t\t\t\t},\r\n\t\t\t\tsize: 2,\r\n\t\t\t};\r\n\t\t}\r\n\t}\r\n\r\n\tpublic deserialize(@NotNull data: Uint16Array): ATN {\r\n\t\tdata = data.slice(0);\r\n\r\n\t\t// Each Uint16 value in data is shifted by +2 at the entry to this method. This is an encoding optimization\r\n\t\t// targeting the serialized values 0 and -1 (serialized to 0xFFFF), each of which are very common in the\r\n\t\t// serialized form of the ATN. In the modified UTF-8 that Java uses for compiled string literals, these two\r\n\t\t// character values have multi-byte forms. By shifting each value by +2, they become characters 2 and 1 prior to\r\n\t\t// writing the string, each of which have single-byte representations. Since the shift occurs in the tool during\r\n\t\t// ATN serialization, each target is responsible for adjusting the values during deserialization.\r\n\t\t//\r\n\t\t// As a special case, note that the first element of data is not adjusted because it contains the major version\r\n\t\t// number of the serialized ATN, which was fixed at 3 at the time the value shifting was implemented.\r\n\t\tfor (let i = 1; i < data.length; i++) {\r\n\t\t\tdata[i] = (data[i] - 2) & 0xFFFF;\r\n\t\t}\r\n\r\n\t\tlet p: number = 0;\r\n\t\tlet version: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tif (version !== ATNDeserializer.SERIALIZED_VERSION) {\r\n\t\t\tlet reason = `Could not deserialize ATN with version ${version} (expected ${ATNDeserializer.SERIALIZED_VERSION}).`;\r\n\t\t\tthrow new Error(reason);\r\n\t\t}\r\n\r\n\t\tlet uuid: UUID = ATNDeserializer.toUUID(data, p);\r\n\t\tp += 8;\r\n\t\tif (ATNDeserializer.SUPPORTED_UUIDS.findIndex((e) => e.equals(uuid)) < 0) {\r\n\t\t\tlet reason = `Could not deserialize ATN with UUID ${uuid} (expected ${ATNDeserializer.SERIALIZED_UUID} or a legacy UUID).`;\r\n\t\t\tthrow new Error(reason);\r\n\t\t}\r\n\r\n\t\tlet supportsLexerActions: boolean = ATNDeserializer.isFeatureSupported(ATNDeserializer.ADDED_LEXER_ACTIONS, uuid);\r\n\r\n\t\tlet grammarType: ATNType = ATNDeserializer.toInt(data[p++]);\r\n\t\tlet maxTokenType: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tlet atn: ATN = new ATN(grammarType, maxTokenType);\r\n\r\n\t\t//\r\n\t\t// STATES\r\n\t\t//\r\n\t\tlet loopBackStateNumbers: Array<[LoopEndState, number]> = [];\r\n\t\tlet endStateNumbers: Array<[BlockStartState, number]> = [];\r\n\t\tlet nstates: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < nstates; i++) {\r\n\t\t\tlet stype: ATNStateType = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t// ignore bad type of states\r\n\t\t\tif (stype === ATNStateType.INVALID_TYPE) {\r\n\t\t\t\tatn.addState(new InvalidState());\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet ruleIndex: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\tif (ruleIndex === 0xFFFF) {\r\n\t\t\t\truleIndex = -1;\r\n\t\t\t}\r\n\r\n\t\t\tlet s: ATNState = this.stateFactory(stype, ruleIndex);\r\n\t\t\tif (stype === ATNStateType.LOOP_END) { // special case\r\n\t\t\t\tlet loopBackStateNumber: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\tloopBackStateNumbers.push([s as LoopEndState, loopBackStateNumber]);\r\n\t\t\t}\r\n\t\t\telse if (s instanceof BlockStartState) {\r\n\t\t\t\tlet endStateNumber: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\tendStateNumbers.push([s, endStateNumber]);\r\n\t\t\t}\r\n\t\t\tatn.addState(s);\r\n\t\t}\r\n\r\n\t\t// delay the assignment of loop back and end states until we know all the state instances have been initialized\r\n\t\tfor (let pair of loopBackStateNumbers) {\r\n\t\t\tpair[0].loopBackState = atn.states[pair[1]];\r\n\t\t}\r\n\r\n\t\tfor (let pair of endStateNumbers) {\r\n\t\t\tpair[0].endState = atn.states[pair[1]] as BlockEndState;\r\n\t\t}\r\n\r\n\t\tlet numNonGreedyStates: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < numNonGreedyStates; i++) {\r\n\t\t\tlet stateNumber: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t(atn.states[stateNumber] as DecisionState).nonGreedy = true;\r\n\t\t}\r\n\r\n\t\tlet numSllDecisions: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < numSllDecisions; i++) {\r\n\t\t\tlet stateNumber: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t(atn.states[stateNumber] as DecisionState).sll = true;\r\n\t\t}\r\n\r\n\t\tlet numPrecedenceStates: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < numPrecedenceStates; i++) {\r\n\t\t\tlet stateNumber: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t(atn.states[stateNumber] as RuleStartState).isPrecedenceRule = true;\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// RULES\r\n\t\t//\r\n\t\tlet nrules: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tif (atn.grammarType === ATNType.LEXER) {\r\n\t\t\tatn.ruleToTokenType = new Int32Array(nrules);\r\n\t\t}\r\n\r\n\t\tatn.ruleToStartState = new Array<RuleStartState>(nrules);\r\n\t\tfor (let i = 0; i < nrules; i++) {\r\n\t\t\tlet s: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\tlet startState: RuleStartState = atn.states[s] as RuleStartState;\r\n\t\t\tstartState.leftFactored = ATNDeserializer.toInt(data[p++]) !== 0;\r\n\t\t\tatn.ruleToStartState[i] = startState;\r\n\t\t\tif (atn.grammarType === ATNType.LEXER) {\r\n\t\t\t\tlet tokenType: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\tif (tokenType === 0xFFFF) {\r\n\t\t\t\t\ttokenType = Token.EOF;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tatn.ruleToTokenType[i] = tokenType;\r\n\r\n\t\t\t\tif (!ATNDeserializer.isFeatureSupported(ATNDeserializer.ADDED_LEXER_ACTIONS, uuid)) {\r\n\t\t\t\t\t// this piece of unused metadata was serialized prior to the\r\n\t\t\t\t\t// addition of LexerAction\r\n\t\t\t\t\tlet actionIndexIgnored: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\t\tif (actionIndexIgnored === 0xFFFF) {\r\n\t\t\t\t\t\tactionIndexIgnored = -1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tatn.ruleToStopState = new Array<RuleStopState>(nrules);\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tif (!(state instanceof RuleStopState)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tatn.ruleToStopState[state.ruleIndex] = state;\r\n\t\t\tatn.ruleToStartState[state.ruleIndex].stopState = state;\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// MODES\r\n\t\t//\r\n\t\tlet nmodes: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < nmodes; i++) {\r\n\t\t\tlet s: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\tatn.modeToStartState.push(atn.states[s] as TokensStartState);\r\n\t\t}\r\n\r\n\t\tatn.modeToDFA = new Array<DFA>(nmodes);\r\n\t\tfor (let i = 0; i < nmodes; i++) {\r\n\t\t\tatn.modeToDFA[i] = new DFA(atn.modeToStartState[i]);\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// SETS\r\n\t\t//\r\n\t\tlet sets: IntervalSet[] = [];\r\n\r\n\t\t// First, read all sets with 16-bit Unicode code points <= U+FFFF.\r\n\t\tp = this.deserializeSets(data, p, sets, ATNDeserializer.getUnicodeDeserializer(UnicodeDeserializingMode.UNICODE_BMP));\r\n\r\n\t\t// Next, if the ATN was serialized with the Unicode SMP feature,\r\n\t\t// deserialize sets with 32-bit arguments <= U+10FFFF.\r\n\t\tif (ATNDeserializer.isFeatureSupported(ATNDeserializer.ADDED_UNICODE_SMP, uuid)) {\r\n\t\t\tp = this.deserializeSets(data, p, sets, ATNDeserializer.getUnicodeDeserializer(UnicodeDeserializingMode.UNICODE_SMP));\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// EDGES\r\n\t\t//\r\n\t\tlet nedges: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < nedges; i++) {\r\n\t\t\tlet src: number = ATNDeserializer.toInt(data[p]);\r\n\t\t\tlet trg: number = ATNDeserializer.toInt(data[p + 1]);\r\n\t\t\tlet ttype: number = ATNDeserializer.toInt(data[p + 2]);\r\n\t\t\tlet arg1: number = ATNDeserializer.toInt(data[p + 3]);\r\n\t\t\tlet arg2: number = ATNDeserializer.toInt(data[p + 4]);\r\n\t\t\tlet arg3: number = ATNDeserializer.toInt(data[p + 5]);\r\n\t\t\tlet trans: Transition = this.edgeFactory(atn, ttype, src, trg, arg1, arg2, arg3, sets);\r\n\t\t\t// console.log(`EDGE ${trans.constructor.name} ${src}->${trg} ${Transition.serializationNames[ttype]} ${arg1},${arg2},${arg3}`);\r\n\t\t\tlet srcState: ATNState = atn.states[src];\r\n\t\t\tsrcState.addTransition(trans);\r\n\t\t\tp += 6;\r\n\t\t}\r\n\r\n\t\t// edges for rule stop states can be derived, so they aren't serialized\r\n\t\tinterface T { stopState: number; returnState: number; outermostPrecedenceReturn: number; }\r\n\t\tlet returnTransitionsSet = new Array2DHashSet<T>({\r\n\t\t\thashCode: (o: T) => o.stopState ^ o.returnState ^ o.outermostPrecedenceReturn,\r\n\r\n\t\t\tequals: (a: T, b: T): boolean => {\r\n\t\t\t\treturn a.stopState === b.stopState\r\n\t\t\t\t\t&& a.returnState === b.returnState\r\n\t\t\t\t\t&& a.outermostPrecedenceReturn === b.outermostPrecedenceReturn;\r\n\t\t\t},\r\n\t\t});\r\n\t\tlet returnTransitions: T[] = [];\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tlet returningToLeftFactored: boolean = state.ruleIndex >= 0 && atn.ruleToStartState[state.ruleIndex].leftFactored;\r\n\t\t\tfor (let i = 0; i < state.numberOfTransitions; i++) {\r\n\t\t\t\tlet t: Transition = state.transition(i);\r\n\t\t\t\tif (!(t instanceof RuleTransition)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet ruleTransition: RuleTransition = t;\r\n\t\t\t\tlet returningFromLeftFactored: boolean = atn.ruleToStartState[ruleTransition.target.ruleIndex].leftFactored;\r\n\t\t\t\tif (!returningFromLeftFactored && returningToLeftFactored) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet outermostPrecedenceReturn: number = -1;\r\n\t\t\t\tif (atn.ruleToStartState[ruleTransition.target.ruleIndex].isPrecedenceRule) {\r\n\t\t\t\t\tif (ruleTransition.precedence === 0) {\r\n\t\t\t\t\t\toutermostPrecedenceReturn = ruleTransition.target.ruleIndex;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet current = { stopState: ruleTransition.target.ruleIndex, returnState: ruleTransition.followState.stateNumber, outermostPrecedenceReturn };\r\n\t\t\t\tif (returnTransitionsSet.add(current)) {\r\n\t\t\t\t\treturnTransitions.push(current);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Add all elements from returnTransitions to the ATN\r\n\t\tfor (let returnTransition of returnTransitions) {\r\n\t\t\tlet transition = new EpsilonTransition(atn.states[returnTransition.returnState], returnTransition.outermostPrecedenceReturn);\r\n\t\t\tatn.ruleToStopState[returnTransition.stopState].addTransition(transition);\r\n\t\t}\r\n\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tif (state instanceof BlockStartState) {\r\n\t\t\t\t// we need to know the end state to set its start state\r\n\t\t\t\tif (state.endState === undefined) {\r\n\t\t\t\t\tthrow new Error(\"IllegalStateException\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// block end states can only be associated to a single block start state\r\n\t\t\t\tif (state.endState.startState !== undefined) {\r\n\t\t\t\t\tthrow new Error(\"IllegalStateException\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tstate.endState.startState = state;\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof PlusLoopbackState) {\r\n\t\t\t\tlet loopbackState: PlusLoopbackState = state;\r\n\t\t\t\tfor (let i = 0; i < loopbackState.numberOfTransitions; i++) {\r\n\t\t\t\t\tlet target: ATNState = loopbackState.transition(i).target;\r\n\t\t\t\t\tif (target instanceof PlusBlockStartState) {\r\n\t\t\t\t\t\ttarget.loopBackState = loopbackState;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse if (state instanceof StarLoopbackState) {\r\n\t\t\t\tlet loopbackState: StarLoopbackState = state;\r\n\t\t\t\tfor (let i = 0; i < loopbackState.numberOfTransitions; i++) {\r\n\t\t\t\t\tlet target: ATNState = loopbackState.transition(i).target;\r\n\t\t\t\t\tif (target instanceof StarLoopEntryState) {\r\n\t\t\t\t\t\ttarget.loopBackState = loopbackState;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// DECISIONS\r\n\t\t//\r\n\t\tlet ndecisions: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 1; i <= ndecisions; i++) {\r\n\t\t\tlet s: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\tlet decState: DecisionState = atn.states[s] as DecisionState;\r\n\t\t\tatn.decisionToState.push(decState);\r\n\t\t\tdecState.decision = i - 1;\r\n\t\t}\r\n\r\n\t\t//\r\n\t\t// LEXER ACTIONS\r\n\t\t//\r\n\t\tif (atn.grammarType === ATNType.LEXER) {\r\n\t\t\tif (supportsLexerActions) {\r\n\t\t\t\tatn.lexerActions = new Array<LexerAction>(ATNDeserializer.toInt(data[p++]));\r\n\t\t\t\tfor (let i = 0; i < atn.lexerActions.length; i++) {\r\n\t\t\t\t\tlet actionType: LexerActionType = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\t\tlet data1: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\t\tif (data1 === 0xFFFF) {\r\n\t\t\t\t\t\tdata1 = -1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet data2: number = ATNDeserializer.toInt(data[p++]);\r\n\t\t\t\t\tif (data2 === 0xFFFF) {\r\n\t\t\t\t\t\tdata2 = -1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet lexerAction: LexerAction = this.lexerActionFactory(actionType, data1, data2);\r\n\r\n\t\t\t\t\tatn.lexerActions[i] = lexerAction;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\t// for compatibility with older serialized ATNs, convert the old\r\n\t\t\t\t// serialized action index for action transitions to the new\r\n\t\t\t\t// form, which is the index of a LexerCustomAction\r\n\t\t\t\tlet legacyLexerActions: LexerAction[] = [];\r\n\t\t\t\tfor (let state of atn.states) {\r\n\t\t\t\t\tfor (let i = 0; i < state.numberOfTransitions; i++) {\r\n\t\t\t\t\t\tlet transition: Transition = state.transition(i);\r\n\t\t\t\t\t\tif (!(transition instanceof ActionTransition)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tlet ruleIndex: number = transition.ruleIndex;\r\n\t\t\t\t\t\tlet actionIndex: number = transition.actionIndex;\r\n\t\t\t\t\t\tlet lexerAction: LexerCustomAction = new LexerCustomAction(ruleIndex, actionIndex);\r\n\t\t\t\t\t\tstate.setTransition(i, new ActionTransition(transition.target, ruleIndex, legacyLexerActions.length, false));\r\n\t\t\t\t\t\tlegacyLexerActions.push(lexerAction);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tatn.lexerActions = legacyLexerActions;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.markPrecedenceDecisions(atn);\r\n\r\n\t\tatn.decisionToDFA = new Array<DFA>(ndecisions);\r\n\t\tfor (let i = 0; i < ndecisions; i++) {\r\n\t\t\tatn.decisionToDFA[i] = new DFA(atn.decisionToState[i], i);\r\n\t\t}\r\n\r\n\t\tif (this.deserializationOptions.isVerifyATN) {\r\n\t\t\tthis.verifyATN(atn);\r\n\t\t}\r\n\r\n\t\tif (this.deserializationOptions.isGenerateRuleBypassTransitions && atn.grammarType === ATNType.PARSER) {\r\n\t\t\tatn.ruleToTokenType = new Int32Array(atn.ruleToStartState.length);\r\n\t\t\tfor (let i = 0; i < atn.ruleToStartState.length; i++) {\r\n\t\t\t\tatn.ruleToTokenType[i] = atn.maxTokenType + i + 1;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let i = 0; i < atn.ruleToStartState.length; i++) {\r\n\t\t\t\tlet bypassStart: BasicBlockStartState = new BasicBlockStartState();\r\n\t\t\t\tbypassStart.ruleIndex = i;\r\n\t\t\t\tatn.addState(bypassStart);\r\n\r\n\t\t\t\tlet bypassStop: BlockEndState = new BlockEndState();\r\n\t\t\t\tbypassStop.ruleIndex = i;\r\n\t\t\t\tatn.addState(bypassStop);\r\n\r\n\t\t\t\tbypassStart.endState = bypassStop;\r\n\t\t\t\tatn.defineDecisionState(bypassStart);\r\n\r\n\t\t\t\tbypassStop.startState = bypassStart;\r\n\r\n\t\t\t\tlet endState: ATNState | undefined;\r\n\t\t\t\tlet excludeTransition: Transition | undefined;\r\n\t\t\t\tif (atn.ruleToStartState[i].isPrecedenceRule) {\r\n\t\t\t\t\t// wrap from the beginning of the rule to the StarLoopEntryState\r\n\t\t\t\t\tendState = undefined;\r\n\t\t\t\t\tfor (let state of atn.states) {\r\n\t\t\t\t\t\tif (state.ruleIndex !== i) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (!(state instanceof StarLoopEntryState)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tlet maybeLoopEndState: ATNState = state.transition(state.numberOfTransitions - 1).target;\r\n\t\t\t\t\t\tif (!(maybeLoopEndState instanceof LoopEndState)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (maybeLoopEndState.epsilonOnlyTransitions && maybeLoopEndState.transition(0).target instanceof RuleStopState) {\r\n\t\t\t\t\t\t\tendState = state;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (!endState) {\r\n\t\t\t\t\t\tthrow new Error(\"Couldn't identify final state of the precedence rule prefix section.\");\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\texcludeTransition = (endState as StarLoopEntryState).loopBackState.transition(0);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tendState = atn.ruleToStopState[i];\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// all non-excluded transitions that currently target end state need to target blockEnd instead\r\n\t\t\t\tfor (let state of atn.states) {\r\n\t\t\t\t\tfor (let i = 0; i < state.numberOfTransitions; i++) {\r\n\t\t\t\t\t\tlet transition = state.transition(i);\r\n\t\t\t\t\t\tif (transition === excludeTransition) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (transition.target === endState) {\r\n\t\t\t\t\t\t\ttransition.target = bypassStop;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// all transitions leaving the rule start state need to leave blockStart instead\r\n\t\t\t\twhile (atn.ruleToStartState[i].numberOfTransitions > 0) {\r\n\t\t\t\t\tlet transition: Transition = atn.ruleToStartState[i].removeTransition(atn.ruleToStartState[i].numberOfTransitions - 1);\r\n\t\t\t\t\tbypassStart.addTransition(transition);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// link the new states\r\n\t\t\t\tatn.ruleToStartState[i].addTransition(new EpsilonTransition(bypassStart));\r\n\t\t\t\tbypassStop.addTransition(new EpsilonTransition(endState));\r\n\r\n\t\t\t\tlet matchState: ATNState = new BasicState();\r\n\t\t\t\tatn.addState(matchState);\r\n\t\t\t\tmatchState.addTransition(new AtomTransition(bypassStop, atn.ruleToTokenType[i]));\r\n\t\t\t\tbypassStart.addTransition(new EpsilonTransition(matchState));\r\n\t\t\t}\r\n\r\n\t\t\tif (this.deserializationOptions.isVerifyATN) {\r\n\t\t\t\t// reverify after modification\r\n\t\t\t\tthis.verifyATN(atn);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this.deserializationOptions.isOptimize) {\r\n\t\t\twhile (true) {\r\n\t\t\t\tlet optimizationCount: number = 0;\r\n\t\t\t\toptimizationCount += ATNDeserializer.inlineSetRules(atn);\r\n\t\t\t\toptimizationCount += ATNDeserializer.combineChainedEpsilons(atn);\r\n\t\t\t\tlet preserveOrder: boolean = atn.grammarType === ATNType.LEXER;\r\n\t\t\t\toptimizationCount += ATNDeserializer.optimizeSets(atn, preserveOrder);\r\n\t\t\t\tif (optimizationCount === 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (this.deserializationOptions.isVerifyATN) {\r\n\t\t\t\t// reverify after modification\r\n\t\t\t\tthis.verifyATN(atn);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tATNDeserializer.identifyTailCalls(atn);\r\n\r\n\t\treturn atn;\r\n\t}\r\n\r\n\tprivate deserializeSets(data: Uint16Array, p: number, sets: IntervalSet[], unicodeDeserializer: UnicodeDeserializer): number {\r\n\t\tlet nsets: number = ATNDeserializer.toInt(data[p++]);\r\n\t\tfor (let i = 0; i < nsets; i++) {\r\n\t\t\tlet nintervals: number = ATNDeserializer.toInt(data[p]);\r\n\t\t\tp++;\r\n\t\t\tlet set: IntervalSet = new IntervalSet();\r\n\t\t\tsets.push(set);\r\n\r\n\t\t\tlet containsEof: boolean = ATNDeserializer.toInt(data[p++]) !== 0;\r\n\t\t\tif (containsEof) {\r\n\t\t\t\tset.add(-1);\r\n\t\t\t}\r\n\r\n\t\t\tfor (let j: number = 0; j < nintervals; j++) {\r\n\t\t\t\tlet a: number = unicodeDeserializer.readUnicode(data, p);\r\n\t\t\t\tp += unicodeDeserializer.size;\r\n\t\t\t\tlet b: number = unicodeDeserializer.readUnicode(data, p);\r\n\t\t\t\tp += unicodeDeserializer.size;\r\n\t\t\t\tset.add(a, b);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn p;\r\n\t}\r\n\r\n\t/**\r\n\t * Analyze the {@link StarLoopEntryState} states in the specified ATN to set\r\n\t * the {@link StarLoopEntryState#precedenceRuleDecision} field to the\r\n\t * correct value.\r\n\t *\r\n\t * @param atn The ATN.\r\n\t */\r\n\tprotected markPrecedenceDecisions(@NotNull atn: ATN): void {\r\n\t\t// Map rule index -> precedence decision for that rule\r\n\t\tlet rulePrecedenceDecisions = new Map<number, StarLoopEntryState>();\r\n\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tif (!(state instanceof StarLoopEntryState)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t/* We analyze the ATN to determine if this ATN decision state is the\r\n\t\t\t * decision for the closure block that determines whether a\r\n\t\t\t * precedence rule should continue or complete.\r\n\t\t\t */\r\n\t\t\tif (atn.ruleToStartState[state.ruleIndex].isPrecedenceRule) {\r\n\t\t\t\tlet maybeLoopEndState: ATNState = state.transition(state.numberOfTransitions - 1).target;\r\n\t\t\t\tif (maybeLoopEndState instanceof LoopEndState) {\r\n\t\t\t\t\tif (maybeLoopEndState.epsilonOnlyTransitions && maybeLoopEndState.transition(0).target instanceof RuleStopState) {\r\n\t\t\t\t\t\trulePrecedenceDecisions.set(state.ruleIndex, state);\r\n\t\t\t\t\t\tstate.precedenceRuleDecision = true;\r\n\t\t\t\t\t\tstate.precedenceLoopbackStates = new BitSet(atn.states.length);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// After marking precedence decisions, we go back through and fill in\r\n\t\t// StarLoopEntryState.precedenceLoopbackStates.\r\n\t\tfor (let precedenceDecision of rulePrecedenceDecisions) {\r\n\t\t\tfor (let transition of atn.ruleToStopState[precedenceDecision[0]].getTransitions()) {\r\n\t\t\t\tif (transition.serializationType !== TransitionType.EPSILON) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet epsilonTransition = transition as EpsilonTransition;\r\n\t\t\t\tif (epsilonTransition.outermostPrecedenceReturn !== -1) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tprecedenceDecision[1].precedenceLoopbackStates.set(transition.target.stateNumber);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected verifyATN(atn: ATN): void {\r\n\t\t// verify assumptions\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tthis.checkCondition(state !== undefined, \"ATN states should not be undefined.\");\r\n\t\t\tif (state.stateType === ATNStateType.INVALID_TYPE) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tthis.checkCondition(state.onlyHasEpsilonTransitions || state.numberOfTransitions <= 1);\r\n\r\n\t\t\tif (state instanceof PlusBlockStartState) {\r\n\t\t\t\tthis.checkCondition(state.loopBackState !== undefined);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof StarLoopEntryState) {\r\n\t\t\t\tlet starLoopEntryState: StarLoopEntryState = state;\r\n\t\t\t\tthis.checkCondition(starLoopEntryState.loopBackState !== undefined);\r\n\t\t\t\tthis.checkCondition(starLoopEntryState.numberOfTransitions === 2);\r\n\r\n\t\t\t\tif (starLoopEntryState.transition(0).target instanceof StarBlockStartState) {\r\n\t\t\t\t\tthis.checkCondition(starLoopEntryState.transition(1).target instanceof LoopEndState);\r\n\t\t\t\t\tthis.checkCondition(!starLoopEntryState.nonGreedy);\r\n\t\t\t\t}\r\n\t\t\t\telse if (starLoopEntryState.transition(0).target instanceof LoopEndState) {\r\n\t\t\t\t\tthis.checkCondition(starLoopEntryState.transition(1).target instanceof StarBlockStartState);\r\n\t\t\t\t\tthis.checkCondition(starLoopEntryState.nonGreedy);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tthrow new Error(\"IllegalStateException\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof StarLoopbackState) {\r\n\t\t\t\tthis.checkCondition(state.numberOfTransitions === 1);\r\n\t\t\t\tthis.checkCondition(state.transition(0).target instanceof StarLoopEntryState);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof LoopEndState) {\r\n\t\t\t\tthis.checkCondition(state.loopBackState !== undefined);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof RuleStartState) {\r\n\t\t\t\tthis.checkCondition(state.stopState !== undefined);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof BlockStartState) {\r\n\t\t\t\tthis.checkCondition(state.endState !== undefined);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof BlockEndState) {\r\n\t\t\t\tthis.checkCondition(state.startState !== undefined);\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof DecisionState) {\r\n\t\t\t\tlet decisionState: DecisionState = state;\r\n\t\t\t\tthis.checkCondition(decisionState.numberOfTransitions <= 1 || decisionState.decision >= 0);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tthis.checkCondition(state.numberOfTransitions <= 1 || state instanceof RuleStopState);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected checkCondition(condition: boolean, message?: string): void {\r\n\t\tif (!condition) {\r\n\t\t\tthrow new Error(\"IllegalStateException: \" + message);\r\n\t\t}\r\n\t}\r\n\r\n\tprivate static inlineSetRules(atn: ATN): number {\r\n\t\tlet inlinedCalls: number = 0;\r\n\r\n\t\tlet ruleToInlineTransition = new Array<Transition | undefined>(atn.ruleToStartState.length);\r\n\t\tfor (let i = 0; i < atn.ruleToStartState.length; i++) {\r\n\t\t\tlet startState: RuleStartState = atn.ruleToStartState[i];\r\n\t\t\tlet middleState: ATNState = startState;\r\n\t\t\twhile (middleState.onlyHasEpsilonTransitions\r\n\t\t\t\t&& middleState.numberOfOptimizedTransitions === 1\r\n\t\t\t\t&& middleState.getOptimizedTransition(0).serializationType === TransitionType.EPSILON) {\r\n\t\t\t\tmiddleState = middleState.getOptimizedTransition(0).target;\r\n\t\t\t}\r\n\r\n\t\t\tif (middleState.numberOfOptimizedTransitions !== 1) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet matchTransition: Transition = middleState.getOptimizedTransition(0);\r\n\t\t\tlet matchTarget: ATNState = matchTransition.target;\r\n\t\t\tif (matchTransition.isEpsilon\r\n\t\t\t\t|| !matchTarget.onlyHasEpsilonTransitions\r\n\t\t\t\t|| matchTarget.numberOfOptimizedTransitions !== 1\r\n\t\t\t\t|| !(matchTarget.getOptimizedTransition(0).target instanceof RuleStopState)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tswitch (matchTransition.serializationType) {\r\n\t\t\tcase TransitionType.ATOM:\r\n\t\t\tcase TransitionType.RANGE:\r\n\t\t\tcase TransitionType.SET:\r\n\t\t\t\truleToInlineTransition[i] = matchTransition;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase TransitionType.NOT_SET:\r\n\t\t\tcase TransitionType.WILDCARD:\r\n\t\t\t\t// not implemented yet\r\n\t\t\t\tcontinue;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tif (state.ruleIndex < 0) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet optimizedTransitions: Transition[] | undefined;\r\n\t\t\tfor (let i = 0; i < state.numberOfOptimizedTransitions; i++) {\r\n\t\t\t\tlet transition: Transition = state.getOptimizedTransition(i);\r\n\t\t\t\tif (!(transition instanceof RuleTransition)) {\r\n\t\t\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\t\t\toptimizedTransitions.push(transition);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet ruleTransition: RuleTransition = transition;\r\n\t\t\t\tlet effective: Transition | undefined = ruleToInlineTransition[ruleTransition.target.ruleIndex];\r\n\t\t\t\tif (effective === undefined) {\r\n\t\t\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\t\t\toptimizedTransitions.push(transition);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (optimizedTransitions === undefined) {\r\n\t\t\t\t\toptimizedTransitions = [];\r\n\t\t\t\t\tfor (let j = 0; j < i; j++) {\r\n\t\t\t\t\t\toptimizedTransitions.push(state.getOptimizedTransition(i));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinlinedCalls++;\r\n\t\t\t\tlet target: ATNState = ruleTransition.followState;\r\n\t\t\t\tlet intermediateState: ATNState = new BasicState();\r\n\t\t\t\tintermediateState.setRuleIndex(target.ruleIndex);\r\n\t\t\t\tatn.addState(intermediateState);\r\n\t\t\t\toptimizedTransitions.push(new EpsilonTransition(intermediateState));\r\n\r\n\t\t\t\tswitch (effective.serializationType) {\r\n\t\t\t\tcase TransitionType.ATOM:\r\n\t\t\t\t\tintermediateState.addTransition(new AtomTransition(target, (effective as AtomTransition)._label));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase TransitionType.RANGE:\r\n\t\t\t\t\tintermediateState.addTransition(new RangeTransition(target, (effective as RangeTransition).from, (effective as RangeTransition).to));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase TransitionType.SET:\r\n\t\t\t\t\tintermediateState.addTransition(new SetTransition(target, (effective as SetTransition).label));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthrow new Error(\"UnsupportedOperationException\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\tif (state.isOptimized) {\r\n\t\t\t\t\twhile (state.numberOfOptimizedTransitions > 0) {\r\n\t\t\t\t\t\tstate.removeOptimizedTransition(state.numberOfOptimizedTransitions - 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let transition of optimizedTransitions) {\r\n\t\t\t\t\tstate.addOptimizedTransition(transition);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"ATN runtime optimizer removed \" + inlinedCalls + \" rule invocations by inlining sets.\");\r\n\t\t}\r\n\r\n\t\treturn inlinedCalls;\r\n\t}\r\n\r\n\tprivate static combineChainedEpsilons(atn: ATN): number {\r\n\t\tlet removedEdges: number = 0;\r\n\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tif (!state.onlyHasEpsilonTransitions || state instanceof RuleStopState) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet optimizedTransitions: Transition[] | undefined;\r\n\t\t\tnextTransition:\r\n\t\t\tfor (let i = 0; i < state.numberOfOptimizedTransitions; i++) {\r\n\t\t\t\tlet transition: Transition = state.getOptimizedTransition(i);\r\n\t\t\t\tlet intermediate: ATNState = transition.target;\r\n\t\t\t\tif (transition.serializationType !== TransitionType.EPSILON\r\n\t\t\t\t\t|| (transition as EpsilonTransition).outermostPrecedenceReturn !== -1\r\n\t\t\t\t\t|| intermediate.stateType !== ATNStateType.BASIC\r\n\t\t\t\t\t|| !intermediate.onlyHasEpsilonTransitions) {\r\n\t\t\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\t\t\toptimizedTransitions.push(transition);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcontinue nextTransition;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let j = 0; j < intermediate.numberOfOptimizedTransitions; j++) {\r\n\t\t\t\t\tif (intermediate.getOptimizedTransition(j).serializationType !== TransitionType.EPSILON\r\n\t\t\t\t\t\t|| (intermediate.getOptimizedTransition(j) as EpsilonTransition).outermostPrecedenceReturn !== -1) {\r\n\t\t\t\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\t\t\t\toptimizedTransitions.push(transition);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tcontinue nextTransition;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tremovedEdges++;\r\n\t\t\t\tif (optimizedTransitions === undefined) {\r\n\t\t\t\t\toptimizedTransitions = [];\r\n\t\t\t\t\tfor (let j = 0; j < i; j++) {\r\n\t\t\t\t\t\toptimizedTransitions.push(state.getOptimizedTransition(j));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let j = 0; j < intermediate.numberOfOptimizedTransitions; j++) {\r\n\t\t\t\t\tlet target: ATNState = intermediate.getOptimizedTransition(j).target;\r\n\t\t\t\t\toptimizedTransitions.push(new EpsilonTransition(target));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (optimizedTransitions !== undefined) {\r\n\t\t\t\tif (state.isOptimized) {\r\n\t\t\t\t\twhile (state.numberOfOptimizedTransitions > 0) {\r\n\t\t\t\t\t\tstate.removeOptimizedTransition(state.numberOfOptimizedTransitions - 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let transition of optimizedTransitions) {\r\n\t\t\t\t\tstate.addOptimizedTransition(transition);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"ATN runtime optimizer removed \" + removedEdges + \" transitions by combining chained epsilon transitions.\");\r\n\t\t}\r\n\r\n\t\treturn removedEdges;\r\n\t}\r\n\r\n\tprivate static optimizeSets(atn: ATN, preserveOrder: boolean): number {\r\n\t\tif (preserveOrder) {\r\n\t\t\t// this optimization currently doesn't preserve edge order.\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\tlet removedPaths: number = 0;\r\n\t\tlet decisions: DecisionState[] = atn.decisionToState;\r\n\t\tfor (let decision of decisions) {\r\n\t\t\tlet setTransitions: IntervalSet = new IntervalSet();\r\n\t\t\tfor (let i = 0; i < decision.numberOfOptimizedTransitions; i++) {\r\n\t\t\t\tlet epsTransition: Transition = decision.getOptimizedTransition(i);\r\n\t\t\t\tif (!(epsTransition instanceof EpsilonTransition)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (epsTransition.target.numberOfOptimizedTransitions !== 1) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet transition: Transition = epsTransition.target.getOptimizedTransition(0);\r\n\t\t\t\tif (!(transition.target instanceof BlockEndState)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (transition instanceof NotSetTransition) {\r\n\t\t\t\t\t// TODO: not yet implemented\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (transition instanceof AtomTransition\r\n\t\t\t\t\t|| transition instanceof RangeTransition\r\n\t\t\t\t\t|| transition instanceof SetTransition) {\r\n\t\t\t\t\tsetTransitions.add(i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (setTransitions.size <= 1) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet optimizedTransitions: Transition[] = [];\r\n\t\t\tfor (let i = 0; i < decision.numberOfOptimizedTransitions; i++) {\r\n\t\t\t\tif (!setTransitions.contains(i)) {\r\n\t\t\t\t\toptimizedTransitions.push(decision.getOptimizedTransition(i));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet blockEndState: ATNState = decision.getOptimizedTransition(setTransitions.minElement).target.getOptimizedTransition(0).target;\r\n\t\t\tlet matchSet: IntervalSet = new IntervalSet();\r\n\t\t\tfor (let interval of setTransitions.intervals) {\r\n\t\t\t\tfor (let j = interval.a; j <= interval.b; j++) {\r\n\t\t\t\t\tlet matchTransition: Transition = decision.getOptimizedTransition(j).target.getOptimizedTransition(0);\r\n\t\t\t\t\tif (matchTransition instanceof NotSetTransition) {\r\n\t\t\t\t\t\tthrow new Error(\"Not yet implemented.\");\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tmatchSet.addAll(matchTransition.label as IntervalSet);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet newTransition: Transition;\r\n\t\t\tif (matchSet.intervals.length === 1) {\r\n\t\t\t\tif (matchSet.size === 1) {\r\n\t\t\t\t\tnewTransition = new AtomTransition(blockEndState, matchSet.minElement);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet matchInterval: Interval = matchSet.intervals[0];\r\n\t\t\t\t\tnewTransition = new RangeTransition(blockEndState, matchInterval.a, matchInterval.b);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tnewTransition = new SetTransition(blockEndState, matchSet);\r\n\t\t\t}\r\n\r\n\t\t\tlet setOptimizedState: ATNState = new BasicState();\r\n\t\t\tsetOptimizedState.setRuleIndex(decision.ruleIndex);\r\n\t\t\tatn.addState(setOptimizedState);\r\n\r\n\t\t\tsetOptimizedState.addTransition(newTransition);\r\n\t\t\toptimizedTransitions.push(new EpsilonTransition(setOptimizedState));\r\n\r\n\t\t\tremovedPaths += decision.numberOfOptimizedTransitions - optimizedTransitions.length;\r\n\r\n\t\t\tif (decision.isOptimized) {\r\n\t\t\t\twhile (decision.numberOfOptimizedTransitions > 0) {\r\n\t\t\t\t\tdecision.removeOptimizedTransition(decision.numberOfOptimizedTransitions - 1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tfor (let transition of optimizedTransitions) {\r\n\t\t\t\tdecision.addOptimizedTransition(transition);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (ParserATNSimulator.debug) {\r\n\t\t\tconsole.log(\"ATN runtime optimizer removed \" + removedPaths + \" paths by collapsing sets.\");\r\n\t\t}\r\n\r\n\t\treturn removedPaths;\r\n\t}\r\n\r\n\tprivate static identifyTailCalls(atn: ATN): void {\r\n\t\tfor (let state of atn.states) {\r\n\t\t\tfor (let i = 0; i < state.numberOfTransitions; i++) {\r\n\t\t\t\tlet transition = state.transition(i);\r\n\t\t\t\tif (!(transition instanceof RuleTransition)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttransition.tailCall = this.testTailCall(atn, transition, false);\r\n\t\t\t\ttransition.optimizedTailCall = this.testTailCall(atn, transition, true);\r\n\t\t\t}\r\n\r\n\t\t\tif (!state.isOptimized) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let i = 0; i < state.numberOfOptimizedTransitions; i++) {\r\n\t\t\t\tlet transition = state.getOptimizedTransition(i);\r\n\t\t\t\tif (!(transition instanceof RuleTransition)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttransition.tailCall = this.testTailCall(atn, transition, false);\r\n\t\t\t\ttransition.optimizedTailCall = this.testTailCall(atn, transition, true);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprivate static testTailCall(atn: ATN, transition: RuleTransition, optimizedPath: boolean): boolean {\r\n\t\tif (!optimizedPath && transition.tailCall) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\tif (optimizedPath && transition.optimizedTailCall) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tlet reachable: BitSet = new BitSet(atn.states.length);\r\n\t\tlet worklist: ATNState[] = [];\r\n\t\tworklist.push(transition.followState);\r\n\t\twhile (true) {\r\n\t\t\tlet state = worklist.pop();\r\n\t\t\tif (!state) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tif (reachable.get(state.stateNumber)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (state instanceof RuleStopState) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (!state.onlyHasEpsilonTransitions) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tlet transitionCount = optimizedPath ? state.numberOfOptimizedTransitions : state.numberOfTransitions;\r\n\t\t\tfor (let i = 0; i < transitionCount; i++) {\r\n\t\t\t\tlet t = optimizedPath ? state.getOptimizedTransition(i) : state.transition(i);\r\n\t\t\t\tif (t.serializationType !== TransitionType.EPSILON) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tworklist.push(t.target);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\tprotected static toInt(c: number): number {\r\n\t\treturn c;\r\n\t}\r\n\r\n\tprotected static toInt32(data: Uint16Array, offset: number): number {\r\n\t\treturn (data[offset] | (data[offset + 1] << 16)) >>> 0;\r\n\t}\r\n\r\n\tprotected static toUUID(data: Uint16Array, offset: number): UUID {\r\n\t\tlet leastSigBits: number = ATNDeserializer.toInt32(data, offset);\r\n\t\tlet lessSigBits: number = ATNDeserializer.toInt32(data, offset + 2);\r\n\t\tlet moreSigBits: number = ATNDeserializer.toInt32(data, offset + 4);\r\n\t\tlet mostSigBits: number = ATNDeserializer.toInt32(data, offset + 6);\r\n\t\treturn new UUID(mostSigBits, moreSigBits, lessSigBits, leastSigBits);\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected edgeFactory(\r\n\t\t@NotNull atn: ATN,\r\n\t\ttype: TransitionType, src: number, trg: number,\r\n\t\targ1: number, arg2: number, arg3: number,\r\n\t\tsets: IntervalSet[]): Transition {\r\n\t\tlet target: ATNState = atn.states[trg];\r\n\t\tswitch (type) {\r\n\t\t\tcase TransitionType.EPSILON: return new EpsilonTransition(target);\r\n\t\t\tcase TransitionType.RANGE:\r\n\t\t\t\tif (arg3 !== 0) {\r\n\t\t\t\t\treturn new RangeTransition(target, Token.EOF, arg2);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\treturn new RangeTransition(target, arg1, arg2);\r\n\t\t\t\t}\r\n\t\t\tcase TransitionType.RULE:\r\n\t\t\t\tlet rt: RuleTransition = new RuleTransition(atn.states[arg1] as RuleStartState, arg2, arg3, target);\r\n\t\t\t\treturn rt;\r\n\t\t\tcase TransitionType.PREDICATE:\r\n\t\t\t\tlet pt: PredicateTransition = new PredicateTransition(target, arg1, arg2, arg3 !== 0);\r\n\t\t\t\treturn pt;\r\n\t\t\tcase TransitionType.PRECEDENCE:\r\n\t\t\t\treturn new PrecedencePredicateTransition(target, arg1);\r\n\t\t\tcase TransitionType.ATOM:\r\n\t\t\t\tif (arg3 !== 0) {\r\n\t\t\t\t\treturn new AtomTransition(target, Token.EOF);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\treturn new AtomTransition(target, arg1);\r\n\t\t\t\t}\r\n\t\t\tcase TransitionType.ACTION:\r\n\t\t\t\tlet a: ActionTransition = new ActionTransition(target, arg1, arg2, arg3 !== 0);\r\n\t\t\t\treturn a;\r\n\t\t\tcase TransitionType.SET: return new SetTransition(target, sets[arg1]);\r\n\t\t\tcase TransitionType.NOT_SET: return new NotSetTransition(target, sets[arg1]);\r\n\t\t\tcase TransitionType.WILDCARD: return new WildcardTransition(target);\r\n\t\t}\r\n\r\n\t\tthrow new Error(\"The specified transition type is not valid.\");\r\n\t}\r\n\r\n\tprotected stateFactory(type: ATNStateType, ruleIndex: number): ATNState {\r\n\t\tlet s: ATNState;\r\n\t\tswitch (type) {\r\n\t\t\tcase ATNStateType.INVALID_TYPE: return new InvalidState();\r\n\t\t\tcase ATNStateType.BASIC: s = new BasicState(); break;\r\n\t\t\tcase ATNStateType.RULE_START: s = new RuleStartState(); break;\r\n\t\t\tcase ATNStateType.BLOCK_START: s = new BasicBlockStartState(); break;\r\n\t\t\tcase ATNStateType.PLUS_BLOCK_START: s = new PlusBlockStartState(); break;\r\n\t\t\tcase ATNStateType.STAR_BLOCK_START: s = new StarBlockStartState(); break;\r\n\t\t\tcase ATNStateType.TOKEN_START: s = new TokensStartState(); break;\r\n\t\t\tcase ATNStateType.RULE_STOP: s = new RuleStopState(); break;\r\n\t\t\tcase ATNStateType.BLOCK_END: s = new BlockEndState(); break;\r\n\t\t\tcase ATNStateType.STAR_LOOP_BACK: s = new StarLoopbackState(); break;\r\n\t\t\tcase ATNStateType.STAR_LOOP_ENTRY: s = new StarLoopEntryState(); break;\r\n\t\t\tcase ATNStateType.PLUS_LOOP_BACK: s = new PlusLoopbackState(); break;\r\n\t\t\tcase ATNStateType.LOOP_END: s = new LoopEndState(); break;\r\n\t\t\tdefault:\r\n\t\t\t\tlet message: string = `The specified state type ${type} is not valid.`;\r\n\t\t\t\tthrow new Error(message);\r\n\t\t}\r\n\r\n\t\ts.ruleIndex = ruleIndex;\r\n\t\treturn s;\r\n\t}\r\n\r\n\tprotected lexerActionFactory(type: LexerActionType, data1: number, data2: number): LexerAction {\r\n\t\tswitch (type) {\r\n\t\tcase LexerActionType.CHANNEL:\r\n\t\t\treturn new LexerChannelAction(data1);\r\n\r\n\t\tcase LexerActionType.CUSTOM:\r\n\t\t\treturn new LexerCustomAction(data1, data2);\r\n\r\n\t\tcase LexerActionType.MODE:\r\n\t\t\treturn new LexerModeAction(data1);\r\n\r\n\t\tcase LexerActionType.MORE:\r\n\t\t\treturn LexerMoreAction.INSTANCE;\r\n\r\n\t\tcase LexerActionType.POP_MODE:\r\n\t\t\treturn LexerPopModeAction.INSTANCE;\r\n\r\n\t\tcase LexerActionType.PUSH_MODE:\r\n\t\t\treturn new LexerPushModeAction(data1);\r\n\r\n\t\tcase LexerActionType.SKIP:\r\n\t\t\treturn LexerSkipAction.INSTANCE;\r\n\r\n\t\tcase LexerActionType.TYPE:\r\n\t\t\treturn new LexerTypeAction(data1);\r\n\r\n\t\tdefault:\r\n\t\t\tlet message: string = `The specified lexer action type ${type} is not valid.`;\r\n\t\t\tthrow new Error(message);\r\n\t\t}\r\n\t}\r\n}\r\n"]}