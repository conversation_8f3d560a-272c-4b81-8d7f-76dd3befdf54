{"version": 3, "file": "PlusLoopbackState.js", "sourceRoot": "", "sources": ["../../../src/atn/PlusLoopbackState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iDAA8C;AAC9C,mDAAgD;AAChD,8CAAyC;AAEzC;;GAEG;AACH,MAAa,iBAAkB,SAAQ,6BAAa;IAGnD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,cAAc,CAAC;IACpC,CAAC;CACD;AAHA;IADC,qBAAQ;kDAGR;AALF,8CAMC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.0257730-07:00\r\n\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** Decision state for `A+` and `(A|B)+`.  It has two transitions:\r\n *  one to the loop back to start of the block and one to exit.\r\n */\r\nexport class PlusLoopbackState extends DecisionState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.PLUS_LOOP_BACK;\r\n\t}\r\n}\r\n"]}