{"version": 3, "file": "StarLoopbackState.js", "sourceRoot": "", "sources": ["../../../src/atn/StarLoopbackState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAC9C,8CAAyC;AAGzC,MAAa,iBAAkB,SAAQ,mBAAQ;IAC9C,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAA4B,CAAC;IACxD,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,cAAc,CAAC;IACpC,CAAC;CACD;AAHA;IADC,qBAAQ;kDAGR;AARF,8CASC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.6368726-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { Override } from \"../Decorators\";\r\nimport { StarLoopEntryState } from \"./StarLoopEntryState\";\r\n\r\nexport class StarLoopbackState extends ATNState {\r\n\tget loopEntryState(): StarLoopEntryState {\r\n\t\treturn this.transition(0).target as StarLoopEntryState;\r\n\t}\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.STAR_LOOP_BACK;\r\n\t}\r\n}\r\n"]}