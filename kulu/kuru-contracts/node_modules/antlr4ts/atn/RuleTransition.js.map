{"version": 3, "file": "RuleTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/RuleTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAkD;AAElD,6CAA0C;AAG1C,MAAM;AACN,IAAa,cAAc,GAA3B,MAAa,cAAe,SAAQ,uBAAU;IAa7C,YAAqB,SAAyB,EAAE,SAAiB,EAAE,UAAkB,EAAW,WAAqB;QACpH,KAAK,CAAC,SAAS,CAAC,CAAC;QAJX,aAAQ,GAAY,KAAK,CAAC;QAC1B,sBAAiB,GAAY,KAAK,CAAC;QAIzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAGD,IAAI,iBAAiB;QACpB,oBAA2B;IAC5B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,KAAK,CAAC;IACd,CAAC;CACD,CAAA;AA1BA;IADC,oBAAO;mDACqB;AAa7B;IADC,qBAAQ;uDAGR;AAGD;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;6CAGR;AAjCW,cAAc;IAab,WAAA,oBAAO,CAAA,EAAoE,WAAA,oBAAO,CAAA;GAbnF,cAAc,CAkC1B;AAlCY,wCAAc", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.8294453-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { RuleStartState } from \"./RuleStartState\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/** */\r\nexport class RuleTransition extends Transition {\r\n\t/** Ptr to the rule definition object for this rule ref */\r\n\tpublic ruleIndex: number;      // no Rule object at runtime\r\n\r\n\tpublic precedence: number;\r\n\r\n\t/** What node to begin computations following ref to rule */\r\n\t@NotNull\r\n\tpublic followState: ATNState;\r\n\r\n\tpublic tailCall: boolean = false;\r\n\tpublic optimizedTailCall: boolean = false;\r\n\r\n\tconstructor(@NotNull ruleStart: RuleStartState, ruleIndex: number, precedence: number, @NotNull followState: ATNState) {\r\n\t\tsuper(ruleStart);\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t\tthis.precedence = precedence;\r\n\t\tthis.followState = followState;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.RULE;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEpsilon(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn false;\r\n\t}\r\n}\r\n"]}