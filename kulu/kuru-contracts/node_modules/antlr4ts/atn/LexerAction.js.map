{"version": 3, "file": "LexerAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.7973969-07:00\r\n\r\nimport { Equatable } from \"../misc/Stubs\";\r\nimport { <PERSON><PERSON> } from \"../Lexer\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\n\r\n/**\r\n * Represents a single action which can be executed following the successful\r\n * match of a lexer rule. Lexer actions are used for both embedded action syntax\r\n * and ANTLR 4's new lexer command syntax.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport interface LexerAction extends Equatable {\r\n\t/**\r\n\t * Gets the serialization type of the lexer action.\r\n\t *\r\n\t * @returns The serialization type of the lexer action.\r\n\t */\r\n\t//@NotNull\r\n\treadonly actionType: LexerActionType;\r\n\r\n\t/**\r\n\t * Gets whether the lexer action is position-dependent. Position-dependent\r\n\t * actions may have different semantics depending on the {@link CharStream}\r\n\t * index at the time the action is executed.\r\n\t *\r\n\t * Many lexer commands, including `type`, `skip`, and\r\n\t * `more`, do not check the input index during their execution.\r\n\t * Actions like this are position-independent, and may be stored more\r\n\t * efficiently as part of the `ATNConfig.lexerActionExecutor`.\r\n\t *\r\n\t * @returns `true` if the lexer action semantics can be affected by the\r\n\t * position of the input {@link CharStream} at the time it is executed;\r\n\t * otherwise, `false`.\r\n\t */\r\n\treadonly isPositionDependent: boolean;\r\n\r\n\t/**\r\n\t * Execute the lexer action in the context of the specified {@link Lexer}.\r\n\t *\r\n\t * For position-dependent actions, the input stream must already be\r\n\t * positioned correctly prior to calling this method.\r\n\t *\r\n\t * @param lexer The lexer instance.\r\n\t */\r\n\texecute(/*@NotNull*/ lexer: Lexer): void;\r\n}\r\n"]}