{"version": 3, "file": "EpsilonTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/EpsilonTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAkD;AAClD,6CAA0C;AAG1C,IAAa,iBAAiB,GAA9B,MAAa,iBAAkB,SAAQ,uBAAU;IAIhD,YAAqB,MAAgB,EAAE,4BAAoC,CAAC,CAAC;QAC5E,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,0BAA0B,GAAG,yBAAyB,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,yBAAyB;QAC5B,OAAO,IAAI,CAAC,0BAA0B,CAAC;IACxC,CAAC;IAGD,IAAI,iBAAiB;QACpB,uBAA8B;IAC/B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,KAAK,CAAC;IACd,CAAC;IAIM,QAAQ;QACd,OAAO,SAAS,CAAC;IAClB,CAAC;CACD,CAAA;AAnBA;IADC,qBAAQ;0DAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;gDAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;iDAGP;AAxCW,iBAAiB;IAIhB,WAAA,oBAAO,CAAA;GAJR,iBAAiB,CAyC7B;AAzCY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.6283213-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\nexport class EpsilonTransition extends Transition {\r\n\r\n\tprivate _outermostPrecedenceReturn: number;\r\n\r\n\tconstructor(@NotNull target: ATNState, outermostPrecedenceReturn: number = -1) {\r\n\t\tsuper(target);\r\n\t\tthis._outermostPrecedenceReturn = outermostPrecedenceReturn;\r\n\t}\r\n\r\n\t/**\r\n\t * @returns the rule index of a precedence rule for which this transition is\r\n\t * returning from, where the precedence value is 0; otherwise, -1.\r\n\t *\r\n\t * @see ATNConfig.isPrecedenceFilterSuppressed\r\n\t * @see ParserATNSimulator#applyPrecedenceFilter(ATNConfigSet, ParserRuleContext, PredictionContextCache)\r\n\t * @since 4.4.1\r\n\t */\r\n\tget outermostPrecedenceReturn(): number {\r\n\t\treturn this._outermostPrecedenceReturn;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.EPSILON;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEpsilon(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn \"epsilon\";\r\n\t}\r\n}\r\n"]}