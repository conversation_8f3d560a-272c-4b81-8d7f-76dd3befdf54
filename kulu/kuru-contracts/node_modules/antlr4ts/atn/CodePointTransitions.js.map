{"version": 3, "file": "CodePointTransitions.js", "sourceRoot": "", "sources": ["../../../src/atn/CodePointTransitions.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,+CAA+C;AAE/C,qDAAkD;AAClD,qDAAkD;AAClD,uDAAoD;AACpD,mDAAgD;AAGhD;;;;;;;;;GASG;AAEH;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,MAAgB,EAAE,SAAiB;IACtE,IAAI,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,EAAE;QAClD,OAAO,IAAI,6BAAa,CAAC,MAAM,EAAE,yBAAW,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;KAC5D;SACI;QACJ,OAAO,IAAI,+BAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;KAC7C;AACF,CAAC;AAPD,kDAOC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CAAC,MAAgB,EAAE,aAAqB,EAAE,WAAmB;IACpG,IAAI,SAAS,CAAC,wBAAwB,CAAC,aAAa,CAAC,IAAI,SAAS,CAAC,wBAAwB,CAAC,WAAW,CAAC,EAAE;QACzG,OAAO,IAAI,6BAAa,CAAC,MAAM,EAAE,yBAAW,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KAC7E;SACI;QACJ,OAAO,IAAI,iCAAe,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;KAC/D;AACF,CAAC;AAPD,4DAOC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport * as Character from \"../misc/Character\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { AtomTransition } from \"./AtomTransition\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { RangeTransition } from \"./RangeTransition\";\r\nimport { SetTransition } from \"./SetTransition\";\r\nimport { Transition } from \"./Transition\";\r\n\r\n/**\r\n * Utility functions to create {@link AtomTransition}, {@link RangeTransition},\r\n * and {@link SetTransition} appropriately based on the range of the input.\r\n *\r\n * To keep the serialized ATN size small, we only inline atom and\r\n * range transitions for Unicode code points <= U+FFFF.\r\n *\r\n * Whenever we encounter a Unicode code point > U+FFFF, we represent that\r\n * as a set transition (even if it is logically an atom or a range).\r\n */\r\n\r\n/**\r\n * If {@code codePoint} is <= U+FFFF, returns a new {@link AtomTransition}.\r\n * Otherwise, returns a new {@link SetTransition}.\r\n */\r\nexport function createWithCodePoint(target: ATNState, codePoint: number): Transition {\r\n\tif (Character.isSupplementaryCodePoint(codePoint)) {\r\n\t\treturn new SetTransition(target, IntervalSet.of(codePoint));\r\n\t}\r\n\telse {\r\n\t\treturn new AtomTransition(target, codePoint);\r\n\t}\r\n}\r\n\r\n/**\r\n * If {@code codePointFrom} and {@code codePointTo} are both\r\n * <= U+FFFF, returns a new {@link RangeTransition}.\r\n * Otherwise, returns a new {@link SetTransition}.\r\n */\r\nexport function createWithCodePointRange(target: ATNState, codePointFrom: number, codePointTo: number): Transition {\r\n\tif (Character.isSupplementaryCodePoint(codePointFrom) || Character.isSupplementaryCodePoint(codePointTo)) {\r\n\t\treturn new SetTransition(target, IntervalSet.of(codePointFrom, codePointTo));\r\n\t}\r\n\telse {\r\n\t\treturn new RangeTransition(target, codePointFrom, codePointTo);\r\n\t}\r\n}\r\n"]}