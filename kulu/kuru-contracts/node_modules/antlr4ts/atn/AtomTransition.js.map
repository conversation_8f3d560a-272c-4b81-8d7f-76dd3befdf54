{"version": 3, "file": "AtomTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/AtomTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,qDAAkD;AAClD,8CAAkD;AAClD,6CAA0C;AAG1C,mEAAmE;AACnE,IAAa,cAAc,GAA3B,MAAa,cAAe,SAAQ,uBAAU;IAI7C,YAAqB,MAAgB,EAAE,KAAa;QACnD,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IAGD,IAAI,iBAAiB;QACpB,oBAA2B;IAC5B,CAAC;IAID,IAAI,KAAK;QACR,OAAO,yBAAW,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC;IAC/B,CAAC;IAIM,QAAQ;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;CACD,CAAA;AApBA;IADC,qBAAQ;uDAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;2CAGP;AAGD;IADC,qBAAQ;6CAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;8CAGP;AA7BW,cAAc;IAIb,WAAA,oBAAO,CAAA;GAJR,cAAc,CA8B1B;AA9BY,wCAAc", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.6769122-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/** TODO: make all transitions sets? no, should remove set edges */\r\nexport class AtomTransition extends Transition {\r\n\t/** The token type or character value; or, signifies special label. */\r\n\tpublic _label: number;\r\n\r\n\tconstructor(@NotNull target: ATNState, label: number) {\r\n\t\tsuper(target);\r\n\t\tthis._label = label;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.ATOM;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tget label(): IntervalSet {\r\n\t\treturn IntervalSet.of(this._label);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn this._label === symbol;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn String(this.label);\r\n\t}\r\n}\r\n"]}