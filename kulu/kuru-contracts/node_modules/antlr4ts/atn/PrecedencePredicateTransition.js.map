{"version": 3, "file": "PrecedencePredicateTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/PrecedencePredicateTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,+EAA4E;AAE5E,8CAAkD;AAClD,uDAAoD;AAGpD;;;GAGG;AACH,IAAa,6BAA6B,GAA1C,MAAa,6BAA8B,SAAQ,yDAA2B;IAG7E,YAAsB,MAAgB,EAAE,UAAkB;QACzD,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,CAAC;IAGD,IAAI,iBAAiB;QACpB,2BAAiC;IAClC,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,iCAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IACnC,CAAC;CACD,CAAA;AAtBA;IADC,qBAAQ;sEAGR;AAGD;IADC,qBAAQ;8DAGR;AAGD;IADC,qBAAQ;4DAGR;AAOD;IADC,qBAAQ;6DAGR;AA9BW,6BAA6B;IAG3B,WAAA,oBAAO,CAAA;GAHT,6BAA6B,CA+BzC;AA/BY,sEAA6B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.0994191-07:00\r\n\r\nimport { AbstractPredicateTransition } from \"./AbstractPredicateTransition\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class PrecedencePredicateTransition extends AbstractPredicateTransition {\r\n\tpublic precedence: number;\r\n\r\n\tconstructor( @NotNull target: ATNState, precedence: number) {\r\n\t\tsuper(target);\r\n\t\tthis.precedence = precedence;\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.PRECEDENCE;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEpsilon(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget predicate(): SemanticContext.PrecedencePredicate {\r\n\t\treturn new SemanticContext.PrecedencePredicate(this.precedence);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this.precedence + \" >= _p\";\r\n\t}\r\n}\r\n"]}