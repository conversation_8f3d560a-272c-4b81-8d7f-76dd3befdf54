{"version": 3, "file": "ATNDeserializationOptions.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNDeserializationOptions.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,8CAAwC;AAExC;;;GAGG;AACH,MAAa,yBAAyB;IAQrC,YAAY,OAAmC;QALvC,aAAQ,GAAY,KAAK,CAAC;QAMjC,IAAI,OAAO,EAAE;YACZ,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,6BAA6B,GAAG,OAAO,CAAC,6BAA6B,CAAC;YAC3E,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;aAAM;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACrB;IACF,CAAC;IAGD,MAAM,KAAK,cAAc;QACxB,IAAI,yBAAyB,CAAC,eAAe,IAAI,IAAI,EAAE;YACtD,yBAAyB,CAAC,eAAe,GAAG,IAAI,yBAAyB,EAAE,CAAC;YAC5E,yBAAyB,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;SACzD;QAED,OAAO,yBAAyB,CAAC,eAAe,CAAC;IAClD,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAEM,YAAY;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAED,IAAI,WAAW,CAAC,SAAkB;QACjC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAED,IAAI,+BAA+B;QAClC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC3C,CAAC;IAED,IAAI,+BAA+B,CAAC,6BAAsC;QACzE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;IACpE,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED,IAAI,UAAU,CAAC,QAAiB;QAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAES,eAAe;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC5C;IACF,CAAC;CACD;AAjDA;IADC,oBAAO;qDAQP;AA5BF,8DAsEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:25.8187912-07:00\r\n\r\nimport { NotNull } from \"../Decorators\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ATNDeserializationOptions {\r\n\tprivate static _defaultOptions?: ATNDeserializationOptions;\r\n\r\n\tprivate readOnly: boolean = false;\r\n\tprivate verifyATN: boolean;\r\n\tprivate generateRuleBypassTransitions: boolean;\r\n\tprivate optimize: boolean;\r\n\r\n\tconstructor(options?: ATNDeserializationOptions) {\r\n\t\tif (options) {\r\n\t\t\tthis.verifyATN = options.verifyATN;\r\n\t\t\tthis.generateRuleBypassTransitions = options.generateRuleBypassTransitions;\r\n\t\t\tthis.optimize = options.optimize;\r\n\t\t} else {\r\n\t\t\tthis.verifyATN = true;\r\n\t\t\tthis.generateRuleBypassTransitions = false;\r\n\t\t\tthis.optimize = true;\r\n\t\t}\r\n\t}\r\n\r\n\t@NotNull\r\n\tstatic get defaultOptions(): ATNDeserializationOptions {\r\n\t\tif (ATNDeserializationOptions._defaultOptions == null) {\r\n\t\t\tATNDeserializationOptions._defaultOptions = new ATNDeserializationOptions();\r\n\t\t\tATNDeserializationOptions._defaultOptions.makeReadOnly();\r\n\t\t}\r\n\r\n\t\treturn ATNDeserializationOptions._defaultOptions;\r\n\t}\r\n\r\n\tget isReadOnly(): boolean {\r\n\t\treturn this.readOnly;\r\n\t}\r\n\r\n\tpublic makeReadOnly(): void {\r\n\t\tthis.readOnly = true;\r\n\t}\r\n\r\n\tget isVerifyATN(): boolean {\r\n\t\treturn this.verifyATN;\r\n\t}\r\n\r\n\tset isVerifyATN(verifyATN: boolean) {\r\n\t\tthis.throwIfReadOnly();\r\n\t\tthis.verifyATN = verifyATN;\r\n\t}\r\n\r\n\tget isGenerateRuleBypassTransitions(): boolean {\r\n\t\treturn this.generateRuleBypassTransitions;\r\n\t}\r\n\r\n\tset isGenerateRuleBypassTransitions(generateRuleBypassTransitions: boolean) {\r\n\t\tthis.throwIfReadOnly();\r\n\t\tthis.generateRuleBypassTransitions = generateRuleBypassTransitions;\r\n\t}\r\n\r\n\tget isOptimize(): boolean {\r\n\t\treturn this.optimize;\r\n\t}\r\n\r\n\tset isOptimize(optimize: boolean) {\r\n\t\tthis.throwIfReadOnly();\r\n\t\tthis.optimize = optimize;\r\n\t}\r\n\r\n\tprotected throwIfReadOnly(): void {\r\n\t\tif (this.isReadOnly) {\r\n\t\t\tthrow new Error(\"The object is read only.\");\r\n\t\t}\r\n\t}\r\n}\r\n"]}