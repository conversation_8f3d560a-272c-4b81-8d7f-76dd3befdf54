{"version": 3, "file": "SemanticContext.js", "sourceRoot": "", "sources": ["../../../src/atn/SemanticContext.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,6EAA0E;AAG1E,mDAAgD;AAChD,8CAAkD;AAClD,+EAA4E;AAG5E,uCAAuC;AAEvC,SAAS,GAAG,CAA0B,KAAkB;IACvD,IAAI,MAAqB,CAAC;IAC1B,KAAK,IAAI,OAAO,IAAI,KAAK,EAAE;QAC1B,IAAI,MAAM,KAAK,SAAS,EAAE;YACzB,MAAM,GAAG,OAAO,CAAC;YACjB,SAAS;SACT;QAED,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,UAAU,GAAG,CAAC,EAAE;YACnB,MAAM,GAAG,OAAO,CAAC;SACjB;KACD;IAED,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,GAAG,CAA0B,KAAkB;IACvD,IAAI,MAAqB,CAAC;IAC1B,KAAK,IAAI,OAAO,IAAI,KAAK,EAAE;QAC1B,IAAI,MAAM,KAAK,SAAS,EAAE;YACzB,MAAM,GAAG,OAAO,CAAC;YACjB,SAAS;SACT;QAED,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,UAAU,GAAG,CAAC,EAAE;YACnB,MAAM,GAAG,OAAO,CAAC;SACjB;KACD;IAED,OAAO,MAAM,CAAC;AACf,CAAC;AAED;;;;;;GAMG;AACH,MAAsB,eAAe;IAGpC;;;OAGG;IACH,MAAM,KAAK,IAAI;QACd,IAAI,eAAe,CAAC,KAAK,KAAK,SAAS,EAAE;YACxC,eAAe,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;SACxD;QAED,OAAO,eAAe,CAAC,KAAK,CAAC;IAC9B,CAAC;IAiBD;;;;;;;;;;;;;;;;OAgBG;IACI,cAAc,CAAC,MAA4B,EAAE,eAA4B;QAC/E,OAAO,IAAI,CAAC;IACb,CAAC;IAMM,MAAM,CAAC,GAAG,CAAC,CAA8B,EAAE,CAAkB;QACnE,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,eAAe,CAAC,IAAI,EAAE;YACrC,OAAO,CAAC,CAAC;SACT;QACD,IAAI,CAAC,KAAK,eAAe,CAAC,IAAI,EAAE;YAC/B,OAAO,CAAC,CAAC;SACT;QACD,IAAI,MAAM,GAAwB,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACvB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,EAAE,CAAC,CAA8B,EAAE,CAAkB;QAClE,IAAI,CAAC,CAAC,EAAE;YACP,OAAO,CAAC,CAAC;SACT;QAED,IAAI,CAAC,KAAK,eAAe,CAAC,IAAI,IAAI,CAAC,KAAK,eAAe,CAAC,IAAI,EAAE;YAC7D,OAAO,eAAe,CAAC,IAAI,CAAC;SAC5B;QACD,IAAI,MAAM,GAAuB,IAAI,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACvB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAzFD,0CAyFC;AAED,WAAiB,eAAe;IAC/B;;OAEG;IACH,MAAM,YAAY,GAAG,QAAQ,CAAC;IAC9B;;OAEG;IACH,MAAM,WAAW,GAAG,SAAS,CAAC;IAE9B,SAAS,0BAA0B,CAAC,UAA6B;QAChE,IAAI,MAAM,GAA0C,EAAE,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,OAAO,GAAoB,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,OAAO,YAAY,eAAe,CAAC,mBAAmB,EAAE;gBAC3D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAErB,uFAAuF;gBACvF,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxB,CAAC,EAAE,CAAC;aACJ;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,MAAa,SAAU,SAAQ,eAAe;QAQ7C,YAAY,YAAoB,CAAC,CAAC,EAAE,YAAoB,CAAC,CAAC,EAAE,iBAA0B,KAAK;YAC1F,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACtC,CAAC;QAGM,IAAI,CAAI,MAA0B,EAAE,eAA4B;YACtE,IAAI,QAAQ,GAA4B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;YAC1F,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QAGM,QAAQ;YACd,IAAI,QAAQ,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;YAC/C,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC1C,OAAO,QAAQ,CAAC;QACjB,CAAC;QAGM,MAAM,CAAC,GAAQ;YACrB,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE;gBAChC,OAAO,KAAK,CAAC;aACb;YACD,IAAI,IAAI,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;YACD,OAAO,IAAI,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS;gBACtC,IAAI,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS;gBAChC,IAAI,CAAC,cAAc,KAAK,GAAG,CAAC,cAAc,CAAC;QAC7C,CAAC;QAGM,QAAQ;YACd,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3D,CAAC;KACD;IAhCA;QADC,qBAAQ;yCAIR;IAGD;QADC,qBAAQ;6CAQR;IAGD;QADC,qBAAQ;2CAWR;IAGD;QADC,qBAAQ;6CAGR;IA/CW,yBAAS,YAgDrB,CAAA;IAED,MAAa,mBAAoB,SAAQ,eAAe;QAGvD,YAAY,UAAkB;YAC7B,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC9B,CAAC;QAGM,IAAI,CAAI,MAA0B,EAAE,eAA4B;YACtE,OAAO,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC;QAGM,cAAc,CAAC,MAA4B,EAAE,eAA4B;YAC/E,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;gBACtD,OAAO,eAAe,CAAC,IAAI,CAAC;aAC5B;iBACI;gBACJ,OAAO,SAAS,CAAC;aACjB;QACF,CAAC;QAGM,SAAS,CAAC,CAAsB;YACtC,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;QACvC,CAAC;QAGM,QAAQ;YACd,IAAI,QAAQ,GAAW,CAAC,CAAC;YACzB,QAAQ,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;YAC3C,OAAO,QAAQ,CAAC;QACjB,CAAC;QAGM,MAAM,CAAC,GAAQ;YACrB,IAAI,CAAC,CAAC,GAAG,YAAY,mBAAmB,CAAC,EAAE;gBAC1C,OAAO,KAAK,CAAC;aACb;YAED,IAAI,IAAI,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;YAED,OAAO,IAAI,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,CAAC;QAC3C,CAAC;QAIM,QAAQ;YACd,OAAO,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;KACD;IA5CA;QADC,qBAAQ;mDAGR;IAGD;QADC,qBAAQ;6DAQR;IAGD;QADC,qBAAQ;wDAGR;IAGD;QADC,qBAAQ;uDAKR;IAGD;QADC,qBAAQ;qDAWR;IAID;QAFC,qBAAQ;uDAIR;IApDW,mCAAmB,sBAqD/B,CAAA;IAED;;;;;OAKG;IACH,MAAsB,QAAS,SAAQ,eAAe;KAWrD;IAXqB,wBAAQ,WAW7B,CAAA;IAED;;;OAGG;IACH,IAAa,GAAG,GAAhB,MAAa,GAAI,SAAQ,QAAQ;QAGhC,YAAqB,CAAkB,EAAW,CAAkB;YACnE,KAAK,EAAE,CAAC;YAER,IAAI,QAAQ,GAAoC,IAAI,+BAAc,CAAkB,mDAAwB,CAAC,QAAQ,CAAC,CAAC;YACvH,IAAI,CAAC,YAAY,GAAG,EAAE;gBACrB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACN,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAChB;YAED,IAAI,CAAC,YAAY,GAAG,EAAE;gBACrB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACN,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAChB;YAED,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,oBAAoB,GAA0B,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzF,0DAA0D;YAC1D,IAAI,OAAO,GAAG,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACxC,IAAI,OAAO,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACzB;QACF,CAAC;QAGD,IAAI,QAAQ;YACX,OAAO,IAAI,CAAC,KAAK,CAAC;QACnB,CAAC;QAGM,MAAM,CAAC,GAAQ;YACrB,IAAI,IAAI,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;YACD,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,EAAE;gBAC1B,OAAO,KAAK,CAAC;aACb;YACD,OAAO,iDAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC;QAGM,QAAQ;YACd,OAAO,uBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QACtD,CAAC;QAED;;;;;WAKG;QAEI,IAAI,CAAI,MAA0B,EAAE,eAA4B;YACtE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE;oBACxC,OAAO,KAAK,CAAC;iBACb;aACD;YAED,OAAO,IAAI,CAAC;QACb,CAAC;QAGM,cAAc,CAAC,MAA4B,EAAE,eAA4B;YAC/E,IAAI,OAAO,GAAY,KAAK,CAAC;YAC7B,IAAI,QAAQ,GAAsB,EAAE,CAAC;YACrC,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC/B,IAAI,SAAS,GAAgC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC7F,OAAO,GAAG,OAAO,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC;gBAC7C,IAAI,SAAS,IAAI,IAAI,EAAE;oBACtB,mDAAmD;oBACnD,OAAO,SAAS,CAAC;iBACjB;qBACI,IAAI,SAAS,KAAK,eAAe,CAAC,IAAI,EAAE;oBAC5C,8CAA8C;oBAC9C,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACzB;aACD;YAED,IAAI,CAAC,OAAO,EAAE;gBACb,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,qDAAqD;gBACrD,OAAO,eAAe,CAAC,IAAI,CAAC;aAC5B;YAED,IAAI,MAAM,GAAoB,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;YAED,OAAO,MAAM,CAAC;QACf,CAAC;QAGM,QAAQ;YACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC;KACD,CAAA;IA3EA;QADC,qBAAQ;uCAGR;IAGD;QADC,qBAAQ;qCASR;IAGD;QADC,qBAAQ;uCAGR;IASD;QADC,qBAAQ;mCASR;IAGD;QADC,qBAAQ;6CAgCR;IAGD;QADC,qBAAQ;uCAGR;IAxGW,GAAG;QAGF,WAAA,oBAAO,CAAA,EAAsB,WAAA,oBAAO,CAAA;OAHrC,GAAG,CAyGf;IAzGY,mBAAG,MAyGf,CAAA;IAED;;;OAGG;IACH,IAAa,EAAE,GAAf,MAAa,EAAG,SAAQ,QAAQ;QAG/B,YAAqB,CAAkB,EAAW,CAAkB;YACnE,KAAK,EAAE,CAAC;YAER,IAAI,QAAQ,GAAoC,IAAI,+BAAc,CAAkB,mDAAwB,CAAC,QAAQ,CAAC,CAAC;YACvH,IAAI,CAAC,YAAY,EAAE,EAAE;gBACpB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACN,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAChB;YAED,IAAI,CAAC,YAAY,EAAE,EAAE;gBACpB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACN,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAChB;YAED,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,oBAAoB,GAA0B,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzF,2DAA2D;YAC3D,IAAI,OAAO,GAAG,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACxC,IAAI,OAAO,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACzB;QACF,CAAC;QAGD,IAAI,QAAQ;YACX,OAAO,IAAI,CAAC,KAAK,CAAC;QACnB,CAAC;QAGM,MAAM,CAAC,GAAQ;YACrB,IAAI,IAAI,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;YACD,IAAI,CAAC,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE;gBACzB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,iDAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC;QAGM,QAAQ;YACd,OAAO,uBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACrD,CAAC;QAED;;;;;WAKG;QAEI,IAAI,CAAI,MAA0B,EAAE,eAA4B;YACtE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE;oBACvC,OAAO,IAAI,CAAC;iBACZ;aACD;YAED,OAAO,KAAK,CAAC;QACd,CAAC;QAGM,cAAc,CAAC,MAA4B,EAAE,eAA4B;YAC/E,IAAI,OAAO,GAAY,KAAK,CAAC;YAC7B,IAAI,QAAQ,GAAsB,EAAE,CAAC;YACrC,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC/B,IAAI,SAAS,GAAgC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC7F,OAAO,GAAG,OAAO,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC;gBAC7C,IAAI,SAAS,KAAK,eAAe,CAAC,IAAI,EAAE;oBACvC,gDAAgD;oBAChD,OAAO,eAAe,CAAC,IAAI,CAAC;iBAC5B;qBAAM,IAAI,SAAS,EAAE;oBACrB,+CAA+C;oBAC/C,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACzB;aACD;YAED,IAAI,CAAC,OAAO,EAAE;gBACb,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,sDAAsD;gBACtD,OAAO,SAAS,CAAC;aACjB;YAED,IAAI,MAAM,GAAoB,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,MAAM,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;YAED,OAAO,MAAM,CAAC;QACf,CAAC;QAGM,QAAQ;YACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC;KACD,CAAA;IA1EA;QADC,qBAAQ;sCAGR;IAGD;QADC,qBAAQ;oCASR;IAGD;QADC,qBAAQ;sCAGR;IASD;QADC,qBAAQ;kCASR;IAGD;QADC,qBAAQ;4CA+BR;IAGD;QADC,qBAAQ;sCAGR;IAvGW,EAAE;QAGD,WAAA,oBAAO,CAAA,EAAsB,WAAA,oBAAO,CAAA;OAHrC,EAAE,CAwGd;IAxGY,kBAAE,KAwGd,CAAA;AACF,CAAC,EAlXgB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAkX/B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.9521478-07:00\r\n\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { ArrayEqualityComparator } from \"../misc/ArrayEqualityComparator\";\r\nimport { Comparable } from \"../misc/Stubs\";\r\nimport { Equatable } from \"../misc/Stubs\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { Recognizer } from \"../Recognizer\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport * as Utils from \"../misc/Utils\";\r\n\r\nfunction max<T extends Comparable<T>>(items: Iterable<T>): T | undefined {\r\n\tlet result: T | undefined;\r\n\tfor (let current of items) {\r\n\t\tif (result === undefined) {\r\n\t\t\tresult = current;\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tlet comparison = result.compareTo(current);\r\n\t\tif (comparison < 0) {\r\n\t\t\tresult = current;\r\n\t\t}\r\n\t}\r\n\r\n\treturn result;\r\n}\r\n\r\nfunction min<T extends Comparable<T>>(items: Iterable<T>): T | undefined {\r\n\tlet result: T | undefined;\r\n\tfor (let current of items) {\r\n\t\tif (result === undefined) {\r\n\t\t\tresult = current;\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tlet comparison = result.compareTo(current);\r\n\t\tif (comparison > 0) {\r\n\t\t\tresult = current;\r\n\t\t}\r\n\t}\r\n\r\n\treturn result;\r\n}\r\n\r\n/** A tree structure used to record the semantic context in which\r\n *  an ATN configuration is valid.  It's either a single predicate,\r\n *  a conjunction `p1&&p2`, or a sum of products `p1||p2`.\r\n *\r\n *  I have scoped the {@link AND}, {@link OR}, and {@link Predicate} subclasses of\r\n *  {@link SemanticContext} within the scope of this outer class.\r\n */\r\nexport abstract class SemanticContext implements Equatable {\r\n\tprivate static _NONE: SemanticContext;\r\n\r\n\t/**\r\n\t * The default {@link SemanticContext}, which is semantically equivalent to\r\n\t * a predicate of the form `{true}?`.\r\n\t */\r\n\tstatic get NONE(): SemanticContext {\r\n\t\tif (SemanticContext._NONE === undefined) {\r\n\t\t\tSemanticContext._NONE = new SemanticContext.Predicate();\r\n\t\t}\r\n\r\n\t\treturn SemanticContext._NONE;\r\n\t}\r\n\r\n\t/**\r\n\t * For context independent predicates, we evaluate them without a local\r\n\t * context (i.e., unedfined context). That way, we can evaluate them without\r\n\t * having to create proper rule-specific context during prediction (as\r\n\t * opposed to the parser, which creates them naturally). In a practical\r\n\t * sense, this avoids a cast exception from RuleContext to myruleContext.\r\n\t *\r\n\t * For context dependent predicates, we must pass in a local context so that\r\n\t * references such as $arg evaluate properly as _localctx.arg. We only\r\n\t * capture context dependent predicates in the context in which we begin\r\n\t * prediction, so we passed in the outer context here in case of context\r\n\t * dependent predicate evaluation.\r\n\t */\r\n\tpublic abstract eval<T>(parser: Recognizer<T, any>, parserCallStack: RuleContext): boolean;\r\n\r\n\t/**\r\n\t * Evaluate the precedence predicates for the context and reduce the result.\r\n\t *\r\n\t * @param parser The parser instance.\r\n\t * @param parserCallStack\r\n\t * @returns The simplified semantic context after precedence predicates are\r\n\t * evaluated, which will be one of the following values.\r\n\t *\r\n\t * * {@link #NONE}: if the predicate simplifies to `true` after\r\n\t *   precedence predicates are evaluated.\r\n\t * * `undefined`: if the predicate simplifies to `false` after\r\n\t *   precedence predicates are evaluated.\r\n\t * * `this`: if the semantic context is not changed as a result of\r\n\t *   precedence predicate evaluation.\r\n\t * * A non-`undefined` {@link SemanticContext}: the new simplified\r\n\t *   semantic context after precedence predicates are evaluated.\r\n\t */\r\n\tpublic evalPrecedence(parser: Recognizer<any, any>, parserCallStack: RuleContext): SemanticContext | undefined {\r\n\t\treturn this;\r\n\t}\r\n\r\n\tpublic abstract hashCode(): number;\r\n\r\n\tpublic abstract equals(obj: any): boolean;\r\n\r\n\tpublic static and(a: SemanticContext | undefined, b: SemanticContext): SemanticContext {\r\n\t\tif (!a || a === SemanticContext.NONE) {\r\n\t\t\treturn b;\r\n\t\t}\r\n\t\tif (b === SemanticContext.NONE) {\r\n\t\t\treturn a;\r\n\t\t}\r\n\t\tlet result: SemanticContext.AND = new SemanticContext.AND(a, b);\r\n\t\tif (result.opnds.length === 1) {\r\n\t\t\treturn result.opnds[0];\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t/**\r\n\t *\r\n\t *  @see ParserATNSimulator#getPredsForAmbigAlts\r\n\t */\r\n\tpublic static or(a: SemanticContext | undefined, b: SemanticContext): SemanticContext {\r\n\t\tif (!a) {\r\n\t\t\treturn b;\r\n\t\t}\r\n\r\n\t\tif (a === SemanticContext.NONE || b === SemanticContext.NONE) {\r\n\t\t\treturn SemanticContext.NONE;\r\n\t\t}\r\n\t\tlet result: SemanticContext.OR = new SemanticContext.OR(a, b);\r\n\t\tif (result.opnds.length === 1) {\r\n\t\t\treturn result.opnds[0];\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n}\r\n\r\nexport namespace SemanticContext {\r\n\t/**\r\n\t * This random 30-bit prime represents the value of `AND.class.hashCode()`.\r\n\t */\r\n\tconst AND_HASHCODE = 40363613;\r\n\t/**\r\n\t * This random 30-bit prime represents the value of `OR.class.hashCode()`.\r\n\t */\r\n\tconst OR_HASHCODE = 486279973;\r\n\r\n\tfunction filterPrecedencePredicates(collection: SemanticContext[]): SemanticContext.PrecedencePredicate[] {\r\n\t\tlet result: SemanticContext.PrecedencePredicate[] = [];\r\n\t\tfor (let i = 0; i < collection.length; i++) {\r\n\t\t\tlet context: SemanticContext = collection[i];\r\n\t\t\tif (context instanceof SemanticContext.PrecedencePredicate) {\r\n\t\t\t\tresult.push(context);\r\n\r\n\t\t\t\t// Remove the item from 'collection' and move i back so we look at the same index again\r\n\t\t\t\tcollection.splice(i, 1);\r\n\t\t\t\ti--;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\texport class Predicate extends SemanticContext {\r\n\t\tpublic ruleIndex: number;\r\n\t\tpublic predIndex: number;\r\n\t\tpublic isCtxDependent: boolean;   // e.g., $i ref in pred\r\n\r\n\t\tconstructor();\r\n\t\tconstructor(ruleIndex: number, predIndex: number, isCtxDependent: boolean);\r\n\r\n\t\tconstructor(ruleIndex: number = -1, predIndex: number = -1, isCtxDependent: boolean = false) {\r\n\t\t\tsuper();\r\n\t\t\tthis.ruleIndex = ruleIndex;\r\n\t\t\tthis.predIndex = predIndex;\r\n\t\t\tthis.isCtxDependent = isCtxDependent;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic eval<T>(parser: Recognizer<T, any>, parserCallStack: RuleContext): boolean {\r\n\t\t\tlet localctx: RuleContext | undefined = this.isCtxDependent ? parserCallStack : undefined;\r\n\t\t\treturn parser.sempred(localctx, this.ruleIndex, this.predIndex);\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\tlet hashCode: number = MurmurHash.initialize();\r\n\t\t\thashCode = MurmurHash.update(hashCode, this.ruleIndex);\r\n\t\t\thashCode = MurmurHash.update(hashCode, this.predIndex);\r\n\t\t\thashCode = MurmurHash.update(hashCode, this.isCtxDependent ? 1 : 0);\r\n\t\t\thashCode = MurmurHash.finish(hashCode, 3);\r\n\t\t\treturn hashCode;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(obj: any): boolean {\r\n\t\t\tif (!(obj instanceof Predicate)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (this === obj) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn this.ruleIndex === obj.ruleIndex &&\r\n\t\t\t\tthis.predIndex === obj.predIndex &&\r\n\t\t\t\tthis.isCtxDependent === obj.isCtxDependent;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic toString(): string {\r\n\t\t\treturn \"{\" + this.ruleIndex + \":\" + this.predIndex + \"}?\";\r\n\t\t}\r\n\t}\r\n\r\n\texport class PrecedencePredicate extends SemanticContext implements Comparable<PrecedencePredicate> {\r\n\t\tpublic precedence: number;\r\n\r\n\t\tconstructor(precedence: number) {\r\n\t\t\tsuper();\r\n\t\t\tthis.precedence = precedence;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic eval<T>(parser: Recognizer<T, any>, parserCallStack: RuleContext): boolean {\r\n\t\t\treturn parser.precpred(parserCallStack, this.precedence);\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic evalPrecedence(parser: Recognizer<any, any>, parserCallStack: RuleContext): SemanticContext | undefined {\r\n\t\t\tif (parser.precpred(parserCallStack, this.precedence)) {\r\n\t\t\t\treturn SemanticContext.NONE;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\treturn undefined;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic compareTo(o: PrecedencePredicate): number {\r\n\t\t\treturn this.precedence - o.precedence;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\tlet hashCode: number = 1;\r\n\t\t\thashCode = 31 * hashCode + this.precedence;\r\n\t\t\treturn hashCode;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(obj: any): boolean {\r\n\t\t\tif (!(obj instanceof PrecedencePredicate)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tif (this === obj) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\treturn this.precedence === obj.precedence;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\t// precedence >= _precedenceStack.peek()\r\n\t\tpublic toString(): string {\r\n\t\t\treturn \"{\" + this.precedence + \">=prec}?\";\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * This is the base class for semantic context \"operators\", which operate on\r\n\t * a collection of semantic context \"operands\".\r\n\t *\r\n\t * @since 4.3\r\n\t */\r\n\texport abstract class Operator extends SemanticContext {\r\n\t\t/**\r\n\t\t * Gets the operands for the semantic context operator.\r\n\t\t *\r\n\t\t * @returns a collection of {@link SemanticContext} operands for the\r\n\t\t * operator.\r\n\t\t *\r\n\t\t * @since 4.3\r\n\t\t */\r\n\t\t// @NotNull\r\n\t\tpublic abstract readonly operands: Iterable<SemanticContext>;\r\n\t}\r\n\r\n\t/**\r\n\t * A semantic context which is true whenever none of the contained contexts\r\n\t * is false.\r\n\t */\r\n\texport class AND extends Operator {\r\n\t\tpublic opnds: SemanticContext[];\r\n\r\n\t\tconstructor(@NotNull a: SemanticContext, @NotNull b: SemanticContext) {\r\n\t\t\tsuper();\r\n\r\n\t\t\tlet operands: Array2DHashSet<SemanticContext> = new Array2DHashSet<SemanticContext>(ObjectEqualityComparator.INSTANCE);\r\n\t\t\tif (a instanceof AND) {\r\n\t\t\t\toperands.addAll(a.opnds);\r\n\t\t\t} else {\r\n\t\t\t\toperands.add(a);\r\n\t\t\t}\r\n\r\n\t\t\tif (b instanceof AND) {\r\n\t\t\t\toperands.addAll(b.opnds);\r\n\t\t\t} else {\r\n\t\t\t\toperands.add(b);\r\n\t\t\t}\r\n\r\n\t\t\tthis.opnds = operands.toArray();\r\n\t\t\tlet precedencePredicates: PrecedencePredicate[] = filterPrecedencePredicates(this.opnds);\r\n\r\n\t\t\t// interested in the transition with the lowest precedence\r\n\t\t\tlet reduced = min(precedencePredicates);\r\n\t\t\tif (reduced) {\r\n\t\t\t\tthis.opnds.push(reduced);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tget operands(): Iterable<SemanticContext> {\r\n\t\t\treturn this.opnds;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(obj: any): boolean {\r\n\t\t\tif (this === obj) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tif (!(obj instanceof AND)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn ArrayEqualityComparator.INSTANCE.equals(this.opnds, obj.opnds);\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\treturn MurmurHash.hashCode(this.opnds, AND_HASHCODE);\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * {@inheritDoc}\r\n\t\t *\r\n\t\t * The evaluation of predicates by this context is short-circuiting, but\r\n\t\t * unordered.\r\n\t\t */\r\n\t\t@Override\r\n\t\tpublic eval<T>(parser: Recognizer<T, any>, parserCallStack: RuleContext): boolean {\r\n\t\t\tfor (let opnd of this.opnds) {\r\n\t\t\t\tif (!opnd.eval(parser, parserCallStack)) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic evalPrecedence(parser: Recognizer<any, any>, parserCallStack: RuleContext): SemanticContext | undefined {\r\n\t\t\tlet differs: boolean = false;\r\n\t\t\tlet operands: SemanticContext[] = [];\r\n\t\t\tfor (let context of this.opnds) {\r\n\t\t\t\tlet evaluated: SemanticContext | undefined = context.evalPrecedence(parser, parserCallStack);\r\n\t\t\t\tdiffers = differs || (evaluated !== context);\r\n\t\t\t\tif (evaluated == null) {\r\n\t\t\t\t\t// The AND context is false if any element is false\r\n\t\t\t\t\treturn undefined;\r\n\t\t\t\t}\r\n\t\t\t\telse if (evaluated !== SemanticContext.NONE) {\r\n\t\t\t\t\t// Reduce the result by skipping true elements\r\n\t\t\t\t\toperands.push(evaluated);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (!differs) {\r\n\t\t\t\treturn this;\r\n\t\t\t}\r\n\r\n\t\t\tif (operands.length === 0) {\r\n\t\t\t\t// all elements were true, so the AND context is true\r\n\t\t\t\treturn SemanticContext.NONE;\r\n\t\t\t}\r\n\r\n\t\t\tlet result: SemanticContext = operands[0];\r\n\t\t\tfor (let i = 1; i < operands.length; i++) {\r\n\t\t\t\tresult = SemanticContext.and(result, operands[i]);\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic toString(): string {\r\n\t\t\treturn Utils.join(this.opnds, \"&&\");\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * A semantic context which is true whenever at least one of the contained\r\n\t * contexts is true.\r\n\t */\r\n\texport class OR extends Operator {\r\n\t\tpublic opnds: SemanticContext[];\r\n\r\n\t\tconstructor(@NotNull a: SemanticContext, @NotNull b: SemanticContext) {\r\n\t\t\tsuper();\r\n\r\n\t\t\tlet operands: Array2DHashSet<SemanticContext> = new Array2DHashSet<SemanticContext>(ObjectEqualityComparator.INSTANCE);\r\n\t\t\tif (a instanceof OR) {\r\n\t\t\t\toperands.addAll(a.opnds);\r\n\t\t\t} else {\r\n\t\t\t\toperands.add(a);\r\n\t\t\t}\r\n\r\n\t\t\tif (b instanceof OR) {\r\n\t\t\t\toperands.addAll(b.opnds);\r\n\t\t\t} else {\r\n\t\t\t\toperands.add(b);\r\n\t\t\t}\r\n\r\n\t\t\tthis.opnds = operands.toArray();\r\n\t\t\tlet precedencePredicates: PrecedencePredicate[] = filterPrecedencePredicates(this.opnds);\r\n\r\n\t\t\t// interested in the transition with the highest precedence\r\n\t\t\tlet reduced = max(precedencePredicates);\r\n\t\t\tif (reduced) {\r\n\t\t\t\tthis.opnds.push(reduced);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tget operands(): Iterable<SemanticContext> {\r\n\t\t\treturn this.opnds;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(obj: any): boolean {\r\n\t\t\tif (this === obj) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tif (!(obj instanceof OR)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn ArrayEqualityComparator.INSTANCE.equals(this.opnds, obj.opnds);\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\treturn MurmurHash.hashCode(this.opnds, OR_HASHCODE);\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * {@inheritDoc}\r\n\t\t *\r\n\t\t * The evaluation of predicates by this context is short-circuiting, but\r\n\t\t * unordered.\r\n\t\t */\r\n\t\t@Override\r\n\t\tpublic eval<T>(parser: Recognizer<T, any>, parserCallStack: RuleContext): boolean {\r\n\t\t\tfor (let opnd of this.opnds) {\r\n\t\t\t\tif (opnd.eval(parser, parserCallStack)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic evalPrecedence(parser: Recognizer<any, any>, parserCallStack: RuleContext): SemanticContext | undefined {\r\n\t\t\tlet differs: boolean = false;\r\n\t\t\tlet operands: SemanticContext[] = [];\r\n\t\t\tfor (let context of this.opnds) {\r\n\t\t\t\tlet evaluated: SemanticContext | undefined = context.evalPrecedence(parser, parserCallStack);\r\n\t\t\t\tdiffers = differs || (evaluated !== context);\r\n\t\t\t\tif (evaluated === SemanticContext.NONE) {\r\n\t\t\t\t\t// The OR context is true if any element is true\r\n\t\t\t\t\treturn SemanticContext.NONE;\r\n\t\t\t\t} else if (evaluated) {\r\n\t\t\t\t\t// Reduce the result by skipping false elements\r\n\t\t\t\t\toperands.push(evaluated);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (!differs) {\r\n\t\t\t\treturn this;\r\n\t\t\t}\r\n\r\n\t\t\tif (operands.length === 0) {\r\n\t\t\t\t// all elements were false, so the OR context is false\r\n\t\t\t\treturn undefined;\r\n\t\t\t}\r\n\r\n\t\t\tlet result: SemanticContext = operands[0];\r\n\t\t\tfor (let i = 1; i < operands.length; i++) {\r\n\t\t\t\tresult = SemanticContext.or(result, operands[i]);\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic toString(): string {\r\n\t\t\treturn Utils.join(this.opnds, \"||\");\r\n\t\t}\r\n\t}\r\n}\r\n"]}