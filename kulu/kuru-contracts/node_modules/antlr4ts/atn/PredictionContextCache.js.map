{"version": 3, "file": "PredictionContextCache.js", "sourceRoot": "", "sources": ["../../../src/atn/PredictionContextCache.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,8CAAyC;AAEzC,+EAA4E;AAC5E,2DAAwD;AACxD,iCAAiC;AAEjC;;;;;GAKG;AACH,MAAa,sBAAsB;IAYlC,YAAY,cAAuB,IAAI;QAT/B,aAAQ,GACf,IAAI,+BAAc,CAAuC,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QACrF,kBAAa,GACpB,IAAI,+BAAc,CAAoE,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAClH,iBAAY,GACnB,IAAI,+BAAc,CAAyF,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAK9I,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAEM,WAAW,CAAC,OAA0B;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,OAAO,OAAO,CAAC;SACf;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE;YACZ,MAAM,GAAG,OAAO,CAAC;YACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACpC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,QAAQ,CAAC,OAA0B,EAAE,aAAqB;QAChE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,OAAO,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;SACvC;QAED,IAAI,QAAQ,GAAmD,IAAI,sBAAsB,CAAC,uBAAuB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC1I,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACZ,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SACzC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,IAAI,CAAC,CAAoB,EAAE,CAAoB;QACrD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,OAAO,qCAAiB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC1C;QAED,IAAI,QAAQ,GAAwE,IAAI,sBAAsB,CAAC,4CAA4C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClK,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE;YACX,OAAO,MAAM,CAAC;SACd;QAED,MAAM,GAAG,qCAAiB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC5C,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,OAAO,MAAM,CAAC;IACf,CAAC;;AA7DF,wDA8DC;AA7Dc,+BAAQ,GAA2B,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC;AA+DpF,WAAiB,sBAAsB;IACtC,MAAa,uBAAuB;QAInC,YAAY,GAAsB,EAAE,KAAa;YAChD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,CAAC;QAGM,MAAM,CAAC,GAAQ;YACrB,IAAI,CAAC,CAAC,GAAG,YAAY,uBAAuB,CAAC,EAAE;gBAC9C,OAAO,KAAK,CAAC;aACb;iBAAM,IAAI,GAAG,KAAK,IAAI,EAAE;gBACxB,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,KAAK,GAA4B,GAAG,CAAC;YACzC,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;mBAC7B,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC;QAGM,QAAQ;YACd,IAAI,QAAQ,GAAW,CAAC,CAAC;YACzB,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YACrC,OAAO,QAAQ,CAAC;QACjB,CAAC;KACD;IAnBA;QADC,qBAAQ;yDAWR;IAGD;QADC,qBAAQ;2DAMR;IA5BW,8CAAuB,0BA6BnC,CAAA;IAED,MAAa,4CAA4C;QAIxD,YAAY,CAAoB,EAAE,CAAoB;YACrD,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;YAClB,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;YAClB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACb,CAAC;QAED,IAAI,CAAC;YACJ,OAAO,IAAI,CAAC,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACJ,OAAO,IAAI,CAAC,EAAE,CAAC;QAChB,CAAC;QAGM,MAAM,CAAC,CAAM;YACnB,IAAI,CAAC,CAAC,CAAC,YAAY,4CAA4C,CAAC,EAAE;gBACjE,OAAO,KAAK,CAAC;aACb;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;gBACtB,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,KAAK,GAAiD,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;QACzG,CAAC;QAGM,QAAQ;YACd,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;QAChD,CAAC;KACD;IAfA;QADC,qBAAQ;8EAUR;IAGD;QADC,qBAAQ;gFAGR;IAlCW,mEAA4C,+CAmCxD,CAAA;AACF,CAAC,EApEgB,sBAAsB,GAAtB,8BAAsB,KAAtB,8BAAsB,QAoEtC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:35.6390614-07:00\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { Override } from \"../Decorators\";\r\nimport { JavaMap } from \"../misc/Stubs\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport * as assert from \"assert\";\r\n\r\n/** Used to cache {@link PredictionContext} objects. Its used for the shared\r\n *  context cash associated with contexts in DFA states. This cache\r\n *  can be used for both lexers and parsers.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class PredictionContextCache {\r\n\tpublic static UNCACHED: PredictionContextCache = new PredictionContextCache(false);\r\n\r\n\tprivate contexts: JavaMap<PredictionContext, PredictionContext> =\r\n\t\tnew Array2DHashMap<PredictionContext, PredictionContext>(ObjectEqualityComparator.INSTANCE);\r\n\tprivate childContexts: JavaMap<PredictionContextCache.PredictionContextAndInt, PredictionContext> =\r\n\t\tnew Array2DHashMap<PredictionContextCache.PredictionContextAndInt, PredictionContext>(ObjectEqualityComparator.INSTANCE);\r\n\tprivate joinContexts: JavaMap<PredictionContextCache.IdentityCommutativePredictionContextOperands, PredictionContext> =\r\n\t\tnew Array2DHashMap<PredictionContextCache.IdentityCommutativePredictionContextOperands, PredictionContext>(ObjectEqualityComparator.INSTANCE);\r\n\r\n\tprivate enableCache: boolean;\r\n\r\n\tconstructor(enableCache: boolean = true) {\r\n\t\tthis.enableCache = enableCache;\r\n\t}\r\n\r\n\tpublic getAsCached(context: PredictionContext): PredictionContext {\r\n\t\tif (!this.enableCache) {\r\n\t\t\treturn context;\r\n\t\t}\r\n\r\n\t\tlet result = this.contexts.get(context);\r\n\t\tif (!result) {\r\n\t\t\tresult = context;\r\n\t\t\tthis.contexts.put(context, context);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic getChild(context: PredictionContext, invokingState: number): PredictionContext {\r\n\t\tif (!this.enableCache) {\r\n\t\t\treturn context.getChild(invokingState);\r\n\t\t}\r\n\r\n\t\tlet operands: PredictionContextCache.PredictionContextAndInt = new PredictionContextCache.PredictionContextAndInt(context, invokingState);\r\n\t\tlet result = this.childContexts.get(operands);\r\n\t\tif (!result) {\r\n\t\t\tresult = context.getChild(invokingState);\r\n\t\t\tresult = this.getAsCached(result);\r\n\t\t\tthis.childContexts.put(operands, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic join(x: PredictionContext, y: PredictionContext): PredictionContext {\r\n\t\tif (!this.enableCache) {\r\n\t\t\treturn PredictionContext.join(x, y, this);\r\n\t\t}\r\n\r\n\t\tlet operands: PredictionContextCache.IdentityCommutativePredictionContextOperands = new PredictionContextCache.IdentityCommutativePredictionContextOperands(x, y);\r\n\t\tlet result = this.joinContexts.get(operands);\r\n\t\tif (result) {\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\tresult = PredictionContext.join(x, y, this);\r\n\t\tresult = this.getAsCached(result);\r\n\t\tthis.joinContexts.put(operands, result);\r\n\t\treturn result;\r\n\t}\r\n}\r\n\r\nexport namespace PredictionContextCache {\r\n\texport class PredictionContextAndInt {\r\n\t\tprivate obj: PredictionContext;\r\n\t\tprivate value: number;\r\n\r\n\t\tconstructor(obj: PredictionContext, value: number) {\r\n\t\t\tthis.obj = obj;\r\n\t\t\tthis.value = value;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(obj: any): boolean {\r\n\t\t\tif (!(obj instanceof PredictionContextAndInt)) {\r\n\t\t\t\treturn false;\r\n\t\t\t} else if (obj === this) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\tlet other: PredictionContextAndInt = obj;\r\n\t\t\treturn this.value === other.value\r\n\t\t\t\t&& (this.obj === other.obj || (this.obj != null && this.obj.equals(other.obj)));\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\tlet hashCode: number = 5;\r\n\t\t\thashCode = 7 * hashCode + (this.obj != null ? this.obj.hashCode() : 0);\r\n\t\t\thashCode = 7 * hashCode + this.value;\r\n\t\t\treturn hashCode;\r\n\t\t}\r\n\t}\r\n\r\n\texport class IdentityCommutativePredictionContextOperands {\r\n\t\tprivate _x: PredictionContext;\r\n\t\tprivate _y: PredictionContext;\r\n\r\n\t\tconstructor(x: PredictionContext, y: PredictionContext) {\r\n\t\t\tassert(x != null);\r\n\t\t\tassert(y != null);\r\n\t\t\tthis._x = x;\r\n\t\t\tthis._y = y;\r\n\t\t}\r\n\r\n\t\tget x(): PredictionContext {\r\n\t\t\treturn this._x;\r\n\t\t}\r\n\r\n\t\tget y(): PredictionContext {\r\n\t\t\treturn this._y;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(o: any): boolean {\r\n\t\t\tif (!(o instanceof IdentityCommutativePredictionContextOperands)) {\r\n\t\t\t\treturn false;\r\n\t\t\t} else if (this === o) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\tlet other: IdentityCommutativePredictionContextOperands = o;\r\n\t\t\treturn (this._x === other._x && this._y === other._y) || (this._x === other._y && this._y === other._x);\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic hashCode(): number {\r\n\t\t\treturn this._x.hashCode() ^ this._y.hashCode();\r\n\t\t}\r\n\t}\r\n}\r\n"]}