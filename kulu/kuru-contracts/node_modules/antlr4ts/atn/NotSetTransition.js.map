{"version": 3, "file": "NotSetTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/NotSetTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,8CAA4D;AAC5D,mDAAgD;AAIhD,IAAa,gBAAgB,GAA7B,MAAa,gBAAiB,SAAQ,6BAAa;IAClD,YAAqB,MAAgB,EAAY,GAAgB;QAChE,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACpB,CAAC;IAGD,IAAI,iBAAiB;QACpB,uBAA8B;IAC/B,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,MAAM,IAAI,cAAc;eAC3B,MAAM,IAAI,cAAc;eACxB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAC5D,CAAC;IAGM,QAAQ;QACd,OAAO,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;CACD,CAAA;AAfA;IADC,qBAAQ;yDAGR;AAGD;IADC,qBAAQ;+CAKR;AAGD;IADC,qBAAQ;gDAGR;AApBW,gBAAgB;IACf,WAAA,oBAAO,CAAA,EAAoB,WAAA,qBAAQ,CAAA;GADpC,gBAAgB,CAqB5B;AArBY,4CAAgB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.8483617-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { Override, NotNull, Nullable } from \"../Decorators\";\r\nimport { SetTransition } from \"./SetTransition\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\nexport class NotSetTransition extends SetTransition {\r\n\tconstructor(@NotNull target: ATNState, @Nullable set: IntervalSet) {\r\n\t\tsuper(target, set);\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.NOT_SET;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn symbol >= minVocabSymbol\r\n\t\t\t&& symbol <= maxVocabSymbol\r\n\t\t\t&& !super.matches(symbol, minVocabSymbol, maxVocabSymbol);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn \"~\" + super.toString();\r\n\t}\r\n}\r\n"]}