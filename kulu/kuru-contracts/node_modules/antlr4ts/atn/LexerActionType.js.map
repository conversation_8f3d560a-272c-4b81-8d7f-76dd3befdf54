{"version": 3, "file": "LexerActionType.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerActionType.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;;;GAKG;AACH,IAAkB,eAiCjB;AAjCD,WAAkB,eAAe;IAChC;;OAEG;IACH,2DAAO,CAAA;IACP;;OAEG;IACH,yDAAM,CAAA;IACN;;OAEG;IACH,qDAAI,CAAA;IACJ;;OAEG;IACH,qDAAI,CAAA;IACJ;;OAEG;IACH,6DAAQ,CAAA;IACR;;OAEG;IACH,+DAAS,CAAA;IACT;;OAEG;IACH,qDAAI,CAAA;IACJ;;OAEG;IACH,qDAAI,CAAA;AACL,CAAC,EAjCiB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAiChC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.0172086-07:00\r\n\r\n/**\r\n * Represents the serialization type of a {@link LexerAction}.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport const enum LexerActionType {\r\n\t/**\r\n\t * The type of a {@link LexerChannelAction} action.\r\n\t */\r\n\tCHANNEL,\r\n\t/**\r\n\t * The type of a {@link LexerCustomAction} action.\r\n\t */\r\n\tCUSTOM,\r\n\t/**\r\n\t * The type of a {@link LexerModeAction} action.\r\n\t */\r\n\tMODE,\r\n\t/**\r\n\t * The type of a {@link LexerMoreAction} action.\r\n\t */\r\n\tMORE,\r\n\t/**\r\n\t * The type of a {@link LexerPopModeAction} action.\r\n\t */\r\n\tPOP_MODE,\r\n\t/**\r\n\t * The type of a {@link LexerPushModeAction} action.\r\n\t */\r\n\tPUSH_MODE,\r\n\t/**\r\n\t * The type of a {@link LexerSkipAction} action.\r\n\t */\r\n\tSKIP,\r\n\t/**\r\n\t * The type of a {@link LexerTypeAction} action.\r\n\t */\r\n\tTYPE,\r\n}\r\n"]}