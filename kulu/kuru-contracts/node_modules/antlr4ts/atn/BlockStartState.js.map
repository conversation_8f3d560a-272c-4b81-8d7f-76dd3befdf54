{"version": 3, "file": "BlockStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/BlockStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH,mDAAgD;AAGhD,6CAA6C;AAC7C,MAAsB,eAAgB,SAAQ,6BAAa;CAG1D;AAHD,0CAGC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.9930394-07:00\r\n\r\nimport { BlockEndState } from \"./BlockEndState\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/**  The start of a regular `(...)` block. */\r\nexport abstract class BlockStartState extends DecisionState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic endState!: BlockEndState;\r\n}\r\n"]}