{"version": 3, "file": "AbstractPredicateTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/AbstractPredicateTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH,6CAA0C;AAE1C;;;GAGG;AACH,MAAsB,2BAA4B,SAAQ,uBAAU;IAEnE,YAAY,MAAgB;QAC3B,KAAK,CAAC,MAAM,CAAC,CAAC;IACf,CAAC;CAED;AAND,kEAMC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:24.6596177-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { Transition } from \"./Transition\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport abstract class AbstractPredicateTransition extends Transition {\r\n\r\n\tconstructor(target: ATNState) {\r\n\t\tsuper(target);\r\n\t}\r\n\r\n}\r\n"]}