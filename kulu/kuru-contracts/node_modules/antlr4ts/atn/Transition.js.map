{"version": 3, "file": "Transition.js", "sourceRoot": "", "sources": ["../../../src/atn/Transition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,8CAAwC;AAGxC;;;;;;;;;;;GAWG;AACH,IAAsB,UAAU,GAAhC,MAAsB,UAAU;IAkC/B,YAAqB,MAAgB;QACpC,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,CAAC;IAID;;;;;;;;OAQG;IACH,IAAI,SAAS;QACZ,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,KAAK;QACR,OAAO,SAAS,CAAC;IAClB,CAAC;CAGD,CAAA;AA7DuB,6BAAkB,GAAa;IACrD,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;IACN,WAAW;IACX,MAAM;IACN,QAAQ;IACR,KAAK;IACL,SAAS;IACT,UAAU;IACV,YAAY;CACZ,CAAC;AAmBF;IADC,oBAAO;0CACgB;AAhCH,UAAU;IAkClB,WAAA,oBAAO,CAAA;GAlCC,UAAU,CA8D/B;AA9DqB,gCAAU", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.8530496-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { IntervalSet } from \"../misc/IntervalSet\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\n/** An ATN transition between any two ATN states.  Subclasses define\r\n *  atom, set, epsilon, action, predicate, rule transitions.\r\n *\r\n *  This is a one way link.  It emanates from a state (usually via a list of\r\n *  transitions) and has a target state.\r\n *\r\n *  Since we never have to change the ATN transitions once we construct it,\r\n *  we can fix these transitions as specific classes. The DFA transitions\r\n *  on the other hand need to update the labels as it adds transitions to\r\n *  the states. We'll use the term Edge for the DFA to distinguish them from\r\n *  ATN transitions.\r\n */\r\nexport abstract class Transition {\r\n\tpublic static readonly serializationNames: string[] = [\r\n\t\t\"INVALID\",\r\n\t\t\"EPSILON\",\r\n\t\t\"RANGE\",\r\n\t\t\"RULE\",\r\n\t\t\"PREDICATE\",\r\n\t\t\"ATOM\",\r\n\t\t\"ACTION\",\r\n\t\t\"SET\",\r\n\t\t\"NOT_SET\",\r\n\t\t\"WILDCARD\",\r\n\t\t\"PRECEDENCE\",\r\n\t];\r\n\r\n\t// @SuppressWarnings(\"serial\")\r\n\t// static serializationTypes: Map<Class<? extends Transition>, number> =\r\n\t// \tCollections.unmodifiableMap(new HashMap<Class<? extends Transition>, Integer>() {{\r\n\t// \t\tput(EpsilonTransition.class, EPSILON);\r\n\t// \t\tput(RangeTransition.class, RANGE);\r\n\t// \t\tput(RuleTransition.class, RULE);\r\n\t// \t\tput(PredicateTransition.class, PREDICATE);\r\n\t// \t\tput(AtomTransition.class, ATOM);\r\n\t// \t\tput(ActionTransition.class, ACTION);\r\n\t// \t\tput(SetTransition.class, SET);\r\n\t// \t\tput(NotSetTransition.class, NOT_SET);\r\n\t// \t\tput(WildcardTransition.class, WILDCARD);\r\n\t// \t\tput(PrecedencePredicateTransition.class, PRECEDENCE);\r\n\t// \t}});\r\n\r\n\t/** The target of this transition. */\r\n\t@NotNull\r\n\tpublic target: ATNState;\r\n\r\n\tconstructor(@NotNull target: ATNState) {\r\n\t\tif (target == null) {\r\n\t\t\tthrow new Error(\"target cannot be null.\");\r\n\t\t}\r\n\r\n\t\tthis.target = target;\r\n\t}\r\n\r\n\tpublic abstract readonly serializationType: TransitionType;\r\n\r\n\t/**\r\n\t * Determines if the transition is an \"epsilon\" transition.\r\n\t *\r\n\t * The default implementation returns `false`.\r\n\t *\r\n\t * @returns `true` if traversing this transition in the ATN does not\r\n\t * consume an input symbol; otherwise, `false` if traversing this\r\n\t * transition consumes (matches) an input symbol.\r\n\t */\r\n\tget isEpsilon(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget label(): IntervalSet | undefined {\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tpublic abstract matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean;\r\n}\r\n"]}