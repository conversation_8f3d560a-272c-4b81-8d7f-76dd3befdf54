{"version": 3, "file": "TransitionType.js", "sourceRoot": "", "sources": ["../../../src/atn/TransitionType.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,IAAkB,cAYjB;AAZD,WAAkB,cAAc;IAC/B,8BAA8B;IAC9B,yDAAW,CAAA;IACX,qDAAS,CAAA;IACT,mDAAQ,CAAA;IACR,6DAAa,CAAA;IACb,mDAAQ,CAAA;IACR,uDAAU,CAAA;IACV,iDAAO,CAAA;IACP,yDAAW,CAAA;IACX,2DAAY,CAAA;IACZ,gEAAe,CAAA;AAChB,CAAC,EAZiB,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAY/B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.8530496-07:00\r\n\r\nexport const enum TransitionType {\r\n\t// constants for serialization\r\n\tEPSILON = 1,\r\n\tRANGE = 2,\r\n\tRULE = 3,\r\n\tPREDICATE = 4, // e.g., {isType(input.LT(1))}?\r\n\tATOM = 5,\r\n\tACTION = 6,\r\n\tSET = 7, // ~(A|B) or ~atom, wildcard, which convert to next 2\r\n\tNOT_SET = 8,\r\n\tWILDCARD = 9,\r\n\tPRECEDENCE = 10,\r\n}\r\n"]}