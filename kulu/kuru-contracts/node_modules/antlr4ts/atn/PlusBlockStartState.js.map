{"version": 3, "file": "PlusBlockStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/PlusBlockStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iDAA8C;AAC9C,uDAAoD;AACpD,8CAAyC;AAGzC;;;;GAIG;AACH,MAAa,mBAAoB,SAAQ,iCAAe;IAKvD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,gBAAgB,CAAC;IACtC,CAAC;CACD;AAHA;IADC,qBAAQ;oDAGR;AAPF,kDAQC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:34.9572142-07:00\r\n\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BlockStartState } from \"./BlockStartState\";\r\nimport { Override } from \"../Decorators\";\r\nimport { PlusLoopbackState } from \"./PlusLoopbackState\";\r\n\r\n/** Start of `(A|B|...)+` loop. Technically a decision state, but\r\n *  we don't use for code generation; somebody might need it, so I'm defining\r\n *  it for completeness. In reality, the {@link PlusLoopbackState} node is the\r\n *  real decision-making note for `A+`.\r\n */\r\nexport class PlusBlockStartState extends BlockStartState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic loopBackState!: PlusLoopbackState;\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.PLUS_BLOCK_START;\r\n\t}\r\n}\r\n"]}