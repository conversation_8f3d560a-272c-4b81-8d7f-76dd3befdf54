{"version": 3, "file": "LexerCustomAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerCustomAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;;;;;;;GAYG;AACH,MAAa,iBAAiB;IAI7B;;;;;;;;OAQG;IACH,YAAY,SAAiB,EAAE,WAAmB;QACjD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IAEH,IAAI,UAAU;QACb,sBAA8B;IAC/B,CAAC;IAED;;;;;;;;;;OAUG;IAEH,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,iBAAiB,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU;eACrC,IAAI,CAAC,YAAY,KAAK,GAAG,CAAC,YAAY,CAAC;IAC5C,CAAC;CACD;AAnDA;IADC,qBAAQ;mDAGR;AAcD;IADC,qBAAQ;4DAGR;AASD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;gDAEtB;AAGD;IADC,qBAAQ;iDAOR;AAGD;IADC,qBAAQ;+CAUR;AA5FF,8CA6FC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.6567992-07:00\r\n\r\nimport { Lexer } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Executes a custom lexer action by calling {@link Recognizer#action} with the\r\n * rule and action indexes assigned to the custom action. The implementation of\r\n * a custom action is added to the generated code for the lexer in an override\r\n * of {@link Recognizer#action} when the grammar is compiled.\r\n *\r\n * This class may represent embedded actions created with the `{...}`\r\n * syntax in ANTLR 4, as well as actions created for lexer commands where the\r\n * command argument could not be evaluated when the grammar was compiled.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerCustomAction implements LexerAction {\r\n\tprivate readonly _ruleIndex: number;\r\n\tprivate readonly _actionIndex: number;\r\n\r\n\t/**\r\n\t * Constructs a custom lexer action with the specified rule and action\r\n\t * indexes.\r\n\t *\r\n\t * @param ruleIndex The rule index to use for calls to\r\n\t * {@link Recognizer#action}.\r\n\t * @param actionIndex The action index to use for calls to\r\n\t * {@link Recognizer#action}.\r\n\t */\r\n\tconstructor(ruleIndex: number, actionIndex: number) {\r\n\t\tthis._ruleIndex = ruleIndex;\r\n\t\tthis._actionIndex = actionIndex;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the rule index to use for calls to {@link Recognizer#action}.\r\n\t *\r\n\t * @returns The rule index for the custom action.\r\n\t */\r\n\tget ruleIndex(): number {\r\n\t\treturn this._ruleIndex;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the action index to use for calls to {@link Recognizer#action}.\r\n\t *\r\n\t * @returns The action index for the custom action.\r\n\t */\r\n\tget actionIndex(): number {\r\n\t\treturn this._actionIndex;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * @returns This method returns {@link LexerActionType#CUSTOM}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.CUSTOM;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets whether the lexer action is position-dependent. Position-dependent\r\n\t * actions may have different semantics depending on the {@link CharStream}\r\n\t * index at the time the action is executed.\r\n\t *\r\n\t * Custom actions are position-dependent since they may represent a\r\n\t * user-defined embedded action which makes calls to methods like\r\n\t * {@link Lexer#getText}.\r\n\t *\r\n\t * @returns This method returns `true`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * Custom actions are implemented by calling {@link Lexer#action} with the\r\n\t * appropriate rule and action indexes.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.action(undefined, this._ruleIndex, this._actionIndex);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\thash = MurmurHash.update(hash, this._ruleIndex);\r\n\t\thash = MurmurHash.update(hash, this._actionIndex);\r\n\t\treturn MurmurHash.finish(hash, 3);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerCustomAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._ruleIndex === obj._ruleIndex\r\n\t\t\t&& this._actionIndex === obj._actionIndex;\r\n\t}\r\n}\r\n"]}