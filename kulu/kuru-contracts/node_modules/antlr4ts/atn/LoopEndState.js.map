{"version": 3, "file": "LoopEndState.js", "sourceRoot": "", "sources": ["../../../src/atn/LoopEndState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,yCAAsC;AACtC,iDAA8C;AAC9C,8CAAyC;AAEzC,qCAAqC;AACrC,MAAa,YAAa,SAAQ,mBAAQ;IAKzC,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,QAAQ,CAAC;IAC9B,CAAC;CACD;AAHA;IADC,qBAAQ;6CAGR;AAPF,oCAQC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:30.7737978-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/** Mark the end of a * or + loop. */\r\nexport class LoopEndState extends ATNState {\r\n\t// This is always set during ATN deserialization\r\n\tpublic loopBackState!: ATNState;\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.LOOP_END;\r\n\t}\r\n}\r\n"]}