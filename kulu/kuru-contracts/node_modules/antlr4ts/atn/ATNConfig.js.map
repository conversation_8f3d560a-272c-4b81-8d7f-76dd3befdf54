{"version": 3, "file": "ATNConfig.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNConfig.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AAExD,mDAAgD;AAGhD,mDAAgD;AAChD,8CAAkD;AAClD,+EAA4E;AAC5E,2DAAwD;AAGxD,uDAAoD;AAEpD,iCAAiC;AAEjC;;;;GAIG;AACH,MAAM,0BAA0B,GAAW,UAAU,CAAC;AAEtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,IAAa,SAAS,GAAtB,MAAa,SAAS;IAwBrB,YAAqB,KAAe,EAAE,WAA+B,EAAW,OAA0B;QACzG,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACpC,MAAM,CAAC,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW,CAAC,CAAC;YACjD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,uBAAuB,GAAG,WAAW,CAAC;YAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACxB;aAAM;YACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,uBAAuB,GAAG,WAAW,CAAC,uBAAuB,CAAC;YACnE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACxB;IACF,CAAC;IAQM,MAAM,CAAC,MAAM,CAAU,KAAe,EAAE,GAAW,EAAE,OAA0B,EAAW,kBAAmC,iCAAe,CAAC,IAAI,EAAE,mBAAyC;QAClM,IAAI,eAAe,KAAK,iCAAe,CAAC,IAAI,EAAE;YAC7C,IAAI,mBAAmB,IAAI,IAAI,EAAE;gBAChC,OAAO,IAAI,8BAA8B,CAAC,mBAAmB,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC5G;iBACI;gBACJ,OAAO,IAAI,wBAAwB,CAAC,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;aAC1E;SACD;aACI,IAAI,mBAAmB,IAAI,IAAI,EAAE;YACrC,OAAO,IAAI,eAAe,CAAC,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SAC5E;aACI;YACJ,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC1C;IACF,CAAC;IAED,4DAA4D;IAE5D,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED,kEAAkE;IAClE,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC;IAClD,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAU,OAA0B;QAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,IAAI,uBAAuB;QAC1B,OAAO,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;OAUG;IACH,IAAI,iBAAiB;QACpB,OAAO,CAAC,IAAI,CAAC,uBAAuB,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACrD,CAAC;IAED,IAAI,iBAAiB,CAAC,iBAAyB;QAC9C,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAC;QAC/B,4FAA4F;QAC5F,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,iBAAiB,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACjH,CAAC;IAED,IAAI,mBAAmB;QACtB,OAAO,SAAS,CAAC;IAClB,CAAC;IAGD,IAAI,eAAe;QAClB,OAAO,iCAAe,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED,IAAI,iCAAiC;QACpC,OAAO,KAAK,CAAC;IACd,CAAC;IAGM,KAAK;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAMM,SAAS,CAAC,YAAY,CAAC,KAAe,EAAE,cAAuB,EAAE,IAAgE;QACvI,IAAI,IAAI,IAAI,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAChH;aAAM,IAAI,IAAI,YAAY,qCAAiB,EAAE;YAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACvG;aAAM,IAAI,IAAI,YAAY,iCAAe,EAAE;YAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAChG;aAAM;YACN,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;SAC5F;IACF,CAAC;IAEO,aAAa,CAAU,KAAe,EAAE,OAA0B,EAAW,eAAgC,EAAE,cAAuB,EAAE,mBAAoD;QACnM,IAAI,sBAAsB,GAAY,cAAc,IAAI,SAAS,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtG,IAAI,eAAe,KAAK,iCAAe,CAAC,IAAI,EAAE;YAC7C,IAAI,mBAAmB,IAAI,IAAI,IAAI,sBAAsB,EAAE;gBAC1D,OAAO,IAAI,8BAA8B,CAAC,mBAAmB,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;aAC9H;iBACI;gBACJ,OAAO,IAAI,wBAAwB,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3E;SACD;aACI,IAAI,mBAAmB,IAAI,IAAI,IAAI,sBAAsB,EAAE;YAC/D,OAAO,IAAI,eAAe,CAAC,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;SAC9F;aACI;YACJ,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC3C;IACF,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,MAAiB,EAAE,MAAgB;QACxE,OAAO,MAAM,CAAC,iCAAiC;eAC3C,MAAM,YAAY,6BAAa,IAAI,MAAM,CAAC,SAAS,CAAC;IACzD,CAAC;IAIM,aAAa,CAAC,OAAmC,EAAE,YAAoC;QAC7F,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAChC,IAAI,eAAe,GAAsB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACjG,IAAI,MAAM,GAAc,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;YAC3E,OAAO,MAAM,CAAC;SACd;aAAM;YACN,IAAI,eAAe,GAAsB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC3F,IAAI,MAAM,GAAc,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;YAC3E,OAAO,MAAM,CAAC;SACd;IACF,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,QAAQ,CAAC,SAAoB;QACnC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,SAAS,CAAC,KAAK,CAAC,WAAW;eACtD,IAAI,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG;eAC1B,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE;YAC5D,OAAO,KAAK,CAAC;SACb;QAED,IAAI,YAAY,GAAwB,EAAE,CAAC;QAC3C,IAAI,aAAa,GAAwB,EAAE,CAAC;QAC5C,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,IAAI,EAAE;YACZ,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;YAC9B,IAAI,KAAK,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACpB,MAAM;aACN;YAED,IAAI,IAAI,KAAK,KAAK,EAAE;gBACnB,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACb;YAED,IAAI,KAAK,CAAC,OAAO,EAAE;gBAClB,OAAO,IAAI,CAAC,QAAQ,CAAC;aACrB;iBAAM;gBACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;oBACpC,IAAI,KAAK,GAAW,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,IAAI,KAAK,GAAG,CAAC,EAAE;wBACd,kDAAkD;wBAClD,OAAO,KAAK,CAAC;qBACb;oBAED,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBACzC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBACvC;aACD;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,4BAA4B;QAC/B,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,0BAA0B,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,4BAA4B,CAAC,KAAc;QAC9C,IAAI,KAAK,EAAE;YACV,IAAI,CAAC,uBAAuB,IAAI,0BAA0B,CAAC;SAC3D;aACI;YACJ,IAAI,CAAC,uBAAuB,IAAI,CAAC,0BAA0B,CAAC;SAC5D;IACF,CAAC;IAED;;;OAGG;IAEI,MAAM,CAAC,CAAM;QACnB,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,CAAC,YAAY,SAAS,CAAC,EAAE;YACrC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW;eACjD,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;eAClB,IAAI,CAAC,uBAAuB,KAAK,CAAC,CAAC,uBAAuB;eAC1D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;eAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC;eAC9C,IAAI,CAAC,4BAA4B,KAAK,CAAC,CAAC,4BAA4B;eACpE,IAAI,CAAC,iCAAiC,KAAK,CAAC,CAAC,iCAAiC;eAC9E,mDAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC;IAC/F,CAAC;IAGM,QAAQ;QACd,IAAI,QAAQ,GAAW,uBAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC/D,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACjD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACrD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvF,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACjE,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC1C,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,WAAW;QACjB,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC;QAE7B,IAAI,OAAO,GAAG,IAAI,+BAAc,CAA4B,qCAAiB,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,QAAQ,GAAwB,EAAE,CAAC;QACvC,SAAS,eAAe,CAAC,OAA0B;YAClD,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;YAC7B,IAAI,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACrD,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,2BAA2B;gBAC3B,OAAO,MAAM,CAAC;aACd;YAED,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,IAAI,EAAE;YACZ,IAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,EAAE;gBACb,MAAM;aACN;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;gBAChD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;aACpE;SACD;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAKM,QAAQ,CAAC,KAA4B,EAAE,OAAiB,EAAE,WAAqB;QACrF,+EAA+E;QAC/E,IAAI,WAAW,IAAI,IAAI,EAAE;YACxB,WAAW,GAAG,OAAO,IAAI,IAAI,CAAC;SAC9B;QAED,IAAI,OAAO,IAAI,IAAI,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC;SACf;QAED,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,mCAAmC;QACnC,wBAAwB;QACxB,0DAA0D;QAC1D,YAAY;QACZ,yCAAyC;QACzC,KAAK;QACL,IAAI;QACJ,IAAI,QAAkB,CAAC;QACvB,IAAI,WAAW,EAAE;YAChB,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SACjE;aACI;YACJ,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;SACjB;QAED,IAAI,KAAK,GAAY,IAAI,CAAC;QAC1B,KAAK,IAAI,WAAW,IAAI,QAAQ,EAAE;YACjC,IAAI,KAAK,EAAE;gBACV,KAAK,GAAG,KAAK,CAAC;aACd;iBACI;gBACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;aACd;YAED,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACb,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,OAAO,EAAE;gBACZ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;YACD,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;aACrB;YACD,IAAI,IAAI,CAAC,eAAe,KAAK,iCAAe,CAAC,IAAI,EAAE;gBAClD,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aAC9B;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBACjC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC3C;YACD,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;SACb;QACD,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;CACD,CAAA;AA/YA;IADC,oBAAO;yCACiB;AAgBzB;IADC,oBAAO;2CAC4B;AA2CpC;IADC,oBAAO;sCAGP;AAQD;IADC,oBAAO;IAKK,WAAA,oBAAO,CAAA;wCAFnB;AAqCD;IADC,oBAAO;gDAGP;AAOD;IADC,qBAAQ;sCAGR;AAkBD;IAAuB,WAAA,oBAAO,CAAA,EAA+C,WAAA,oBAAO,CAAA;8CAgBnF;AA6GD;IADC,qBAAQ;uCAgBR;AAGD;IADC,qBAAQ;yCAYR;AA3PD;IAAsB,WAAA,oBAAO,CAAA,EAA4D,WAAA,oBAAO,CAAA;6BAe/F;AA1DW,SAAS;IAwBR,WAAA,oBAAO,CAAA,EAAoD,WAAA,oBAAO,CAAA;GAxBnE,SAAS,CAkZrB;AAlZY,8BAAS;AAoZtB;;;;;;GAMG;AACH,IAAM,wBAAwB,GAA9B,MAAM,wBAAyB,SAAQ,SAAS;IAM/C,YAAY,eAAgC,EAAW,KAAe,EAAW,WAA+B,EAAE,OAA0B;QAC3I,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACpC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SACnC;aAAM;YACN,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IACzC,CAAC;IAGD,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;CAED,CAAA;AAnBA;IADC,oBAAO;kEACkC;AAe1C;IADC,qBAAQ;+DAGR;AAnBI,wBAAwB;IAMkB,WAAA,oBAAO,CAAA,EAAmB,WAAA,oBAAO,CAAA;GAN3E,wBAAwB,CAqB7B;AAED;;;;;;GAMG;AACH,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,SAAS;IAMtC,YAAY,mBAAoD,EAAW,KAAe,EAAW,WAA+B,EAAE,OAA0B,EAAE,8BAAuC;QACxM,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACpC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SACnC;aAAM;YACN,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YACnC,IAAI,WAAW,CAAC,eAAe,KAAK,iCAAe,CAAC,IAAI,EAAE;gBACzD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;aACjC;SACD;QAED,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,IAAI,CAAC,8BAA8B,GAAG,8BAA8B,CAAC;IACtE,CAAC;IAGD,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IAClC,CAAC;IAGD,IAAI,iCAAiC;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC5C,CAAC;CACD,CAAA;AARA;IADC,qBAAQ;0DAGR;AAGD;IADC,qBAAQ;wEAGR;AA5BI,eAAe;IAM+C,WAAA,oBAAO,CAAA,EAAmB,WAAA,oBAAO,CAAA;GAN/F,eAAe,CA6BpB;AAED;;;;;;GAMG;AACH,IAAM,8BAA8B,GAApC,MAAM,8BAA+B,SAAQ,wBAAwB;IAMpE,YAAY,mBAAoD,EAAW,eAAgC,EAAW,KAAe,EAAE,WAA+B,EAAE,OAA0B,EAAE,8BAAuC;QAC1O,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACpC,KAAK,CAAC,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;aAAM;YACN,KAAK,CAAC,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,IAAI,CAAC,8BAA8B,GAAG,8BAA8B,CAAC;IACtE,CAAC;IAGD,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IAClC,CAAC;IAGD,IAAI,iCAAiC;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC5C,CAAC;CACD,CAAA;AARA;IADC,qBAAQ;yEAGR;AAGD;IADC,qBAAQ;uFAGR;AAzBI,8BAA8B;IAMgC,WAAA,oBAAO,CAAA,EAAoC,WAAA,oBAAO,CAAA;GANhH,8BAA8B,CA0BnC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:25.2796692-07:00\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { DecisionState } from \"./DecisionState\";\r\nimport { Equatable } from \"../misc/Stubs\";\r\nimport { LexerActionExecutor } from \"./LexerActionExecutor\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { PredictionContextCache } from \"./PredictionContextCache\";\r\nimport { Recognizer } from \"../Recognizer\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\n\r\nimport * as assert from \"assert\";\r\n\r\n/**\r\n * This field stores the bit mask for implementing the\r\n * {@link #isPrecedenceFilterSuppressed} property as a bit within the\r\n * existing {@link #altAndOuterContextDepth} field.\r\n */\r\nconst SUPPRESS_PRECEDENCE_FILTER: number = 0x80000000;\r\n\r\n/**\r\n * Represents a location with context in an ATN. The location is identified by the following values:\r\n *\r\n * * The current ATN state\r\n * * The predicted alternative\r\n * * The semantic context which must be true for this configuration to be enabled\r\n * * The syntactic context, which is represented as a graph-structured stack whose path(s) lead to the root of the rule\r\n *   invocations leading to this state\r\n *\r\n * In addition to these values, `ATNConfig` stores several properties about paths taken to get to the location which\r\n * were added over time to help with performance, correctness, and/or debugging.\r\n *\r\n * * `reachesIntoOuterContext`:: Used to ensure semantic predicates are not evaluated in the wrong context.\r\n * * `hasPassedThroughNonGreedyDecision`: Used for enabling first-match-wins instead of longest-match-wins after\r\n *   crossing a non-greedy decision.\r\n * * `lexerActionExecutor`: Used for tracking the lexer action(s) to execute should this instance be selected during\r\n *   lexing.\r\n * * `isPrecedenceFilterSuppressed`: A state variable for one of the dynamic disambiguation strategies employed by\r\n *   `ParserATNSimulator.applyPrecedenceFilter`.\r\n *\r\n * Due to the use of a graph-structured stack, a single `ATNConfig` is capable of representing many individual ATN\r\n * configurations which reached the same location in an ATN by following different paths.\r\n *\r\n * PERF: To conserve memory, `ATNConfig` is split into several different concrete types. `ATNConfig` itself stores the\r\n * minimum amount of information typically used to define an `ATNConfig` instance. Various derived types provide\r\n * additional storage space for cases where a non-default value is used for some of the object properties. The\r\n * `ATNConfig.create` and `ATNConfig.transform` methods automatically select the smallest concrete type capable of\r\n * representing the unique information for any given `ATNConfig`.\r\n */\r\nexport class ATNConfig implements Equatable {\r\n\t/** The ATN state associated with this configuration */\r\n\t@NotNull\r\n\tprivate _state: ATNState;\r\n\r\n\t/**\r\n\t * This is a bit-field currently containing the following values.\r\n\t *\r\n\t * * 0x00FFFFFF: Alternative\r\n\t * * 0x7F000000: Outer context depth\r\n\t * * 0x80000000: Suppress precedence filter\r\n\t */\r\n\tprivate altAndOuterContextDepth: number;\r\n\r\n\t/** The stack of invoking states leading to the rule/states associated\r\n\t *  with this config.  We track only those contexts pushed during\r\n\t *  execution of the ATN simulator.\r\n\t */\r\n\t@NotNull\r\n\tprivate _context: PredictionContext;\r\n\r\n\tconstructor(/*@NotNull*/ state: ATNState, alt: number, /*@NotNull*/ context: PredictionContext);\r\n\tconstructor(/*@NotNull*/ state: ATNState, /*@NotNull*/ c: ATNConfig, /*@NotNull*/ context: PredictionContext);\r\n\r\n\tconstructor(@NotNull state: ATNState, altOrConfig: number | ATNConfig, @NotNull context: PredictionContext) {\r\n\t\tif (typeof altOrConfig === \"number\") {\r\n\t\t\tassert((altOrConfig & 0xFFFFFF) === altOrConfig);\r\n\t\t\tthis._state = state;\r\n\t\t\tthis.altAndOuterContextDepth = altOrConfig;\r\n\t\t\tthis._context = context;\r\n\t\t} else {\r\n\t\t\tthis._state = state;\r\n\t\t\tthis.altAndOuterContextDepth = altOrConfig.altAndOuterContextDepth;\r\n\t\t\tthis._context = context;\r\n\t\t}\r\n\t}\r\n\r\n\tpublic static create(/*@NotNull*/ state: ATNState, alt: number, context: PredictionContext): ATNConfig;\r\n\r\n\tpublic static create(/*@NotNull*/ state: ATNState, alt: number, context: PredictionContext, /*@NotNull*/ semanticContext: SemanticContext): ATNConfig;\r\n\r\n\tpublic static create(/*@NotNull*/ state: ATNState, alt: number, context: PredictionContext, /*@*/ semanticContext: SemanticContext, lexerActionExecutor: LexerActionExecutor | undefined): ATNConfig;\r\n\r\n\tpublic static create(@NotNull state: ATNState, alt: number, context: PredictionContext, @NotNull semanticContext: SemanticContext = SemanticContext.NONE, lexerActionExecutor?: LexerActionExecutor): ATNConfig {\r\n\t\tif (semanticContext !== SemanticContext.NONE) {\r\n\t\t\tif (lexerActionExecutor != null) {\r\n\t\t\t\treturn new ActionSemanticContextATNConfig(lexerActionExecutor, semanticContext, state, alt, context, false);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\treturn new SemanticContextATNConfig(semanticContext, state, alt, context);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (lexerActionExecutor != null) {\r\n\t\t\treturn new ActionATNConfig(lexerActionExecutor, state, alt, context, false);\r\n\t\t}\r\n\t\telse {\r\n\t\t\treturn new ATNConfig(state, alt, context);\r\n\t\t}\r\n\t}\r\n\r\n\t/** Gets the ATN state associated with this configuration */\r\n\t@NotNull\r\n\tget state(): ATNState {\r\n\t\treturn this._state;\r\n\t}\r\n\r\n\t/** What alt (or lexer rule) is predicted by this configuration */\r\n\tget alt(): number {\r\n\t\treturn this.altAndOuterContextDepth & 0x00FFFFFF;\r\n\t}\r\n\r\n\t@NotNull\r\n\tget context(): PredictionContext {\r\n\t\treturn this._context;\r\n\t}\r\n\r\n\tset context(@NotNull context: PredictionContext) {\r\n\t\tthis._context = context;\r\n\t}\r\n\r\n\tget reachesIntoOuterContext(): boolean {\r\n\t\treturn this.outerContextDepth !== 0;\r\n\t}\r\n\r\n\t/**\r\n\t * We cannot execute predicates dependent upon local context unless\r\n\t * we know for sure we are in the correct context. Because there is\r\n\t * no way to do this efficiently, we simply cannot evaluate\r\n\t * dependent predicates unless we are in the rule that initially\r\n\t * invokes the ATN simulator.\r\n\t *\r\n\t * closure() tracks the depth of how far we dip into the outer context:\r\n\t * depth &gt; 0.  Note that it may not be totally accurate depth since I\r\n\t * don't ever decrement. TODO: make it a boolean then\r\n\t */\r\n\tget outerContextDepth(): number {\r\n\t\treturn (this.altAndOuterContextDepth >>> 24) & 0x7F;\r\n\t}\r\n\r\n\tset outerContextDepth(outerContextDepth: number) {\r\n\t\tassert(outerContextDepth >= 0);\r\n\t\t// saturate at 0x7F - everything but zero/positive is only used for debug information anyway\r\n\t\touterContextDepth = Math.min(outerContextDepth, 0x7F);\r\n\t\tthis.altAndOuterContextDepth = ((outerContextDepth << 24) | (this.altAndOuterContextDepth & ~0x7F000000) >>> 0);\r\n\t}\r\n\r\n\tget lexerActionExecutor(): LexerActionExecutor | undefined {\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t@NotNull\r\n\tget semanticContext(): SemanticContext {\r\n\t\treturn SemanticContext.NONE;\r\n\t}\r\n\r\n\tget hasPassedThroughNonGreedyDecision(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic clone(): ATNConfig {\r\n\t\treturn this.transform(this.state, false);\r\n\t}\r\n\r\n\tpublic transform(/*@NotNull*/ state: ATNState, checkNonGreedy: boolean): ATNConfig;\r\n\tpublic transform(/*@NotNull*/ state: ATNState, checkNonGreedy: boolean, /*@NotNull*/ semanticContext: SemanticContext): ATNConfig;\r\n\tpublic transform(/*@NotNull*/ state: ATNState, checkNonGreedy: boolean, context: PredictionContext): ATNConfig;\r\n\tpublic transform(/*@NotNull*/ state: ATNState, checkNonGreedy: boolean, lexerActionExecutor: LexerActionExecutor): ATNConfig;\r\n\tpublic transform(/*@NotNull*/ state: ATNState, checkNonGreedy: boolean, arg2?: SemanticContext | PredictionContext | LexerActionExecutor): ATNConfig {\r\n\t\tif (arg2 == null) {\r\n\t\t\treturn this.transformImpl(state, this._context, this.semanticContext, checkNonGreedy, this.lexerActionExecutor);\r\n\t\t} else if (arg2 instanceof PredictionContext) {\r\n\t\t\treturn this.transformImpl(state, arg2, this.semanticContext, checkNonGreedy, this.lexerActionExecutor);\r\n\t\t} else if (arg2 instanceof SemanticContext) {\r\n\t\t\treturn this.transformImpl(state, this._context, arg2, checkNonGreedy, this.lexerActionExecutor);\r\n\t\t} else {\r\n\t\t\treturn this.transformImpl(state, this._context, this.semanticContext, checkNonGreedy, arg2);\r\n\t\t}\r\n\t}\r\n\r\n\tprivate transformImpl(@NotNull state: ATNState, context: PredictionContext, @NotNull semanticContext: SemanticContext, checkNonGreedy: boolean, lexerActionExecutor: LexerActionExecutor | undefined): ATNConfig {\r\n\t\tlet passedThroughNonGreedy: boolean = checkNonGreedy && ATNConfig.checkNonGreedyDecision(this, state);\r\n\t\tif (semanticContext !== SemanticContext.NONE) {\r\n\t\t\tif (lexerActionExecutor != null || passedThroughNonGreedy) {\r\n\t\t\t\treturn new ActionSemanticContextATNConfig(lexerActionExecutor, semanticContext, state, this, context, passedThroughNonGreedy);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\treturn new SemanticContextATNConfig(semanticContext, state, this, context);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (lexerActionExecutor != null || passedThroughNonGreedy) {\r\n\t\t\treturn new ActionATNConfig(lexerActionExecutor, state, this, context, passedThroughNonGreedy);\r\n\t\t}\r\n\t\telse {\r\n\t\t\treturn new ATNConfig(state, this, context);\r\n\t\t}\r\n\t}\r\n\r\n\tprivate static checkNonGreedyDecision(source: ATNConfig, target: ATNState): boolean {\r\n\t\treturn source.hasPassedThroughNonGreedyDecision\r\n\t\t\t|| target instanceof DecisionState && target.nonGreedy;\r\n\t}\r\n\r\n\tpublic appendContext(context: number, contextCache: PredictionContextCache): ATNConfig;\r\n\tpublic appendContext(context: PredictionContext, contextCache: PredictionContextCache): ATNConfig;\r\n\tpublic appendContext(context: number | PredictionContext, contextCache: PredictionContextCache): ATNConfig {\r\n\t\tif (typeof context === \"number\") {\r\n\t\t\tlet appendedContext: PredictionContext = this.context.appendSingleContext(context, contextCache);\r\n\t\t\tlet result: ATNConfig = this.transform(this.state, false, appendedContext);\r\n\t\t\treturn result;\r\n\t\t} else {\r\n\t\t\tlet appendedContext: PredictionContext = this.context.appendContext(context, contextCache);\r\n\t\t\tlet result: ATNConfig = this.transform(this.state, false, appendedContext);\r\n\t\t\treturn result;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Determines if this `ATNConfig` fully contains another `ATNConfig`.\r\n\t *\r\n\t * An ATN configuration represents a position (including context) in an ATN during parsing. Since `ATNConfig` stores\r\n\t * the context as a graph, a single `ATNConfig` instance is capable of representing many ATN configurations which\r\n\t * are all in the same \"location\" but have different contexts. These `ATNConfig` instances are again merged when\r\n\t * they are added to an `ATNConfigSet`. This method supports `ATNConfigSet.contains` by evaluating whether a\r\n\t * particular `ATNConfig` contains all of the ATN configurations represented by another `ATNConfig`.\r\n\t *\r\n\t * An `ATNConfig` _a_ contains another `ATNConfig` _b_ if all of the following conditions are met:\r\n\t *\r\n\t * * The configurations are in the same state (`state`)\r\n\t * * The configurations predict the same alternative (`alt`)\r\n\t * * The semantic context of _a_ implies the semantic context of _b_ (this method performs a weaker equality check)\r\n\t * * Joining the prediction contexts of _a_ and _b_ results in the prediction context of _a_\r\n\t *\r\n\t * This method implements a conservative approximation of containment. As a result, when this method returns `true`\r\n\t * it is known that parsing from `subconfig` can only recognize a subset of the inputs which can be recognized\r\n\t * starting at the current `ATNConfig`. However, due to the imprecise evaluation of implication for the semantic\r\n\t * contexts, no assumptions can be made about the relationship between the configurations when this method returns\r\n\t * `false`.\r\n\t *\r\n\t * @param subconfig The sub configuration.\r\n\t * @returns `true` if this configuration contains `subconfig`; otherwise, `false`.\r\n\t */\r\n\tpublic contains(subconfig: ATNConfig): boolean {\r\n\t\tif (this.state.stateNumber !== subconfig.state.stateNumber\r\n\t\t\t|| this.alt !== subconfig.alt\r\n\t\t\t|| !this.semanticContext.equals(subconfig.semanticContext)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tlet leftWorkList: PredictionContext[] = [];\r\n\t\tlet rightWorkList: PredictionContext[] = [];\r\n\t\tleftWorkList.push(this.context);\r\n\t\trightWorkList.push(subconfig.context);\r\n\t\twhile (true) {\r\n\t\t\tlet left = leftWorkList.pop();\r\n\t\t\tlet right = rightWorkList.pop();\r\n\t\t\tif (!left || !right) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tif (left === right) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\tif (left.size < right.size) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tif (right.isEmpty) {\r\n\t\t\t\treturn left.hasEmpty;\r\n\t\t\t} else {\r\n\t\t\t\tfor (let i = 0; i < right.size; i++) {\r\n\t\t\t\t\tlet index: number = left.findReturnState(right.getReturnState(i));\r\n\t\t\t\t\tif (index < 0) {\r\n\t\t\t\t\t\t// assumes invokingStates has no duplicate entries\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tleftWorkList.push(left.getParent(index));\r\n\t\t\t\t\trightWorkList.push(right.getParent(i));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget isPrecedenceFilterSuppressed(): boolean {\r\n\t\treturn (this.altAndOuterContextDepth & SUPPRESS_PRECEDENCE_FILTER) !== 0;\r\n\t}\r\n\r\n\tset isPrecedenceFilterSuppressed(value: boolean) {\r\n\t\tif (value) {\r\n\t\t\tthis.altAndOuterContextDepth |= SUPPRESS_PRECEDENCE_FILTER;\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.altAndOuterContextDepth &= ~SUPPRESS_PRECEDENCE_FILTER;\r\n\t\t}\r\n\t}\r\n\r\n\t/** An ATN configuration is equal to another if both have\r\n\t *  the same state, they predict the same alternative, and\r\n\t *  syntactic/semantic contexts are the same.\r\n\t */\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (this === o) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(o instanceof ATNConfig)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.state.stateNumber === o.state.stateNumber\r\n\t\t\t&& this.alt === o.alt\r\n\t\t\t&& this.reachesIntoOuterContext === o.reachesIntoOuterContext\r\n\t\t\t&& this.context.equals(o.context)\r\n\t\t\t&& this.semanticContext.equals(o.semanticContext)\r\n\t\t\t&& this.isPrecedenceFilterSuppressed === o.isPrecedenceFilterSuppressed\r\n\t\t\t&& this.hasPassedThroughNonGreedyDecision === o.hasPassedThroughNonGreedyDecision\r\n\t\t\t&& ObjectEqualityComparator.INSTANCE.equals(this.lexerActionExecutor, o.lexerActionExecutor);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hashCode: number = MurmurHash.initialize(7);\r\n\t\thashCode = MurmurHash.update(hashCode, this.state.stateNumber);\r\n\t\thashCode = MurmurHash.update(hashCode, this.alt);\r\n\t\thashCode = MurmurHash.update(hashCode, this.reachesIntoOuterContext ? 1 : 0);\r\n\t\thashCode = MurmurHash.update(hashCode, this.context);\r\n\t\thashCode = MurmurHash.update(hashCode, this.semanticContext);\r\n\t\thashCode = MurmurHash.update(hashCode, this.hasPassedThroughNonGreedyDecision ? 1 : 0);\r\n\t\thashCode = MurmurHash.update(hashCode, this.lexerActionExecutor);\r\n\t\thashCode = MurmurHash.finish(hashCode, 7);\r\n\t\treturn hashCode;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a graphical representation of the current `ATNConfig` in Graphviz format. The graph can be stored to a\r\n\t * **.dot** file and then rendered to an image using Graphviz.\r\n\t *\r\n\t * @returns A Graphviz graph representing the current `ATNConfig`.\r\n\t *\r\n\t * @see http://www.graphviz.org/\r\n\t */\r\n\tpublic toDotString(): string {\r\n\t\tlet builder = \"\";\r\n\t\tbuilder += (\"digraph G {\\n\");\r\n\t\tbuilder += (\"rankdir=LR;\\n\");\r\n\r\n\t\tlet visited = new Array2DHashMap<PredictionContext, number>(PredictionContext.IdentityEqualityComparator.INSTANCE);\r\n\t\tlet workList: PredictionContext[] = [];\r\n\t\tfunction getOrAddContext(context: PredictionContext): number {\r\n\t\t\tlet newNumber = visited.size;\r\n\t\t\tlet result = visited.putIfAbsent(context, newNumber);\r\n\t\t\tif (result != null) {\r\n\t\t\t\t// Already saw this context\r\n\t\t\t\treturn result;\r\n\t\t\t}\r\n\r\n\t\t\tworkList.push(context);\r\n\t\t\treturn newNumber;\r\n\t\t}\r\n\r\n\t\tworkList.push(this.context);\r\n\t\tvisited.put(this.context, 0);\r\n\t\twhile (true) {\r\n\t\t\tlet current = workList.pop();\r\n\t\t\tif (!current) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let i = 0; i < current.size; i++) {\r\n\t\t\t\tbuilder += (\"  s\") + (getOrAddContext(current));\r\n\t\t\t\tbuilder += (\"->\");\r\n\t\t\t\tbuilder += (\"s\") + (getOrAddContext(current.getParent(i)));\r\n\t\t\t\tbuilder += (\"[label=\\\"\") + (current.getReturnState(i)) + (\"\\\"];\\n\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tbuilder += (\"}\\n\");\r\n\t\treturn builder.toString();\r\n\t}\r\n\r\n\tpublic toString(): string;\r\n\tpublic toString(recog: Recognizer<any, any> | undefined, showAlt: boolean): string;\r\n\tpublic toString(recog: Recognizer<any, any> | undefined, showAlt: boolean, showContext: boolean): string;\r\n\tpublic toString(recog?: Recognizer<any, any>, showAlt?: boolean, showContext?: boolean): string {\r\n\t\t// Must check showContext before showAlt to preserve original overload behavior\r\n\t\tif (showContext == null) {\r\n\t\t\tshowContext = showAlt != null;\r\n\t\t}\r\n\r\n\t\tif (showAlt == null) {\r\n\t\t\tshowAlt = true;\r\n\t\t}\r\n\r\n\t\tlet buf = \"\";\r\n\t\t// if (this.state.ruleIndex >= 0) {\r\n\t\t// \tif (recog != null) {\r\n\t\t// \t\tbuf += (recog.ruleNames[this.state.ruleIndex] + \":\");\r\n\t\t// \t} else {\r\n\t\t// \t\tbuf += (this.state.ruleIndex + \":\");\r\n\t\t// \t}\r\n\t\t// }\r\n\t\tlet contexts: string[];\r\n\t\tif (showContext) {\r\n\t\t\tcontexts = this.context.toStrings(recog, this.state.stateNumber);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tcontexts = [\"?\"];\r\n\t\t}\r\n\r\n\t\tlet first: boolean = true;\r\n\t\tfor (let contextDesc of contexts) {\r\n\t\t\tif (first) {\r\n\t\t\t\tfirst = false;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tbuf += (\", \");\r\n\t\t\t}\r\n\r\n\t\t\tbuf += (\"(\");\r\n\t\t\tbuf += (this.state);\r\n\t\t\tif (showAlt) {\r\n\t\t\t\tbuf += (\",\");\r\n\t\t\t\tbuf += (this.alt);\r\n\t\t\t}\r\n\t\t\tif (this.context) {\r\n\t\t\t\tbuf += (\",\");\r\n\t\t\t\tbuf += (contextDesc);\r\n\t\t\t}\r\n\t\t\tif (this.semanticContext !== SemanticContext.NONE) {\r\n\t\t\t\tbuf += (\",\");\r\n\t\t\t\tbuf += (this.semanticContext);\r\n\t\t\t}\r\n\t\t\tif (this.reachesIntoOuterContext) {\r\n\t\t\t\tbuf += (\",up=\") + (this.outerContextDepth);\r\n\t\t\t}\r\n\t\t\tbuf += (\")\");\r\n\t\t}\r\n\t\treturn buf.toString();\r\n\t}\r\n}\r\n\r\n/**\r\n * This class was derived from `ATNConfig` purely as a memory optimization. It allows for the creation of an `ATNConfig`\r\n * with a non-default semantic context.\r\n *\r\n * See the `ATNConfig` documentation for more information about conserving memory through the use of several concrete\r\n * types.\r\n */\r\nclass SemanticContextATNConfig extends ATNConfig {\r\n\t@NotNull\r\n\tprivate _semanticContext: SemanticContext;\r\n\r\n\tconstructor(semanticContext: SemanticContext, /*@NotNull*/ state: ATNState, alt: number, context: PredictionContext);\r\n\tconstructor(semanticContext: SemanticContext, /*@NotNull*/ state: ATNState, /*@NotNull*/ c: ATNConfig, context: PredictionContext);\r\n\tconstructor(semanticContext: SemanticContext, @NotNull state: ATNState, @NotNull altOrConfig: number | ATNConfig, context: PredictionContext) {\r\n\t\tif (typeof altOrConfig === \"number\") {\r\n\t\t\tsuper(state, altOrConfig, context);\r\n\t\t} else {\r\n\t\t\tsuper(state, altOrConfig, context);\r\n\t\t}\r\n\r\n\t\tthis._semanticContext = semanticContext;\r\n\t}\r\n\r\n\t@Override\r\n\tget semanticContext(): SemanticContext {\r\n\t\treturn this._semanticContext;\r\n\t}\r\n\r\n}\r\n\r\n/**\r\n * This class was derived from `ATNConfig` purely as a memory optimization. It allows for the creation of an `ATNConfig`\r\n * with a lexer action.\r\n *\r\n * See the `ATNConfig` documentation for more information about conserving memory through the use of several concrete\r\n * types.\r\n */\r\nclass ActionATNConfig extends ATNConfig {\r\n\tprivate _lexerActionExecutor?: LexerActionExecutor;\r\n\tprivate passedThroughNonGreedyDecision: boolean;\r\n\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, /*@NotNull*/ state: ATNState, alt: number, context: PredictionContext, passedThroughNonGreedyDecision: boolean);\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, /*@NotNull*/ state: ATNState, /*@NotNull*/ c: ATNConfig, context: PredictionContext, passedThroughNonGreedyDecision: boolean);\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, @NotNull state: ATNState, @NotNull altOrConfig: number | ATNConfig, context: PredictionContext, passedThroughNonGreedyDecision: boolean) {\r\n\t\tif (typeof altOrConfig === \"number\") {\r\n\t\t\tsuper(state, altOrConfig, context);\r\n\t\t} else {\r\n\t\t\tsuper(state, altOrConfig, context);\r\n\t\t\tif (altOrConfig.semanticContext !== SemanticContext.NONE) {\r\n\t\t\t\tthrow new Error(\"Not supported\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis._lexerActionExecutor = lexerActionExecutor;\r\n\t\tthis.passedThroughNonGreedyDecision = passedThroughNonGreedyDecision;\r\n\t}\r\n\r\n\t@Override\r\n\tget lexerActionExecutor(): LexerActionExecutor | undefined {\r\n\t\treturn this._lexerActionExecutor;\r\n\t}\r\n\r\n\t@Override\r\n\tget hasPassedThroughNonGreedyDecision(): boolean {\r\n\t\treturn this.passedThroughNonGreedyDecision;\r\n\t}\r\n}\r\n\r\n/**\r\n * This class was derived from `SemanticContextATNConfig` purely as a memory optimization. It allows for the creation of\r\n * an `ATNConfig` with both a lexer action and a non-default semantic context.\r\n *\r\n * See the `ATNConfig` documentation for more information about conserving memory through the use of several concrete\r\n * types.\r\n */\r\nclass ActionSemanticContextATNConfig extends SemanticContextATNConfig {\r\n\tprivate _lexerActionExecutor?: LexerActionExecutor;\r\n\tprivate passedThroughNonGreedyDecision: boolean;\r\n\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, /*@NotNull*/ semanticContext: SemanticContext, /*@NotNull*/ state: ATNState, alt: number, context: PredictionContext, passedThroughNonGreedyDecision: boolean);\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, /*@NotNull*/ semanticContext: SemanticContext, /*@NotNull*/ state: ATNState, /*@NotNull*/ c: ATNConfig, context: PredictionContext, passedThroughNonGreedyDecision: boolean);\r\n\tconstructor(lexerActionExecutor: LexerActionExecutor | undefined, @NotNull semanticContext: SemanticContext, @NotNull state: ATNState, altOrConfig: number | ATNConfig, context: PredictionContext, passedThroughNonGreedyDecision: boolean) {\r\n\t\tif (typeof altOrConfig === \"number\") {\r\n\t\t\tsuper(semanticContext, state, altOrConfig, context);\r\n\t\t} else {\r\n\t\t\tsuper(semanticContext, state, altOrConfig, context);\r\n\t\t}\r\n\r\n\t\tthis._lexerActionExecutor = lexerActionExecutor;\r\n\t\tthis.passedThroughNonGreedyDecision = passedThroughNonGreedyDecision;\r\n\t}\r\n\r\n\t@Override\r\n\tget lexerActionExecutor(): LexerActionExecutor | undefined {\r\n\t\treturn this._lexerActionExecutor;\r\n\t}\r\n\r\n\t@Override\r\n\tget hasPassedThroughNonGreedyDecision(): boolean {\r\n\t\treturn this.passedThroughNonGreedyDecision;\r\n\t}\r\n}\r\n"]}