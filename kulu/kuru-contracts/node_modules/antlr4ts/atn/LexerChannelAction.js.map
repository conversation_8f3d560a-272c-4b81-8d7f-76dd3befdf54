{"version": 3, "file": "LexerChannelAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerChannelAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;GAMG;AACH,MAAa,kBAAkB;IAG9B;;;OAGG;IACH,YAAY,OAAe;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;;;OAGG;IAEH,IAAI,UAAU;QACb,uBAA+B;IAChC,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAU,KAAY;QACnC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,kBAAkB,CAAC,EAAE;YAChD,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC;IACvC,CAAC;IAGM,QAAQ;QACd,OAAO,WAAW,IAAI,CAAC,QAAQ,GAAG,CAAC;IACpC,CAAC;CACD;AA/CA;IADC,qBAAQ;oDAGR;AAOD;IADC,qBAAQ;6DAGR;AASD;IADC,qBAAQ;IACO,WAAA,oBAAO,CAAA;iDAEtB;AAGD;IADC,qBAAQ;kDAMR;AAGD;IADC,qBAAQ;gDASR;AAGD;IADC,qBAAQ;kDAGR;AAvEF,gDAwEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.5634388-07:00\r\n\r\nimport { <PERSON><PERSON> } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Implements the `channel` lexer action by calling\r\n * {@link Lexer#setChannel} with the assigned channel.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerChannelAction implements LexerAction {\r\n\tprivate readonly _channel: number;\r\n\r\n\t/**\r\n\t * Constructs a new `channel` action with the specified channel value.\r\n\t * @param channel The channel value to pass to {@link Lexer#setChannel}.\r\n\t */\r\n\tconstructor(channel: number) {\r\n\t\tthis._channel = channel;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the channel to use for the {@link Token} created by the lexer.\r\n\t *\r\n\t * @returns The channel to use for the {@link Token} created by the lexer.\r\n\t */\r\n\tget channel(): number {\r\n\t\treturn this._channel;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns {@link LexerActionType#CHANNEL}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn LexerActionType.CHANNEL;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `false`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This action is implemented by calling {@link Lexer#setChannel} with the\r\n\t * value provided by {@link #getChannel}.\r\n\t */\r\n\t@Override\r\n\tpublic execute(@NotNull lexer: Lexer): void {\r\n\t\tlexer.channel = this._channel;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this.actionType);\r\n\t\thash = MurmurHash.update(hash, this._channel);\r\n\t\treturn MurmurHash.finish(hash, 2);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerChannelAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._channel === obj._channel;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn `channel(${this._channel})`;\r\n\t}\r\n}\r\n"]}