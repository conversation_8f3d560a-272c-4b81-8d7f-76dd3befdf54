{"version": 3, "file": "BasicBlockStartState.js", "sourceRoot": "", "sources": ["../../../src/atn/BasicBlockStartState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iDAA8C;AAC9C,uDAAoD;AACpD,8CAAyC;AAEzC;;;GAGG;AACH,MAAa,oBAAqB,SAAQ,iCAAe;IAGxD,IAAI,SAAS;QACZ,OAAO,2BAAY,CAAC,WAAW,CAAC;IACjC,CAAC;CAED;AAJA;IADC,qBAAQ;qDAGR;AALF,oDAOC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:27.7669801-07:00\r\n\r\nimport { ATNStateType } from \"./ATNStateType\";\r\nimport { BlockStartState } from \"./BlockStartState\";\r\nimport { Override } from \"../Decorators\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class BasicBlockStartState extends BlockStartState {\r\n\r\n\t@Override\r\n\tget stateType(): ATNStateType {\r\n\t\treturn ATNStateType.BLOCK_START;\r\n\t}\r\n\r\n}\r\n"]}