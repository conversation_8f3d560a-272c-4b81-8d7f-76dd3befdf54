{"version": 3, "file": "LexerIndexedCustomAction.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerIndexedCustomAction.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;;;;;;;GAYG;AACH,IAAa,wBAAwB,GAArC,MAAa,wBAAwB;IAIpC;;;;;;;;;;;;OAYG;IACH,YAAY,MAAc,EAAW,MAAmB;QACvD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED;;;;OAIG;IAEH,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IAEH,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;;OAGG;IAEH,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;OAKG;IAEI,OAAO,CAAC,KAAY;QAC1B,wEAAwE;QACxE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,OAAO,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,wBAAwB,CAAC,EAAE;YACtD,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO;eAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;CACD,CAAA;AAvDA;IADC,oBAAO;sDAGP;AASD;IADC,qBAAQ;0DAGR;AAOD;IADC,qBAAQ;mEAGR;AASD;IADC,qBAAQ;uDAIR;AAGD;IADC,qBAAQ;wDAMR;AAGD;IADC,qBAAQ;sDAUR;AA9FW,wBAAwB;IAiBP,WAAA,oBAAO,CAAA;GAjBxB,wBAAwB,CA+FpC;AA/FY,4DAAwB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.7613038-07:00\r\n\r\nimport { Lexer } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerActionType } from \"./LexerActionType\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * This implementation of {@link LexerAction} is used for tracking input offsets\r\n * for position-dependent actions within a {@link LexerActionExecutor}.\r\n *\r\n * This action is not serialized as part of the ATN, and is only required for\r\n * position-dependent lexer actions which appear at a location other than the\r\n * end of a rule. For more information about DFA optimizations employed for\r\n * lexer actions, see {@link LexerActionExecutor#append} and\r\n * {@link LexerActionExecutor#fixOffsetBeforeMatch}.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerIndexedCustomAction implements LexerAction {\r\n\tprivate readonly _offset: number;\r\n\tprivate readonly _action: LexerAction;\r\n\r\n\t/**\r\n\t * Constructs a new indexed custom action by associating a character offset\r\n\t * with a {@link LexerAction}.\r\n\t *\r\n\t * Note: This class is only required for lexer actions for which\r\n\t * {@link LexerAction#isPositionDependent} returns `true`.\r\n\t *\r\n\t * @param offset The offset into the input {@link CharStream}, relative to\r\n\t * the token start index, at which the specified lexer action should be\r\n\t * executed.\r\n\t * @param action The lexer action to execute at a particular offset in the\r\n\t * input {@link CharStream}.\r\n\t */\r\n\tconstructor(offset: number, @NotNull action: LexerAction) {\r\n\t\tthis._offset = offset;\r\n\t\tthis._action = action;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the location in the input {@link CharStream} at which the lexer\r\n\t * action should be executed. The value is interpreted as an offset relative\r\n\t * to the token start index.\r\n\t *\r\n\t * @returns The location in the input {@link CharStream} at which the lexer\r\n\t * action should be executed.\r\n\t */\r\n\tget offset(): number {\r\n\t\treturn this._offset;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the lexer action to execute.\r\n\t *\r\n\t * @returns A {@link LexerAction} object which executes the lexer action.\r\n\t */\r\n\t@NotNull\r\n\tget action(): LexerAction {\r\n\t\treturn this._action;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * @returns This method returns the result of calling {@link #getActionType}\r\n\t * on the {@link LexerAction} returned by {@link #getAction}.\r\n\t */\r\n\t@Override\r\n\tget actionType(): LexerActionType {\r\n\t\treturn this._action.actionType;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t * @returns This method returns `true`.\r\n\t */\r\n\t@Override\r\n\tget isPositionDependent(): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This method calls {@link #execute} on the result of {@link #getAction}\r\n\t * using the provided `lexer`.\r\n\t */\r\n\t@Override\r\n\tpublic execute(lexer: Lexer): void {\r\n\t\t// assume the input stream position was properly set by the calling code\r\n\t\tthis._action.execute(lexer);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\thash = MurmurHash.update(hash, this._offset);\r\n\t\thash = MurmurHash.update(hash, this._action);\r\n\t\treturn MurmurHash.finish(hash, 2);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerIndexedCustomAction)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._offset === obj._offset\r\n\t\t\t&& this._action.equals(obj._action);\r\n\t}\r\n}\r\n"]}