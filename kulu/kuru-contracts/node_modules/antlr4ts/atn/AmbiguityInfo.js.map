{"version": 3, "file": "AmbiguityInfo.js", "sourceRoot": "", "sources": ["../../../src/atn/AmbiguityInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,2DAAwD;AACxD,8CAAwC;AAIxC;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,IAAa,aAAa,GAA1B,MAAa,aAAc,SAAQ,qCAAiB;IAKnD;;;;;;;;;;;;;OAaG;IACH,YACC,QAAgB,EACP,KAAqB,EACrB,SAAiB,EACjB,KAAkB,EAC3B,UAAkB,EAClB,SAAiB;QACjB,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IAEH,IAAI,qBAAqB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;CACD,CAAA;AApCA;IADC,oBAAO;gDACkB;AAiC1B;IADC,oBAAO;0DAGP;AAtCW,aAAa;IAqBvB,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;GAvBG,aAAa,CAuCzB;AAvCY,sCAAa", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:24.8229279-07:00\r\n\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { DecisionEventInfo } from \"./DecisionEventInfo\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { SimulatorState } from \"./SimulatorState\";\r\nimport { TokenStream } from \"../TokenStream\";\r\n\r\n/**\r\n * This class represents profiling event information for an ambiguity.\r\n * Ambiguities are decisions where a particular input resulted in an SLL\r\n * conflict, followed by LL prediction also reaching a conflict state\r\n * (indicating a true ambiguity in the grammar).\r\n *\r\n * This event may be reported during SLL prediction in cases where the\r\n * conflicting SLL configuration set provides sufficient information to\r\n * determine that the SLL conflict is truly an ambiguity. For example, if none\r\n * of the ATN configurations in the conflicting SLL configuration set have\r\n * traversed a global follow transition (i.e.\r\n * {@link ATNConfig#getReachesIntoOuterContext} is `false` for all\r\n * configurations), then the result of SLL prediction for that input is known to\r\n * be equivalent to the result of LL prediction for that input.\r\n *\r\n * In some cases, the minimum represented alternative in the conflicting LL\r\n * configuration set is not equal to the minimum represented alternative in the\r\n * conflicting SLL configuration set. Grammars and inputs which result in this\r\n * scenario are unable to use {@link PredictionMode#SLL}, which in turn means\r\n * they cannot use the two-stage parsing strategy to improve parsing performance\r\n * for that input.\r\n *\r\n * @see ParserATNSimulator#reportAmbiguity\r\n * @see ParserErrorListener#reportAmbiguity\r\n *\r\n * @since 4.3\r\n */\r\nexport class AmbiguityInfo extends DecisionEventInfo {\r\n\t/** The set of alternative numbers for this decision event that lead to a valid parse. */\r\n\t@NotNull\r\n\tprivate ambigAlts: BitSet;\r\n\r\n\t/**\r\n\t * Constructs a new instance of the {@link AmbiguityInfo} class with the\r\n\t * specified detailed ambiguity information.\r\n\t *\r\n\t * @param decision The decision number\r\n\t * @param state The final simulator state identifying the ambiguous\r\n\t * alternatives for the current input\r\n\t * @param ambigAlts The set of alternatives in the decision that lead to a valid parse.\r\n\t *                  The predicted alt is the min(ambigAlts)\r\n\t * @param input The input token stream\r\n\t * @param startIndex The start index for the current prediction\r\n\t * @param stopIndex The index at which the ambiguity was identified during\r\n\t * prediction\r\n\t */\r\n\tconstructor(\r\n\t\tdecision: number,\r\n\t\t@NotNull state: SimulatorState,\r\n\t\t@NotNull ambigAlts: BitSet,\r\n\t\t@NotNull input: TokenStream,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number) {\r\n\t\tsuper(decision, state, input, startIndex, stopIndex, state.useContext);\r\n\t\tthis.ambigAlts = ambigAlts;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the set of alternatives in the decision that lead to a valid parse.\r\n\t *\r\n\t * @since 4.5\r\n\t */\r\n\t@NotNull\r\n\tget ambiguousAlternatives(): BitSet {\r\n\t\treturn this.ambigAlts;\r\n\t}\r\n}\r\n"]}