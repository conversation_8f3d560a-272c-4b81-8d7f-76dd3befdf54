{"version": 3, "file": "LexerActionExecutor.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerActionExecutor.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,6EAA0E;AAI1E,yEAAsE;AACtE,mDAAgD;AAChD,8CAAkD;AAElD;;;;;;;;;;GAUG;AACH,IAAa,mBAAmB,GAAhC,MAAa,mBAAmB;IAU/B;;;OAGG;IACH,YAAqB,YAA2B;QAC/C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,KAAK,IAAI,WAAW,IAAI,YAAY,EAAE;YACrC,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SAC5C;QAED,IAAI,CAAC,cAAc,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;OAcG;IAEI,MAAM,CAAC,MAAM,CAAC,mBAAoD,EAAW,WAAwB;QAC3G,IAAI,CAAC,mBAAmB,EAAE;YACzB,OAAO,IAAI,mBAAmB,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9C;QAED,IAAI,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9D,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/B,OAAO,IAAI,mBAAmB,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACI,oBAAoB,CAAC,MAAc;QACzC,IAAI,mBAA8C,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,mDAAwB,CAAC,EAAE;gBAC9G,IAAI,CAAC,mBAAmB,EAAE;oBACzB,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAClD;gBAED,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,mDAAwB,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aACrF;SACD;QAED,IAAI,CAAC,mBAAmB,EAAE;YACzB,OAAO,IAAI,CAAC;SACZ;QAED,OAAO,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IAEH,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,OAAO,CAAU,KAAY,EAAE,KAAiB,EAAE,UAAkB;QAC1E,IAAI,YAAY,GAAY,KAAK,CAAC;QAClC,IAAI,SAAS,GAAW,KAAK,CAAC,KAAK,CAAC;QACpC,IAAI;YACH,KAAK,IAAI,WAAW,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC3C,IAAI,WAAW,YAAY,mDAAwB,EAAE;oBACpD,IAAI,MAAM,GAAW,WAAW,CAAC,MAAM,CAAC;oBACxC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;oBAChC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;oBACjC,YAAY,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,SAAS,CAAC;iBACnD;qBAAM,IAAI,WAAW,CAAC,mBAAmB,EAAE;oBAC3C,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACtB,YAAY,GAAG,KAAK,CAAC;iBACrB;gBAED,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC3B;SACD;gBAAS;YACT,IAAI,YAAY,EAAE;gBACjB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtB;SACD;IACF,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC5B,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,mBAAmB,CAAC,EAAE;YACjD,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,cAAc,KAAK,GAAG,CAAC,cAAc;eAC7C,iDAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;IACpF,CAAC;CACD,CAAA;AArKA;IADC,oBAAO;0DAC6B;AAsGrC;IADC,oBAAO;uDAGP;AAqBD;IAAgB,WAAA,oBAAO,CAAA;kDAsBtB;AAGD;IADC,qBAAQ;mDAGR;AAGD;IADC,qBAAQ;iDAUR;AA7HD;IADC,oBAAO;IACoE,WAAA,oBAAO,CAAA;uCAQlF;AAjDW,mBAAmB;IAclB,WAAA,oBAAO,CAAA;GAdR,mBAAmB,CAuK/B;AAvKY,kDAAmB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:28.8810453-07:00\r\n\r\nimport { ArrayEqualityComparator } from \"../misc/ArrayEqualityComparator\";\r\nimport { CharStream } from \"../CharStream\";\r\nimport { Lexer } from \"../Lexer\";\r\nimport { LexerAction } from \"./LexerAction\";\r\nimport { LexerIndexedCustomAction } from \"./LexerIndexedCustomAction\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\n\r\n/**\r\n * Represents an executor for a sequence of lexer actions which traversed during\r\n * the matching operation of a lexer rule (token).\r\n *\r\n * The executor tracks position information for position-dependent lexer actions\r\n * efficiently, ensuring that actions appearing only at the end of the rule do\r\n * not cause bloating of the {@link DFA} created for the lexer.\r\n *\r\n * <AUTHOR>\r\n * @since 4.2\r\n */\r\nexport class LexerActionExecutor {\r\n\t@NotNull\r\n\tprivate _lexerActions: LexerAction[];\r\n\r\n\t/**\r\n\t * Caches the result of {@link #hashCode} since the hash code is an element\r\n\t * of the performance-critical {@link LexerATNConfig#hashCode} operation.\r\n\t */\r\n\tprivate cachedHashCode: number;\r\n\r\n\t/**\r\n\t * Constructs an executor for a sequence of {@link LexerAction} actions.\r\n\t * @param lexerActions The lexer actions to execute.\r\n\t */\r\n\tconstructor(@NotNull lexerActions: LexerAction[]) {\r\n\t\tthis._lexerActions = lexerActions;\r\n\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\tfor (let lexerAction of lexerActions) {\r\n\t\t\thash = MurmurHash.update(hash, lexerAction);\r\n\t\t}\r\n\r\n\t\tthis.cachedHashCode = MurmurHash.finish(hash, lexerActions.length);\r\n\t}\r\n\r\n\t/**\r\n\t * Creates a {@link LexerActionExecutor} which executes the actions for\r\n\t * the input `lexerActionExecutor` followed by a specified\r\n\t * `lexerAction`.\r\n\t *\r\n\t * @param lexerActionExecutor The executor for actions already traversed by\r\n\t * the lexer while matching a token within a particular\r\n\t * {@link ATNConfig}. If this is `undefined`, the method behaves as though\r\n\t * it were an empty executor.\r\n\t * @param lexerAction The lexer action to execute after the actions\r\n\t * specified in `lexerActionExecutor`.\r\n\t *\r\n\t * @returns A {@link LexerActionExecutor} for executing the combine actions\r\n\t * of `lexerActionExecutor` and `lexerAction`.\r\n\t */\r\n\t@NotNull\r\n\tpublic static append(lexerActionExecutor: LexerActionExecutor | undefined, @NotNull lexerAction: LexerAction): LexerActionExecutor {\r\n\t\tif (!lexerActionExecutor) {\r\n\t\t\treturn new LexerActionExecutor([lexerAction]);\r\n\t\t}\r\n\r\n\t\tlet lexerActions = lexerActionExecutor._lexerActions.slice(0);\r\n\t\tlexerActions.push(lexerAction);\r\n\t\treturn new LexerActionExecutor(lexerActions);\r\n\t}\r\n\r\n\t/**\r\n\t * Creates a {@link LexerActionExecutor} which encodes the current offset\r\n\t * for position-dependent lexer actions.\r\n\t *\r\n\t * Normally, when the executor encounters lexer actions where\r\n\t * {@link LexerAction#isPositionDependent} returns `true`, it calls\r\n\t * {@link IntStream#seek} on the input {@link CharStream} to set the input\r\n\t * position to the *end* of the current token. This behavior provides\r\n\t * for efficient DFA representation of lexer actions which appear at the end\r\n\t * of a lexer rule, even when the lexer rule matches a variable number of\r\n\t * characters.\r\n\t *\r\n\t * Prior to traversing a match transition in the ATN, the current offset\r\n\t * from the token start index is assigned to all position-dependent lexer\r\n\t * actions which have not already been assigned a fixed offset. By storing\r\n\t * the offsets relative to the token start index, the DFA representation of\r\n\t * lexer actions which appear in the middle of tokens remains efficient due\r\n\t * to sharing among tokens of the same length, regardless of their absolute\r\n\t * position in the input stream.\r\n\t *\r\n\t * If the current executor already has offsets assigned to all\r\n\t * position-dependent lexer actions, the method returns `this`.\r\n\t *\r\n\t * @param offset The current offset to assign to all position-dependent\r\n\t * lexer actions which do not already have offsets assigned.\r\n\t *\r\n\t * @returns A {@link LexerActionExecutor} which stores input stream offsets\r\n\t * for all position-dependent lexer actions.\r\n\t */\r\n\tpublic fixOffsetBeforeMatch(offset: number): LexerActionExecutor {\r\n\t\tlet updatedLexerActions: LexerAction[] | undefined;\r\n\t\tfor (let i = 0; i < this._lexerActions.length; i++) {\r\n\t\t\tif (this._lexerActions[i].isPositionDependent && !(this._lexerActions[i] instanceof LexerIndexedCustomAction)) {\r\n\t\t\t\tif (!updatedLexerActions) {\r\n\t\t\t\t\tupdatedLexerActions = this._lexerActions.slice(0);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tupdatedLexerActions[i] = new LexerIndexedCustomAction(offset, this._lexerActions[i]);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (!updatedLexerActions) {\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\treturn new LexerActionExecutor(updatedLexerActions);\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the lexer actions to be executed by this executor.\r\n\t * @returns The lexer actions to be executed by this executor.\r\n\t */\r\n\t@NotNull\r\n\tget lexerActions(): LexerAction[] {\r\n\t\treturn this._lexerActions;\r\n\t}\r\n\r\n\t/**\r\n\t * Execute the actions encapsulated by this executor within the context of a\r\n\t * particular {@link Lexer}.\r\n\t *\r\n\t * This method calls {@link IntStream#seek} to set the position of the\r\n\t * `input` {@link CharStream} prior to calling\r\n\t * {@link LexerAction#execute} on a position-dependent action. Before the\r\n\t * method returns, the input position will be restored to the same position\r\n\t * it was in when the method was invoked.\r\n\t *\r\n\t * @param lexer The lexer instance.\r\n\t * @param input The input stream which is the source for the current token.\r\n\t * When this method is called, the current {@link IntStream#index} for\r\n\t * `input` should be the start of the following token, i.e. 1\r\n\t * character past the end of the current token.\r\n\t * @param startIndex The token start index. This value may be passed to\r\n\t * {@link IntStream#seek} to set the `input` position to the beginning\r\n\t * of the token.\r\n\t */\r\n\tpublic execute(@NotNull lexer: Lexer, input: CharStream, startIndex: number): void {\r\n\t\tlet requiresSeek: boolean = false;\r\n\t\tlet stopIndex: number = input.index;\r\n\t\ttry {\r\n\t\t\tfor (let lexerAction of this._lexerActions) {\r\n\t\t\t\tif (lexerAction instanceof LexerIndexedCustomAction) {\r\n\t\t\t\t\tlet offset: number = lexerAction.offset;\r\n\t\t\t\t\tinput.seek(startIndex + offset);\r\n\t\t\t\t\tlexerAction = lexerAction.action;\r\n\t\t\t\t\trequiresSeek = (startIndex + offset) !== stopIndex;\r\n\t\t\t\t} else if (lexerAction.isPositionDependent) {\r\n\t\t\t\t\tinput.seek(stopIndex);\r\n\t\t\t\t\trequiresSeek = false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlexerAction.execute(lexer);\r\n\t\t\t}\r\n\t\t} finally {\r\n\t\t\tif (requiresSeek) {\r\n\t\t\t\tinput.seek(stopIndex);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\treturn this.cachedHashCode;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof LexerActionExecutor)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.cachedHashCode === obj.cachedHashCode\r\n\t\t\t&& ArrayEqualityComparator.INSTANCE.equals(this._lexerActions, obj._lexerActions);\r\n\t}\r\n}\r\n"]}