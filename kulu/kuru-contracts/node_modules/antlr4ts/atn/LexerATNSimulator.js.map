{"version": 3, "file": "LexerATNSimulator.js", "sourceRoot": "", "sources": ["../../../src/atn/LexerATNSimulator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,4DAAyD;AAEzD,+BAA4B;AAC5B,2CAAwC;AACxC,iDAA8C;AAC9C,iDAA8C;AAI9C,8CAA2C;AAC3C,+CAA4C;AAC5C,4CAAyC;AACzC,oCAAiC;AACjC,+DAA4D;AAC5D,4EAAyE;AACzE,8CAAkD;AAClD,+DAA4D;AAC5D,2DAAwD;AAExD,mDAAgD;AAEhD,oCAAiC;AAGjC,iCAAiC;AAEjC,iCAAiC;AACjC,IAAa,iBAAiB,GAA9B,MAAa,iBAAkB,SAAQ,2BAAY;IA0BlD,YAAqB,GAAQ,EAAE,KAAa;QAC3C,KAAK,CAAC,GAAG,CAAC,CAAC;QA1BL,wBAAmB,GAAY,IAAI,CAAC;QAI3C;;;;WAIG;QACO,eAAU,GAAW,CAAC,CAAC,CAAC;QAElC,wCAAwC;QAChC,UAAK,GAAW,CAAC,CAAC;QAE1B,8EAA8E;QACtE,wBAAmB,GAAW,CAAC,CAAC;QAE9B,SAAI,GAAW,aAAK,CAAC,YAAY,CAAC;QAE5C,mFAAmF;QAEzE,eAAU,GAA+B,IAAI,iBAAiB,CAAC,QAAQ,EAAE,CAAC;QAMnF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAEM,SAAS,CAAU,SAA4B;QACrD,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;IACxC,CAAC;IAEM,KAAK,CAAU,KAAiB,EAAE,IAAY;QACpD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,GAAW,KAAK,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI;YACH,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,EAAE,GAAyB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3D,IAAI,EAAE,IAAI,IAAI,EAAE;gBACf,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC5B;iBACI;gBACJ,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aAC/B;SACD;gBACO;YACP,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;IACF,CAAC;IAGM,KAAK;QACX,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,aAAK,CAAC,YAAY,CAAC;IAChC,CAAC;IAES,QAAQ,CAAU,KAAiB;QAC5C,IAAI,UAAU,GAAa,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,iBAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,IAAI,WAAW,UAAU,EAAE,CAAC,CAAC;SAC/D;QAED,IAAI,QAAQ,GAAW,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,UAAU,GAAiB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,YAAY,GAAY,UAAU,CAAC,kBAAkB,CAAC;QAC1D,IAAI,YAAY,EAAE;YACjB,UAAU,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACtC;QAED,IAAI,IAAI,GAAa,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE;YAClB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;gBACZ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;aACd;iBAAM;gBACN,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;aACd;SACD;QAED,IAAI,OAAO,GAAW,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEhD,IAAI,iBAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;SACnF;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;IAES,OAAO,CAAU,KAAiB,EAAW,GAAa;QACnE,qEAAqE;QACrE,IAAI,iBAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SAClD;QAED,IAAI,GAAG,CAAC,aAAa,EAAE;YACtB,2BAA2B;YAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,GAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B,WAAW;QACX,IAAI,CAAC,GAAa,GAAG,CAAC,CAAC,8BAA8B;QAErD,OAAO,IAAI,EAAE,EAAE,kBAAkB;YAChC,IAAI,iBAAiB,CAAC,KAAK,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aAC3D;YAED,sEAAsE;YACtE,4DAA4D;YAC5D,+DAA+D;YAC/D,+DAA+D;YAC/D,2DAA2D;YAC3D,iEAAiE;YACjE,oEAAoE;YACpE,+DAA+D;YAC/D,2DAA2D;YAC3D,kEAAkE;YAClE,+DAA+D;YAC/D,8DAA8D;YAC9D,iEAAiE;YACjE,4DAA4D;YAC5D,+DAA+D;YAC/D,yDAAyD;YACzD,kEAAkE;YAClE,IAAI,MAAM,GAAyB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC9C;YAED,IAAI,MAAM,KAAK,2BAAY,CAAC,KAAK,EAAE;gBAClC,MAAM;aACN;YAED,qEAAqE;YACrE,gEAAgE;YAChE,kEAAkE;YAClE,oBAAoB;YACpB,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACpB;YAED,IAAI,MAAM,CAAC,aAAa,EAAE;gBACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACrD,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;oBACxB,MAAM;iBACN;aACD;YAED,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,GAAG,MAAM,CAAC,CAAC,sDAAsD;SAClE;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;OAUG;IACO,sBAAsB,CAAU,CAAW,EAAE,CAAS;QAC/D,IAAI,MAAM,GAAyB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,iBAAiB,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,EAAE;YAC9C,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,CAAC,WAAW;gBACzC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;SACnC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;;;;;;OAWG;IAEO,kBAAkB,CAAU,KAAiB,EAAW,CAAW,EAAE,CAAS;QACvF,IAAI,KAAK,GAAiB,IAAI,yCAAmB,EAAE,CAAC;QAEpD,yCAAyC;QACzC,4DAA4D;QAC5D,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAEvD,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,6BAA6B;YACjD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBAC9B,4DAA4D;gBAC5D,mCAAmC;gBACnC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,2BAAY,CAAC,KAAK,CAAC,CAAC;aAC1C;YAED,yCAAyC;YACzC,OAAO,2BAAY,CAAC,KAAK,CAAC;SAC1B;QAED,2DAA2D;QAC3D,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAES,YAAY,CACrB,UAAsC,EAAE,KAAiB,EACzD,KAAmB,EAAE,CAAS;QAC9B,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;YAChC,IAAI,mBAAmB,GAAoC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YACnG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,mBAAmB,EAAE,IAAI,CAAC,UAAU,EACtD,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACxD,OAAO,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC;SACtC;aACI;YACJ,iDAAiD;YACjD,IAAI,CAAC,KAAK,qBAAS,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC3D,OAAO,aAAK,CAAC,GAAG,CAAC;aACjB;YAED,MAAM,IAAI,qDAAyB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC/E;IACF,CAAC;IAED;;;OAGG;IACO,qBAAqB,CAAU,KAAiB,EAAW,OAAqB,EAAW,KAAmB,EAAE,CAAS;QAClI,0EAA0E;QAC1E,uEAAuE;QACvE,IAAI,OAAO,GAAW,SAAG,CAAC,kBAAkB,CAAC;QAC7C,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,4BAA4B,GAAY,CAAC,CAAC,GAAG,KAAK,OAAO,CAAC;YAC9D,IAAI,4BAA4B,IAAI,CAAC,CAAC,iCAAiC,EAAE;gBACxE,SAAS;aACT;YAED,IAAI,iBAAiB,CAAC,KAAK,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aAClF;YAED,IAAI,CAAC,GAAW,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC;YACrD,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAgB,gCAAgC;gBAC9E,IAAI,KAAK,GAAe,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;gBAC3D,IAAI,MAAM,GAAyB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACrE,IAAI,MAAM,IAAI,IAAI,EAAE;oBACnB,IAAI,mBAAmB,GAAoC,CAAC,CAAC,mBAAmB,CAAC;oBACjF,IAAI,MAAiB,CAAC;oBACtB,IAAI,mBAAmB,IAAI,IAAI,EAAE;wBAChC,mBAAmB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC9F,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;qBACxD;yBAAM;wBACN,MAAM,CAAC,CAAC,CAAC,mBAAmB,IAAI,IAAI,CAAC,CAAC;wBACtC,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;qBACnC;oBAED,IAAI,iBAAiB,GAAY,CAAC,KAAK,qBAAS,CAAC,GAAG,CAAC;oBACrD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,4BAA4B,EAAE,IAAI,EAAE,iBAAiB,CAAC,EAAE;wBAC9F,gEAAgE;wBAChE,6CAA6C;wBAC7C,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;wBAChB,MAAM;qBACN;iBACD;aACD;SACD;IACF,CAAC;IAES,MAAM,CACN,KAAiB,EAAE,mBAAoD,EAChF,UAAkB,EAAE,KAAa,EAAE,IAAY,EAAE,OAAe;QAChE,IAAI,iBAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,UAAU,mBAAmB,EAAE,CAAC,CAAC;SAC7C;QAED,mCAAmC;QACnC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;QAEnC,IAAI,mBAAmB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtD,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;SAC3D;IACF,CAAC;IAES,kBAAkB,CAAC,KAAiB,EAAE,CAAS;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,aAAK,CAAC,cAAc,EAAE,aAAK,CAAC,cAAc,CAAC,EAAE;YACjE,OAAO,KAAK,CAAC,MAAM,CAAC;SACpB;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAGS,iBAAiB,CACjB,KAAiB,EACjB,CAAW;QACpB,IAAI,cAAc,GAAsB,qCAAiB,CAAC,UAAU,CAAC;QACrE,IAAI,OAAO,GAAiB,IAAI,yCAAmB,EAAE,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,MAAM,GAAa,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC9C,IAAI,CAAC,GAAc,qBAAS,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACO,OAAO,CAAU,KAAiB,EAAW,MAAiB,EAAW,OAAqB,EAAE,4BAAqC,EAAE,WAAoB,EAAE,iBAA0B;QAChM,IAAI,iBAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;SAClE;QAED,IAAI,MAAM,CAAC,KAAK,YAAY,6BAAa,EAAE;YAC1C,IAAI,iBAAiB,CAAC,KAAK,EAAE;gBAC5B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;oBACvB,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,MAAM,EAAE,CAAC,CAAC;iBAC9F;qBACI;oBACJ,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;iBAC9C;aACD;YAED,IAAI,OAAO,GAAsB,MAAM,CAAC,OAAO,CAAC;YAChD,IAAI,OAAO,CAAC,OAAO,EAAE;gBACpB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACpB,OAAO,IAAI,CAAC;aACZ;iBACI,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC1B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,qCAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;gBAChF,4BAA4B,GAAG,IAAI,CAAC;aACpC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,iBAAiB,GAAW,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC1D,IAAI,iBAAiB,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;oBACjE,SAAS;iBACT;gBAED,IAAI,UAAU,GAAsB,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;gBAC/E,IAAI,WAAW,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC/D,IAAI,CAAC,GAAc,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpE,4BAA4B,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;aAC7H;YAED,OAAO,4BAA4B,CAAC;SACpC;QAED,eAAe;QACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;YAC5C,IAAI,CAAC,4BAA4B,IAAI,CAAC,MAAM,CAAC,iCAAiC,EAAE;gBAC/E,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aACpB;SACD;QAED,IAAI,CAAC,GAAa,MAAM,CAAC,KAAK,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,GAAe,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,GAA0B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAChH,IAAI,CAAC,IAAI,IAAI,EAAE;gBACd,4BAA4B,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;aAC7H;SACD;QAED,OAAO,4BAA4B,CAAC;IACrC,CAAC;IAED,oDAAoD;IAC1C,gBAAgB,CAChB,KAAiB,EACjB,MAAiB,EACjB,CAAa,EACb,OAAqB,EAC9B,WAAoB,EACpB,iBAA0B;QAC1B,IAAI,CAAwB,CAAC;QAE7B,QAAQ,CAAC,CAAC,iBAAiB,EAAE;YAC7B;gBACC,IAAI,cAAc,GAAmB,CAAmB,CAAC;gBACzD,IAAI,IAAI,CAAC,mBAAmB,IAAI,cAAc,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAC7F,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACrC;qBACI;oBACJ,IAAI,UAAU,GAAsB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBACpG,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;iBACjD;gBAED,MAAM;YAEP;gBACC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YAEvE;gBACC;;;;;;;;;;;;;;;;;kBAiBE;gBACF,IAAI,EAAE,GAAwB,CAAwB,CAAC;gBACvD,IAAI,iBAAiB,CAAC,KAAK,EAAE;oBAC5B,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;iBAC9D;gBACD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAClC,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE;oBAC3E,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACrC;qBACI;oBACJ,CAAC,GAAG,SAAS,CAAC;iBACd;gBAED,MAAM;YAEP;gBACC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAC5B,0DAA0D;oBAC1D,EAAE;oBACF,uDAAuD;oBACvD,yDAAyD;oBACzD,+CAA+C;oBAC/C,yDAAyD;oBACzD,yDAAyD;oBACzD,kDAAkD;oBAClD,sDAAsD;oBACtD,oDAAoD;oBACpD,4DAA4D;oBAC5D,uBAAuB;oBACvB,IAAI,mBAAmB,GAAwB,yCAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAE,CAAsB,CAAC,WAAW,CAAC,CAAC,CAAC;oBAClK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;oBAC1D,MAAM;iBACN;qBACI;oBACJ,qCAAqC;oBACrC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBACrC,MAAM;iBACN;YAEF;gBACC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACrC,MAAM;YAEP,kBAAyB;YACzB,mBAA0B;YAC1B;gBACC,IAAI,iBAAiB,EAAE;oBACtB,IAAI,CAAC,CAAC,OAAO,CAAC,qBAAS,CAAC,GAAG,EAAE,aAAK,CAAC,cAAc,EAAE,aAAK,CAAC,cAAc,CAAC,EAAE;wBACzE,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;wBACtC,MAAM;qBACN;iBACD;gBAED,CAAC,GAAG,SAAS,CAAC;gBACd,MAAM;YAEP;gBACC,CAAC,GAAG,SAAS,CAAC;gBACd,MAAM;SACN;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACO,iBAAiB,CAAU,KAAiB,EAAE,SAAiB,EAAE,SAAiB,EAAE,WAAoB;QACjH,4CAA4C;QAC5C,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACvB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,CAAC,WAAW,EAAE;YACjB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SAC3D;QAED,IAAI,uBAAuB,GAAW,IAAI,CAAC,mBAAmB,CAAC;QAC/D,IAAI,SAAS,GAAW,IAAI,CAAC,KAAK,CAAC;QACnC,IAAI,KAAK,GAAW,KAAK,CAAC,KAAK,CAAC;QAChC,IAAI,MAAM,GAAW,KAAK,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI;YACH,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SAC3D;gBACO;YACP,IAAI,CAAC,mBAAmB,GAAG,uBAAuB,CAAC;YACnD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACtB;IACF,CAAC;IAES,eAAe,CACf,QAAoC,EACpC,KAAiB,EACjB,QAAkB;QAC3B,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC7B,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC5C,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAKS,UAAU,CAAC,CAAW,EAAE,CAAS,EAAE,CAA0B;QACtE,IAAI,CAAC,YAAY,2BAAY,EAAE;YAC9B;;;;;;;;;;cAUE;YACF,IAAI,YAAY,GAAY,CAAC,CAAC,kBAAkB,CAAC;YACjD,IAAI,YAAY,EAAE;gBACjB,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC;aAC7B;YAED,WAAW;YACX,IAAI,EAAE,GAAa,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,YAAY,EAAE;gBACjB,OAAO,EAAE,CAAC;aACV;YAED,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC;SACV;aAAM;YACN,IAAI,iBAAiB,CAAC,KAAK,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1E;YAED,IAAI,CAAC,IAAI,IAAI,EAAE;gBACd,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAClB;SACD;IACF,CAAC;IAED;;;;OAIG;IAEO,WAAW,CAAU,OAAqB;QACnD;;WAEG;QACH,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAEpC,IAAI,QAAQ,GAAa,IAAI,mBAAQ,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,QAAQ,GAAyB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxF,IAAI,QAAQ,IAAI,IAAI,EAAE;YACrB,OAAO,QAAQ,CAAC;SAChB;QAED,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAa,IAAI,mBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAE3D,IAAI,4BAAmD,CAAC;QACxD,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,CAAC,CAAC,KAAK,YAAY,6BAAa,EAAE;gBACrC,4BAA4B,GAAG,CAAC,CAAC;gBACjC,MAAM;aACN;SACD;QAED,IAAI,4BAA4B,IAAI,IAAI,EAAE;YACzC,IAAI,UAAU,GAAW,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,4BAA4B,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAChG,IAAI,mBAAmB,GAAoC,4BAA4B,CAAC,mBAAmB,CAAC;YAC5G,QAAQ,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;SAChF;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAGM,MAAM,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;OACG;IAEI,OAAO,CAAU,KAAiB;QACxC,gDAAgD;QAChD,OAAO,KAAK,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,IAAI,IAAI,CAAC,IAAY;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,kBAAkB;QACrB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACjC,CAAC;IAED,IAAI,kBAAkB,CAAC,kBAA0B;QAChD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAC/C,CAAC;IAEM,OAAO,CAAU,KAAiB;QACxC,IAAI,OAAO,GAAW,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;SAC7B;aAAM;YACN,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC3B;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAGM,YAAY,CAAC,CAAS;QAC5B,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACb,OAAO,KAAK,CAAC;SACb;QACD,yDAAyD;QACzD,OAAO,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAC3C,CAAC;CACD,CAAA;AA1pBA;IADC,oBAAO;qDAC4E;AASpF;IAAkB,WAAA,oBAAO,CAAA;kDAKxB;AAED;IAAc,WAAA,oBAAO,CAAA;8CAiBpB;AAGD;IADC,qBAAQ;8CAOR;AAED;IAAoB,WAAA,oBAAO,CAAA;iDAgC1B;AAED;IAAmB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;gDAkErD;AAaD;IAAkC,WAAA,oBAAO,CAAA;+DAQxC;AAeD;IADC,oBAAO;IACsB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;2DAoBhE;AAyBD;IAAiC,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA,EAAyB,WAAA,oBAAO,CAAA;8DAuCnG;AAED;IACE,WAAA,oBAAO,CAAA;+CAcR;AAWD;IADC,oBAAO;IAEN,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;0DASR;AAYD;IAAmB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA,EAAqB,WAAA,oBAAO,CAAA;gDAyDjF;AAGD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;yDAqGR;AAuBD;IAA6B,WAAA,oBAAO,CAAA;0DAwBnC;AAED;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;wDAKR;AAiDD;IADC,oBAAO;IACe,WAAA,oBAAO,CAAA;oDA8B7B;AAGD;IADC,oBAAO;+CAGP;AAKD;IADC,oBAAO;IACQ,WAAA,oBAAO,CAAA;gDAGtB;AAkBD;IAAgB,WAAA,oBAAO,CAAA;gDAStB;AAGD;IADC,oBAAO;qDAOP;AA/qBW,iBAAiB;IA0BhB,WAAA,oBAAO,CAAA;GA1BR,iBAAiB,CAgrB7B;AAhrBY,8CAAiB;AAkrB9B,WAAiB,iBAAiB;IACpB,uBAAK,GAAY,KAAK,CAAC;IACvB,2BAAS,GAAY,KAAK,CAAC;IAExC;;;;;;;;;;;;;;OAcG;IACH,MAAa,QAAQ;QAArB;YACQ,UAAK,GAAW,CAAC,CAAC,CAAC;YACnB,SAAI,GAAW,CAAC,CAAC;YACjB,YAAO,GAAW,CAAC,CAAC,CAAC;QAS7B,CAAC;QANO,KAAK;YACX,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACd,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC3B,CAAC;KACD;IAZY,0BAAQ,WAYpB,CAAA;AACF,CAAC,EAhCgB,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QAgCjC;AAltBY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:29.1083066-07:00\r\n\r\nimport { AcceptStateInfo } from \"../dfa/AcceptStateInfo\";\r\nimport { ActionTransition } from \"./ActionTransition\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { ATNSimulator } from \"./ATNSimulator\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { CharStream } from \"../CharStream\";\r\nimport { DFA } from \"../dfa/DFA\";\r\nimport { DFAState } from \"../dfa/DFAState\";\r\nimport { Interval } from \"../misc/Interval\";\r\nimport { IntStream } from \"../IntStream\";\r\nimport { Lexer } from \"../Lexer\";\r\nimport { LexerActionExecutor } from \"./LexerActionExecutor\";\r\nimport { LexerNoViableAltException } from \"../LexerNoViableAltException\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { OrderedATNConfigSet } from \"./OrderedATNConfigSet\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { PredicateTransition } from \"./PredicateTransition\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { RuleTransition } from \"./RuleTransition\";\r\nimport { Token } from \"../Token\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\nimport * as assert from \"assert\";\r\n\r\n/** \"dup\" of ParserInterpreter */\r\nexport class LexerATNSimulator extends ATNSimulator {\r\n\tpublic optimize_tail_calls: boolean = true;\r\n\r\n\tprotected recog: Lexer | undefined;\r\n\r\n\t/** The current token's starting index into the character stream.\r\n\t *  Shared across DFA to ATN simulation in case the ATN fails and the\r\n\t *  DFA did not have a previous accept state. In this case, we use the\r\n\t *  ATN-generated exception object.\r\n\t */\r\n\tprotected startIndex: number = -1;\r\n\r\n\t/** line number 1..n within the input */\r\n\tprivate _line: number = 1;\r\n\r\n\t/** The index of the character relative to the beginning of the line 0..n-1 */\r\n\tprivate _charPositionInLine: number = 0;\r\n\r\n\tprotected mode: number = Lexer.DEFAULT_MODE;\r\n\r\n\t/** Used during DFA/ATN exec to record the most recent accept configuration info */\r\n\t@NotNull\r\n\tprotected prevAccept: LexerATNSimulator.SimState = new LexerATNSimulator.SimState();\r\n\r\n\tconstructor(/*@NotNull*/ atn: ATN);\r\n\tconstructor(/*@NotNull*/ atn: ATN, recog: Lexer | undefined);\r\n\tconstructor(@NotNull atn: ATN, recog?: Lexer) {\r\n\t\tsuper(atn);\r\n\t\tthis.recog = recog;\r\n\t}\r\n\r\n\tpublic copyState(@NotNull simulator: LexerATNSimulator): void {\r\n\t\tthis._charPositionInLine = simulator.charPositionInLine;\r\n\t\tthis._line = simulator._line;\r\n\t\tthis.mode = simulator.mode;\r\n\t\tthis.startIndex = simulator.startIndex;\r\n\t}\r\n\r\n\tpublic match(@NotNull input: CharStream, mode: number): number {\r\n\t\tthis.mode = mode;\r\n\t\tlet mark: number = input.mark();\r\n\t\ttry {\r\n\t\t\tthis.startIndex = input.index;\r\n\t\t\tthis.prevAccept.reset();\r\n\t\t\tlet s0: DFAState | undefined = this.atn.modeToDFA[mode].s0;\r\n\t\t\tif (s0 == null) {\r\n\t\t\t\treturn this.matchATN(input);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\treturn this.execATN(input, s0);\r\n\t\t\t}\r\n\t\t}\r\n\t\tfinally {\r\n\t\t\tinput.release(mark);\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reset(): void {\r\n\t\tthis.prevAccept.reset();\r\n\t\tthis.startIndex = -1;\r\n\t\tthis._line = 1;\r\n\t\tthis._charPositionInLine = 0;\r\n\t\tthis.mode = Lexer.DEFAULT_MODE;\r\n\t}\r\n\r\n\tprotected matchATN(@NotNull input: CharStream): number {\r\n\t\tlet startState: ATNState = this.atn.modeToStartState[this.mode];\r\n\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(`matchATN mode ${this.mode} start: ${startState}`);\r\n\t\t}\r\n\r\n\t\tlet old_mode: number = this.mode;\r\n\r\n\t\tlet s0_closure: ATNConfigSet = this.computeStartState(input, startState);\r\n\t\tlet suppressEdge: boolean = s0_closure.hasSemanticContext;\r\n\t\tif (suppressEdge) {\r\n\t\t\ts0_closure.hasSemanticContext = false;\r\n\t\t}\r\n\r\n\t\tlet next: DFAState = this.addDFAState(s0_closure);\r\n\t\tif (!suppressEdge) {\r\n\t\t\tlet dfa = this.atn.modeToDFA[this.mode];\r\n\t\t\tif (!dfa.s0) {\r\n\t\t\t\tdfa.s0 = next;\r\n\t\t\t} else {\r\n\t\t\t\tnext = dfa.s0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet predict: number = this.execATN(input, next);\r\n\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(`DFA after matchATN: ${this.atn.modeToDFA[old_mode].toLexerString()}`);\r\n\t\t}\r\n\r\n\t\treturn predict;\r\n\t}\r\n\r\n\tprotected execATN(@NotNull input: CharStream, @NotNull ds0: DFAState): number {\r\n\t\t// console.log(\"enter exec index \"+input.index+\" from \"+ds0.configs);\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(`start state closure=${ds0.configs}`);\r\n\t\t}\r\n\r\n\t\tif (ds0.isAcceptState) {\r\n\t\t\t// allow zero-length tokens\r\n\t\t\tthis.captureSimState(this.prevAccept, input, ds0);\r\n\t\t}\r\n\r\n\t\tlet t: number = input.LA(1);\r\n\t\t// @NotNull\r\n\t\tlet s: DFAState = ds0; // s is current/from DFA state\r\n\r\n\t\twhile (true) { // while more work\r\n\t\t\tif (LexerATNSimulator.debug) {\r\n\t\t\t\tconsole.log(`execATN loop starting closure: ${s.configs}`);\r\n\t\t\t}\r\n\r\n\t\t\t// As we move src->trg, src->trg, we keep track of the previous trg to\r\n\t\t\t// avoid looking up the DFA state again, which is expensive.\r\n\t\t\t// If the previous target was already part of the DFA, we might\r\n\t\t\t// be able to avoid doing a reach operation upon t. If s!=null,\r\n\t\t\t// it means that semantic predicates didn't prevent us from\r\n\t\t\t// creating a DFA state. Once we know s!=null, we check to see if\r\n\t\t\t// the DFA state has an edge already for t. If so, we can just reuse\r\n\t\t\t// it's configuration set; there's no point in re-computing it.\r\n\t\t\t// This is kind of like doing DFA simulation within the ATN\r\n\t\t\t// simulation because DFA simulation is really just a way to avoid\r\n\t\t\t// computing reach/closure sets. Technically, once we know that\r\n\t\t\t// we have a previously added DFA state, we could jump over to\r\n\t\t\t// the DFA simulator. But, that would mean popping back and forth\r\n\t\t\t// a lot and making things more complicated algorithmically.\r\n\t\t\t// This optimization makes a lot of sense for loops within DFA.\r\n\t\t\t// A character will take us back to an existing DFA state\r\n\t\t\t// that already has lots of edges out of it. e.g., .* in comments.\r\n\t\t\tlet target: DFAState | undefined = this.getExistingTargetState(s, t);\r\n\t\t\tif (target == null) {\r\n\t\t\t\ttarget = this.computeTargetState(input, s, t);\r\n\t\t\t}\r\n\r\n\t\t\tif (target === ATNSimulator.ERROR) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\t// If this is a consumable input element, make sure to consume before\r\n\t\t\t// capturing the accept state so the input index, line, and char\r\n\t\t\t// position accurately reflect the state of the interpreter at the\r\n\t\t\t// end of the token.\r\n\t\t\tif (t !== IntStream.EOF) {\r\n\t\t\t\tthis.consume(input);\r\n\t\t\t}\r\n\r\n\t\t\tif (target.isAcceptState) {\r\n\t\t\t\tthis.captureSimState(this.prevAccept, input, target);\r\n\t\t\t\tif (t === IntStream.EOF) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tt = input.LA(1);\r\n\t\t\ts = target; // flip; current DFA target becomes new src/from state\r\n\t\t}\r\n\r\n\t\treturn this.failOrAccept(this.prevAccept, input, s.configs, t);\r\n\t}\r\n\r\n\t/**\r\n\t * Get an existing target state for an edge in the DFA. If the target state\r\n\t * for the edge has not yet been computed or is otherwise not available,\r\n\t * this method returns `undefined`.\r\n\t *\r\n\t * @param s The current DFA state\r\n\t * @param t The next input symbol\r\n\t * @returns The existing target DFA state for the given input symbol\r\n\t * `t`, or `undefined` if the target state for this edge is not\r\n\t * already cached\r\n\t */\r\n\tprotected getExistingTargetState(@NotNull s: DFAState, t: number): DFAState | undefined {\r\n\t\tlet target: DFAState | undefined = s.getTarget(t);\r\n\t\tif (LexerATNSimulator.debug && target != null) {\r\n\t\t\tconsole.log(\"reuse state \" + s.stateNumber +\r\n\t\t\t\t\" edge to \" + target.stateNumber);\r\n\t\t}\r\n\r\n\t\treturn target;\r\n\t}\r\n\r\n\t/**\r\n\t * Compute a target state for an edge in the DFA, and attempt to add the\r\n\t * computed state and corresponding edge to the DFA.\r\n\t *\r\n\t * @param input The input stream\r\n\t * @param s The current DFA state\r\n\t * @param t The next input symbol\r\n\t *\r\n\t * @returns The computed target DFA state for the given input symbol\r\n\t * `t`. If `t` does not lead to a valid DFA state, this method\r\n\t * returns {@link #ERROR}.\r\n\t */\r\n\t@NotNull\r\n\tprotected computeTargetState(@NotNull input: CharStream, @NotNull s: DFAState, t: number): DFAState {\r\n\t\tlet reach: ATNConfigSet = new OrderedATNConfigSet();\r\n\r\n\t\t// if we don't find an existing DFA state\r\n\t\t// Fill reach starting from closure, following t transitions\r\n\t\tthis.getReachableConfigSet(input, s.configs, reach, t);\r\n\r\n\t\tif (reach.isEmpty) { // we got nowhere on t from s\r\n\t\t\tif (!reach.hasSemanticContext) {\r\n\t\t\t\t// we got nowhere on t, don't throw out this knowledge; it'd\r\n\t\t\t\t// cause a failover from DFA later.\r\n\t\t\t\tthis.addDFAEdge(s, t, ATNSimulator.ERROR);\r\n\t\t\t}\r\n\r\n\t\t\t// stop when we can't match any more char\r\n\t\t\treturn ATNSimulator.ERROR;\r\n\t\t}\r\n\r\n\t\t// Add an edge from s to target DFA found/created for reach\r\n\t\treturn this.addDFAEdge(s, t, reach);\r\n\t}\r\n\r\n\tprotected failOrAccept(\r\n\t\tprevAccept: LexerATNSimulator.SimState, input: CharStream,\r\n\t\treach: ATNConfigSet, t: number): number {\r\n\t\tif (prevAccept.dfaState != null) {\r\n\t\t\tlet lexerActionExecutor: LexerActionExecutor | undefined = prevAccept.dfaState.lexerActionExecutor;\r\n\t\t\tthis.accept(input, lexerActionExecutor, this.startIndex,\r\n\t\t\t\tprevAccept.index, prevAccept.line, prevAccept.charPos);\r\n\t\t\treturn prevAccept.dfaState.prediction;\r\n\t\t}\r\n\t\telse {\r\n\t\t\t// if no accept and EOF is first char, return EOF\r\n\t\t\tif (t === IntStream.EOF && input.index === this.startIndex) {\r\n\t\t\t\treturn Token.EOF;\r\n\t\t\t}\r\n\r\n\t\t\tthrow new LexerNoViableAltException(this.recog, input, this.startIndex, reach);\r\n\t\t}\r\n\t}\r\n\r\n\t/** Given a starting configuration set, figure out all ATN configurations\r\n\t *  we can reach upon input `t`. Parameter `reach` is a return\r\n\t *  parameter.\r\n\t */\r\n\tprotected getReachableConfigSet(@NotNull input: CharStream, @NotNull closure: ATNConfigSet, @NotNull reach: ATNConfigSet, t: number): void {\r\n\t\t// this is used to skip processing for configs which have a lower priority\r\n\t\t// than a config that already reached an accept state for the same rule\r\n\t\tlet skipAlt: number = ATN.INVALID_ALT_NUMBER;\r\n\t\tfor (let c of closure) {\r\n\t\t\tlet currentAltReachedAcceptState: boolean = c.alt === skipAlt;\r\n\t\t\tif (currentAltReachedAcceptState && c.hasPassedThroughNonGreedyDecision) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (LexerATNSimulator.debug) {\r\n\t\t\t\tconsole.log(`testing ${this.getTokenName(t)} at ${c.toString(this.recog, true)}`);\r\n\t\t\t}\r\n\r\n\t\t\tlet n: number = c.state.numberOfOptimizedTransitions;\r\n\t\t\tfor (let ti = 0; ti < n; ti++) {               // for each optimized transition\r\n\t\t\t\tlet trans: Transition = c.state.getOptimizedTransition(ti);\r\n\t\t\t\tlet target: ATNState | undefined = this.getReachableTarget(trans, t);\r\n\t\t\t\tif (target != null) {\r\n\t\t\t\t\tlet lexerActionExecutor: LexerActionExecutor | undefined = c.lexerActionExecutor;\r\n\t\t\t\t\tlet config: ATNConfig;\r\n\t\t\t\t\tif (lexerActionExecutor != null) {\r\n\t\t\t\t\t\tlexerActionExecutor = lexerActionExecutor.fixOffsetBeforeMatch(input.index - this.startIndex);\r\n\t\t\t\t\t\tconfig = c.transform(target, true, lexerActionExecutor);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tassert(c.lexerActionExecutor == null);\r\n\t\t\t\t\t\tconfig = c.transform(target, true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet treatEofAsEpsilon: boolean = t === IntStream.EOF;\r\n\t\t\t\t\tif (this.closure(input, config, reach, currentAltReachedAcceptState, true, treatEofAsEpsilon)) {\r\n\t\t\t\t\t\t// any remaining configs for this alt have a lower priority than\r\n\t\t\t\t\t\t// the one that just reached an accept state.\r\n\t\t\t\t\t\tskipAlt = c.alt;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected accept(\r\n\t\t@NotNull input: CharStream, lexerActionExecutor: LexerActionExecutor | undefined,\r\n\t\tstartIndex: number, index: number, line: number, charPos: number): void {\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(`ACTION ${lexerActionExecutor}`);\r\n\t\t}\r\n\r\n\t\t// seek to after last char in token\r\n\t\tinput.seek(index);\r\n\t\tthis._line = line;\r\n\t\tthis._charPositionInLine = charPos;\r\n\r\n\t\tif (lexerActionExecutor != null && this.recog != null) {\r\n\t\t\tlexerActionExecutor.execute(this.recog, input, startIndex);\r\n\t\t}\r\n\t}\r\n\r\n\tprotected getReachableTarget(trans: Transition, t: number): ATNState | undefined {\r\n\t\tif (trans.matches(t, Lexer.MIN_CHAR_VALUE, Lexer.MAX_CHAR_VALUE)) {\r\n\t\t\treturn trans.target;\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected computeStartState(\r\n\t\t@NotNull input: CharStream,\r\n\t\t@NotNull p: ATNState): ATNConfigSet {\r\n\t\tlet initialContext: PredictionContext = PredictionContext.EMPTY_FULL;\r\n\t\tlet configs: ATNConfigSet = new OrderedATNConfigSet();\r\n\t\tfor (let i = 0; i < p.numberOfTransitions; i++) {\r\n\t\t\tlet target: ATNState = p.transition(i).target;\r\n\t\t\tlet c: ATNConfig = ATNConfig.create(target, i + 1, initialContext);\r\n\t\t\tthis.closure(input, c, configs, false, false, false);\r\n\t\t}\r\n\t\treturn configs;\r\n\t}\r\n\r\n\t/**\r\n\t * Since the alternatives within any lexer decision are ordered by\r\n\t * preference, this method stops pursuing the closure as soon as an accept\r\n\t * state is reached. After the first accept state is reached by depth-first\r\n\t * search from `config`, all other (potentially reachable) states for\r\n\t * this rule would have a lower priority.\r\n\t *\r\n\t * @returns `true` if an accept state is reached, otherwise\r\n\t * `false`.\r\n\t */\r\n\tprotected closure(@NotNull input: CharStream, @NotNull config: ATNConfig, @NotNull configs: ATNConfigSet, currentAltReachedAcceptState: boolean, speculative: boolean, treatEofAsEpsilon: boolean): boolean {\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(\"closure(\" + config.toString(this.recog, true) + \")\");\r\n\t\t}\r\n\r\n\t\tif (config.state instanceof RuleStopState) {\r\n\t\t\tif (LexerATNSimulator.debug) {\r\n\t\t\t\tif (this.recog != null) {\r\n\t\t\t\t\tconsole.log(`closure at ${this.recog.ruleNames[config.state.ruleIndex]} rule stop ${config}`);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tconsole.log(`closure at rule stop ${config}`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet context: PredictionContext = config.context;\r\n\t\t\tif (context.isEmpty) {\r\n\t\t\t\tconfigs.add(config);\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\telse if (context.hasEmpty) {\r\n\t\t\t\tconfigs.add(config.transform(config.state, true, PredictionContext.EMPTY_FULL));\r\n\t\t\t\tcurrentAltReachedAcceptState = true;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let i = 0; i < context.size; i++) {\r\n\t\t\t\tlet returnStateNumber: number = context.getReturnState(i);\r\n\t\t\t\tif (returnStateNumber === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet newContext: PredictionContext = context.getParent(i); // \"pop\" return state\r\n\t\t\t\tlet returnState: ATNState = this.atn.states[returnStateNumber];\r\n\t\t\t\tlet c: ATNConfig = config.transform(returnState, false, newContext);\r\n\t\t\t\tcurrentAltReachedAcceptState = this.closure(input, c, configs, currentAltReachedAcceptState, speculative, treatEofAsEpsilon);\r\n\t\t\t}\r\n\r\n\t\t\treturn currentAltReachedAcceptState;\r\n\t\t}\r\n\r\n\t\t// optimization\r\n\t\tif (!config.state.onlyHasEpsilonTransitions) {\r\n\t\t\tif (!currentAltReachedAcceptState || !config.hasPassedThroughNonGreedyDecision) {\r\n\t\t\t\tconfigs.add(config);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet p: ATNState = config.state;\r\n\t\tfor (let i = 0; i < p.numberOfOptimizedTransitions; i++) {\r\n\t\t\tlet t: Transition = p.getOptimizedTransition(i);\r\n\t\t\tlet c: ATNConfig | undefined = this.getEpsilonTarget(input, config, t, configs, speculative, treatEofAsEpsilon);\r\n\t\t\tif (c != null) {\r\n\t\t\t\tcurrentAltReachedAcceptState = this.closure(input, c, configs, currentAltReachedAcceptState, speculative, treatEofAsEpsilon);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn currentAltReachedAcceptState;\r\n\t}\r\n\r\n\t// side-effect: can alter configs.hasSemanticContext\r\n\tprotected getEpsilonTarget(\r\n\t\t@NotNull input: CharStream,\r\n\t\t@NotNull config: ATNConfig,\r\n\t\t@NotNull t: Transition,\r\n\t\t@NotNull configs: ATNConfigSet,\r\n\t\tspeculative: boolean,\r\n\t\ttreatEofAsEpsilon: boolean): ATNConfig | undefined {\r\n\t\tlet c: ATNConfig | undefined;\r\n\r\n\t\tswitch (t.serializationType) {\r\n\t\tcase TransitionType.RULE:\r\n\t\t\tlet ruleTransition: RuleTransition = t as RuleTransition;\r\n\t\t\tif (this.optimize_tail_calls && ruleTransition.optimizedTailCall && !config.context.hasEmpty) {\r\n\t\t\t\tc = config.transform(t.target, true);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlet newContext: PredictionContext = config.context.getChild(ruleTransition.followState.stateNumber);\r\n\t\t\t\tc = config.transform(t.target, true, newContext);\r\n\t\t\t}\r\n\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.PRECEDENCE:\r\n\t\t\tthrow new Error(\"Precedence predicates are not supported in lexers.\");\r\n\r\n\t\tcase TransitionType.PREDICATE:\r\n\t\t\t/*  Track traversing semantic predicates. If we traverse,\r\n\t\t\t\twe cannot add a DFA state for this \"reach\" computation\r\n\t\t\t\tbecause the DFA would not test the predicate again in the\r\n\t\t\t\tfuture. Rather than creating collections of semantic predicates\r\n\t\t\t\tlike v3 and testing them on prediction, v4 will test them on the\r\n\t\t\t\tfly all the time using the ATN not the DFA. This is slower but\r\n\t\t\t\tsemantically it's not used that often. One of the key elements to\r\n\t\t\t\tthis predicate mechanism is not adding DFA states that see\r\n\t\t\t\tpredicates immediately afterwards in the ATN. For example,\r\n\r\n\t\t\t\ta : ID {p1}? | ID {p2}? ;\r\n\r\n\t\t\t\tshould create the start state for rule 'a' (to save start state\r\n\t\t\t\tcompetition), but should not create target of ID state. The\r\n\t\t\t\tcollection of ATN states the following ID references includes\r\n\t\t\t\tstates reached by traversing predicates. Since this is when we\r\n\t\t\t\ttest them, we cannot cash the DFA state target of ID.\r\n\t\t\t*/\r\n\t\t\tlet pt: PredicateTransition = t as PredicateTransition;\r\n\t\t\tif (LexerATNSimulator.debug) {\r\n\t\t\t\tconsole.log(\"EVAL rule \" + pt.ruleIndex + \":\" + pt.predIndex);\r\n\t\t\t}\r\n\t\t\tconfigs.hasSemanticContext = true;\r\n\t\t\tif (this.evaluatePredicate(input, pt.ruleIndex, pt.predIndex, speculative)) {\r\n\t\t\t\tc = config.transform(t.target, true);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tc = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.ACTION:\r\n\t\t\tif (config.context.hasEmpty) {\r\n\t\t\t\t// execute actions anywhere in the start rule for a token.\r\n\t\t\t\t//\r\n\t\t\t\t// TODO: if the entry rule is invoked recursively, some\r\n\t\t\t\t// actions may be executed during the recursive call. The\r\n\t\t\t\t// problem can appear when hasEmpty is true but\r\n\t\t\t\t// isEmpty is false. In this case, the config needs to be\r\n\t\t\t\t// split into two contexts - one with just the empty path\r\n\t\t\t\t// and another with everything but the empty path.\r\n\t\t\t\t// Unfortunately, the current algorithm does not allow\r\n\t\t\t\t// getEpsilonTarget to return two configurations, so\r\n\t\t\t\t// additional modifications are needed before we can support\r\n\t\t\t\t// the split operation.\r\n\t\t\t\tlet lexerActionExecutor: LexerActionExecutor = LexerActionExecutor.append(config.lexerActionExecutor, this.atn.lexerActions[(t as ActionTransition).actionIndex]);\r\n\t\t\t\tc = config.transform(t.target, true, lexerActionExecutor);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\t// ignore actions in referenced rules\r\n\t\t\t\tc = config.transform(t.target, true);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\tcase TransitionType.EPSILON:\r\n\t\t\tc = config.transform(t.target, true);\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.ATOM:\r\n\t\tcase TransitionType.RANGE:\r\n\t\tcase TransitionType.SET:\r\n\t\t\tif (treatEofAsEpsilon) {\r\n\t\t\t\tif (t.matches(IntStream.EOF, Lexer.MIN_CHAR_VALUE, Lexer.MAX_CHAR_VALUE)) {\r\n\t\t\t\t\tc = config.transform(t.target, false);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tc = undefined;\r\n\t\t\tbreak;\r\n\r\n\t\tdefault:\r\n\t\t\tc = undefined;\r\n\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\treturn c;\r\n\t}\r\n\r\n\t/**\r\n\t * Evaluate a predicate specified in the lexer.\r\n\t *\r\n\t * If `speculative` is `true`, this method was called before\r\n\t * {@link #consume} for the matched character. This method should call\r\n\t * {@link #consume} before evaluating the predicate to ensure position\r\n\t * sensitive values, including {@link Lexer#getText}, {@link Lexer#getLine},\r\n\t * and {@link Lexer#getCharPositionInLine}, properly reflect the current\r\n\t * lexer state. This method should restore `input` and the simulator\r\n\t * to the original state before returning (i.e. undo the actions made by the\r\n\t * call to {@link #consume}.\r\n\t *\r\n\t * @param input The input stream.\r\n\t * @param ruleIndex The rule containing the predicate.\r\n\t * @param predIndex The index of the predicate within the rule.\r\n\t * @param speculative `true` if the current index in `input` is\r\n\t * one character before the predicate's location.\r\n\t *\r\n\t * @returns `true` if the specified predicate evaluates to\r\n\t * `true`.\r\n\t */\r\n\tprotected evaluatePredicate(@NotNull input: CharStream, ruleIndex: number, predIndex: number, speculative: boolean): boolean {\r\n\t\t// assume true if no recognizer was provided\r\n\t\tif (this.recog == null) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tif (!speculative) {\r\n\t\t\treturn this.recog.sempred(undefined, ruleIndex, predIndex);\r\n\t\t}\r\n\r\n\t\tlet savedCharPositionInLine: number = this._charPositionInLine;\r\n\t\tlet savedLine: number = this._line;\r\n\t\tlet index: number = input.index;\r\n\t\tlet marker: number = input.mark();\r\n\t\ttry {\r\n\t\t\tthis.consume(input);\r\n\t\t\treturn this.recog.sempred(undefined, ruleIndex, predIndex);\r\n\t\t}\r\n\t\tfinally {\r\n\t\t\tthis._charPositionInLine = savedCharPositionInLine;\r\n\t\t\tthis._line = savedLine;\r\n\t\t\tinput.seek(index);\r\n\t\t\tinput.release(marker);\r\n\t\t}\r\n\t}\r\n\r\n\tprotected captureSimState(\r\n\t\t@NotNull settings: LexerATNSimulator.SimState,\r\n\t\t@NotNull input: CharStream,\r\n\t\t@NotNull dfaState: DFAState): void {\r\n\t\tsettings.index = input.index;\r\n\t\tsettings.line = this._line;\r\n\t\tsettings.charPos = this._charPositionInLine;\r\n\t\tsettings.dfaState = dfaState;\r\n\t}\r\n\r\n\t// @NotNull\r\n\tprotected addDFAEdge(/*@NotNull*/ p: DFAState, t: number, /*@NotNull*/ q: ATNConfigSet): DFAState;\r\n\tprotected addDFAEdge(/*@NotNull*/ p: DFAState, t: number, /*@NotNull*/ q: DFAState): void;\r\n\tprotected addDFAEdge(p: DFAState, t: number, q: ATNConfigSet | DFAState): DFAState | void {\r\n\t\tif (q instanceof ATNConfigSet) {\r\n\t\t\t/* leading to this call, ATNConfigSet.hasSemanticContext is used as a\r\n\t\t\t* marker indicating dynamic predicate evaluation makes this edge\r\n\t\t\t* dependent on the specific input sequence, so the static edge in the\r\n\t\t\t* DFA should be omitted. The target DFAState is still created since\r\n\t\t\t* execATN has the ability to resynchronize with the DFA state cache\r\n\t\t\t* following the predicate evaluation step.\r\n\t\t\t*\r\n\t\t\t* TJP notes: next time through the DFA, we see a pred again and eval.\r\n\t\t\t* If that gets us to a previously created (but dangling) DFA\r\n\t\t\t* state, we can continue in pure DFA mode from there.\r\n\t\t\t*/\r\n\t\t\tlet suppressEdge: boolean = q.hasSemanticContext;\r\n\t\t\tif (suppressEdge) {\r\n\t\t\t\tq.hasSemanticContext = false;\r\n\t\t\t}\r\n\r\n\t\t\t// @NotNull\r\n\t\t\tlet to: DFAState = this.addDFAState(q);\r\n\r\n\t\t\tif (suppressEdge) {\r\n\t\t\t\treturn to;\r\n\t\t\t}\r\n\r\n\t\t\tthis.addDFAEdge(p, t, to);\r\n\t\t\treturn to;\r\n\t\t} else {\r\n\t\t\tif (LexerATNSimulator.debug) {\r\n\t\t\t\tconsole.log(\"EDGE \" + p + \" -> \" + q + \" upon \" + String.fromCharCode(t));\r\n\t\t\t}\r\n\r\n\t\t\tif (p != null) {\r\n\t\t\t\tp.setTarget(t, q);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/** Add a new DFA state if there isn't one with this set of\r\n\t * \tconfigurations already. This method also detects the first\r\n\t * \tconfiguration containing an ATN rule stop state. Later, when\r\n\t * \ttraversing the DFA, we will know which rule to accept.\r\n\t */\r\n\t@NotNull\r\n\tprotected addDFAState(@NotNull configs: ATNConfigSet): DFAState {\r\n\t\t/* the lexer evaluates predicates on-the-fly; by this point configs\r\n\t\t * should not contain any configurations with unevaluated predicates.\r\n\t\t */\r\n\t\tassert(!configs.hasSemanticContext);\r\n\r\n\t\tlet proposed: DFAState = new DFAState(configs);\r\n\t\tlet existing: DFAState | undefined = this.atn.modeToDFA[this.mode].states.get(proposed);\r\n\t\tif (existing != null) {\r\n\t\t\treturn existing;\r\n\t\t}\r\n\r\n\t\tconfigs.optimizeConfigs(this);\r\n\t\tlet newState: DFAState = new DFAState(configs.clone(true));\r\n\r\n\t\tlet firstConfigWithRuleStopState: ATNConfig | undefined;\r\n\t\tfor (let c of configs) {\r\n\t\t\tif (c.state instanceof RuleStopState) {\r\n\t\t\t\tfirstConfigWithRuleStopState = c;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (firstConfigWithRuleStopState != null) {\r\n\t\t\tlet prediction: number = this.atn.ruleToTokenType[firstConfigWithRuleStopState.state.ruleIndex];\r\n\t\t\tlet lexerActionExecutor: LexerActionExecutor | undefined = firstConfigWithRuleStopState.lexerActionExecutor;\r\n\t\t\tnewState.acceptStateInfo = new AcceptStateInfo(prediction, lexerActionExecutor);\r\n\t\t}\r\n\r\n\t\treturn this.atn.modeToDFA[this.mode].addState(newState);\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getDFA(mode: number): DFA {\r\n\t\treturn this.atn.modeToDFA[mode];\r\n\t}\r\n\r\n\t/** Get the text matched so far for the current token.\r\n\t */\r\n\t@NotNull\r\n\tpublic getText(@NotNull input: CharStream): string {\r\n\t\t// index is first lookahead char, don't include.\r\n\t\treturn input.getText(Interval.of(this.startIndex, input.index - 1));\r\n\t}\r\n\r\n\tget line(): number {\r\n\t\treturn this._line;\r\n\t}\r\n\r\n\tset line(line: number) {\r\n\t\tthis._line = line;\r\n\t}\r\n\r\n\tget charPositionInLine(): number {\r\n\t\treturn this._charPositionInLine;\r\n\t}\r\n\r\n\tset charPositionInLine(charPositionInLine: number) {\r\n\t\tthis._charPositionInLine = charPositionInLine;\r\n\t}\r\n\r\n\tpublic consume(@NotNull input: CharStream): void {\r\n\t\tlet curChar: number = input.LA(1);\r\n\t\tif (curChar === \"\\n\".charCodeAt(0)) {\r\n\t\t\tthis._line++;\r\n\t\t\tthis._charPositionInLine = 0;\r\n\t\t} else {\r\n\t\t\tthis._charPositionInLine++;\r\n\t\t}\r\n\t\tinput.consume();\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getTokenName(t: number): string {\r\n\t\tif (t === -1) {\r\n\t\t\treturn \"EOF\";\r\n\t\t}\r\n\t\t//if ( atn.g!=null ) return atn.g.getTokenDisplayName(t);\r\n\t\treturn \"'\" + String.fromCharCode(t) + \"'\";\r\n\t}\r\n}\r\n\r\nexport namespace LexerATNSimulator {\r\n\texport const debug: boolean = false;\r\n\texport const dfa_debug: boolean = false;\r\n\r\n\t/** When we hit an accept state in either the DFA or the ATN, we\r\n\t *  have to notify the character stream to start buffering characters\r\n\t *  via {@link IntStream#mark} and record the current state. The current sim state\r\n\t *  includes the current index into the input, the current line,\r\n\t *  and current character position in that line. Note that the Lexer is\r\n\t *  tracking the starting line and characterization of the token. These\r\n\t *  variables track the \"state\" of the simulator when it hits an accept state.\r\n\t *\r\n\t *  We track these variables separately for the DFA and ATN simulation\r\n\t *  because the DFA simulation often has to fail over to the ATN\r\n\t *  simulation. If the ATN simulation fails, we need the DFA to fall\r\n\t *  back to its previously accepted state, if any. If the ATN succeeds,\r\n\t *  then the ATN does the accept and the DFA simulator that invoked it\r\n\t *  can simply return the predicted token type.\r\n\t */\r\n\texport class SimState {\r\n\t\tpublic index: number = -1;\r\n\t\tpublic line: number = 0;\r\n\t\tpublic charPos: number = -1;\r\n\t\tpublic dfaState?: DFAState;\r\n\r\n\t\tpublic reset(): void {\r\n\t\t\tthis.index = -1;\r\n\t\t\tthis.line = 0;\r\n\t\t\tthis.charPos = -1;\r\n\t\t\tthis.dfaState = undefined;\r\n\t\t}\r\n\t}\r\n}\r\n"]}