{"version": 3, "file": "PredictionMode.js", "sourceRoot": "", "sources": ["../../../src/atn/PredictionMode.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AAOxD,mDAAgD;AAEhD,8CAAyC;AACzC,mDAAgD;AAGhD;;;;GAIG;AACH,IAAY,cAsDX;AAtDD,WAAY,cAAc;IACzB;;;;;;;;;;;;;;;;;;OAkBG;IACH,iDAAG,CAAA;IACH;;;;;;;;;;;;;;;OAeG;IACH,+CAAE,CAAA;IACF;;;;;;;;;;;;;;OAcG;IACH,2FAAwB,CAAA;AACzB,CAAC,EAtDW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAsDzB;AAED,WAAiB,cAAc;IAC9B,uEAAuE;IACvE,gEAAgE;IAChE,MAAM,gBAAiB,SAAQ,+BAAiC;QAC/D;YACC,KAAK,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;KACD;IAED,MAAM,qCAAqC;QAGlC,qCAAqC;YAC5C,sBAAsB;QACvB,CAAC;QAED;;;WAGG;QAEI,QAAQ,CAAC,CAAY;YAC3B,IAAI,QAAQ,GAAW,uBAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC5D,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;YAClD,QAAQ,GAAG,uBAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC1C,OAAO,QAAQ,CAAC;QACjB,CAAC;QAGM,MAAM,CAAC,CAAY,EAAE,CAAY;YACvC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,OAAO,IAAI,CAAC;aACZ;YACD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW;mBAC9C,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;;IA7BsB,8CAAQ,GAA0C,IAAI,qCAAqC,EAAE,CAAC;IAWrH;QADC,qBAAQ;yEAOR;IAGD;QADC,qBAAQ;uEAUR;IAGF;;;;;;;;;OASG;IACH,SAAgB,wBAAwB,CAAC,OAAqB;QAC7D,KAAK,IAAI,CAAC,IAAI,OAAO,EAAE;YACtB,IAAI,CAAC,CAAC,KAAK,YAAY,6BAAa,EAAE;gBACrC,OAAO,IAAI,CAAC;aACZ;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IARe,uCAAwB,2BAQvC,CAAA;IAED;;;;;;;;;OASG;IACH,SAAgB,0BAA0B,CAAC,YAAY,CAAC,OAAqB;QAC5E,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,YAAY,6BAAa,CAAC,EAAE;gBAC7C,OAAO,KAAK,CAAC;aACb;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IARe,yCAA0B,6BAQzC,CAAA;AACF,CAAC,EAjFgB,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAiF9B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:36.2673893-07:00\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNConfigSet } from \"./ATNConfigSet\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { EqualityComparator } from \"../misc/EqualityComparator\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { Override } from \"../Decorators\";\r\nimport { RuleStopState } from \"./RuleStopState\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\n\r\n/**\r\n * This enumeration defines the prediction modes available in ANTLR 4 along with\r\n * utility methods for analyzing configuration sets for conflicts and/or\r\n * ambiguities.\r\n */\r\nexport enum PredictionMode {\r\n\t/**\r\n\t * The SLL(*) prediction mode. This prediction mode ignores the current\r\n\t * parser context when making predictions. This is the fastest prediction\r\n\t * mode, and provides correct results for many grammars. This prediction\r\n\t * mode is more powerful than the prediction mode provided by ANTLR 3, but\r\n\t * may result in syntax errors for grammar and input combinations which are\r\n\t * not SLL.\r\n\t *\r\n\t * When using this prediction mode, the parser will either return a correct\r\n\t * parse tree (i.e. the same parse tree that would be returned with the\r\n\t * {@link #LL} prediction mode), or it will report a syntax error. If a\r\n\t * syntax error is encountered when using the {@link #SLL} prediction mode,\r\n\t * it may be due to either an actual syntax error in the input or indicate\r\n\t * that the particular combination of grammar and input requires the more\r\n\t * powerful {@link #LL} prediction abilities to complete successfully.\r\n\t *\r\n\t * This prediction mode does not provide any guarantees for prediction\r\n\t * behavior for syntactically-incorrect inputs.\r\n\t */\r\n\tSLL,\r\n\t/**\r\n\t * The LL(*) prediction mode. This prediction mode allows the current parser\r\n\t * context to be used for resolving SLL conflicts that occur during\r\n\t * prediction. This is the fastest prediction mode that guarantees correct\r\n\t * parse results for all combinations of grammars with syntactically correct\r\n\t * inputs.\r\n\t *\r\n\t * When using this prediction mode, the parser will make correct decisions\r\n\t * for all syntactically-correct grammar and input combinations. However, in\r\n\t * cases where the grammar is truly ambiguous this prediction mode might not\r\n\t * report a precise answer for *exactly which* alternatives are\r\n\t * ambiguous.\r\n\t *\r\n\t * This prediction mode does not provide any guarantees for prediction\r\n\t * behavior for syntactically-incorrect inputs.\r\n\t */\r\n\tLL,\r\n\t/**\r\n\t * The LL(*) prediction mode with exact ambiguity detection. In addition to\r\n\t * the correctness guarantees provided by the {@link #LL} prediction mode,\r\n\t * this prediction mode instructs the prediction algorithm to determine the\r\n\t * complete and exact set of ambiguous alternatives for every ambiguous\r\n\t * decision encountered while parsing.\r\n\t *\r\n\t * This prediction mode may be used for diagnosing ambiguities during\r\n\t * grammar development. Due to the performance overhead of calculating sets\r\n\t * of ambiguous alternatives, this prediction mode should be avoided when\r\n\t * the exact results are not necessary.\r\n\t *\r\n\t * This prediction mode does not provide any guarantees for prediction\r\n\t * behavior for syntactically-incorrect inputs.\r\n\t */\r\n\tLL_EXACT_AMBIG_DETECTION,\r\n}\r\n\r\nexport namespace PredictionMode {\r\n\t/** A Map that uses just the state and the stack context as the key. */\r\n\t// NOTE: Base type used to be FlexibleHashMap<ATNConfig, BitSet>\r\n\tclass AltAndContextMap extends Array2DHashMap<ATNConfig, BitSet> {\r\n\t\tconstructor() {\r\n\t\t\tsuper(AltAndContextConfigEqualityComparator.INSTANCE);\r\n\t\t}\r\n\t}\r\n\r\n\tclass AltAndContextConfigEqualityComparator implements EqualityComparator<ATNConfig> {\r\n\t\tpublic static readonly INSTANCE: AltAndContextConfigEqualityComparator = new AltAndContextConfigEqualityComparator();\r\n\r\n\t\tprivate AltAndContextConfigEqualityComparator() {\r\n\t\t\t// intentionally empty\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * The hash code is only a function of the {@link ATNState#stateNumber}\r\n\t\t * and {@link ATNConfig#context}.\r\n\t\t */\r\n\t\t@Override\r\n\t\tpublic hashCode(o: ATNConfig): number {\r\n\t\t\tlet hashCode: number = MurmurHash.initialize(7);\r\n\t\t\thashCode = MurmurHash.update(hashCode, o.state.stateNumber);\r\n\t\t\thashCode = MurmurHash.update(hashCode, o.context);\r\n\t\t\thashCode = MurmurHash.finish(hashCode, 2);\r\n\t\t\treturn hashCode;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic equals(a: ATNConfig, b: ATNConfig): boolean {\r\n\t\t\tif (a === b) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tif (a == null || b == null) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn a.state.stateNumber === b.state.stateNumber\r\n\t\t\t\t&& a.context.equals(b.context);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Checks if any configuration in `configs` is in a\r\n\t * {@link RuleStopState}. Configurations meeting this condition have reached\r\n\t * the end of the decision rule (local context) or end of start rule (full\r\n\t * context).\r\n\t *\r\n\t * @param configs the configuration set to test\r\n\t * @returns `true` if any configuration in `configs` is in a\r\n\t * {@link RuleStopState}, otherwise `false`\r\n\t */\r\n\texport function hasConfigInRuleStopState(configs: ATNConfigSet): boolean {\r\n\t\tfor (let c of configs) {\r\n\t\t\tif (c.state instanceof RuleStopState) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Checks if all configurations in `configs` are in a\r\n\t * {@link RuleStopState}. Configurations meeting this condition have reached\r\n\t * the end of the decision rule (local context) or end of start rule (full\r\n\t * context).\r\n\t *\r\n\t * @param configs the configuration set to test\r\n\t * @returns `true` if all configurations in `configs` are in a\r\n\t * {@link RuleStopState}, otherwise `false`\r\n\t */\r\n\texport function allConfigsInRuleStopStates(/*@NotNull*/ configs: ATNConfigSet): boolean {\r\n\t\tfor (let config of configs) {\r\n\t\t\tif (!(config.state instanceof RuleStopState)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n}\r\n"]}