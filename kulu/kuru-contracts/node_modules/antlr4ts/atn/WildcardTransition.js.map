{"version": 3, "file": "WildcardTransition.js", "sourceRoot": "", "sources": ["../../../src/atn/WildcardTransition.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAkD;AAClD,6CAA0C;AAG1C,IAAa,kBAAkB,GAA/B,MAAa,kBAAmB,SAAQ,uBAAU;IACjD,YAAqB,MAAgB;QACpC,KAAK,CAAC,MAAM,CAAC,CAAC;IACf,CAAC;IAGD,IAAI,iBAAiB;QACpB,wBAA+B;IAChC,CAAC;IAGM,OAAO,CAAC,MAAc,EAAE,cAAsB,EAAE,cAAsB;QAC5E,OAAO,MAAM,IAAI,cAAc,IAAI,MAAM,IAAI,cAAc,CAAC;IAC7D,CAAC;IAIM,QAAQ;QACd,OAAO,GAAG,CAAC;IACZ,CAAC;CACD,CAAA;AAdA;IADC,qBAAQ;2DAGR;AAGD;IADC,qBAAQ;iDAGR;AAID;IAFC,qBAAQ;IACR,oBAAO;kDAGP;AAnBW,kBAAkB;IACjB,WAAA,oBAAO,CAAA;GADR,kBAAkB,CAoB9B;AApBY,gDAAkB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:37.9456839-07:00\r\n\r\nimport { ATNState } from \"./ATNState\";\r\nimport { Override, NotNull } from \"../Decorators\";\r\nimport { Transition } from \"./Transition\";\r\nimport { TransitionType } from \"./TransitionType\";\r\n\r\nexport class WildcardTransition extends Transition {\r\n\tconstructor(@NotNull target: ATNState) {\r\n\t\tsuper(target);\r\n\t}\r\n\r\n\t@Override\r\n\tget serializationType(): TransitionType {\r\n\t\treturn TransitionType.WILDCARD;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic matches(symbol: number, minVocabSymbol: number, maxVocabSymbol: number): boolean {\r\n\t\treturn symbol >= minVocabSymbol && symbol <= maxVocabSymbol;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic toString(): string {\r\n\t\treturn \".\";\r\n\t}\r\n}\r\n"]}