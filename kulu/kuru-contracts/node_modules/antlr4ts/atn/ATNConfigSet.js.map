{"version": 3, "file": "ATNConfigSet.js", "sourceRoot": "", "sources": ["../../../src/atn/ATNConfigSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AACxD,2DAAwD;AACxD,6EAA0E;AAC1E,+BAA4B;AAC5B,2CAAwC;AAGxC,2CAAwC;AAIxC,8CAAkD;AAClD,+EAA4E;AAC5E,2DAAwD;AACxD,qEAAkE;AAClE,uDAAoD;AAEpD,iCAAiC;AACjC,uCAAuC;AAIvC,MAAM,uBAAuB;IACrB,QAAQ,CAAC,GAAY;QAC3B,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,CAAC;IAEM,MAAM,CAAC,CAAU,EAAE,CAAU;QACnC,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC;IAC/C,CAAC;;AAEsB,gCAAQ,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAGjE,SAAS,iBAAiB,CAAC,GAAwC;IAClE,IAAI,GAAG,EAAE;QACR,OAAO,IAAI,+BAAc,CAAqB,GAAG,CAAC,CAAC;KACnD;SAAM;QACN,OAAO,IAAI,+BAAc,CAAqB,uBAAuB,CAAC,QAAQ,CAAC,CAAC;KAChF;AACF,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAa,YAAY;IAkDxB,YAAY,GAAkB,EAAE,QAAkB;QArB1C,eAAU,GAAW,CAAC,CAAC;QAE/B,iEAAiE;QACjE,0EAA0E;QAClE,wBAAmB,GAAY,KAAK,CAAC;QACrC,0BAAqB,GAAY,KAAK,CAAC;QAC/C;;;;;;;;WAQG;QACK,uBAAkB,GAAY,KAAK,CAAC;QAEpC,mBAAc,GAAW,CAAC,CAAC,CAAC;QAKnC,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,aAAa,GAAG,iBAAiB,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YAElB,IAAI,CAAC,UAAU,GAAG,SAAG,CAAC,kBAAkB,CAAC;SACzC;aAAM;YAEN,IAAI,QAAQ,EAAE;gBACb,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;gBAC/B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;aAC1B;iBAAM,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC1D,IAAI,CAAC,QAAQ,GAAI,GAAG,CAAC,QAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACvD;iBAAM;gBACN,IAAI,CAAC,aAAa,GAAG,iBAAiB,EAAE,CAAC;gBACzC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;aACnB;YAED,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC,qBAAqB,CAAC;YACvD,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC,mBAAmB,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC,kBAAkB,CAAC;YAEjD,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;gBAChC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;gBACjC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC;aACvC;YAED,oEAAoE;SACpE;IACF,CAAC;IAED;;;OAGG;IAEI,0BAA0B;QAChC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;SACjD;QAED,IAAI,IAAI,GAAW,IAAI,eAAM,EAAE,CAAC;QAChC,KAAK,IAAI,MAAM,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACrB;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;IACnC,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IAChC,CAAC;IAED,IAAI,oBAAoB,CAAC,kBAA2B;QACnD,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,kBAAkB,EAAE;YACnD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SACzC;QAED,MAAM,CAAC,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC9C,CAAC;IAEM,SAAS;QACf,IAAI,MAAM,GAAG,IAAI,+BAAc,CAAW,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAC7E,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACpB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,eAAe,CAAC,WAAyB;QAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO;SACP;QAED,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAClE;IACF,CAAC;IAEM,KAAK,CAAC,QAAiB;QAC7B,IAAI,IAAI,GAAiB,IAAI,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC5B,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;IAClC,CAAC;IAGM,QAAQ,CAAC,CAAM;QACrB,IAAI,CAAC,CAAC,CAAC,YAAY,qBAAS,CAAC,EAAE;YAC9B,OAAO,KAAK,CAAC;SACb;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxC,IAAI,MAAM,GAAc,CAAC,CAAC;YAC1B,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE;gBAC3E,OAAO,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC5B,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAClB,OAAO,IAAI,CAAC;iBACZ;aACD;SACD;aAAM;YACN,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC3B,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAClB,OAAO,IAAI,CAAC;iBACZ;aACD;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAGM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QACxB,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAGM,OAAO;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAIM,GAAG,CAAC,CAAY,EAAE,YAAqC;QAC7D,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;SAClG;QAED,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC;QAE/D,IAAI,YAAY,IAAI,IAAI,EAAE;YACzB,YAAY,GAAG,+CAAsB,CAAC,QAAQ,CAAC;SAC/C;QAED,IAAI,MAAe,CAAC;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,GAAG,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC;QAChC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE;YAChE,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC;YAC/F,IAAI,CAAC,CAAC,4BAA4B,EAAE;gBACnC,YAAY,CAAC,4BAA4B,GAAG,IAAI,CAAC;aACjD;YAED,IAAI,MAAM,GAAsB,qCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACtG,IAAI,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,YAAY,CAAC,OAAO,KAAK,MAAM,EAAE;gBACpC,OAAO,KAAK,CAAC;aACb;YAED,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC;YAC9B,OAAO,IAAI,CAAC;SACZ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,cAAc,GAAc,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,EAAE;gBAC1C,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC;gBACnG,IAAI,CAAC,CAAC,4BAA4B,EAAE;oBACnC,cAAc,CAAC,4BAA4B,GAAG,IAAI,CAAC;iBACnD;gBAED,IAAI,MAAM,GAAsB,qCAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBACxG,IAAI,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,cAAc,CAAC,OAAO,KAAK,MAAM,EAAE;oBACtC,OAAO,KAAK,CAAC;iBACb;gBAED,cAAc,CAAC,OAAO,GAAG,MAAM,CAAC;gBAEhC,IAAI,MAAM,EAAE;oBACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;oBAC5C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3B;gBAED,OAAO,IAAI,CAAC;aACZ;SACD;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,MAAM,EAAE;YACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SAC/B;aAAM;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,+BAA+B,CAAC,MAAiB;QACxD,0DAA0D;QAC1D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,MAAM,CAAC,uBAAuB,CAAC;QAC1F,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACjE,CAAC;IAEO,8BAA8B,CAAC,MAAiB;QACvD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC;SAC7B;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,UAAU,GAAG,SAAG,CAAC,kBAAkB,CAAC;SACzC;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,IAAI,CAAC,iCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAC5G,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,MAAM,CAAC,uBAAuB,CAAC;QAC1F,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACjE,CAAC;IAES,QAAQ,CAAC,IAAe,EAAE,OAAuC,EAAE,KAAgB;QAC5F,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE;YACvD,OAAO,KAAK,CAAC;SACb;QAED,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE;YAC9B,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAC3D,CAAC;IAES,MAAM,CAAC,CAAY;QAC5B,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;IACnD,CAAC;IAGM,WAAW,CAAC,CAAgB;QAClC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI,CAAC,CAAC,CAAC,YAAY,qBAAS,CAAC,EAAE;gBAC9B,OAAO,KAAK,CAAC;aACb;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACtB,OAAO,KAAK,CAAC;aACb;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAIM,MAAM,CAAC,CAAsB,EAAE,YAAqC;QAC1E,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,KAAK,IAAI,KAAK,IAAI,CAAC,EAAE;YACpB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,EAAE;gBAClC,OAAO,GAAG,IAAI,CAAC;aACf;SACD;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;IAGM,KAAK;QACX,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;SAClG;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,SAAG,CAAC,kBAAkB,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;IAChC,CAAC;IAGM,MAAM,CAAC,GAAQ;QACrB,IAAI,IAAI,KAAK,GAAG,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,CAAC,CAAC,GAAG,YAAY,YAAY,CAAC,EAAE;YACnC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,kBAAkB,KAAK,GAAG,CAAC,kBAAkB;eACrD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,CAAC;eACnD,iDAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;IACxE,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,CAAC,EAAE;YAClD,OAAO,IAAI,CAAC,cAAc,CAAC;SAC3B;QAED,IAAI,QAAQ,GAAW,CAAC,CAAC;QACzB,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,iDAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElF,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;SAC/B;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;IAIM,QAAQ,CAAC,WAAqB;QACpC,IAAI,WAAW,IAAI,IAAI,EAAE;YACxB,WAAW,GAAG,KAAK,CAAC;SACpB;QAED,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1C,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;YAC7B,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,EAAE;gBACtB,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;aACvB;iBACI,IAAI,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE;gBACvD,OAAO,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC;aACnD;iBACI;gBACJ,OAAO,EAAE,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;aAClF;QACF,CAAC,CAAC,CAAC;QAEH,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,CAAC,GAAG,CAAC,EAAE;gBACV,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;aACd;YACD,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;SACjE;QACD,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC7B,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC7D;QACD,IAAI,IAAI,CAAC,UAAU,KAAK,SAAG,CAAC,kBAAkB,EAAE;YAC/C,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC3C;QACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;YAC/B,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAChC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;aACb;SACD;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC/B,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACjC;QACD,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED,IAAI,kBAAkB;QACrB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACjC,CAAC;IAED,IAAI,kBAAkB,CAAC,KAAc;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED,IAAI,YAAY,CAAC,YAAsC;QACtD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACnC,CAAC;IAED,IAAI,eAAe;QAClB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;YAC/B,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;IAC1C,CAAC;IAED,IAAI,eAAe;QAClB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;YAC/B,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACnC,CAAC;IAEM,GAAG,CAAC,KAAa;QACvB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAES,cAAc;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACnD;IACF,CAAC;CACD;AAtYA;IADC,oBAAO;8DAYP;AAgDD;IADC,qBAAQ;wCAGR;AAGD;IADC,qBAAQ;2CAGR;AAGD;IADC,qBAAQ;4CA4BR;AAGD;IADC,qBAAQ;2BACA,MAAM,CAAC,QAAQ,OAEvB;AAGD;IADC,qBAAQ;2CAGR;AA2GD;IADC,qBAAQ;+CAaR;AAkBD;IADC,qBAAQ;yCAeR;AAGD;IADC,qBAAQ;0CAaR;AAGD;IADC,qBAAQ;4CAeR;AA3XF,oCAgeC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:25.5488013-07:00\r\n\r\nimport { Array2DHashMap } from \"../misc/Array2DHashMap\";\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { ArrayEqualityComparator } from \"../misc/ArrayEqualityComparator\";\r\nimport { ATN } from \"./ATN\";\r\nimport { ATNConfig } from \"./ATNConfig\";\r\nimport { ATNSimulator } from \"./ATNSimulator\";\r\nimport { ATNState } from \"./ATNState\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { ConflictInfo } from \"./ConflictInfo\";\r\nimport { EqualityComparator } from \"../misc/EqualityComparator\";\r\nimport { JavaSet } from \"../misc/Stubs\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { PredictionContext } from \"./PredictionContext\";\r\nimport { PredictionContextCache } from \"./PredictionContextCache\";\r\nimport { SemanticContext } from \"./SemanticContext\";\r\n\r\nimport * as assert from \"assert\";\r\nimport * as Utils from \"../misc/Utils\";\r\n\r\ninterface KeyType { state: number; alt: number; }\r\n\r\nclass KeyTypeEqualityComparer implements EqualityComparator<KeyType> {\r\n\tpublic hashCode(key: KeyType) {\r\n\t\treturn key.state ^ key.alt;\r\n\t}\r\n\r\n\tpublic equals(a: KeyType, b: KeyType) {\r\n\t\treturn a.state === b.state && a.alt === b.alt;\r\n\t}\r\n\r\n\tpublic static readonly INSTANCE = new KeyTypeEqualityComparer();\r\n}\r\n\r\nfunction NewKeyedConfigMap(map?: Array2DHashMap<KeyType, ATNConfig>) {\r\n\tif (map) {\r\n\t\treturn new Array2DHashMap<KeyType, ATNConfig>(map);\r\n\t} else {\r\n\t\treturn new Array2DHashMap<KeyType, ATNConfig>(KeyTypeEqualityComparer.INSTANCE);\r\n\t}\r\n}\r\n\r\n/**\r\n * Represents a set of ATN configurations (see `ATNConfig`). As configurations are added to the set, they are merged\r\n * with other `ATNConfig` instances already in the set when possible using the graph-structured stack.\r\n *\r\n * An instance of this class represents the complete set of positions (with context) in an ATN which would be associated\r\n * with a single DFA state. Its internal representation is more complex than traditional state used for NFA to DFA\r\n * conversion due to performance requirements (both improving speed and reducing memory overhead) as well as supporting\r\n * features such as semantic predicates and non-greedy operators in a form to support ANTLR's prediction algorithm.\r\n *\r\n * <AUTHOR> Harwell\r\n */\r\nexport class ATNConfigSet implements JavaSet<ATNConfig> {\r\n\t/**\r\n\t * This maps (state, alt) -> merged {@link ATNConfig}. The key does not account for\r\n\t * the {@link ATNConfig#getSemanticContext} of the value, which is only a problem if a single\r\n\t * `ATNConfigSet` contains two configs with the same state and alternative\r\n\t * but different semantic contexts. When this case arises, the first config\r\n\t * added to this map stays, and the remaining configs are placed in {@link #unmerged}.\r\n\t *\r\n\t * This map is only used for optimizing the process of adding configs to the set,\r\n\t * and is `undefined` for read-only sets stored in the DFA.\r\n\t */\r\n\tprivate mergedConfigs?: Array2DHashMap<KeyType, ATNConfig>;\r\n\r\n\t/**\r\n\t * This is an \"overflow\" list holding configs which cannot be merged with one\r\n\t * of the configs in {@link #mergedConfigs} but have a colliding key. This\r\n\t * occurs when two configs in the set have the same state and alternative but\r\n\t * different semantic contexts.\r\n\t *\r\n\t * This list is only used for optimizing the process of adding configs to the set,\r\n\t * and is `undefined` for read-only sets stored in the DFA.\r\n\t */\r\n\tprivate unmerged?: ATNConfig[];\r\n\r\n\t/**\r\n\t * This is a list of all configs in this set.\r\n\t */\r\n\tprivate configs: ATNConfig[];\r\n\r\n\tprivate _uniqueAlt: number = 0;\r\n\tprivate _conflictInfo?: ConflictInfo;\r\n\t// Used in parser and lexer. In lexer, it indicates we hit a pred\r\n\t// while computing a closure operation.  Don't make a DFA state from this.\r\n\tprivate _hasSemanticContext: boolean = false;\r\n\tprivate _dipsIntoOuterContext: boolean = false;\r\n\t/**\r\n\t * When `true`, this config set represents configurations where the entire\r\n\t * outer context has been consumed by the ATN interpreter. This prevents the\r\n\t * {@link ParserATNSimulator#closure} from pursuing the global FOLLOW when a\r\n\t * rule stop state is reached with an empty prediction context.\r\n\t *\r\n\t * Note: `outermostConfigSet` and {@link #dipsIntoOuterContext} should never\r\n\t * be true at the same time.\r\n\t */\r\n\tprivate outermostConfigSet: boolean = false;\r\n\r\n\tprivate cachedHashCode: number = -1;\r\n\r\n\tconstructor();\r\n\tconstructor(set: ATNConfigSet, readonly: boolean);\r\n\tconstructor(set?: ATNConfigSet, readonly?: boolean) {\r\n\t\tif (!set) {\r\n\t\t\tthis.mergedConfigs = NewKeyedConfigMap();\r\n\t\t\tthis.unmerged = [];\r\n\t\t\tthis.configs = [];\r\n\r\n\t\t\tthis._uniqueAlt = ATN.INVALID_ALT_NUMBER;\r\n\t\t} else {\r\n\r\n\t\t\tif (readonly) {\r\n\t\t\t\tthis.mergedConfigs = undefined;\r\n\t\t\t\tthis.unmerged = undefined;\r\n\t\t\t} else if (!set.isReadOnly) {\r\n\t\t\t\tthis.mergedConfigs = NewKeyedConfigMap(set.mergedConfigs);\r\n\t\t\t\tthis.unmerged = (set.unmerged as ATNConfig[]).slice(0);\r\n\t\t\t} else {\r\n\t\t\t\tthis.mergedConfigs = NewKeyedConfigMap();\r\n\t\t\t\tthis.unmerged = [];\r\n\t\t\t}\r\n\r\n\t\t\tthis.configs = set.configs.slice(0);\r\n\r\n\t\t\tthis._dipsIntoOuterContext = set._dipsIntoOuterContext;\r\n\t\t\tthis._hasSemanticContext = set._hasSemanticContext;\r\n\t\t\tthis.outermostConfigSet = set.outermostConfigSet;\r\n\r\n\t\t\tif (readonly || !set.isReadOnly) {\r\n\t\t\t\tthis._uniqueAlt = set._uniqueAlt;\r\n\t\t\t\tthis._conflictInfo = set._conflictInfo;\r\n\t\t\t}\r\n\r\n\t\t\t// if (!readonly && set.isReadOnly) -> addAll is called from clone()\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get the set of all alternatives represented by configurations in this\r\n\t * set.\r\n\t */\r\n\t@NotNull\r\n\tpublic getRepresentedAlternatives(): BitSet {\r\n\t\tif (this._conflictInfo != null) {\r\n\t\t\treturn this._conflictInfo.conflictedAlts.clone();\r\n\t\t}\r\n\r\n\t\tlet alts: BitSet = new BitSet();\r\n\t\tfor (let config of this) {\r\n\t\t\talts.set(config.alt);\r\n\t\t}\r\n\r\n\t\treturn alts;\r\n\t}\r\n\r\n\tget isReadOnly(): boolean {\r\n\t\treturn this.mergedConfigs == null;\r\n\t}\r\n\r\n\tget isOutermostConfigSet(): boolean {\r\n\t\treturn this.outermostConfigSet;\r\n\t}\r\n\r\n\tset isOutermostConfigSet(outermostConfigSet: boolean) {\r\n\t\tif (this.outermostConfigSet && !outermostConfigSet) {\r\n\t\t\tthrow new Error(\"IllegalStateException\");\r\n\t\t}\r\n\r\n\t\tassert(!outermostConfigSet || !this._dipsIntoOuterContext);\r\n\t\tthis.outermostConfigSet = outermostConfigSet;\r\n\t}\r\n\r\n\tpublic getStates(): Array2DHashSet<ATNState> {\r\n\t\tlet states = new Array2DHashSet<ATNState>(ObjectEqualityComparator.INSTANCE);\r\n\t\tfor (let c of this.configs) {\r\n\t\t\tstates.add(c.state);\r\n\t\t}\r\n\r\n\t\treturn states;\r\n\t}\r\n\r\n\tpublic optimizeConfigs(interpreter: ATNSimulator): void {\r\n\t\tif (this.configs.length === 0) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tfor (let config of this.configs) {\r\n\t\t\tconfig.context = interpreter.atn.getCachedContext(config.context);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic clone(readonly: boolean): ATNConfigSet {\r\n\t\tlet copy: ATNConfigSet = new ATNConfigSet(this, readonly);\r\n\t\tif (!readonly && this.isReadOnly) {\r\n\t\t\tcopy.addAll(this.configs);\r\n\t\t}\r\n\r\n\t\treturn copy;\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn this.configs.length;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEmpty(): boolean {\r\n\t\treturn this.configs.length === 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic contains(o: any): boolean {\r\n\t\tif (!(o instanceof ATNConfig)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (this.mergedConfigs && this.unmerged) {\r\n\t\t\tlet config: ATNConfig = o;\r\n\t\t\tlet configKey = this.getKey(config);\r\n\t\t\tlet mergedConfig = this.mergedConfigs.get(configKey);\r\n\t\t\tif (mergedConfig != null && this.canMerge(config, configKey, mergedConfig)) {\r\n\t\t\t\treturn mergedConfig.contains(config);\r\n\t\t\t}\r\n\r\n\t\t\tfor (let c of this.unmerged) {\r\n\t\t\t\tif (c.contains(o)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tfor (let c of this.configs) {\r\n\t\t\t\tif (c.contains(o)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic *[Symbol.iterator](): IterableIterator<ATNConfig> {\r\n\t\tyield* this.configs;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toArray(): ATNConfig[] {\r\n\t\treturn this.configs;\r\n\t}\r\n\r\n\tpublic add(e: ATNConfig): boolean;\r\n\tpublic add(e: ATNConfig, contextCache: PredictionContextCache | undefined): boolean;\r\n\tpublic add(e: ATNConfig, contextCache?: PredictionContextCache): boolean {\r\n\t\tthis.ensureWritable();\r\n\t\tif (!this.mergedConfigs || !this.unmerged) {\r\n\t\t\tthrow new Error(\"Covered by ensureWritable but duplicated here for strict null check limitation\");\r\n\t\t}\r\n\r\n\t\tassert(!this.outermostConfigSet || !e.reachesIntoOuterContext);\r\n\r\n\t\tif (contextCache == null) {\r\n\t\t\tcontextCache = PredictionContextCache.UNCACHED;\r\n\t\t}\r\n\r\n\t\tlet addKey: boolean;\r\n\t\tlet key = this.getKey(e);\r\n\t\tlet mergedConfig = this.mergedConfigs.get(key);\r\n\t\taddKey = (mergedConfig == null);\r\n\t\tif (mergedConfig != null && this.canMerge(e, key, mergedConfig)) {\r\n\t\t\tmergedConfig.outerContextDepth = Math.max(mergedConfig.outerContextDepth, e.outerContextDepth);\r\n\t\t\tif (e.isPrecedenceFilterSuppressed) {\r\n\t\t\t\tmergedConfig.isPrecedenceFilterSuppressed = true;\r\n\t\t\t}\r\n\r\n\t\t\tlet joined: PredictionContext = PredictionContext.join(mergedConfig.context, e.context, contextCache);\r\n\t\t\tthis.updatePropertiesForMergedConfig(e);\r\n\t\t\tif (mergedConfig.context === joined) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tmergedConfig.context = joined;\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < this.unmerged.length; i++) {\r\n\t\t\tlet unmergedConfig: ATNConfig = this.unmerged[i];\r\n\t\t\tif (this.canMerge(e, key, unmergedConfig)) {\r\n\t\t\t\tunmergedConfig.outerContextDepth = Math.max(unmergedConfig.outerContextDepth, e.outerContextDepth);\r\n\t\t\t\tif (e.isPrecedenceFilterSuppressed) {\r\n\t\t\t\t\tunmergedConfig.isPrecedenceFilterSuppressed = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet joined: PredictionContext = PredictionContext.join(unmergedConfig.context, e.context, contextCache);\r\n\t\t\t\tthis.updatePropertiesForMergedConfig(e);\r\n\t\t\t\tif (unmergedConfig.context === joined) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tunmergedConfig.context = joined;\r\n\r\n\t\t\t\tif (addKey) {\r\n\t\t\t\t\tthis.mergedConfigs.put(key, unmergedConfig);\r\n\t\t\t\t\tthis.unmerged.splice(i, 1);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.configs.push(e);\r\n\t\tif (addKey) {\r\n\t\t\tthis.mergedConfigs.put(key, e);\r\n\t\t} else {\r\n\t\t\tthis.unmerged.push(e);\r\n\t\t}\r\n\r\n\t\tthis.updatePropertiesForAddedConfig(e);\r\n\t\treturn true;\r\n\t}\r\n\r\n\tprivate updatePropertiesForMergedConfig(config: ATNConfig): void {\r\n\t\t// merged configs can't change the alt or semantic context\r\n\t\tthis._dipsIntoOuterContext = this._dipsIntoOuterContext || config.reachesIntoOuterContext;\r\n\t\tassert(!this.outermostConfigSet || !this._dipsIntoOuterContext);\r\n\t}\r\n\r\n\tprivate updatePropertiesForAddedConfig(config: ATNConfig): void {\r\n\t\tif (this.configs.length === 1) {\r\n\t\t\tthis._uniqueAlt = config.alt;\r\n\t\t} else if (this._uniqueAlt !== config.alt) {\r\n\t\t\tthis._uniqueAlt = ATN.INVALID_ALT_NUMBER;\r\n\t\t}\r\n\r\n\t\tthis._hasSemanticContext = this._hasSemanticContext || !SemanticContext.NONE.equals(config.semanticContext);\r\n\t\tthis._dipsIntoOuterContext = this._dipsIntoOuterContext || config.reachesIntoOuterContext;\r\n\t\tassert(!this.outermostConfigSet || !this._dipsIntoOuterContext);\r\n\t}\r\n\r\n\tprotected canMerge(left: ATNConfig, leftKey: { state: number, alt: number }, right: ATNConfig): boolean {\r\n\t\tif (left.state.stateNumber !== right.state.stateNumber) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (leftKey.alt !== right.alt) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn left.semanticContext.equals(right.semanticContext);\r\n\t}\r\n\r\n\tprotected getKey(e: ATNConfig): { state: number, alt: number } {\r\n\t\treturn { state: e.state.stateNumber, alt: e.alt };\r\n\t}\r\n\r\n\t@Override\r\n\tpublic containsAll(c: Iterable<any>): boolean {\r\n\t\tfor (let o of c) {\r\n\t\t\tif (!(o instanceof ATNConfig)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tif (!this.contains(o)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\tpublic addAll(c: Iterable<ATNConfig>): boolean;\r\n\tpublic addAll(c: Iterable<ATNConfig>, contextCache: PredictionContextCache): boolean;\r\n\tpublic addAll(c: Iterable<ATNConfig>, contextCache?: PredictionContextCache): boolean {\r\n\t\tthis.ensureWritable();\r\n\r\n\t\tlet changed: boolean = false;\r\n\t\tfor (let group of c) {\r\n\t\t\tif (this.add(group, contextCache)) {\r\n\t\t\t\tchanged = true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn changed;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic clear(): void {\r\n\t\tthis.ensureWritable();\r\n\t\tif (!this.mergedConfigs || !this.unmerged) {\r\n\t\t\tthrow new Error(\"Covered by ensureWritable but duplicated here for strict null check limitation\");\r\n\t\t}\r\n\r\n\t\tthis.mergedConfigs.clear();\r\n\t\tthis.unmerged.length = 0;\r\n\t\tthis.configs.length = 0;\r\n\r\n\t\tthis._dipsIntoOuterContext = false;\r\n\t\tthis._hasSemanticContext = false;\r\n\t\tthis._uniqueAlt = ATN.INVALID_ALT_NUMBER;\r\n\t\tthis._conflictInfo = undefined;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (this === obj) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tif (!(obj instanceof ATNConfigSet)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.outermostConfigSet === obj.outermostConfigSet\r\n\t\t\t&& Utils.equals(this._conflictInfo, obj._conflictInfo)\r\n\t\t\t&& ArrayEqualityComparator.INSTANCE.equals(this.configs, obj.configs);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tif (this.isReadOnly && this.cachedHashCode !== -1) {\r\n\t\t\treturn this.cachedHashCode;\r\n\t\t}\r\n\r\n\t\tlet hashCode: number = 1;\r\n\t\thashCode = 5 * hashCode ^ (this.outermostConfigSet ? 1 : 0);\r\n\t\thashCode = 5 * hashCode ^ ArrayEqualityComparator.INSTANCE.hashCode(this.configs);\r\n\r\n\t\tif (this.isReadOnly) {\r\n\t\t\tthis.cachedHashCode = hashCode;\r\n\t\t}\r\n\r\n\t\treturn hashCode;\r\n\t}\r\n\r\n\tpublic toString(): string;\r\n\tpublic toString(showContext: boolean): string;\r\n\tpublic toString(showContext?: boolean): string {\r\n\t\tif (showContext == null) {\r\n\t\t\tshowContext = false;\r\n\t\t}\r\n\r\n\t\tlet buf = \"\";\r\n\t\tlet sortedConfigs = this.configs.slice(0);\r\n\t\tsortedConfigs.sort((o1, o2) => {\r\n\t\t\tif (o1.alt !== o2.alt) {\r\n\t\t\t\treturn o1.alt - o2.alt;\r\n\t\t\t}\r\n\t\t\telse if (o1.state.stateNumber !== o2.state.stateNumber) {\r\n\t\t\t\treturn o1.state.stateNumber - o2.state.stateNumber;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\treturn o1.semanticContext.toString().localeCompare(o2.semanticContext.toString());\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tbuf += (\"[\");\r\n\t\tfor (let i = 0; i < sortedConfigs.length; i++) {\r\n\t\t\tif (i > 0) {\r\n\t\t\t\tbuf += (\", \");\r\n\t\t\t}\r\n\t\t\tbuf += (sortedConfigs[i].toString(undefined, true, showContext));\r\n\t\t}\r\n\t\tbuf += (\"]\");\r\n\r\n\t\tif (this._hasSemanticContext) {\r\n\t\t\tbuf += (\",hasSemanticContext=\") + (this._hasSemanticContext);\r\n\t\t}\r\n\t\tif (this._uniqueAlt !== ATN.INVALID_ALT_NUMBER) {\r\n\t\t\tbuf += (\",uniqueAlt=\") + (this._uniqueAlt);\r\n\t\t}\r\n\t\tif (this._conflictInfo != null) {\r\n\t\t\tbuf += (\",conflictingAlts=\") + (this._conflictInfo.conflictedAlts);\r\n\t\t\tif (!this._conflictInfo.isExact) {\r\n\t\t\t\tbuf += (\"*\");\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (this._dipsIntoOuterContext) {\r\n\t\t\tbuf += (\",dipsIntoOuterContext\");\r\n\t\t}\r\n\t\treturn buf.toString();\r\n\t}\r\n\r\n\tget uniqueAlt(): number {\r\n\t\treturn this._uniqueAlt;\r\n\t}\r\n\r\n\tget hasSemanticContext(): boolean {\r\n\t\treturn this._hasSemanticContext;\r\n\t}\r\n\r\n\tset hasSemanticContext(value: boolean) {\r\n\t\tthis.ensureWritable();\r\n\t\tthis._hasSemanticContext = value;\r\n\t}\r\n\r\n\tget conflictInfo(): ConflictInfo | undefined {\r\n\t\treturn this._conflictInfo;\r\n\t}\r\n\r\n\tset conflictInfo(conflictInfo: ConflictInfo | undefined) {\r\n\t\tthis.ensureWritable();\r\n\t\tthis._conflictInfo = conflictInfo;\r\n\t}\r\n\r\n\tget conflictingAlts(): BitSet | undefined {\r\n\t\tif (this._conflictInfo == null) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn this._conflictInfo.conflictedAlts;\r\n\t}\r\n\r\n\tget isExactConflict(): boolean {\r\n\t\tif (this._conflictInfo == null) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this._conflictInfo.isExact;\r\n\t}\r\n\r\n\tget dipsIntoOuterContext(): boolean {\r\n\t\treturn this._dipsIntoOuterContext;\r\n\t}\r\n\r\n\tpublic get(index: number): ATNConfig {\r\n\t\treturn this.configs[index];\r\n\t}\r\n\r\n\tprotected ensureWritable(): void {\r\n\t\tif (this.isReadOnly) {\r\n\t\t\tthrow new Error(\"This ATNConfigSet is read only.\");\r\n\t\t}\r\n\t}\r\n}\r\n"]}