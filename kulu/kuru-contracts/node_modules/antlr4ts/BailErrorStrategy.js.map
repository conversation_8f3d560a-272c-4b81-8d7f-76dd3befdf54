{"version": 3, "file": "BailErrorStrategy.js", "sourceRoot": "", "sources": ["../../src/BailErrorStrategy.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,iEAA8D;AAE9D,qEAAkE;AAClE,6CAAwC;AACxC,kFAA+E;AAK/E;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAa,iBAAkB,SAAQ,2CAAoB;IAC1D;;;;OAIG;IAEI,OAAO,CAAC,UAAkB,EAAE,CAAuB;QACzD,KAAK,IAAI,OAAO,GAAkC,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE;YACxG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;QAED,MAAM,IAAI,uDAA0B,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IAEI,aAAa,CAAC,UAAkB;QACtC,IAAI,CAAC,GAAG,IAAI,+CAAsB,CAAC,UAAU,CAAC,CAAC;QAC/C,KAAK,IAAI,OAAO,GAAkC,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE;YACxG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;QAED,MAAM,IAAI,uDAA0B,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,uEAAuE;IAEhE,IAAI,CAAC,UAAkB;QAC7B,sBAAsB;IACvB,CAAC;CACD;AA1BA;IADC,qBAAQ;gDAOR;AAMD;IADC,qBAAQ;sDAQR;AAID;IADC,qBAAQ;6CAGR;AAhCF,8CAiCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:49.2855056-07:00\r\n\r\nimport { DefaultErrorStrategy } from \"./DefaultErrorStrategy\";\r\nimport { Parser } from \"./Parser\";\r\nimport { InputMismatchException } from \"./InputMismatchException\";\r\nimport { Override } from \"./Decorators\";\r\nimport { ParseCancellationException } from \"./misc/ParseCancellationException\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Token } from \"./Token\";\r\n\r\n/**\r\n * This implementation of {@link ANTLRErrorStrategy} responds to syntax errors\r\n * by immediately canceling the parse operation with a\r\n * {@link ParseCancellationException}. The implementation ensures that the\r\n * {@link ParserRuleContext#exception} field is set for all parse tree nodes\r\n * that were not completed prior to encountering the error.\r\n *\r\n * This error strategy is useful in the following scenarios.\r\n *\r\n * * **Two-stage parsing:** This error strategy allows the first\r\n *   stage of two-stage parsing to immediately terminate if an error is\r\n *   encountered, and immediately fall back to the second stage. In addition to\r\n *   avoiding wasted work by attempting to recover from errors here, the empty\r\n *   implementation of {@link BailErrorStrategy#sync} improves the performance of\r\n *   the first stage.\r\n * * **Silent validation:** When syntax errors are not being\r\n *   reported or logged, and the parse result is simply ignored if errors occur,\r\n *   the {@link BailErrorStrategy} avoids wasting work on recovering from errors\r\n *   when the result will be ignored either way.\r\n *\r\n * ```\r\n * myparser.errorHandler = new BailErrorStrategy();\r\n * ```\r\n *\r\n * @see Parser.errorHandler\r\n */\r\nexport class BailErrorStrategy extends DefaultErrorStrategy {\r\n\t/** Instead of recovering from exception `e`, re-throw it wrapped\r\n\t *  in a {@link ParseCancellationException} so it is not caught by the\r\n\t *  rule function catches.  Use {@link Exception#getCause()} to get the\r\n\t *  original {@link RecognitionException}.\r\n\t */\r\n\t@Override\r\n\tpublic recover(recognizer: Parser, e: RecognitionException): void {\r\n\t\tfor (let context: ParserRuleContext | undefined = recognizer.context; context; context = context.parent) {\r\n\t\t\tcontext.exception = e;\r\n\t\t}\r\n\r\n\t\tthrow new ParseCancellationException(e);\r\n\t}\r\n\r\n\t/** Make sure we don't attempt to recover inline; if the parser\r\n\t *  successfully recovers, it won't throw an exception.\r\n\t */\r\n\t@Override\r\n\tpublic recoverInline(recognizer: Parser): Token {\r\n\t\tlet e = new InputMismatchException(recognizer);\r\n\t\tfor (let context: ParserRuleContext | undefined = recognizer.context; context; context = context.parent) {\r\n\t\t\tcontext.exception = e;\r\n\t\t}\r\n\r\n\t\tthrow new ParseCancellationException(e);\r\n\t}\r\n\r\n\t/** Make sure we don't attempt to recover from problems in subrules. */\r\n\t@Override\r\n\tpublic sync(recognizer: Parser): void {\r\n\t\t// intentionally empty\r\n\t}\r\n}\r\n"]}