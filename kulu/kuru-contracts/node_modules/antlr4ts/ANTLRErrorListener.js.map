{"version": 3, "file": "ANTLRErrorListener.js", "sourceRoot": "", "sources": ["../../src/ANTLRErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:48.7499997-07:00\r\n\r\n/** How to emit recognition errors. */\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\n\r\nexport interface ANTLRErrorListener<TSymbol> {\r\n\t/**\r\n\t * Upon syntax error, notify any interested parties. This is not how to\r\n\t * recover from errors or compute error messages. {@link ANTLRErrorStrategy}\r\n\t * specifies how to recover from syntax errors and how to compute error\r\n\t * messages. This listener's job is simply to emit a computed message,\r\n\t * though it has enough information to create its own message in many cases.\r\n\t *\r\n\t * The {@link RecognitionException} is non-`undefined` for all syntax errors except\r\n\t * when we discover mismatched token errors that we can recover from\r\n\t * in-line, without returning from the surrounding rule (via the single\r\n\t * token insertion and deletion mechanism).\r\n\t *\r\n\t * @param recognizer\r\n\t *        What parser got the error. From this\r\n\t * \t\t  object, you can access the context as well\r\n\t * \t\t  as the input stream.\r\n\t * @param offendingSymbol\r\n\t *        The offending token in the input token\r\n\t * \t\t  stream, unless recognizer is a lexer (then it's `undefined`). If\r\n\t * \t\t  no viable alternative error, `e` has token at which we\r\n\t * \t\t  started production for the decision.\r\n\t * @param line\r\n\t * \t\t  The line number in the input where the error occurred.\r\n\t * @param charPositionInLine\r\n\t * \t\t  The character position within that line where the error occurred.\r\n\t * @param msg\r\n\t * \t\t  The message to emit.\r\n\t * @param e\r\n\t *        The exception generated by the parser that led to\r\n\t *        the reporting of an error. It is `undefined` in the case where\r\n\t *        the parser was able to recover in line without exiting the\r\n\t *        surrounding rule.\r\n\t */\r\n\tsyntaxError?: <T extends TSymbol>(\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Recognizer<T, any>,\r\n\t\toffendingSymbol: T | undefined,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number,\r\n\t\t/*@NotNull*/\r\n\t\tmsg: string,\r\n\t\te: RecognitionException | undefined) => void;\r\n}\r\n"]}