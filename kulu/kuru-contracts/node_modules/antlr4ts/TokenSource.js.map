{"version": 3, "file": "TokenSource.js", "sourceRoot": "", "sources": ["../../src/TokenSource.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.9604200-07:00\r\n\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\n\r\n/**\r\n * A source of tokens must provide a sequence of tokens via {@link #nextToken()}\r\n * and also must reveal it's source of characters; {@link CommonToken}'s text is\r\n * computed from a {@link CharStream}; it only store indices into the char\r\n * stream.\r\n *\r\n * Errors from the lexer are never passed to the parser. Either you want to keep\r\n * going or you do not upon token recognition error. If you do not want to\r\n * continue lexing then you do not want to continue parsing. Just throw an\r\n * exception not under {@link RecognitionException} and Java will naturally toss\r\n * you all the way out of the recognizers. If you want to continue lexing then\r\n * you should not throw an exception to the parser--it has already requested a\r\n * token. Keep lexing until you get a valid one. Just report errors and keep\r\n * going, looking for a valid token.\r\n */\r\nexport interface TokenSource {\r\n\t/**\r\n\t * Return a {@link Token} object from your input stream (usually a\r\n\t * {@link CharStream}). Do not fail/return upon lexing error; keep chewing\r\n\t * on the characters until you get a good one; errors are not passed through\r\n\t * to the parser.\r\n\t */\r\n\t//@NotNull\r\n\tnextToken(): Token;\r\n\r\n\t/**\r\n\t * Get the line number for the current position in the input stream. The\r\n\t * first line in the input is line 1.\r\n\t *\r\n\t * @returns The line number for the current position in the input stream, or\r\n\t * 0 if the current token source does not track line numbers.\r\n\t */\r\n\treadonly line: number;\r\n\r\n\t/**\r\n\t * Get the index into the current line for the current position in the input\r\n\t * stream. The first character on a line has position 0.\r\n\t *\r\n\t * @returns The line number for the current position in the input stream, or\r\n\t * -1 if the current token source does not track character positions.\r\n\t */\r\n\treadonly charPositionInLine: number;\r\n\r\n\t/**\r\n\t * Get the {@link CharStream} from which this token source is currently\r\n\t * providing tokens.\r\n\t *\r\n\t * @returns The {@link CharStream} associated with the current position in\r\n\t * the input, or `undefined` if no input stream is available for the token\r\n\t * source.\r\n\t */\r\n\treadonly inputStream: CharStream | undefined;\r\n\r\n\t/**\r\n\t * Gets the name of the underlying input source. This method returns a\r\n\t * non-undefined, non-empty string. If such a name is not known, this method\r\n\t * returns {@link IntStream#UNKNOWN_SOURCE_NAME}.\r\n\t */\r\n\t//@NotNull\r\n\treadonly sourceName: string;\r\n\r\n\t/**\r\n\t * Gets or sets the `TokenFactory` this token source is currently using for\r\n\t * creating `Token` objects from the input.\r\n\t */\r\n\t//@NotNull\r\n\ttokenFactory: TokenFactory;\r\n}\r\n"]}