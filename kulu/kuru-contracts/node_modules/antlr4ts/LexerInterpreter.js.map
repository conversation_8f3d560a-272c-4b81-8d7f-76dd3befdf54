{"version": 3, "file": "LexerInterpreter.js", "sourceRoot": "", "sources": ["../../src/LexerInterpreter.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAOH,mCAAgC;AAChC,+DAA4D;AAC5D,6CAAuC;AACvC,6CAAwC;AAGxC,IAAa,gBAAgB,GAA7B,MAAa,gBAAiB,SAAQ,aAAK;IAU1C,YAAY,eAAuB,EAAW,UAAsB,EAAE,SAAmB,EAAE,YAAsB,EAAE,SAAmB,EAAE,GAAQ,EAAE,KAAiB;QAClK,KAAK,CAAC,KAAK,CAAC,CAAC;QAEb,IAAI,GAAG,CAAC,WAAW,kBAAkB,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC1E;QAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,qCAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAGD,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAGD,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAGD,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;CACD,CAAA;AAhDA;IADC,oBAAO;qDACwB;AAoBhC;IADC,qBAAQ;2CAGR;AAGD;IADC,qBAAQ;uDAGR;AAGD;IADC,qBAAQ;iDAGR;AAGD;IADC,qBAAQ;oDAGR;AAGD;IADC,qBAAQ;iDAGR;AAGD;IADC,qBAAQ;kDAGR;AAvDW,gBAAgB;IAUU,WAAA,oBAAO,CAAA;GAVjC,gBAAgB,CAwD5B;AAxDY,4CAAgB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.9954566-07:00\r\n\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNType } from \"./atn/ATNType\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Lexer } from \"./Lexer\";\r\nimport { LexerATNSimulator } from \"./atn/LexerATNSimulator\";\r\nimport { NotNull } from \"./Decorators\";\r\nimport { Override } from \"./Decorators\";\r\nimport { Vocabulary } from \"./Vocabulary\";\r\n\r\nexport class LexerInterpreter extends Lexer {\r\n\tprotected _grammarFileName: string;\r\n\tprotected _atn: ATN;\r\n\r\n\tprotected _ruleNames: string[];\r\n\tprotected _channelNames: string[];\r\n\tprotected _modeNames: string[];\r\n\t@NotNull\r\n\tprivate _vocabulary: Vocabulary;\r\n\r\n\tconstructor(grammarFileName: string, @NotNull vocabulary: Vocabulary, ruleNames: string[], channelNames: string[], modeNames: string[], atn: ATN, input: CharStream) {\r\n\t\tsuper(input);\r\n\r\n\t\tif (atn.grammarType !== ATNType.LEXER) {\r\n\t\t\tthrow new Error(\"IllegalArgumentException: The ATN must be a lexer ATN.\");\r\n\t\t}\r\n\r\n\t\tthis._grammarFileName = grammarFileName;\r\n\t\tthis._atn = atn;\r\n\r\n\t\tthis._ruleNames = ruleNames.slice(0);\r\n\t\tthis._channelNames = channelNames.slice(0);\r\n\t\tthis._modeNames = modeNames.slice(0);\r\n\t\tthis._vocabulary = vocabulary;\r\n\t\tthis._interp = new LexerATNSimulator(atn, this);\r\n\t}\r\n\r\n\t@Override\r\n\tget atn(): ATN {\r\n\t\treturn this._atn;\r\n\t}\r\n\r\n\t@Override\r\n\tget grammarFileName(): string {\r\n\t\treturn this._grammarFileName;\r\n\t}\r\n\r\n\t@Override\r\n\tget ruleNames(): string[] {\r\n\t\treturn this._ruleNames;\r\n\t}\r\n\r\n\t@Override\r\n\tget channelNames(): string[] {\r\n\t\treturn this._channelNames;\r\n\t}\r\n\r\n\t@Override\r\n\tget modeNames(): string[] {\r\n\t\treturn this._modeNames;\r\n\t}\r\n\r\n\t@Override\r\n\tget vocabulary(): Vocabulary {\r\n\t\treturn this._vocabulary;\r\n\t}\r\n}\r\n"]}