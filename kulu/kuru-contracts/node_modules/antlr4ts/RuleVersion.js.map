{"version": 3, "file": "RuleVersion.js", "sourceRoot": "", "sources": ["../../src/RuleVersion.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAOH;;;GAGG;AACH,SAAgB,WAAW,CAAC,OAAe;IAE1C,OAAO,CAA8B,MAAc,EAAE,WAAwB,EAAE,kBAAkE,EAAE,EAAE;QACpJ,sBAAsB;IACvB,CAAC,CAAC;AAEH,CAAC;AAND,kCAMC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.7170027-07:00\r\n\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport function RuleVersion(version: number) {\r\n\r\n\treturn <T extends ParserRuleContext>(target: Parser, propertyKey: PropertyKey, propertyDescriptor: TypedPropertyDescriptor<(...args: any[]) => T>) => {\r\n\t\t// intentionally empty\r\n\t};\r\n\r\n}\r\n"]}