{"version": 3, "file": "ListTokenSource.js", "sourceRoot": "", "sources": ["../../src/ListTokenSource.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,6DAA0D;AAC1D,6CAAiD;AACjD,mCAAgC;AAIhC;;;;;;;GAOG;AACH,IAAa,eAAe,GAA5B,MAAa,eAAe;IAgC3B;;;;;;;;;;;;OAYG;IACH,YAAqB,MAAe,EAAE,UAAmB;QA/BzD;;;;WAIG;QACO,MAAC,GAAW,CAAC,CAAC;QAOxB;;;WAGG;QACK,aAAQ,GAAiB,uCAAkB,CAAC,OAAO,CAAC;QAgB3D,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IAEH,IAAI,kBAAkB;QACrB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAChC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC;SAC9C;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,oEAAoE;YACpE,2CAA2C;YAC3C,IAAI,SAAS,GAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAI,SAAS,GAAuB,SAAS,CAAC,IAAI,CAAC;YACnD,IAAI,SAAS,IAAI,IAAI,EAAE;gBACtB,IAAI,WAAW,GAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,WAAW,IAAI,CAAC,EAAE;oBACrB,OAAO,SAAS,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC;iBAC1C;aACD;YAED,OAAO,SAAS,CAAC,kBAAkB,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;SACrF;QAED,sEAAsE;QACtE,wBAAwB;QACxB,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IAEI,SAAS;QACf,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACjC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAC1B,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;gBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3B,IAAI,YAAY,GAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;oBACzE,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;wBACxB,KAAK,GAAG,YAAY,GAAG,CAAC,CAAC;qBACzB;iBACD;gBAED,IAAI,IAAI,GAAW,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,aAAK,CAAC,GAAG,EAAE,KAAK,EAAE,aAAK,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC3K;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC;SACrB;QAED,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;YAC9D,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,CAAC,CAAC,EAAE,CAAC;QACT,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IAEH,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAChC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SAChC;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,oEAAoE;YACpE,2CAA2C;YAC3C,IAAI,SAAS,GAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAI,IAAI,GAAW,SAAS,CAAC,IAAI,CAAC;YAElC,IAAI,SAAS,GAAuB,SAAS,CAAC,IAAI,CAAC;YACnD,IAAI,SAAS,IAAI,IAAI,EAAE;gBACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;wBACjC,IAAI,EAAE,CAAC;qBACP;iBACD;aACD;YAED,oFAAoF;YACpF,OAAO,IAAI,CAAC;SACZ;QAED,sEAAsE;QACtE,wBAAwB;QACxB,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IAEH,IAAI,WAAW;QACd,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAChC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;SACvC;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;SACjC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;SACvD;QAED,2CAA2C;QAC3C,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;OAEG;IAEH,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO,IAAI,CAAC,WAAW,CAAC;SACxB;QAED,IAAI,WAAW,GAA2B,IAAI,CAAC,WAAW,CAAC;QAC3D,IAAI,WAAW,IAAI,IAAI,EAAE;YACxB,OAAO,WAAW,CAAC,UAAU,CAAC;SAC9B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;OAEG;IACH,YAAY;IACZ,IAAI,YAAY,CAAU,OAAqB;QAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IAGH,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;CACD,CAAA;AA3IA;IADC,qBAAQ;yDAwBR;AAMD;IADC,qBAAQ;gDA0BR;AAMD;IADC,qBAAQ;2CA4BR;AAMD;IADC,qBAAQ;kDAYR;AAMD;IADC,qBAAQ;iDAYR;AAeD;IAFC,qBAAQ;IACR,oBAAO;IARU,WAAA,oBAAO,CAAA;mDAWxB;AApMW,eAAe;IA6Cd,WAAA,oBAAO,CAAA;GA7CR,eAAe,CAqM3B;AArMY,0CAAe", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:52.1916955-07:00\r\n\r\nimport { CharStream } from \"./CharStream\";\r\nimport { CommonTokenFactory } from \"./CommonTokenFactory\";\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/**\r\n * Provides an implementation of {@link TokenSource} as a wrapper around a list\r\n * of {@link Token} objects.\r\n *\r\n * If the final token in the list is an {@link Token#EOF} token, it will be used\r\n * as the EOF token for every call to {@link #nextToken} after the end of the\r\n * list is reached. Otherwise, an EOF token will be created.\r\n */\r\nexport class ListTokenSource implements TokenSource {\r\n\t/**\r\n\t * The wrapped collection of {@link Token} objects to return.\r\n\t */\r\n\tprotected tokens: Token[];\r\n\r\n\t/**\r\n\t * The name of the input source. If this value is `undefined`, a call to\r\n\t * {@link #getSourceName} should return the source name used to create the\r\n\t * the next token in {@link #tokens} (or the previous token if the end of\r\n\t * the input has been reached).\r\n\t */\r\n\tprivate _sourceName?: string;\r\n\r\n\t/**\r\n\t * The index into {@link #tokens} of token to return by the next call to\r\n\t * {@link #nextToken}. The end of the input is indicated by this value\r\n\t * being greater than or equal to the number of items in {@link #tokens}.\r\n\t */\r\n\tprotected i: number = 0;\r\n\r\n\t/**\r\n\t * This field caches the EOF token for the token source.\r\n\t */\r\n\tprotected eofToken?: Token;\r\n\r\n\t/**\r\n\t * This is the backing field for {@link #getTokenFactory} and\r\n\t * {@link setTokenFactory}.\r\n\t */\r\n\tprivate _factory: TokenFactory = CommonTokenFactory.DEFAULT;\r\n\r\n\t/**\r\n\t * Constructs a new {@link ListTokenSource} instance from the specified\r\n\t * collection of {@link Token} objects and source name.\r\n\t *\r\n\t * @param tokens The collection of {@link Token} objects to provide as a\r\n\t * {@link TokenSource}.\r\n\t * @param sourceName The name of the {@link TokenSource}. If this value is\r\n\t * `undefined`, {@link #getSourceName} will attempt to infer the name from\r\n\t * the next {@link Token} (or the previous token if the end of the input has\r\n\t * been reached).\r\n\t *\r\n\t * @exception NullPointerException if `tokens` is `undefined`\r\n\t */\r\n\tconstructor(@NotNull tokens: Token[], sourceName?: string) {\r\n\t\tif (tokens == null) {\r\n\t\t\tthrow new Error(\"tokens cannot be null\");\r\n\t\t}\r\n\r\n\t\tthis.tokens = tokens;\r\n\t\tthis._sourceName = sourceName;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tget charPositionInLine(): number {\r\n\t\tif (this.i < this.tokens.length) {\r\n\t\t\treturn this.tokens[this.i].charPositionInLine;\r\n\t\t} else if (this.eofToken != null) {\r\n\t\t\treturn this.eofToken.charPositionInLine;\r\n\t\t} else if (this.tokens.length > 0) {\r\n\t\t\t// have to calculate the result from the line/column of the previous\r\n\t\t\t// token, along with the text of the token.\r\n\t\t\tlet lastToken: Token = this.tokens[this.tokens.length - 1];\r\n\t\t\tlet tokenText: string | undefined = lastToken.text;\r\n\t\t\tif (tokenText != null) {\r\n\t\t\t\tlet lastNewLine: number = tokenText.lastIndexOf(\"\\n\");\r\n\t\t\t\tif (lastNewLine >= 0) {\r\n\t\t\t\t\treturn tokenText.length - lastNewLine - 1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn lastToken.charPositionInLine + lastToken.stopIndex - lastToken.startIndex + 1;\r\n\t\t}\r\n\r\n\t\t// only reach this if tokens is empty, meaning EOF occurs at the first\r\n\t\t// position in the input\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tpublic nextToken(): Token {\r\n\t\tif (this.i >= this.tokens.length) {\r\n\t\t\tif (this.eofToken == null) {\r\n\t\t\t\tlet start: number = -1;\r\n\t\t\t\tif (this.tokens.length > 0) {\r\n\t\t\t\t\tlet previousStop: number = this.tokens[this.tokens.length - 1].stopIndex;\r\n\t\t\t\t\tif (previousStop !== -1) {\r\n\t\t\t\t\t\tstart = previousStop + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet stop: number = Math.max(-1, start - 1);\r\n\t\t\t\tthis.eofToken = this._factory.create({ source: this, stream: this.inputStream }, Token.EOF, \"EOF\", Token.DEFAULT_CHANNEL, start, stop, this.line, this.charPositionInLine);\r\n\t\t\t}\r\n\r\n\t\t\treturn this.eofToken;\r\n\t\t}\r\n\r\n\t\tlet t: Token = this.tokens[this.i];\r\n\t\tif (this.i === this.tokens.length - 1 && t.type === Token.EOF) {\r\n\t\t\tthis.eofToken = t;\r\n\t\t}\r\n\r\n\t\tthis.i++;\r\n\t\treturn t;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tget line(): number {\r\n\t\tif (this.i < this.tokens.length) {\r\n\t\t\treturn this.tokens[this.i].line;\r\n\t\t} else if (this.eofToken != null) {\r\n\t\t\treturn this.eofToken.line;\r\n\t\t} else if (this.tokens.length > 0) {\r\n\t\t\t// have to calculate the result from the line/column of the previous\r\n\t\t\t// token, along with the text of the token.\r\n\t\t\tlet lastToken: Token = this.tokens[this.tokens.length - 1];\r\n\t\t\tlet line: number = lastToken.line;\r\n\r\n\t\t\tlet tokenText: string | undefined = lastToken.text;\r\n\t\t\tif (tokenText != null) {\r\n\t\t\t\tfor (let i = 0; i < tokenText.length; i++) {\r\n\t\t\t\t\tif (tokenText.charAt(i) === \"\\n\") {\r\n\t\t\t\t\t\tline++;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// if no text is available, assume the token did not contain any newline characters.\r\n\t\t\treturn line;\r\n\t\t}\r\n\r\n\t\t// only reach this if tokens is empty, meaning EOF occurs at the first\r\n\t\t// position in the input\r\n\t\treturn 1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tget inputStream(): CharStream | undefined {\r\n\t\tif (this.i < this.tokens.length) {\r\n\t\t\treturn this.tokens[this.i].inputStream;\r\n\t\t} else if (this.eofToken != null) {\r\n\t\t\treturn this.eofToken.inputStream;\r\n\t\t} else if (this.tokens.length > 0) {\r\n\t\t\treturn this.tokens[this.tokens.length - 1].inputStream;\r\n\t\t}\r\n\r\n\t\t// no input stream information is available\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tget sourceName(): string {\r\n\t\tif (this._sourceName) {\r\n\t\t\treturn this._sourceName;\r\n\t\t}\r\n\r\n\t\tlet inputStream: CharStream | undefined = this.inputStream;\r\n\t\tif (inputStream != null) {\r\n\t\t\treturn inputStream.sourceName;\r\n\t\t}\r\n\r\n\t\treturn \"List\";\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t// @Override\r\n\tset tokenFactory(@NotNull factory: TokenFactory) {\r\n\t\tthis._factory = factory;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\t@NotNull\r\n\tget tokenFactory(): TokenFactory {\r\n\t\treturn this._factory;\r\n\t}\r\n}\r\n"]}