{"version": 3, "file": "ParserErrorListener.js", "sourceRoot": "", "sources": ["../../src/ParserErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:52.9471863-07:00\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { ATNConfigSet } from \"./atn/ATNConfigSet\";\r\nimport { BitSet } from \"./misc/BitSet\";\r\nimport { DFA } from \"./dfa/DFA\";\r\nimport { Parser } from \"./Parser\";\r\nimport { SimulatorState } from \"./atn/SimulatorState\";\r\nimport { Token } from \"./Token\";\r\nimport * as Stubs from \"./misc/Stubs\";\r\nimport * as Exception from \"./RecognitionException\";\r\n\r\n/** How to emit recognition errors for parsers.\r\n */\r\nexport interface ParserErrorListener extends ANTLRErrorListener<Token> {\r\n\t/**\r\n\t * This method is called by the parser when a full-context prediction\r\n\t * results in an ambiguity.\r\n\t *\r\n\t * Each full-context prediction which does not result in a syntax error\r\n\t * will call either {@link #reportContextSensitivity} or\r\n\t * {@link #reportAmbiguity}.\r\n\t *\r\n\t * When `ambigAlts` is not `undefined`, it contains the set of potentially\r\n\t * viable alternatives identified by the prediction algorithm. When\r\n\t * `ambigAlts` is `undefined`, use\r\n\t * {@link ATNConfigSet#getRepresentedAlternatives} to obtain the represented\r\n\t * alternatives from the `configs` argument.\r\n\t *\r\n\t * When `exact` is `true`, *all* of the potentially\r\n\t * viable alternatives are truly viable, i.e. this is reporting an exact\r\n\t * ambiguity. When `exact` is `false`, *at least two* of\r\n\t * the potentially viable alternatives are viable for the current input, but\r\n\t * the prediction algorithm terminated as soon as it determined that at\r\n\t * least the *minimum* potentially viable alternative is truly\r\n\t * viable.\r\n\t *\r\n\t * When the {@link PredictionMode#LL_EXACT_AMBIG_DETECTION} prediction\r\n\t * mode is used, the parser is required to identify exact ambiguities so\r\n\t * `exact` will always be `true`.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param dfa the DFA for the current decision\r\n\t * @param startIndex the input index where the decision started\r\n\t * @param stopIndex the input input where the ambiguity was identified\r\n\t * @param exact `true` if the ambiguity is exactly known, otherwise\r\n\t * `false`. This is always `true` when\r\n\t * {@link PredictionMode#LL_EXACT_AMBIG_DETECTION} is used.\r\n\t * @param ambigAlts the potentially ambiguous alternatives, or `undefined`\r\n\t * to indicate that the potentially ambiguous alternatives are the complete\r\n\t * set of represented alternatives in `configs`\r\n\t * @param configs the ATN configuration set where the ambiguity was\r\n\t * identified\r\n\t */\r\n\treportAmbiguity?: (\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Parser,\r\n\t\t/*@NotNull*/\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\texact: boolean,\r\n\t\tambigAlts: BitSet | undefined,\r\n\t\t/*@NotNull*/\r\n\t\tconfigs: ATNConfigSet) => void;\r\n\r\n\t/**\r\n\t * This method is called when an SLL conflict occurs and the parser is about\r\n\t * to use the full context information to make an LL decision.\r\n\t *\r\n\t * If one or more configurations in `configs` contains a semantic\r\n\t * predicate, the predicates are evaluated before this method is called. The\r\n\t * subset of alternatives which are still viable after predicates are\r\n\t * evaluated is reported in `conflictingAlts`.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param dfa the DFA for the current decision\r\n\t * @param startIndex the input index where the decision started\r\n\t * @param stopIndex the input index where the SLL conflict occurred\r\n\t * @param conflictingAlts The specific conflicting alternatives. If this is\r\n\t * `undefined`, the conflicting alternatives are all alternatives\r\n\t * represented in `configs`.\r\n\t * @param conflictState the simulator state when the SLL conflict was\r\n\t * detected\r\n\t */\r\n\treportAttemptingFullContext?: (\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Parser,\r\n\t\t/*@NotNull*/\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tconflictingAlts: BitSet | undefined,\r\n\t\t/*@NotNull*/\r\n\t\tconflictState: SimulatorState) => void;\r\n\r\n\t/**\r\n\t * This method is called by the parser when a full-context prediction has a\r\n\t * unique result.\r\n\t *\r\n\t * Each full-context prediction which does not result in a syntax error\r\n\t * will call either {@link #reportContextSensitivity} or\r\n\t * {@link #reportAmbiguity}.\r\n\t *\r\n\t * For prediction implementations that only evaluate full-context\r\n\t * predictions when an SLL conflict is found (including the default\r\n\t * {@link ParserATNSimulator} implementation), this method reports cases\r\n\t * where SLL conflicts were resolved to unique full-context predictions,\r\n\t * i.e. the decision was context-sensitive. This report does not necessarily\r\n\t * indicate a problem, and it may appear even in completely unambiguous\r\n\t * grammars.\r\n\t *\r\n\t * `configs` may have more than one represented alternative if the\r\n\t * full-context prediction algorithm does not evaluate predicates before\r\n\t * beginning the full-context prediction. In all cases, the final prediction\r\n\t * is passed as the `prediction` argument.\r\n\t *\r\n\t * Note that the definition of \"context sensitivity\" in this method\r\n\t * differs from the concept in {@link DecisionInfo#contextSensitivities}.\r\n\t * This method reports all instances where an SLL conflict occurred but LL\r\n\t * parsing produced a unique result, whether or not that unique result\r\n\t * matches the minimum alternative in the SLL conflicting set.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param dfa the DFA for the current decision\r\n\t * @param startIndex the input index where the decision started\r\n\t * @param stopIndex the input index where the context sensitivity was\r\n\t * finally determined\r\n\t * @param prediction the unambiguous result of the full-context prediction\r\n\t * @param acceptState the simulator state when the unambiguous prediction\r\n\t * was determined\r\n\t */\r\n\treportContextSensitivity?: (\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Parser,\r\n\t\t/*@NotNull*/\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tprediction: number,\r\n\t\t/*@NotNull*/\r\n\t\tacceptState: SimulatorState) => void;\r\n}\r\n"]}