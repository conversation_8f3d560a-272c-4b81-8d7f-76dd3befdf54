{"version": 3, "file": "CodePointBuffer.js", "sourceRoot": "", "sources": ["../../src/CodePointBuffer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,iCAAiC;AACjC,8CAA8C;AAE9C;;GAEG;AACH,MAAa,eAAe;IAK3B,YAAY,MAA6C,EAAE,IAAY;QACtE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,MAA6C;QACpE,OAAO,IAAI,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAED,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAED,IAAW,QAAQ,CAAC,WAAmB;QACtC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE;YAChD,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;IAC9B,CAAC;IAED,IAAW,SAAS;QACnB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;IACnC,CAAC;IAEM,GAAG,CAAC,MAAc;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEM,KAAK;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,iBAAyB;QAC9C,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACvD,CAAC;CACD;AA1CD,0CA0CC;AAED,WAAiB,eAAe;IAC/B,IAAW,IAIV;IAJD,WAAW,IAAI;QACd,+BAAI,CAAA;QACJ,+BAAI,CAAA;QACJ,6BAAG,CAAA;IACJ,CAAC,EAJU,IAAI,KAAJ,IAAI,QAId;IAED,MAAa,OAAO;QAMnB,YAAY,iBAAyB;YACpC,IAAI,CAAC,IAAI,eAAY,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAChD,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACnB,CAAC;QAEM,KAAK;YACX,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC;QAEO,MAAM,CAAC,uBAAuB,CAAC,CAAS;YAC/C,IAAI,cAAc,GAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QACpC,CAAC;QAEM,eAAe,CAAC,eAAuB;YAC7C,QAAQ,IAAI,CAAC,IAAI,EAAE;gBAClB;oBACC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,eAAe,EAAE;wBACzD,IAAI,WAAW,GAAW,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC;wBAChG,IAAI,SAAS,GAAe,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;wBACxD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;wBACzD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;qBACxB;oBACD,MAAM;gBACP;oBACC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,eAAe,EAAE;wBACzD,IAAI,WAAW,GAAW,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC;wBAChG,IAAI,SAAS,GAAgB,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;wBAC1D,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;wBACzD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;qBACxB;oBACD,MAAM;gBACP;oBACC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,eAAe,EAAE;wBACzD,IAAI,WAAW,GAAW,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC;wBAChG,IAAI,SAAS,GAAe,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;wBACxD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;wBACzD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;qBACxB;oBACD,MAAM;aACP;QACF,CAAC;QAEM,MAAM,CAAC,OAAoB;YACjC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QAEO,WAAW,CAAC,OAAoB;YACvC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBAClB;oBACC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAC9B,MAAM;gBACP;oBACC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAC9B,MAAM;gBACP;oBACC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAC7B,MAAM;aACP;QACF,CAAC;QAEO,eAAe,CAAC,OAAoB;YAC3C,MAAM,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,KAAK,GAAgB,OAAO,CAAC;YACjC,IAAI,QAAQ,GAAW,CAAC,CAAC;YACzB,IAAI,OAAO,GAAW,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,IAAI,SAAS,GAAW,IAAI,CAAC,QAAQ,CAAC;YAEtC,OAAO,QAAQ,GAAG,OAAO,EAAE;gBAC1B,IAAI,CAAC,GAAW,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,CAAC,IAAI,IAAI,EAAE;oBACd,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;iBACvB;qBAAM;oBACN,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;wBAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACtC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;wBAC9B,OAAO;qBACP;yBAAM;wBACN,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACrC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;wBAC7B,OAAO;qBACP;iBACD;gBAED,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;aACZ;YAED,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC3B,CAAC;QAEO,eAAe,CAAC,OAAoB;YAC3C,MAAM,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,KAAK,GAAgB,OAAO,CAAC;YACjC,IAAI,QAAQ,GAAW,CAAC,CAAC;YACzB,IAAI,OAAO,GAAW,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,IAAI,SAAS,GAAW,IAAI,CAAC,QAAQ,CAAC;YAEtC,OAAO,QAAQ,GAAG,OAAO,EAAE;gBAC1B,IAAI,CAAC,GAAW,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;oBAClC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;iBACvB;qBAAM;oBACN,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;oBAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACrC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAC7B,OAAO;iBACP;gBAED,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;aACZ;YAED,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC3B,CAAC;QAEO,cAAc,CAAC,OAAoB;YAC1C,IAAI,KAAK,GAAgB,OAAO,CAAC;YACjC,IAAI,QAAQ,GAAW,CAAC,CAAC;YACzB,IAAI,OAAO,GAAW,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE9B,OAAO,QAAQ,GAAG,OAAO,EAAE;gBAC1B,IAAI,CAAC,GAAW,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChC,QAAQ,EAAE,CAAC;gBACX,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;oBAClC,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;wBAChC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC;wBACnF,SAAS,EAAE,CAAC;wBACZ,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;qBAC5B;yBAAM;wBACN,0BAA0B;wBAC1B,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;wBAC3C,SAAS,EAAE,CAAC;wBACZ,IAAI,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;4BACjC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;yBAC3B;6BAAM;4BACN,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BACtB,SAAS,EAAE,CAAC;4BACZ,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;yBAC5B;qBACD;iBACD;qBAAM,IAAI,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;oBACxC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;iBAC3B;qBAAM;oBACN,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACtB,SAAS,EAAE,CAAC;iBACZ;aACD;YAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;gBAClC,0BAA0B;gBAC1B,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;gBAC3C,SAAS,EAAE,CAAC;aACZ;YAED,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC3B,CAAC;QAEO,gBAAgB,CAAC,QAAgB;YACxC,2FAA2F;YAC3F,IAAI,SAAS,GAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1G,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzD,IAAI,CAAC,IAAI,eAAY,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACzB,CAAC;QAEO,eAAe,CAAC,QAAgB;YACvC,sGAAsG;YACtG,IAAI,SAAS,GAAe,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YACxG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzD,IAAI,CAAC,IAAI,cAAW,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACzB,CAAC;QAEO,eAAe,CAAC,QAAgB;YACvC,kGAAkG;YAClG,IAAI,SAAS,GAAe,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YACxG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzD,IAAI,CAAC,IAAI,cAAW,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACzB,CAAC;KACD;IA7MY,uBAAO,UA6MnB,CAAA;AACF,CAAC,EArNgB,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAqN/B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport * as assert from \"assert\";\r\nimport * as Character from \"./misc/Character\";\r\n\r\n/**\r\n * Wrapper for `Uint8Array` / `Uint16Array` / `Int32Array`.\r\n */\r\nexport class CodePointBuffer {\r\n\tprivate readonly buffer: Uint8Array | Uint16Array | Int32Array;\r\n\tprivate _position: number;\r\n\tprivate _size: number;\r\n\r\n\tconstructor(buffer: Uint8Array | Uint16Array | Int32Array, size: number) {\r\n\t\tthis.buffer = buffer;\r\n\t\tthis._position = 0;\r\n\t\tthis._size = size;\r\n\t}\r\n\r\n\tpublic static withArray(buffer: Uint8Array | Uint16Array | Int32Array): CodePointBuffer {\r\n\t\treturn new CodePointBuffer(buffer, buffer.length);\r\n\t}\r\n\r\n\tpublic get position(): number {\r\n\t\treturn this._position;\r\n\t}\r\n\r\n\tpublic set position(newPosition: number) {\r\n\t\tif (newPosition < 0 || newPosition > this._size) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\tthis._position = newPosition;\r\n\t}\r\n\r\n\tpublic get remaining(): number {\r\n\t\treturn this._size - this.position;\r\n\t}\r\n\r\n\tpublic get(offset: number): number {\r\n\t\treturn this.buffer[offset];\r\n\t}\r\n\r\n\tpublic array(): Uint8Array | Uint16Array | Int32Array {\r\n\t\treturn this.buffer.slice(0, this._size);\r\n\t}\r\n\r\n\tpublic static builder(initialBufferSize: number): CodePointBuffer.Builder {\r\n\t\treturn new CodePointBuffer.Builder(initialBufferSize);\r\n\t}\r\n}\r\n\r\nexport namespace CodePointBuffer {\r\n\tconst enum Type {\r\n\t\tBYTE,\r\n\t\tCHAR,\r\n\t\tINT,\r\n\t}\r\n\r\n\texport class Builder {\r\n\t\tprivate type: Type;\r\n\t\tprivate buffer: Uint8Array | Uint16Array | Int32Array;\r\n\t\tprivate prevHighSurrogate: number;\r\n\t\tprivate position: number;\r\n\r\n\t\tconstructor(initialBufferSize: number) {\r\n\t\t\tthis.type = Type.BYTE;\r\n\t\t\tthis.buffer = new Uint8Array(initialBufferSize);\r\n\t\t\tthis.prevHighSurrogate = -1;\r\n\t\t\tthis.position = 0;\r\n\t\t}\r\n\r\n\t\tpublic build(): CodePointBuffer {\r\n\t\t\treturn new CodePointBuffer(this.buffer, this.position);\r\n\t\t}\r\n\r\n\t\tprivate static roundUpToNextPowerOfTwo(i: number): number {\r\n\t\t\tlet nextPowerOfTwo: number = 32 - Math.clz32(i - 1);\r\n\t\t\treturn Math.pow(2, nextPowerOfTwo);\r\n\t\t}\r\n\r\n\t\tpublic ensureRemaining(remainingNeeded: number): void {\r\n\t\t\tswitch (this.type) {\r\n\t\t\t\tcase Type.BYTE:\r\n\t\t\t\t\tif (this.buffer.length - this.position < remainingNeeded) {\r\n\t\t\t\t\t\tlet newCapacity: number = Builder.roundUpToNextPowerOfTwo(this.buffer.length + remainingNeeded);\r\n\t\t\t\t\t\tlet newBuffer: Uint8Array = new Uint8Array(newCapacity);\r\n\t\t\t\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\t\t\t\t\t\tthis.buffer = newBuffer;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase Type.CHAR:\r\n\t\t\t\t\tif (this.buffer.length - this.position < remainingNeeded) {\r\n\t\t\t\t\t\tlet newCapacity: number = Builder.roundUpToNextPowerOfTwo(this.buffer.length + remainingNeeded);\r\n\t\t\t\t\t\tlet newBuffer: Uint16Array = new Uint16Array(newCapacity);\r\n\t\t\t\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\t\t\t\t\t\tthis.buffer = newBuffer;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase Type.INT:\r\n\t\t\t\t\tif (this.buffer.length - this.position < remainingNeeded) {\r\n\t\t\t\t\t\tlet newCapacity: number = Builder.roundUpToNextPowerOfTwo(this.buffer.length + remainingNeeded);\r\n\t\t\t\t\t\tlet newBuffer: Int32Array = new Int32Array(newCapacity);\r\n\t\t\t\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\t\t\t\t\t\tthis.buffer = newBuffer;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tpublic append(utf16In: Uint16Array): void {\r\n\t\t\tthis.ensureRemaining(utf16In.length);\r\n\t\t\tthis.appendArray(utf16In);\r\n\t\t}\r\n\r\n\t\tprivate appendArray(utf16In: Uint16Array): void {\r\n\t\t\tswitch (this.type) {\r\n\t\t\t\tcase Type.BYTE:\r\n\t\t\t\t\tthis.appendArrayByte(utf16In);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase Type.CHAR:\r\n\t\t\t\t\tthis.appendArrayChar(utf16In);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase Type.INT:\r\n\t\t\t\t\tthis.appendArrayInt(utf16In);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tprivate appendArrayByte(utf16In: Uint16Array): void {\r\n\t\t\tassert(this.prevHighSurrogate === -1);\r\n\r\n\t\t\tlet input: Uint16Array = utf16In;\r\n\t\t\tlet inOffset: number = 0;\r\n\t\t\tlet inLimit: number = utf16In.length;\r\n\r\n\t\t\tlet outByte = this.buffer;\r\n\t\t\tlet outOffset: number = this.position;\r\n\r\n\t\t\twhile (inOffset < inLimit) {\r\n\t\t\t\tlet c: number = input[inOffset];\r\n\t\t\t\tif (c <= 0xFF) {\r\n\t\t\t\t\toutByte[outOffset] = c;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tutf16In = utf16In.subarray(inOffset, inLimit);\r\n\t\t\t\t\tthis.position = outOffset;\r\n\t\t\t\t\tif (!Character.isHighSurrogate(c)) {\r\n\t\t\t\t\t\tthis.byteToCharBuffer(utf16In.length);\r\n\t\t\t\t\t\tthis.appendArrayChar(utf16In);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.byteToIntBuffer(utf16In.length);\r\n\t\t\t\t\t\tthis.appendArrayInt(utf16In);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinOffset++;\r\n\t\t\t\toutOffset++;\r\n\t\t\t}\r\n\r\n\t\t\tthis.position = outOffset;\r\n\t\t}\r\n\r\n\t\tprivate appendArrayChar(utf16In: Uint16Array): void {\r\n\t\t\tassert(this.prevHighSurrogate === -1);\r\n\r\n\t\t\tlet input: Uint16Array = utf16In;\r\n\t\t\tlet inOffset: number = 0;\r\n\t\t\tlet inLimit: number = utf16In.length;\r\n\r\n\t\t\tlet outChar = this.buffer;\r\n\t\t\tlet outOffset: number = this.position;\r\n\r\n\t\t\twhile (inOffset < inLimit) {\r\n\t\t\t\tlet c: number = input[inOffset];\r\n\t\t\t\tif (!Character.isHighSurrogate(c)) {\r\n\t\t\t\t\toutChar[outOffset] = c;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tutf16In = utf16In.subarray(inOffset, inLimit);\r\n\t\t\t\t\tthis.position = outOffset;\r\n\t\t\t\t\tthis.charToIntBuffer(utf16In.length);\r\n\t\t\t\t\tthis.appendArrayInt(utf16In);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinOffset++;\r\n\t\t\t\toutOffset++;\r\n\t\t\t}\r\n\r\n\t\t\tthis.position = outOffset;\r\n\t\t}\r\n\r\n\t\tprivate appendArrayInt(utf16In: Uint16Array): void {\r\n\t\t\tlet input: Uint16Array = utf16In;\r\n\t\t\tlet inOffset: number = 0;\r\n\t\t\tlet inLimit: number = utf16In.length;\r\n\r\n\t\t\tlet outInt = this.buffer;\r\n\t\t\tlet outOffset = this.position;\r\n\r\n\t\t\twhile (inOffset < inLimit) {\r\n\t\t\t\tlet c: number = input[inOffset];\r\n\t\t\t\tinOffset++;\r\n\t\t\t\tif (this.prevHighSurrogate !== -1) {\r\n\t\t\t\t\tif (Character.isLowSurrogate(c)) {\r\n\t\t\t\t\t\toutInt[outOffset] = String.fromCharCode(this.prevHighSurrogate, c).codePointAt(0)!;\r\n\t\t\t\t\t\toutOffset++;\r\n\t\t\t\t\t\tthis.prevHighSurrogate = -1;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Dangling high surrogate\r\n\t\t\t\t\t\toutInt[outOffset] = this.prevHighSurrogate;\r\n\t\t\t\t\t\toutOffset++;\r\n\t\t\t\t\t\tif (Character.isHighSurrogate(c)) {\r\n\t\t\t\t\t\t\tthis.prevHighSurrogate = c;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\toutInt[outOffset] = c;\r\n\t\t\t\t\t\t\toutOffset++;\r\n\t\t\t\t\t\t\tthis.prevHighSurrogate = -1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (Character.isHighSurrogate(c)) {\r\n\t\t\t\t\tthis.prevHighSurrogate = c;\r\n\t\t\t\t} else {\r\n\t\t\t\t\toutInt[outOffset] = c;\r\n\t\t\t\t\toutOffset++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (this.prevHighSurrogate !== -1) {\r\n\t\t\t\t// Dangling high surrogate\r\n\t\t\t\toutInt[outOffset] = this.prevHighSurrogate;\r\n\t\t\t\toutOffset++;\r\n\t\t\t}\r\n\r\n\t\t\tthis.position = outOffset;\r\n\t\t}\r\n\r\n\t\tprivate byteToCharBuffer(toAppend: number): void {\r\n\t\t\t// CharBuffers hold twice as much per unit as ByteBuffers, so start with half the capacity.\r\n\t\t\tlet newBuffer: Uint16Array = new Uint16Array(Math.max(this.position + toAppend, this.buffer.length >> 1));\r\n\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\r\n\t\t\tthis.type = Type.CHAR;\r\n\t\t\tthis.buffer = newBuffer;\r\n\t\t}\r\n\r\n\t\tprivate byteToIntBuffer(toAppend: number): void {\r\n\t\t\t// IntBuffers hold four times as much per unit as ByteBuffers, so start with one quarter the capacity.\r\n\t\t\tlet newBuffer: Int32Array = new Int32Array(Math.max(this.position + toAppend, this.buffer.length >> 2));\r\n\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\r\n\t\t\tthis.type = Type.INT;\r\n\t\t\tthis.buffer = newBuffer;\r\n\t\t}\r\n\r\n\t\tprivate charToIntBuffer(toAppend: number): void {\r\n\t\t\t// IntBuffers hold two times as much per unit as ByteBuffers, so start with one half the capacity.\r\n\t\t\tlet newBuffer: Int32Array = new Int32Array(Math.max(this.position + toAppend, this.buffer.length >> 1));\r\n\t\t\tnewBuffer.set(this.buffer.subarray(0, this.position), 0);\r\n\r\n\t\t\tthis.type = Type.INT;\r\n\t\t\tthis.buffer = newBuffer;\r\n\t\t}\r\n\t}\r\n}\r\n"]}