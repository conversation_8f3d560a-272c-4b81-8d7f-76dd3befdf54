{"version": 3, "file": "ANTLRErrorStrategy.js", "sourceRoot": "", "sources": ["../../src/ANTLRErrorStrategy.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:48.9102174-07:00\r\n\r\nimport { Parser } from \"./Parser\";\r\nimport { Token } from \"./Token\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\n\r\n/**\r\n * The interface for defining strategies to deal with syntax errors encountered\r\n * during a parse by ANTLR-generated parsers. We distinguish between three\r\n * different kinds of errors:\r\n *\r\n * * The parser could not figure out which path to take in the ATN (none of\r\n *   the available alternatives could possibly match)\r\n * * The current input does not match what we were looking for\r\n * * A predicate evaluated to false\r\n *\r\n * Implementations of this interface report syntax errors by calling\r\n * {@link Parser#notifyErrorListeners}.\r\n *\r\n * TODO: what to do about lexers\r\n */\r\nexport interface ANTLRErrorStrategy {\r\n\t/**\r\n\t * Reset the error handler state for the specified `recognizer`.\r\n\t * @param recognizer the parser instance\r\n\t */\r\n\treset(/*@NotNull*/ recognizer: Parser): void;\r\n\r\n\t/**\r\n\t * This method is called when an unexpected symbol is encountered during an\r\n\t * inline match operation, such as {@link Parser#match}. If the error\r\n\t * strategy successfully recovers from the match failure, this method\r\n\t * returns the {@link Token} instance which should be treated as the\r\n\t * successful result of the match.\r\n\t *\r\n\t * This method handles the consumption of any tokens - the caller should\r\n\t * *not* call {@link Parser#consume} after a successful recovery.\r\n\t *\r\n\t * Note that the calling code will not report an error if this method\r\n\t * returns successfully. The error strategy implementation is responsible\r\n\t * for calling {@link Parser#notifyErrorListeners} as appropriate.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @ if the error strategy was not able to\r\n\t * recover from the unexpected input symbol\r\n\t */\r\n\trecoverInline(/*@NotNull*/ recognizer: Parser): Token;\r\n\r\n\t/**\r\n\t * This method is called to recover from exception `e`. This method is\r\n\t * called after {@link #reportError} by the default exception handler\r\n\t * generated for a rule method.\r\n\t *\r\n\t * @see #reportError\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param e the recognition exception to recover from\r\n\t * @ if the error strategy could not recover from\r\n\t * the recognition exception\r\n\t */\r\n\trecover(/*@NotNull*/ recognizer: Parser, /*@NotNull*/ e: RecognitionException): void;\r\n\r\n\t/**\r\n\t * This method provides the error handler with an opportunity to handle\r\n\t * syntactic or semantic errors in the input stream before they result in a\r\n\t * {@link RecognitionException}.\r\n\t *\r\n\t * The generated code currently contains calls to {@link #sync} after\r\n\t * entering the decision state of a closure block (`(...)*` or\r\n\t * `(...)+`).\r\n\t *\r\n\t * For an implementation based on Jim Idle's \"magic sync\" mechanism, see\r\n\t * {@link DefaultErrorStrategy#sync}.\r\n\t *\r\n\t * @see DefaultErrorStrategy#sync\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @ if an error is detected by the error\r\n\t * strategy but cannot be automatically recovered at the current state in\r\n\t * the parsing process\r\n\t */\r\n\tsync(/*@NotNull*/ recognizer: Parser): void;\r\n\r\n\t/**\r\n\t * Tests whether or not `recognizer` is in the process of recovering\r\n\t * from an error. In error recovery mode, {@link Parser#consume} adds\r\n\t * symbols to the parse tree by calling\r\n\t * {@link Parser#createErrorNode(ParserRuleContext, Token)} then\r\n\t * {@link ParserRuleContext#addErrorNode(ErrorNode)} instead of\r\n\t * {@link Parser#createTerminalNode(ParserRuleContext, Token)}.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @returns `true` if the parser is currently recovering from a parse\r\n\t * error, otherwise `false`\r\n\t */\r\n\tinErrorRecoveryMode(/*@NotNull*/ recognizer: Parser): boolean;\r\n\r\n\t/**\r\n\t * This method is called by when the parser successfully matches an input\r\n\t * symbol.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t */\r\n\treportMatch(/*@NotNull*/ recognizer: Parser): void;\r\n\r\n\t/**\r\n\t * Report any kind of {@link RecognitionException}. This method is called by\r\n\t * the default exception handler generated for a rule method.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param e the recognition exception to report\r\n\t */\r\n\treportError(\r\n\t\t/*@NotNull*/ recognizer: Parser,\r\n\t\t/*@NotNull*/ e: RecognitionException): void;\r\n}\r\n"]}