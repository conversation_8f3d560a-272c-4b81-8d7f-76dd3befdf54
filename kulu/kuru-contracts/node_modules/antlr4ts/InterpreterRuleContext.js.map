{"version": 3, "file": "InterpreterRuleContext.js", "sourceRoot": "", "sources": ["../../src/InterpreterRuleContext.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,6CAAwC;AACxC,2DAAwD;AAExD;;;;;;;;;;GAUG;AACH,MAAa,sBAAuB,SAAQ,qCAAiB;IAkB5D,YAAY,SAAiB,EAAE,MAA0B,EAAE,mBAA4B;QACtF,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACtC,KAAK,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;SACnC;aAAM;YACN,KAAK,EAAE,CAAC;SACR;QAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC7B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;CACD;AAHA;IADC,qBAAQ;uDAGR;AA/BF,wDAgCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.5898546-07:00\r\n\r\nimport { Override } from \"./Decorators\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\n\r\n/**\r\n * This class extends {@link ParserRuleContext} by allowing the value of\r\n * {@link #getRuleIndex} to be explicitly set for the context.\r\n *\r\n * {@link ParserRuleContext} does not include field storage for the rule index\r\n * since the context classes created by the code generator override the\r\n * {@link #getRuleIndex} method to return the correct value for that context.\r\n * Since the parser interpreter does not use the context classes generated for a\r\n * parser, this class (with slightly more memory overhead per node) is used to\r\n * provide equivalent functionality.\r\n */\r\nexport class InterpreterRuleContext extends ParserRuleContext {\r\n\t/**\r\n\t * This is the backing field for {@link #getRuleIndex}.\r\n\t */\r\n\tprivate _ruleIndex: number;\r\n\r\n\tconstructor(ruleIndex: number);\r\n\r\n\t/**\r\n\t * Constructs a new {@link InterpreterRuleContext} with the specified\r\n\t * parent, invoking state, and rule index.\r\n\t *\r\n\t * @param ruleIndex The rule index for the current context.\r\n\t * @param parent The parent context.\r\n\t * @param invokingStateNumber The invoking state number.\r\n\t */\r\n\tconstructor(ruleIndex: number, parent: ParserRuleContext | undefined, invokingStateNumber: number);\r\n\r\n\tconstructor(ruleIndex: number, parent?: ParserRuleContext, invokingStateNumber?: number) {\r\n\t\tif (invokingStateNumber !== undefined) {\r\n\t\t\tsuper(parent, invokingStateNumber);\r\n\t\t} else {\r\n\t\t\tsuper();\r\n\t\t}\r\n\r\n\t\tthis._ruleIndex = ruleIndex;\r\n\t}\r\n\r\n\t@Override\r\n\tget ruleIndex(): number {\r\n\t\treturn this._ruleIndex;\r\n\t}\r\n}\r\n"]}