{"version": 3, "file": "ANTLRInputStream.js", "sourceRoot": "", "sources": ["../../src/ANTLRInputStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG;AACH,wDAAwD;;;;;;;;;AAExD,iCAAiC;AAGjC,6CAAwC;AACxC,2CAAwC;AAGxC,MAAM,gBAAgB,GAAW,IAAI,CAAC;AACtC,MAAM,mBAAmB,GAAW,IAAI,CAAC;AAEzC;;;;;;;;GAQG;AACH,MAAa,gBAAgB;IAa5B,gDAAgD;IAChD,YAAY,KAAa;QAPzB,4CAA4C;QAClC,MAAC,GAAW,CAAC,CAAC;QAOvB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,KAAK;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACZ,CAAC;IAGM,OAAO;QACb,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,qBAAS,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACtC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,CAAC,EAAE,CAAC;YACT,iEAAiE;SACjE;IACF,CAAC;IAGM,EAAE,CAAC,CAAS;QAClB,IAAI,CAAC,KAAK,CAAC,EAAE;YACZ,OAAO,CAAC,CAAC,CAAC,YAAY;SACtB;QACD,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,CAAC,EAAE,CAAC,CAAC,6DAA6D;YAClE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;gBACzB,OAAO,qBAAS,CAAC,GAAG,CAAC,CAAC,qCAAqC;aAC3D;SACD;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;YAC/B,iDAAiD;YACjD,OAAO,qBAAS,CAAC,GAAG,CAAC;SACrB;QACD,mEAAmE;QACnE,4EAA4E;QAC5E,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEM,EAAE,CAAC,CAAS;QAClB,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED;;;OAGG;IAEH,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,CAAC,CAAC;IACf,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,CAAC,CAAC;IACf,CAAC;IAED,qDAAqD;IAE9C,IAAI;QACV,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAGM,OAAO,CAAC,MAAc;QAC5B,uEAAuE;IACxE,CAAC;IAED;;OAEG;IAEI,IAAI,CAAC,KAAa;QACxB,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,mDAAmD;YACnE,OAAO;SACP;QACD,wEAAwE;QACxE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE;YACtB,IAAI,CAAC,OAAO,EAAE,CAAC;SACf;IACF,CAAC;IAGM,OAAO,CAAC,QAAkB;QAChC,IAAI,KAAK,GAAW,QAAQ,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAW,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE;YACnB,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SAClB;QACD,IAAI,KAAK,GAAW,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;QACrC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE;YACpB,OAAO,EAAE,CAAC;SACV;QACD,8DAA8D;QAC9D,2BAA2B;QAC3B,0BAA0B;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAGD,IAAI,UAAU;QACb,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACf,OAAO,qBAAS,CAAC,mBAAmB,CAAC;SACrC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAGM,QAAQ,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CACvC;AA1GA;IADC,qBAAQ;+CAYR;AAGD;IADC,qBAAQ;0CAmBR;AAWD;IADC,qBAAQ;6CAGR;AAGD;IADC,qBAAQ;4CAGR;AAID;IADC,qBAAQ;4CAGR;AAGD;IADC,qBAAQ;+CAGR;AAMD;IADC,qBAAQ;4CAWR;AAGD;IADC,qBAAQ;+CAeR;AAGD;IADC,qBAAQ;kDAMR;AAGD;IADC,qBAAQ;gDAC8B;AArIxC,4CAsIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n// ConvertTo-TS run at 2016-10-04T11:26:49.0828748-07:00\r\n\r\nimport * as assert from \"assert\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Arrays } from \"./misc/Arrays\";\r\nimport { Override } from \"./Decorators\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { Interval } from \"./misc/Interval\";\r\n\r\nconst READ_BUFFER_SIZE: number = 1024;\r\nconst INITIAL_BUFFER_SIZE: number = 1024;\r\n\r\n/**\r\n * Vacuum all input from a {@link Reader}/{@link InputStream} and then treat it\r\n * like a `char[]` buffer. Can also pass in a {@link String} or\r\n * `char[]` to use.\r\n *\r\n * If you need encoding, pass in stream/reader with correct encoding.\r\n *\r\n * @deprecated as of 4.7, please use `CharStreams` interface.\r\n */\r\nexport class ANTLRInputStream implements CharStream {\r\n\t/** The data being scanned */\r\n\tprotected data: string;\r\n\r\n\t/** How many characters are actually in the buffer */\r\n\tprotected n: number;\r\n\r\n\t/** 0..n-1 index into string of next char */\r\n\tprotected p: number = 0;\r\n\r\n\t/** What is name or source of this char stream? */\r\n\tpublic name?: string;\r\n\r\n\t/** Copy data in string to a local char array */\r\n\tconstructor(input: string) {\r\n\t\tthis.data = input;\r\n\t\tthis.n = input.length;\r\n\t}\r\n\r\n\t/** Reset the stream so that it's in the same state it was\r\n\t *  when the object was created *except* the data array is not\r\n\t *  touched.\r\n\t */\r\n\tpublic reset(): void {\r\n\t\tthis.p = 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic consume(): void {\r\n\t\tif (this.p >= this.n) {\r\n\t\t\tassert(this.LA(1) === IntStream.EOF);\r\n\t\t\tthrow new Error(\"cannot consume EOF\");\r\n\t\t}\r\n\r\n\t\t//System.out.println(\"prev p=\"+p+\", c=\"+(char)data[p]);\r\n\t\tif (this.p < this.n) {\r\n\t\t\tthis.p++;\r\n\t\t\t//System.out.println(\"p moves to \"+p+\" (c='\"+(char)data[p]+\"')\");\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic LA(i: number): number {\r\n\t\tif (i === 0) {\r\n\t\t\treturn 0; // undefined\r\n\t\t}\r\n\t\tif (i < 0) {\r\n\t\t\ti++; // e.g., translate LA(-1) to use offset i=0; then data[p+0-1]\r\n\t\t\tif ((this.p + i - 1) < 0) {\r\n\t\t\t\treturn IntStream.EOF; // invalid; no char before first char\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif ((this.p + i - 1) >= this.n) {\r\n\t\t\t//System.out.println(\"char LA(\"+i+\")=EOF; p=\"+p);\r\n\t\t\treturn IntStream.EOF;\r\n\t\t}\r\n\t\t//System.out.println(\"char LA(\"+i+\")=\"+(char)data[p+i-1]+\"; p=\"+p);\r\n\t\t//System.out.println(\"LA(\"+i+\"); p=\"+p+\" n=\"+n+\" data.length=\"+data.length);\r\n\t\treturn this.data.charCodeAt(this.p + i - 1);\r\n\t}\r\n\r\n\tpublic LT(i: number): number {\r\n\t\treturn this.LA(i);\r\n\t}\r\n\r\n\t/** Return the current input symbol index 0..n where n indicates the\r\n\t *  last symbol has been read.  The index is the index of char to\r\n\t *  be returned from LA(1).\r\n\t */\r\n\t@Override\r\n\tget index(): number {\r\n\t\treturn this.p;\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn this.n;\r\n\t}\r\n\r\n\t/** mark/release do nothing; we have entire buffer */\r\n\t@Override\r\n\tpublic mark(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic release(marker: number): void {\r\n\t\t// No default implementation since this stream buffers the entire input\r\n\t}\r\n\r\n\t/** consume() ahead until p==index; can't just set p=index as we must\r\n\t *  update line and charPositionInLine. If we seek backwards, just set p\r\n\t */\r\n\t@Override\r\n\tpublic seek(index: number): void {\r\n\t\tif (index <= this.p) {\r\n\t\t\tthis.p = index; // just jump; don't update stream state (line, ...)\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// seek forward, consume until p hits index or n (whichever comes first)\r\n\t\tindex = Math.min(index, this.n);\r\n\t\twhile (this.p < index) {\r\n\t\t\tthis.consume();\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getText(interval: Interval): string {\r\n\t\tlet start: number = interval.a;\r\n\t\tlet stop: number = interval.b;\r\n\t\tif (stop >= this.n) {\r\n\t\t\tstop = this.n - 1;\r\n\t\t}\r\n\t\tlet count: number = stop - start + 1;\r\n\t\tif (start >= this.n) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\t\t// System.err.println(\"data: \"+Arrays.toString(data)+\", n=\"+n+\r\n\t\t// \t\t\t\t   \", start=\"+start+\r\n\t\t// \t\t\t\t   \", stop=\"+stop);\r\n\t\treturn this.data.substr(start, count);\r\n\t}\r\n\r\n\t@Override\r\n\tget sourceName(): string {\r\n\t\tif (!this.name) {\r\n\t\t\treturn IntStream.UNKNOWN_SOURCE_NAME;\r\n\t\t}\r\n\t\treturn this.name;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString() { return this.data; }\r\n}\r\n"]}