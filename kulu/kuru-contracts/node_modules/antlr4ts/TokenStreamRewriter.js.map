{"version": 3, "file": "TokenStreamRewriter.js", "sourceRoot": "", "sources": ["../../src/TokenStreamRewriter.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,8CAA2C;AAC3C,6CAAwC;AACxC,mCAAgC;AAKhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmEG;AACH,MAAa,mBAAmB;IAiB/B,YAAY,MAAmB;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAA8B,CAAC;QACtD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAChE,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC1D,CAAC;IAEM,cAAc;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAQM,QAAQ,CAAC,gBAAwB,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QACvG,IAAI,EAAE,GAAoC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzE,IAAK,EAAE,IAAI,IAAI,EAAG;YACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,mBAAmB,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAC;SAChG;IACF,CAAC;IAMM,aAAa,CAAC,cAAsB,mBAAmB,CAAC,oBAAoB;QAClF,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IACjE,CAAC;IAMM,WAAW,CAAC,YAA4B,EAAE,IAAQ,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QACxH,IAAI,KAAa,CAAC;QAClB,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,YAAY,CAAC;SACrB;aAAM;YACN,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC;SAChC;QAED,oEAAoE;QACpE,IAAI,QAAQ,GAAuB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACtE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IAMM,YAAY,CAAC,YAA4B,EAAE,IAAQ,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QACzH,IAAI,KAAa,CAAC;QAClB,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,YAAY,CAAC;SACrB;aAAM;YACN,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC;SAChC;QAED,IAAI,QAAQ,GAAuB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,EAAE,GAAqB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACzF,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IAIM,aAAa,CAAC,KAAqB,EAAE,IAAQ;QACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACjC;aAAM;YACN,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACjC;IACF,CAAC;IAUM,OAAO,CAAC,IAAoB,EAAE,EAAkB,EAAE,IAAQ,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QAChI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC7B,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;SACvB;QAED,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC3B,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;SACnB;QAED,IAAK,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAG;YAChE,MAAM,IAAI,UAAU,CAAC,2BAA2B,IAAI,KAAK,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;SACzF;QAED,IAAI,QAAQ,GAAuB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,EAAE,GAAsB,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxF,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IAcM,MAAM,CAAC,IAAoB,EAAE,EAAmB,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QACtH,IAAI,EAAE,KAAK,SAAS,EAAE;YACrB,EAAE,GAAG,IAAI,CAAC;SACV;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAY,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;SAClD;aAAM;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAW,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;SACjD;IACF,CAAC;IAMS,wBAAwB,CAAC,cAAsB,mBAAmB,CAAC,oBAAoB;QAChG,IAAI,CAAC,GAAuB,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC1E,IAAK,CAAC,IAAI,IAAI,EAAG;YAChB,OAAO,CAAC,CAAC,CAAC;SACV;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAES,wBAAwB,CAAC,WAAmB,EAAE,CAAS;QAChE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAES,UAAU,CAAC,IAAY;QAChC,IAAI,EAAE,GAAmC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACjE,IAAK,EAAE,IAAI,IAAI,EAAG;YACjB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SAClC;QAED,OAAO,EAAE,CAAC;IACX,CAAC;IAEO,iBAAiB,CAAC,IAAY;QACrC,IAAI,EAAE,GAAuB,EAAE,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,EAAE,CAAC;IACX,CAAC;IA2BM,OAAO,CAAC,iBAAqC,EAAE,cAAsB,mBAAmB,CAAC,oBAAoB;QACnH,IAAI,QAAkB,CAAC;QACvB,IAAI,iBAAiB,YAAY,mBAAQ,EAAE;YAC1C,QAAQ,GAAG,iBAAiB,CAAC;SAC7B;aAAM;YACN,QAAQ,GAAG,mBAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;YAC1C,WAAW,GAAG,iBAAiB,CAAC;SAChC;QAED,IAAI,QAAQ,GAAmC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9E,IAAI,KAAK,GAAY,QAAQ,CAAC,CAAC,CAAC;QAChC,IAAI,IAAI,GAAY,QAAQ,CAAC,CAAC,CAAC;QAE/B,gCAAgC;QAChC,IAAK,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,EAAG;YAClC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;SAC5B;QACD,IAAK,KAAK,GAAG,CAAC,EAAG;YAChB,KAAK,GAAG,CAAC,CAAC;SACV;QAED,IAAK,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAG;YAChD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,6BAA6B;SACnE;QAED,IAAI,GAAG,GAAa,EAAE,CAAC;QAEvB,qCAAqC;QACrC,IAAI,SAAS,GAAkC,IAAI,CAAC,+BAA+B,CAAC,QAAQ,CAAC,CAAC;QAE9F,0DAA0D;QAC1D,IAAI,CAAC,GAAY,KAAK,CAAC;QACvB,OAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAG;YAC3C,IAAI,EAAE,GAAkC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACzD,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;YAC5D,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClC,IAAK,EAAE,IAAI,IAAI,EAAG;gBACjB,8CAA8C;gBAC9C,IAAK,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAG;oBAC3B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBACzB;gBACD,CAAC,EAAE,CAAC,CAAC,qBAAqB;aAC1B;iBACI;gBACJ,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,6BAA6B;aAClD;SACD;QAED,uDAAuD;QACvD,iEAAiE;QACjE,8BAA8B;QAC9B,IAAK,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,EAAG;YACpC,iDAAiD;YACjD,6CAA6C;YAC7C,KAAK,IAAI,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE;gBAClC,IAAK,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,EAAG;oBACvC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAC7B;aACD;SACD;QAED,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACO,+BAA+B,CAAC,QAA6C;QACtF,2DAA2D;QAE3D,gBAAgB;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,EAAE,GAAiC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,IAAK,EAAE,IAAI,IAAI,EAAG;gBACjB,SAAS;aACT;YACD,IAAK,CAAC,CAAC,EAAE,YAAY,SAAS,CAAC,EAAG;gBACjC,SAAS;aACT;YACD,IAAI,GAAG,GAAc,EAAE,CAAC;YACxB,kCAAkC;YAClC,IAAI,OAAO,GAAqB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;YAC/E,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE;gBACxB,IAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,EAAG;oBAC9B,qDAAqD;oBACrD,6CAA6C;oBAC7C,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;oBAC3C,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC/E;qBACI,IAAK,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,SAAS,EAAG;oBAC/D,iCAAiC;oBACjC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;iBAC3C;aACD;YACD,2CAA2C;YAC3C,IAAI,YAAY,GAAgB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAC1E,KAAK,IAAI,OAAO,IAAI,YAAY,EAAE;gBACjC,IAAK,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,EAAG;oBACvE,kCAAkC;oBAClC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;oBAC/C,SAAS;iBACT;gBACD,+CAA+C;gBAC/C,IAAI,QAAQ,GACX,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC;gBAChE,+CAA+C;gBAC/C,uEAAuE;gBACvE,IAAK,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAG;oBAC5D,0DAA0D;oBAC1D,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,CAAC,oBAAoB;oBACpE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC/C,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;oBAC3D,iCAAiC;iBACjC;qBACI,IAAK,CAAC,QAAQ,EAAG;oBACrB,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,0BAA0B,OAAO,EAAE,CAAC,CAAC;iBACpF;aACD;SACD;QAED,eAAe;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,EAAE,GAAiC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,IAAK,EAAE,IAAI,IAAI,EAAG;gBACjB,SAAS;aACT;YACD,IAAK,CAAC,CAAC,EAAE,YAAY,cAAc,CAAC,EAAG;gBACtC,SAAS;aACT;YACD,IAAI,GAAG,GAAoB,EAAE,CAAC;YAC9B,yDAAyD;YACzD,IAAI,WAAW,GAAqB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;YACnF,KAAK,IAAI,OAAO,IAAI,WAAW,EAAE;gBAChC,IAAK,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,EAAG;oBAClC,IAAI,OAAO,YAAY,aAAa,EAAE;wBACrC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;wBAClD,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;qBAC/C;yBACI,IAAI,OAAO,YAAY,cAAc,EAAE,EAAE,kBAAkB;wBAC/D,wDAAwD;wBACxD,8DAA8D;wBAC9D,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;wBAClD,gCAAgC;wBAChC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;qBAC/C;iBACD;aACD;YACD,uDAAuD;YACvD,IAAI,YAAY,GAAgB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAC1E,KAAK,IAAI,GAAG,IAAI,YAAY,EAAE;gBAC7B,IAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,EAAG;oBAC9B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9C,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,wBAAwB;oBACjD,SAAS;iBACT;gBACD,IAAK,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,SAAS,EAAG;oBAC3D,MAAM,IAAI,KAAK,CAAC,aAAa,GAAG,kCAAkC,GAAG,EAAE,CAAC,CAAC;iBACzE;aACD;SACD;QACD,iEAAiE;QACjE,IAAI,CAAC,GAAmC,IAAI,GAAG,EAA4B,CAAC;QAC5E,KAAK,IAAI,EAAE,IAAI,QAAQ,EAAE;YACxB,IAAK,EAAE,IAAI,IAAI,EAAG;gBACjB,qBAAqB;gBACrB,SAAS;aACT;YACD,IAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,EAAG;gBAC9B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACnD;YACD,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SACpB;QACD,oCAAoC;QACpC,OAAO,CAAC,CAAC;IACV,CAAC;IAES,SAAS,CAAC,CAAK,EAAE,CAAK;QAC/B,IAAI,CAAC,GAAY,EAAE,CAAC;QACpB,IAAI,CAAC,GAAY,EAAE,CAAC;QACpB,IAAK,CAAC,IAAI,IAAI,EAAG;YAChB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;SACjB;QACD,IAAK,CAAC,IAAI,IAAI,EAAG;YAChB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;SACjB;QACD,OAAO,CAAC,GAAG,CAAC,CAAC;IACd,CAAC;IAED,8DAA8D;IACpD,YAAY,CAA6B,QAA6C,EAAE,IAA8B,EAAE,MAAc;QAC/I,IAAI,GAAG,GAAQ,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvD,IAAI,EAAE,GAAkC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACpD,IAAK,EAAE,IAAI,IAAI,EAAG;gBACjB,iBAAiB;gBACjB,SAAS;aACT;YACD,IAAK,EAAE,YAAY,IAAI,EAAG;gBACzB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACb;SACD;QACD,OAAO,GAAG,CAAC;IACZ,CAAC;;AApcF,kDAqcC;AApcuB,wCAAoB,GAAY,SAAS,CAAC;AAC1C,qCAAiB,GAAY,GAAG,CAAC;AACjC,mCAAe,GAAY,CAAC,CAAC;AAocrD,yCAAyC;AAEzC,MAAa,gBAAgB;IAU5B,YAAY,MAAmB,EAAE,KAAa,EAAE,gBAAwB,EAAE,IAAS;QAClF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,GAAa;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAGM,QAAQ;QACd,IAAI,MAAM,GAAW,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAC3C,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACrD,OAAO,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;YACrD,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IAC7B,CAAC;CACD;AAPA;IADC,qBAAQ;gDAOR;AA/BF,4CAgCC;AAED,MAAM,cAAe,SAAQ,gBAAgB;IAC5C,YAAY,MAAmB,EAAE,KAAa,EAAE,gBAAwB,EAAE,IAAQ;QACjF,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAGM,OAAO,CAAC,GAAa;QAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/B,IAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAG;YACrD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;QACD,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACvB,CAAC;CACD;AAPA;IADC,qBAAQ;6CAOR;AAGF;;;GAGG;AACH,MAAM,aAAc,SAAQ,cAAc;IACzC,YAAY,MAAmB,EAAE,KAAa,EAAE,gBAAwB,EAAE,IAAQ;QACjF,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC,wCAAwC;IAC3F,CAAC;CACD;AAED;;GAEG;AACH,MAAM,SAAU,SAAQ,gBAAgB;IAEvC,YAAY,MAAmB,EAAE,IAAY,EAAE,EAAU,EAAE,gBAAwB,EAAE,IAAQ;QAC5F,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACrB,CAAC;IAGM,OAAO,CAAC,GAAa;QAC3B,IAAK,IAAI,CAAC,IAAI,IAAI,IAAI,EAAG;YACxB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IAC3B,CAAC;IAGM,QAAQ;QACd,IAAK,IAAI,CAAC,IAAI,IAAI,IAAI,EAAG;YACxB,OAAO,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC/C,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;SAC/C;QACD,OAAO,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACtE,CAAC;CACD;AAhBA;IADC,qBAAQ;wCAMR;AAGD;IADC,qBAAQ;yCAQR", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:58.1768850-07:00\r\n\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Override } from \"./Decorators\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenStream } from \"./TokenStream\";\r\n\r\nimport * as Utils from \"./misc/Utils\";\r\n\r\n/**\r\n * Useful for rewriting out a buffered input token stream after doing some\r\n * augmentation or other manipulations on it.\r\n *\r\n * You can insert stuff, replace, and delete chunks. Note that the operations\r\n * are done lazily--only if you convert the buffer to a {@link String} with\r\n * {@link TokenStream#getText()}. This is very efficient because you are not\r\n * moving data around all the time. As the buffer of tokens is converted to\r\n * strings, the {@link #getText()} method(s) scan the input token stream and\r\n * check to see if there is an operation at the current index. If so, the\r\n * operation is done and then normal {@link String} rendering continues on the\r\n * buffer. This is like having multiple Turing machine instruction streams\r\n * (programs) operating on a single input tape. :)\r\n *\r\n * This rewriter makes no modifications to the token stream. It does not ask the\r\n * stream to fill itself up nor does it advance the input cursor. The token\r\n * stream `TokenStream.index` will return the same value before and\r\n * after any {@link #getText()} call.\r\n *\r\n * The rewriter only works on tokens that you have in the buffer and ignores the\r\n * current input cursor. If you are buffering tokens on-demand, calling\r\n * {@link #getText()} halfway through the input will only do rewrites for those\r\n * tokens in the first half of the file.\r\n *\r\n * Since the operations are done lazily at {@link #getText}-time, operations do\r\n * not screw up the token index values. That is, an insert operation at token\r\n * index `i` does not change the index values for tokens\r\n * `i`+1..n-1.\r\n *\r\n * Because operations never actually alter the buffer, you may always get the\r\n * original token stream back without undoing anything. Since the instructions\r\n * are queued up, you can easily simulate transactions and roll back any changes\r\n * if there is an error just by removing instructions. For example,\r\n *\r\n * ```\r\n * CharStream input = new ANTLRFileStream(\"input\");\r\n * TLexer lex = new TLexer(input);\r\n * CommonTokenStream tokens = new CommonTokenStream(lex);\r\n * T parser = new T(tokens);\r\n * TokenStreamRewriter rewriter = new TokenStreamRewriter(tokens);\r\n * parser.startRule();\r\n * ```\r\n *\r\n * Then in the rules, you can execute (assuming rewriter is visible):\r\n *\r\n * ```\r\n * Token t,u;\r\n * ...\r\n * rewriter.insertAfter(t, \"text to put after t\");}\r\n * rewriter.insertAfter(u, \"text after u\");}\r\n * System.out.println(rewriter.getText());\r\n * ```\r\n *\r\n * You can also have multiple \"instruction streams\" and get multiple rewrites\r\n * from a single pass over the input. Just name the instruction streams and use\r\n * that name again when printing the buffer. This could be useful for generating\r\n * a C file and also its header file--all from the same buffer:\r\n *\r\n * ```\r\n * rewriter.insertAfter(\"pass1\", t, \"text to put after t\");}\r\n * rewriter.insertAfter(\"pass2\", u, \"text after u\");}\r\n * System.out.println(rewriter.getText(\"pass1\"));\r\n * System.out.println(rewriter.getText(\"pass2\"));\r\n * ```\r\n *\r\n * If you don't use named rewrite streams, a \"default\" stream is used as the\r\n * first example shows.\r\n */\r\nexport class TokenStreamRewriter {\r\n\tpublic static readonly DEFAULT_PROGRAM_NAME: string =  \"default\";\r\n\tpublic static readonly PROGRAM_INIT_SIZE: number =  100;\r\n\tpublic static readonly MIN_TOKEN_INDEX: number =  0;\r\n\r\n\t/** Our source stream */\r\n\tprotected tokens: TokenStream;\r\n\r\n\t/** You may have multiple, named streams of rewrite operations.\r\n\t *  I'm calling these things \"programs.\"\r\n\t *  Maps String (name) &rarr; rewrite (List)\r\n\t */\r\n\tprotected programs: Map<string, RewriteOperation[]>;\r\n\r\n\t/** Map String (program name) &rarr; Integer index */\r\n\tprotected lastRewriteTokenIndexes: Map<string, number>;\r\n\r\n\tconstructor(tokens: TokenStream)  {\r\n\t\tthis.tokens = tokens;\r\n\t\tthis.programs = new Map<string, RewriteOperation[]>();\r\n\t\tthis.programs.set(TokenStreamRewriter.DEFAULT_PROGRAM_NAME, []);\r\n\t\tthis.lastRewriteTokenIndexes = new Map<string, number>();\r\n\t}\r\n\r\n\tpublic getTokenStream(): TokenStream {\r\n\t\treturn this.tokens;\r\n\t}\r\n\r\n\tpublic rollback(instructionIndex: number): void;\r\n\t/** Rollback the instruction stream for a program so that\r\n\t *  the indicated instruction (via instructionIndex) is no\r\n\t *  longer in the stream. UNTESTED!\r\n\t */\r\n\tpublic rollback(instructionIndex: number, programName: string): void;\r\n\tpublic rollback(instructionIndex: number, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tlet is: RewriteOperation[] | undefined =  this.programs.get(programName);\r\n\t\tif ( is != null ) {\r\n\t\t\tthis.programs.set(programName, is.slice(TokenStreamRewriter.MIN_TOKEN_INDEX, instructionIndex));\r\n\t\t}\r\n\t}\r\n\r\n\tpublic deleteProgram(): void;\r\n\r\n\t/** Reset the program so that no instructions exist */\r\n\tpublic deleteProgram(programName: string): void;\r\n\tpublic deleteProgram(programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tthis.rollback(TokenStreamRewriter.MIN_TOKEN_INDEX, programName);\r\n\t}\r\n\r\n\tpublic insertAfter(t: Token, text: {}): void;\r\n\tpublic insertAfter(index: number, text: {}): void;\r\n\tpublic insertAfter(t: Token, text: {}, programName: string): void;\r\n\tpublic insertAfter(index: number, text: {}, programName: string): void;\r\n\tpublic insertAfter(tokenOrIndex: Token | number, text: {}, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tlet index: number;\r\n\t\tif (typeof tokenOrIndex === \"number\") {\r\n\t\t\tindex = tokenOrIndex;\r\n\t\t} else {\r\n\t\t\tindex = tokenOrIndex.tokenIndex;\r\n\t\t}\r\n\r\n\t\t// to insert after, just insert before next index (even if past end)\r\n\t\tlet rewrites: RewriteOperation[] = this.getProgram(programName);\r\n\t\tlet op = new InsertAfterOp(this.tokens, index, rewrites.length, text);\r\n\t\trewrites.push(op);\r\n\t}\r\n\r\n\tpublic insertBefore(t: Token, text: {}): void;\r\n\tpublic insertBefore(index: number, text: {}): void;\r\n\tpublic insertBefore(t: Token, text: {}, programName: string): void;\r\n\tpublic insertBefore(index: number, text: {}, programName: string): void;\r\n\tpublic insertBefore(tokenOrIndex: Token | number, text: {}, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tlet index: number;\r\n\t\tif (typeof tokenOrIndex === \"number\") {\r\n\t\t\tindex = tokenOrIndex;\r\n\t\t} else {\r\n\t\t\tindex = tokenOrIndex.tokenIndex;\r\n\t\t}\r\n\r\n\t\tlet rewrites: RewriteOperation[] = this.getProgram(programName);\r\n\t\tlet op: RewriteOperation = new InsertBeforeOp(this.tokens, index, rewrites.length, text);\r\n\t\trewrites.push(op);\r\n\t}\r\n\r\n\tpublic replaceSingle(index: number, text: {}): void;\r\n\tpublic replaceSingle(indexT: Token, text: {}): void;\r\n\tpublic replaceSingle(index: Token | number, text: {}): void {\r\n\t\tif (typeof index === \"number\") {\r\n\t\t\tthis.replace(index, index, text);\r\n\t\t} else {\r\n\t\t\tthis.replace(index, index, text);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic replace(from: number, to: number, text: {}): void;\r\n\r\n\tpublic replace(from: Token, to: Token, text: {}): void;\r\n\r\n\tpublic replace(from: number, to: number, text: {}, programName: string): void;\r\n\r\n\tpublic replace(from: Token, to: Token, text: {}, programName: string): void;\r\n\r\n\tpublic replace(from: Token | number, to: Token | number, text: {}, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tif (typeof from !== \"number\") {\r\n\t\t\tfrom = from.tokenIndex;\r\n\t\t}\r\n\r\n\t\tif (typeof to !== \"number\") {\r\n\t\t\tto = to.tokenIndex;\r\n\t\t}\r\n\r\n\t\tif ( from > to || from < 0 || to < 0 || to >= this.tokens.size ) {\r\n\t\t\tthrow new RangeError(`replace: range invalid: ${from}..${to}(size=${this.tokens.size})`);\r\n\t\t}\r\n\r\n\t\tlet rewrites: RewriteOperation[] = this.getProgram(programName);\r\n\t\tlet op: RewriteOperation =  new ReplaceOp(this.tokens, from, to, rewrites.length, text);\r\n\t\trewrites.push(op);\r\n\t}\r\n\r\n\tpublic delete(index: number): void;\r\n\r\n\tpublic delete(from: number, to: number): void;\r\n\r\n\tpublic delete(indexT: Token): void;\r\n\r\n\tpublic delete(from: Token, to: Token): void;\r\n\r\n\tpublic delete(from: number, to: number, programName: string): void;\r\n\r\n\tpublic delete(from: Token, to: Token, programName: string): void;\r\n\r\n\tpublic delete(from: Token | number, to?: Token | number, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): void {\r\n\t\tif (to === undefined) {\r\n\t\t\tto = from;\r\n\t\t}\r\n\r\n\t\tif (typeof from === \"number\") {\r\n\t\t\tthis.replace(from, to as number, \"\", programName);\r\n\t\t} else {\r\n\t\t\tthis.replace(from, to as Token, \"\", programName);\r\n\t\t}\r\n\t}\r\n\r\n\tprotected getLastRewriteTokenIndex(): number;\r\n\r\n\tprotected getLastRewriteTokenIndex(programName: string): number;\r\n\r\n\tprotected getLastRewriteTokenIndex(programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): number {\r\n\t\tlet I: number | undefined = this.lastRewriteTokenIndexes.get(programName);\r\n\t\tif ( I == null ) {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\r\n\t\treturn I;\r\n\t}\r\n\r\n\tprotected setLastRewriteTokenIndex(programName: string, i: number): void {\r\n\t\tthis.lastRewriteTokenIndexes.set(programName, i);\r\n\t}\r\n\r\n\tprotected getProgram(name: string): RewriteOperation[] {\r\n\t\tlet is: RewriteOperation[] | undefined = this.programs.get(name);\r\n\t\tif ( is == null ) {\r\n\t\t\tis = this.initializeProgram(name);\r\n\t\t}\r\n\r\n\t\treturn is;\r\n\t}\r\n\r\n\tprivate initializeProgram(name: string): RewriteOperation[] {\r\n\t\tlet is: RewriteOperation[] = [];\r\n\t\tthis.programs.set(name, is);\r\n\t\treturn is;\r\n\t}\r\n\r\n\t/** Return the text from the original tokens altered per the\r\n\t *  instructions given to this rewriter.\r\n\t */\r\n\tpublic getText(): string;\r\n\r\n\t/** Return the text from the original tokens altered per the\r\n\t *  instructions given to this rewriter in programName.\r\n\t *\r\n\t * @since 4.5\r\n\t */\r\n\tpublic getText(programName: string): string;\r\n\r\n\t/** Return the text associated with the tokens in the interval from the\r\n\t *  original token stream but with the alterations given to this rewriter.\r\n\t *  The interval refers to the indexes in the original token stream.\r\n\t *  We do not alter the token stream in any way, so the indexes\r\n\t *  and intervals are still consistent. Includes any operations done\r\n\t *  to the first and last token in the interval. So, if you did an\r\n\t *  insertBefore on the first token, you would get that insertion.\r\n\t *  The same is true if you do an insertAfter the stop token.\r\n\t */\r\n\tpublic getText(interval: Interval): string;\r\n\r\n\tpublic getText(interval: Interval, programName: string): string;\r\n\r\n\tpublic getText(intervalOrProgram?: Interval | string, programName: string = TokenStreamRewriter.DEFAULT_PROGRAM_NAME): string {\r\n\t\tlet interval: Interval;\r\n\t\tif (intervalOrProgram instanceof Interval) {\r\n\t\t\tinterval = intervalOrProgram;\r\n\t\t} else {\r\n\t\t\tinterval = Interval.of(0, this.tokens.size - 1);\r\n\t\t}\r\n\r\n\t\tif (typeof intervalOrProgram === \"string\") {\r\n\t\t\tprogramName = intervalOrProgram;\r\n\t\t}\r\n\r\n\t\tlet rewrites: RewriteOperation[] | undefined = this.programs.get(programName);\r\n\t\tlet start: number =  interval.a;\r\n\t\tlet stop: number =  interval.b;\r\n\r\n\t\t// ensure start/end are in range\r\n\t\tif ( stop > this.tokens.size - 1 ) {\r\n\t\t\tstop = this.tokens.size - 1;\r\n\t\t}\r\n\t\tif ( start < 0 ) {\r\n\t\t\tstart = 0;\r\n\t\t}\r\n\r\n\t\tif ( rewrites == null || rewrites.length === 0 ) {\r\n\t\t\treturn this.tokens.getText(interval); // no instructions to execute\r\n\t\t}\r\n\r\n\t\tlet buf: string[] = [];\r\n\r\n\t\t// First, optimize instruction stream\r\n\t\tlet indexToOp: Map<number, RewriteOperation> = this.reduceToSingleOperationPerIndex(rewrites);\r\n\r\n\t\t// Walk buffer, executing instructions and emitting tokens\r\n\t\tlet i: number =  start;\r\n\t\twhile ( i <= stop && i < this.tokens.size ) {\r\n\t\t\tlet op: RewriteOperation | undefined =  indexToOp.get(i);\r\n\t\t\tindexToOp.delete(i); // remove so any left have index size-1\r\n\t\t\tlet t: Token = this.tokens.get(i);\r\n\t\t\tif ( op == null ) {\r\n\t\t\t\t// no operation at that index, just dump token\r\n\t\t\t\tif ( t.type !== Token.EOF ) {\r\n\t\t\t\t\tbuf.push(String(t.text));\r\n\t\t\t\t}\r\n\t\t\t\ti++; // move to next token\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\ti = op.execute(buf); // execute operation and skip\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// include stuff after end if it's last index in buffer\r\n\t\t// So, if they did an insertAfter(lastValidIndex, \"foo\"), include\r\n\t\t// foo if end==lastValidIndex.\r\n\t\tif ( stop === this.tokens.size - 1 ) {\r\n\t\t\t// Scan any remaining operations after last token\r\n\t\t\t// should be included (they will be inserts).\r\n\t\t\tfor (let op of indexToOp.values()) {\r\n\t\t\t\tif ( op.index >= this.tokens.size - 1 ) {\r\n\t\t\t\t\tbuf.push(op.text.toString());\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn buf.join(\"\");\r\n\t}\r\n\r\n\t/** We need to combine operations and report invalid operations (like\r\n\t *  overlapping replaces that are not completed nested). Inserts to\r\n\t *  same index need to be combined etc...  Here are the cases:\r\n\t *\r\n\t *  I.i.u I.j.v\t\t\t\t\t\t\t\tleave alone, nonoverlapping\r\n\t *  I.i.u I.i.v\t\t\t\t\t\t\t\tcombine: Iivu\r\n\t *\r\n\t *  R.i-j.u R.x-y.v\t| i-j in x-y\t\t\tdelete first R\r\n\t *  R.i-j.u R.i-j.v\t\t\t\t\t\t\tdelete first R\r\n\t *  R.i-j.u R.x-y.v\t| x-y in i-j\t\t\tERROR\r\n\t *  R.i-j.u R.x-y.v\t| boundaries overlap\tERROR\r\n\t *\r\n\t *  Delete special case of replace (text==undefined):\r\n\t *  D.i-j.u D.x-y.v\t| boundaries overlap\tcombine to max(min)..max(right)\r\n\t *\r\n\t *  I.i.u R.x-y.v | i in (x+1)-y\t\t\tdelete I (since insert before\r\n\t * \t\t\t\t\t\t\t\t\t\t\twe're not deleting i)\r\n\t *  I.i.u R.x-y.v | i not in (x+1)-y\t\tleave alone, nonoverlapping\r\n\t *  R.x-y.v I.i.u | i in x-y\t\t\t\tERROR\r\n\t *  R.x-y.v I.x.u \t\t\t\t\t\t\tR.x-y.uv (combine, delete I)\r\n\t *  R.x-y.v I.i.u | i not in x-y\t\t\tleave alone, nonoverlapping\r\n\t *\r\n\t *  I.i.u = insert u before op @ index i\r\n\t *  R.x-y.u = replace x-y indexed tokens with u\r\n\t *\r\n\t *  First we need to examine replaces. For any replace op:\r\n\t *\r\n\t * \t\t1. wipe out any insertions before op within that range.\r\n\t * \t\t2. Drop any replace op before that is contained completely within\r\n\t * \t that range.\r\n\t * \t\t3. Throw exception upon boundary overlap with any previous replace.\r\n\t *\r\n\t *  Then we can deal with inserts:\r\n\t *\r\n\t * \t\t1. for any inserts to same index, combine even if not adjacent.\r\n\t * \t\t2. for any prior replace with same left boundary, combine this\r\n\t * \t insert with replace and delete this replace.\r\n\t * \t\t3. throw exception if index in same range as previous replace\r\n\t *\r\n\t *  Don't actually delete; make op undefined in list. Easier to walk list.\r\n\t *  Later we can throw as we add to index &rarr; op map.\r\n\t *\r\n\t *  Note that I.2 R.2-2 will wipe out I.2 even though, technically, the\r\n\t *  inserted stuff would be before the replace range. But, if you\r\n\t *  add tokens in front of a method body '{' and then delete the method\r\n\t *  body, I think the stuff before the '{' you added should disappear too.\r\n\t *\r\n\t *  Return a map from token index to operation.\r\n\t */\r\n\tprotected reduceToSingleOperationPerIndex(rewrites: Array<RewriteOperation | undefined>): Map<number, RewriteOperation> {\r\n\t\t// console.log(`rewrites=[${Utils.join(rewrites, \", \")}]`);\r\n\r\n\t\t// WALK REPLACES\r\n\t\tfor (let i = 0; i < rewrites.length; i++) {\r\n\t\t\tlet op: RewriteOperation | undefined = rewrites[i];\r\n\t\t\tif ( op == null ) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tif ( !(op instanceof ReplaceOp) ) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tlet rop: ReplaceOp = op;\r\n\t\t\t// Wipe prior inserts within range\r\n\t\t\tlet inserts: InsertBeforeOp[] = this.getKindOfOps(rewrites, InsertBeforeOp, i);\r\n\t\t\tfor (let iop of inserts) {\r\n\t\t\t\tif ( iop.index === rop.index ) {\r\n\t\t\t\t\t// E.g., insert before 2, delete 2..2; update replace\r\n\t\t\t\t\t// text to include insert before, kill insert\r\n\t\t\t\t\trewrites[iop.instructionIndex] = undefined;\r\n\t\t\t\t\trop.text = iop.text.toString() + (rop.text != null ? rop.text.toString() : \"\");\r\n\t\t\t\t}\r\n\t\t\t\telse if ( iop.index > rop.index && iop.index <= rop.lastIndex ) {\r\n\t\t\t\t\t// delete insert as it's a no-op.\r\n\t\t\t\t\trewrites[iop.instructionIndex] = undefined;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Drop any prior replaces contained within\r\n\t\t\tlet prevReplaces: ReplaceOp[] = this.getKindOfOps(rewrites, ReplaceOp, i);\r\n\t\t\tfor (let prevRop of prevReplaces) {\r\n\t\t\t\tif ( prevRop.index >= rop.index && prevRop.lastIndex <= rop.lastIndex ) {\r\n\t\t\t\t\t// delete replace as it's a no-op.\r\n\t\t\t\t\trewrites[prevRop.instructionIndex] = undefined;\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\t// throw exception unless disjoint or identical\r\n\t\t\t\tlet disjoint: boolean =\r\n\t\t\t\t\tprevRop.lastIndex < rop.index || prevRop.index > rop.lastIndex;\r\n\t\t\t\t// Delete special case of replace (text==null):\r\n\t\t\t\t// D.i-j.u D.x-y.v\t| boundaries overlap\tcombine to max(min)..max(right)\r\n\t\t\t\tif ( prevRop.text == null && rop.text == null && !disjoint ) {\r\n\t\t\t\t\t// console.log(`overlapping deletes: ${prevRop}, ${rop}`);\r\n\t\t\t\t\trewrites[prevRop.instructionIndex] = undefined; // kill first delete\r\n\t\t\t\t\trop.index = Math.min(prevRop.index, rop.index);\r\n\t\t\t\t\trop.lastIndex = Math.max(prevRop.lastIndex, rop.lastIndex);\r\n\t\t\t\t\t// console.log(`new rop ${rop}`);\r\n\t\t\t\t}\r\n\t\t\t\telse if ( !disjoint ) {\r\n\t\t\t\t\tthrow new Error(`replace op boundaries of ${rop} overlap with previous ${prevRop}`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// WALK INSERTS\r\n\t\tfor (let i = 0; i < rewrites.length; i++) {\r\n\t\t\tlet op: RewriteOperation | undefined = rewrites[i];\r\n\t\t\tif ( op == null ) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tif ( !(op instanceof InsertBeforeOp) ) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tlet iop: InsertBeforeOp =  op;\r\n\t\t\t// combine current insert with prior if any at same index\r\n\t\t\tlet prevInserts: InsertBeforeOp[] = this.getKindOfOps(rewrites, InsertBeforeOp, i);\r\n\t\t\tfor (let prevIop of prevInserts) {\r\n\t\t\t\tif ( prevIop.index === iop.index ) {\r\n\t\t\t\t\tif (prevIop instanceof InsertAfterOp) {\r\n\t\t\t\t\t\tiop.text = this.catOpText(prevIop.text, iop.text);\r\n\t\t\t\t\t\trewrites[prevIop.instructionIndex] = undefined;\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse if (prevIop instanceof InsertBeforeOp) { // combine objects\r\n\t\t\t\t\t\t// convert to strings...we're in process of toString'ing\r\n\t\t\t\t\t\t// whole token buffer so no lazy eval issue with any templates\r\n\t\t\t\t\t\tiop.text = this.catOpText(iop.text, prevIop.text);\r\n\t\t\t\t\t\t// delete redundant prior insert\r\n\t\t\t\t\t\trewrites[prevIop.instructionIndex] = undefined;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// look for replaces where iop.index is in range; error\r\n\t\t\tlet prevReplaces: ReplaceOp[] = this.getKindOfOps(rewrites, ReplaceOp, i);\r\n\t\t\tfor (let rop of prevReplaces) {\r\n\t\t\t\tif ( iop.index === rop.index ) {\r\n\t\t\t\t\trop.text = this.catOpText(iop.text, rop.text);\r\n\t\t\t\t\trewrites[i] = undefined;\t// delete current insert\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\tif ( iop.index >= rop.index && iop.index <= rop.lastIndex ) {\r\n\t\t\t\t\tthrow new Error(`insert op ${iop} within boundaries of previous ${rop}`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t// console.log(`rewrites after=[${Utils.join(rewrites, \", \")}]`);\r\n\t\tlet m: Map<number, RewriteOperation> =  new Map<number, RewriteOperation>();\r\n\t\tfor (let op of rewrites) {\r\n\t\t\tif ( op == null ) {\r\n\t\t\t\t// ignore deleted ops\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tif ( m.get(op.index) != null ) {\r\n\t\t\t\tthrow new Error(\"should only be one op per index\");\r\n\t\t\t}\r\n\t\t\tm.set(op.index, op);\r\n\t\t}\r\n\t\t// console.log(`index to op: ${m}`);\r\n\t\treturn m;\r\n\t}\r\n\r\n\tprotected catOpText(a: {}, b: {}): string {\r\n\t\tlet x: string =  \"\";\r\n\t\tlet y: string =  \"\";\r\n\t\tif ( a != null ) {\r\n\t\t\tx = a.toString();\r\n\t\t}\r\n\t\tif ( b != null ) {\r\n\t\t\ty = b.toString();\r\n\t\t}\r\n\t\treturn x + y;\r\n\t}\r\n\r\n\t/** Get all operations before an index of a particular kind */\r\n\tprotected getKindOfOps<T extends RewriteOperation>(rewrites: Array<RewriteOperation | undefined>, kind: {new(...args: any[]): T}, before: number): T[] {\r\n\t\tlet ops: T[] = [];\r\n\t\tfor (let i = 0; i < before && i < rewrites.length; i++) {\r\n\t\t\tlet op: RewriteOperation | undefined =  rewrites[i];\r\n\t\t\tif ( op == null ) {\r\n\t\t\t\t// ignore deleted\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tif ( op instanceof kind ) {\r\n\t\t\t\tops.push(op);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn ops;\r\n\t}\r\n}\r\n\r\n// Define the rewrite operation hierarchy\r\n\r\nexport class RewriteOperation {\r\n\tprotected readonly tokens: TokenStream;\r\n\t/** What index into rewrites List are we? */\r\n\tpublic readonly instructionIndex: number;\r\n\t/** Token buffer index. */\r\n\tpublic index: number;\r\n\tpublic text: {};\r\n\r\n\tconstructor(tokens: TokenStream, index: number, instructionIndex: number);\r\n\tconstructor(tokens: TokenStream, index: number, instructionIndex: number, text: {});\r\n\tconstructor(tokens: TokenStream, index: number, instructionIndex: number, text?: {}) {\r\n\t\tthis.tokens = tokens;\r\n\t\tthis.instructionIndex = instructionIndex;\r\n\t\tthis.index = index;\r\n\t\tthis.text = text === undefined ? \"\" : text;\r\n\t}\r\n\r\n\t/** Execute the rewrite operation by possibly adding to the buffer.\r\n\t *  Return the index of the next token to operate on.\r\n\t */\r\n\tpublic execute(buf: string[]): number {\r\n\t\treturn this.index;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tlet opName: string = this.constructor.name;\r\n\t\tlet $index = opName.indexOf(\"$\");\r\n\t\topName = opName.substring($index + 1, opName.length);\r\n\t\treturn \"<\" + opName + \"@\" + this.tokens.get(this.index) +\r\n\t\t\t\t\":\\\"\" + this.text + \"\\\">\";\r\n\t}\r\n}\r\n\r\nclass InsertBeforeOp extends RewriteOperation {\r\n\tconstructor(tokens: TokenStream, index: number, instructionIndex: number, text: {}) {\r\n\t\tsuper(tokens, index, instructionIndex, text);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic execute(buf: string[]): number {\r\n\t\tbuf.push(this.text.toString());\r\n\t\tif ( this.tokens.get(this.index).type !== Token.EOF ) {\r\n\t\t\tbuf.push(String(this.tokens.get(this.index).text));\r\n\t\t}\r\n\t\treturn this.index + 1;\r\n\t}\r\n}\r\n\r\n/** Distinguish between insert after/before to do the \"insert afters\"\r\n *  first and then the \"insert befores\" at same index. Implementation\r\n *  of \"insert after\" is \"insert before index+1\".\r\n */\r\nclass InsertAfterOp extends InsertBeforeOp {\r\n\tconstructor(tokens: TokenStream, index: number, instructionIndex: number, text: {}) {\r\n\t\tsuper(tokens, index + 1, instructionIndex, text); // insert after is insert before index+1\r\n\t}\r\n}\r\n\r\n/** I'm going to try replacing range from x..y with (y-x)+1 ReplaceOp\r\n *  instructions.\r\n */\r\nclass ReplaceOp extends RewriteOperation {\r\n\tpublic lastIndex: number;\r\n\tconstructor(tokens: TokenStream, from: number, to: number, instructionIndex: number, text: {}) {\r\n\t\tsuper(tokens, from, instructionIndex, text);\r\n\t\tthis.lastIndex = to;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic execute(buf: string[]): number {\r\n\t\tif ( this.text != null ) {\r\n\t\t\tbuf.push(this.text.toString());\r\n\t\t}\r\n\t\treturn this.lastIndex + 1;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tif ( this.text == null ) {\r\n\t\t\treturn \"<DeleteOp@\" + this.tokens.get(this.index) +\r\n\t\t\t\t\t\"..\" + this.tokens.get(this.lastIndex) + \">\";\r\n\t\t}\r\n\t\treturn \"<ReplaceOp@\" + this.tokens.get(this.index) +\r\n\t\t\t\t\"..\" + this.tokens.get(this.lastIndex) + \":\\\"\" + this.text + \"\\\">\";\r\n\t}\r\n}\r\n"]}