{"version": 3, "file": "ParserInterpreter.js", "sourceRoot": "", "sources": ["../../src/ParserInterpreter.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,6CAA0C;AAC1C,qDAAkD;AAElD,0CAAuC;AAEvC,yEAAsE;AACtE,qEAAkE;AAClE,qEAAkE;AAClE,qDAAkD;AAClD,6CAAuC;AACvC,6CAAwC;AACxC,qCAAkC;AAClC,iEAA8D;AAI9D,iEAA8D;AAG9D,iEAA8D;AAC9D,mCAAgC;AAMhC;;;;;;;;;;;;GAYG;AACH,IAAa,iBAAiB,GAA9B,MAAa,iBAAkB,SAAQ,eAAM;IAsD5C,YACC,eAA2C,EAAW,UAAuB,EAC7E,SAAoB,EAAE,GAAS,EAAE,KAAmB;QACpD,KAAK,CAAC,eAAe,YAAY,iBAAiB,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,KAAM,CAAC,CAAC;QA7C5F;;;;;;;;;;;;WAYG;QACgB,wBAAmB,GAAuC,EAAE,CAAC;QAEhF;;WAEG;QACO,qBAAgB,GAAW,CAAC,CAAC,CAAC;QAC9B,+BAA0B,GAAW,CAAC,CAAC,CAAC;QACxC,wBAAmB,GAAW,CAAC,CAAC,CAAC;QACjC,4BAAuB,GAAY,KAAK,CAAC,CAAC,kEAAkE;QAEtH;;;WAGG;QACO,0BAAqB,GAA4B,SAAS,CAAC;QAmBpE,IAAI,eAAe,YAAY,iBAAiB,EAAE;YACjD,IAAI,GAAG,GAAsB,eAAe,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,CAAC;YAC7C,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,0BAA0B,GAAG,GAAG,CAAC,0BAA0B,CAAC;YACjE,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,IAAI,uCAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3D;aAAM;YACN,qDAAqD;YACrD,UAAU,GAAG,UAAW,CAAC;YACzB,SAAS,GAAG,SAAU,CAAC;YACvB,GAAG,GAAG,GAAI,CAAC;YAEX,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;YACxC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAE9B,yEAAyE;YACzE,IAAI,CAAC,0BAA0B,GAAG,IAAI,eAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChE,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,CAAC,KAAK,YAAY,uCAAkB,CAAC,EAAE;oBAC3C,SAAS;iBACT;gBAED,IAAI,KAAK,CAAC,sBAAsB,EAAE;oBACjC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;iBACvD;aACD;YAED,qDAAqD;YACrD,IAAI,CAAC,WAAW,GAAG,IAAI,uCAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SACrD;IACF,CAAC;IAGM,KAAK,CAAC,UAAoB;QAChC,IAAI,UAAU,KAAK,SAAS,EAAE;YAC7B,KAAK,CAAC,KAAK,EAAE,CAAC;SACd;aAAM;YACN,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;SACxB;QAED,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;IACxC,CAAC;IAGD,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAGD,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED,sCAAsC;IAC/B,KAAK,CAAC,cAAsB;QAClC,IAAI,mBAAmB,GAAmB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAErF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,mBAAQ,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;QAChH,IAAI,mBAAmB,CAAC,gBAAgB,EAAE;YACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;SAC/F;aACI;YACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;SACnF;QAED,OAAO,IAAI,EAAE;YACZ,IAAI,CAAC,GAAa,IAAI,CAAC,QAAQ,CAAC;YAChC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACrB,KAAK,2BAAY,CAAC,SAAS;oBAC1B,wBAAwB;oBACxB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACtB,IAAI,mBAAmB,CAAC,gBAAgB,EAAE;4BACzC,IAAI,MAAM,GAAsB,IAAI,CAAC,IAAI,CAAC;4BAC1C,IAAI,aAAa,GAAgC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAI,CAAC;4BAClF,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC/C,OAAO,MAAM,CAAC;yBACd;6BACI;4BACJ,IAAI,CAAC,QAAQ,EAAE,CAAC;4BAChB,OAAO,IAAI,CAAC,YAAY,CAAC;yBACzB;qBACD;oBAED,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;oBAC3B,MAAM;gBAEP;oBACC,IAAI;wBACH,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBACnB;oBACD,OAAO,CAAC,EAAE;wBACT,IAAI,CAAC,YAAY,2CAAoB,EAAE;4BACtC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;4BAChE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;4BAC3B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;4BACvC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;yBAChB;6BAAM;4BACN,MAAM,CAAC,CAAC;yBACR;qBACD;oBAED,MAAM;aACN;SACD;IACF,CAAC;IAGM,kBAAkB,CAAC,QAA2B,EAAE,KAAa,EAAE,SAAiB,EAAE,UAAkB;QAC1G,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;QACnE,KAAK,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IAClE,CAAC;IAED,IAAc,QAAQ;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAES,UAAU,CAAC,CAAW;QAC/B,IAAI,YAAY,GAAW,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,mBAAmB,GAAG,CAAC,EAAE;YAC9B,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAkB,CAAC,CAAC;SAC3D;QAED,IAAI,UAAU,GAAe,CAAC,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAC5D,QAAQ,UAAU,CAAC,iBAAiB,EAAE;YACtC;gBACC,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;oBACrD,CAAC,CAAC,UAAU,CAAC,MAAM,YAAY,2BAAY,CAAC,EAAE;oBAC9C,6DAA6D;oBAC7D,gDAAgD;oBAChD,IAAI,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClF,IAAI,QAAQ,GACX,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC5F,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,WAAW,EACnD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACtB;gBACD,MAAM;YAEP;gBACC,IAAI,CAAC,KAAK,CAAE,UAA6B,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM;YAEP,mBAA0B;YAC1B,iBAAwB;YACxB;gBACC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,aAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAE;oBAC7E,IAAI,CAAC,aAAa,EAAE,CAAC;iBACrB;gBACD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;YAEP;gBACC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;YAEP;gBACC,IAAI,cAAc,GAAmB,UAAU,CAAC,MAAwB,CAAC;gBACzE,IAAI,SAAS,GAAW,cAAc,CAAC,SAAS,CAAC;gBACjD,IAAI,MAAM,GAA2B,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;gBAC5G,IAAI,cAAc,CAAC,gBAAgB,EAAE;oBACpC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,WAAW,EAAE,SAAS,EAAG,UAA6B,CAAC,UAAU,CAAC,CAAC;iBAClH;qBACI;oBACJ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;iBACjE;gBACD,MAAM;YAEP;gBACC,IAAI,mBAAmB,GAAwB,UAAiC,CAAC;gBACjF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE;oBAC3F,MAAM,IAAI,mDAAwB,CAAC,IAAI,CAAC,CAAC;iBACzC;gBAED,MAAM;YAEP;gBACC,IAAI,gBAAgB,GAAqB,UAA8B,CAAC;gBACxE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBACjF,MAAM;YAEP;gBACC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAG,UAA4C,CAAC,UAAU,CAAC,EAAE;oBACxF,IAAI,UAAU,GAAI,UAA4C,CAAC,UAAU,CAAC;oBAC1E,MAAM,IAAI,mDAAwB,CAAC,IAAI,EAAE,kBAAkB,UAAU,GAAG,CAAC,CAAC;iBAC1E;gBACD,MAAM;YAEP;gBACC,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACO,kBAAkB,CAAC,CAAgB;QAC5C,IAAI,YAAoB,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,QAAQ,GAAW,CAAC,CAAC,QAAQ,CAAC;QAClC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjI,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACxC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACpC;aACI;YACJ,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAClF;QACD,OAAO,YAAY,CAAC;IACrB,CAAC;IAED;;OAEG;IACO,4BAA4B,CACrC,MAAqC,EACrC,mBAA2B,EAC3B,SAAiB;QACjB,OAAO,IAAI,+CAAsB,CAAC,SAAS,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;IAC3E,CAAC;IAES,kBAAkB,CAAC,CAAW;QACvC,IAAI,cAAc,GAAmB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC7E,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACpC,IAAI,aAAa,GAAgC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAG,CAAC;YACjF,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;SAC9B;aACI;YACJ,IAAI,CAAC,QAAQ,EAAE,CAAC;SAChB;QAED,IAAI,cAAc,GAAmB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;QAClG,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACI,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,SAAiB;QACjF,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,0BAA0B,GAAG,UAAU,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;IACtC,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACO,OAAO,CAAC,CAAuB;QACxC,IAAI,CAAC,GAAW,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;YAC5B,8CAA8C;YAC9C,IAAI,GAAG,GAAsB,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACnD,IAAI,CAAC,GAAG,EAAE;gBACT,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;aACjE;YAED,IAAI,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;YAC7B,IAAI,MAAM,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;YACnE,IAAI,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YAEpC,IAAI,CAAC,YAAY,+CAAsB,EAAE;gBACxC,IAAI,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;gBACtC,IAAI,cAAc,KAAK,SAAS,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;iBACrE;gBAED,IAAI,iBAAiB,GAAW,aAAK,CAAC,YAAY,CAAC;gBACnD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;oBAC1B,kBAAkB;oBAClB,iBAAiB,GAAG,cAAc,CAAC,UAAU,CAAC;iBAC9C;gBAED,IAAI,QAAQ,GACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,EAClC,iBAAiB,EAAE,GAAG,CAAC,IAAI,EAC3B,aAAK,CAAC,eAAe,EACrB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,qBAAqB;gBAC7B,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;aAClE;iBACI,EAAE,cAAc;gBACpB,IAAI,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;gBAC7B,IAAI,QAAQ,GACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,EAClC,aAAK,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,EAC5B,aAAK,CAAC,eAAe,EACrB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,qBAAqB;gBAC7B,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;aAClE;SACD;IACF,CAAC;IAES,aAAa;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;CACD,CAAA;AAhaA;IADC,oBAAO;sDACwB;AAqFhC;IADC,qBAAQ;8CAUR;AAGD;IADC,qBAAQ;4CAGR;AAGD;IADC,qBAAQ;mDAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;wDAGR;AAwDD;IADC,qBAAQ;2DAIR;AAvLW,iBAAiB;IAuDkB,WAAA,oBAAO,CAAA;GAvD1C,iBAAiB,CA0a7B;AA1aY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:53.1043451-07:00\r\n\r\nimport { ActionTransition } from \"./atn/ActionTransition\";\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNState } from \"./atn/ATNState\";\r\nimport { ATNStateType } from \"./atn/ATNStateType\";\r\nimport { AtomTransition } from \"./atn/AtomTransition\";\r\nimport { BitSet } from \"./misc/BitSet\";\r\nimport { DecisionState } from \"./atn/DecisionState\";\r\nimport { FailedPredicateException } from \"./FailedPredicateException\";\r\nimport { InputMismatchException } from \"./InputMismatchException\";\r\nimport { InterpreterRuleContext } from \"./InterpreterRuleContext\";\r\nimport { LoopEndState } from \"./atn/LoopEndState\";\r\nimport { NotNull } from \"./Decorators\";\r\nimport { Override } from \"./Decorators\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserATNSimulator } from \"./atn/ParserATNSimulator\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { PrecedencePredicateTransition } from \"./atn/PrecedencePredicateTransition\";\r\nimport { PredicateTransition } from \"./atn/PredicateTransition\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { RuleStartState } from \"./atn/RuleStartState\";\r\nimport { RuleTransition } from \"./atn/RuleTransition\";\r\nimport { StarLoopEntryState } from \"./atn/StarLoopEntryState\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenStream } from \"./TokenStream\";\r\nimport { Transition } from \"./atn/Transition\";\r\nimport { TransitionType } from \"./atn/TransitionType\";\r\nimport { Vocabulary } from \"./Vocabulary\";\r\n\r\n/** A parser simulator that mimics what ANTLR's generated\r\n *  parser code does. A ParserATNSimulator is used to make\r\n *  predictions via adaptivePredict but this class moves a pointer through the\r\n *  ATN to simulate parsing. ParserATNSimulator just\r\n *  makes us efficient rather than having to backtrack, for example.\r\n *\r\n *  This properly creates parse trees even for left recursive rules.\r\n *\r\n *  We rely on the left recursive rule invocation and special predicate\r\n *  transitions to make left recursive rules work.\r\n *\r\n *  See TestParserInterpreter for examples.\r\n */\r\nexport class ParserInterpreter extends Parser {\r\n\tprotected _grammarFileName: string;\r\n\tprotected _atn: ATN;\r\n\t/** This identifies StarLoopEntryState's that begin the (...)*\r\n\t *  precedence loops of left recursive rules.\r\n\t */\r\n\tprotected pushRecursionContextStates: BitSet;\r\n\r\n\tprotected _ruleNames: string[];\r\n\t@NotNull\r\n\tprivate _vocabulary: Vocabulary;\r\n\r\n\t/** This stack corresponds to the _parentctx, _parentState pair of locals\r\n\t *  that would exist on call stack frames with a recursive descent parser;\r\n\t *  in the generated function for a left-recursive rule you'd see:\r\n\t *\r\n\t *  private EContext e(int _p) {\r\n\t *      ParserRuleContext _parentctx = _ctx;    // Pair.a\r\n\t *      int _parentState = state;          // Pair.b\r\n\t *      ...\r\n\t *  }\r\n\t *\r\n\t *  Those values are used to create new recursive rule invocation contexts\r\n\t *  associated with left operand of an alt like \"expr '*' expr\".\r\n\t */\r\n\tprotected readonly _parentContextStack: Array<[ParserRuleContext, number]> = [];\r\n\r\n\t/** We need a map from (decision,inputIndex)->forced alt for computing ambiguous\r\n\t *  parse trees. For now, we allow exactly one override.\r\n\t */\r\n\tprotected overrideDecision: number = -1;\r\n\tprotected overrideDecisionInputIndex: number = -1;\r\n\tprotected overrideDecisionAlt: number = -1;\r\n\tprotected overrideDecisionReached: boolean = false; // latch and only override once; error might trigger infinite loop\r\n\r\n\t/** What is the current context when we override a decisions?  This tells\r\n\t *  us what the root of the parse tree is when using override\r\n\t *  for an ambiguity/lookahead check.\r\n\t */\r\n\tprotected _overrideDecisionRoot?: InterpreterRuleContext = undefined;\r\n\r\n\tprotected _rootContext!: InterpreterRuleContext;\r\n\r\n\t/** A copy constructor that creates a new parser interpreter by reusing\r\n\t *  the fields of a previous interpreter.\r\n\t *\r\n\t *  @param old The interpreter to copy\r\n\t *\r\n\t *  @since 4.5\r\n\t */\r\n\tconstructor(/*@NotNull*/ old: ParserInterpreter);\r\n\tconstructor(\r\n\t\tgrammarFileName: string, /*@NotNull*/ vocabulary: Vocabulary,\r\n\t\truleNames: string[], atn: ATN, input: TokenStream);\r\n\tconstructor(\r\n\t\tgrammarFileName: ParserInterpreter | string, @NotNull vocabulary?: Vocabulary,\r\n\t\truleNames?: string[], atn?: ATN, input?: TokenStream) {\r\n\t\tsuper(grammarFileName instanceof ParserInterpreter ? grammarFileName.inputStream : input!);\r\n\t\tif (grammarFileName instanceof ParserInterpreter) {\r\n\t\t\tlet old: ParserInterpreter = grammarFileName;\r\n\t\t\tthis._grammarFileName = old._grammarFileName;\r\n\t\t\tthis._atn = old._atn;\r\n\t\t\tthis.pushRecursionContextStates = old.pushRecursionContextStates;\r\n\t\t\tthis._ruleNames = old._ruleNames;\r\n\t\t\tthis._vocabulary = old._vocabulary;\r\n\t\t\tthis.interpreter = new ParserATNSimulator(this._atn, this);\r\n\t\t} else {\r\n\t\t\t// The second constructor requires non-null arguments\r\n\t\t\tvocabulary = vocabulary!;\r\n\t\t\truleNames = ruleNames!;\r\n\t\t\tatn = atn!;\r\n\r\n\t\t\tthis._grammarFileName = grammarFileName;\r\n\t\t\tthis._atn = atn;\r\n\t\t\tthis._ruleNames = ruleNames.slice(0);\r\n\t\t\tthis._vocabulary = vocabulary;\r\n\r\n\t\t\t// identify the ATN states where pushNewRecursionContext() must be called\r\n\t\t\tthis.pushRecursionContextStates = new BitSet(atn.states.length);\r\n\t\t\tfor (let state of atn.states) {\r\n\t\t\t\tif (!(state instanceof StarLoopEntryState)) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (state.precedenceRuleDecision) {\r\n\t\t\t\t\tthis.pushRecursionContextStates.set(state.stateNumber);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// get atn simulator that knows how to do predictions\r\n\t\t\tthis.interpreter = new ParserATNSimulator(atn, this);\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reset(resetInput?: boolean): void {\r\n\t\tif (resetInput === undefined) {\r\n\t\t\tsuper.reset();\r\n\t\t} else {\r\n\t\t\tsuper.reset(resetInput);\r\n\t\t}\r\n\r\n\t\tthis.overrideDecisionReached = false;\r\n\t\tthis._overrideDecisionRoot = undefined;\r\n\t}\r\n\r\n\t@Override\r\n\tget atn(): ATN {\r\n\t\treturn this._atn;\r\n\t}\r\n\r\n\t@Override\r\n\tget vocabulary(): Vocabulary {\r\n\t\treturn this._vocabulary;\r\n\t}\r\n\r\n\t@Override\r\n\tget ruleNames(): string[] {\r\n\t\treturn this._ruleNames;\r\n\t}\r\n\r\n\t@Override\r\n\tget grammarFileName(): string {\r\n\t\treturn this._grammarFileName;\r\n\t}\r\n\r\n\t/** Begin parsing at startRuleIndex */\r\n\tpublic parse(startRuleIndex: number): ParserRuleContext {\r\n\t\tlet startRuleStartState: RuleStartState = this._atn.ruleToStartState[startRuleIndex];\r\n\r\n\t\tthis._rootContext = this.createInterpreterRuleContext(undefined, ATNState.INVALID_STATE_NUMBER, startRuleIndex);\r\n\t\tif (startRuleStartState.isPrecedenceRule) {\r\n\t\t\tthis.enterRecursionRule(this._rootContext, startRuleStartState.stateNumber, startRuleIndex, 0);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.enterRule(this._rootContext, startRuleStartState.stateNumber, startRuleIndex);\r\n\t\t}\r\n\r\n\t\twhile (true) {\r\n\t\t\tlet p: ATNState = this.atnState;\r\n\t\t\tswitch (p.stateType) {\r\n\t\t\tcase ATNStateType.RULE_STOP:\r\n\t\t\t\t// pop; return from rule\r\n\t\t\t\tif (this._ctx.isEmpty) {\r\n\t\t\t\t\tif (startRuleStartState.isPrecedenceRule) {\r\n\t\t\t\t\t\tlet result: ParserRuleContext = this._ctx;\r\n\t\t\t\t\t\tlet parentContext: [ParserRuleContext, number] = this._parentContextStack.pop() !;\r\n\t\t\t\t\t\tthis.unrollRecursionContexts(parentContext[0]);\r\n\t\t\t\t\t\treturn result;\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tthis.exitRule();\r\n\t\t\t\t\t\treturn this._rootContext;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.visitRuleStopState(p);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.visitState(p);\r\n\t\t\t\t}\r\n\t\t\t\tcatch (e) {\r\n\t\t\t\t\tif (e instanceof RecognitionException) {\r\n\t\t\t\t\t\tthis.state = this._atn.ruleToStopState[p.ruleIndex].stateNumber;\r\n\t\t\t\t\t\tthis.context.exception = e;\r\n\t\t\t\t\t\tthis.errorHandler.reportError(this, e);\r\n\t\t\t\t\t\tthis.recover(e);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow e;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\tpublic enterRecursionRule(localctx: ParserRuleContext, state: number, ruleIndex: number, precedence: number): void {\r\n\t\tthis._parentContextStack.push([this._ctx, localctx.invokingState]);\r\n\t\tsuper.enterRecursionRule(localctx, state, ruleIndex, precedence);\r\n\t}\r\n\r\n\tprotected get atnState(): ATNState {\r\n\t\treturn this._atn.states[this.state];\r\n\t}\r\n\r\n\tprotected visitState(p: ATNState): void {\r\n\t\tlet predictedAlt: number = 1;\r\n\t\tif (p.numberOfTransitions > 1) {\r\n\t\t\tpredictedAlt = this.visitDecisionState(p as DecisionState);\r\n\t\t}\r\n\r\n\t\tlet transition: Transition = p.transition(predictedAlt - 1);\r\n\t\tswitch (transition.serializationType) {\r\n\t\tcase TransitionType.EPSILON:\r\n\t\t\tif (this.pushRecursionContextStates.get(p.stateNumber) &&\r\n\t\t\t\t!(transition.target instanceof LoopEndState)) {\r\n\t\t\t\t// We are at the start of a left recursive rule's (...)* loop\r\n\t\t\t\t// and we're not taking the exit branch of loop.\r\n\t\t\t\tlet parentContext = this._parentContextStack[this._parentContextStack.length - 1];\r\n\t\t\t\tlet localctx: InterpreterRuleContext =\r\n\t\t\t\t\tthis.createInterpreterRuleContext(parentContext[0], parentContext[1], this._ctx.ruleIndex);\r\n\t\t\t\tthis.pushNewRecursionContext(localctx,\r\n\t\t\t\t\tthis._atn.ruleToStartState[p.ruleIndex].stateNumber,\r\n\t\t\t\t\tthis._ctx.ruleIndex);\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.ATOM:\r\n\t\t\tthis.match((transition as AtomTransition)._label);\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.RANGE:\r\n\t\tcase TransitionType.SET:\r\n\t\tcase TransitionType.NOT_SET:\r\n\t\t\tif (!transition.matches(this._input.LA(1), Token.MIN_USER_TOKEN_TYPE, 65535)) {\r\n\t\t\t\tthis.recoverInline();\r\n\t\t\t}\r\n\t\t\tthis.matchWildcard();\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.WILDCARD:\r\n\t\t\tthis.matchWildcard();\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.RULE:\r\n\t\t\tlet ruleStartState: RuleStartState = transition.target as RuleStartState;\r\n\t\t\tlet ruleIndex: number = ruleStartState.ruleIndex;\r\n\t\t\tlet newctx: InterpreterRuleContext = this.createInterpreterRuleContext(this._ctx, p.stateNumber, ruleIndex);\r\n\t\t\tif (ruleStartState.isPrecedenceRule) {\r\n\t\t\t\tthis.enterRecursionRule(newctx, ruleStartState.stateNumber, ruleIndex, (transition as RuleTransition).precedence);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tthis.enterRule(newctx, transition.target.stateNumber, ruleIndex);\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.PREDICATE:\r\n\t\t\tlet predicateTransition: PredicateTransition = transition as PredicateTransition;\r\n\t\t\tif (!this.sempred(this._ctx, predicateTransition.ruleIndex, predicateTransition.predIndex)) {\r\n\t\t\t\tthrow new FailedPredicateException(this);\r\n\t\t\t}\r\n\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.ACTION:\r\n\t\t\tlet actionTransition: ActionTransition = transition as ActionTransition;\r\n\t\t\tthis.action(this._ctx, actionTransition.ruleIndex, actionTransition.actionIndex);\r\n\t\t\tbreak;\r\n\r\n\t\tcase TransitionType.PRECEDENCE:\r\n\t\t\tif (!this.precpred(this._ctx, (transition as PrecedencePredicateTransition).precedence)) {\r\n\t\t\t\tlet precedence = (transition as PrecedencePredicateTransition).precedence;\r\n\t\t\t\tthrow new FailedPredicateException(this, `precpred(_ctx, ${precedence})`);\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\r\n\t\tdefault:\r\n\t\t\tthrow new Error(\"UnsupportedOperationException: Unrecognized ATN transition type.\");\r\n\t\t}\r\n\r\n\t\tthis.state = transition.target.stateNumber;\r\n\t}\r\n\r\n\t/** Method visitDecisionState() is called when the interpreter reaches\r\n\t *  a decision state (instance of DecisionState). It gives an opportunity\r\n\t *  for subclasses to track interesting things.\r\n\t */\r\n\tprotected visitDecisionState(p: DecisionState): number {\r\n\t\tlet predictedAlt: number;\r\n\t\tthis.errorHandler.sync(this);\r\n\t\tlet decision: number = p.decision;\r\n\t\tif (decision === this.overrideDecision && this._input.index === this.overrideDecisionInputIndex && !this.overrideDecisionReached) {\r\n\t\t\tpredictedAlt = this.overrideDecisionAlt;\r\n\t\t\tthis.overrideDecisionReached = true;\r\n\t\t}\r\n\t\telse {\r\n\t\t\tpredictedAlt = this.interpreter.adaptivePredict(this._input, decision, this._ctx);\r\n\t\t}\r\n\t\treturn predictedAlt;\r\n\t}\r\n\r\n\t/** Provide simple \"factory\" for InterpreterRuleContext's.\r\n\t *  @since 4.5.1\r\n\t */\r\n\tprotected createInterpreterRuleContext(\r\n\t\tparent: ParserRuleContext | undefined,\r\n\t\tinvokingStateNumber: number,\r\n\t\truleIndex: number): InterpreterRuleContext {\r\n\t\treturn new InterpreterRuleContext(ruleIndex, parent, invokingStateNumber);\r\n\t}\r\n\r\n\tprotected visitRuleStopState(p: ATNState): void {\r\n\t\tlet ruleStartState: RuleStartState = this._atn.ruleToStartState[p.ruleIndex];\r\n\t\tif (ruleStartState.isPrecedenceRule) {\r\n\t\t\tlet parentContext: [ParserRuleContext, number] = this._parentContextStack.pop()!;\r\n\t\t\tthis.unrollRecursionContexts(parentContext[0]);\r\n\t\t\tthis.state = parentContext[1];\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.exitRule();\r\n\t\t}\r\n\r\n\t\tlet ruleTransition: RuleTransition = this._atn.states[this.state].transition(0) as RuleTransition;\r\n\t\tthis.state = ruleTransition.followState.stateNumber;\r\n\t}\r\n\r\n\t/** Override this parser interpreters normal decision-making process\r\n\t *  at a particular decision and input token index. Instead of\r\n\t *  allowing the adaptive prediction mechanism to choose the\r\n\t *  first alternative within a block that leads to a successful parse,\r\n\t *  force it to take the alternative, 1..n for n alternatives.\r\n\t *\r\n\t *  As an implementation limitation right now, you can only specify one\r\n\t *  override. This is sufficient to allow construction of different\r\n\t *  parse trees for ambiguous input. It means re-parsing the entire input\r\n\t *  in general because you're never sure where an ambiguous sequence would\r\n\t *  live in the various parse trees. For example, in one interpretation,\r\n\t *  an ambiguous input sequence would be matched completely in expression\r\n\t *  but in another it could match all the way back to the root.\r\n\t *\r\n\t *  s : e '!'? ;\r\n\t *  e : ID\r\n\t *    | ID '!'\r\n\t *    ;\r\n\t *\r\n\t *  Here, x! can be matched as (s (e ID) !) or (s (e ID !)). In the first\r\n\t *  case, the ambiguous sequence is fully contained only by the root.\r\n\t *  In the second case, the ambiguous sequences fully contained within just\r\n\t *  e, as in: (e ID !).\r\n\t *\r\n\t *  Rather than trying to optimize this and make\r\n\t *  some intelligent decisions for optimization purposes, I settled on\r\n\t *  just re-parsing the whole input and then using\r\n\t *  {link Trees#getRootOfSubtreeEnclosingRegion} to find the minimal\r\n\t *  subtree that contains the ambiguous sequence. I originally tried to\r\n\t *  record the call stack at the point the parser detected and ambiguity but\r\n\t *  left recursive rules create a parse tree stack that does not reflect\r\n\t *  the actual call stack. That impedance mismatch was enough to make\r\n\t *  it it challenging to restart the parser at a deeply nested rule\r\n\t *  invocation.\r\n\t *\r\n\t *  Only parser interpreters can override decisions so as to avoid inserting\r\n\t *  override checking code in the critical ALL(*) prediction execution path.\r\n\t *\r\n\t *  @since 4.5\r\n\t */\r\n\tpublic addDecisionOverride(decision: number, tokenIndex: number, forcedAlt: number): void {\r\n\t\tthis.overrideDecision = decision;\r\n\t\tthis.overrideDecisionInputIndex = tokenIndex;\r\n\t\tthis.overrideDecisionAlt = forcedAlt;\r\n\t}\r\n\r\n\tget overrideDecisionRoot(): InterpreterRuleContext | undefined {\r\n\t\treturn this._overrideDecisionRoot;\r\n\t}\r\n\r\n\t/** Rely on the error handler for this parser but, if no tokens are consumed\r\n\t *  to recover, add an error node. Otherwise, nothing is seen in the parse\r\n\t *  tree.\r\n\t */\r\n\tprotected recover(e: RecognitionException): void {\r\n\t\tlet i: number = this._input.index;\r\n\t\tthis.errorHandler.recover(this, e);\r\n\t\tif (this._input.index === i) {\r\n\t\t\t// no input consumed, better add an error node\r\n\t\t\tlet tok: Token | undefined = e.getOffendingToken();\r\n\t\t\tif (!tok) {\r\n\t\t\t\tthrow new Error(\"Expected exception to have an offending token\");\r\n\t\t\t}\r\n\r\n\t\t\tlet source = tok.tokenSource;\r\n\t\t\tlet stream = source !== undefined ? source.inputStream : undefined;\r\n\t\t\tlet sourcePair = { source, stream };\r\n\r\n\t\t\tif (e instanceof InputMismatchException) {\r\n\t\t\t\tlet expectedTokens = e.expectedTokens;\r\n\t\t\t\tif (expectedTokens === undefined) {\r\n\t\t\t\t\tthrow new Error(\"Expected the exception to provide expected tokens\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet expectedTokenType: number = Token.INVALID_TYPE;\r\n\t\t\t\tif (!expectedTokens.isNil) {\r\n\t\t\t\t\t// get any element\r\n\t\t\t\t\texpectedTokenType = expectedTokens.minElement;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet errToken: Token =\r\n\t\t\t\t\tthis.tokenFactory.create(sourcePair,\r\n\t\t\t\t\t\texpectedTokenType, tok.text,\r\n\t\t\t\t\t\tToken.DEFAULT_CHANNEL,\r\n\t\t\t\t\t\t-1, -1, // invalid start/stop\r\n\t\t\t\t\t\ttok.line, tok.charPositionInLine);\r\n\t\t\t\tthis._ctx.addErrorNode(this.createErrorNode(this._ctx, errToken));\r\n\t\t\t}\r\n\t\t\telse { // NoViableAlt\r\n\t\t\t\tlet source = tok.tokenSource;\r\n\t\t\t\tlet errToken: Token =\r\n\t\t\t\t\tthis.tokenFactory.create(sourcePair,\r\n\t\t\t\t\t\tToken.INVALID_TYPE, tok.text,\r\n\t\t\t\t\t\tToken.DEFAULT_CHANNEL,\r\n\t\t\t\t\t\t-1, -1, // invalid start/stop\r\n\t\t\t\t\t\ttok.line, tok.charPositionInLine);\r\n\t\t\t\tthis._ctx.addErrorNode(this.createErrorNode(this._ctx, errToken));\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected recoverInline(): Token {\r\n\t\treturn this._errHandler.recoverInline(this);\r\n\t}\r\n\r\n\t/** Return the root of the parse, which can be useful if the parser\r\n\t *  bails out. You still can access the top node. Note that,\r\n\t *  because of the way left recursive rules add children, it's possible\r\n\t *  that the root will not have any children if the start rule immediately\r\n\t *  called and left recursive rule that fails.\r\n\t *\r\n\t * @since 4.5.1\r\n\t */\r\n\tget rootContext(): InterpreterRuleContext {\r\n\t\treturn this._rootContext;\r\n\t}\r\n}\r\n"]}