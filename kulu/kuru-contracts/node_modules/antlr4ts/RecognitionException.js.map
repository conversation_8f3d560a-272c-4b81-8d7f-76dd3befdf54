{"version": 3, "file": "RecognitionException.js", "sourceRoot": "", "sources": ["../../src/RecognitionException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAcH;;;;;GAKG;AACH,MAAa,oBAAqB,SAAQ,KAAK;IAkC9C,YACC,UAAsD,EACtD,KAAyC,EACzC,GAAuB,EACvB,OAAgB;QAChB,KAAK,CAAC,OAAO,CAAC,CAAC;QAtBR,oBAAe,GAAW,CAAC,CAAC,CAAC;QAwBpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,UAAU,EAAE;YACf,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC;SACxC;IACF,CAAC;IAED;;;;;;;;OAQG;IACH,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAES,iBAAiB,CAAC,cAAsB;QACjD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI,cAAc;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9E;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,GAAG,CAAC;IACjB,CAAC;IAED;;;;;;;;;OASG;IAEH,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEM,iBAAiB,CAAC,UAAmC;QAC3D,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,EAAE;YAClD,OAAO,SAAS,CAAC;SACjB;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC5B,CAAC;IAES,iBAAiB,CAC1B,UAAoC,EACpC,cAAwB;QACxB,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,EAAE;YACpC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;SACrC;IACF,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;CACD;AAxID,oDAwIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.0697674-07:00\r\nimport { CharStream } from \"./CharStream\";\r\nimport { IntervalSet } from \"./misc/IntervalSet\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { Lexer } from \"./Lexer\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { Token } from \"./Token\";\r\n\r\n\r\n/** The root of the ANTLR exception hierarchy. In general, ANTLR tracks just\r\n *  3 kinds of errors: prediction errors, failed predicate errors, and\r\n *  mismatched input errors. In each case, the parser knows where it is\r\n *  in the input, where it is in the ATN, the rule invocation stack,\r\n *  and what kind of problem occurred.\r\n */\r\nexport class RecognitionException extends Error {\r\n\t// private static serialVersionUID: number =  -3861826954750022374L;\r\n\r\n\t/** The {@link Recognizer} where this exception originated. */\r\n\tprivate _recognizer?: Recognizer<any, any>;\r\n\r\n\tprivate ctx?: RuleContext;\r\n\r\n\tprivate input?: IntStream;\r\n\r\n\t/**\r\n\t * The current {@link Token} when an error occurred. Since not all streams\r\n\t * support accessing symbols by index, we have to track the {@link Token}\r\n\t * instance itself.\r\n\t */\r\n\tprivate offendingToken?: Token;\r\n\r\n\tprivate _offendingState: number = -1;\r\n\r\n\tconstructor(\r\n\t\tlexer: Lexer | undefined,\r\n\t\tinput: CharStream);\r\n\r\n\tconstructor(\r\n\t\trecognizer: Recognizer<Token, any> | undefined,\r\n\t\tinput: IntStream | undefined,\r\n\t\tctx: ParserRuleContext | undefined);\r\n\r\n\tconstructor(\r\n\t\trecognizer: Recognizer<Token, any> | undefined,\r\n\t\tinput: IntStream | undefined,\r\n\t\tctx: ParserRuleContext | undefined,\r\n\t\tmessage: string);\r\n\r\n\tconstructor(\r\n\t\trecognizer: Lexer | Recognizer<Token, any> | undefined,\r\n\t\tinput: CharStream | IntStream | undefined,\r\n\t\tctx?: ParserRuleContext,\r\n\t\tmessage?: string) {\r\n\t\tsuper(message);\r\n\r\n\t\tthis._recognizer = recognizer;\r\n\t\tthis.input = input;\r\n\t\tthis.ctx = ctx;\r\n\t\tif (recognizer) {\r\n\t\t\tthis._offendingState = recognizer.state;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get the ATN state number the parser was in at the time the error\r\n\t * occurred. For {@link NoViableAltException} and\r\n\t * {@link LexerNoViableAltException} exceptions, this is the\r\n\t * {@link DecisionState} number. For others, it is the state whose outgoing\r\n\t * edge we couldn't match.\r\n\t *\r\n\t * If the state number is not known, this method returns -1.\r\n\t */\r\n\tget offendingState(): number {\r\n\t\treturn this._offendingState;\r\n\t}\r\n\r\n\tprotected setOffendingState(offendingState: number): void {\r\n\t\tthis._offendingState = offendingState;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the set of input symbols which could potentially follow the\r\n\t * previously matched symbol at the time this exception was thrown.\r\n\t *\r\n\t * If the set of expected tokens is not known and could not be computed,\r\n\t * this method returns `undefined`.\r\n\t *\r\n\t * @returns The set of token types that could potentially follow the current\r\n\t * state in the ATN, or `undefined` if the information is not available.\r\n\t */\r\n\tget expectedTokens(): IntervalSet | undefined {\r\n\t\tif (this._recognizer) {\r\n\t\t\treturn this._recognizer.atn.getExpectedTokens(this._offendingState, this.ctx);\r\n\t\t}\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the {@link RuleContext} at the time this exception was thrown.\r\n\t *\r\n\t * If the context is not available, this method returns `undefined`.\r\n\t *\r\n\t * @returns The {@link RuleContext} at the time this exception was thrown.\r\n\t * If the context is not available, this method returns `undefined`.\r\n\t */\r\n\tget context(): RuleContext | undefined {\r\n\t\treturn this.ctx;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the input stream which is the symbol source for the recognizer where\r\n\t * this exception was thrown.\r\n\t *\r\n\t * If the input stream is not available, this method returns `undefined`.\r\n\t *\r\n\t * @returns The input stream which is the symbol source for the recognizer\r\n\t * where this exception was thrown, or `undefined` if the stream is not\r\n\t * available.\r\n\t */\r\n\r\n\tget inputStream(): IntStream | undefined {\r\n\t\treturn this.input;\r\n\t}\r\n\r\n\tpublic getOffendingToken(recognizer?: Recognizer<Token, any>): Token | undefined {\r\n\t\tif (recognizer && recognizer !== this._recognizer) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\t\treturn this.offendingToken;\r\n\t}\r\n\r\n\tprotected setOffendingToken<TSymbol extends Token>(\r\n\t\trecognizer: Recognizer<TSymbol, any>,\r\n\t\toffendingToken?: TSymbol): void {\r\n\t\tif (recognizer === this._recognizer) {\r\n\t\t\tthis.offendingToken = offendingToken;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the {@link Recognizer} where this exception occurred.\r\n\t *\r\n\t * If the recognizer is not available, this method returns `undefined`.\r\n\t *\r\n\t * @returns The recognizer where this exception occurred, or `undefined` if\r\n\t * the recognizer is not available.\r\n\t */\r\n\tget recognizer(): Recognizer<any, any> | undefined {\r\n\t\treturn this._recognizer;\r\n\t}\r\n}\r\n"]}