{"version": 3, "file": "NoViableAltException.js", "sourceRoot": "", "sources": ["../../src/NoViableAltException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,qCAAkC;AAElC,iEAA8D;AAK9D,6CAAuC;AAEvC;;;;GAIG;AACH,MAAa,oBAAqB,SAAQ,2CAAoB;IA4B7D,YACC,UAAkC,EAClC,KAAmB,EACnB,UAAkB,EAClB,cAAsB,EACtB,cAA6B,EAC7B,GAAuB;QACvB,IAAI,UAAU,YAAY,eAAM,EAAE;YACjC,IAAI,KAAK,KAAK,SAAS,EAAE;gBACxB,KAAK,GAAG,UAAU,CAAC,WAAW,CAAC;aAC/B;YAED,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC7B,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC;aACrC;YAED,IAAI,cAAc,KAAK,SAAS,EAAE;gBACjC,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC;aACzC;YAED,IAAI,GAAG,KAAK,SAAS,EAAE;gBACtB,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;aACzB;SACD;QAED,KAAK,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,UAAmB,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;CAED;AAvDA;IADC,oBAAO;yDACmB;AAZ5B,oDAmEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:52.3255548-07:00\r\n\r\nimport { ATNConfigSet } from \"./atn/ATNConfigSet\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenStream } from \"./TokenStream\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { NotNull } from \"./Decorators\";\r\n\r\n/** Indicates that the parser could not decide which of two or more paths\r\n *  to take based upon the remaining input. It tracks the starting token\r\n *  of the offending input and also knows where the parser was\r\n *  in the various paths when the error. Reported by reportNoViableAlternative()\r\n */\r\nexport class NoViableAltException extends RecognitionException {\r\n\t//private static serialVersionUID: number =  5096000008992867052L;\r\n\r\n\t/** Which configurations did we try at input.index that couldn't match input.LT(1)? */\r\n\tprivate _deadEndConfigs?: ATNConfigSet;\r\n\r\n\t/** The token object at the start index; the input stream might\r\n\t * \tnot be buffering tokens so get a reference to it. (At the\r\n\t *  time the error occurred, of course the stream needs to keep a\r\n\t *  buffer all of the tokens but later we might not have access to those.)\r\n\t */\r\n\t@NotNull\r\n\tprivate _startToken: Token;\r\n\r\n\tconstructor(/*@NotNull*/ recognizer: Parser);\r\n\tconstructor(\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Recognizer<Token, any>,\r\n\t\t/*@NotNull*/\r\n\t\tinput: TokenStream,\r\n\t\t/*@NotNull*/\r\n\t\tstartToken: Token,\r\n\t\t/*@NotNull*/\r\n\t\toffendingToken: Token,\r\n\t\tdeadEndConfigs: ATNConfigSet | undefined,\r\n\t\t/*@NotNull*/\r\n\t\tctx: ParserRuleContext);\r\n\r\n\tconstructor(\r\n\t\trecognizer: Recognizer<Token, any>,\r\n\t\tinput?: TokenStream,\r\n\t\tstartToken?: Token,\r\n\t\toffendingToken?: Token,\r\n\t\tdeadEndConfigs?: ATNConfigSet,\r\n\t\tctx?: ParserRuleContext) {\r\n\t\tif (recognizer instanceof Parser) {\r\n\t\t\tif (input === undefined) {\r\n\t\t\t\tinput = recognizer.inputStream;\r\n\t\t\t}\r\n\r\n\t\t\tif (startToken === undefined) {\r\n\t\t\t\tstartToken = recognizer.currentToken;\r\n\t\t\t}\r\n\r\n\t\t\tif (offendingToken === undefined) {\r\n\t\t\t\toffendingToken = recognizer.currentToken;\r\n\t\t\t}\r\n\r\n\t\t\tif (ctx === undefined) {\r\n\t\t\t\tctx = recognizer.context;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tsuper(recognizer, input, ctx);\r\n\t\tthis._deadEndConfigs = deadEndConfigs;\r\n\t\tthis._startToken = startToken as Token;\r\n\t\tthis.setOffendingToken(recognizer, offendingToken);\r\n\t}\r\n\r\n\tget startToken(): Token {\r\n\t\treturn this._startToken;\r\n\t}\r\n\r\n\tget deadEndConfigs(): ATNConfigSet | undefined {\r\n\t\treturn this._deadEndConfigs;\r\n\t}\r\n\r\n}\r\n"]}