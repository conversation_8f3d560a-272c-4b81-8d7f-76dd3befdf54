{"version": 3, "file": "DefaultErrorStrategy.js", "sourceRoot": "", "sources": ["../../src/DefaultErrorStrategy.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,6CAA0C;AAC1C,qDAAkD;AAClD,yEAAsE;AACtE,qEAAkE;AAClE,oDAAiD;AACjD,iEAA8D;AAG9D,+DAA4D;AAK5D,mCAAgC;AAIhC,6CAAiD;AAEjD;;;GAGG;AACH,MAAa,oBAAoB;IAAjC;QACC;;;;;;WAMG;QACO,sBAAiB,GAAY,KAAK,CAAC;QAE7C;;;;;WAKG;QACO,mBAAc,GAAW,CAAC,CAAC,CAAC;QActC;;WAEG;QACO,oBAAe,GAAW,mBAAQ,CAAC,oBAAoB,CAAC;IA+uBnE,CAAC;IA7uBA;;;;;OAKG;IAEI,KAAK,CAAC,UAAkB;QAC9B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACO,mBAAmB,CAAU,UAAkB;QACxD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IAEI,mBAAmB,CAAC,UAAkB;QAC5C,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACO,iBAAiB,CAAU,UAAkB;QACtD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IAEI,WAAW,CAAC,UAAkB;QACpC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IAEI,WAAW,CACjB,UAAkB,EAClB,CAAuB;QACvB,kEAAkE;QAClE,6CAA6C;QAC7C,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE;YAC5C,qCAAqC;YAClC,OAAO,CAAC,+BAA+B;SACvC;QACD,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,2CAAoB,EAAE;YACtC,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SAC9C;aACI,IAAI,CAAC,YAAY,+CAAsB,EAAE;YAC7C,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACxC;aACI,IAAI,CAAC,YAAY,mDAAwB,EAAE;YAC/C,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SAC1C;aACI;YACJ,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;SACvD;IACF,CAAC;IAES,oBAAoB,CAAU,UAAkB,EAAE,OAAe,EAAE,CAAuB;QACnG,IAAI,cAAc,GAA6B,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,cAAc,KAAK,SAAS,EAAE;YACjC,4GAA4G;YAC5G,wGAAwG;YACxG,cAAc,GAAG,IAAI,CAAC;SACtB;QAED,UAAU,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;OAMG;IAEI,OAAO,CAAC,UAAkB,EAAE,CAAuB;QAC3D,yEAAyE;QACzE,kDAAkD;QAClD,+BAA+B;QAC/B,0BAA0B;QAC1B,wCAAwC;QACtC,IAAI,IAAI,CAAC,cAAc,KAAK,UAAU,CAAC,WAAW,CAAC,KAAK;YACvD,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YACjD,kEAAkE;YAClE,8DAA8D;YAC9D,4DAA4D;YAC5D,4DAA4D;YAC/D,6DAA6D;YAC7D,wDAAwD;YACxD,uGAAuG;YACpG,UAAU,CAAC,OAAO,EAAE,CAAC;SACrB;QACD,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,yBAAW,EAAE,CAAC;SACzC;QACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,SAAS,GAAgB,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6CG;IAEI,IAAI,CAAC,UAAkB;QAC7B,IAAI,CAAC,GAAa,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACxE,iFAAiF;QAC/E,2CAA2C;QAC3C,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE;YACzC,OAAO;SACP;QAED,IAAI,MAAM,GAAgB,UAAU,CAAC,WAAW,CAAC;QACjD,IAAI,EAAE,GAAW,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE9B,0EAA0E;QAC1E,IAAI,UAAU,GAAgB,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;YAC5B,gCAAgC;YAChC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,mBAAQ,CAAC,oBAAoB,CAAC;YACrD,OAAO;SACP;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YACvC,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;gBACzC,gEAAgE;gBAChE,yCAAyC;gBACzC,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC;aACxC;YAED,OAAO;SACP;QAED,QAAQ,CAAC,CAAC,SAAS,EAAE;YACrB,KAAK,2BAAY,CAAC,WAAW,CAAC;YAC9B,KAAK,2BAAY,CAAC,gBAAgB,CAAC;YACnC,KAAK,2BAAY,CAAC,gBAAgB,CAAC;YACnC,KAAK,2BAAY,CAAC,eAAe;gBAChC,uCAAuC;gBACvC,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE;oBACzC,OAAO;iBACP;gBAED,MAAM,IAAI,+CAAsB,CAAC,UAAU,CAAC,CAAC;YAE9C,KAAK,2BAAY,CAAC,cAAc,CAAC;YACjC,KAAK,2BAAY,CAAC,cAAc;gBAClC,uEAAuE;gBACpE,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBACrC,IAAI,SAAS,GAAgB,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBAC5D,IAAI,8BAA8B,GACjC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,8BAA8B,CAAC,CAAC;gBAC9D,MAAM;YAEP;gBACC,8DAA8D;gBAC9D,MAAM;SACN;IACF,CAAC;IAED;;;;;;;;OAQG;IACO,yBAAyB,CACzB,UAAkB,EAClB,CAAuB;QAChC,IAAI,MAAM,GAAgB,UAAU,CAAC,WAAW,CAAC;QACjD,IAAI,KAAa,CAAC;QAClB,IAAI,MAAM,EAAE;YACX,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBACpC,KAAK,GAAG,OAAO,CAAC;aAChB;iBAAM;gBACN,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC;aACrE;SACD;aACI;YACJ,KAAK,GAAG,iBAAiB,CAAC;SAC1B;QACD,IAAI,GAAG,GAAW,iCAAiC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnF,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACO,mBAAmB,CACnB,UAAkB,EAClB,CAAyB;QAClC,IAAI,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC;QAChC,IAAI,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxF,IAAI,GAAG,GAAW,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACjG,aAAa,GAAG,cAAc,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACO,qBAAqB,CACrB,UAAkB,EAClB,CAA2B;QACpC,IAAI,QAAQ,GAAW,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1E,IAAI,GAAG,GAAW,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACO,mBAAmB,CAAU,UAAkB;QACxD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE;YACzC,OAAO;SACP;QAED,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,GAAU,UAAU,CAAC,YAAY,CAAC;QACvC,IAAI,SAAS,GAAW,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,SAAS,GAAgB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,GAAG,GAAW,mBAAmB,GAAG,SAAS,GAAG,aAAa;YAChE,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACrD,UAAU,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACO,kBAAkB,CAAU,UAAkB;QACvD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE;YACzC,OAAO;SACP;QAED,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,GAAU,UAAU,CAAC,YAAY,CAAC;QACvC,IAAI,SAAS,GAAgB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,GAAG,GAAW,UAAU,GAAG,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC;YACjF,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAEvC,UAAU,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiDG;IAEI,aAAa,CAAC,UAAkB;QACtC,wBAAwB;QACxB,IAAI,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,aAAa,EAAE;YAClB,mCAAmC;YACnC,+CAA+C;YAC/C,UAAU,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,aAAa,CAAC;SACrB;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;YAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;SACzC;QAED,kDAAkD;QAClD,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACzC,MAAM,IAAI,+CAAsB,CAAC,UAAU,CAAC,CAAC;SAC7C;aAAM;YACN,MAAM,IAAI,+CAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC3F;IACF,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACO,oBAAoB,CAAU,UAAkB;QACzD,IAAI,iBAAiB,GAAW,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7D,oEAAoE;QACpE,gEAAgE;QAChE,qDAAqD;QACrD,IAAI,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvE,IAAI,IAAI,GAAa,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACvD,IAAI,GAAG,GAAQ,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,IAAI,cAAc,GAAgB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,qCAAiB,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;QACrH,mFAAmF;QACjF,IAAI,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YAC/C,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;SACZ;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACO,mBAAmB,CAAU,UAAkB;QACxD,IAAI,aAAa,GAAW,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,SAAS,GAAgB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACrC;;;;;cAKE;YACF,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,4BAA4B;YAClD,sDAAsD;YACtD,IAAI,aAAa,GAAU,UAAU,CAAC,YAAY,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAE,mCAAmC;YAClE,OAAO,aAAa,CAAC;SACrB;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IAEO,gBAAgB,CAAU,UAAkB;QACrD,IAAI,aAAa,GAAU,UAAU,CAAC,YAAY,CAAC;QACnD,IAAI,SAAS,GAAgB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,iBAAiB,GAAW,aAAK,CAAC,YAAY,CAAC;QACnD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACrB,kBAAkB;YAClB,iBAAiB,GAAG,SAAS,CAAC,UAAU,CAAC;SACzC;QAED,IAAI,SAAiB,CAAC;QACtB,IAAI,iBAAiB,KAAK,aAAK,CAAC,GAAG,EAAE;YACpC,SAAS,GAAG,eAAe,CAAC;SAC5B;aAAM;YACN,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC;SACxF;QACD,IAAI,OAAO,GAAU,aAAa,CAAC;QACnC,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE;YACnD,OAAO,GAAG,QAAQ,CAAC;SACnB;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,EAAE,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACvG,CAAC;IAES,cAAc,CACvB,WAAwB,EACxB,iBAAyB,EACzB,SAAiB,EACjB,OAAc;QACd,IAAI,OAAO,GAAiB,WAAW,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC;QAC5B,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;QAE3C,OAAO,OAAO,CAAC,MAAM,CACpB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,EAC/B,iBAAiB,EAAE,SAAS,EAC5B,aAAK,CAAC,eAAe,EACrB,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAGS,iBAAiB,CAAU,UAAkB;QACtD,OAAO,UAAU,CAAC,iBAAiB,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;;OAOG;IACO,oBAAoB,CAAC,CAAoB;QAClD,IAAI,CAAC,CAAC,EAAE;YACP,OAAO,YAAY,CAAC;SACpB;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,CAAC,EAAE;YACP,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;gBACxC,CAAC,GAAG,OAAO,CAAC;aACZ;iBAAM;gBACN,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC;aACjC;SACD;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAES,aAAa,CAAU,MAAa;QAC7C,OAAO,MAAM,CAAC,IAAI,CAAC;IACpB,CAAC;IAES,aAAa,CAAU,MAAa;QAC7C,OAAO,MAAM,CAAC,IAAI,CAAC;IACpB,CAAC;IAGS,gBAAgB,CAAU,CAAS;QAC9C,4BAA4B;QAC1B,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2FG;IAEO,mBAAmB,CAAU,UAAkB;QACxD,IAAI,GAAG,GAAQ,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,IAAI,GAAG,GAA4B,UAAU,CAAC,OAAO,CAAC;QACtD,IAAI,UAAU,GAAgB,IAAI,yBAAW,EAAE,CAAC;QAChD,OAAO,GAAG,IAAI,GAAG,CAAC,aAAa,IAAI,CAAC,EAAE;YACrC,sCAAsC;YACtC,IAAI,aAAa,GAAa,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC5D,IAAI,EAAE,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;YACvD,IAAI,MAAM,GAAgB,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YACzD,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC;SAClB;QACD,UAAU,CAAC,MAAM,CAAC,aAAK,CAAC,OAAO,CAAC,CAAC;QACnC,uFAAuF;QACrF,OAAO,UAAU,CAAC;IACnB,CAAC;IAED,4DAA4D;IAClD,YAAY,CAAU,UAAkB,EAAW,GAAgB;QAC9E,qFAAqF;QACnF,IAAI,KAAK,GAAW,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjD,OAAO,KAAK,KAAK,aAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACnD,mFAAmF;YACtF,sCAAsC;YACnC,UAAU,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACrC;IACF,CAAC;CACD;AAtuBA;IADC,qBAAQ;iDAGR;AAQD;IAA+B,WAAA,oBAAO,CAAA;+DAErC;AAMD;IADC,qBAAQ;+DAGR;AAQD;IAA6B,WAAA,oBAAO,CAAA;6DAInC;AAQD;IADC,qBAAQ;uDAGR;AAoBD;IADC,qBAAQ;uDAwBR;AAED;IAAgC,WAAA,oBAAO,CAAA;gEAStC;AAUD;IADC,qBAAQ;mDA0BR;AAiDD;IADC,qBAAQ;gDA0DR;AAWD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;qEAeR;AAWD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;+DAMR;AAWD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;iEAIR;AAoBD;IAA+B,WAAA,oBAAO,CAAA;+DAarC;AAmBD;IAA8B,WAAA,oBAAO,CAAA;8DAapC;AAqDD;IADC,qBAAQ;yDAsBR;AAmBD;IAAgC,WAAA,oBAAO,CAAA;gEAetC;AAqBD;IAA+B,WAAA,oBAAO,CAAA;+DAkBrC;AAsBD;IADC,oBAAO;IACoB,WAAA,oBAAO,CAAA;4DAsBlC;AAoBD;IADC,oBAAO;IACqB,WAAA,oBAAO,CAAA;6DAEnC;AAyBD;IAAyB,WAAA,oBAAO,CAAA;yDAE/B;AAED;IAAyB,WAAA,oBAAO,CAAA;yDAE/B;AAGD;IADC,oBAAO;IACoB,WAAA,oBAAO,CAAA;4DAMlC;AA+FD;IADC,oBAAO;IACuB,WAAA,oBAAO,CAAA;+DAerC;AAGD;IAAwB,WAAA,oBAAO,CAAA,EAAsB,WAAA,oBAAO,CAAA;wDAS3D;AA/wBF,oDAgxBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.8290527-07:00\r\n\r\nimport { ANTLRErrorStrategy } from \"./ANTLRErrorStrategy\";\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNState } from \"./atn/ATNState\";\r\nimport { ATNStateType } from \"./atn/ATNStateType\";\r\nimport { FailedPredicateException } from \"./FailedPredicateException\";\r\nimport { InputMismatchException } from \"./InputMismatchException\";\r\nimport { IntervalSet } from \"./misc/IntervalSet\";\r\nimport { NoViableAltException } from \"./NoViableAltException\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { PredictionContext } from \"./atn/PredictionContext\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { RuleTransition } from \"./atn/RuleTransition\";\r\nimport { TokenStream } from \"./TokenStream\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\nimport { TokenSource } from \"./TokenSource\";\r\nimport { Vocabulary } from \"./Vocabulary\";\r\nimport { Override, NotNull } from \"./Decorators\";\r\n\r\n/**\r\n * This is the default implementation of {@link ANTLRErrorStrategy} used for\r\n * error reporting and recovery in ANTLR parsers.\r\n */\r\nexport class DefaultErrorStrategy implements ANTLRErrorStrategy {\r\n\t/**\r\n\t * Indicates whether the error strategy is currently \"recovering from an\r\n\t * error\". This is used to suppress reporting multiple error messages while\r\n\t * attempting to recover from a detected syntax error.\r\n\t *\r\n\t * @see #inErrorRecoveryMode\r\n\t */\r\n\tprotected errorRecoveryMode: boolean = false;\r\n\r\n\t/** The index into the input stream where the last error occurred.\r\n\t * \tThis is used to prevent infinite loops where an error is found\r\n\t *  but no token is consumed during recovery...another error is found,\r\n\t *  ad nauseum.  This is a failsafe mechanism to guarantee that at least\r\n\t *  one token/tree node is consumed for two errors.\r\n\t */\r\n\tprotected lastErrorIndex: number = -1;\r\n\r\n\tprotected lastErrorStates?: IntervalSet;\r\n\r\n\t/**\r\n\t * This field is used to propagate information about the lookahead following\r\n\t * the previous match. Since prediction prefers completing the current rule\r\n\t * to error recovery efforts, error reporting may occur later than the\r\n\t * original point where it was discoverable. The original context is used to\r\n\t * compute the true expected sets as though the reporting occurred as early\r\n\t * as possible.\r\n\t */\r\n\tprotected nextTokensContext?: ParserRuleContext;\r\n\r\n\t/**\r\n\t * @see #nextTokensContext\r\n\t */\r\n\tprotected nextTokensState: number = ATNState.INVALID_STATE_NUMBER;\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation simply calls {@link #endErrorCondition} to\r\n\t * ensure that the handler is not in error recovery mode.\r\n\t */\r\n\t@Override\r\n\tpublic reset(recognizer: Parser): void {\r\n\t\tthis.endErrorCondition(recognizer);\r\n\t}\r\n\r\n\t/**\r\n\t * This method is called to enter error recovery mode when a recognition\r\n\t * exception is reported.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t */\r\n\tprotected beginErrorCondition(@NotNull recognizer: Parser): void {\r\n\t\tthis.errorRecoveryMode = true;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tpublic inErrorRecoveryMode(recognizer: Parser): boolean {\r\n\t\treturn this.errorRecoveryMode;\r\n\t}\r\n\r\n\t/**\r\n\t * This method is called to leave error recovery mode after recovering from\r\n\t * a recognition exception.\r\n\t *\r\n\t * @param recognizer\r\n\t */\r\n\tprotected endErrorCondition(@NotNull recognizer: Parser): void {\r\n\t\tthis.errorRecoveryMode = false;\r\n\t\tthis.lastErrorStates = undefined;\r\n\t\tthis.lastErrorIndex = -1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation simply calls {@link #endErrorCondition}.\r\n\t */\r\n\t@Override\r\n\tpublic reportMatch(recognizer: Parser): void {\r\n\t\tthis.endErrorCondition(recognizer);\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation returns immediately if the handler is already\r\n\t * in error recovery mode. Otherwise, it calls {@link #beginErrorCondition}\r\n\t * and dispatches the reporting task based on the runtime type of `e`\r\n\t * according to the following table.\r\n\t *\r\n\t * * {@link NoViableAltException}: Dispatches the call to\r\n\t *   {@link #reportNoViableAlternative}\r\n\t * * {@link InputMismatchException}: Dispatches the call to\r\n\t *   {@link #reportInputMismatch}\r\n\t * * {@link FailedPredicateException}: Dispatches the call to\r\n\t *   {@link #reportFailedPredicate}\r\n\t * * All other types: calls {@link Parser#notifyErrorListeners} to report\r\n\t *   the exception\r\n\t */\r\n\t@Override\r\n\tpublic reportError(\r\n\t\trecognizer: Parser,\r\n\t\te: RecognitionException): void {\r\n\t\t// if we've already reported an error and have not matched a token\r\n\t\t// yet successfully, don't report any errors.\r\n\t\tif (this.inErrorRecoveryMode(recognizer)) {\r\n//\t\t\tSystem.err.print(\"[SPURIOUS] \");\r\n\t\t\treturn; // don't report spurious errors\r\n\t\t}\r\n\t\tthis.beginErrorCondition(recognizer);\r\n\t\tif (e instanceof NoViableAltException) {\r\n\t\t\tthis.reportNoViableAlternative(recognizer, e);\r\n\t\t}\r\n\t\telse if (e instanceof InputMismatchException) {\r\n\t\t\tthis.reportInputMismatch(recognizer, e);\r\n\t\t}\r\n\t\telse if (e instanceof FailedPredicateException) {\r\n\t\t\tthis.reportFailedPredicate(recognizer, e);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tconsole.error(`unknown recognition error type: ${e}`);\r\n\t\t\tthis.notifyErrorListeners(recognizer, e.toString(), e);\r\n\t\t}\r\n\t}\r\n\r\n\tprotected notifyErrorListeners(@NotNull recognizer: Parser, message: string, e: RecognitionException): void {\r\n\t\tlet offendingToken: Token | null | undefined = e.getOffendingToken(recognizer);\r\n\t\tif (offendingToken === undefined) {\r\n\t\t\t// Pass null to notifyErrorListeners so it in turn calls the error listeners with undefined as the offending\r\n\t\t\t// token. If we passed undefined, it would instead call the listeners with currentToken from the parser.\r\n\t\t\toffendingToken = null;\r\n\t\t}\r\n\r\n\t\trecognizer.notifyErrorListeners(message, offendingToken, e);\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation resynchronizes the parser by consuming tokens\r\n\t * until we find one in the resynchronization set--loosely the set of tokens\r\n\t * that can follow the current rule.\r\n\t */\r\n\t@Override\r\n\tpublic recover(recognizer: Parser, e: RecognitionException): void {\r\n//\t\tSystem.out.println(\"recover in \"+recognizer.getRuleInvocationStack()+\r\n//\t\t\t\t\t\t   \" index=\"+recognizer.inputStream.index+\r\n//\t\t\t\t\t\t   \", lastErrorIndex=\"+\r\n//\t\t\t\t\t\t   lastErrorIndex+\r\n//\t\t\t\t\t\t   \", states=\"+lastErrorStates);\r\n\t\tif (this.lastErrorIndex === recognizer.inputStream.index &&\r\n\t\t\tthis.lastErrorStates &&\r\n\t\t\tthis.lastErrorStates.contains(recognizer.state)) {\r\n\t\t\t// uh oh, another error at same token index and previously-visited\r\n\t\t\t// state in ATN; must be a case where LT(1) is in the recovery\r\n\t\t\t// token set so nothing got consumed. Consume a single token\r\n\t\t\t// at least to prevent an infinite loop; this is a failsafe.\r\n//\t\t\tSystem.err.println(\"seen error condition before index=\"+\r\n//\t\t\t\t\t\t\t   lastErrorIndex+\", states=\"+lastErrorStates);\r\n//\t\t\tSystem.err.println(\"FAILSAFE consumes \"+recognizer.getTokenNames()[recognizer.inputStream.LA(1)]);\r\n\t\t\trecognizer.consume();\r\n\t\t}\r\n\t\tthis.lastErrorIndex = recognizer.inputStream.index;\r\n\t\tif (!this.lastErrorStates) {\r\n\t\t\tthis.lastErrorStates = new IntervalSet();\r\n\t\t}\r\n\t\tthis.lastErrorStates.add(recognizer.state);\r\n\t\tlet followSet: IntervalSet = this.getErrorRecoverySet(recognizer);\r\n\t\tthis.consumeUntil(recognizer, followSet);\r\n\t}\r\n\r\n\t/**\r\n\t * The default implementation of {@link ANTLRErrorStrategy#sync} makes sure\r\n\t * that the current lookahead symbol is consistent with what were expecting\r\n\t * at this point in the ATN. You can call this anytime but ANTLR only\r\n\t * generates code to check before subrules/loops and each iteration.\r\n\t *\r\n\t * Implements Jim Idle's magic sync mechanism in closures and optional\r\n\t * subrules. E.g.,\r\n\t *\r\n\t * ```antlr\r\n\t * a : sync ( stuff sync )* ;\r\n\t * sync : {consume to what can follow sync} ;\r\n\t * ```\r\n\t *\r\n\t * At the start of a sub rule upon error, {@link #sync} performs single\r\n\t * token deletion, if possible. If it can't do that, it bails on the current\r\n\t * rule and uses the default error recovery, which consumes until the\r\n\t * resynchronization set of the current rule.\r\n\t *\r\n\t * If the sub rule is optional (`(...)?`, `(...)*`, or block\r\n\t * with an empty alternative), then the expected set includes what follows\r\n\t * the subrule.\r\n\t *\r\n\t * During loop iteration, it consumes until it sees a token that can start a\r\n\t * sub rule or what follows loop. Yes, that is pretty aggressive. We opt to\r\n\t * stay in the loop as long as possible.\r\n\t *\r\n\t * **ORIGINS**\r\n\t *\r\n\t * Previous versions of ANTLR did a poor job of their recovery within loops.\r\n\t * A single mismatch token or missing token would force the parser to bail\r\n\t * out of the entire rules surrounding the loop. So, for rule\r\n\t *\r\n\t * ```antlr\r\n\t * classDef : 'class' ID '{' member* '}'\r\n\t * ```\r\n\t *\r\n\t * input with an extra token between members would force the parser to\r\n\t * consume until it found the next class definition rather than the next\r\n\t * member definition of the current class.\r\n\t *\r\n\t * This functionality cost a little bit of effort because the parser has to\r\n\t * compare token set at the start of the loop and at each iteration. If for\r\n\t * some reason speed is suffering for you, you can turn off this\r\n\t * functionality by simply overriding this method as a blank { }.\r\n\t */\r\n\t@Override\r\n\tpublic sync(recognizer: Parser): void {\r\n\t\tlet s: ATNState = recognizer.interpreter.atn.states[recognizer.state];\r\n//\t\tSystem.err.println(\"sync @ \"+s.stateNumber+\"=\"+s.getClass().getSimpleName());\r\n\t\t// If already recovering, don't try to sync\r\n\t\tif (this.inErrorRecoveryMode(recognizer)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet tokens: TokenStream = recognizer.inputStream;\r\n\t\tlet la: number = tokens.LA(1);\r\n\r\n\t\t// try cheaper subset first; might get lucky. seems to shave a wee bit off\r\n\t\tlet nextTokens: IntervalSet = recognizer.atn.nextTokens(s);\r\n\t\tif (nextTokens.contains(la)) {\r\n\t\t\t// We are sure the token matches\r\n\t\t\tthis.nextTokensContext = undefined;\r\n\t\t\tthis.nextTokensState = ATNState.INVALID_STATE_NUMBER;\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (nextTokens.contains(Token.EPSILON)) {\r\n\t\t\tif (this.nextTokensContext === undefined) {\r\n\t\t\t\t// It's possible the next token won't match; information tracked\r\n\t\t\t\t// by sync is restricted for performance.\r\n\t\t\t\tthis.nextTokensContext = recognizer.context;\r\n\t\t\t\tthis.nextTokensState = recognizer.state;\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tswitch (s.stateType) {\r\n\t\tcase ATNStateType.BLOCK_START:\r\n\t\tcase ATNStateType.STAR_BLOCK_START:\r\n\t\tcase ATNStateType.PLUS_BLOCK_START:\r\n\t\tcase ATNStateType.STAR_LOOP_ENTRY:\r\n\t\t\t// report error and recover if possible\r\n\t\t\tif (this.singleTokenDeletion(recognizer)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthrow new InputMismatchException(recognizer);\r\n\r\n\t\tcase ATNStateType.PLUS_LOOP_BACK:\r\n\t\tcase ATNStateType.STAR_LOOP_BACK:\r\n//\t\t\tSystem.err.println(\"at loop back: \"+s.getClass().getSimpleName());\r\n\t\t\tthis.reportUnwantedToken(recognizer);\r\n\t\t\tlet expecting: IntervalSet = recognizer.getExpectedTokens();\r\n\t\t\tlet whatFollowsLoopIterationOrRule: IntervalSet =\r\n\t\t\t\texpecting.or(this.getErrorRecoverySet(recognizer));\r\n\t\t\tthis.consumeUntil(recognizer, whatFollowsLoopIterationOrRule);\r\n\t\t\tbreak;\r\n\r\n\t\tdefault:\r\n\t\t\t// do nothing if we can't identify the exact kind of ATN state\r\n\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * This is called by {@link #reportError} when the exception is a\r\n\t * {@link NoViableAltException}.\r\n\t *\r\n\t * @see #reportError\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param e the recognition exception\r\n\t */\r\n\tprotected reportNoViableAlternative(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull e: NoViableAltException): void {\r\n\t\tlet tokens: TokenStream = recognizer.inputStream;\r\n\t\tlet input: string;\r\n\t\tif (tokens) {\r\n\t\t\tif (e.startToken.type === Token.EOF) {\r\n\t\t\t\tinput = \"<EOF>\";\r\n\t\t\t} else {\r\n\t\t\t\tinput = tokens.getTextFromRange(e.startToken, e.getOffendingToken());\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tinput = \"<unknown input>\";\r\n\t\t}\r\n\t\tlet msg: string = \"no viable alternative at input \" + this.escapeWSAndQuote(input);\r\n\t\tthis.notifyErrorListeners(recognizer, msg, e);\r\n\t}\r\n\r\n\t/**\r\n\t * This is called by {@link #reportError} when the exception is an\r\n\t * {@link InputMismatchException}.\r\n\t *\r\n\t * @see #reportError\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param e the recognition exception\r\n\t */\r\n\tprotected reportInputMismatch(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull e: InputMismatchException): void {\r\n\t\tlet expected = e.expectedTokens;\r\n\t\tlet expectedString = expected ? expected.toStringVocabulary(recognizer.vocabulary) : \"\";\r\n\t\tlet msg: string = \"mismatched input \" + this.getTokenErrorDisplay(e.getOffendingToken(recognizer)) +\r\n\t\t\t\" expecting \" + expectedString;\r\n\t\tthis.notifyErrorListeners(recognizer, msg, e);\r\n\t}\r\n\r\n\t/**\r\n\t * This is called by {@link #reportError} when the exception is a\r\n\t * {@link FailedPredicateException}.\r\n\t *\r\n\t * @see #reportError\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @param e the recognition exception\r\n\t */\r\n\tprotected reportFailedPredicate(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull e: FailedPredicateException): void {\r\n\t\tlet ruleName: string = recognizer.ruleNames[recognizer.context.ruleIndex];\r\n\t\tlet msg: string = \"rule \" + ruleName + \" \" + e.message;\r\n\t\tthis.notifyErrorListeners(recognizer, msg, e);\r\n\t}\r\n\r\n\t/**\r\n\t * This method is called to report a syntax error which requires the removal\r\n\t * of a token from the input stream. At the time this method is called, the\r\n\t * erroneous symbol is current `LT(1)` symbol and has not yet been\r\n\t * removed from the input stream. When this method returns,\r\n\t * `recognizer` is in error recovery mode.\r\n\t *\r\n\t * This method is called when {@link #singleTokenDeletion} identifies\r\n\t * single-token deletion as a viable recovery strategy for a mismatched\r\n\t * input error.\r\n\t *\r\n\t * The default implementation simply returns if the handler is already in\r\n\t * error recovery mode. Otherwise, it calls {@link #beginErrorCondition} to\r\n\t * enter error recovery mode, followed by calling\r\n\t * {@link Parser#notifyErrorListeners}.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t */\r\n\tprotected reportUnwantedToken(@NotNull recognizer: Parser): void {\r\n\t\tif (this.inErrorRecoveryMode(recognizer)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tthis.beginErrorCondition(recognizer);\r\n\r\n\t\tlet t: Token = recognizer.currentToken;\r\n\t\tlet tokenName: string = this.getTokenErrorDisplay(t);\r\n\t\tlet expecting: IntervalSet = this.getExpectedTokens(recognizer);\r\n\t\tlet msg: string = \"extraneous input \" + tokenName + \" expecting \" +\r\n\t\t\texpecting.toStringVocabulary(recognizer.vocabulary);\r\n\t\trecognizer.notifyErrorListeners(msg, t, undefined);\r\n\t}\r\n\r\n\t/**\r\n\t * This method is called to report a syntax error which requires the\r\n\t * insertion of a missing token into the input stream. At the time this\r\n\t * method is called, the missing token has not yet been inserted. When this\r\n\t * method returns, `recognizer` is in error recovery mode.\r\n\t *\r\n\t * This method is called when {@link #singleTokenInsertion} identifies\r\n\t * single-token insertion as a viable recovery strategy for a mismatched\r\n\t * input error.\r\n\t *\r\n\t * The default implementation simply returns if the handler is already in\r\n\t * error recovery mode. Otherwise, it calls {@link #beginErrorCondition} to\r\n\t * enter error recovery mode, followed by calling\r\n\t * {@link Parser#notifyErrorListeners}.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t */\r\n\tprotected reportMissingToken(@NotNull recognizer: Parser): void {\r\n\t\tif (this.inErrorRecoveryMode(recognizer)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tthis.beginErrorCondition(recognizer);\r\n\r\n\t\tlet t: Token = recognizer.currentToken;\r\n\t\tlet expecting: IntervalSet = this.getExpectedTokens(recognizer);\r\n\t\tlet msg: string = \"missing \" + expecting.toStringVocabulary(recognizer.vocabulary) +\r\n\t\t\t\" at \" + this.getTokenErrorDisplay(t);\r\n\r\n\t\trecognizer.notifyErrorListeners(msg, t, undefined);\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation attempts to recover from the mismatched input\r\n\t * by using single token insertion and deletion as described below. If the\r\n\t * recovery attempt fails, this method\r\n\t * {@link InputMismatchException}.\r\n\t *\r\n\t * **EXTRA TOKEN** (single token deletion)\r\n\t *\r\n\t * `LA(1)` is not what we are looking for. If `LA(2)` has the\r\n\t * right token, however, then assume `LA(1)` is some extra spurious\r\n\t * token and delete it. Then consume and return the next token (which was\r\n\t * the `LA(2)` token) as the successful result of the match operation.\r\n\t *\r\n\t * This recovery strategy is implemented by {@link #singleTokenDeletion}.\r\n\t *\r\n\t * **MISSING TOKEN** (single token insertion)\r\n\t *\r\n\t * If current token (at `LA(1)`) is consistent with what could come\r\n\t * after the expected `LA(1)` token, then assume the token is missing\r\n\t * and use the parser's {@link TokenFactory} to create it on the fly. The\r\n\t * \"insertion\" is performed by returning the created token as the successful\r\n\t * result of the match operation.\r\n\t *\r\n\t * This recovery strategy is implemented by {@link #singleTokenInsertion}.\r\n\t *\r\n\t * **EXAMPLE**\r\n\t *\r\n\t * For example, Input `i=(3;` is clearly missing the `')'`. When\r\n\t * the parser returns from the nested call to `expr`, it will have\r\n\t * call chain:\r\n\t *\r\n\t * ```\r\n\t * stat → expr → atom\r\n\t * ```\r\n\t *\r\n\t * and it will be trying to match the `')'` at this point in the\r\n\t * derivation:\r\n\t *\r\n\t * ```\r\n\t * => ID '=' '(' INT ')' ('+' atom)* ';'\r\n\t *                    ^\r\n\t * ```\r\n\t *\r\n\t * The attempt to match `')'` will fail when it sees `';'` and\r\n\t * call {@link #recoverInline}. To recover, it sees that `LA(1)==';'`\r\n\t * is in the set of tokens that can follow the `')'` token reference\r\n\t * in rule `atom`. It can assume that you forgot the `')'`.\r\n\t */\r\n\t@Override\r\n\tpublic recoverInline(recognizer: Parser): Token {\r\n\t\t// SINGLE TOKEN DELETION\r\n\t\tlet matchedSymbol = this.singleTokenDeletion(recognizer);\r\n\t\tif (matchedSymbol) {\r\n\t\t\t// we have deleted the extra token.\r\n\t\t\t// now, move past ttype token as if all were ok\r\n\t\t\trecognizer.consume();\r\n\t\t\treturn matchedSymbol;\r\n\t\t}\r\n\r\n\t\t// SINGLE TOKEN INSERTION\r\n\t\tif (this.singleTokenInsertion(recognizer)) {\r\n\t\t\treturn this.getMissingSymbol(recognizer);\r\n\t\t}\r\n\r\n\t\t// even that didn't work; must throw the exception\r\n\t\tif (this.nextTokensContext === undefined) {\r\n\t\t\tthrow new InputMismatchException(recognizer);\r\n\t\t} else {\r\n\t\t\tthrow new InputMismatchException(recognizer, this.nextTokensState, this.nextTokensContext);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * This method implements the single-token insertion inline error recovery\r\n\t * strategy. It is called by {@link #recoverInline} if the single-token\r\n\t * deletion strategy fails to recover from the mismatched input. If this\r\n\t * method returns `true`, `recognizer` will be in error recovery\r\n\t * mode.\r\n\t *\r\n\t * This method determines whether or not single-token insertion is viable by\r\n\t * checking if the `LA(1)` input symbol could be successfully matched\r\n\t * if it were instead the `LA(2)` symbol. If this method returns\r\n\t * `true`, the caller is responsible for creating and inserting a\r\n\t * token with the correct type to produce this behavior.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @returns `true` if single-token insertion is a viable recovery\r\n\t * strategy for the current mismatched input, otherwise `false`\r\n\t */\r\n\tprotected singleTokenInsertion(@NotNull recognizer: Parser): boolean {\r\n\t\tlet currentSymbolType: number = recognizer.inputStream.LA(1);\r\n\t\t// if current token is consistent with what could come after current\r\n\t\t// ATN state, then we know we're missing a token; error recovery\r\n\t\t// is free to conjure up and insert the missing token\r\n\t\tlet currentState = recognizer.interpreter.atn.states[recognizer.state];\r\n\t\tlet next: ATNState = currentState.transition(0).target;\r\n\t\tlet atn: ATN = recognizer.interpreter.atn;\r\n\t\tlet expectingAtLL2: IntervalSet = atn.nextTokens(next, PredictionContext.fromRuleContext(atn, recognizer.context));\r\n//\t\tconsole.warn(\"LT(2) set=\"+expectingAtLL2.toString(recognizer.getTokenNames()));\r\n\t\tif (expectingAtLL2.contains(currentSymbolType)) {\r\n\t\t\tthis.reportMissingToken(recognizer);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * This method implements the single-token deletion inline error recovery\r\n\t * strategy. It is called by {@link #recoverInline} to attempt to recover\r\n\t * from mismatched input. If this method returns `undefined`, the parser and error\r\n\t * handler state will not have changed. If this method returns non-`undefined`,\r\n\t * `recognizer` will *not* be in error recovery mode since the\r\n\t * returned token was a successful match.\r\n\t *\r\n\t * If the single-token deletion is successful, this method calls\r\n\t * {@link #reportUnwantedToken} to report the error, followed by\r\n\t * {@link Parser#consume} to actually \"delete\" the extraneous token. Then,\r\n\t * before returning {@link #reportMatch} is called to signal a successful\r\n\t * match.\r\n\t *\r\n\t * @param recognizer the parser instance\r\n\t * @returns the successfully matched {@link Token} instance if single-token\r\n\t * deletion successfully recovers from the mismatched input, otherwise\r\n\t * `undefined`\r\n\t */\r\n\tprotected singleTokenDeletion(@NotNull recognizer: Parser): Token | undefined {\r\n\t\tlet nextTokenType: number = recognizer.inputStream.LA(2);\r\n\t\tlet expecting: IntervalSet = this.getExpectedTokens(recognizer);\r\n\t\tif (expecting.contains(nextTokenType)) {\r\n\t\t\tthis.reportUnwantedToken(recognizer);\r\n\t\t\t/*\r\n\t\t\tSystem.err.println(\"recoverFromMismatchedToken deleting \"+\r\n\t\t\t\t\t\t\t   ((TokenStream)recognizer.inputStream).LT(1)+\r\n\t\t\t\t\t\t\t   \" since \"+((TokenStream)recognizer.inputStream).LT(2)+\r\n\t\t\t\t\t\t\t   \" is what we want\");\r\n\t\t\t*/\r\n\t\t\trecognizer.consume(); // simply delete extra token\r\n\t\t\t// we want to return the token we're actually matching\r\n\t\t\tlet matchedSymbol: Token = recognizer.currentToken;\r\n\t\t\tthis.reportMatch(recognizer);  // we know current token is correct\r\n\t\t\treturn matchedSymbol;\r\n\t\t}\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/** Conjure up a missing token during error recovery.\r\n\t *\r\n\t *  The recognizer attempts to recover from single missing\r\n\t *  symbols. But, actions might refer to that missing symbol.\r\n\t *  For example, x=ID {f($x);}. The action clearly assumes\r\n\t *  that there has been an identifier matched previously and that\r\n\t *  $x points at that token. If that token is missing, but\r\n\t *  the next token in the stream is what we want we assume that\r\n\t *  this token is missing and we keep going. Because we\r\n\t *  have to return some token to replace the missing token,\r\n\t *  we have to conjure one up. This method gives the user control\r\n\t *  over the tokens returned for missing tokens. Mostly,\r\n\t *  you will want to create something special for identifier\r\n\t *  tokens. For literals such as '{' and ',', the default\r\n\t *  action in the parser or tree parser works. It simply creates\r\n\t *  a CommonToken of the appropriate type. The text will be the token.\r\n\t *  If you change what tokens must be created by the lexer,\r\n\t *  override this method to create the appropriate tokens.\r\n\t */\r\n\t@NotNull\r\n\tprotected getMissingSymbol(@NotNull recognizer: Parser): Token {\r\n\t\tlet currentSymbol: Token = recognizer.currentToken;\r\n\t\tlet expecting: IntervalSet = this.getExpectedTokens(recognizer);\r\n\t\tlet expectedTokenType: number = Token.INVALID_TYPE;\r\n\t\tif (!expecting.isNil) {\r\n\t\t\t// get any element\r\n\t\t\texpectedTokenType = expecting.minElement;\r\n\t\t}\r\n\r\n\t\tlet tokenText: string;\r\n\t\tif (expectedTokenType === Token.EOF) {\r\n\t\t\ttokenText = \"<missing EOF>\";\r\n\t\t} else {\r\n\t\t\ttokenText = \"<missing \" + recognizer.vocabulary.getDisplayName(expectedTokenType) + \">\";\r\n\t\t}\r\n\t\tlet current: Token = currentSymbol;\r\n\t\tlet lookback = recognizer.inputStream.tryLT(-1);\r\n\t\tif (current.type === Token.EOF && lookback != null) {\r\n\t\t\tcurrent = lookback;\r\n\t\t}\r\n\r\n\t\treturn this.constructToken(recognizer.inputStream.tokenSource, expectedTokenType, tokenText, current);\r\n\t}\r\n\r\n\tprotected constructToken(\r\n\t\ttokenSource: TokenSource,\r\n\t\texpectedTokenType: number,\r\n\t\ttokenText: string,\r\n\t\tcurrent: Token): Token {\r\n\t\tlet factory: TokenFactory = tokenSource.tokenFactory;\r\n\t\tlet x = current.tokenSource;\r\n\t\tlet stream = x ? x.inputStream : undefined;\r\n\r\n\t\treturn factory.create(\r\n\t\t\t{ source: tokenSource, stream },\r\n\t\t\texpectedTokenType, tokenText,\r\n\t\t\tToken.DEFAULT_CHANNEL,\r\n\t\t\t-1, -1,\r\n\t\t\tcurrent.line, current.charPositionInLine);\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected getExpectedTokens(@NotNull recognizer: Parser): IntervalSet {\r\n\t\treturn recognizer.getExpectedTokens();\r\n\t}\r\n\r\n\t/** How should a token be displayed in an error message? The default\r\n\t *  is to display just the text, but during development you might\r\n\t *  want to have a lot of information spit out.  Override in that case\r\n\t *  to use t.toString() (which, for CommonToken, dumps everything about\r\n\t *  the token). This is better than forcing you to override a method in\r\n\t *  your token objects because you don't have to go modify your lexer\r\n\t *  so that it creates a new Java type.\r\n\t */\r\n\tprotected getTokenErrorDisplay(t: Token | undefined): string {\r\n\t\tif (!t) {\r\n\t\t\treturn \"<no token>\";\r\n\t\t}\r\n\t\tlet s = this.getSymbolText(t);\r\n\t\tif (!s) {\r\n\t\t\tif (this.getSymbolType(t) === Token.EOF) {\r\n\t\t\t\ts = \"<EOF>\";\r\n\t\t\t} else {\r\n\t\t\t\ts = `<${this.getSymbolType(t)}>`;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.escapeWSAndQuote(s);\r\n\t}\r\n\r\n\tprotected getSymbolText(@NotNull symbol: Token): string | undefined {\r\n\t\treturn symbol.text;\r\n\t}\r\n\r\n\tprotected getSymbolType(@NotNull symbol: Token): number {\r\n\t\treturn symbol.type;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprotected escapeWSAndQuote(@NotNull s: string): string {\r\n//\t\tif ( s==null ) return s;\r\n\t\ts = s.replace(\"\\n\", \"\\\\n\");\r\n\t\ts = s.replace(\"\\r\", \"\\\\r\");\r\n\t\ts = s.replace(\"\\t\", \"\\\\t\");\r\n\t\treturn \"'\" + s + \"'\";\r\n\t}\r\n\r\n\t/*  Compute the error recovery set for the current rule.  During\r\n\t *  rule invocation, the parser pushes the set of tokens that can\r\n\t *  follow that rule reference on the stack; this amounts to\r\n\t *  computing FIRST of what follows the rule reference in the\r\n\t *  enclosing rule. See LinearApproximator.FIRST().\r\n\t *  This local follow set only includes tokens\r\n\t *  from within the rule; i.e., the FIRST computation done by\r\n\t *  ANTLR stops at the end of a rule.\r\n\t *\r\n\t *  EXAMPLE\r\n\t *\r\n\t *  When you find a \"no viable alt exception\", the input is not\r\n\t *  consistent with any of the alternatives for rule r.  The best\r\n\t *  thing to do is to consume tokens until you see something that\r\n\t *  can legally follow a call to r *or* any rule that called r.\r\n\t *  You don't want the exact set of viable next tokens because the\r\n\t *  input might just be missing a token--you might consume the\r\n\t *  rest of the input looking for one of the missing tokens.\r\n\t *\r\n\t *  Consider grammar:\r\n\t *\r\n\t *  a : '[' b ']'\r\n\t *    | '(' b ')'\r\n\t *    ;\r\n\t *  b : c '^' INT ;\r\n\t *  c : ID\r\n\t *    | INT\r\n\t *    ;\r\n\t *\r\n\t *  At each rule invocation, the set of tokens that could follow\r\n\t *  that rule is pushed on a stack.  Here are the various\r\n\t *  context-sensitive follow sets:\r\n\t *\r\n\t *  FOLLOW(b1_in_a) = FIRST(']') = ']'\r\n\t *  FOLLOW(b2_in_a) = FIRST(')') = ')'\r\n\t *  FOLLOW(c_in_b) = FIRST('^') = '^'\r\n\t *\r\n\t *  Upon erroneous input \"[]\", the call chain is\r\n\t *\r\n\t *  a -> b -> c\r\n\t *\r\n\t *  and, hence, the follow context stack is:\r\n\t *\r\n\t *  depth     follow set       start of rule execution\r\n\t *    0         <EOF>                    a (from main())\r\n\t *    1          ']'                     b\r\n\t *    2          '^'                     c\r\n\t *\r\n\t *  Notice that ')' is not included, because b would have to have\r\n\t *  been called from a different context in rule a for ')' to be\r\n\t *  included.\r\n\t *\r\n\t *  For error recovery, we cannot consider FOLLOW(c)\r\n\t *  (context-sensitive or otherwise).  We need the combined set of\r\n\t *  all context-sensitive FOLLOW sets--the set of all tokens that\r\n\t *  could follow any reference in the call chain.  We need to\r\n\t *  resync to one of those tokens.  Note that FOLLOW(c)='^' and if\r\n\t *  we resync'd to that token, we'd consume until EOF.  We need to\r\n\t *  sync to context-sensitive FOLLOWs for a, b, and c: {']','^'}.\r\n\t *  In this case, for input \"[]\", LA(1) is ']' and in the set, so we would\r\n\t *  not consume anything. After printing an error, rule c would\r\n\t *  return normally.  Rule b would not find the required '^' though.\r\n\t *  At this point, it gets a mismatched token error and\r\n\t *  exception (since LA(1) is not in the viable following token\r\n\t *  set).  The rule exception handler tries to recover, but finds\r\n\t *  the same recovery set and doesn't consume anything.  Rule b\r\n\t *  exits normally returning to rule a.  Now it finds the ']' (and\r\n\t *  with the successful match exits errorRecovery mode).\r\n\t *\r\n\t *  So, you can see that the parser walks up the call chain looking\r\n\t *  for the token that was a member of the recovery set.\r\n\t *\r\n\t *  Errors are not generated in errorRecovery mode.\r\n\t *\r\n\t *  ANTLR's error recovery mechanism is based upon original ideas:\r\n\t *\r\n\t *  \"Algorithms + Data Structures = Programs\" by Niklaus Wirth\r\n\t *\r\n\t *  and\r\n\t *\r\n\t *  \"A note on error recovery in recursive descent parsers\":\r\n\t *  http://portal.acm.org/citation.cfm?id=947902.947905\r\n\t *\r\n\t *  Later, Josef Grosch had some good ideas:\r\n\t *\r\n\t *  \"Efficient and Comfortable Error Recovery in Recursive Descent\r\n\t *  Parsers\":\r\n\t *  ftp://www.cocolab.com/products/cocktail/doca4.ps/ell.ps.zip\r\n\t *\r\n\t *  Like Grosch I implement context-sensitive FOLLOW sets that are combined\r\n\t *  at run-time upon error to avoid overhead during parsing.\r\n\t */\r\n\t@NotNull\r\n\tprotected getErrorRecoverySet(@NotNull recognizer: Parser): IntervalSet {\r\n\t\tlet atn: ATN = recognizer.interpreter.atn;\r\n\t\tlet ctx: RuleContext | undefined = recognizer.context;\r\n\t\tlet recoverSet: IntervalSet = new IntervalSet();\r\n\t\twhile (ctx && ctx.invokingState >= 0) {\r\n\t\t\t// compute what follows who invoked us\r\n\t\t\tlet invokingState: ATNState = atn.states[ctx.invokingState];\r\n\t\t\tlet rt = invokingState.transition(0) as RuleTransition;\r\n\t\t\tlet follow: IntervalSet = atn.nextTokens(rt.followState);\r\n\t\t\trecoverSet.addAll(follow);\r\n\t\t\tctx = ctx._parent;\r\n\t\t}\r\n\t\trecoverSet.remove(Token.EPSILON);\r\n//\t\tSystem.out.println(\"recover set \"+recoverSet.toString(recognizer.getTokenNames()));\r\n\t\treturn recoverSet;\r\n\t}\r\n\r\n\t/** Consume tokens until one matches the given token set. */\r\n\tprotected consumeUntil(@NotNull recognizer: Parser, @NotNull set: IntervalSet): void {\r\n//\t\tSystem.err.println(\"consumeUntil(\"+set.toString(recognizer.getTokenNames())+\")\");\r\n\t\tlet ttype: number = recognizer.inputStream.LA(1);\r\n\t\twhile (ttype !== Token.EOF && !set.contains(ttype)) {\r\n\t\t\t//System.out.println(\"consume during recover LA(1)=\"+getTokenNames()[input.LA(1)]);\r\n//\t\t\trecognizer.inputStream.consume();\r\n\t\t\trecognizer.consume();\r\n\t\t\tttype = recognizer.inputStream.LA(1);\r\n\t\t}\r\n\t}\r\n}\r\n"]}