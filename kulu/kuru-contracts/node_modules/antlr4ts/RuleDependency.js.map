{"version": 3, "file": "RuleDependency.js", "sourceRoot": "", "sources": ["../../src/RuleDependency.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAOH;;;;;;;GAOG;AACH,SAAgB,cAAc,CAAC,UAAmC;IACjE,OAAO,CAAC,MAAc,EAAE,WAAwB,EAAE,kBAAsC,EAAE,EAAE;QAC3F,sBAAsB;IACvB,CAAC,CAAC;AACH,CAAC;AAJD,wCAIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.6271221-07:00\r\n\r\nimport { Dependents } from \"./Dependents\";\r\nimport { Parser } from \"./Parser\";\r\n\r\n/**\r\n * Declares a dependency upon a grammar rule, along with a set of zero or more dependent rules.\r\n *\r\n * Version numbers within a grammar should be assigned on a monotonically increasing basis to allow for accurate\r\n * tracking of dependent rules.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport function RuleDependency(dependency: DependencySpecification) {\r\n\treturn (target: object, propertyKey: PropertyKey, propertyDescriptor: PropertyDescriptor) => {\r\n\t\t// intentionally empty\r\n\t};\r\n}\r\n\r\nexport interface DependencySpecification {\r\n\treadonly recognizer: { new (...args: any[]): Parser; };\r\n\r\n\treadonly rule: number;\r\n\r\n\treadonly version: number;\r\n\r\n\t/**\r\n\t * Specifies the set of grammar rules related to `rule` which the annotated element depends on. Even when absent\r\n\t * from this set, the annotated element is implicitly dependent upon the explicitly specified `rule`, which\r\n\t * corresponds to the `Dependents.SELF` element.\r\n\t *\r\n\t * By default, the annotated element is dependent upon the specified `rule` and its `Dependents.PARENTS`, i.e. the\r\n\t * rule within one level of context information. The parents are included since the most frequent assumption about a\r\n\t * rule is where it's used in the grammar.\r\n\t */\r\n\treadonly dependents?: Dependents[];\r\n}\r\n"]}