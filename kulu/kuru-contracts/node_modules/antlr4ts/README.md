# antlr4ts - TypeScript/JavaScript target for ANTLR 4

[![Join the chat at https://gitter.im/tunnelvisionlabs/antlr4ts](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/tunnelvisionlabs/antlr4ts?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)

[![Build status](https://ci.appveyor.com/api/projects/status/d4gpmnrkfo3tb2t1/branch/master?svg=true)](https://ci.appveyor.com/project/sharwell/antlr4ts/branch/master)

[![License](https://img.shields.io/badge/License-BSD%203--Clause-blue.svg)](./LICENSE)

## Overview

* **Releases:** See the [GitHub Releases](https://github.com/tunnelvisionlabs/antlr4ts/releases) page for release notes and
  links to the distribution
* **Feedback:** Use [GitHub Issues](https://github.com/tunnelvisionlabs/antlr4ts/issues) for issues (bugs, enhancements,
  features, and questions)

## Requirements

This project has separate requirements for developers and end users.

> :bulb: The requirements listed on this page only cover user scenarios - that is, scenarios where developers wish to
> use ANTLR 4 for parsing tasks inside of a TypeScript application. If you are interested in contributing to ANTLR 4
> itself, see [CONTRIBUTING.md](CONTRIBUTING.md) for contributor documentation.

### End user requirements

Parsers generated by the ANTLR 4 TypeScript target have a runtime dependency on the
[antlr4ts](https://www.npmjs.com/package/antlr4ts) package. The package is tested and known to work with Node.js 6.7.

### Development requirements

The tool used to generate TypeScript code from an ANTLR 4 grammar is written in Java. To fully utilize the ANTLR 4
TypeScript target (including the ability to regenerate code from a grammar file after changes are made), a Java Runtime
Environment (JRE) needs to be installed on the developer machine. The generated code itself uses several features new to
TypeScript 2.0.

* Java Runtime Environment 1.6+ (1.8+ recommended)
* TypeScript 2.0+

## Getting started

1. Install `antlr4ts` as a runtime dependency using your preferred package manager.

  ```bash
  npm install antlr4ts --save
  ```
  
  ```bash
  yarn add antlr4ts
  ```

2. Install `antlr4ts-cli` as a development dependency using your preferred package manager.

  ```bash
  npm install antlr4ts-cli --save-dev
  ```
  
  ```bash
  yarn add -D antlr4ts-cli
  ```

3. Add a grammar to your project, e.g. **path/to/MyGrammar.g4**

4. Add a script to **package.json** for compiling your grammar to TypeScript

    ```
    "scripts": {
      // ...
      "antlr4ts": "antlr4ts -visitor path/to/MyGrammar.g4"
    }
    ```

5. Use your grammar in TypeScript

    ```typescript
    import { ANTLRInputStream, CommonTokenStream } from 'antlr4ts';

    // Create the lexer and parser
    let inputStream = new ANTLRInputStream("text");
    let lexer = new MyGrammarLexer(inputStream);
    let tokenStream = new CommonTokenStream(lexer);
    let parser = new MyGrammarParser(tokenStream);

    // Parse the input, where `compilationUnit` is whatever entry point you defined
    let tree = parser.compilationUnit();
    ```

    The two main ways to inspect the tree are by using a listener or a visitor, you can read about the differences between the two [here](https://github.com/antlr/antlr4/blob/master/doc/listeners.md).

    ###### Listener Approach

    ```typescript
    // ...
    import { MyGrammarParserListener } from './MyGrammarParserListener'
    import { FunctionDeclarationContext } from './MyGrammarParser'
    import { ParseTreeWalker } from 'antlr4ts/tree/ParseTreeWalker'


    class EnterFunctionListener implements MyGrammarParserListener {
      // Assuming a parser rule with name: `functionDeclaration`
      enterFunctionDeclaration(context: FunctionDeclarationContext) {
        console.log(`Function start line number ${context._start.line}`)
        // ...
      }

      // other enterX functions...
    }

    // Create the listener
    const listener: MyGrammarParserListener = new EnterFunctionListener();
    // Use the entry point for listeners
    ParseTreeWalker.DEFAULT.walk(listener, tree)
    ```

    ###### Visitor Approach

    Note you must pass the `-visitor` flag to antlr4ts to get the generated visitor file.

    ```typescript
    // ...
    import { MyGrammarParserVisitor } from './MyGrammarParserVisitor'
    import { AbstractParseTreeVisitor } from 'antlr4ts/tree/AbstractParseTreeVisitor'

    // Extend the AbstractParseTreeVisitor to get default visitor behaviour
    class CountFunctionsVisitor extends AbstractParseTreeVisitor<number> implements MyGrammarParserVisitor<number> {

      defaultResult() {
        return 0
      }

      aggregateResult(aggregate: number, nextResult: number) {
        return aggregate + nextResult
      }

      visitFunctionDeclaration(context: FunctionDeclarationContext): number {
        return 1 + super.visitChildren(context)
      }
    }

    // Create the visitor
    const countFunctionsVisitor = new CountFunctionsVisitor()
    // Use the visitor entry point
    countFunctionsVisitor.visit(tree)
    ```
