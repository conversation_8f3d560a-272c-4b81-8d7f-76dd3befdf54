{"version": 3, "file": "CommonTokenFactory.js", "sourceRoot": "", "sources": ["../../src/CommonTokenFactory.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,+CAA4C;AAC5C,8CAA2C;AAC3C,6CAAwC;AAIxC;;;GAGG;AACH,MAAa,kBAAkB;IAiB9B;;;;;;;;OAQG;IACH,YAAY,WAAoB,KAAK;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAGM,MAAM,CACZ,MAAqD,EACrD,IAAY,EACZ,IAAwB,EACxB,OAAe,EACf,KAAa,EACb,IAAY,EACZ,IAAY,EACZ,kBAA0B;QAE1B,IAAI,CAAC,GAAgB,IAAI,yBAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/E,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;QACd,CAAC,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC1C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;YAC3D,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,YAAY,CAAC,IAAY,EAAE,IAAY;QAC7C,OAAO,IAAI,yBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;CACD;AAxBA;IADC,qBAAQ;gDAmBR;AAGD;IADC,qBAAQ;sDAGR;AAtDF,gDAuDC;AAED,WAAiB,kBAAkB;IAClC;;;;;OAKG;IACU,0BAAO,GAAiB,IAAI,kBAAkB,EAAE,CAAC;AAC/D,CAAC,EARgB,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAQlC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.3010112-07:00\r\n\r\nimport { CharStream } from \"./CharStream\";\r\nimport { CommonToken } from \"./CommonToken\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Override } from \"./Decorators\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/**\r\n * This default implementation of {@link TokenFactory} creates\r\n * {@link CommonToken} objects.\r\n */\r\nexport class CommonTokenFactory implements TokenFactory {\r\n\t/**\r\n\t * Indicates whether {@link CommonToken#setText} should be called after\r\n\t * constructing tokens to explicitly set the text. This is useful for cases\r\n\t * where the input stream might not be able to provide arbitrary substrings\r\n\t * of text from the input after the lexer creates a token (e.g. the\r\n\t * implementation of {@link CharStream#getText} in\r\n\t * {@link UnbufferedCharStream}\r\n\t * {@link UnsupportedOperationException}). Explicitly setting the token text\r\n\t * allows {@link Token#getText} to be called at any time regardless of the\r\n\t * input stream implementation.\r\n\t *\r\n\t * The default value is `false` to avoid the performance and memory\r\n\t * overhead of copying text for every token unless explicitly requested.\r\n\t */\r\n\tprotected copyText: boolean;\r\n\r\n\t/**\r\n\t * Constructs a {@link CommonTokenFactory} with the specified value for\r\n\t * {@link #copyText}.\r\n\t *\r\n\t * When `copyText` is `false`, the {@link #DEFAULT} instance\r\n\t * should be used instead of constructing a new instance.\r\n\t *\r\n\t * @param copyText The value for {@link #copyText}.\r\n\t */\r\n\tconstructor(copyText: boolean = false) {\r\n\t\tthis.copyText = copyText;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic create(\r\n\t\tsource: { source?: TokenSource, stream?: CharStream },\r\n\t\ttype: number,\r\n\t\ttext: string | undefined,\r\n\t\tchannel: number,\r\n\t\tstart: number,\r\n\t\tstop: number,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number): CommonToken {\r\n\r\n\t\tlet t: CommonToken = new CommonToken(type, text, source, channel, start, stop);\r\n\t\tt.line = line;\r\n\t\tt.charPositionInLine = charPositionInLine;\r\n\t\tif (text == null && this.copyText && source.stream != null) {\r\n\t\t\tt.text = source.stream.getText(Interval.of(start, stop));\r\n\t\t}\r\n\r\n\t\treturn t;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic createSimple(type: number, text: string): CommonToken {\r\n\t\treturn new CommonToken(type, text);\r\n\t}\r\n}\r\n\r\nexport namespace CommonTokenFactory {\r\n\t/**\r\n\t * The default {@link CommonTokenFactory} instance.\r\n\t *\r\n\t * This token factory does not explicitly copy token text when constructing\r\n\t * tokens.\r\n\t */\r\n\texport const DEFAULT: TokenFactory = new CommonTokenFactory();\r\n}\r\n"]}