{"version": 3, "file": "ParserRuleContext.js", "sourceRoot": "", "sources": ["../../src/ParserRuleContext.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AACxD,gDAA6C;AAC7C,8CAA2C;AAC3C,6CAAwC;AAKxC,+CAA4C;AAC5C,sDAAmD;AAGnD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,iBAAkB,SAAQ,yBAAW;IA0CjD,YAAY,MAA0B,EAAE,mBAA4B;QACnE,IAAI,mBAAmB,IAAI,IAAI,EAAE;YAChC,KAAK,EAAE,CAAC;SACR;aAAM;YACN,KAAK,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;SACnC;IACF,CAAC;IAEM,MAAM,CAAC,YAAY;QACzB,OAAO,iBAAiB,CAAC,KAAK,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,QAAQ,CAAC,GAAsB;QACrC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC;QAEvC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QAEvB,yCAAyC;QACzC,IAAI,GAAG,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,2CAA2C;YAC3C,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,QAAQ,EAAE;gBAC/B,IAAI,KAAK,YAAY,qBAAS,EAAE;oBAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACrB;aACD;SACD;IACF,CAAC;IAED,wCAAwC;IAEjC,SAAS,CAAC,QAA2B;QAC3C,sBAAsB;IACvB,CAAC;IACM,QAAQ,CAAC,QAA2B;QAC1C,sBAAsB;IACvB,CAAC;IAED;;;;;;;;;;OAUG;IACI,WAAW,CAAsB,CAAI;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;SACpB;aAAM;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAcM,QAAQ,CAAC,CAAqC;QACpD,IAAI,MAA2B,CAAC;QAChC,IAAI,CAAC,YAAY,2BAAY,EAAE;YAC9B,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,OAAO;SACP;aAAM,IAAI,CAAC,YAAY,yBAAW,EAAE;YACpC,2BAA2B;YAC3B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,OAAO;SACP;aAAM;YACN,uBAAuB;YACvB,CAAC,GAAG,IAAI,2BAAY,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC;SACT;IACF,CAAC;IAiBM,YAAY,CAAC,IAAuB;QAC1C,IAAI,IAAI,YAAY,qBAAS,EAAE;YAC9B,MAAM,SAAS,GAAc,IAAI,CAAC;YAClC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SACnC;aAAM;YACN,kBAAkB;YAClB,MAAM,QAAQ,GAAU,IAAI,CAAC;YAC7B,IAAI,CAAC,GAAG,IAAI,qBAAS,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC;SACT;IACF,CAAC;IAEF,6BAA6B;IAC7B,0DAA0D;IAC1D,kBAAkB;IAClB,IAAI;IAEH;;;OAGG;IACI,eAAe;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SACpB;IACF,CAAC;IAID,IAAI,MAAM;QACT,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,YAAY,iBAAiB,EAAE;YAChE,OAAO,MAAM,CAAC;SACd;QAED,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;IAClE,CAAC;IAID,mDAAmD;IAC5C,QAAQ,CAAsB,CAAS,EAAE,OAAsC;QACrF,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzD,MAAM,IAAI,UAAU,CAAC,iEAAiE,CAAC,CAAC;SACxF;QAED,IAAI,OAAO,IAAI,IAAI,EAAE;YACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACxB;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,MAAM,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACrD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,WAAW,CAAsB,CAAS,EAAE,OAAqC;QACvF,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzD,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC,wCAAwC;QAC5D,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,YAAY,OAAO,EAAE;gBACzB,CAAC,EAAE,CAAC;gBACJ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACZ,OAAO,CAAC,CAAC;iBACT;aACD;SACD;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,KAAa,EAAE,CAAS;QACvC,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACtD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,WAAW,CAAC,KAAa,EAAE,CAAS;QAC1C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzD,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,CAAC,uCAAuC;QAC3D,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,YAAY,2BAAY,EAAE;gBAC9B,IAAI,MAAM,GAAU,CAAC,CAAC,MAAM,CAAC;gBAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBAC1B,CAAC,EAAE,CAAC;oBACJ,IAAI,CAAC,KAAK,CAAC,EAAE;wBACZ,OAAO,CAAC,CAAC;qBACT;iBACD;aACD;SACD;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAEM,SAAS,CAAC,KAAa;QAC7B,IAAI,MAAM,GAAmB,EAAE,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnB,OAAO,MAAM,CAAC;SACd;QAED,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,YAAY,2BAAY,EAAE;gBAC9B,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;gBACtB,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACf;aACD;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC;IACb,CAAC;IAED,gDAAgD;IACzC,cAAc,CAA8B,CAAS,EAAE,OAAqC;QAClG,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAEM,iBAAiB,CAA8B,CAAS,EAAE,OAAqC;QACrG,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAEM,eAAe,CAA8B,OAAqC;QACxF,IAAI,QAAQ,GAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnB,OAAO,QAAQ,CAAC;SAChB;QAED,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,YAAY,OAAO,EAAE;gBACzB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;SACD;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAGD,IAAI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACjB,OAAO,mBAAQ,CAAC,OAAO,CAAC;SACxB;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAClE,OAAO,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ;SAChF;QACD,OAAO,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACH,IAAI,KAAK,KAAY,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1C;;;;OAIG;IACH,IAAI,IAAI,KAAwB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAEpD,4FAA4F;IACrF,YAAY,CAAC,UAAkB;QACrC,IAAI,KAAK,GACR,UAAU,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;QACnD,OAAO,mBAAmB,GAAG,KAAK,GAAG,GAAG;YACvC,QAAQ,GAAG,IAAI,CAAC,MAAM;YACtB,SAAS,GAAG,IAAI,CAAC,KAAK;YACtB,GAAG,CAAC;IACN,CAAC;;AAlWuB,uBAAK,GAAsB,IAAI,iBAAiB,EAAE,CAAC;AAiM3E;IAFC,qBAAQ;+CASR;AAsHD;IADC,qBAAQ;mDAGR;AAGD;IADC,qBAAQ;uDASR;AA5UF,8CAoWC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:56.6285494-07:00\r\nimport { ErrorNode } from \"./tree/ErrorNode\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Override } from \"./Decorators\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParseTree } from \"./tree/ParseTree\";\r\nimport { ParseTreeListener } from \"./tree/ParseTreeListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { TerminalNode } from \"./tree/TerminalNode\";\r\nimport { Token } from \"./Token\";\r\n\r\n/** A rule invocation record for parsing.\r\n *\r\n *  Contains all of the information about the current rule not stored in the\r\n *  RuleContext. It handles parse tree children list, Any ATN state\r\n *  tracing, and the default values available for rule invocations:\r\n *  start, stop, rule index, current alt number.\r\n *\r\n *  Subclasses made for each rule and grammar track the parameters,\r\n *  return values, locals, and labels specific to that rule. These\r\n *  are the objects that are returned from rules.\r\n *\r\n *  Note text is not an actual field of a rule return value; it is computed\r\n *  from start and stop using the input stream's toString() method.  I\r\n *  could add a ctor to this so that we can pass in and store the input\r\n *  stream, but I'm not sure we want to do that.  It would seem to be undefined\r\n *  to get the .text property anyway if the rule matches tokens from multiple\r\n *  input streams.\r\n *\r\n *  I do not use getters for fields of objects that are used simply to\r\n *  group values such as this aggregate.  The getters/setters are there to\r\n *  satisfy the superclass interface.\r\n */\r\nexport class ParserRuleContext extends RuleContext {\r\n\tprivate static readonly EMPTY: ParserRuleContext = new ParserRuleContext();\r\n\r\n\t/** If we are debugging or building a parse tree for a visitor,\r\n\t *  we need to track all of the tokens and rule invocations associated\r\n\t *  with this rule's context. This is empty for parsing w/o tree constr.\r\n\t *  operation because we don't the need to track the details about\r\n\t *  how we parse this rule.\r\n\t */\r\n\tpublic children?: ParseTree[];\r\n\r\n\t/** For debugging/tracing purposes, we want to track all of the nodes in\r\n\t *  the ATN traversed by the parser for a particular rule.\r\n\t *  This list indicates the sequence of ATN nodes used to match\r\n\t *  the elements of the children list. This list does not include\r\n\t *  ATN nodes and other rules used to match rule invocations. It\r\n\t *  traces the rule invocation node itself but nothing inside that\r\n\t *  other rule's ATN submachine.\r\n\t *\r\n\t *  There is NOT a one-to-one correspondence between the children and\r\n\t *  states list. There are typically many nodes in the ATN traversed\r\n\t *  for each element in the children list. For example, for a rule\r\n\t *  invocation there is the invoking state and the following state.\r\n\t *\r\n\t *  The parser state property updates field s and adds it to this list\r\n\t *  if we are debugging/tracing.\r\n\t *\r\n\t *  This does not trace states visited during prediction.\r\n\t */\r\n//\tpublic Array<number> states;\r\n\r\n\tpublic _start!: Token;\r\n\tpublic _stop: Token | undefined;\r\n\r\n\t/**\r\n\t * The exception that forced this rule to return. If the rule successfully\r\n\t * completed, this is `undefined`.\r\n\t */\r\n\tpublic exception?: RecognitionException;\r\n\r\n\tconstructor();\r\n\tconstructor(parent: ParserRuleContext | undefined, invokingStateNumber: number);\r\n\tconstructor(parent?: ParserRuleContext, invokingStateNumber?: number) {\r\n\t\tif (invokingStateNumber == null) {\r\n\t\t\tsuper();\r\n\t\t} else {\r\n\t\t\tsuper(parent, invokingStateNumber);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic static emptyContext(): ParserRuleContext {\r\n\t\treturn ParserRuleContext.EMPTY;\r\n\t}\r\n\r\n\t/**\r\n\t * COPY a ctx (I'm deliberately not using copy constructor) to avoid\r\n\t * confusion with creating node with parent. Does not copy children\r\n\t * (except error leaves).\r\n\t *\r\n\t * This is used in the generated parser code to flip a generic XContext\r\n\t * node for rule X to a YContext for alt label Y. In that sense, it is not\r\n\t * really a generic copy function.\r\n\t *\r\n\t * If we do an error sync() at start of a rule, we might add error nodes\r\n\t * to the generic XContext so this function must copy those nodes to the\r\n\t * YContext as well else they are lost!\r\n\t */\r\n\tpublic copyFrom(ctx: ParserRuleContext): void {\r\n\t\tthis._parent = ctx._parent;\r\n\t\tthis.invokingState = ctx.invokingState;\r\n\r\n\t\tthis._start = ctx._start;\r\n\t\tthis._stop = ctx._stop;\r\n\r\n\t\t// copy any error nodes to alt label node\r\n\t\tif (ctx.children) {\r\n\t\t\tthis.children = [];\r\n\t\t\t// reset parent pointer for any error nodes\r\n\t\t\tfor (let child of ctx.children) {\r\n\t\t\t\tif (child instanceof ErrorNode) {\r\n\t\t\t\t\tthis.addChild(child);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Double dispatch methods for listeners\r\n\r\n\tpublic enterRule(listener: ParseTreeListener): void {\r\n\t\t// intentionally empty\r\n\t}\r\n\tpublic exitRule(listener: ParseTreeListener): void {\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\t/** Add a parse tree node to this as a child.  Works for\r\n\t *  internal and leaf nodes. Does not set parent link;\r\n\t *  other add methods must do that. Other addChild methods\r\n\t *  call this.\r\n\t *\r\n\t *  We cannot set the parent pointer of the incoming node\r\n\t *  because the existing interfaces do not have a setParent()\r\n\t *  method and I don't want to break backward compatibility for this.\r\n\t *\r\n\t *  @since 4.7\r\n\t */\r\n\tpublic addAnyChild<T extends ParseTree>(t: T): T {\r\n\t\tif (!this.children) {\r\n\t\t\tthis.children = [t];\r\n\t\t} else {\r\n\t\t\tthis.children.push(t);\r\n\t\t}\r\n\r\n\t\treturn t;\r\n\t}\r\n\r\n\t/** Add a token leaf node child and force its parent to be this node. */\r\n\tpublic addChild(t: TerminalNode): void;\r\n\tpublic addChild(ruleInvocation: RuleContext): void;\r\n\t/**\r\n\t * Add a child to this node based upon matchedToken. It\r\n\t * creates a TerminalNodeImpl rather than using\r\n\t * {@link Parser#createTerminalNode(ParserRuleContext, Token)}. I'm leaving this\r\n\t * in for compatibility but the parser doesn't use this anymore.\r\n\t *\r\n\t * @deprecated Use another overload instead.\r\n\t */\r\n\tpublic addChild(matchedToken: Token): TerminalNode;\r\n\tpublic addChild(t: TerminalNode | RuleContext | Token): TerminalNode | void {\r\n\t\tlet result: TerminalNode | void;\r\n\t\tif (t instanceof TerminalNode) {\r\n\t\t\tt.setParent(this);\r\n\t\t\tthis.addAnyChild(t);\r\n\t\t\treturn;\r\n\t\t} else if (t instanceof RuleContext) {\r\n\t\t\t// Does not set parent link\r\n\t\t\tthis.addAnyChild(t);\r\n\t\t\treturn;\r\n\t\t} else {\r\n\t\t\t// Deprecated code path\r\n\t\t\tt = new TerminalNode(t);\r\n\t\t\tthis.addAnyChild(t);\r\n\t\t\tt.setParent(this);\r\n\t\t\treturn t;\r\n\t\t}\r\n\t}\r\n\r\n\t/** Add an error node child and force its parent to be this node.\r\n\t *\r\n\t * @since 4.7\r\n\t */\r\n\tpublic addErrorNode(errorNode: ErrorNode): ErrorNode;\r\n\r\n\t/**\r\n\t * Add a child to this node based upon badToken. It\r\n\t * creates a ErrorNode rather than using\r\n\t * {@link Parser#createErrorNode(ParserRuleContext, Token)}. I'm leaving this\r\n\t * in for compatibility but the parser doesn't use this anymore.\r\n\t *\r\n\t * @deprecated Use another overload instead.\r\n\t */\r\n\tpublic addErrorNode(badToken: Token): ErrorNode;\r\n\tpublic addErrorNode(node: ErrorNode | Token): ErrorNode {\r\n\t\tif (node instanceof ErrorNode) {\r\n\t\t\tconst errorNode: ErrorNode = node;\r\n\t\t\terrorNode.setParent(this);\r\n\t\t\treturn this.addAnyChild(errorNode);\r\n\t\t} else {\r\n\t\t\t// deprecated path\r\n\t\t\tconst badToken: Token = node;\r\n\t\t\tlet t = new ErrorNode(badToken);\r\n\t\t\tthis.addAnyChild(t);\r\n\t\t\tt.setParent(this);\r\n\t\t\treturn t;\r\n\t\t}\r\n\t}\r\n\r\n//\tpublic void trace(int s) {\r\n//\t\tif ( states==null ) states = new ArrayList<Integer>();\r\n//\t\tstates.add(s);\r\n//\t}\r\n\r\n\t/** Used by enterOuterAlt to toss out a RuleContext previously added as\r\n\t *  we entered a rule. If we have # label, we will need to remove\r\n\t *  generic ruleContext object.\r\n\t */\r\n\tpublic removeLastChild(): void {\r\n\t\tif (this.children) {\r\n\t\t\tthis.children.pop();\r\n\t\t}\r\n\t}\r\n\r\n\t@Override\r\n\t/** Override to make type more specific */\r\n\tget parent(): ParserRuleContext | undefined {\r\n\t\tlet parent = super.parent;\r\n\t\tif (parent === undefined || parent instanceof ParserRuleContext) {\r\n\t\t\treturn parent;\r\n\t\t}\r\n\r\n\t\tthrow new TypeError(\"Invalid parent type for ParserRuleContext\");\r\n\t}\r\n\r\n\tpublic getChild(i: number): ParseTree;\r\n\tpublic getChild<T extends ParseTree>(i: number, ctxType: { new (...args: any[]): T; }): T;\r\n\t// Note: in TypeScript, order or arguments reversed\r\n\tpublic getChild<T extends ParseTree>(i: number, ctxType?: { new (...args: any[]): T; }): ParseTree {\r\n\t\tif (!this.children || i < 0 || i >= this.children.length) {\r\n\t\t\tthrow new RangeError(\"index parameter must be between >= 0 and <= number of children.\");\r\n\t\t}\r\n\r\n\t\tif (ctxType == null) {\r\n\t\t\treturn this.children[i];\r\n\t\t}\r\n\r\n\t\tlet result = this.tryGetChild(i, ctxType);\r\n\t\tif (result === undefined) {\r\n\t\t\tthrow new Error(\"The specified node does not exist\");\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic tryGetChild<T extends ParseTree>(i: number, ctxType: { new (...args: any[]): T; }): T | undefined {\r\n\t\tif (!this.children || i < 0 || i >= this.children.length) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet j: number = -1; // what node with ctxType have we found?\r\n\t\tfor (let o of this.children) {\r\n\t\t\tif (o instanceof ctxType) {\r\n\t\t\t\tj++;\r\n\t\t\t\tif (j === i) {\r\n\t\t\t\t\treturn o;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tpublic getToken(ttype: number, i: number): TerminalNode {\r\n\t\tlet result = this.tryGetToken(ttype, i);\r\n\t\tif (result === undefined) {\r\n\t\t\tthrow new Error(\"The specified token does not exist\");\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic tryGetToken(ttype: number, i: number): TerminalNode | undefined {\r\n\t\tif (!this.children || i < 0 || i >= this.children.length) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet j: number = -1; // what token with ttype have we found?\r\n\t\tfor (let o of this.children) {\r\n\t\t\tif (o instanceof TerminalNode) {\r\n\t\t\t\tlet symbol: Token = o.symbol;\r\n\t\t\t\tif (symbol.type === ttype) {\r\n\t\t\t\t\tj++;\r\n\t\t\t\t\tif (j === i) {\r\n\t\t\t\t\t\treturn o;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tpublic getTokens(ttype: number): TerminalNode[] {\r\n\t\tlet tokens: TerminalNode[] = [];\r\n\r\n\t\tif (!this.children) {\r\n\t\t\treturn tokens;\r\n\t\t}\r\n\r\n\t\tfor (let o of this.children) {\r\n\t\t\tif (o instanceof TerminalNode) {\r\n\t\t\t\tlet symbol = o.symbol;\r\n\t\t\t\tif (symbol.type === ttype) {\r\n\t\t\t\t\ttokens.push(o);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn tokens;\r\n\t}\r\n\r\n\tget ruleContext(): this {\r\n\t\treturn this;\r\n\t}\r\n\r\n\t// NOTE: argument order change from Java version\r\n\tpublic getRuleContext<T extends ParserRuleContext>(i: number, ctxType: { new (...args: any[]): T; }): T {\r\n\t\treturn this.getChild(i, ctxType);\r\n\t}\r\n\r\n\tpublic tryGetRuleContext<T extends ParserRuleContext>(i: number, ctxType: { new (...args: any[]): T; }): T | undefined {\r\n\t\treturn this.tryGetChild(i, ctxType);\r\n\t}\r\n\r\n\tpublic getRuleContexts<T extends ParserRuleContext>(ctxType: { new (...args: any[]): T; }): T[] {\r\n\t\tlet contexts: T[] = [];\r\n\t\tif (!this.children) {\r\n\t\t\treturn contexts;\r\n\t\t}\r\n\r\n\t\tfor (let o of this.children) {\r\n\t\t\tif (o instanceof ctxType) {\r\n\t\t\t\tcontexts.push(o);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn contexts;\r\n\t}\r\n\r\n\t@Override\r\n\tget childCount() {\r\n\t\treturn this.children ? this.children.length : 0;\r\n\t}\r\n\r\n\t@Override\r\n\tget sourceInterval(): Interval {\r\n\t\tif (!this._start) {\r\n\t\t\treturn Interval.INVALID;\r\n\t\t}\r\n\t\tif (!this._stop || this._stop.tokenIndex < this._start.tokenIndex) {\r\n\t\t\treturn Interval.of(this._start.tokenIndex, this._start.tokenIndex - 1); // empty\r\n\t\t}\r\n\t\treturn Interval.of(this._start.tokenIndex, this._stop.tokenIndex);\r\n\t}\r\n\r\n\t/**\r\n\t * Get the initial token in this context.\r\n\t * Note that the range from start to stop is inclusive, so for rules that do not consume anything\r\n\t * (for example, zero length or error productions) this token may exceed stop.\r\n\t */\r\n\tget start(): Token { return this._start; }\r\n\t/**\r\n\t * Get the final token in this context.\r\n\t * Note that the range from start to stop is inclusive, so for rules that do not consume anything\r\n\t * (for example, zero length or error productions) this token may precede start.\r\n\t */\r\n\tget stop(): Token | undefined { return this._stop; }\r\n\r\n\t/** Used for rule context info debugging during parse-time, not so much for ATN debugging */\r\n\tpublic toInfoString(recognizer: Parser): string {\r\n\t\tlet rules: string[] =\r\n\t\t\trecognizer.getRuleInvocationStack(this).reverse();\r\n\t\treturn \"ParserRuleContext\" + rules + \"{\" +\r\n\t\t\t\"start=\" + this._start +\r\n\t\t\t\", stop=\" + this._stop +\r\n\t\t\t\"}\";\r\n\t}\r\n}\r\n"]}