{"version": 3, "file": "WritableToken.js", "sourceRoot": "", "sources": ["../../src/WritableToken.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:59.7015751-07:00\r\n\r\nimport { Token } from \"./Token\";\r\n\r\nexport interface WritableToken extends Token {\r\n\ttext: string | undefined;\r\n\r\n\ttype: number;\r\n\r\n\tline: number;\r\n\r\n\tcharPositionInLine: number;\r\n\r\n\tchannel: number;\r\n\r\n\ttokenIndex: number;\r\n}\r\n"]}