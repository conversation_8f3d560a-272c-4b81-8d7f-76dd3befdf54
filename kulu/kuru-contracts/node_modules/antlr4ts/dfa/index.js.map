{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/dfa/index.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,oDAAkC;AAClC,wCAAsB;AACtB,kDAAgC;AAChC,6CAA2B;AAC3B,uDAAqC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport * from \"./AcceptStateInfo\";\r\nexport * from \"./DFA\";\r\nexport * from \"./DFASerializer\";\r\nexport * from \"./DFAState\";\r\nexport * from \"./LexerDFASerializer\";\r\n"]}