{"version": 3, "file": "DFAState.js", "sourceRoot": "", "sources": ["../../../src/dfa/DFAState.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,oCAAiC;AAEjC,2CAAwC;AAExC,mDAAgD;AAChD,8CAAkD;AAClD,gEAA6D;AAG7D,iCAAiC;AAEjC;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAa,QAAQ;IAyBpB;;;;OAIG;IACH,YAAY,OAAqB;QA7B1B,gBAAW,GAAW,CAAC,CAAC,CAAC;QA8B/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAoB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAoB,CAAC;IACjD,CAAC;IAED,IAAI,kBAAkB;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;IAC9B,CAAC;IAEM,eAAe,CAAC,MAAc;QACpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC7B,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,cAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAEM,gBAAgB,CAAC,MAAc;QACrC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChC,IAAI,CAAC,cAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEM,mBAAmB,CAAC,GAAQ;QAClC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC5B,OAAO;SACP;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,IAAI,eAAM,EAAE,CAAC;SACnC;IACF,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe,CAAC,eAA4C;QAC/D,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IACzC,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC3B,OAAO,SAAG,CAAC,kBAAkB,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;IACzC,CAAC;IAED,IAAI,mBAAmB;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC3B,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;IAClD,CAAC;IAEM,SAAS,CAAC,MAAc;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAEM,SAAS,CAAC,MAAc,EAAE,MAAgB;QAChD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC;IAEM,UAAU;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEM,gBAAgB,CAAC,aAAqB;QAC5C,IAAI,aAAa,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;YAC7D,aAAa,GAAG,CAAC,CAAC,CAAC;SACnB;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAEM,gBAAgB,CAAC,aAAqB,EAAE,MAAgB;QAC9D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACvD;QAED,IAAI,aAAa,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;YAC7D,aAAa,GAAG,CAAC,CAAC,CAAC;SACnB;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEM,iBAAiB;QACvB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAmB,IAAI,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC3B,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;gBACnB,IAAI,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,qCAAiB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;gBAC7D,OAAO,MAAM,CAAC;aACd;iBACI;gBACJ,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,GAAG,CAAC,GAAG,CAAC,qCAAiB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;aAC1D;SACD;QAED,OAAO,GAAG,CAAC;IACZ,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;OAYG;IAEI,MAAM,CAAC,CAAM;QACnB,2DAA2D;QAC3D,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,CAAC,CAAC,CAAC,YAAY,QAAQ,CAAC,EAAE;YAC7B,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,GAAa,CAAC,CAAC;QACxB,IAAI,OAAO,GAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5D,sFAAsF;QACpF,OAAO,OAAO,CAAC;IAChB,CAAC;IAGM,QAAQ;QACd,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,IAAI,IAAI,CAAC,UAAU,EAAE;gBACpB,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC;aACvB;iBACI;gBACJ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACzB;SACD;QACD,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;CACD;AA/LA;IADC,oBAAO;yCACqB;AAK7B;IADC,oBAAO;uCACsC;AAM9C;IADC,oBAAO;8CAC6C;AAgIrD;IADC,qBAAQ;wCAMR;AAgBD;IADC,qBAAQ;sCAeR;AAGD;IADC,qBAAQ;wCAcR;AAlMF,4BAmMC;AAED,WAAiB,QAAQ;IACxB,kDAAkD;IAClD,IAAa,cAAc,GAA3B,MAAa,cAAc;QAI1B,YAAqB,IAAqB,EAAE,GAAW;YACtD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,CAAC;QAGM,QAAQ;YACd,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAChD,CAAC;KACD,CAAA;IAXA;QADC,oBAAO;gDACqB;IAQ7B;QADC,qBAAQ;kDAGR;IAZW,cAAc;QAIb,WAAA,oBAAO,CAAA;OAJR,cAAc,CAa1B;IAbY,uBAAc,iBAa1B,CAAA;AACF,CAAC,EAhBgB,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAgBxB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:38.7771056-07:00\r\n\r\nimport { AcceptStateInfo } from \"./AcceptStateInfo\";\r\nimport { ATN } from \"../atn/ATN\";\r\nimport { ATNConfigSet } from \"../atn/ATNConfigSet\";\r\nimport { BitSet } from \"../misc/BitSet\";\r\nimport { LexerActionExecutor } from \"../atn/LexerActionExecutor\";\r\nimport { MurmurHash } from \"../misc/MurmurHash\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { PredictionContext } from \"../atn/PredictionContext\";\r\nimport { SemanticContext } from \"../atn/SemanticContext\";\r\n\r\nimport * as assert from \"assert\";\r\n\r\n/** A DFA state represents a set of possible ATN configurations.\r\n *  As <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> p. 117 says \"The DFA uses its state\r\n *  to keep track of all possible states the ATN can be in after\r\n *  reading each input symbol.  That is to say, after reading\r\n *  input a1a2..an, the DFA is in a state that represents the\r\n *  subset T of the states of the ATN that are reachable from the\r\n *  ATN's start state along some path labeled a1a2..an.\"\r\n *  In conventional NFA&rarr;DFA conversion, therefore, the subset T\r\n *  would be a bitset representing the set of states the\r\n *  ATN could be in.  We need to track the alt predicted by each\r\n *  state as well, however.  More importantly, we need to maintain\r\n *  a stack of states, tracking the closure operations as they\r\n *  jump from rule to rule, emulating rule invocations (method calls).\r\n *  I have to add a stack to simulate the proper lookahead sequences for\r\n *  the underlying LL grammar from which the ATN was derived.\r\n *\r\n *  I use a set of ATNConfig objects not simple states.  An ATNConfig\r\n *  is both a state (ala normal conversion) and a RuleContext describing\r\n *  the chain of rules (if any) followed to arrive at that state.\r\n *\r\n *  A DFA state may have multiple references to a particular state,\r\n *  but with different ATN contexts (with same or different alts)\r\n *  meaning that state was reached via a different set of rule invocations.\r\n */\r\nexport class DFAState {\r\n\tpublic stateNumber: number = -1;\r\n\r\n\t@NotNull\r\n\tpublic configs: ATNConfigSet;\r\n\r\n\t/** `edges.get(symbol)` points to target of symbol.\r\n\t */\r\n\t@NotNull\r\n\tprivate readonly edges: Map<number, DFAState>;\r\n\r\n\tprivate _acceptStateInfo: AcceptStateInfo | undefined;\r\n\r\n\t/** These keys for these edges are the top level element of the global context. */\r\n\t@NotNull\r\n\tprivate readonly contextEdges: Map<number, DFAState>;\r\n\r\n\t/** Symbols in this set require a global context transition before matching an input symbol. */\r\n\tprivate contextSymbols: BitSet | undefined;\r\n\r\n\t/**\r\n\t * This list is computed by {@link ParserATNSimulator#predicateDFAState}.\r\n\t */\r\n\tpublic predicates: DFAState.PredPrediction[] | undefined;\r\n\r\n\t/**\r\n\t * Constructs a new `DFAState`.\r\n\t *\r\n\t * @param configs The set of ATN configurations defining this state.\r\n\t */\r\n\tconstructor(configs: ATNConfigSet) {\r\n\t\tthis.configs = configs;\r\n\t\tthis.edges = new Map<number, DFAState>();\r\n\t\tthis.contextEdges = new Map<number, DFAState>();\r\n\t}\r\n\r\n\tget isContextSensitive(): boolean {\r\n\t\treturn !!this.contextSymbols;\r\n\t}\r\n\r\n\tpublic isContextSymbol(symbol: number): boolean {\r\n\t\tif (!this.isContextSensitive) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.contextSymbols!.get(symbol);\r\n\t}\r\n\r\n\tpublic setContextSymbol(symbol: number): void {\r\n\t\tassert(this.isContextSensitive);\r\n\t\tthis.contextSymbols!.set(symbol);\r\n\t}\r\n\r\n\tpublic setContextSensitive(atn: ATN): void {\r\n\t\tassert(!this.configs.isOutermostConfigSet);\r\n\t\tif (this.isContextSensitive) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (!this.contextSymbols) {\r\n\t\t\tthis.contextSymbols = new BitSet();\r\n\t\t}\r\n\t}\r\n\r\n\tget acceptStateInfo(): AcceptStateInfo | undefined {\r\n\t\treturn this._acceptStateInfo;\r\n\t}\r\n\r\n\tset acceptStateInfo(acceptStateInfo: AcceptStateInfo | undefined) {\r\n\t\tthis._acceptStateInfo = acceptStateInfo;\r\n\t}\r\n\r\n\tget isAcceptState(): boolean {\r\n\t\treturn !!this._acceptStateInfo;\r\n\t}\r\n\r\n\tget prediction(): number {\r\n\t\tif (!this._acceptStateInfo) {\r\n\t\t\treturn ATN.INVALID_ALT_NUMBER;\r\n\t\t}\r\n\r\n\t\treturn this._acceptStateInfo.prediction;\r\n\t}\r\n\r\n\tget lexerActionExecutor(): LexerActionExecutor | undefined {\r\n\t\tif (!this._acceptStateInfo) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn this._acceptStateInfo.lexerActionExecutor;\r\n\t}\r\n\r\n\tpublic getTarget(symbol: number): DFAState | undefined {\r\n\t\treturn this.edges.get(symbol);\r\n\t}\r\n\r\n\tpublic setTarget(symbol: number, target: DFAState): void {\r\n\t\tthis.edges.set(symbol, target);\r\n\t}\r\n\r\n\tpublic getEdgeMap(): Map<number, DFAState> {\r\n\t\treturn this.edges;\r\n\t}\r\n\r\n\tpublic getContextTarget(invokingState: number): DFAState | undefined {\r\n\t\tif (invokingState === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\tinvokingState = -1;\r\n\t\t}\r\n\r\n\t\treturn this.contextEdges.get(invokingState);\r\n\t}\r\n\r\n\tpublic setContextTarget(invokingState: number, target: DFAState): void {\r\n\t\tif (!this.isContextSensitive) {\r\n\t\t\tthrow new Error(\"The state is not context sensitive.\");\r\n\t\t}\r\n\r\n\t\tif (invokingState === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\tinvokingState = -1;\r\n\t\t}\r\n\r\n\t\tthis.contextEdges.set(invokingState, target);\r\n\t}\r\n\r\n\tpublic getContextEdgeMap(): Map<number, DFAState> {\r\n\t\tlet map = new Map<number, DFAState>(this.contextEdges);\r\n\t\tlet existing = map.get(-1);\r\n\t\tif (existing !== undefined) {\r\n\t\t\tif (map.size === 1) {\r\n\t\t\t\tlet result = new Map<number, DFAState>();\r\n\t\t\t\tresult.set(PredictionContext.EMPTY_FULL_STATE_KEY, existing);\r\n\t\t\t\treturn result;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tmap.delete(-1);\r\n\t\t\t\tmap.set(PredictionContext.EMPTY_FULL_STATE_KEY, existing);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn map;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize(7);\r\n\t\thash = MurmurHash.update(hash, this.configs.hashCode());\r\n\t\thash = MurmurHash.finish(hash, 1);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t/**\r\n\t * Two {@link DFAState} instances are equal if their ATN configuration sets\r\n\t * are the same. This method is used to see if a state already exists.\r\n\t *\r\n\t * Because the number of alternatives and number of ATN configurations are\r\n\t * finite, there is a finite number of DFA states that can be processed.\r\n\t * This is necessary to show that the algorithm terminates.\r\n\t *\r\n\t * Cannot test the DFA state numbers here because in\r\n\t * {@link ParserATNSimulator#addDFAState} we need to know if any other state\r\n\t * exists that has this exact set of ATN configurations. The\r\n\t * {@link #stateNumber} is irrelevant.\r\n\t */\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\t// compare set of ATN configurations in this set with other\r\n\t\tif (this === o) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tif (!(o instanceof DFAState)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tlet other: DFAState = o;\r\n\t\tlet sameSet: boolean = this.configs.equals(other.configs);\r\n//\t\tSystem.out.println(\"DFAState.equals: \"+configs+(sameSet?\"==\":\"!=\")+other.configs);\r\n\t\treturn sameSet;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tlet buf = \"\";\r\n\t\tbuf += (this.stateNumber) + (\":\") + (this.configs);\r\n\t\tif (this.isAcceptState) {\r\n\t\t\tbuf += (\"=>\");\r\n\t\t\tif (this.predicates) {\r\n\t\t\t\tbuf += this.predicates;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tbuf += (this.prediction);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn buf.toString();\r\n\t}\r\n}\r\n\r\nexport namespace DFAState {\r\n\t/** Map a predicate to a predicted alternative. */\r\n\texport class PredPrediction {\r\n\t\t@NotNull\r\n\t\tpublic pred: SemanticContext;  // never null; at least SemanticContext.NONE\r\n\t\tpublic alt: number;\r\n\t\tconstructor(@NotNull pred: SemanticContext, alt: number) {\r\n\t\t\tthis.alt = alt;\r\n\t\t\tthis.pred = pred;\r\n\t\t}\r\n\r\n\t\t@Override\r\n\t\tpublic toString(): string {\r\n\t\t\treturn \"(\" + this.pred + \", \" + this.alt + \")\";\r\n\t\t}\r\n\t}\r\n}\r\n"]}