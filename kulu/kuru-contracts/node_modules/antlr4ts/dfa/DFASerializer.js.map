{"version": 3, "file": "DFASerializer.js", "sourceRoot": "", "sources": ["../../../src/dfa/DFASerializer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,sDAAmD;AAInD,8CAAkD;AAClD,gEAA6D;AAC7D,8CAA2C;AAE3C,sDAAmD;AAEnD,sEAAsE;AACtE,MAAa,aAAa;IAazB,YAAY,GAAQ,EAAE,UAAyD,EAAE,SAAoB,EAAE,GAAS;QAC/G,IAAI,UAAU,YAAY,uBAAU,EAAE;YACrC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;YACjC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;YACrB,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;SACnC;aAAM,IAAI,CAAC,UAAU,EAAE;YACvB,UAAU,GAAG,+BAAc,CAAC,gBAAgB,CAAC;SAC7C;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IAChB,CAAC;IAGM,QAAQ;QACd,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;YACjB,OAAO,EAAE,CAAC;SACV;QAED,IAAI,GAAG,GAAG,EAAE,CAAC;QAEb,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACpB,IAAI,MAAM,GAAe,IAAI,KAAK,CAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC;YAEzD,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,KAAK,GAA0B,CAAC,CAAC,UAAU,EAAE,CAAC;gBAClD,IAAI,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvD,IAAI,YAAY,GAA0B,CAAC,CAAC,iBAAiB,EAAE,CAAC;gBAChE,IAAI,eAAe,GAAG,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrE,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE;oBAC3B,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC7B,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,2BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;wBACjF,SAAS;qBACT;oBAED,IAAI,aAAa,GAAY,KAAK,CAAC;oBACnC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9E,IAAI,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;wBAC7B,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;wBACb,aAAa,GAAG,IAAI,CAAC;qBACrB;oBAED,IAAI,CAAC,GAAyB,KAAK,CAAC;oBACpC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,2BAAY,CAAC,KAAK,CAAC,WAAW,EAAE;wBAC1D,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;qBACzC;yBACI,IAAI,aAAa,EAAE;wBACvB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;qBACjB;iBACD;gBAED,IAAI,CAAC,CAAC,kBAAkB,EAAE;oBACzB,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE;wBAClC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;8BAC5B,CAAC,GAAG,CAAC;8BACL,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;8BAC7B,CAAC,IAAI,CAAC;8BACN,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,CAAC;8BAC/C,CAAC,IAAI,CAAC,CAAC;qBACV;iBACD;aACD;SACD;QACD,IAAI,MAAM,GAAW,GAAG,CAAC;QACzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,EAAE,CAAC;SACV;QACD,yCAAyC;QACzC,OAAO,MAAM,CAAC;IACf,CAAC;IAES,eAAe,CAAC,CAAS;QAClC,IAAI,CAAC,KAAK,qCAAiB,CAAC,oBAAoB,EAAE;YACjD,OAAO,gBAAgB,CAAC;SACxB;aACI,IAAI,CAAC,KAAK,qCAAiB,CAAC,qBAAqB,EAAE;YACvD,OAAO,iBAAiB,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE;YACrD,IAAI,KAAK,GAAa,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,SAAS,GAAW,KAAK,CAAC,SAAS,CAAC;YACxC,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC1E,OAAO,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;aAClE;SACD;QAED,OAAO,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAES,YAAY,CAAC,CAAS;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEM,cAAc,CAAC,CAAW;QAChC,IAAI,CAAC,KAAK,2BAAY,CAAC,KAAK,EAAE;YAC7B,OAAO,OAAO,CAAC;SACf;QAED,IAAI,CAAC,GAAW,CAAC,CAAC,WAAW,CAAC;QAC9B,IAAI,QAAQ,GAAW,GAAG,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,CAAC,UAAU,EAAE;gBACjB,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;aAC1C;iBACI;gBACJ,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;aAC1C;SACD;QAED,IAAI,CAAC,CAAC,kBAAkB,EAAE;YACzB,QAAQ,IAAI,GAAG,CAAC;YAChB,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE;gBAC7B,IAAI,MAAM,CAAC,uBAAuB,EAAE;oBACnC,QAAQ,IAAI,GAAG,CAAC;oBAChB,MAAM;iBACN;aACD;SACD;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC;CACD;AAvIA;IADC,oBAAO;0CACS;AAEjB;IADC,oBAAO;iDACuB;AAyB/B;IADC,qBAAQ;6CAyDR;AArFF,sCAyIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:38.5097925-07:00\r\n\r\nimport { ATN } from \"../atn/ATN\";\r\nimport { ATNSimulator } from \"../atn/ATNSimulator\";\r\nimport { ATNState } from \"../atn/ATNState\";\r\nimport { DFA } from \"./DFA\";\r\nimport { DFAState } from \"./DFAState\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { PredictionContext } from \"../atn/PredictionContext\";\r\nimport { Recognizer } from \"../Recognizer\";\r\nimport { Vocabulary } from \"../Vocabulary\";\r\nimport { VocabularyImpl } from \"../VocabularyImpl\";\r\n\r\n/** A DFA walker that knows how to dump them to serialized strings. */\r\nexport class DFASerializer {\r\n\t@NotNull\r\n\tprivate dfa: DFA;\r\n\t@NotNull\r\n\tprivate vocabulary: Vocabulary;\r\n\r\n\tpublic ruleNames?: string[];\r\n\r\n\tpublic atn?: ATN;\r\n\r\n\tconstructor(/*@NotNull*/ dfa: DFA, /*@NotNull*/ vocabulary: Vocabulary);\r\n\tconstructor(/*@NotNull*/ dfa: DFA, /*@Nullable*/ parser: Recognizer<any, any> | undefined);\r\n\tconstructor(/*@NotNull*/ dfa: DFA, /*@NotNull*/ vocabulary: Vocabulary, /*@Nullable*/ ruleNames: string[] | undefined, /*@Nullable*/ atn: ATN | undefined);\r\n\tconstructor(dfa: DFA, vocabulary: Vocabulary | Recognizer<any, any> | undefined, ruleNames?: string[], atn?: ATN) {\r\n\t\tif (vocabulary instanceof Recognizer) {\r\n\t\t\truleNames = vocabulary.ruleNames;\r\n\t\t\tatn = vocabulary.atn;\r\n\t\t\tvocabulary = vocabulary.vocabulary;\r\n\t\t} else if (!vocabulary) {\r\n\t\t\tvocabulary = VocabularyImpl.EMPTY_VOCABULARY;\r\n\t\t}\r\n\r\n\t\tthis.dfa = dfa;\r\n\t\tthis.vocabulary = vocabulary;\r\n\t\tthis.ruleNames = ruleNames;\r\n\t\tthis.atn = atn;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tif (!this.dfa.s0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\tlet buf = \"\";\r\n\r\n\t\tif (this.dfa.states) {\r\n\t\t\tlet states: DFAState[] = new Array<DFAState>(...this.dfa.states.toArray());\r\n\t\t\tstates.sort((o1, o2) => o1.stateNumber - o2.stateNumber);\r\n\r\n\t\t\tfor (let s of states) {\r\n\t\t\t\tlet edges: Map<number, DFAState> = s.getEdgeMap();\r\n\t\t\t\tlet edgeKeys = [...edges.keys()].sort((a, b) => a - b);\r\n\t\t\t\tlet contextEdges: Map<number, DFAState> = s.getContextEdgeMap();\r\n\t\t\t\tlet contextEdgeKeys = [...contextEdges.keys()].sort((a, b) => a - b);\r\n\t\t\t\tfor (let entry of edgeKeys) {\r\n\t\t\t\t\tlet value = edges.get(entry);\r\n\t\t\t\t\tif ((value == null || value === ATNSimulator.ERROR) && !s.isContextSymbol(entry)) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet contextSymbol: boolean = false;\r\n\t\t\t\t\tbuf += (this.getStateString(s)) + (\"-\") + (this.getEdgeLabel(entry)) + (\"->\");\r\n\t\t\t\t\tif (s.isContextSymbol(entry)) {\r\n\t\t\t\t\t\tbuf += (\"!\");\r\n\t\t\t\t\t\tcontextSymbol = true;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet t: DFAState | undefined = value;\r\n\t\t\t\t\tif (t && t.stateNumber !== ATNSimulator.ERROR.stateNumber) {\r\n\t\t\t\t\t\tbuf += (this.getStateString(t)) + (\"\\n\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse if (contextSymbol) {\r\n\t\t\t\t\t\tbuf += (\"ctx\\n\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (s.isContextSensitive) {\r\n\t\t\t\t\tfor (let entry of contextEdgeKeys) {\r\n\t\t\t\t\t\tbuf += (this.getStateString(s))\r\n\t\t\t\t\t\t\t+ (\"-\")\r\n\t\t\t\t\t\t\t+ (this.getContextLabel(entry))\r\n\t\t\t\t\t\t\t+ (\"->\")\r\n\t\t\t\t\t\t\t+ (this.getStateString(contextEdges.get(entry)!))\r\n\t\t\t\t\t\t\t+ (\"\\n\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tlet output: string = buf;\r\n\t\tif (output.length === 0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\t\t//return Utils.sortLinesInString(output);\r\n\t\treturn output;\r\n\t}\r\n\r\n\tprotected getContextLabel(i: number): string {\r\n\t\tif (i === PredictionContext.EMPTY_FULL_STATE_KEY) {\r\n\t\t\treturn \"ctx:EMPTY_FULL\";\r\n\t\t}\r\n\t\telse if (i === PredictionContext.EMPTY_LOCAL_STATE_KEY) {\r\n\t\t\treturn \"ctx:EMPTY_LOCAL\";\r\n\t\t}\r\n\r\n\t\tif (this.atn && i > 0 && i <= this.atn.states.length) {\r\n\t\t\tlet state: ATNState = this.atn.states[i];\r\n\t\t\tlet ruleIndex: number = state.ruleIndex;\r\n\t\t\tif (this.ruleNames && ruleIndex >= 0 && ruleIndex < this.ruleNames.length) {\r\n\t\t\t\treturn \"ctx:\" + String(i) + \"(\" + this.ruleNames[ruleIndex] + \")\";\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn \"ctx:\" + String(i);\r\n\t}\r\n\r\n\tprotected getEdgeLabel(i: number): string {\r\n\t\treturn this.vocabulary.getDisplayName(i);\r\n\t}\r\n\r\n\tpublic getStateString(s: DFAState): string {\r\n\t\tif (s === ATNSimulator.ERROR) {\r\n\t\t\treturn \"ERROR\";\r\n\t\t}\r\n\r\n\t\tlet n: number = s.stateNumber;\r\n\t\tlet stateStr: string = \"s\" + n;\r\n\t\tif (s.isAcceptState) {\r\n\t\t\tif (s.predicates) {\r\n\t\t\t\tstateStr = \":s\" + n + \"=>\" + s.predicates;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tstateStr = \":s\" + n + \"=>\" + s.prediction;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (s.isContextSensitive) {\r\n\t\t\tstateStr += \"*\";\r\n\t\t\tfor (let config of s.configs) {\r\n\t\t\t\tif (config.reachesIntoOuterContext) {\r\n\t\t\t\t\tstateStr += \"*\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn stateStr;\r\n\t}\r\n}\r\n"]}