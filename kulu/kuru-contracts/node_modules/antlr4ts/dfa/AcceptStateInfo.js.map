{"version": 3, "file": "AcceptStateInfo.js", "sourceRoot": "", "sources": ["../../../src/dfa/AcceptStateInfo.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAMH;;;;;;;;GAQG;AACH,MAAa,eAAe;IAM3B,YAAY,UAAkB,EAAE,mBAAyC;QACxE,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IAClC,CAAC;CACD;AA7BD,0CA6BC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:38.1172076-07:00\r\n\r\nimport { LexerActionExecutor } from \"../atn/LexerActionExecutor\";\r\n\r\n/**\r\n * Stores information about a {@link DFAState} which is an accept state under\r\n * some condition. Certain settings, such as\r\n * {@link ParserATNSimulator#getPredictionMode()}, may be used in addition to\r\n * this information to determine whether or not a particular state is an accept\r\n * state.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class AcceptStateInfo {\r\n\tprivate readonly _prediction: number;\r\n\tprivate readonly _lexerActionExecutor?: LexerActionExecutor;\r\n\r\n\tconstructor(prediction: number);\r\n\tconstructor(prediction: number, lexerActionExecutor: LexerActionExecutor | undefined);\r\n\tconstructor(prediction: number, lexerActionExecutor?: LexerActionExecutor) {\r\n\t\tthis._prediction = prediction;\r\n\t\tthis._lexerActionExecutor = lexerActionExecutor;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the prediction made by this accept state. Note that this value\r\n\t * assumes the predicates, if any, in the {@link DFAState} evaluate to\r\n\t * `true`. If predicate evaluation is enabled, the final prediction of\r\n\t * the accept state will be determined by the result of predicate\r\n\t * evaluation.\r\n\t */\r\n\tget prediction(): number {\r\n\t\treturn this._prediction;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the {@link LexerActionExecutor} which can be used to execute actions\r\n\t * and/or commands after the lexer matches a token.\r\n\t */\r\n\tget lexerActionExecutor(): LexerActionExecutor | undefined {\r\n\t\treturn this._lexerActionExecutor;\r\n\t}\r\n}\r\n"]}