{"version": 3, "file": "LexerDFASerializer.js", "sourceRoot": "", "sources": ["../../../src/dfa/LexerDFASerializer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,mDAAgD;AAChD,8CAAkD;AAClD,sDAAmD;AAEnD,IAAa,kBAAkB,GAA/B,MAAa,kBAAmB,SAAQ,6BAAa;IACpD,YAAsB,GAAQ;QAC7B,KAAK,CAAC,GAAG,EAAE,+BAAc,CAAC,gBAAgB,CAAC,CAAC;IAC7C,CAAC;IAIS,YAAY,CAAC,CAAS;QAC/B,OAAO,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAC5C,CAAC;CACD,CAAA;AAHA;IAFC,qBAAQ;IACR,oBAAO;sDAGP;AATW,kBAAkB;IAChB,WAAA,oBAAO,CAAA;GADT,kBAAkB,CAU9B;AAVY,gDAAkB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:39.2167238-07:00\r\n\r\nimport { DFA } from \"./DFA\";\r\nimport { DFASerializer } from \"./DFASerializer\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { VocabularyImpl } from \"../VocabularyImpl\";\r\n\r\nexport class LexerDFASerializer extends DFASerializer {\r\n\tconstructor( @NotNull dfa: DFA) {\r\n\t\tsuper(dfa, VocabularyImpl.EMPTY_VOCABULARY);\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tprotected getEdgeLabel(i: number): string {\r\n\t\treturn \"'\" + String.fromCodePoint(i) + \"'\";\r\n\t}\r\n}\r\n"]}