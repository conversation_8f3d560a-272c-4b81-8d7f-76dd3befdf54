{"version": 3, "file": "DFA.js", "sourceRoot": "", "sources": ["../../../src/dfa/DFA.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,2DAAwD;AAExD,sDAAmD;AAInD,mDAAgD;AAChD,yCAAsC;AAEtC,6DAA0D;AAC1D,8CAAwC;AACxC,+EAA4E;AAC5E,kEAA+D;AAI/D,sDAAmD;AAEnD,IAAa,GAAG,GAAhB,MAAa,GAAG;IAmDf,YAAqB,aAAuB,EAAE,WAAmB,CAAC;QAlDlE;;;;;WAKG;QAEa,WAAM,GAA6B,IAAI,+BAAc,CAAW,mDAAwB,CAAC,QAAQ,CAAC,CAAC;QAkB3G,oBAAe,GAAW,CAAC,CAAC;QA0BnC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAC/D;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,6GAA6G;QAC7G,8GAA8G;QAC9G,uGAAuG;QACvG,uGAAuG;QACvG,IAAI,eAAe,GAAY,KAAK,CAAC;QACrC,IAAI,aAAa,YAAY,uCAAkB,EAAE;YAChD,IAAI,aAAa,CAAC,sBAAsB,EAAE;gBACzC,eAAe,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,EAAE,GAAG,IAAI,mBAAQ,CAAC,IAAI,2BAAY,EAAE,CAAC,CAAC;gBAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAQ,CAAC,IAAI,2BAAY,EAAE,CAAC,CAAC;aAC/C;SACD;QAED,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;IACtC,CAAC;IAED;;;;;;;;;;OAUG;IACH,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED;;;;;;;;;OASG;IACI,uBAAuB,CAAC,UAAkB,EAAE,WAAoB;QACtE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC9E;QAED,oDAAoD;QACpD,IAAI,WAAW,EAAE;YAChB,OAAQ,IAAI,CAAC,MAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACvD;aACI;YACJ,OAAQ,IAAI,CAAC,EAAe,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACnD;IACF,CAAC;IAED;;;;;;;;;OASG;IACI,uBAAuB,CAAC,UAAkB,EAAE,WAAoB,EAAE,UAAoB;QAC5F,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC9E;QAED,IAAI,UAAU,GAAG,CAAC,EAAE;YACnB,OAAO;SACP;QAED,IAAI,WAAW,EAAE;YAChB,4CAA4C;YAC3C,IAAI,CAAC,MAAmB,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SAC5D;aACI;YACJ,wCAAwC;YACvC,IAAI,CAAC,EAAe,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SACxD;IACF,CAAC;IAED,IAAI,OAAO;QACV,IAAI,IAAI,CAAC,eAAe,EAAE;YACzB,oDAAoD;YACpD,OAAO,IAAI,CAAC,EAAG,CAAC,UAAU,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,MAAO,CAAC,UAAU,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC;SAChF;QAED,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED,IAAI,kBAAkB;QACrB,IAAI,IAAI,CAAC,eAAe,EAAE;YACzB,4CAA4C;YAC5C,OAAQ,IAAI,CAAC,MAAmB,CAAC,UAAU,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC5B,CAAC;IAEM,QAAQ,CAAC,KAAe;QAC9B,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAKM,QAAQ,CAAC,UAAuB,EAAE,SAAoB;QAC5D,IAAI,CAAC,UAAU,EAAE;YAChB,UAAU,GAAG,+BAAc,CAAC,gBAAgB,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACb,OAAO,EAAE,CAAC;SACV;QAED,IAAI,UAAyB,CAAC;QAC9B,IAAI,SAAS,EAAE;YACd,UAAU,GAAG,IAAI,6BAAa,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;SACpF;aAAM;YACN,UAAU,GAAG,IAAI,6BAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SACjD;QAED,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACb,OAAO,EAAE,CAAC;SACV;QAED,IAAI,UAAU,GAAkB,IAAI,uCAAkB,CAAC,IAAI,CAAC,CAAC;QAC7D,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACD,CAAA;AA7LA;IADC,oBAAO;mCAC2G;AAUnH;IADC,oBAAO;0CACuB;AAM/B;IADC,oBAAO;gCACQ;AAxBJ,GAAG;IAmDF,WAAA,oBAAO,CAAA;GAnDR,GAAG,CAqMf;AArMY,kBAAG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:38.3567094-07:00\r\n\r\nimport { Array2DHashSet } from \"../misc/Array2DHashSet\";\r\nimport { ATN } from \"../atn/ATN\";\r\nimport { ATNConfigSet } from \"../atn/ATNConfigSet\";\r\nimport { ATNState } from \"../atn/ATNState\";\r\nimport { ATNType } from \"../atn/ATNType\";\r\nimport { DecisionState } from \"../atn/DecisionState\";\r\nimport { DFASerializer } from \"./DFASerializer\";\r\nimport { DFAState } from \"./DFAState\";\r\nimport { LexerATNSimulator } from \"../atn/LexerATNSimulator\";\r\nimport { LexerDFASerializer } from \"./LexerDFASerializer\";\r\nimport { NotNull } from \"../Decorators\";\r\nimport { ObjectEqualityComparator } from \"../misc/ObjectEqualityComparator\";\r\nimport { StarLoopEntryState } from \"../atn/StarLoopEntryState\";\r\nimport { Token } from \"../Token\";\r\nimport { TokensStartState } from \"../atn/TokensStartState\";\r\nimport { Vocabulary } from \"../Vocabulary\";\r\nimport { VocabularyImpl } from \"../VocabularyImpl\";\r\n\r\nexport class DFA {\r\n\t/**\r\n\t * A set of all states in the `DFA`.\r\n\t *\r\n\t * Note that this collection of states holds the DFA states for both SLL and LL prediction. Only the start state\r\n\t * needs to be differentiated for these cases, which is tracked by the `s0` and `s0full` fields.\r\n\t */\r\n\t@NotNull\r\n\tpublic readonly states: Array2DHashSet<DFAState> = new Array2DHashSet<DFAState>(ObjectEqualityComparator.INSTANCE);\r\n\r\n\tpublic s0: DFAState | undefined;\r\n\r\n\tpublic s0full: DFAState | undefined;\r\n\r\n\tpublic readonly decision: number;\r\n\r\n\t/** From which ATN state did we create this DFA? */\r\n\t@NotNull\r\n\tpublic atnStartState: ATNState;\r\n\t/**\r\n\t * Note: this field is accessed as `atnStartState.atn` in other targets. The TypeScript target keeps a separate copy\r\n\t * to avoid a number of additional null/undefined checks each time the ATN is accessed.\r\n\t */\r\n\t@NotNull\r\n\tpublic atn: ATN;\r\n\r\n\tprivate nextStateNumber: number = 0;\r\n\r\n\t/**\r\n\t * `true` if this DFA is for a precedence decision; otherwise,\r\n\t * `false`. This is the backing field for {@link #isPrecedenceDfa}.\r\n\t */\r\n\tprivate precedenceDfa: boolean;\r\n\r\n\t/**\r\n\t * Constructs a `DFA` instance associated with a lexer mode.\r\n\t *\r\n\t * The start state for a `DFA` constructed with this constructor should be a `TokensStartState`, which is the start\r\n\t * state for a lexer mode. The prediction made by this DFA determines the lexer rule which matches the current\r\n\t * input.\r\n\t *\r\n\t * @param atnStartState The start state for the mode.\r\n\t */\r\n\tconstructor(atnStartState: TokensStartState);\r\n\t/**\r\n\t * Constructs a `DFA` instance associated with a decision.\r\n\t *\r\n\t * @param atnStartState The decision associated with this DFA.\r\n\t * @param decision The decision number.\r\n\t */\r\n\tconstructor(atnStartState: DecisionState, decision: number);\r\n\tconstructor(@NotNull atnStartState: ATNState, decision: number = 0) {\r\n\t\tif (!atnStartState.atn) {\r\n\t\t\tthrow new Error(\"The ATNState must be associated with an ATN\");\r\n\t\t}\r\n\r\n\t\tthis.atnStartState = atnStartState;\r\n\t\tthis.atn = atnStartState.atn;\r\n\t\tthis.decision = decision;\r\n\r\n\t\t// Precedence DFAs are associated with the special precedence decision created for left-recursive rules which\r\n\t\t// evaluate their alternatives using a precedence hierarchy. When such a decision is encountered, we mark this\r\n\t\t// DFA instance as a precedence DFA and initialize the initial states s0 and s0full to special DFAState\r\n\t\t// instances which use outgoing edges to link to the actual start state used for each precedence level.\r\n\t\tlet isPrecedenceDfa: boolean = false;\r\n\t\tif (atnStartState instanceof StarLoopEntryState) {\r\n\t\t\tif (atnStartState.precedenceRuleDecision) {\r\n\t\t\t\tisPrecedenceDfa = true;\r\n\t\t\t\tthis.s0 = new DFAState(new ATNConfigSet());\r\n\t\t\t\tthis.s0full = new DFAState(new ATNConfigSet());\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.precedenceDfa = isPrecedenceDfa;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets whether this DFA is a precedence DFA. Precedence DFAs use a special\r\n\t * start state {@link #s0} which is not stored in {@link #states}. The\r\n\t * {@link DFAState#edges} array for this start state contains outgoing edges\r\n\t * supplying individual start states corresponding to specific precedence\r\n\t * values.\r\n\t *\r\n\t * @returns `true` if this is a precedence DFA; otherwise,\r\n\t * `false`.\r\n\t * @see Parser.precedence\r\n\t */\r\n\tget isPrecedenceDfa(): boolean {\r\n\t\treturn this.precedenceDfa;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the start state for a specific precedence value.\r\n\t *\r\n\t * @param precedence The current precedence.\r\n\t * @returns The start state corresponding to the specified precedence, or\r\n\t * `undefined` if no start state exists for the specified precedence.\r\n\t *\r\n\t * @ if this is not a precedence DFA.\r\n\t * @see `isPrecedenceDfa`\r\n\t */\r\n\tpublic getPrecedenceStartState(precedence: number, fullContext: boolean): DFAState | undefined {\r\n\t\tif (!this.isPrecedenceDfa) {\r\n\t\t\tthrow new Error(\"Only precedence DFAs may contain a precedence start state.\");\r\n\t\t}\r\n\r\n\t\t// s0 and s0full are never null for a precedence DFA\r\n\t\tif (fullContext) {\r\n\t\t\treturn (this.s0full as DFAState).getTarget(precedence);\r\n\t\t}\r\n\t\telse {\r\n\t\t\treturn (this.s0 as DFAState).getTarget(precedence);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Set the start state for a specific precedence value.\r\n\t *\r\n\t * @param precedence The current precedence.\r\n\t * @param startState The start state corresponding to the specified\r\n\t * precedence.\r\n\t *\r\n\t * @ if this is not a precedence DFA.\r\n\t * @see `isPrecedenceDfa`\r\n\t */\r\n\tpublic setPrecedenceStartState(precedence: number, fullContext: boolean, startState: DFAState): void {\r\n\t\tif (!this.isPrecedenceDfa) {\r\n\t\t\tthrow new Error(\"Only precedence DFAs may contain a precedence start state.\");\r\n\t\t}\r\n\r\n\t\tif (precedence < 0) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (fullContext) {\r\n\t\t\t// s0full is never null for a precedence DFA\r\n\t\t\t(this.s0full as DFAState).setTarget(precedence, startState);\r\n\t\t}\r\n\t\telse {\r\n\t\t\t// s0 is never null for a precedence DFA\r\n\t\t\t(this.s0 as DFAState).setTarget(precedence, startState);\r\n\t\t}\r\n\t}\r\n\r\n\tget isEmpty(): boolean {\r\n\t\tif (this.isPrecedenceDfa) {\r\n\t\t\t// s0 and s0full are never null for a precedence DFA\r\n\t\t\treturn this.s0!.getEdgeMap().size === 0 && this.s0full!.getEdgeMap().size === 0;\r\n\t\t}\r\n\r\n\t\treturn this.s0 == null && this.s0full == null;\r\n\t}\r\n\r\n\tget isContextSensitive(): boolean {\r\n\t\tif (this.isPrecedenceDfa) {\r\n\t\t\t// s0full is never null for a precedence DFA\r\n\t\t\treturn (this.s0full as DFAState).getEdgeMap().size > 0;\r\n\t\t}\r\n\r\n\t\treturn this.s0full != null;\r\n\t}\r\n\r\n\tpublic addState(state: DFAState): DFAState {\r\n\t\tstate.stateNumber = this.nextStateNumber++;\r\n\t\treturn this.states.getOrAdd(state);\r\n\t}\r\n\r\n\tpublic toString(): string;\r\n\tpublic toString(/*@NotNull*/ vocabulary: Vocabulary): string;\r\n\tpublic toString(/*@NotNull*/ vocabulary: Vocabulary, ruleNames: string[] | undefined): string;\r\n\tpublic toString(vocabulary?: Vocabulary, ruleNames?: string[]): string {\r\n\t\tif (!vocabulary) {\r\n\t\t\tvocabulary = VocabularyImpl.EMPTY_VOCABULARY;\r\n\t\t}\r\n\r\n\t\tif (!this.s0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\tlet serializer: DFASerializer;\r\n\t\tif (ruleNames) {\r\n\t\t\tserializer = new DFASerializer(this, vocabulary, ruleNames, this.atnStartState.atn);\r\n\t\t} else {\r\n\t\t\tserializer = new DFASerializer(this, vocabulary);\r\n\t\t}\r\n\r\n\t\treturn serializer.toString();\r\n\t}\r\n\r\n\tpublic toLexerString(): string {\r\n\t\tif (!this.s0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\tlet serializer: DFASerializer = new LexerDFASerializer(this);\r\n\t\treturn serializer.toString();\r\n\t}\r\n}\r\n"]}