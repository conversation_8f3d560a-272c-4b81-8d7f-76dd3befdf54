{"version": 3, "file": "Dependents.js", "sourceRoot": "", "sources": ["../../src/Dependents.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;GAGG;AACH,IAAY,UA6DX;AA7DD,WAAY,UAAU;IAErB;;OAEG;IACH,2CAAI,CAAA;IACJ;;;OAGG;IACH,iDAAO,CAAA;IACP;;;OAGG;IACH,mDAAQ,CAAA;IACR;;;OAGG;IACH,qDAAS,CAAA;IACT;;;OAGG;IACH,yDAAW,CAAA;IACX;;;OAGG;IACH,mDAAQ,CAAA;IACR;;;;OAIG;IACH,yEAAmB,CAAA;IACnB;;;;OAIG;IACH,uEAAkB,CAAA;IAClB;;;;;;;OAOG;IACH,uDAAU,CAAA;IACV;;;;;;;OAOG;IACH,qDAAS,CAAA;AACV,CAAC,EA7DW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QA6DrB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.1349829-07:00\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport enum Dependents {\r\n\r\n\t/**\r\n\t * The element is dependent upon the specified rule.\r\n\t */\r\n\tSELF,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's parents\r\n\t * (rules which directly reference it).\r\n\t */\r\n\tPARENTS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's children\r\n\t * (rules which it directly references).\r\n\t */\r\n\tCHILDREN,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's ancestors\r\n\t * (the transitive closure of `PARENTS` rules).\r\n\t */\r\n\tANCESTORS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's descendants\r\n\t * (the transitive closure of `CHILDREN` rules).\r\n\t */\r\n\tDESCENDANTS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's siblings\r\n\t * (the union of `CHILDREN` of its `PARENTS`).\r\n\t */\r\n\tSIBLINGS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's preceeding\r\n\t * siblings (the union of `CHILDREN` of its `PARENTS` which\r\n\t * appear before a reference to the rule).\r\n\t */\r\n\tPRECEEDING_SIBLINGS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's following\r\n\t * siblings (the union of `CHILDREN` of its `PARENTS` which\r\n\t * appear after a reference to the rule).\r\n\t */\r\n\tFOLLOWING_SIBLINGS,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's preceeding\r\n\t * elements (rules which might end before the start of the specified rule\r\n\t * while parsing). This is calculated by taking the\r\n\t * `PRECEEDING_SIBLINGS` of the rule and each of its\r\n\t * `ANCESTORS`, along with the `DESCENDANTS` of those\r\n\t * elements.\r\n\t */\r\n\tPRECEEDING,\r\n\t/**\r\n\t * The element is dependent upon the set of the specified rule's following\r\n\t * elements (rules which might start after the end of the specified rule\r\n\t * while parsing). This is calculated by taking the\r\n\t * `FOLLOWING_SIBLINGS` of the rule and each of its\r\n\t * `ANCESTORS`, along with the `DESCENDANTS` of those\r\n\t * elements.\r\n\t */\r\n\tFOLLOWING,\r\n}\r\n"]}