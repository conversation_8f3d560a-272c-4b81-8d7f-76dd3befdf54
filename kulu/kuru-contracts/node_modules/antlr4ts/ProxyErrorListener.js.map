{"version": 3, "file": "ProxyErrorListener.js", "sourceRoot": "", "sources": ["../../src/ProxyErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,6CAAiD;AAEjD;;;;;;GAMG;AACH,MAAa,kBAAkB;IAE9B,YAAoB,SAAsB;QAAtB,cAAS,GAAT,SAAS,CAAa;QACzC,IAAI,CAAC,SAAS,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACrC;IACF,CAAC;IAES,YAAY;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAGM,WAAW,CACR,UAA8B,EACvC,eAA8B,EAC9B,IAAY,EACZ,kBAA0B,EACjB,GAAW,EACpB,CAAmC;QACnC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnC,IAAI,QAAQ,CAAC,WAAW,EAAE;gBACzB,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aACpF;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD;AAbA;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IAIP,WAAA,oBAAO,CAAA;qDAOR;AAzBF,gDA0BC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:56.8126690-07:00\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { Override, NotNull } from \"./Decorators\";\r\n\r\n/**\r\n * This implementation of {@link ANTLRErrorListener} dispatches all calls to a\r\n * collection of delegate listeners. This reduces the effort required to support multiple\r\n * listeners.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ProxyErrorListener<TSymbol, TL<PERSON><PERSON> extends ANTLRErrorListener<TSymbol>> implements ANTLRErrorListener<TSymbol> {\r\n\r\n\tconstructor(private delegates: TListener[]) {\r\n\t\tif (!delegates) {\r\n\t\t\tthrow new Error(\"Invalid delegates\");\r\n\t\t}\r\n\t}\r\n\r\n\tprotected getDelegates(): ReadonlyArray<TListener> {\r\n\t\treturn this.delegates;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic syntaxError<T extends TSymbol>(\r\n\t\t@NotNull recognizer: Recognizer<T, any>,\r\n\t\toffendingSymbol: T | undefined,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number,\r\n\t\t@NotNull msg: string,\r\n\t\te: RecognitionException | undefined): void {\r\n\t\tthis.delegates.forEach((listener) => {\r\n\t\t\tif (listener.syntaxError) {\r\n\t\t\t\tlistener.syntaxError(recognizer, offendingSymbol, line, charPositionInLine, msg, e);\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n}\r\n"]}