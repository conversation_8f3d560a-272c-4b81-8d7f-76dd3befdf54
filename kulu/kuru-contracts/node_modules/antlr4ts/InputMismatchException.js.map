{"version": 3, "file": "InputMismatchException.js", "sourceRoot": "", "sources": ["../../src/InputMismatchException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,iEAA8D;AAC9D,6CAAuC;AAIvC;;GAEG;AACH,IAAa,sBAAsB,GAAnC,MAAa,sBAAuB,SAAQ,2CAAoB;IAK/D,YAAqB,UAAkB,EAAE,KAAc,EAAE,OAA2B;QACnF,IAAI,OAAO,KAAK,SAAS,EAAE;YAC1B,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;SAC7B;QAED,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAEnD,IAAI,KAAK,KAAK,SAAS,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;CACD,CAAA;AAlBY,sBAAsB;IAKrB,WAAA,oBAAO,CAAA;GALR,sBAAsB,CAkBlC;AAlBY,wDAAsB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.5187682-07:00\r\n\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { NotNull } from \"./Decorators\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\n\r\n/** This signifies any kind of mismatched input exceptions such as\r\n *  when the current input does not match the expected token.\r\n */\r\nexport class InputMismatchException extends RecognitionException {\r\n\t//private static serialVersionUID: number =  1532568338707443067L;\r\n\r\n\tconstructor(/*@NotNull*/ recognizer: Parser);\r\n\tconstructor(/*@NotNull*/ recognizer: Parser, state: number, context: ParserRuleContext);\r\n\tconstructor(@NotNull recognizer: Parser, state?: number, context?: ParserRuleContext) {\r\n\t\tif (context === undefined) {\r\n\t\t\tcontext = recognizer.context;\r\n\t\t}\r\n\r\n\t\tsuper(recognizer, recognizer.inputStream, context);\r\n\r\n\t\tif (state !== undefined) {\r\n\t\t\tthis.setOffendingState(state);\r\n\t\t}\r\n\r\n\t\tthis.setOffendingToken(recognizer, recognizer.currentToken);\r\n\t}\r\n}\r\n"]}