{"version": 3, "file": "CodePointCharStream.js", "sourceRoot": "", "sources": ["../../src/CodePointCharStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,iCAAiC;AAGjC,2CAAwC;AACxC,8CAA2C;AAC3C,6CAAwC;AAExC;;;;;;;GAOG;AACH,MAAa,mBAAmB;IAO/B,iEAAiE;IACjE,oCAAoC;IACpC,YAAsB,KAA4C,EAAE,QAAgB,EAAE,SAAiB,EAAE,IAAY;QACpH,OAAO;QACP,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,IAAW,eAAe;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAaM,MAAM,CAAC,UAAU,CAAC,eAAgC,EAAE,IAAa;QACvE,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,GAAG,qBAAS,CAAC,mBAAmB,CAAC;SACrC;QAED,0CAA0C;QAC1C,EAAE;QACF,mDAAmD;QACnD,oDAAoD;QACpD,gCAAgC;QAChC,EAAE;QACF,mDAAmD;QACnD,iDAAiD;QACjD,qDAAqD;QACrD,0CAA0C;QAC1C,OAAO,IAAI,mBAAmB,CAC7B,eAAe,CAAC,KAAK,EAAE,EACvB,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,CAAC;IACR,CAAC;IAGM,OAAO;QACb,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE;YACtC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,qBAAS,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;IAClB,CAAC;IAGD,IAAW,KAAK;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAGD,IAAW,IAAI;QACd,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,qDAAqD;IAE9C,IAAI;QACV,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAGM,OAAO,CAAC,MAAc;QAC5B,uEAAuE;IACxE,CAAC;IAGM,IAAI,CAAC,KAAa;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACxB,CAAC;IAGD,IAAW,UAAU;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAGM,EAAE,CAAC,CAAS;QAClB,IAAI,MAAc,CAAC;QACnB,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACrB,KAAK,CAAC,CAAC;gBACN,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBACxB,IAAI,MAAM,GAAG,CAAC,EAAE;oBACf,OAAO,qBAAS,CAAC,GAAG,CAAC;iBACrB;gBAED,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE5B,KAAK,CAAC;gBACL,YAAY;gBACZ,OAAO,CAAC,CAAC;YAEV,KAAK,CAAC;gBACL,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;oBACxB,OAAO,qBAAS,CAAC,GAAG,CAAC;iBACrB;gBAED,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC5B;QAED,MAAM,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED,8DAA8D;IAEvD,OAAO,CAAC,QAAkB;QAChC,MAAM,QAAQ,GAAW,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,MAAM,GAAG,GAAW,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;QAEhF,IAAI,IAAI,CAAC,MAAM,YAAY,UAAU,EAAE;YACtC,OAAO,MAAM,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAC3F;aAAM;YACN,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAC1F;IACF,CAAC;CACD;AArFA;IADC,qBAAQ;kDAQR;AAGD;IADC,qBAAQ;gDAGR;AAGD;IADC,qBAAQ;+CAGR;AAID;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;+CAGR;AAGD;IADC,qBAAQ;qDAGR;AAGD;IADC,qBAAQ;mDAGR;AAGD;IADC,qBAAQ;6CA0BR;AAID;IADC,qBAAQ;kDAUR;AA5IF,kDA6IC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport * as assert from \"assert\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { CodePointBuffer } from \"./CodePointBuffer\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Override } from \"./Decorators\";\r\n\r\n/**\r\n * Alternative to {@link ANTLRInputStream} which treats the input\r\n * as a series of Unicode code points, instead of a series of UTF-16\r\n * code units.\r\n *\r\n * Use this if you need to parse input which potentially contains\r\n * Unicode values > U+FFFF.\r\n */\r\nexport class CodePointCharStream implements CharStream {\r\n\tprivate readonly _array: Uint8Array | Uint16Array | Int32Array;\r\n\tprivate readonly _size: number;\r\n\tprivate readonly _name: string;\r\n\r\n\tprivate _position: number;\r\n\r\n\t// Use the factory method {@link #fromBuffer(CodePointBuffer)} to\r\n\t// construct instances of this type.\r\n\tprotected constructor(array: Uint8Array | Uint16Array | Int32Array, position: number, remaining: number, name: string) {\r\n\t\t// TODO\r\n\t\tassert(position === 0);\r\n\t\tthis._array = array;\r\n\t\tthis._size = remaining;\r\n\t\tthis._name = name;\r\n\t\tthis._position = 0;\r\n\t}\r\n\r\n\tpublic get internalStorage(): Uint8Array | Uint16Array | Int32Array {\r\n\t\treturn this._array;\r\n\t}\r\n\r\n\t/**\r\n\t * Constructs a {@link CodePointCharStream} which provides access\r\n\t * to the Unicode code points stored in {@code codePointBuffer}.\r\n\t */\r\n\tpublic static fromBuffer(codePointBuffer: CodePointBuffer): CodePointCharStream;\r\n\r\n\t/**\r\n\t * Constructs a named {@link CodePointCharStream} which provides access\r\n\t * to the Unicode code points stored in {@code codePointBuffer}.\r\n\t */\r\n\tpublic static fromBuffer(codePointBuffer: CodePointBuffer, name: string): CodePointCharStream;\r\n\tpublic static fromBuffer(codePointBuffer: CodePointBuffer, name?: string): CodePointCharStream {\r\n\t\tif (name === undefined || name.length === 0) {\r\n\t\t\tname = IntStream.UNKNOWN_SOURCE_NAME;\r\n\t\t}\r\n\r\n\t\t// Java lacks generics on primitive types.\r\n\t\t//\r\n\t\t// To avoid lots of calls to virtual methods in the\r\n\t\t// very hot codepath of LA() below, we construct one\r\n\t\t// of three concrete subclasses.\r\n\t\t//\r\n\t\t// The concrete subclasses directly access the code\r\n\t\t// points stored in the underlying array (byte[],\r\n\t\t// char[], or int[]), so we can avoid lots of virtual\r\n\t\t// method calls to ByteBuffer.get(offset).\r\n\t\treturn new CodePointCharStream(\r\n\t\t\tcodePointBuffer.array(),\r\n\t\t\tcodePointBuffer.position,\r\n\t\t\tcodePointBuffer.remaining,\r\n\t\t\tname);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic consume(): void {\r\n\t\tif (this._size - this._position === 0) {\r\n\t\t\tassert(this.LA(1) === IntStream.EOF);\r\n\t\t\tthrow new RangeError(\"cannot consume EOF\");\r\n\t\t}\r\n\r\n\t\tthis._position++;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic get index(): number {\r\n\t\treturn this._position;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic get size(): number {\r\n\t\treturn this._size;\r\n\t}\r\n\r\n\t/** mark/release do nothing; we have entire buffer */\r\n\t@Override\r\n\tpublic mark(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic release(marker: number): void {\r\n\t\t// No default implementation since this stream buffers the entire input\r\n\t}\r\n\r\n\t@Override\r\n\tpublic seek(index: number): void {\r\n\t\tthis._position = index;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic get sourceName(): string {\r\n\t\treturn this._name;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this.getText(Interval.of(0, this.size - 1));\r\n\t}\r\n\r\n\t@Override\r\n\tpublic LA(i: number): number {\r\n\t\tlet offset: number;\r\n\t\tswitch (Math.sign(i)) {\r\n\t\t\tcase -1:\r\n\t\t\t\toffset = this.index + i;\r\n\t\t\t\tif (offset < 0) {\r\n\t\t\t\t\treturn IntStream.EOF;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn this._array[offset];\r\n\r\n\t\t\tcase 0:\r\n\t\t\t\t// Undefined\r\n\t\t\t\treturn 0;\r\n\r\n\t\t\tcase 1:\r\n\t\t\t\toffset = this.index + i - 1;\r\n\t\t\t\tif (offset >= this.size) {\r\n\t\t\t\t\treturn IntStream.EOF;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn this._array[offset];\r\n\t\t}\r\n\r\n\t\tthrow new RangeError(\"Not reached\");\r\n\t}\r\n\r\n\t/** Return the UTF-16 encoded string for the given interval */\r\n\t@Override\r\n\tpublic getText(interval: Interval): string {\r\n\t\tconst startIdx: number = Math.min(interval.a, this.size);\r\n\t\tconst len: number = Math.min(interval.b - interval.a + 1, this.size - startIdx);\r\n\r\n\t\tif (this._array instanceof Int32Array) {\r\n\t\t\treturn String.fromCodePoint(...Array.from(this._array.subarray(startIdx, startIdx + len)));\r\n\t\t} else {\r\n\t\t\treturn String.fromCharCode(...Array.from(this._array.subarray(startIdx, startIdx + len)));\r\n\t\t}\r\n\t}\r\n}\r\n"]}