{"version": 3, "file": "Recognizer.js", "sourceRoot": "", "sources": ["../../src/Recognizer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,iEAA8D;AAG9D,6DAA0D;AAG1D,6CAAyD;AACzD,mCAAgC;AAIhC,sCAAsC;AAEtC,MAAsB,UAAU;IAAhC;QAUkB,eAAU,GAAuC,CAAC,2CAAoB,CAAC,QAAQ,CAAC,CAAC;QAI1F,iBAAY,GAAG,CAAC,CAAC,CAAC;IAkN3B,CAAC;IAtMA;;;;OAIG;IAEI,eAAe;QACrB,IAAI,UAAU,GAAe,IAAI,CAAC,UAAU,CAAC;QAC7C,IAAI,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,IAAI,kBAAkB,GAAG,IAAI,GAAG,EAAkB,CAAC;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAI,WAAW,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,WAAW,IAAI,IAAI,EAAE;oBACxB,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;iBACvC;gBAED,IAAI,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACjD,IAAI,YAAY,IAAI,IAAI,EAAE;oBACzB,kBAAkB,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;iBACxC;aACD;YAED,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAK,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,GAAG,kBAAkB,CAAC;YAC5B,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SACrD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;OAIG;IAEI,eAAe;QACrB,IAAI,SAAS,GAAa,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SACjF;QAED,IAAI,MAAM,GAA4C,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClG,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAChC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACpD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,YAAY,CAAC,SAAiB;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,KAAK,IAAI,IAAI,EAAE;YAClB,OAAO,KAAK,CAAC;SACb;QACD,OAAO,aAAK,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IAEH,IAAI,aAAa;QAChB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAC/C,CAAC;IAOD;;;;OAIG;IAEH,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IACzB,CAAC;IAED;;;;OAIG;IAEH,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACH,IAAI,WAAW,CAAU,WAA2B;QACnD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,IAAI,SAAS;QACZ,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,8EAA8E;IAEvE,cAAc,CAAU,CAAuB;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACX,OAAO,EAAE,CAAC;SACV;QACD,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,IAAI,kBAAkB,GAAW,KAAK,CAAC,kBAAkB,CAAC;QAC1D,OAAO,OAAO,GAAG,IAAI,GAAG,GAAG,GAAG,kBAAkB,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAU,QAAqC;QACrE,IAAI,CAAC,QAAQ,EAAE;YACd,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAEM,mBAAmB,CAAU,QAAqC;QACxE,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SACpC;IACF,CAAC;IAEM,oBAAoB;QAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,CAAC;IAGM,iBAAiB;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAEM,wBAAwB;QAC9B,OAAO,IAAI,uCAAkB,CAAuC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC/F,CAAC;IAED,oEAAoE;IACpE,uCAAuC;IAChC,OAAO,CACb,SAAkC,EAClC,SAAiB,EACjB,WAAmB;QACnB,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,QAAQ,CACd,QAAiC,EACjC,UAAkB;QAClB,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,MAAM,CACZ,SAAkC,EAClC,SAAiB,EACjB,WAAmB;QACnB,sBAAsB;IACvB,CAAC;IAED,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED;;;;;;OAMG;IACH,IAAI,KAAK,CAAC,QAAgB;QAC3B,6CAA6C;QAC3C,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC/B,+CAA+C;IAC9C,CAAC;;AA5NsB,cAAG,GAAW,CAAC,CAAC,CAAC;AAEzB,4BAAiB,GAC/B,IAAI,OAAO,EAA2C,CAAC;AACzC,4BAAiB,GAC/B,IAAI,OAAO,EAAyC,CAAC;AAItD;IAFC,6BAAgB,CAAC,QAAQ,CAAC;IAC1B,oBAAO;8CAC0F;AAsBlG;IADC,oBAAO;iDAwBP;AAQD;IADC,oBAAO;iDAcP;AAkBD;IADC,oBAAO;+CAGP;AAaD;IADC,oBAAO;qCAGP;AAQD;IADC,oBAAO;IAWS,WAAA,oBAAO,CAAA;6CARvB;AAuBD;IADC,oBAAO;IACe,WAAA,oBAAO,CAAA;gDAQ7B;AAKD;IAAyB,WAAA,oBAAO,CAAA;kDAK/B;AAED;IAA4B,WAAA,oBAAO,CAAA;qDAKlC;AAOD;IADC,oBAAO;mDAGP;AAlLF,gCAgOC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.1954441-07:00\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNSimulator } from \"./atn/ATNSimulator\";\r\nimport { ConsoleErrorListener } from \"./ConsoleErrorListener\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { ParseInfo } from \"./atn/ParseInfo\";\r\nimport { ProxyErrorListener } from \"./ProxyErrorListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { SuppressWarnings, NotNull } from \"./Decorators\";\r\nimport { Token } from \"./Token\";\r\nimport { Vocabulary } from \"./Vocabulary\";\r\nimport { VocabularyImpl } from \"./VocabularyImpl\";\r\n\r\nimport * as Utils from \"./misc/Utils\";\r\n\r\nexport abstract class Recognizer<TSymbol, ATNInterpreter extends ATNSimulator> {\r\n\tpublic static readonly EOF: number = -1;\r\n\r\n\tprivate static tokenTypeMapCache =\r\n\t\tnew WeakMap<Vocabulary, ReadonlyMap<string, number>>();\r\n\tprivate static ruleIndexMapCache =\r\n\t\tnew WeakMap<string[], ReadonlyMap<string, number>>();\r\n\r\n\t@SuppressWarnings(\"serial\")\r\n\t@NotNull\r\n\tprivate readonly _listeners: Array<ANTLRErrorListener<TSymbol>> = [ConsoleErrorListener.INSTANCE];\r\n\r\n\tprotected _interp!: ATNInterpreter;\r\n\r\n\tprivate _stateNumber = -1;\r\n\r\n\tpublic abstract readonly ruleNames: string[];\r\n\r\n\t/**\r\n\t * Get the vocabulary used by the recognizer.\r\n\t *\r\n\t * @returns A {@link Vocabulary} instance providing information about the\r\n\t * vocabulary used by the grammar.\r\n\t */\r\n\tpublic abstract readonly vocabulary: Vocabulary;\r\n\r\n\t/**\r\n\t * Get a map from token names to token types.\r\n\t *\r\n\t * Used for XPath and tree pattern compilation.\r\n\t */\r\n\t@NotNull\r\n\tpublic getTokenTypeMap(): ReadonlyMap<string, number> {\r\n\t\tlet vocabulary: Vocabulary = this.vocabulary;\r\n\t\tlet result = Recognizer.tokenTypeMapCache.get(vocabulary);\r\n\t\tif (result == null) {\r\n\t\t\tlet intermediateResult = new Map<string, number>();\r\n\t\t\tfor (let i = 0; i <= this.atn.maxTokenType; i++) {\r\n\t\t\t\tlet literalName = vocabulary.getLiteralName(i);\r\n\t\t\t\tif (literalName != null) {\r\n\t\t\t\t\tintermediateResult.set(literalName, i);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet symbolicName = vocabulary.getSymbolicName(i);\r\n\t\t\t\tif (symbolicName != null) {\r\n\t\t\t\t\tintermediateResult.set(symbolicName, i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tintermediateResult.set(\"EOF\", Token.EOF);\r\n\t\t\tresult = intermediateResult;\r\n\t\t\tRecognizer.tokenTypeMapCache.set(vocabulary, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t/**\r\n\t * Get a map from rule names to rule indexes.\r\n\t *\r\n\t * Used for XPath and tree pattern compilation.\r\n\t */\r\n\t@NotNull\r\n\tpublic getRuleIndexMap(): ReadonlyMap<string, number> {\r\n\t\tlet ruleNames: string[] = this.ruleNames;\r\n\t\tif (ruleNames == null) {\r\n\t\t\tthrow new Error(\"The current recognizer does not provide a list of rule names.\");\r\n\t\t}\r\n\r\n\t\tlet result: ReadonlyMap<string, number> | undefined = Recognizer.ruleIndexMapCache.get(ruleNames);\r\n\t\tif (result == null) {\r\n\t\t\tresult = Utils.toMap(ruleNames);\r\n\t\t\tRecognizer.ruleIndexMapCache.set(ruleNames, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic getTokenType(tokenName: string): number {\r\n\t\tlet ttype = this.getTokenTypeMap().get(tokenName);\r\n\t\tif (ttype != null) {\r\n\t\t\treturn ttype;\r\n\t\t}\r\n\t\treturn Token.INVALID_TYPE;\r\n\t}\r\n\r\n\t/**\r\n\t * If this recognizer was generated, it will have a serialized ATN\r\n\t * representation of the grammar.\r\n\t *\r\n\t * For interpreters, we don't know their serialized ATN despite having\r\n\t * created the interpreter from it.\r\n\t */\r\n\t@NotNull\r\n\tget serializedATN(): string {\r\n\t\tthrow new Error(\"there is no serialized ATN\");\r\n\t}\r\n\r\n\t/** For debugging and other purposes, might want the grammar name.\r\n\t *  Have ANTLR generate an implementation for this method.\r\n\t */\r\n\tpublic abstract readonly grammarFileName: string;\r\n\r\n\t/**\r\n\t * Get the {@link ATN} used by the recognizer for prediction.\r\n\t *\r\n\t * @returns The {@link ATN} used by the recognizer for prediction.\r\n\t */\r\n\t@NotNull\r\n\tget atn(): ATN {\r\n\t\treturn this._interp.atn;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the ATN interpreter used by the recognizer for prediction.\r\n\t *\r\n\t * @returns The ATN interpreter used by the recognizer for prediction.\r\n\t */\r\n\t@NotNull\r\n\tget interpreter(): ATNInterpreter {\r\n\t\treturn this._interp;\r\n\t}\r\n\r\n\t/**\r\n\t * Set the ATN interpreter used by the recognizer for prediction.\r\n\t *\r\n\t * @param interpreter The ATN interpreter used by the recognizer for\r\n\t * prediction.\r\n\t */\r\n\tset interpreter(@NotNull interpreter: ATNInterpreter) {\r\n\t\tthis._interp = interpreter;\r\n\t}\r\n\r\n\t/** If profiling during the parse/lex, this will return DecisionInfo records\r\n\t *  for each decision in recognizer in a ParseInfo object.\r\n\t *\r\n\t * @since 4.3\r\n\t */\r\n\tget parseInfo(): Promise<ParseInfo | undefined> {\r\n\t\treturn Promise.resolve(undefined);\r\n\t}\r\n\r\n\t/** What is the error header, normally line/character position information? */\r\n\t@NotNull\r\n\tpublic getErrorHeader(@NotNull e: RecognitionException): string {\r\n\t\tlet token = e.getOffendingToken();\r\n\t\tif (!token) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\t\tlet line = token.line;\r\n\t\tlet charPositionInLine: number = token.charPositionInLine;\r\n\t\treturn \"line \" + line + \":\" + charPositionInLine;\r\n\t}\r\n\r\n\t/**\r\n\t * @exception NullPointerException if `listener` is `undefined`.\r\n\t */\r\n\tpublic addErrorListener(@NotNull listener: ANTLRErrorListener<TSymbol>): void {\r\n\t\tif (!listener) {\r\n\t\t\tthrow new TypeError(\"listener must not be null\");\r\n\t\t}\r\n\t\tthis._listeners.push(listener);\r\n\t}\r\n\r\n\tpublic removeErrorListener(@NotNull listener: ANTLRErrorListener<TSymbol>): void {\r\n\t\tlet position = this._listeners.indexOf(listener);\r\n\t\tif (position !== -1) {\r\n\t\t\tthis._listeners.splice(position, 1);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic removeErrorListeners(): void {\r\n\t\tthis._listeners.length = 0;\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getErrorListeners(): Array<ANTLRErrorListener<TSymbol>> {\r\n\t\treturn this._listeners.slice(0);\r\n\t}\r\n\r\n\tpublic getErrorListenerDispatch(): ANTLRErrorListener<TSymbol> {\r\n\t\treturn new ProxyErrorListener<TSymbol, ANTLRErrorListener<TSymbol>>(this.getErrorListeners());\r\n\t}\r\n\r\n\t// subclass needs to override these if there are sempreds or actions\r\n\t// that the ATN interp needs to execute\r\n\tpublic sempred(\r\n\t\t_localctx: RuleContext | undefined,\r\n\t\truleIndex: number,\r\n\t\tactionIndex: number): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\tpublic precpred(\r\n\t\tlocalctx: RuleContext | undefined,\r\n\t\tprecedence: number): boolean {\r\n\t\treturn true;\r\n\t}\r\n\r\n\tpublic action(\r\n\t\t_localctx: RuleContext | undefined,\r\n\t\truleIndex: number,\r\n\t\tactionIndex: number): void {\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\tget state(): number {\r\n\t\treturn this._stateNumber;\r\n\t}\r\n\r\n\t/** Indicate that the recognizer has changed internal state that is\r\n\t *  consistent with the ATN state passed in.  This way we always know\r\n\t *  where we are in the ATN as the parser goes along. The rule\r\n\t *  context objects form a stack that lets us see the stack of\r\n\t *  invoking rules. Combine this and we have complete ATN\r\n\t *  configuration information.\r\n\t */\r\n\tset state(atnState: number) {\r\n//\t\tSystem.err.println(\"setState \"+atnState);\r\n\t\tthis._stateNumber = atnState;\r\n//\t\tif ( traceATNStates ) _ctx.trace(atnState);\r\n\t}\r\n\r\n\tpublic abstract readonly inputStream: IntStream | undefined;\r\n}\r\n"]}