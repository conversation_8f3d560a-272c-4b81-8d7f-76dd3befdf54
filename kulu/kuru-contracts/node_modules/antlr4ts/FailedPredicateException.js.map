{"version": 3, "file": "FailedPredicateException.js", "sourceRoot": "", "sources": ["../../src/FailedPredicateException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAQH,iEAA8D;AAE9D,6CAAuC;AACvC,mEAAgE;AAEhE;;;;GAIG;AACH,IAAa,wBAAwB,GAArC,MAAa,wBAAyB,SAAQ,2CAAoB;IAOjE,YAAqB,UAAkB,EAAE,SAAkB,EAAE,OAAgB;QAC5E,KAAK,CACJ,UAAU,EACV,UAAU,CAAC,WAAW,EACtB,UAAU,CAAC,OAAO,EAClB,wBAAwB,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,GAAa,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAgC,CAAC;QAC3D,IAAI,KAAK,YAAY,yCAAmB,EAAE;YACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;YAClC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;SACvC;aACI;YACJ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;SACzB;QAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,KAAK,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAGO,MAAM,CAAC,aAAa,CAAC,SAA6B,EAAE,OAA2B;QACtF,IAAI,OAAO,EAAE;YACZ,OAAO,OAAO,CAAC;SACf;QAED,OAAO,sBAAsB,SAAS,IAAI,CAAC;IAC5C,CAAC;CACD,CAAA;AAPA;IADC,oBAAO;mDAOP;AAhDW,wBAAwB;IAOvB,WAAA,oBAAO,CAAA;GAPR,wBAAwB,CAiDpC;AAjDY,4DAAwB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.4099946-07:00\r\n\r\nimport { AbstractPredicateTransition } from \"./atn/AbstractPredicateTransition\";\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNState } from \"./atn/ATNState\";\r\nimport { Parser } from \"./Parser\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { NotNull } from \"./Decorators\";\r\nimport { PredicateTransition } from \"./atn/PredicateTransition\";\r\n\r\n/** A semantic predicate failed during validation.  Validation of predicates\r\n *  occurs when normally parsing the alternative just like matching a token.\r\n *  Disambiguating predicate evaluation occurs when we test a predicate during\r\n *  prediction.\r\n */\r\nexport class FailedPredicateException extends RecognitionException {\r\n\t//private static serialVersionUID: number =  5379330841495778709L;\r\n\r\n\tprivate _ruleIndex: number;\r\n\tprivate _predicateIndex: number;\r\n\tprivate _predicate?: string;\r\n\r\n\tconstructor(@NotNull recognizer: Parser, predicate?: string, message?: string) {\r\n\t\tsuper(\r\n\t\t\trecognizer,\r\n\t\t\trecognizer.inputStream,\r\n\t\t\trecognizer.context,\r\n\t\t\tFailedPredicateException.formatMessage(predicate, message));\r\n\t\tlet s: ATNState = recognizer.interpreter.atn.states[recognizer.state];\r\n\r\n\t\tlet trans = s.transition(0) as AbstractPredicateTransition;\r\n\t\tif (trans instanceof PredicateTransition) {\r\n\t\t\tthis._ruleIndex = trans.ruleIndex;\r\n\t\t\tthis._predicateIndex = trans.predIndex;\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis._ruleIndex = 0;\r\n\t\t\tthis._predicateIndex = 0;\r\n\t\t}\r\n\r\n\t\tthis._predicate = predicate;\r\n\t\tsuper.setOffendingToken(recognizer, recognizer.currentToken);\r\n\t}\r\n\r\n\tget ruleIndex(): number {\r\n\t\treturn this._ruleIndex;\r\n\t}\r\n\r\n\tget predicateIndex(): number {\r\n\t\treturn this._predicateIndex;\r\n\t}\r\n\r\n\tget predicate(): string | undefined {\r\n\t\treturn this._predicate;\r\n\t}\r\n\r\n\t@NotNull\r\n\tprivate static formatMessage(predicate: string | undefined, message: string | undefined): string {\r\n\t\tif (message) {\r\n\t\t\treturn message;\r\n\t\t}\r\n\r\n\t\treturn `failed predicate: {${predicate}}?`;\r\n\t}\r\n}\r\n"]}