{"version": 3, "file": "CommonToken.js", "sourceRoot": "", "sources": ["../../src/CommonToken.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,8CAA2C;AAC3C,6CAAiD;AAEjD,mCAAgC;AAIhC,IAAa,WAAW,GAAxB,MAAa,WAAW;IA6DvB,YAAY,IAAY,EAAE,IAAa,EAAW,SAAwD,WAAW,CAAC,YAAY,EAAE,UAAkB,aAAK,CAAC,eAAe,EAAE,QAAgB,CAAC,EAAE,OAAe,CAAC;QAjDhN;;WAEG;QACK,UAAK,GAAW,CAAC,CAAC;QAC1B;;;WAGG;QACK,wBAAmB,GAAW,CAAC,CAAC,CAAC,CAAC,0BAA0B;QACpE;;;WAGG;QACK,aAAQ,GAAW,aAAK,CAAC,eAAe,CAAC;QAqBjD;;WAEG;QACO,UAAK,GAAW,CAAC,CAAC,CAAC;QAa5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;YAChC,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;SAC5D;IACF,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,SAAS,CAAU,QAAe;QAC/C,IAAI,MAAM,GAAgB,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACzJ,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC7B,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;QACnC,MAAM,CAAC,mBAAmB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;QAEzD,IAAI,QAAQ,YAAY,WAAW,EAAE;YACpC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC9B,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;SAChC;aAAM;YACN,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC7B,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC;SAC/E;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,YAAY;IACZ,IAAI,IAAI,CAAC,IAAY;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,YAAY;IACZ,IAAI,IAAI,CAAC,IAAY;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAGD,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC;SAClB;QAED,IAAI,KAAK,GAA2B,IAAI,CAAC,WAAW,CAAC;QACrD,IAAI,KAAK,IAAI,IAAI,EAAE;YAClB,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,CAAC,GAAW,KAAK,CAAC,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;aAAM;YACN,OAAO,OAAO,CAAC;SACf;IACF,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY;IACZ,IAAI,IAAI,CAAC,IAAwB;QAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAGD,IAAI,kBAAkB;QACrB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACjC,CAAC;IAED,YAAY;IACZ,IAAI,kBAAkB,CAAC,kBAA0B;QAChD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAC/C,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED,YAAY;IACZ,IAAI,OAAO,CAAC,OAAe;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,IAAI,UAAU,CAAC,KAAa;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,IAAI,SAAS,CAAC,IAAY;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,YAAY;IACZ,IAAI,UAAU,CAAC,KAAa;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC3B,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC3B,CAAC;IAMM,QAAQ,CAA+C,UAAgD;QAC7G,IAAI,UAAU,GAAW,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;YACtB,UAAU,GAAG,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;SACzC;QAED,IAAI,GAAG,GAAuB,IAAI,CAAC,IAAI,CAAC;QACxC,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAChC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAChC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChC;aAAM;YACN,GAAG,GAAG,WAAW,CAAC;SAClB;QAED,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,UAAU,EAAE;YACf,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;IAClL,CAAC;CACD,CAAA;AA/OA;;;GAGG;AACuB,wBAAY,GACrC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;AA8B1C;IADC,oBAAO;2CACwD;AAoEhE;IADC,qBAAQ;uCAGR;AAQD;IADC,qBAAQ;uCAGR;AAQD;IADC,qBAAQ;uCAiBR;AAiBD;IADC,qBAAQ;qDAGR;AAQD;IADC,qBAAQ;0CAGR;AAQD;IADC,qBAAQ;6CAGR;AAOD;IADC,qBAAQ;4CAGR;AAOD;IADC,qBAAQ;6CAGR;AAQD;IADC,qBAAQ;8CAGR;AAGD;IADC,qBAAQ;8CAGR;AAMD;IADC,qBAAQ;2CAsBR;AAzJD;IAAyB,WAAA,oBAAO,CAAA;kCAe/B;AArGW,WAAW;IA6DmB,WAAA,oBAAO,CAAA;GA7DrC,WAAW,CAgPvB;AAhPY,kCAAW", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.1614404-07:00\r\n\r\nimport { ATNSimulator } from \"./atn/ATNSimulator\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenSource } from \"./TokenSource\";\r\nimport { WritableToken } from \"./WritableToken\";\r\n\r\nexport class CommonToken implements WritableToken {\r\n\t/**\r\n\t * An empty {@link Tuple2} which is used as the default value of\r\n\t * {@link #source} for tokens that do not have a source.\r\n\t */\r\n\tprotected static readonly EMPTY_SOURCE: { source?: TokenSource, stream?: CharStream } =\r\n\t\t{ source: undefined, stream: undefined };\r\n\r\n\t/**\r\n\t * This is the backing field for `type`.\r\n\t */\r\n\tprivate _type: number;\r\n\t/**\r\n\t * This is the backing field for {@link #getLine} and {@link #setLine}.\r\n\t */\r\n\tprivate _line: number = 0;\r\n\t/**\r\n\t * This is the backing field for {@link #getCharPositionInLine} and\r\n\t * {@link #setCharPositionInLine}.\r\n\t */\r\n\tprivate _charPositionInLine: number = -1; // set to invalid position\r\n\t/**\r\n\t * This is the backing field for {@link #getChannel} and\r\n\t * {@link #setChannel}.\r\n\t */\r\n\tprivate _channel: number = Token.DEFAULT_CHANNEL;\r\n\t/**\r\n\t * This is the backing field for {@link #getTokenSource} and\r\n\t * {@link #getInputStream}.\r\n\t *\r\n\t * These properties share a field to reduce the memory footprint of\r\n\t * {@link CommonToken}. Tokens created by a {@link CommonTokenFactory} from\r\n\t * the same source and input stream share a reference to the same\r\n\t * {@link Tuple2} containing these values.\r\n\t */\r\n\t@NotNull\r\n\tprotected source: { source?: TokenSource, stream?: CharStream };\r\n\r\n\t/**\r\n\t * This is the backing field for {@link #getText} when the token text is\r\n\t * explicitly set in the constructor or via {@link #setText}.\r\n\t *\r\n\t * @see `text`\r\n\t */\r\n\tprivate _text?: string;\r\n\r\n\t/**\r\n\t * This is the backing field for `tokenIndex`.\r\n\t */\r\n\tprotected index: number = -1;\r\n\r\n\t/**\r\n\t * This is the backing field for `startIndex`.\r\n\t */\r\n\tprotected start: number;\r\n\r\n\t/**\r\n\t * This is the backing field for `stopIndex`.\r\n\t */\r\n\tprivate stop: number;\r\n\r\n\tconstructor(type: number, text?: string, @NotNull source: { source?: TokenSource, stream?: CharStream } = CommonToken.EMPTY_SOURCE, channel: number = Token.DEFAULT_CHANNEL, start: number = 0, stop: number = 0) {\r\n\t\tthis._text = text;\r\n\t\tthis._type = type;\r\n\t\tthis.source = source;\r\n\t\tthis._channel = channel;\r\n\t\tthis.start = start;\r\n\t\tthis.stop = stop;\r\n\t\tif (source.source != null) {\r\n\t\t\tthis._line = source.source.line;\r\n\t\t\tthis._charPositionInLine = source.source.charPositionInLine;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Constructs a new {@link CommonToken} as a copy of another {@link Token}.\r\n\t *\r\n\t * If `oldToken` is also a {@link CommonToken} instance, the newly\r\n\t * constructed token will share a reference to the {@link #text} field and\r\n\t * the {@link Tuple2} stored in {@link #source}. Otherwise, {@link #text} will\r\n\t * be assigned the result of calling {@link #getText}, and {@link #source}\r\n\t * will be constructed from the result of {@link Token#getTokenSource} and\r\n\t * {@link Token#getInputStream}.\r\n\t *\r\n\t * @param oldToken The token to copy.\r\n\t */\r\n\tpublic static fromToken(@NotNull oldToken: Token): CommonToken {\r\n\t\tlet result: CommonToken = new CommonToken(oldToken.type, undefined, CommonToken.EMPTY_SOURCE, oldToken.channel, oldToken.startIndex, oldToken.stopIndex);\r\n\t\tresult._line = oldToken.line;\r\n\t\tresult.index = oldToken.tokenIndex;\r\n\t\tresult._charPositionInLine = oldToken.charPositionInLine;\r\n\r\n\t\tif (oldToken instanceof CommonToken) {\r\n\t\t\tresult._text = oldToken._text;\r\n\t\t\tresult.source = oldToken.source;\r\n\t\t} else {\r\n\t\t\tresult._text = oldToken.text;\r\n\t\t\tresult.source = { source: oldToken.tokenSource, stream: oldToken.inputStream };\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t@Override\r\n\tget type(): number {\r\n\t\treturn this._type;\r\n\t}\r\n\r\n\t// @Override\r\n\tset type(type: number) {\r\n\t\tthis._type = type;\r\n\t}\r\n\r\n\t@Override\r\n\tget line(): number {\r\n\t\treturn this._line;\r\n\t}\r\n\r\n\t// @Override\r\n\tset line(line: number) {\r\n\t\tthis._line = line;\r\n\t}\r\n\r\n\t@Override\r\n\tget text(): string | undefined {\r\n\t\tif (this._text != null) {\r\n\t\t\treturn this._text;\r\n\t\t}\r\n\r\n\t\tlet input: CharStream | undefined = this.inputStream;\r\n\t\tif (input == null) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet n: number = input.size;\r\n\t\tif (this.start < n && this.stop < n) {\r\n\t\t\treturn input.getText(Interval.of(this.start, this.stop));\r\n\t\t} else {\r\n\t\t\treturn \"<EOF>\";\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Explicitly set the text for this token. If {code text} is not\r\n\t * `undefined`, then {@link #getText} will return this value rather than\r\n\t * extracting the text from the input.\r\n\t *\r\n\t * @param text The explicit text of the token, or `undefined` if the text\r\n\t * should be obtained from the input along with the start and stop indexes\r\n\t * of the token.\r\n\t */\r\n\t// @Override\r\n\tset text(text: string | undefined) {\r\n\t\tthis._text = text;\r\n\t}\r\n\r\n\t@Override\r\n\tget charPositionInLine(): number {\r\n\t\treturn this._charPositionInLine;\r\n\t}\r\n\r\n\t// @Override\r\n\tset charPositionInLine(charPositionInLine: number) {\r\n\t\tthis._charPositionInLine = charPositionInLine;\r\n\t}\r\n\r\n\t@Override\r\n\tget channel(): number {\r\n\t\treturn this._channel;\r\n\t}\r\n\r\n\t// @Override\r\n\tset channel(channel: number) {\r\n\t\tthis._channel = channel;\r\n\t}\r\n\r\n\t@Override\r\n\tget startIndex(): number {\r\n\t\treturn this.start;\r\n\t}\r\n\r\n\tset startIndex(start: number) {\r\n\t\tthis.start = start;\r\n\t}\r\n\r\n\t@Override\r\n\tget stopIndex(): number {\r\n\t\treturn this.stop;\r\n\t}\r\n\r\n\tset stopIndex(stop: number) {\r\n\t\tthis.stop = stop;\r\n\t}\r\n\r\n\t@Override\r\n\tget tokenIndex(): number {\r\n\t\treturn this.index;\r\n\t}\r\n\r\n\t// @Override\r\n\tset tokenIndex(index: number) {\r\n\t\tthis.index = index;\r\n\t}\r\n\r\n\t@Override\r\n\tget tokenSource(): TokenSource | undefined {\r\n\t\treturn this.source.source;\r\n\t}\r\n\r\n\t@Override\r\n\tget inputStream(): CharStream | undefined {\r\n\t\treturn this.source.stream;\r\n\t}\r\n\r\n\tpublic toString(): string;\r\n\tpublic toString<TSymbol, ATNInterpreter extends ATNSimulator>(recognizer: Recognizer<TSymbol, ATNInterpreter> | undefined): string;\r\n\r\n\t@Override\r\n\tpublic toString<TSymbol, ATNInterpreter extends ATNSimulator>(recognizer?: Recognizer<TSymbol, ATNInterpreter>): string {\r\n\t\tlet channelStr: string = \"\";\r\n\t\tif (this._channel > 0) {\r\n\t\t\tchannelStr = \",channel=\" + this._channel;\r\n\t\t}\r\n\r\n\t\tlet txt: string | undefined = this.text;\r\n\t\tif (txt != null) {\r\n\t\t\ttxt = txt.replace(/\\n/g, \"\\\\n\");\r\n\t\t\ttxt = txt.replace(/\\r/g, \"\\\\r\");\r\n\t\t\ttxt = txt.replace(/\\t/g, \"\\\\t\");\r\n\t\t} else {\r\n\t\t\ttxt = \"<no text>\";\r\n\t\t}\r\n\r\n\t\tlet typeString = String(this._type);\r\n\t\tif (recognizer) {\r\n\t\t\ttypeString = recognizer.vocabulary.getDisplayName(this._type);\r\n\t\t}\r\n\r\n\t\treturn \"[@\" + this.tokenIndex + \",\" + this.start + \":\" + this.stop + \"='\" + txt + \"',<\" + typeString + \">\" + channelStr + \",\" + this._line + \":\" + this.charPositionInLine + \"]\";\r\n\t}\r\n}\r\n"]}