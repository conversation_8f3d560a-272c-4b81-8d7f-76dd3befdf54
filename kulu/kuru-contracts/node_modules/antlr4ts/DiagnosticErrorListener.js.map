{"version": 3, "file": "DiagnosticErrorListener.js", "sourceRoot": "", "sources": ["../../src/DiagnosticErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAMH,0CAAuC;AAQvC,6CAAiD;AACjD,8CAA2C;AAE3C;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,uBAAuB;IAEnC;;;;;;OAMG;IACH,YAAsB,YAAqB,IAAI;QAAzB,cAAS,GAAT,SAAS,CAAgB;QAC9C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAGM,WAAW;IACjB,YAAY;IACZ,UAA8B,EAC9B,eAA8B,EAC9B,IAAY,EACZ,kBAA0B;IAC1B,YAAY;IACZ,GAAW,EACX,CAAmC;QAEnC,sBAAsB;IACvB,CAAC;IAGM,eAAe,CACZ,UAAkB,EAClB,GAAQ,EACjB,UAAkB,EAClB,SAAiB,EACjB,KAAc,EACd,SAA6B,EACpB,OAAqB;QAC9B,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;YAC7B,OAAO;SACP;QAED,IAAI,QAAQ,GAAW,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACpE,IAAI,eAAe,GAAW,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC1E,IAAI,IAAI,GAAW,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;QACtF,IAAI,OAAO,GAAW,qBAAqB,QAAQ,eAAe,eAAe,YAAY,IAAI,GAAG,CAAC;QACrG,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAGM,2BAA2B,CACxB,UAAkB,EAClB,GAAQ,EACjB,UAAkB,EAClB,SAAiB,EACjB,eAAmC,EAC1B,aAA6B;QACtC,IAAI,MAAM,GAAW,8CAA8C,CAAC;QACpE,IAAI,QAAQ,GAAW,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACpE,IAAI,IAAI,GAAW,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;QACtF,IAAI,OAAO,GAAW,iCAAiC,QAAQ,YAAY,IAAI,GAAG,CAAC;QACnF,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAGM,wBAAwB,CACrB,UAAkB,EAClB,GAAQ,EACjB,UAAkB,EAClB,SAAiB,EACjB,UAAkB,EACT,WAA2B;QACpC,IAAI,MAAM,GAAW,2CAA2C,CAAC;QACjE,IAAI,QAAQ,GAAW,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACpE,IAAI,IAAI,GAAW,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;QACtF,IAAI,OAAO,GAAW,8BAA8B,QAAQ,YAAY,IAAI,GAAG,CAAC;QAChF,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAES,sBAAsB,CACtB,UAAkB,EAClB,GAAQ;QACjB,IAAI,QAAQ,GAAW,GAAG,CAAC,QAAQ,CAAC;QACpC,IAAI,SAAS,GAAW,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC;QAEpD,IAAI,SAAS,GAAa,UAAU,CAAC,SAAS,CAAC;QAC/C,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;YACnD,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAC3B;QAED,IAAI,QAAQ,GAAW,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,EAAE;YACd,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAC3B;QAED,OAAO,GAAG,QAAQ,KAAK,QAAQ,GAAG,CAAC;IACpC,CAAC;IAED;;;;;;;;;;OAUG;IAEO,kBAAkB,CAAC,YAAgC,EAAW,OAAqB;QAC5F,IAAI,YAAY,IAAI,IAAI,EAAE;YACzB,OAAO,YAAY,CAAC;SACpB;QAED,IAAI,MAAM,GAAW,IAAI,eAAM,EAAE,CAAC;QAClC,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;YAC3B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACvB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AA1GA;IADC,qBAAQ;0DAYR;AAGD;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IAKP,WAAA,oBAAO,CAAA;8DAUR;AAGD;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IAIP,WAAA,oBAAO,CAAA;0EAMR;AAGD;IADC,qBAAQ;IAEP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IAIP,WAAA,oBAAO,CAAA;uEAMR;AAED;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;qEAeR;AAcD;IADC,oBAAO;IACwD,WAAA,oBAAO,CAAA;iEAWtE;AAvHF,0DAwHC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.2133685-07:00\r\n\r\nimport { ATNConfig } from \"./atn/ATNConfig\";\r\nimport { ATNConfigSet } from \"./atn/ATNConfigSet\";\r\nimport { BitSet } from \"./misc/BitSet\";\r\nimport { DFA } from \"./dfa/DFA\";\r\nimport { Parser } from \"./Parser\";\r\nimport { ParserErrorListener } from \"./ParserErrorListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { SimulatorState } from \"./atn/SimulatorState\";\r\nimport { Token } from \"./Token\";\r\nimport { Override, NotNull } from \"./Decorators\";\r\nimport { Interval } from \"./misc/Interval\";\r\n\r\n/**\r\n * This implementation of {@link ANTLRErrorListener} can be used to identify\r\n * certain potential correctness and performance problems in grammars. \"Reports\"\r\n * are made by calling {@link Parser#notifyErrorListeners} with the appropriate\r\n * message.\r\n *\r\n * * **Ambiguities**: These are cases where more than one path through the\r\n *   grammar can match the input.\r\n * * **Weak context sensitivity**: These are cases where full-context\r\n *   prediction resolved an SLL conflict to a unique alternative which equaled the\r\n *   minimum alternative of the SLL conflict.\r\n * * **Strong (forced) context sensitivity**: These are cases where the\r\n *   full-context prediction resolved an SLL conflict to a unique alternative,\r\n *   *and* the minimum alternative of the SLL conflict was found to not be\r\n *   a truly viable alternative. Two-stage parsing cannot be used for inputs where\r\n *   this situation occurs.\r\n *\r\n * <AUTHOR> Harwell\r\n */\r\nexport class DiagnosticErrorListener implements ParserErrorListener {\r\n\r\n\t/**\r\n\t * Initializes a new instance of {@link DiagnosticErrorListener}, specifying\r\n\t * whether all ambiguities or only exact ambiguities are reported.\r\n\t *\r\n\t * @param exactOnly `true` to report only exact ambiguities, otherwise\r\n\t * `false` to report all ambiguities.  Defaults to true.\r\n\t */\r\n\tconstructor(protected exactOnly: boolean = true) {\r\n\t\tthis.exactOnly = exactOnly;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic syntaxError<T extends Token>(\r\n\t\t/*@NotNull*/\r\n\t\trecognizer: Recognizer<T, any>,\r\n\t\toffendingSymbol: T | undefined,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number,\r\n\t\t/*@NotNull*/\r\n\t\tmsg: string,\r\n\t\te: RecognitionException | undefined): void\r\n\t{\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportAmbiguity(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull dfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\texact: boolean,\r\n\t\tambigAlts: BitSet | undefined,\r\n\t\t@NotNull configs: ATNConfigSet): void {\r\n\t\tif (this.exactOnly && !exact) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet decision: string = this.getDecisionDescription(recognizer, dfa);\r\n\t\tlet conflictingAlts: BitSet = this.getConflictingAlts(ambigAlts, configs);\r\n\t\tlet text: string = recognizer.inputStream.getText(Interval.of(startIndex, stopIndex));\r\n\t\tlet message: string = `reportAmbiguity d=${decision}: ambigAlts=${conflictingAlts}, input='${text}'`;\r\n\t\trecognizer.notifyErrorListeners(message);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportAttemptingFullContext(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull dfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tconflictingAlts: BitSet | undefined,\r\n\t\t@NotNull conflictState: SimulatorState): void {\r\n\t\tlet format: string = \"reportAttemptingFullContext d=%s, input='%s'\";\r\n\t\tlet decision: string = this.getDecisionDescription(recognizer, dfa);\r\n\t\tlet text: string = recognizer.inputStream.getText(Interval.of(startIndex, stopIndex));\r\n\t\tlet message: string = `reportAttemptingFullContext d=${decision}, input='${text}'`;\r\n\t\trecognizer.notifyErrorListeners(message);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportContextSensitivity(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull dfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tprediction: number,\r\n\t\t@NotNull acceptState: SimulatorState): void {\r\n\t\tlet format: string = \"reportContextSensitivity d=%s, input='%s'\";\r\n\t\tlet decision: string = this.getDecisionDescription(recognizer, dfa);\r\n\t\tlet text: string = recognizer.inputStream.getText(Interval.of(startIndex, stopIndex));\r\n\t\tlet message: string = `reportContextSensitivity d=${decision}, input='${text}'`;\r\n\t\trecognizer.notifyErrorListeners(message);\r\n\t}\r\n\r\n\tprotected getDecisionDescription(\r\n\t\t@NotNull recognizer: Parser,\r\n\t\t@NotNull dfa: DFA): string {\r\n\t\tlet decision: number = dfa.decision;\r\n\t\tlet ruleIndex: number = dfa.atnStartState.ruleIndex;\r\n\r\n\t\tlet ruleNames: string[] = recognizer.ruleNames;\r\n\t\tif (ruleIndex < 0 || ruleIndex >= ruleNames.length) {\r\n\t\t\treturn decision.toString();\r\n\t\t}\r\n\r\n\t\tlet ruleName: string = ruleNames[ruleIndex];\r\n\t\tif (!ruleName) {\r\n\t\t\treturn decision.toString();\r\n\t\t}\r\n\r\n\t\treturn `${decision} (${ruleName})`;\r\n\t}\r\n\r\n\t/**\r\n\t * Computes the set of conflicting or ambiguous alternatives from a\r\n\t * configuration set, if that information was not already provided by the\r\n\t * parser.\r\n\t *\r\n\t * @param reportedAlts The set of conflicting or ambiguous alternatives, as\r\n\t * reported by the parser.\r\n\t * @param configs The conflicting or ambiguous configuration set.\r\n\t * @returns Returns `reportedAlts` if it is not `undefined`, otherwise\r\n\t * returns the set of alternatives represented in `configs`.\r\n\t */\r\n\t@NotNull\r\n\tprotected getConflictingAlts(reportedAlts: BitSet | undefined, @NotNull configs: ATNConfigSet): BitSet {\r\n\t\tif (reportedAlts != null) {\r\n\t\t\treturn reportedAlts;\r\n\t\t}\r\n\r\n\t\tlet result: BitSet = new BitSet();\r\n\t\tfor (let config of configs) {\r\n\t\t\tresult.set(config.alt);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n}\r\n"]}