{"version": 3, "file": "RuleContext.js", "sourceRoot": "", "sources": ["../../src/RuleContext.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,mCAAgC;AAEhC,6CAA0C;AAC1C,8CAA2C;AAE3C,8CAA2C;AAC3C,6CAAwC;AACxC,wCAAqC;AAErC,2DAAwD;AAExD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiDG;AACH,MAAa,WAAY,SAAQ,mBAAQ;IAMxC,YAAY,MAAoB,EAAE,aAAsB;QACvD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,MAAmB,EAAE,aAAqB;QACvE,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAA4B,IAAI,CAAC;QACtC,OAAO,CAAC,EAAE;YACT,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;YACd,CAAC,EAAE,CAAC;SACJ;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,+CAA+C;IAG/C,IAAI,cAAc;QACjB,OAAO,mBAAQ,CAAC,OAAO,CAAC;IACzB,CAAC;IAGD,IAAI,WAAW,KAAkB,OAAO,IAAI,CAAC,CAAC,CAAC;IAG/C,IAAI,MAAM,KAA8B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9D,qDAAqD;IAE9C,SAAS,CAAC,MAAmB;QACnC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAGD,IAAI,OAAO,KAAkB,OAAO,IAAI,CAAC,CAAC,CAAC;IAE3C;;;;;;OAMG;IAEH,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YAC1B,OAAO,EAAE,CAAC;SACV;QAED,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACzC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SACjC;QAED,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,SAAS,KAAa,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtC;;;;;;;;OAQG;IACH,IAAI,SAAS,KAAa,OAAO,SAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAE1D;;;;;;;OAOG;IACH,IAAI,SAAS,CAAC,SAAiB;QAC9B,mDAAmD;IACpD,CAAC;IAGM,QAAQ,CAAC,CAAS;QACxB,MAAM,IAAI,UAAU,CAAC,+DAA+D,CAAC,CAAC;IACvF,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,MAAM,CAAI,OAA4B;QAC5C,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAgBM,YAAY,CAAC,KAAyB;QAC5C,OAAO,aAAK,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAWM,QAAQ,CACd,IAAsC,EACtC,IAAkB;QAElB,MAAM,SAAS,GAAG,CAAC,IAAI,YAAY,uBAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QACvE,IAAI,GAAG,IAAI,IAAI,qCAAiB,CAAC,YAAY,EAAE,CAAC;QAEhD,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,GAA4B,IAAI,CAAC;QACtC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YACvB,IAAI,CAAC,SAAS,EAAE;gBACf,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;oBACf,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;iBACzB;aACD;iBAAM;gBACN,IAAI,SAAS,GAAW,CAAC,CAAC,SAAS,CAAC;gBACpC,IAAI,QAAQ,GAAW,CAAC,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;oBACtE,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC/C,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClB;YAED,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACnD,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;aACb;YAED,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;SACd;QAED,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;CACD;AA3IA;IADC,qBAAQ;iDAGR;AAGD;IADC,qBAAQ;8CACsC;AAG/C;IADC,qBAAQ;yCACqD;AAI9D;IADC,qBAAQ;4CAGR;AAGD;IADC,qBAAQ;0CACkC;AAU3C;IADC,qBAAQ;uCAYR;AA4BD;IADC,qBAAQ;2CAGR;AAGD;IADC,qBAAQ;6CAGR;AAGD;IADC,qBAAQ;yCAGR;AAgBD;IADC,qBAAQ;+CAGR;AApIF,kCA+KC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.3490837-07:00\r\n\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { Parser } from \"./Parser\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { RuleNode } from \"./tree/RuleNode\";\r\nimport { ParseTree } from \"./tree/ParseTree\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { Override } from \"./Decorators\";\r\nimport { Trees } from \"./tree/Trees\";\r\nimport { ParseTreeVisitor } from \"./tree/ParseTreeVisitor\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\n\r\n/** A rule context is a record of a single rule invocation.\r\n *\r\n *  We form a stack of these context objects using the parent\r\n *  pointer. A parent pointer of `undefined` indicates that the current\r\n *  context is the bottom of the stack. The ParserRuleContext subclass\r\n *  as a children list so that we can turn this data structure into a\r\n *  tree.\r\n *\r\n *  The root node always has a `undefined` pointer and invokingState of -1.\r\n *\r\n *  Upon entry to parsing, the first invoked rule function creates a\r\n *  context object (a subclass specialized for that rule such as\r\n *  SContext) and makes it the root of a parse tree, recorded by field\r\n *  Parser._ctx.\r\n *\r\n *  public final SContext s() throws RecognitionException {\r\n *      SContext _localctx = new SContext(_ctx, state); <-- create new node\r\n *      enterRule(_localctx, 0, RULE_s);                     <-- push it\r\n *      ...\r\n *      exitRule();                                          <-- pop back to _localctx\r\n *      return _localctx;\r\n *  }\r\n *\r\n *  A subsequent rule invocation of r from the start rule s pushes a\r\n *  new context object for r whose parent points at s and use invoking\r\n *  state is the state with r emanating as edge label.\r\n *\r\n *  The invokingState fields from a context object to the root\r\n *  together form a stack of rule indication states where the root\r\n *  (bottom of the stack) has a -1 sentinel value. If we invoke start\r\n *  symbol s then call r1, which calls r2, the  would look like\r\n *  this:\r\n *\r\n *     SContext[-1]   <- root node (bottom of the stack)\r\n *     R1Context[p]   <- p in rule s called r1\r\n *     R2Context[q]   <- q in rule r1 called r2\r\n *\r\n *  So the top of the stack, _ctx, represents a call to the current\r\n *  rule and it holds the return address from another rule that invoke\r\n *  to this rule. To invoke a rule, we must always have a current context.\r\n *\r\n *  The parent contexts are useful for computing lookahead sets and\r\n *  getting error information.\r\n *\r\n *  These objects are used during parsing and prediction.\r\n *  For the special case of parsers, we use the subclass\r\n *  ParserRuleContext.\r\n *\r\n *  @see ParserRuleContext\r\n */\r\nexport class RuleContext extends RuleNode {\r\n\tpublic _parent: RuleContext | undefined;\r\n\tpublic invokingState: number;\r\n\r\n\tconstructor();\r\n\tconstructor(parent: RuleContext | undefined, invokingState: number);\r\n\tconstructor(parent?: RuleContext, invokingState?: number) {\r\n\t\tsuper();\r\n\t\tthis._parent = parent;\r\n\t\tthis.invokingState = invokingState != null ? invokingState : -1;\r\n\t}\r\n\r\n\tpublic static getChildContext(parent: RuleContext, invokingState: number): RuleContext {\r\n\t\treturn new RuleContext(parent, invokingState);\r\n\t}\r\n\r\n\tpublic depth(): number {\r\n\t\tlet n = 0;\r\n\t\tlet p: RuleContext | undefined = this;\r\n\t\twhile (p) {\r\n\t\t\tp = p._parent;\r\n\t\t\tn++;\r\n\t\t}\r\n\t\treturn n;\r\n\t}\r\n\r\n\t/** A context is empty if there is no invoking state; meaning nobody called\r\n\t *  current context.\r\n\t */\r\n\tget isEmpty(): boolean {\r\n\t\treturn this.invokingState === -1;\r\n\t}\r\n\r\n\t// satisfy the ParseTree / SyntaxTree interface\r\n\r\n\t@Override\r\n\tget sourceInterval(): Interval {\r\n\t\treturn Interval.INVALID;\r\n\t}\r\n\r\n\t@Override\r\n\tget ruleContext(): RuleContext { return this; }\r\n\r\n\t@Override\r\n\tget parent(): RuleContext | undefined { return this._parent; }\r\n\r\n\t/** @since 4.7. {@see ParseTree#setParent} comment */\r\n\t@Override\r\n\tpublic setParent(parent: RuleContext): void {\r\n\t\tthis._parent = parent;\r\n\t}\r\n\r\n\t@Override\r\n\tget payload(): RuleContext { return this; }\r\n\r\n\t/** Return the combined text of all child nodes. This method only considers\r\n\t *  tokens which have been added to the parse tree.\r\n\t *\r\n\t *  Since tokens on hidden channels (e.g. whitespace or comments) are not\r\n\t *  added to the parse trees, they will not appear in the output of this\r\n\t *  method.\r\n\t */\r\n\t@Override\r\n\tget text(): string {\r\n\t\tif (this.childCount === 0) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\tlet builder = \"\";\r\n\t\tfor (let i = 0; i < this.childCount; i++) {\r\n\t\t\tbuilder += this.getChild(i).text;\r\n\t\t}\r\n\r\n\t\treturn builder.toString();\r\n\t}\r\n\r\n\tget ruleIndex(): number { return -1; }\r\n\r\n\t/** For rule associated with this parse tree internal node, return\r\n\t *  the outer alternative number used to match the input. Default\r\n\t *  implementation does not compute nor store this alt num. Create\r\n\t *  a subclass of ParserRuleContext with backing field and set\r\n\t *  option contextSuperClass.\r\n\t *  to set it.\r\n\t *\r\n\t *  @since 4.5.3\r\n\t */\r\n\tget altNumber(): number { return ATN.INVALID_ALT_NUMBER; }\r\n\r\n\t/** Set the outer alternative number for this context node. Default\r\n\t *  implementation does nothing to avoid backing field overhead for\r\n\t *  trees that don't need it.  Create\r\n\t *  a subclass of ParserRuleContext with backing field and set\r\n\t *  option contextSuperClass.\r\n\t *\r\n\t *  @since 4.5.3\r\n\t */\r\n\tset altNumber(altNumber: number) {\r\n\t\t// intentionally ignored by the base implementation\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getChild(i: number): ParseTree {\r\n\t\tthrow new RangeError(\"i must be greater than or equal to 0 and less than childCount\");\r\n\t}\r\n\r\n\t@Override\r\n\tget childCount(): number {\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic accept<T>(visitor: ParseTreeVisitor<T>): T {\r\n\t\treturn visitor.visitChildren(this);\r\n\t}\r\n\r\n\t/** Print out a whole tree, not just a node, in LISP format\r\n\t *  (root child1 .. childN). Print just a node if this is a leaf.\r\n\t *  We have to know the recognizer so we can get rule names.\r\n\t */\r\n\tpublic toStringTree(recog: Parser): string;\r\n\r\n\t/** Print out a whole tree, not just a node, in LISP format\r\n\t *  (root child1 .. childN). Print just a node if this is a leaf.\r\n\t */\r\n\tpublic toStringTree(ruleNames: string[] | undefined): string;\r\n\r\n\tpublic toStringTree(): string;\r\n\r\n\t@Override\r\n\tpublic toStringTree(recog?: Parser | string[]): string {\r\n\t\treturn Trees.toStringTree(this, recog);\r\n\t}\r\n\r\n\tpublic toString(): string;\r\n\tpublic toString(recog: Recognizer<any, any> | undefined): string;\r\n\tpublic toString(ruleNames: string[] | undefined): string;\r\n\r\n\t// // recog undefined unless ParserRuleContext, in which case we use subclass toString(...)\r\n\tpublic toString(recog: Recognizer<any, any> | undefined, stop: RuleContext | undefined): string;\r\n\r\n\tpublic toString(ruleNames: string[] | undefined, stop: RuleContext | undefined): string;\r\n\r\n\tpublic toString(\r\n\t\targ1?: Recognizer<any, any> | string[],\r\n\t\tstop?: RuleContext)\r\n\t\t: string {\r\n\t\tconst ruleNames = (arg1 instanceof Recognizer) ? arg1.ruleNames : arg1;\r\n\t\tstop = stop || ParserRuleContext.emptyContext();\r\n\r\n\t\tlet buf = \"\";\r\n\t\tlet p: RuleContext | undefined = this;\r\n\t\tbuf += (\"[\");\r\n\t\twhile (p && p !== stop) {\r\n\t\t\tif (!ruleNames) {\r\n\t\t\t\tif (!p.isEmpty) {\r\n\t\t\t\t\tbuf += (p.invokingState);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tlet ruleIndex: number = p.ruleIndex;\r\n\t\t\t\tlet ruleName: string = (ruleIndex >= 0 && ruleIndex < ruleNames.length)\r\n\t\t\t\t\t? ruleNames[ruleIndex] : ruleIndex.toString();\r\n\t\t\t\tbuf += (ruleName);\r\n\t\t\t}\r\n\r\n\t\t\tif (p._parent && (ruleNames || !p._parent.isEmpty)) {\r\n\t\t\t\tbuf += (\" \");\r\n\t\t\t}\r\n\r\n\t\t\tp = p._parent;\r\n\t\t}\r\n\r\n\t\tbuf += (\"]\");\r\n\t\treturn buf.toString();\r\n\t}\r\n}\r\n"]}