{"version": 3, "file": "CommonTokenStream.js", "sourceRoot": "", "sources": ["../../src/CommonTokenStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,+DAA4D;AAC5D,6CAAiD;AACjD,mCAAgC;AAGhC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,IAAa,iBAAiB,GAA9B,MAAa,iBAAkB,SAAQ,yCAAmB;IASzD;;;;;;;;;OASG;IACH,YAAqB,WAAwB,EAAE,UAAkB,aAAK,CAAC,eAAe;QACrF,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAGS,eAAe,CAAC,CAAS;QAClC,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAGS,KAAK,CAAC,CAAS;QACxB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,SAAS,CAAC;SACjB;QAED,IAAI,CAAC,GAAW,IAAI,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,uCAAuC;QACvC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACvB,0BAA0B;YAC1B,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,CAAC,EAAE,CAAC;SACJ;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAGM,KAAK,CAAC,CAAS;QACrB,wCAAwC;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,EAAE;YACZ,MAAM,IAAI,UAAU,CAAC,kCAAkC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,GAAW,IAAI,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,kCAAkC;QACrD,qBAAqB;QACrB,OAAO,CAAC,GAAG,CAAC,EAAE;YACb,8DAA8D;YAC9D,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBACrB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACjD;YACD,CAAC,EAAE,CAAC;SACJ;QAED,6BAA6B;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,2BAA2B;IACpB,0BAA0B;QAChC,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;gBAC/B,CAAC,EAAE,CAAC;aACJ;YAED,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;gBACzB,MAAM;aACN;SACD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;CACD,CAAA;AArEA;IADC,qBAAQ;wDAGR;AAGD;IADC,qBAAQ;8CAoBR;AAGD;IADC,qBAAQ;8CAyBR;AA5EW,iBAAiB;IAmBhB,WAAA,oBAAO,CAAA;GAnBR,iBAAiB,CA8F7B;AA9FY,8CAAiB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.3953157-07:00\r\n\r\nimport { BufferedTokenStream } from \"./BufferedTokenStream\";\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/**\r\n * This class extends {@link BufferedTokenStream} with functionality to filter\r\n * token streams to tokens on a particular channel (tokens where\r\n * {@link Token#getChannel} returns a particular value).\r\n *\r\n * This token stream provides access to all tokens by index or when calling\r\n * methods like {@link #getText}. The channel filtering is only used for code\r\n * accessing tokens via the lookahead methods {@link #LA}, {@link #LT}, and\r\n * {@link #LB}.\r\n *\r\n * By default, tokens are placed on the default channel\r\n * ({@link Token#DEFAULT_CHANNEL}), but may be reassigned by using the\r\n * `->channel(HIDDEN)` lexer command, or by using an embedded action to\r\n * call {@link Lexer#setChannel}.\r\n *\r\n * Note: lexer rules which use the `->skip` lexer command or call\r\n * {@link Lexer#skip} do not produce tokens at all, so input text matched by\r\n * such a rule will not be available as part of the token stream, regardless of\r\n * channel.\r\n */\r\nexport class CommonTokenStream extends BufferedTokenStream {\r\n\t/**\r\n\t * Specifies the channel to use for filtering tokens.\r\n\t *\r\n\t * The default value is {@link Token#DEFAULT_CHANNEL}, which matches the\r\n\t * default channel assigned to tokens created by the lexer.\r\n\t */\r\n\tprotected channel: number;\r\n\r\n\t/**\r\n\t * Constructs a new {@link CommonTokenStream} using the specified token\r\n\t * source and filtering tokens to the specified channel. Only tokens whose\r\n\t * {@link Token#getChannel} matches `channel` or have the\r\n\t * `Token.type` equal to {@link Token#EOF} will be returned by the\r\n\t * token stream lookahead methods.\r\n\t *\r\n\t * @param tokenSource The token source.\r\n\t * @param channel The channel to use for filtering tokens.\r\n\t */\r\n\tconstructor(@NotNull tokenSource: TokenSource, channel: number = Token.DEFAULT_CHANNEL) {\r\n\t\tsuper(tokenSource);\r\n\t\tthis.channel = channel;\r\n\t}\r\n\r\n\t@Override\r\n\tprotected adjustSeekIndex(i: number): number {\r\n\t\treturn this.nextTokenOnChannel(i, this.channel);\r\n\t}\r\n\r\n\t@Override\r\n\tprotected tryLB(k: number): Token | undefined {\r\n\t\tif ((this.p - k) < 0) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tlet i: number = this.p;\r\n\t\tlet n: number = 1;\r\n\t\t// find k good tokens looking backwards\r\n\t\twhile (n <= k && i > 0) {\r\n\t\t\t// skip off-channel tokens\r\n\t\t\ti = this.previousTokenOnChannel(i - 1, this.channel);\r\n\t\t\tn++;\r\n\t\t}\r\n\r\n\t\tif (i < 0) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn this.tokens[i];\r\n\t}\r\n\r\n\t@Override\r\n\tpublic tryLT(k: number): Token | undefined {\r\n\t\t//System.out.println(\"enter LT(\"+k+\")\");\r\n\t\tthis.lazyInit();\r\n\t\tif (k === 0) {\r\n\t\t\tthrow new RangeError(\"0 is not a valid lookahead index\");\r\n\t\t}\r\n\r\n\t\tif (k < 0) {\r\n\t\t\treturn this.tryLB(-k);\r\n\t\t}\r\n\r\n\t\tlet i: number = this.p;\r\n\t\tlet n: number = 1; // we know tokens[p] is a good one\r\n\t\t// find k good tokens\r\n\t\twhile (n < k) {\r\n\t\t\t// skip off-channel tokens, but make sure to not look past EOF\r\n\t\t\tif (this.sync(i + 1)) {\r\n\t\t\t\ti = this.nextTokenOnChannel(i + 1, this.channel);\r\n\t\t\t}\r\n\t\t\tn++;\r\n\t\t}\r\n\r\n\t\t//\t\tif ( i>range ) range = i;\r\n\t\treturn this.tokens[i];\r\n\t}\r\n\r\n\t/** Count EOF just once. */\r\n\tpublic getNumberOfOnChannelTokens(): number {\r\n\t\tlet n: number = 0;\r\n\t\tthis.fill();\r\n\t\tfor (let t of this.tokens) {\r\n\t\t\tif (t.channel === this.channel) {\r\n\t\t\t\tn++;\r\n\t\t\t}\r\n\r\n\t\t\tif (t.type === Token.EOF) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn n;\r\n\t}\r\n}\r\n"]}