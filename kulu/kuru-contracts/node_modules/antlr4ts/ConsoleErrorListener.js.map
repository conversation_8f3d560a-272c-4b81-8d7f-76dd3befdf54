{"version": 3, "file": "ConsoleErrorListener.js", "sourceRoot": "", "sources": ["../../src/ConsoleErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAQH;;;GAGG;AACH,MAAa,oBAAoB;IAMhC;;;;;;;;;;OAUG;IACI,WAAW,CACjB,UAA8B,EAC9B,eAAkB,EAClB,IAAY,EACZ,kBAA0B,EAC1B,GAAW,EACX,CAAmC;QACnC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE,CAAC,CAAC;IAC5D,CAAC;;AAzBF,oDA0BC;AAzBA;;GAEG;AACoB,6BAAQ,GAAyB,IAAI,oBAAoB,EAAE,CAAC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.5479602-07:00\r\n\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ConsoleErrorListener implements ANTLRErrorListener<any> {\r\n\t/**\r\n\t * Provides a default instance of {@link ConsoleErrorListener}.\r\n\t */\r\n\tpublic static readonly INSTANCE: ConsoleErrorListener = new ConsoleErrorListener();\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation prints messages to {@link System#err} containing the\r\n\t * values of `line`, `charPositionInLine`, and `msg` using\r\n\t * the following format.\r\n\t *\r\n\t * <pre>\r\n\t * line *line*:*charPositionInLine* *msg*\r\n\t * </pre>\r\n\t */\r\n\tpublic syntaxError<T>(\r\n\t\trecognizer: Recognizer<T, any>,\r\n\t\toffendingSymbol: T,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number,\r\n\t\tmsg: string,\r\n\t\te: RecognitionException | undefined): void {\r\n\t\tconsole.error(`line ${line}:${charPositionInLine} ${msg}`);\r\n\t}\r\n}\r\n"]}