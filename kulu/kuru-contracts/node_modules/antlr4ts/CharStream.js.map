{"version": 3, "file": "CharStream.js", "sourceRoot": "", "sources": ["../../src/CharStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:50.0659297-07:00\r\n\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { IntStream } from \"./IntStream\";\r\n\r\n/** A source of characters for an ANTLR lexer. */\r\nexport interface CharStream extends IntStream {\r\n\t/**\r\n\t * This method returns the text for a range of characters within this input\r\n\t * stream. This method is guaranteed to not throw an exception if the\r\n\t * specified `interval` lies entirely within a marked range. For more\r\n\t * information about marked ranges, see {@link IntStream#mark}.\r\n\t *\r\n\t * @param interval an interval within the stream\r\n\t * @returns the text of the specified interval\r\n\t *\r\n\t * @throws NullPointerException if `interval` is `undefined`\r\n\t * @throws IllegalArgumentException if `interval.a < 0`, or if\r\n\t * `interval.b < interval.a - 1`, or if `interval.b` lies at or\r\n\t * past the end of the stream\r\n\t * @throws UnsupportedOperationException if the stream does not support\r\n\t * getting the text of the specified interval\r\n\t */\r\n\t//@NotNull\r\n\tgetText(/*@NotNull*/ interval: Interval): string;\r\n}\r\n"]}