{"version": 3, "file": "Token.js", "sourceRoot": "", "sources": ["../../src/Token.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH,2CAAwC;AA8DxC,IAAiB,KAAK,CAkCrB;AAlCD,WAAiB,KAAK;IACR,kBAAY,GAAW,CAAC,CAAC;IAEtC;;OAEG;IACU,aAAO,GAAW,CAAC,CAAC,CAAC;IAErB,yBAAmB,GAAW,CAAC,CAAC;IAEhC,SAAG,GAAW,qBAAS,CAAC,GAAG,CAAC;IAEzC;;;OAGG;IACU,qBAAe,GAAW,CAAC,CAAC;IAEzC;;OAEG;IACU,oBAAc,GAAW,CAAC,CAAC;IAExC;;;;;;;;;OASG;IACU,4BAAsB,GAAW,CAAC,CAAC;AACjD,CAAC,EAlCgB,KAAK,GAAL,aAAK,KAAL,aAAK,QAkCrB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.7862802-07:00\r\n\r\nimport { CharStream } from \"./CharStream\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { TokenSource } from \"./TokenSource\";\r\nimport { TokenStream } from \"./TokenStream\";\r\n\r\n/** A token has properties: text, type, line, character position in the line\r\n *  (so we can ignore tabs), token channel, index, and source from which\r\n *  we obtained this token.\r\n */\r\nexport interface Token {\r\n\t/**\r\n\t * Get the text of the token.\r\n\t */\r\n\treadonly text: string | undefined;\r\n\r\n\t/** Get the token type of the token */\r\n\treadonly type: number;\r\n\r\n\t/** The line number on which the 1st character of this token was matched,\r\n\t *  line=1..n\r\n\t */\r\n\treadonly line: number;\r\n\r\n\t/** The index of the first character of this token relative to the\r\n\t *  beginning of the line at which it occurs, 0..n-1\r\n\t */\r\n\treadonly charPositionInLine: number;\r\n\r\n\t/** Return the channel this token. Each token can arrive at the parser\r\n\t *  on a different channel, but the parser only \"tunes\" to a single channel.\r\n\t *  The parser ignores everything not on DEFAULT_CHANNEL.\r\n\t */\r\n\treadonly channel: number;\r\n\r\n\t/** An index from 0..n-1 of the token object in the input stream.\r\n\t *  This must be valid in order to print token streams and\r\n\t *  use TokenRewriteStream.\r\n\t *\r\n\t *  Return -1 to indicate that this token was conjured up since\r\n\t *  it doesn't have a valid index.\r\n\t */\r\n\treadonly tokenIndex: number;\r\n\r\n\t/** The starting character index of the token\r\n\t *  This method is optional; return -1 if not implemented.\r\n\t */\r\n\treadonly startIndex: number;\r\n\r\n\t/** The last character index of the token.\r\n\t *  This method is optional; return -1 if not implemented.\r\n\t */\r\n\treadonly stopIndex: number;\r\n\r\n\t/** Gets the {@link TokenSource} which created this token.\r\n\t */\r\n\treadonly tokenSource: TokenSource | undefined;\r\n\r\n\t/**\r\n\t * Gets the {@link CharStream} from which this token was derived.\r\n\t */\r\n\treadonly inputStream: CharStream | undefined;\r\n}\r\n\r\nexport namespace Token {\r\n\texport const INVALID_TYPE: number = 0;\r\n\r\n\t/** During lookahead operations, this \"token\" signifies we hit rule end ATN state\r\n\t *  and did not follow it despite needing to.\r\n\t */\r\n\texport const EPSILON: number = -2;\r\n\r\n\texport const MIN_USER_TOKEN_TYPE: number = 1;\r\n\r\n\texport const EOF: number = IntStream.EOF;\r\n\r\n\t/** All tokens go to the parser (unless skip() is called in that rule)\r\n\t *  on a particular \"channel\".  The parser tunes to a particular channel\r\n\t *  so that whitespace etc... can go to the parser on a \"hidden\" channel.\r\n\t */\r\n\texport const DEFAULT_CHANNEL: number = 0;\r\n\r\n\t/** Anything on different channel than DEFAULT_CHANNEL is not parsed\r\n\t *  by parser.\r\n\t */\r\n\texport const HIDDEN_CHANNEL: number = 1;\r\n\r\n\t/**\r\n\t * This is the minimum constant value which can be assigned to a\r\n\t * user-defined token channel.\r\n\t *\r\n\t * The non-negative numbers less than {@link #MIN_USER_CHANNEL_VALUE} are\r\n\t * assigned to the predefined channels {@link #DEFAULT_CHANNEL} and\r\n\t * {@link #HIDDEN_CHANNEL}.\r\n\t *\r\n\t * @see `Token.channel`\r\n\t */\r\n\texport const MIN_USER_CHANNEL_VALUE: number = 2;\r\n}\r\n"]}