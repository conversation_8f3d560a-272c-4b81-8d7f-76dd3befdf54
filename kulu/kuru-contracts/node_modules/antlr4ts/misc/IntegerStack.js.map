{"version": 3, "file": "IntegerStack.js", "sourceRoot": "", "sources": ["../../../src/misc/IntegerStack.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,+CAA4C;AAE5C;;;GAGG;AACH,MAAa,YAAa,SAAQ,yBAAW;IAE5C,YAAY,GAA2B;QACtC,KAAK,CAAC,GAAG,CAAC,CAAC;IACZ,CAAC;IAEM,IAAI,CAAC,KAAa;QACxB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAEM,GAAG;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAEM,IAAI;QACV,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC;CAED;AAlBD,oCAkBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:40.6647101-07:00\r\n\r\nimport { IntegerList } from \"./IntegerList\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class IntegerStack extends IntegerList {\r\n\r\n\tconstructor(arg?: number | IntegerStack) {\r\n\t\tsuper(arg);\r\n\t}\r\n\r\n\tpublic push(value: number): void {\r\n\t\tthis.add(value);\r\n\t}\r\n\r\n\tpublic pop(): number {\r\n\t\treturn this.removeAt(this.size - 1);\r\n\t}\r\n\r\n\tpublic peek(): number {\r\n\t\treturn this.get(this.size - 1);\r\n\t}\r\n\r\n}\r\n"]}