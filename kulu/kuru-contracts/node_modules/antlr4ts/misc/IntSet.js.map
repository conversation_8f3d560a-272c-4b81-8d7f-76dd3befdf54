{"version": 3, "file": "IntSet.js", "sourceRoot": "", "sources": ["../../../src/misc/IntSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:41.7132712-07:00\r\n\r\n/**\r\n * A generic set of integers.\r\n *\r\n * @see IntervalSet\r\n */\r\nexport interface IntSet {\r\n\t/**\r\n\t * Adds the specified value to the current set.\r\n\t *\r\n\t * @param el the value to add\r\n\t *\r\n\t * @exception IllegalStateException if the current set is read-only\r\n\t */\r\n\tadd(el: number): void;\r\n\r\n\t/**\r\n\t * Modify the current {@link IntSet} object to contain all elements that are\r\n\t * present in itself, the specified `set`, or both.\r\n\t *\r\n\t * @param set The set to add to the current set. An `undefined` argument is\r\n\t * treated as though it were an empty set.\r\n\t * @returns `this` (to support chained calls)\r\n\t *\r\n\t * @exception IllegalStateException if the current set is read-only\r\n\t */\r\n\t//@NotNull\r\n\taddAll(/*@Nullable*/ set: IntSet): IntSet;\r\n\r\n\t/**\r\n\t * Return a new {@link IntSet} object containing all elements that are\r\n\t * present in both the current set and the specified set `a`.\r\n\t *\r\n\t * @param a The set to intersect with the current set.\r\n\t * @returns A new {@link IntSet} instance containing the intersection of the\r\n\t * current set and `a`.\r\n\t */\r\n\tand(a: IntSet): IntSet;\r\n\r\n\t/**\r\n\t * Return a new {@link IntSet} object containing all elements that are\r\n\t * present in `elements` but not present in the current set. The\r\n\t * following expressions are equivalent for input non-`undefined` {@link IntSet}\r\n\t * instances `x` and `y`.\r\n\t *\r\n\t * * `x.complement(y)`\r\n\t * * `y.subtract(x)`\r\n\t *\r\n\t * @param elements The set to compare with the current set.\r\n\t * @returns A new {@link IntSet} instance containing the elements present in\r\n\t * `elements` but not present in the current set.\r\n\t */\r\n\tcomplement(elements: IntSet): IntSet;\r\n\r\n\t/**\r\n\t * Return a new {@link IntSet} object containing all elements that are\r\n\t * present in the current set, the specified set `a`, or both.\r\n\t *\r\n\t * This method is similar to {@link #addAll(IntSet)}, but returns a new\r\n\t * {@link IntSet} instance instead of modifying the current set.\r\n\t *\r\n\t * @param a The set to union with the current set. An `undefined` argument\r\n\t * is treated as though it were an empty set.\r\n\t * @returns A new {@link IntSet} instance containing the union of the current\r\n\t * set and `a`. The value `undefined` may be returned in place of an\r\n\t * empty result set.\r\n\t */\r\n\t//@Nullable\r\n\tor(/*@Nullable*/ a: IntSet): IntSet;\r\n\r\n\t/**\r\n\t * Return a new {@link IntSet} object containing all elements that are\r\n\t * present in the current set but not present in the input set `a`.\r\n\t * The following expressions are equivalent for input non-`undefined`\r\n\t * {@link IntSet} instances `x` and `y`.\r\n\t *\r\n\t * * `y.subtract(x)`\r\n\t * * `x.complement(y)`\r\n\t *\r\n\t * @param a The set to compare with the current set. A `undefined`\r\n\t * argument is treated as though it were an empty set.\r\n\t * @returns A new {@link IntSet} instance containing the elements present in\r\n\t * `elements` but not present in the current set. The value\r\n\t * `undefined` may be returned in place of an empty result set.\r\n\t */\r\n\t//@Nullable\r\n\tsubtract(/*@Nullable*/ a: IntSet): IntSet;\r\n\r\n\t/**\r\n\t * Return the total number of elements represented by the current set.\r\n\t *\r\n\t * @returns the total number of elements represented by the current set,\r\n\t * regardless of the manner in which the elements are stored.\r\n\t */\r\n\treadonly size: number;\r\n\r\n\t/**\r\n\t * Returns `true` if this set contains no elements.\r\n\t *\r\n\t * @returns `true` if the current set contains no elements; otherwise,\r\n\t * `false`.\r\n\t */\r\n\treadonly isNil: boolean;\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t//@Override\r\n\tequals(obj: any): boolean;\r\n\r\n\t/**\r\n\t * Returns `true` if the set contains the specified element.\r\n\t *\r\n\t * @param el The element to check for.\r\n\t * @returns `true` if the set contains `el`; otherwise `false`.\r\n\t */\r\n\tcontains(el: number): boolean;\r\n\r\n\t/**\r\n\t * Removes the specified value from the current set. If the current set does\r\n\t * not contain the element, no changes are made.\r\n\t *\r\n\t * @param el the value to remove\r\n\t *\r\n\t * @exception IllegalStateException if the current set is read-only\r\n\t */\r\n\tremove(el: number): void;\r\n\r\n\t/**\r\n\t * Return an array containing the elements represented by the current set. The\r\n\t * array is returned in ascending numerical order.\r\n\t *\r\n\t * @returns An array containing all element present in the current set, sorted\r\n\t * in ascending numerical order.\r\n\t */\r\n\t//@NotNull\r\n\ttoArray(): number[];\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t//@Override\r\n\ttoString(): string;\r\n}\r\n"]}