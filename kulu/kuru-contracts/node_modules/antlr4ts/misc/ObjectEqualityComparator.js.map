{"version": 3, "file": "ObjectEqualityComparator.js", "sourceRoot": "", "sources": ["../../../src/misc/ObjectEqualityComparator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAIH,8CAAyC;AAGzC;;;;;GAKG;AACH,MAAa,wBAAwB;IAGpC;;;;;OAKG;IAEI,QAAQ,CAAC,GAAiC;QAChD,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,CAAC;SACT;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IAEI,MAAM,CAAC,CAA+B,EAAE,CAA+B;QAC7E,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,OAAO,CAAC,IAAI,IAAI,CAAC;SACjB;QAED,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;AAjCsB,iCAAQ,GAA6B,IAAI,wBAAwB,EAAE,CAAC;AAS3F;IADC,qBAAQ;wDAOR;AAYD;IADC,qBAAQ;sDAOR;AAlCF,4DAoCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-03T02:09:42.2127260-07:00\r\nimport { EqualityComparator } from \"./EqualityComparator\";\r\nimport { Override } from \"../Decorators\";\r\nimport { Equatable } from \"./Stubs\";\r\n\r\n/**\r\n * This default implementation of {@link EqualityComparator} uses object equality\r\n * for comparisons by calling {@link Object#hashCode} and {@link Object#equals}.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ObjectEqualityComparator implements EqualityComparator<Equatable | null | undefined> {\r\n\tpublic static readonly INSTANCE: ObjectEqualityComparator = new ObjectEqualityComparator();\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation returns\r\n\t * `obj.`{@link Object#hashCode hashCode()}.\r\n\t */\r\n\t@Override\r\n\tpublic hashCode(obj: Equatable | null | undefined): number {\r\n\t\tif (obj == null) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\treturn obj.hashCode();\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation relies on object equality. If both objects are\r\n\t * `undefined` or `null`, this method returns `true`. Otherwise if only\r\n\t * `a` is `undefined` or `null`, this method returns `false`. Otherwise,\r\n\t * this method returns the result of\r\n\t * `a.`{@link Object#equals equals}`(b)`.\r\n\t */\r\n\t@Override\r\n\tpublic equals(a: Equatable | null | undefined, b: Equatable | null | undefined): boolean {\r\n\t\tif (a == null) {\r\n\t\t\treturn b == null;\r\n\t\t}\r\n\r\n\t\treturn a.equals(b);\r\n\t}\r\n\r\n}\r\n"]}