{"version": 3, "file": "Interval.js", "sourceRoot": "", "sources": ["../../../src/misc/Interval.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,8CAAyC;AAGzC,MAAM,uBAAuB,GAAW,IAAI,CAAC;AAE7C,2CAA2C;AAC3C,MAAa,QAAQ;IAQpB;;;OAGG;IACH,YAAmB,CAAS,EAAS,CAAS;QAA3B,MAAC,GAAD,CAAC,CAAQ;QAAS,MAAC,GAAD,CAAC,CAAQ;IAC9C,CAAC;IAXD,MAAM,KAAK,OAAO;QACjB,OAAO,QAAQ,CAAC,QAAQ,CAAC;IAC1B,CAAC;IAWD;;;;;OAKG;IACI,MAAM,CAAC,EAAE,CAAC,CAAS,EAAE,CAAS;QACpC,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,uBAAuB,EAAE;YACpD,OAAO,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;QAED,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAC9B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACvC;QAED,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACT,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;YACpB,OAAO,CAAC,CAAC;SACT;QAED,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,KAAK,IAAI,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;aACI,IAAI,CAAC,CAAC,CAAC,YAAY,QAAQ,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,EAAE,CAAC;QACtB,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAC1B,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;IACb,CAAC;IAED,wDAAwD;IACjD,oBAAoB,CAAC,KAAe;QAC1C,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,sDAAsD;IAC/C,uBAAuB,CAAC,KAAe;QAC7C,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,kEAAkE;IAC3D,WAAW,CAAC,KAAe;QACjC,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,uDAAuD;IAChD,mBAAmB,CAAC,KAAe;QACzC,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,+CAA+C;IACxC,sBAAsB,CAAC,KAAe;QAC5C,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,0BAA0B;IACzE,CAAC;IAED,kDAAkD;IAC3C,QAAQ,CAAC,KAAe;QAC9B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC5E,CAAC;IAED,2DAA2D;IACpD,QAAQ,CAAC,KAAe;QAC9B,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IACzD,CAAC;IAEM,gBAAgB,CAAC,KAAe;QACtC,OAAO,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,iEAAiE;IAC1D,KAAK,CAAC,KAAe;QAC3B,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,uDAAuD;IAChD,YAAY,CAAC,KAAe;QAClC,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACI,8BAA8B,CAAC,KAAe;QACpD,IAAI,IAA0B,CAAC;QAC/B,IAAI,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;YACxC,sCAAsC;YACtC,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;SAC1D;aAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAC9C,6BAA6B;YAC7B,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACxC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,QAAQ;QACd,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC;;AAtIc,iBAAQ,GAAa,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAKjC,cAAK,GAAe,IAAI,KAAK,CAAW,uBAAuB,GAAG,CAAC,CAAC,CAAC;AAwC7F;IADC,qBAAQ;sCAUR;AAGD;IADC,qBAAQ;wCAMR;AAsED;IADC,qBAAQ;wCAGR;AAvIF,4BAwIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:40.7402214-07:00\r\n\r\nimport { Override } from \"../Decorators\";\r\nimport { Equatable } from \"./Stubs\";\r\n\r\nconst INTERVAL_POOL_MAX_VALUE: number = 1000;\r\n\r\n/** An immutable inclusive interval a..b */\r\nexport class Interval implements Equatable {\r\n\tprivate static _INVALID: Interval = new Interval(-1, -2);\r\n\tstatic get INVALID(): Interval {\r\n\t\treturn Interval._INVALID;\r\n\t}\r\n\r\n\tprivate static readonly cache: Interval[] = new Array<Interval>(INTERVAL_POOL_MAX_VALUE + 1);\r\n\r\n\t/**\r\n\t * @param a The start of the interval\r\n\t * @param b The end of the interval (inclusive)\r\n\t */\r\n\tconstructor(public a: number, public b: number) {\r\n\t}\r\n\r\n\t/** Interval objects are used readonly so share all with the\r\n\t *  same single value a==b up to some max size.  Use an array as a perfect hash.\r\n\t *  Return shared object for 0..INTERVAL_POOL_MAX_VALUE or a new\r\n\t *  Interval object with a..a in it.  On Java.g4, 218623 IntervalSets\r\n\t *  have a..a (set with 1 element).\r\n\t */\r\n\tpublic static of(a: number, b: number): Interval {\r\n\t\t// cache just a..a\r\n\t\tif (a !== b || a < 0 || a > INTERVAL_POOL_MAX_VALUE) {\r\n\t\t\treturn new Interval(a, b);\r\n\t\t}\r\n\r\n\t\tif (Interval.cache[a] == null) {\r\n\t\t\tInterval.cache[a] = new Interval(a, a);\r\n\t\t}\r\n\r\n\t\treturn Interval.cache[a];\r\n\t}\r\n\r\n\t/** return number of elements between a and b inclusively. x..x is length 1.\r\n\t *  if b &lt; a, then length is 0.  9..10 has length 2.\r\n\t */\r\n\tget length(): number {\r\n\t\tif (this.b < this.a) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\treturn this.b - this.a + 1;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (o === this) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\telse if (!(o instanceof Interval)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.a === o.a && this.b === o.b;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = 23;\r\n\t\thash = hash * 31 + this.a;\r\n\t\thash = hash * 31 + this.b;\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t/** Does this start completely before other? Disjoint */\r\n\tpublic startsBeforeDisjoint(other: Interval): boolean {\r\n\t\treturn this.a < other.a && this.b < other.a;\r\n\t}\r\n\r\n\t/** Does this start at or before other? Nondisjoint */\r\n\tpublic startsBeforeNonDisjoint(other: Interval): boolean {\r\n\t\treturn this.a <= other.a && this.b >= other.a;\r\n\t}\r\n\r\n\t/** Does this.a start after other.b? May or may not be disjoint */\r\n\tpublic startsAfter(other: Interval): boolean {\r\n\t\treturn this.a > other.a;\r\n\t}\r\n\r\n\t/** Does this start completely after other? Disjoint */\r\n\tpublic startsAfterDisjoint(other: Interval): boolean {\r\n\t\treturn this.a > other.b;\r\n\t}\r\n\r\n\t/** Does this start after other? NonDisjoint */\r\n\tpublic startsAfterNonDisjoint(other: Interval): boolean {\r\n\t\treturn this.a > other.a && this.a <= other.b; // this.b>=other.b implied\r\n\t}\r\n\r\n\t/** Are both ranges disjoint? I.e., no overlap? */\r\n\tpublic disjoint(other: Interval): boolean {\r\n\t\treturn this.startsBeforeDisjoint(other) || this.startsAfterDisjoint(other);\r\n\t}\r\n\r\n\t/** Are two intervals adjacent such as 0..41 and 42..42? */\r\n\tpublic adjacent(other: Interval): boolean {\r\n\t\treturn this.a === other.b + 1 || this.b === other.a - 1;\r\n\t}\r\n\r\n\tpublic properlyContains(other: Interval): boolean {\r\n\t\treturn other.a >= this.a && other.b <= this.b;\r\n\t}\r\n\r\n\t/** Return the interval computed from combining this and other */\r\n\tpublic union(other: Interval): Interval {\r\n\t\treturn Interval.of(Math.min(this.a, other.a), Math.max(this.b, other.b));\r\n\t}\r\n\r\n\t/** Return the interval in common between this and o */\r\n\tpublic intersection(other: Interval): Interval {\r\n\t\treturn Interval.of(Math.max(this.a, other.a), Math.min(this.b, other.b));\r\n\t}\r\n\r\n\t/** Return the interval with elements from `this` not in `other`;\r\n\t *  `other` must not be totally enclosed (properly contained)\r\n\t *  within `this`, which would result in two disjoint intervals\r\n\t *  instead of the single one returned by this method.\r\n\t */\r\n\tpublic differenceNotProperlyContained(other: Interval): Interval | undefined {\r\n\t\tlet diff: Interval | undefined;\r\n\t\tif (other.startsBeforeNonDisjoint(this)) {\r\n\t\t\t// other.a to left of this.a (or same)\r\n\t\t\tdiff = Interval.of(Math.max(this.a, other.b + 1), this.b);\r\n\t\t} else if (other.startsAfterNonDisjoint(this)) {\r\n\t\t\t// other.a to right of this.a\r\n\t\t\tdiff = Interval.of(this.a, other.a - 1);\r\n\t\t}\r\n\r\n\t\treturn diff;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this.a + \"..\" + this.b;\r\n\t}\r\n}\r\n"]}