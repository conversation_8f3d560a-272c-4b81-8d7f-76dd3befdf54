{"version": 3, "file": "Arrays.js", "sourceRoot": "", "sources": ["../../../src/misc/Arrays.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,IAAiB,MAAM,CA4DtB;AA5DD,WAAiB,MAAM;IACtB;;;;;;;;;OASG;IACH,SAAgB,YAAY,CAAC,KAAwB,EAAE,GAAW,EAAE,SAAkB,EAAE,OAAgB;QACvG,OAAO,aAAa,CAAC,KAAK,EAAE,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3H,CAAC;IAFe,mBAAY,eAE3B,CAAA;IAED,SAAS,aAAa,CAAC,KAAwB,EAAE,SAAiB,EAAE,OAAe,EAAE,GAAW;QAC/F,IAAI,GAAG,GAAW,SAAS,CAAC;QAC5B,IAAI,IAAI,GAAW,OAAO,GAAG,CAAC,CAAC;QAE/B,OAAO,GAAG,IAAI,IAAI,EAAE;YACnB,IAAI,GAAG,GAAW,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,MAAM,GAAW,KAAK,CAAC,GAAG,CAAC,CAAC;YAEhC,IAAI,MAAM,GAAG,GAAG,EAAE;gBACjB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;aACd;iBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;gBACxB,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;aACf;iBAAM;gBACN,YAAY;gBACZ,OAAO,GAAG,CAAC;aACX;SACD;QAED,iBAAiB;QACjB,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,SAAgB,QAAQ,CAAI,KAAkB;QAC7C,IAAI,MAAM,GAAG,GAAG,CAAC;QAEjB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,KAAK,IAAI,OAAO,IAAI,KAAK,EAAE;YAC1B,IAAI,KAAK,EAAE;gBACV,KAAK,GAAG,KAAK,CAAC;aACd;iBAAM;gBACN,MAAM,IAAI,IAAI,CAAC;aACf;YAED,IAAI,OAAO,KAAK,IAAI,EAAE;gBACrB,MAAM,IAAI,MAAM,CAAC;aACjB;iBAAM,IAAI,OAAO,KAAK,SAAS,EAAE;gBACjC,MAAM,IAAI,WAAW,CAAC;aACtB;iBAAM;gBACN,MAAM,IAAI,OAAO,CAAC;aAClB;SACD;QAED,MAAM,IAAI,GAAG,CAAC;QACd,OAAO,MAAM,CAAC;IACf,CAAC;IAtBe,eAAQ,WAsBvB,CAAA;AACF,CAAC,EA5DgB,MAAM,GAAN,cAAM,KAAN,cAAM,QA4DtB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport namespace Arrays {\r\n\t/**\r\n\t * Searches the specified array of numbers for the specified value using the binary search algorithm. The array must\r\n\t * be sorted prior to making this call. If it is not sorted, the results are unspecified. If the array contains\r\n\t * multiple elements with the specified value, there is no guarantee which one will be found.\r\n\t *\r\n\t * @returns index of the search key, if it is contained in the array; otherwise, (-(insertion point) - 1). The\r\n\t * insertion point is defined as the point at which the key would be inserted into the array: the index of the first\r\n\t * element greater than the key, or array.length if all elements in the array are less than the specified key. Note\r\n\t * that this guarantees that the return value will be >= 0 if and only if the key is found.\r\n\t */\r\n\texport function binarySearch(array: ArrayLike<number>, key: number, fromIndex?: number, toIndex?: number): number {\r\n\t\treturn binarySearch0(array, fromIndex !== undefined ? fromIndex : 0, toIndex !== undefined ? toIndex : array.length, key);\r\n\t}\r\n\r\n\tfunction binarySearch0(array: ArrayLike<number>, fromIndex: number, toIndex: number, key: number): number {\r\n\t\tlet low: number = fromIndex;\r\n\t\tlet high: number = toIndex - 1;\r\n\r\n\t\twhile (low <= high) {\r\n\t\t\tlet mid: number = (low + high) >>> 1;\r\n\t\t\tlet midVal: number = array[mid];\r\n\r\n\t\t\tif (midVal < key) {\r\n\t\t\t\tlow = mid + 1;\r\n\t\t\t} else if (midVal > key) {\r\n\t\t\t\thigh = mid - 1;\r\n\t\t\t} else {\r\n\t\t\t\t// key found\r\n\t\t\t\treturn mid;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// key not found.\r\n\t\treturn -(low + 1);\r\n\t}\r\n\r\n\texport function toString<T>(array: Iterable<T>) {\r\n\t\tlet result = \"[\";\r\n\r\n\t\tlet first = true;\r\n\t\tfor (let element of array) {\r\n\t\t\tif (first) {\r\n\t\t\t\tfirst = false;\r\n\t\t\t} else {\r\n\t\t\t\tresult += \", \";\r\n\t\t\t}\r\n\r\n\t\t\tif (element === null) {\r\n\t\t\t\tresult += \"null\";\r\n\t\t\t} else if (element === undefined) {\r\n\t\t\t\tresult += \"undefined\";\r\n\t\t\t} else {\r\n\t\t\t\tresult += element;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tresult += \"]\";\r\n\t\treturn result;\r\n\t}\r\n}\r\n"]}