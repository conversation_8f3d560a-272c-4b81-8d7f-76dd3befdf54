{"version": 3, "file": "EqualityComparator.js", "sourceRoot": "", "sources": ["../../../src/misc/EqualityComparator.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-03T02:09:42.0574481-07:00\r\n\r\n/**\r\n * This interface provides an abstract concept of object equality independent of\r\n * {@link Object#equals} (object equality) and the `==` operator\r\n * (reference equality). It can be used to provide algorithm-specific unordered\r\n * comparisons without requiring changes to the object itself.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport interface EqualityComparator<T> {\r\n\r\n\t/**\r\n\t * This method returns a hash code for the specified object.\r\n\t *\r\n\t * @param obj The object.\r\n\t * @returns The hash code for `obj`.\r\n\t */\r\n\thashCode(obj: T): number;\r\n\r\n\t/**\r\n\t * This method tests if two objects are equal.\r\n\t *\r\n\t * @param a The first object to compare.\r\n\t * @param b The second object to compare.\r\n\t * @returns `true` if `a` equals `b`, otherwise `false`.\r\n\t */\r\n\tequals(a: T, b: T): boolean;\r\n\r\n}\r\n"]}