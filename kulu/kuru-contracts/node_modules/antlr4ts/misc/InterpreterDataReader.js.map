{"version": 3, "file": "InterpreterDataReader.js", "sourceRoot": "", "sources": ["../../../src/misc/InterpreterDataReader.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,yBAAyB;AACzB,6BAA6B;AAI7B,sDAAmD;AACnD,4DAAyD;AAEzD,SAAS,YAAY,CAAC,MAAc;IACnC,IAAI,KAAK,GAAa,EAAE,CAAC;IAEzB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE;QAC7B,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAI,OAAe,CAAC;QACpB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE;YAClE,OAAO,GAAG,SAAS,CAAC;SACpB;aAAM,IAAI,SAAS,IAAI,CAAC,EAAE;YAC1B,OAAO,GAAG,SAAS,CAAC;SACpB;aAAM;YACN,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;SACxB;QAED,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QACzD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,GAAG,CAAC,KAAK,SAAS,EAAE;YACvD,KAAK,GAAG,OAAO,GAAG,CAAC,CAAC;SACpB;aAAM;YACN,KAAK,GAAG,OAAO,GAAG,CAAC,CAAC;SACpB;KACD;IAED,OAAO,KAAK,CAAC;AACd,CAAC;AAED,iEAAiE;AACjE,IAAiB,qBAAqB,CAiJrC;AAjJD,WAAiB,qBAAqB;IACrC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,SAAsB,SAAS,CAAC,QAAgB;;YAC/C,IAAI,MAAM,GAA0C,IAAI,qBAAqB,CAAC,eAAe,EAAE,CAAC;YAChG,IAAI,KAAK,GAAW,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;YAChE,IAAI,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI;gBACH,IAAI,IAAY,CAAC;gBACjB,IAAI,SAAS,GAAW,CAAC,CAAC;gBAC1B,IAAI,YAAY,GAAa,EAAE,CAAC;gBAChC,IAAI,aAAa,GAAa,EAAE,CAAC;gBAEjC,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,IAAI,KAAK,sBAAsB,EAAE;oBACpC,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;iBAC9C;gBAED,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;oBAC9E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAM;qBACN;oBAED,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;iBAC/C;gBAED,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,IAAI,KAAK,uBAAuB,EAAE;oBACrC,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;iBAC9C;gBAED,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;oBAC9E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAM;qBACN;oBAED,aAAa,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;iBAChD;gBAED,IAAI,YAAY,GAAa,EAAE,CAAC;gBAChC,MAAM,CAAC,UAAU,GAAG,IAAI,+BAAc,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;gBAElF,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,IAAI,KAAK,aAAa,EAAE;oBAC3B,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;iBAC9C;gBAED,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;oBAC9E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAM;qBACN;oBAED,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC5B;gBAED,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,IAAI,KAAK,gBAAgB,EAAE,EAAE,yBAAyB;oBACzD,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACrB,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;wBAC9E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BACtB,MAAM;yBACN;wBAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAC3B;oBAED,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;oBAC1B,IAAI,IAAI,KAAK,aAAa,EAAE;wBAC3B,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;qBAC9C;oBAED,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;oBAClB,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;wBAC9E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BACtB,MAAM;yBACN;wBAED,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACxB;iBACD;gBAED,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,IAAI,KAAK,MAAM,EAAE;oBACpB,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;iBAC9C;gBAED,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC1B,IAAI,QAAQ,GAAa,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,aAAa,GAAgB,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAElE,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACjD,IAAI,KAAa,CAAC;oBAClB,IAAI,OAAO,GAAW,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;wBAC5B,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;qBAClD;yBACI,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;wBAC/B,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;qBACtE;yBACI;wBACJ,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;qBACrC;oBAED,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBACzB;gBAED,IAAI,YAAY,GAAoB,IAAI,iCAAe,EAAE,CAAC;gBAC1D,MAAM,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;aACrD;YACD,OAAO,CAAC,EAAE;gBACT,8DAA8D;aAC9D;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;IAhHqB,+BAAS,YAgH9B,CAAA;IAED,MAAa,eAAe;QAA5B;YAEQ,eAAU,GAAe,+BAAc,CAAC,gBAAgB,CAAC;YACzD,cAAS,GAAa,EAAE,CAAC;QAGjC,CAAC;KAAA;IANY,qCAAe,kBAM3B,CAAA;AACF,CAAC,EAjJgB,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAiJrC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport * as fs from \"fs\";\r\nimport * as util from \"util\";\r\n\r\nimport { ATN } from \"../atn/ATN\";\r\nimport { Vocabulary } from \"../Vocabulary\";\r\nimport { VocabularyImpl } from \"../VocabularyImpl\";\r\nimport { ATNDeserializer } from \"../atn/ATNDeserializer\";\r\n\r\nfunction splitToLines(buffer: Buffer): string[] {\r\n\tlet lines: string[] = [];\r\n\r\n\tlet index = 0;\r\n\twhile (index < buffer.length) {\r\n\t\tlet lineStart = index;\r\n\t\tlet lineEndLF = buffer.indexOf(\"\\n\".charCodeAt(0), index);\r\n\t\tlet lineEndCR = buffer.indexOf(\"\\r\".charCodeAt(0), index);\r\n\t\tlet lineEnd: number;\r\n\t\tif (lineEndCR >= 0 && (lineEndCR < lineEndLF || lineEndLF === -1)) {\r\n\t\t\tlineEnd = lineEndCR;\r\n\t\t} else if (lineEndLF >= 0) {\r\n\t\t\tlineEnd = lineEndLF;\r\n\t\t} else {\r\n\t\t\tlineEnd = buffer.length;\r\n\t\t}\r\n\r\n\t\tlines.push(buffer.toString(\"utf-8\", lineStart, lineEnd));\r\n\t\tif (lineEnd === lineEndCR && lineEnd + 1 === lineEndLF) {\r\n\t\t\tindex = lineEnd + 2;\r\n\t\t} else {\r\n\t\t\tindex = lineEnd + 1;\r\n\t\t}\r\n\t}\r\n\r\n\treturn lines;\r\n}\r\n\r\n// A class to read plain text interpreter data produced by ANTLR.\r\nexport namespace InterpreterDataReader {\r\n\t/**\r\n\t * The structure of the data file is very simple. Everything is line based with empty lines\r\n\t * separating the different parts. For lexers the layout is:\r\n\t * token literal names:\r\n\t * ...\r\n\t *\r\n\t * token symbolic names:\r\n\t * ...\r\n\t *\r\n\t * rule names:\r\n\t * ...\r\n\t *\r\n\t * channel names:\r\n\t * ...\r\n\t *\r\n\t * mode names:\r\n\t * ...\r\n\t *\r\n\t * atn:\r\n\t * <a single line with comma separated int values> enclosed in a pair of squared brackets.\r\n\t *\r\n\t * Data for a parser does not contain channel and mode names.\r\n\t */\r\n\texport async function parseFile(fileName: string): Promise<InterpreterDataReader.InterpreterData> {\r\n\t\tlet result: InterpreterDataReader.InterpreterData = new InterpreterDataReader.InterpreterData();\r\n\t\tlet input: Buffer = await util.promisify(fs.readFile)(fileName);\r\n\t\tlet lines = splitToLines(input);\r\n\r\n\t\ttry {\r\n\t\t\tlet line: string;\r\n\t\t\tlet lineIndex: number = 0;\r\n\t\t\tlet literalNames: string[] = [];\r\n\t\t\tlet symbolicNames: string[] = [];\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tif (line !== \"token literal names:\") {\r\n\t\t\t\tthrow new RangeError(\"Unexpected data entry\");\r\n\t\t\t}\r\n\r\n\t\t\tfor (line = lines[lineIndex++]; line !== undefined; line = lines[lineIndex++]) {\r\n\t\t\t\tif (line.length === 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tliteralNames.push(line === \"null\" ? \"\" : line);\r\n\t\t\t}\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tif (line !== \"token symbolic names:\") {\r\n\t\t\t\tthrow new RangeError(\"Unexpected data entry\");\r\n\t\t\t}\r\n\r\n\t\t\tfor (line = lines[lineIndex++]; line !== undefined; line = lines[lineIndex++]) {\r\n\t\t\t\tif (line.length === 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tsymbolicNames.push(line === \"null\" ? \"\" : line);\r\n\t\t\t}\r\n\r\n\t\t\tlet displayNames: string[] = [];\r\n\t\t\tresult.vocabulary = new VocabularyImpl(literalNames, symbolicNames, displayNames);\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tif (line !== \"rule names:\") {\r\n\t\t\t\tthrow new RangeError(\"Unexpected data entry\");\r\n\t\t\t}\r\n\r\n\t\t\tfor (line = lines[lineIndex++]; line !== undefined; line = lines[lineIndex++]) {\r\n\t\t\t\tif (line.length === 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tresult.ruleNames.push(line);\r\n\t\t\t}\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tif (line === \"channel names:\") { // Additional lexer data.\r\n\t\t\t\tresult.channels = [];\r\n\t\t\t\tfor (line = lines[lineIndex++]; line !== undefined; line = lines[lineIndex++]) {\r\n\t\t\t\t\tif (line.length === 0) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tresult.channels.push(line);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tline = lines[lineIndex++];\r\n\t\t\t\tif (line !== \"mode names:\") {\r\n\t\t\t\t\tthrow new RangeError(\"Unexpected data entry\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tresult.modes = [];\r\n\t\t\t\tfor (line = lines[lineIndex++]; line !== undefined; line = lines[lineIndex++]) {\r\n\t\t\t\t\tif (line.length === 0) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tresult.modes.push(line);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tif (line !== \"atn:\") {\r\n\t\t\t\tthrow new RangeError(\"Unexpected data entry\");\r\n\t\t\t}\r\n\r\n\t\t\tline = lines[lineIndex++];\r\n\t\t\tlet elements: string[] = line.split(\",\");\r\n\t\t\tlet serializedATN: Uint16Array = new Uint16Array(elements.length);\r\n\r\n\t\t\tfor (let i: number = 0; i < elements.length; ++i) {\r\n\t\t\t\tlet value: number;\r\n\t\t\t\tlet element: string = elements[i];\r\n\t\t\t\tif (element.startsWith(\"[\")) {\r\n\t\t\t\t\tvalue = parseInt(element.substring(1).trim(), 10);\r\n\t\t\t\t}\r\n\t\t\t\telse if (element.endsWith(\"]\")) {\r\n\t\t\t\t\tvalue = parseInt(element.substring(0, element.length - 1).trim(), 10);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tvalue = parseInt(element.trim(), 10);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tserializedATN[i] = value;\r\n\t\t\t}\r\n\r\n\t\t\tlet deserializer: ATNDeserializer = new ATNDeserializer();\r\n\t\t\tresult.atn = deserializer.deserialize(serializedATN);\r\n\t\t}\r\n\t\tcatch (e) {\r\n\t\t\t// We just swallow the error and return empty objects instead.\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\texport class InterpreterData {\r\n\t\tpublic atn?: ATN;\r\n\t\tpublic vocabulary: Vocabulary = VocabularyImpl.EMPTY_VOCABULARY;\r\n\t\tpublic ruleNames: string[] = [];\r\n\t\tpublic channels?: string[]; // Only valid for lexer grammars.\r\n\t\tpublic modes?: string[]; // ditto\r\n\t}\r\n}\r\n"]}