{"version": 3, "file": "MurmurHash.js", "sourceRoot": "", "sources": ["../../../src/misc/MurmurHash.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH;;;GAGG;AACH,IAAiB,UAAU,CA4G1B;AA5GD,WAAiB,UAAU;IAE1B,MAAM,YAAY,GAAW,CAAC,CAAC;IAE/B;;;;;OAKG;IACH,SAAgB,UAAU,CAAC,OAAe,YAAY;QACrD,OAAO,IAAI,CAAC;IACb,CAAC;IAFe,qBAAU,aAEzB,CAAA;IAED;;;;;;OAMG;IACH,SAAgB,MAAM,CAAC,IAAY,EAAE,KAAqD;QACzF,MAAM,EAAE,GAAW,UAAU,CAAC;QAC9B,MAAM,EAAE,GAAW,UAAU,CAAC;QAC9B,MAAM,EAAE,GAAW,EAAE,CAAC;QACtB,MAAM,EAAE,GAAW,EAAE,CAAC;QACtB,MAAM,CAAC,GAAW,CAAC,CAAC;QACpB,MAAM,CAAC,GAAW,UAAU,CAAC;QAE7B,IAAI,KAAK,IAAI,IAAI,EAAE;YAClB,KAAK,GAAG,CAAC,CAAC;SACV;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;SAC1B;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACrC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,GAAW,KAAK,CAAC;QACtB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAErB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;QAChB,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;QAE9B,OAAO,IAAI,GAAG,UAAU,CAAC;IAC1B,CAAC;IA1Be,iBAAM,SA0BrB,CAAA;IAGD;;;;;;;OAOG;IACH,SAAgB,MAAM,CAAC,IAAY,EAAE,aAAqB;QACzD,IAAI,GAAG,IAAI,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QAClC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;QAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACnC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;QAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACnC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACb,CAAC;IARe,iBAAM,SAQrB,CAAA;IAED;;;;;;;;OAQG;IACH,SAAgB,QAAQ,CAAwC,IAAiB,EAAE,OAAe,YAAY;QAC7G,IAAI,IAAI,GAAW,UAAU,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;YACvB,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3B,MAAM,EAAE,CAAC;SACT;QAED,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACb,CAAC;IAVe,mBAAQ,WAUvB,CAAA;IAED;;;OAGG;IACH,SAAS,UAAU,CAAC,GAAW;QAC9B,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;QACrB,IAAI,GAAG,KAAK,CAAC,EAAE;YACd,OAAO,CAAC,CAAC;SACT;QAED,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,IAAI,IAAI,CAAC,CAAC;SACV;QAED,OAAO,IAAI,CAAC;IACb,CAAC;AACF,CAAC,EA5GgB,UAAU,GAAV,kBAAU,KAAV,kBAAU,QA4G1B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-03T02:09:42.1239660-07:00\r\nimport { Equatable } from \"./Stubs\";\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport namespace MurmurHash {\r\n\r\n\tconst DEFAULT_SEED: number = 0;\r\n\r\n\t/**\r\n\t * Initialize the hash using the specified `seed`.\r\n\t *\r\n\t * @param seed the seed (optional)\r\n\t * @returns the intermediate hash value\r\n\t */\r\n\texport function initialize(seed: number = DEFAULT_SEED): number {\r\n\t\treturn seed;\r\n\t}\r\n\r\n\t/**\r\n\t * Update the intermediate hash value for the next input `value`.\r\n\t *\r\n\t * @param hash the intermediate hash value\r\n\t * @param value the value to add to the current hash\r\n\t * @returns the updated intermediate hash value\r\n\t */\r\n\texport function update(hash: number, value: number | string | Equatable | null | undefined): number {\r\n\t\tconst c1: number = 0xCC9E2D51;\r\n\t\tconst c2: number = 0x1B873593;\r\n\t\tconst r1: number = 15;\r\n\t\tconst r2: number = 13;\r\n\t\tconst m: number = 5;\r\n\t\tconst n: number = 0xE6546B64;\r\n\r\n\t\tif (value == null) {\r\n\t\t\tvalue = 0;\r\n\t\t} else if (typeof value === \"string\") {\r\n\t\t\tvalue = hashString(value);\r\n\t\t} else if (typeof value === \"object\") {\r\n\t\t\tvalue = value.hashCode();\r\n\t\t}\r\n\r\n\t\tlet k: number = value;\r\n\t\tk = Math.imul(k, c1);\r\n\t\tk = (k << r1) | (k >>> (32 - r1));\r\n\t\tk = Math.imul(k, c2);\r\n\r\n\t\thash = hash ^ k;\r\n\t\thash = (hash << r2) | (hash >>> (32 - r2));\r\n\t\thash = Math.imul(hash, m) + n;\r\n\r\n\t\treturn hash & 0xFFFFFFFF;\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * Apply the final computation steps to the intermediate value `hash`\r\n\t * to form the final result of the MurmurHash 3 hash function.\r\n\t *\r\n\t * @param hash the intermediate hash value\r\n\t * @param numberOfWords the number of integer values added to the hash\r\n\t * @returns the final hash result\r\n\t */\r\n\texport function finish(hash: number, numberOfWords: number): number {\r\n\t\thash = hash ^ (numberOfWords * 4);\r\n\t\thash = hash ^ (hash >>> 16);\r\n\t\thash = Math.imul(hash, 0x85EBCA6B);\r\n\t\thash = hash ^ (hash >>> 13);\r\n\t\thash = Math.imul(hash, 0xC2B2AE35);\r\n\t\thash = hash ^ (hash >>> 16);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t/**\r\n\t * Utility function to compute the hash code of an array using the\r\n\t * MurmurHash algorithm.\r\n\t *\r\n\t * @param <T> the array element type\r\n\t * @param data the array data\r\n\t * @param seed the seed for the MurmurHash algorithm\r\n\t * @returns the hash code of the data\r\n\t */\r\n\texport function hashCode<T extends number | string | Equatable>(data: Iterable<T>, seed: number = DEFAULT_SEED): number {\r\n\t\tlet hash: number = initialize(seed);\r\n\t\tlet length = 0;\r\n\t\tfor (let value of data) {\r\n\t\t\thash = update(hash, value);\r\n\t\t\tlength++;\r\n\t\t}\r\n\r\n\t\thash = finish(hash, length);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t/**\r\n\t * Function to hash a string. Based on the implementation found here:\r\n\t * http://stackoverflow.com/a/7616484\r\n\t */\r\n\tfunction hashString(str: string): number {\r\n\t\tlet len = str.length;\r\n\t\tif (len === 0) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\tlet hash = 0;\r\n\t\tfor (let i = 0; i < len; i++) {\r\n\t\t\tlet c = str.charCodeAt(i);\r\n\t\t\thash = (((hash << 5) >>> 0) - hash) + c;\r\n\t\t\thash |= 0;\r\n\t\t}\r\n\r\n\t\treturn hash;\r\n\t}\r\n}\r\n"]}