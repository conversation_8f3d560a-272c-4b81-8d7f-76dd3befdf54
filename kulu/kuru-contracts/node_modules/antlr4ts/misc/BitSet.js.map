{"version": 3, "file": "BitSet.js", "sourceRoot": "", "sources": ["../../../src/misc/BitSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,6BAA6B;AAC7B,6CAA0C;AAE1C;;GAEG;AACH,MAAM,UAAU,GAAgB,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEnD;;GAEG;AACH,SAAS,QAAQ,CAAC,SAAiB;IAClC,OAAO,SAAS,KAAK,CAAC,CAAC;AACxB,CAAC;AAED;;GAEG;AAEH,SAAS,OAAO,CAAC,CAAS;IACzB,OAAO,CAAC,GAAG,EAAE,CAAC;AACf,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,IAAY;IAC/B,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO,CAAC,CAAC;SACT;QACD,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;KACvB;IACD,MAAM,IAAI,UAAU,CAAC,wBAAwB,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,UAAU,CAAC,IAAY;IAC/B,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO,CAAC,CAAC;SACT;QACD,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;KAChB;IACD,MAAM,IAAI,UAAU,CAAC,wBAAwB,CAAC,CAAC;AAChD,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CAAC,OAAe,EAAE,KAAa;IAC9C,OAAO,IAAI,GAAG,CAAC;IACf,KAAK,IAAI,GAAG,CAAC;IACb,IAAI,OAAO,KAAK,KAAK,EAAE;QACtB,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;KAC5B;IACD,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,GAAe,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;IAC5B,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE;QAC9B,2CAA2C;QAC3C,KAAK,IAAI,MAAM,CAAC;QAEhB,0CAA0C;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACjB,KAAK,EAAE,CAAC;SACR;KACD;CACD;AAED,MAAa,MAAM;IAmBlB;;MAEE;IACF,YAAY,GAA+B;QAC1C,IAAI,CAAC,GAAG,EAAE;YACT,iDAAiD;YACjD,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACnC,IAAI,GAAG,GAAG,CAAC,EAAE;gBACZ,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,CAAC;aACjD;iBAAM;gBACN,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACnD;SACD;aAAM;YACN,IAAI,GAAG,YAAY,MAAM,EAAE;gBAC1B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;aAChD;iBAAM;gBACN,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;gBACb,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;oBAClB,IAAI,GAAG,GAAG,CAAC,EAAE;wBACZ,GAAG,GAAG,CAAC,CAAC;qBACR;iBACD;gBACD,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnD,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;oBAClB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACZ;aACD;SACD;IACF,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,GAAW;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAElD,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,4CAA4C;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;SACxC;IACF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAW;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAElD,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,4CAA4C;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC3C,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;SACxC;IACF,CAAC;IAGD;;OAEG;IACI,WAAW;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,CAAC;SACT;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAyBM,KAAK,CAAC,SAAkB,EAAE,OAAgB;QAChD,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;aAAM,IAAI,OAAO,IAAI,IAAI,EAAE;YAC3B,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC3B;aAAM;YACN,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACpC;IACF,CAAC;IAqBM,IAAI,CAAC,SAAiB,EAAE,OAAgB;QAC9C,IAAI,OAAO,IAAI,IAAI,EAAE;YACpB,OAAO,GAAG,SAAS,CAAC;SACpB;QACD,IAAI,SAAS,GAAG,CAAC,IAAI,OAAO,GAAG,SAAS,EAAE;YACzC,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,IAAI,KAAK,QAAQ,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAC/C;aAAM;YACN,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC5C,OAAO,IAAI,GAAG,QAAQ,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC;aAC5B;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;SACzC;IACF,CAAC;IAqBM,GAAG,CAAC,SAAiB,EAAE,OAAgB;QAC7C,IAAI,OAAO,KAAK,SAAS,EAAE;YAC1B,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;SAC1E;aAAM;YACN,kBAAkB;YAClB,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YACrC,KAAK,IAAI,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B;YACD,OAAO,MAAM,CAAC;SACd;IACF,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,GAAW;QAC5B,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1D,IAAI,aAAa,KAAK,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,GAAG,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC;aACZ;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACtB,OAAO,CAAC,CAAC;SACT;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,SAAiB;QACpC,IAAI,SAAS,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;SACrD;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,MAAM,EAAE;YAClB,OAAO,CAAC,CAAC,CAAC;SACV;QAED,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;YACrC,IAAI,EAAE,CAAC;YACP,MAAM,GAAG,CAAC,CAAC;YACX,OAAO,IAAI,GAAG,MAAM,EAAE,IAAI,EAAE,EAAE;gBAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;oBAC1B,MAAM;iBACN;aACD;YACD,IAAI,IAAI,KAAK,MAAM,EAAE;gBACpB,cAAc;gBACd,OAAO,CAAC,CAAC,CAAC;aACV;SACD;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,UAAU,CAAC,SAAiB;QAClC,IAAI,SAAS,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;SACrD;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,MAAM,EAAE;YAClB,OAAO,CAAC,CAAC,CAAC;SACV;QACD,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,EAAE,CAAC;YACP,IAAI,GAAG,MAAM,CAAC;YACd,OAAO,IAAI,GAAG,MAAM,EAAE,IAAI,EAAE,EAAE;gBAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACrB,MAAM;iBACN;aACD;YACD,IAAI,IAAI,IAAI,MAAM,EAAE;gBACnB,OAAO,CAAC,CAAC,CAAC;aACV;SACD;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACI,EAAE,CAAC,GAAW;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;QAEnE,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAElB,0CAA0C;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,+CAA+C;QAE/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC,EAAE;YACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACjB;aAAM;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACpC;IACF,CAAC;IAED;;;;;;;OAOG;IACI,gBAAgB,CAAC,SAAiB;QACxC,IAAI,SAAS,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;SACrD;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,IAAI,IAAI,MAAM,EAAE;YACnB,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;YACrC,MAAM,GAAG,CAAC,CAAC;YACX,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;gBACzB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;oBAC1B,MAAM;iBACN;aACD;YACD,IAAI,IAAI,GAAG,CAAC,EAAE;gBACb,cAAc;gBACd,OAAO,CAAC,CAAC,CAAC;aACV;SACD;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;IACnE,CAAC;IAGD;;;;;;;;;;;;;;;OAeG;IACI,cAAc,CAAC,SAAiB;QACtC,IAAI,SAAS,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;SACrD;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,IAAI,IAAI,MAAM,EAAE;YACnB,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,EAAE,CAAC;YACP,IAAI,GAAG,MAAM,CAAC;YACd,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;gBACzB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACrB,MAAM;iBACN;aACD;YACD,IAAI,IAAI,GAAG,CAAC,EAAE;gBACb,OAAO,CAAC,CAAC,CAAC;aACV;SACD;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;IA0CM,GAAG,CAAC,SAAiB,EAAE,OAA0B,EAAE,KAAe;QACxE,IAAI,OAAO,KAAK,SAAS,EAAE;YAC1B,OAAO,GAAG,SAAS,CAAC;YACpB,KAAK,GAAG,IAAI,CAAC;SACb;aAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YACxC,KAAK,GAAG,OAAO,CAAC;YAChB,OAAO,GAAG,SAAS,CAAC;SACpB;QAED,IAAI,KAAK,KAAK,SAAS,EAAE;YACxB,KAAK,GAAG,IAAI,CAAC;SACb;QAED,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,OAAO,EAAE;YACzC,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEjC,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC1C,mDAAmD;YACnD,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACjB;aAAM,IAAI,CAAC,KAAK,EAAE;YAClB,oDAAoD;YACpD,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC7B,aAAa;gBACb,OAAO;aACP;YACD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjC,2BAA2B;gBAC3B,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;aACpC;SACD;QAED,IAAI,IAAI,KAAK,QAAQ,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;SACxD;aAAM;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;YACrD,OAAO,IAAI,GAAG,QAAQ,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;SAChD;IACF,CAAC;IAEO,QAAQ,CAAC,IAAY,EAAE,KAAc,EAAE,IAAY;QAC1D,IAAI,KAAK,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;SACxB;aAAM;YACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC;SACjC;IACF,CAAC;IAED;;;OAGG;IACH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;OAOG;IACH,6BAA6B;IAC7B,uCAAuC;IACvC,IAAI;IAEJ;;;;;;;OAOG;IACH,iCAAiC;IACjC,uCAAuC;IACvC,IAAI;IAEG,QAAQ;QACd,OAAO,uBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,GAAQ;QACrB,IAAI,GAAG,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,CAAC;SACZ;aAAM,IAAI,CAAC,CAAC,GAAG,YAAY,MAAM,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SACb;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE1B,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE;YACzB,OAAO,KAAK,CAAC;SACb;QAED,IAAI,GAAG,KAAK,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACjC,OAAO,KAAK,CAAC;aACb;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,QAAQ;QACd,IAAI,MAAM,GAAG,GAAG,CAAC;QAEjB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YACpE,IAAI,KAAK,EAAE;gBACV,KAAK,GAAG,KAAK,CAAC;aACd;iBAAM;gBACN,MAAM,IAAI,IAAI,CAAC;aACf;YAED,MAAM,IAAI,CAAC,CAAC;SACZ;QAED,MAAM,IAAI,GAAG,CAAC;QACd,OAAO,MAAM,CAAC;IACf,CAAC;IAED,4CAA4C;IAC5C,+CAA+C;IAC/C,gDAAgD;IAChD,uEAAuE;IACvE,uCAAuC;IACvC,IAAI;IAEJ;;;;;;OAMG;IACI,GAAG,CAAC,GAAW;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;QAEnE,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAElB,2CAA2C;QAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,+CAA+C;QAE/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC;aACb;SACD;QAED,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC,EAAE;YACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACjB;aAAM;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;SACxC;IACF,CAAC;IAEM,KAAK;QACX,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEM,CAAC,MAAM,CAAC,QAAQ,CAAC;QACvB,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,8CAA8C;IACvC,CAAE,IAAI,CAAC,OAAe,CAAC,MAAM,CAAC;QACpC,OAAO,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;CACD;AA7uBD,wBA6uBC;AAED,MAAM,cAAc;IAInB,YAAoB,IAAiB;QAAjB,SAAI,GAAJ,IAAI,CAAa;QAH7B,UAAK,GAAG,CAAC,CAAC;QACV,SAAI,GAAG,MAAM,CAAC;IAEmB,CAAC;IAEnC,IAAI;QACV,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;YAC/C,IAAI,IAAI,KAAK,CAAC,EAAE;gBACf,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;gBACvC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;aACzC;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;SACnB;QACD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;IAClC,CAAC;IAEM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAA+B,OAAO,IAAI,CAAC,CAAC,CAAC;CACrE", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport * as assert from \"assert\";\r\nimport * as util from \"util\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\n\r\n/**\r\n * Private empty array used to construct empty BitSets\r\n */\r\nconst EMPTY_DATA: Uint16Array = new Uint16Array(0);\r\n\r\n/**\r\n * Gets the word index of the `UInt16` element in `BitSet.data` containing the bit with the specified index.\r\n */\r\nfunction getIndex(bitNumber: number) {\r\n\treturn bitNumber >>> 4;\r\n}\r\n\r\n/**\r\n * Convert a word index into the bit index of the LSB of that word\r\n */\r\n\r\nfunction unIndex(n: number) {\r\n\treturn n * 16;\r\n}\r\n\r\n/**\r\n * Get's the bit number of the least signficant bit set LSB which is set in a word non-zero word;\r\n * Bit numbers run from LSB to MSB starting with 0.\r\n */\r\nfunction findLSBSet(word: number) {\r\n\tlet bit = 1;\r\n\tfor (let i = 0; i < 16; i++) {\r\n\t\tif ((word & bit) !== 0) {\r\n\t\t\treturn i;\r\n\t\t}\r\n\t\tbit = (bit << 1) >>> 0;\r\n\t}\r\n\tthrow new RangeError(\"No specified bit found\");\r\n}\r\n\r\nfunction findMSBSet(word: number) {\r\n\tlet bit = (1 << 15) >>> 0;\r\n\tfor (let i = 15; i >= 0; i--) {\r\n\t\tif ((word & bit) !== 0) {\r\n\t\t\treturn i;\r\n\t\t}\r\n\t\tbit = bit >>> 1;\r\n\t}\r\n\tthrow new RangeError(\"No specified bit found\");\r\n}\r\n\r\n/**\r\n * Gets a 16-bit mask with bit numbers fromBit to toBit (inclusive) set.\r\n * Bit numbers run from LSB to MSB starting with 0.\r\n */\r\nfunction bitsFor(fromBit: number, toBit: number): number {\r\n\tfromBit &= 0xF;\r\n\ttoBit &= 0xF;\r\n\tif (fromBit === toBit) {\r\n\t\treturn (1 << fromBit) >>> 0;\r\n\t}\r\n\treturn ((0xFFFF >>> (15 - toBit)) ^ (0xFFFF >>> (16 - fromBit)));\r\n}\r\n\r\n/**\r\n * A lookup table for number of set bits in a 16-bit integer.   This is used to quickly count the cardinality (number of unique elements) of a BitSet.\r\n */\r\nconst POP_CNT: Uint8Array = new Uint8Array(65536);\r\nfor (let i = 0; i < 16; i++) {\r\n\tconst stride = (1 << i) >>> 0;\r\n\tlet index = 0;\r\n\twhile (index < POP_CNT.length) {\r\n\t\t// skip the numbers where the bit isn't set\r\n\t\tindex += stride;\r\n\r\n\t\t// increment the ones where the bit is set\r\n\t\tfor (let j = 0; j < stride; j++) {\r\n\t\t\tPOP_CNT[index]++;\r\n\t\t\tindex++;\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport class BitSet implements Iterable<number>{\r\n\tprivate data: Uint16Array;\r\n\r\n\t/**\r\n\t * Creates a new bit set. All bits are initially `false`.\r\n\t */\r\n\tconstructor();\r\n\r\n\t/**\r\n\t * Creates a bit set whose initial size is large enough to explicitly represent bits with indices in the range `0`\r\n\t * through `nbits-1`. All bits are initially `false`.\r\n\t */\r\n\tconstructor(nbits: number);\r\n\r\n\t/**\r\n\t * Creates a bit set from a iterable list of numbers (including another BitSet);\r\n\t */\r\n\tconstructor(numbers: Iterable<number>);\r\n\r\n\t/*\r\n\t** constructor implementation\r\n\t*/\r\n\tconstructor(arg?: number | Iterable<number>) {\r\n\t\tif (!arg) {\r\n\t\t\t// covering the case of unspecified and nbits===0\r\n\t\t\tthis.data = EMPTY_DATA;\r\n\t\t} else if (typeof arg === \"number\") {\r\n\t\t\tif (arg < 0) {\r\n\t\t\t\tthrow new RangeError(\"nbits cannot be negative\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.data = new Uint16Array(getIndex(arg - 1) + 1);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (arg instanceof BitSet) {\r\n\t\t\t\tthis.data = arg.data.slice(0); // Clone the data\r\n\t\t\t} else {\r\n\t\t\t\tlet max = -1;\r\n\t\t\t\tfor (let v of arg) {\r\n\t\t\t\t\tif (max < v) {\r\n\t\t\t\t\t\tmax = v;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.data = new Uint16Array(getIndex(max - 1) + 1);\r\n\t\t\t\tfor (let v of arg) {\r\n\t\t\t\t\tthis.set(v);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Performs a logical **AND** of this target bit set with the argument bit set. This bit set is modified so that\r\n\t * each bit in it has the value `true` if and only if it both initially had the value `true` and the corresponding\r\n\t * bit in the bit set argument also had the value `true`.\r\n\t */\r\n\tpublic and(set: BitSet): void {\r\n\t\tconst data = this.data;\r\n\t\tconst other = set.data;\r\n\t\tconst words = Math.min(data.length, other.length);\r\n\r\n\t\tlet lastWord = -1;\t// Keep track of index of last non-zero word\r\n\r\n\t\tfor (let i = 0; i < words; i++) {\r\n\t\t\tlet value = data[i] &= other[i];\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (lastWord === -1) {\r\n\t\t\tthis.data = EMPTY_DATA;\r\n\t\t}\r\n\r\n\t\tif (lastWord < data.length - 1) {\r\n\t\t\tthis.data = data.slice(0, lastWord + 1);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Clears all of the bits in this `BitSet` whose corresponding bit is set in the specified `BitSet`.\r\n\t */\r\n\tpublic andNot(set: BitSet): void {\r\n\t\tconst data = this.data;\r\n\t\tconst other = set.data;\r\n\t\tconst words = Math.min(data.length, other.length);\r\n\r\n\t\tlet lastWord = -1;\t// Keep track of index of last non-zero word\r\n\r\n\t\tfor (let i = 0; i < words; i++) {\r\n\t\t\tlet value = data[i] &= (other[i] ^ 0xFFFF);\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (lastWord === -1) {\r\n\t\t\tthis.data = EMPTY_DATA;\r\n\t\t}\r\n\r\n\t\tif (lastWord < data.length - 1) {\r\n\t\t\tthis.data = data.slice(0, lastWord + 1);\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * Returns the number of bits set to `true` in this `BitSet`.\r\n\t */\r\n\tpublic cardinality(): number {\r\n\t\tif (this.isEmpty) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\t\tconst data = this.data;\r\n\t\tconst length = data.length;\r\n\t\tlet result = 0;\r\n\r\n\t\tfor (let i = 0; i < length; i++) {\r\n\t\t\tresult += POP_CNT[data[i]];\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t/**\r\n\t * Sets all of the bits in this `BitSet` to `false`.\r\n\t */\r\n\tpublic clear(): void;\r\n\r\n\t/**\r\n\t * Sets the bit specified by the index to `false`.\r\n\t *\r\n\t * @param bitIndex the index of the bit to be cleared\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic clear(bitIndex: number): void;\r\n\r\n\t/**\r\n\t * Sets the bits from the specified `fromIndex` (inclusive) to the specified `toIndex` (exclusive) to `false`.\r\n\t *\r\n\t * @param fromIndex index of the first bit to be cleared\r\n\t * @param toIndex index after the last bit to be cleared\r\n\t *\r\n\t * @throws RangeError if `fromIndex` is negative, or `toIndex` is negative, or `fromIndex` is larger than `toIndex`\r\n\t */\r\n\tpublic clear(fromIndex: number, toIndex: number): void;\r\n\tpublic clear(fromIndex?: number, toIndex?: number): void {\r\n\t\tif (fromIndex == null) {\r\n\t\t\tthis.data.fill(0);\r\n\t\t} else if (toIndex == null) {\r\n\t\t\tthis.set(fromIndex, false);\r\n\t\t} else {\r\n\t\t\tthis.set(fromIndex, toIndex, false);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Sets the bit at the specified index to the complement of its current value.\r\n\t *\r\n\t * @param bitIndex the index of the bit to flip\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic flip(bitIndex: number): void;\r\n\r\n\t/**\r\n\t * Sets each bit from the specified `fromIndex` (inclusive) to the specified `toIndex` (exclusive) to the complement\r\n\t * of its current value.\r\n\t *\r\n\t * @param fromIndex index of the first bit to flip\r\n\t * @param toIndex index after the last bit to flip\r\n\t *\r\n\t * @throws RangeError if `fromIndex` is negative, or `toIndex` is negative, or `fromIndex` is larger than `toIndex`\r\n\t */\r\n\tpublic flip(fromIndex: number, toIndex: number): void;\r\n\tpublic flip(fromIndex: number, toIndex?: number): void {\r\n\t\tif (toIndex == null) {\r\n\t\t\ttoIndex = fromIndex;\r\n\t\t}\r\n\t\tif (fromIndex < 0 || toIndex < fromIndex) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tconst lastWord = getIndex(toIndex);\r\n\r\n\t\tif (word === lastWord) {\r\n\t\t\tthis.data[word] ^= bitsFor(fromIndex, toIndex);\r\n\t\t} else {\r\n\t\t\tthis.data[word++] ^= bitsFor(fromIndex, 15);\r\n\t\t\twhile (word < lastWord) {\r\n\t\t\t\tthis.data[word++] ^= 0xFFFF;\r\n\t\t\t}\r\n\t\t\tthis.data[word++] ^= bitsFor(0, toIndex);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the value of the bit with the specified index. The value is `true` if the bit with the index `bitIndex`\r\n\t * is currently set in this `BitSet`; otherwise, the result is `false`.\r\n\t *\r\n\t * @param bitIndex the bit index\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic get(bitIndex: number): boolean;\r\n\r\n\t/**\r\n\t * Returns a new `BitSet` composed of bits from this `BitSet` from `fromIndex` (inclusive) to `toIndex` (exclusive).\r\n\t *\r\n\t * @param fromIndex index of the first bit to include\r\n\t * @param toIndex index after the last bit to include\r\n\t *\r\n\t * @throws RangeError if `fromIndex` is negative, or `toIndex` is negative, or `fromIndex` is larger than `toIndex`\r\n\t */\r\n\tpublic get(fromIndex: number, toIndex: number): BitSet;\r\n\tpublic get(fromIndex: number, toIndex?: number): boolean | BitSet {\r\n\t\tif (toIndex === undefined) {\r\n\t\t\treturn !!(this.data[getIndex(fromIndex)] & bitsFor(fromIndex, fromIndex));\r\n\t\t} else {\r\n\t\t\t// return a BitSet\r\n\t\t\tlet result = new BitSet(toIndex + 1);\r\n\t\t\tfor (let i = fromIndex; i <= toIndex; i++) {\r\n\t\t\t\tresult.set(i, this.get(i));\r\n\t\t\t}\r\n\t\t\treturn result;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns true if the specified `BitSet` has any bits set to `true` that are also set to `true` in this `BitSet`.\r\n\t *\r\n\t * @param set `BitSet` to intersect with\r\n\t */\r\n\tpublic intersects(set: BitSet): boolean {\r\n\t\tlet smallerLength = Math.min(this.length(), set.length());\r\n\t\tif (smallerLength === 0) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tlet bound = getIndex(smallerLength - 1);\r\n\t\tfor (let i = 0; i <= bound; i++) {\r\n\t\t\tif ((this.data[i] & set.data[i]) !== 0) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns true if this `BitSet` contains no bits that are set to `true`.\r\n\t */\r\n\tget isEmpty(): boolean {\r\n\t\treturn this.length() === 0;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the \"logical size\" of this `BitSet`: the index of the highest set bit in the `BitSet` plus one. Returns\r\n\t * zero if the `BitSet` contains no set bits.\r\n\t */\r\n\tpublic length(): number {\r\n\t\tif (!this.data.length) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\t\treturn this.previousSetBit(unIndex(this.data.length) - 1) + 1;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the index of the first bit that is set to `false` that occurs on or after the specified starting index,\r\n\t * If no such bit exists then `-1` is returned.\r\n\t *\r\n\t * @param fromIndex the index to start checking from (inclusive)\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic nextClearBit(fromIndex: number): number {\r\n\t\tif (fromIndex < 0) {\r\n\t\t\tthrow new RangeError(\"fromIndex cannot be negative\");\r\n\t\t}\r\n\r\n\t\tconst data = this.data;\r\n\t\tconst length = data.length;\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tif (word > length) {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\r\n\t\tlet ignore = 0xFFFF ^ bitsFor(fromIndex, 15);\r\n\r\n\t\tif ((data[word] | ignore) === 0xFFFF) {\r\n\t\t\tword++;\r\n\t\t\tignore = 0;\r\n\t\t\tfor (; word < length; word++) {\r\n\t\t\t\tif (data[word] !== 0xFFFF) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (word === length) {\r\n\t\t\t\t// Hit the end\r\n\t\t\t\treturn -1;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn unIndex(word) + findLSBSet((data[word] | ignore) ^ 0xFFFF);\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the index of the first bit that is set to `true` that occurs on or after the specified starting index.\r\n\t * If no such bit exists then `-1` is returned.\r\n\t *\r\n\t * To iterate over the `true` bits in a `BitSet`, use the following loop:\r\n\t *\r\n\t * ```\r\n\t * for (let i = bs.nextSetBit(0); i >= 0; i = bs.nextSetBit(i + 1)) {\r\n\t *   // operate on index i here\r\n\t * }\r\n\t * ```\r\n\t *\r\n\t * @param fromIndex the index to start checking from (inclusive)\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic nextSetBit(fromIndex: number): number {\r\n\t\tif (fromIndex < 0) {\r\n\t\t\tthrow new RangeError(\"fromIndex cannot be negative\");\r\n\t\t}\r\n\r\n\t\tconst data = this.data;\r\n\t\tconst length = data.length;\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tif (word > length) {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t\tlet mask = bitsFor(fromIndex, 15);\r\n\r\n\t\tif ((data[word] & mask) === 0) {\r\n\t\t\tword++;\r\n\t\t\tmask = 0xFFFF;\r\n\t\t\tfor (; word < length; word++) {\r\n\t\t\t\tif (data[word] !== 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (word >= length) {\r\n\t\t\t\treturn -1;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn unIndex(word) + findLSBSet(data[word] & mask);\r\n\t}\r\n\r\n\t/**\r\n\t * Performs a logical **OR** of this bit set with the bit set argument. This bit set is modified so that a bit in it\r\n\t * has the value `true` if and only if it either already had the value `true` or the corresponding bit in the bit\r\n\t * set argument has the value `true`.\r\n\t */\r\n\tpublic or(set: BitSet): void {\r\n\t\tconst data = this.data;\r\n\t\tconst other = set.data;\r\n\t\tconst minWords = Math.min(data.length, other.length);\r\n\t\tconst words = Math.max(data.length, other.length);\r\n\t\tconst dest = data.length === words ? data : new Uint16Array(words);\r\n\r\n\t\tlet lastWord = -1;\r\n\r\n\t\t// Or those words both sets have in common\r\n\r\n\t\tfor (let i = 0; i < minWords; i++) {\r\n\t\t\tlet value = dest[i] = data[i] | other[i];\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Copy words from larger set (if there is one)\r\n\r\n\t\tconst longer = data.length > other.length ? data : other;\r\n\t\tfor (let i = minWords; i < words; i++) {\r\n\t\t\tlet value = dest[i] = longer[i];\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (lastWord === -1) {\r\n\t\t\tthis.data = EMPTY_DATA;\r\n\t\t} else if (dest.length === lastWord + 1) {\r\n\t\t\tthis.data = dest;\r\n\t\t} else {\r\n\t\t\tthis.data = dest.slice(0, lastWord);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the index of the nearest bit that is set to `false` that occurs on or before the specified starting\r\n\t * index. If no such bit exists, or if `-1` is given as the starting index, then `-1` is returned.\r\n\t *\r\n\t * @param fromIndex the index to start checking from (inclusive)\r\n\t *\r\n\t * @throws RangeError if the specified index is less than `-1`\r\n\t */\r\n\tpublic previousClearBit(fromIndex: number): number {\r\n\t\tif (fromIndex < 0) {\r\n\t\t\tthrow new RangeError(\"fromIndex cannot be negative\");\r\n\t\t}\r\n\r\n\t\tconst data = this.data;\r\n\t\tconst length = data.length;\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tif (word >= length) {\r\n\t\t\tword = length - 1;\r\n\t\t}\r\n\r\n\t\tlet ignore = 0xFFFF ^ bitsFor(0, fromIndex);\r\n\r\n\t\tif ((data[word] | ignore) === 0xFFFF) {\r\n\t\t\tignore = 0;\r\n\t\t\tword--;\r\n\t\t\tfor (; word >= 0; word--) {\r\n\t\t\t\tif (data[word] !== 0xFFFF) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (word < 0) {\r\n\t\t\t\t// Hit the end\r\n\t\t\t\treturn -1;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn unIndex(word) + findMSBSet((data[word] | ignore) ^ 0xFFFF);\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * Returns the index of the nearest bit that is set to `true` that occurs on or before the specified starting index.\r\n\t * If no such bit exists, or if `-1` is given as the starting index, then `-1` is returned.\r\n\t *\r\n\t * To iterate over the `true` bits in a `BitSet`, use the following loop:\r\n\t *\r\n\t * ```\r\n\t * for (let i = bs.length(); (i = bs.previousSetBit(i-1)) >= 0; ) {\r\n\t *   // operate on index i here\r\n\t * }\r\n\t * ```\r\n\t *\r\n\t * @param fromIndex the index to start checking from (inclusive)\r\n\t *\r\n\t * @throws RangeError if the specified index is less than `-1`\r\n\t */\r\n\tpublic previousSetBit(fromIndex: number): number {\r\n\t\tif (fromIndex < 0) {\r\n\t\t\tthrow new RangeError(\"fromIndex cannot be negative\");\r\n\t\t}\r\n\r\n\t\tconst data = this.data;\r\n\t\tconst length = data.length;\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tif (word >= length) {\r\n\t\t\tword = length - 1;\r\n\t\t}\r\n\r\n\t\tlet mask = bitsFor(0, fromIndex);\r\n\r\n\t\tif ((data[word] & mask) === 0) {\r\n\t\t\tword--;\r\n\t\t\tmask = 0xFFFF;\r\n\t\t\tfor (; word >= 0; word--) {\r\n\t\t\t\tif (data[word] !== 0) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (word < 0) {\r\n\t\t\t\treturn -1;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn unIndex(word) + findMSBSet(data[word] & mask);\r\n\t}\r\n\r\n\t/**\r\n\t * Sets the bit at the specified index to `true`.\r\n\t *\r\n\t * @param bitIndex a bit index\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic set(bitIndex: number): void;\r\n\r\n\t/**\r\n\t * Sets the bit at the specified index to the specified value.\r\n\t *\r\n\t * @param bitIndex a bit index\r\n\t * @param value a boolean value to set\r\n\t *\r\n\t * @throws RangeError if the specified index is negative\r\n\t */\r\n\tpublic set(bitIndex: number, value: boolean): void;\r\n\r\n\t/**\r\n\t * Sets the bits from the specified `fromIndex` (inclusive) to the specified `toIndex` (exclusive) to `true`.\r\n\t *\r\n\t * @param fromIndex index of the first bit to be set\r\n\t * @param toIndex index after the last bit to be set\r\n\t *\r\n\t * @throws RangeError if `fromIndex` is negative, or `toIndex` is negative, or `fromIndex` is larger than `toIndex`\r\n\t */\r\n\tpublic set(fromIndex: number, toIndex: number): void;\r\n\r\n\t/**\r\n\t * Sets the bits from the specified `fromIndex` (inclusive) to the specified `toIndex` (exclusive) to the specified\r\n\t * value.\r\n\t *\r\n\t * @param fromIndex index of the first bit to be set\r\n\t * @param toIndex index after the last bit to be set\r\n\t * @param value value to set the selected bits to\r\n\t *\r\n\t * @throws RangeError if `fromIndex` is negative, or `toIndex` is negative, or `fromIndex` is larger than `toIndex`\r\n\t */\r\n\tpublic set(fromIndex: number, toIndex: number, value: boolean): void;\r\n\tpublic set(fromIndex: number, toIndex?: boolean | number, value?: boolean): void {\r\n\t\tif (toIndex === undefined) {\r\n\t\t\ttoIndex = fromIndex;\r\n\t\t\tvalue = true;\r\n\t\t} else if (typeof toIndex === \"boolean\") {\r\n\t\t\tvalue = toIndex;\r\n\t\t\ttoIndex = fromIndex;\r\n\t\t}\r\n\r\n\t\tif (value === undefined) {\r\n\t\t\tvalue = true;\r\n\t\t}\r\n\r\n\t\tif (fromIndex < 0 || fromIndex > toIndex) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\tlet word = getIndex(fromIndex);\r\n\t\tlet lastWord = getIndex(toIndex);\r\n\r\n\t\tif (value && lastWord >= this.data.length) {\r\n\t\t\t// Grow array \"just enough\" for bits we need to set\r\n\t\t\tlet temp = new Uint16Array(lastWord + 1);\r\n\t\t\tthis.data.forEach((value, index) => temp[index] = value);\r\n\t\t\tthis.data = temp;\r\n\t\t} else if (!value) {\r\n\t\t\t// But there is no need to grow array to clear bits.\r\n\t\t\tif (word >= this.data.length) {\r\n\t\t\t\t// Early exit\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (lastWord >= this.data.length) {\r\n\t\t\t\t// Adjust work to fit array\r\n\t\t\t\tlastWord = this.data.length - 1;\r\n\t\t\t\ttoIndex = this.data.length * 16 - 1;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (word === lastWord) {\r\n\t\t\tthis._setBits(word, value, bitsFor(fromIndex, toIndex));\r\n\t\t} else {\r\n\t\t\tthis._setBits(word++, value, bitsFor(fromIndex, 15));\r\n\t\t\twhile (word < lastWord) {\r\n\t\t\t\tthis.data[word++] = value ? 0xFFFF : 0;\r\n\t\t\t}\r\n\t\t\tthis._setBits(word, value, bitsFor(0, toIndex));\r\n\t\t}\r\n\t}\r\n\r\n\tprivate _setBits(word: number, value: boolean, mask: number) {\r\n\t\tif (value) {\r\n\t\t\tthis.data[word] |= mask;\r\n\t\t} else {\r\n\t\t\tthis.data[word] &= 0xFFFF ^ mask;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the number of bits of space actually in use by this `BitSet` to represent bit values. The maximum element\r\n\t * in the set is the size - 1st element.\r\n\t */\r\n\tget size(): number {\r\n\t\treturn this.data.byteLength * 8;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a new byte array containing all the bits in this bit set.\r\n\t *\r\n\t * More precisely, if\r\n\t * `let bytes = s.toByteArray();`\r\n\t * then `bytes.length === (s.length()+7)/8` and `s.get(n) === ((bytes[n/8] & (1<<(n%8))) != 0)` for all\r\n\t * `n < 8 * bytes.length`.\r\n\t */\r\n\t// toByteArray(): Int8Array {\r\n\t// \tthrow new Error(\"NOT IMPLEMENTED\");\r\n\t// }\r\n\r\n\t/**\r\n\t * Returns a new integer array containing all the bits in this bit set.\r\n\t *\r\n\t * More precisely, if\r\n\t * `let integers = s.toIntegerArray();`\r\n\t * then `integers.length === (s.length()+31)/32` and `s.get(n) === ((integers[n/32] & (1<<(n%32))) != 0)` for all\r\n\t * `n < 32 * integers.length`.\r\n\t */\r\n\t// toIntegerArray(): Int32Array {\r\n\t// \tthrow new Error(\"NOT IMPLEMENTED\");\r\n\t// }\r\n\r\n\tpublic hashCode(): number {\r\n\t\treturn MurmurHash.hashCode(this.data, 22);\r\n\t}\r\n\r\n\t/**\r\n\t * Compares this object against the specified object. The result is `true` if and only if the argument is not\r\n\t * `undefined` and is a `Bitset` object that has exactly the same set of bits set to `true` as this bit set. That\r\n\t * is, for every nonnegative index `k`,\r\n\t *\r\n\t * ```\r\n\t * ((BitSet)obj).get(k) == this.get(k)\r\n\t * ```\r\n\t *\r\n\t * must be true. The current sizes of the two bit sets are not compared.\r\n\t */\r\n\tpublic equals(obj: any): boolean {\r\n\t\tif (obj === this) {\r\n\t\t\treturn true;\r\n\t\t} else if (!(obj instanceof BitSet)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tconst len = this.length();\r\n\r\n\t\tif (len !== obj.length()) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (len === 0) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tlet bound = getIndex(len - 1);\r\n\t\tfor (let i = 0; i <= bound; i++) {\r\n\t\t\tif (this.data[i] !== obj.data[i]) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a string representation of this bit set. For every index for which this `BitSet` contains a bit in the\r\n\t * set state, the decimal representation of that index is included in the result. Such indices are listed in order\r\n\t * from lowest to highest, separated by \", \" (a comma and a space) and surrounded by braces, resulting in the usual\r\n\t * mathematical notation for a set of integers.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t *     BitSet drPepper = new BitSet();\r\n\t *\r\n\t * Now `drPepper.toString()` returns `\"{}\"`.\r\n\t *\r\n\t *     drPepper.set(2);\r\n\t *\r\n\t * Now `drPepper.toString()` returns `\"{2}\"`.\r\n\t *\r\n\t *     drPepper.set(4);\r\n\t *     drPepper.set(10);\r\n\t *\r\n\t * Now `drPepper.toString()` returns `\"{2, 4, 10}\"`.\r\n\t */\r\n\tpublic toString(): string {\r\n\t\tlet result = \"{\";\r\n\r\n\t\tlet first = true;\r\n\t\tfor (let i = this.nextSetBit(0); i >= 0; i = this.nextSetBit(i + 1)) {\r\n\t\t\tif (first) {\r\n\t\t\t\tfirst = false;\r\n\t\t\t} else {\r\n\t\t\t\tresult += \", \";\r\n\t\t\t}\r\n\r\n\t\t\tresult += i;\r\n\t\t}\r\n\r\n\t\tresult += \"}\";\r\n\t\treturn result;\r\n\t}\r\n\r\n\t// static valueOf(bytes: Int8Array): BitSet;\r\n\t// static valueOf(buffer: ArrayBuffer): BitSet;\r\n\t// static valueOf(integers: Int32Array): BitSet;\r\n\t// static valueOf(data: Int8Array | Int32Array | ArrayBuffer): BitSet {\r\n\t// \tthrow new Error(\"NOT IMPLEMENTED\");\r\n\t// }\r\n\r\n\t/**\r\n\t * Performs a logical **XOR** of this bit set with the bit set argument. This bit set is modified so that a bit in\r\n\t * it has the value `true` if and only if one of the following statements holds:\r\n\t *\r\n\t * * The bit initially has the value `true`, and the corresponding bit in the argument has the value `false`.\r\n\t * * The bit initially has the value `false`, and the corresponding bit in the argument has the value `true`.\r\n\t */\r\n\tpublic xor(set: BitSet): void {\r\n\t\tconst data = this.data;\r\n\t\tconst other = set.data;\r\n\t\tconst minWords = Math.min(data.length, other.length);\r\n\t\tconst words = Math.max(data.length, other.length);\r\n\t\tconst dest = data.length === words ? data : new Uint16Array(words);\r\n\r\n\t\tlet lastWord = -1;\r\n\r\n\t\t// Xor those words both sets have in common\r\n\r\n\t\tfor (let i = 0; i < minWords; i++) {\r\n\t\t\tlet value = dest[i] = data[i] ^ other[i];\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Copy words from larger set (if there is one)\r\n\r\n\t\tconst longer = data.length > other.length ? data : other;\r\n\t\tfor (let i = minWords; i < words; i++) {\r\n\t\t\tlet value = dest[i] = longer[i];\r\n\t\t\tif (value !== 0) {\r\n\t\t\t\tlastWord = i;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (lastWord === -1) {\r\n\t\t\tthis.data = EMPTY_DATA;\r\n\t\t} else if (dest.length === lastWord + 1) {\r\n\t\t\tthis.data = dest;\r\n\t\t} else {\r\n\t\t\tthis.data = dest.slice(0, lastWord + 1);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic clone() {\r\n\t\treturn new BitSet(this);\r\n\t}\r\n\r\n\tpublic [Symbol.iterator](): IterableIterator<number> {\r\n\t\treturn new BitSetIterator(this.data);\r\n\t}\r\n\r\n\t// Overrides formatting for nodejs assert etc.\r\n\tpublic [(util.inspect as any).custom](): string {\r\n\t\treturn \"BitSet \" + this.toString();\r\n\t}\r\n}\r\n\r\nclass BitSetIterator implements IterableIterator<number>{\r\n\tprivate index = 0;\r\n\tprivate mask = 0xFFFF;\r\n\r\n\tconstructor(private data: Uint16Array) { }\r\n\r\n\tpublic next() {\r\n\t\twhile (this.index < this.data.length) {\r\n\t\t\tconst bits = this.data[this.index] & this.mask;\r\n\t\t\tif (bits !== 0) {\r\n\t\t\t\tconst bitNumber = unIndex(this.index) + findLSBSet(bits);\r\n\t\t\t\tthis.mask = bitsFor(bitNumber + 1, 15);\r\n\t\t\t\treturn { done: false, value: bitNumber };\r\n\t\t\t}\r\n\t\t\tthis.index++;\r\n\t\t\tthis.mask = 0xFFFF;\r\n\t\t}\r\n\t\treturn { done: true, value: -1 };\r\n\t}\r\n\r\n\tpublic [Symbol.iterator](): IterableIterator<number> { return this; }\r\n}\r\n"]}