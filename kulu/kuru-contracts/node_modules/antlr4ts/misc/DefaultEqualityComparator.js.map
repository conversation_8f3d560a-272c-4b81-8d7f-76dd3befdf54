{"version": 3, "file": "DefaultEqualityComparator.js", "sourceRoot": "", "sources": ["../../../src/misc/DefaultEqualityComparator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAGH,8CAAyC;AAEzC,6CAA0C;AAC1C,yEAAsE;AAEtE;;;;;GAKG;AACH,MAAa,yBAAyB;IAGrC;;;;;OAKG;IAEI,QAAQ,CAAC,GAAQ;QACvB,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,CAAC;SACT;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC9D,OAAO,uBAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAClC;aAAM;YACN,OAAO,mDAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAgB,CAAC,CAAC;SACpE;IACF,CAAC;IAED;;;;;;;;OAQG;IAEI,MAAM,CAAC,CAAM,EAAE,CAAM;QAC3B,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,OAAO,CAAC,IAAI,IAAI,CAAC;SACjB;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC1D,OAAO,CAAC,KAAK,CAAC,CAAC;SACf;aAAM;YACN,OAAO,mDAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAc,EAAE,CAAc,CAAC,CAAC;SAChF;IACF,CAAC;;AArCsB,kCAAQ,GAA8B,IAAI,yBAAyB,EAAE,CAAC;AAS7F;IADC,qBAAQ;yDASR;AAYD;IADC,qBAAQ;uDASR;AAtCF,8DAuCC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport { EqualityComparator } from \"./EqualityComparator\";\r\nimport { Override } from \"../Decorators\";\r\nimport { Equatable } from \"./Stubs\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\nimport { ObjectEqualityComparator } from \"./ObjectEqualityComparator\";\r\n\r\n/**\r\n * This default implementation of {@link EqualityComparator} uses object equality\r\n * for comparisons by calling {@link Object#hashCode} and {@link Object#equals}.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class DefaultEqualityComparator implements EqualityComparator<any> {\r\n\tpublic static readonly INSTANCE: DefaultEqualityComparator = new DefaultEqualityComparator();\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation returns\r\n\t * `obj.`{@link Object#hashCode hashCode()}.\r\n\t */\r\n\t@Override\r\n\tpublic hashCode(obj: any): number {\r\n\t\tif (obj == null) {\r\n\t\t\treturn 0;\r\n\t\t} else if (typeof obj === \"string\" || typeof obj === \"number\") {\r\n\t\t\treturn MurmurHash.hashCode([obj]);\r\n\t\t} else {\r\n\t\t\treturn ObjectEqualityComparator.INSTANCE.hashCode(obj as Equatable);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation relies on object equality. If both objects are\r\n\t * `undefined` or `null`, this method returns `true`. Otherwise if only\r\n\t * `a` is `undefined` or `null`, this method returns `false`. Otherwise,\r\n\t * this method returns the result of\r\n\t * `a.`{@link Object#equals equals}`(b)`.\r\n\t */\r\n\t@Override\r\n\tpublic equals(a: any, b: any): boolean {\r\n\t\tif (a == null) {\r\n\t\t\treturn b == null;\r\n\t\t} else if (typeof a === \"string\" || typeof a === \"number\") {\r\n\t\t\treturn a === b;\r\n\t\t} else {\r\n\t\t\treturn ObjectEqualityComparator.INSTANCE.equals(a as Equatable, b as Equatable);\r\n\t\t}\r\n\t}\r\n}\r\n"]}