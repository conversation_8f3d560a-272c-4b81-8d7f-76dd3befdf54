{"version": 3, "file": "Array2DHashSet.js", "sourceRoot": "", "sources": ["../../../src/misc/Array2DHashSet.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,iCAAiC;AACjC,2EAAwE;AAExE,8CAA8E;AAG9E,6CAA0C;AAE1C,wEAAwE;AAExE,wFAAwF;AACxF,6CAA6C;AAC7C,sEAAsE;AAEtE,MAAM,eAAe,GAAW,EAAE,CAAC,CAAC,qBAAqB;AACzD,MAAM,WAAW,GAAW,IAAI,CAAC;AAEjC,MAAa,cAAc;IAa1B,YACC,eAA2D,EAC3D,kBAA0B,eAAe;QAT1C,+BAA+B;QACrB,MAAC,GAAW,CAAC,CAAC;QAEd,cAAS,GAAW,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,WAAW,CAAC,CAAC,CAAC,iBAAiB;QAQzF,IAAI,eAAe,YAAY,cAAc,EAAE;YAC9C,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7B,IAAI,MAAM,EAAE;oBACX,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAClC;aACD;YAED,IAAI,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;SAC3C;aAAM;YACN,IAAI,CAAC,UAAU,GAAG,eAAe,IAAI,qDAAyB,CAAC,QAAQ,CAAC;YACxE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;SACnD;IACF,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,CAAI;QACnB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;SACd;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAES,YAAY,CAAC,CAAI;QAC1B,IAAI,CAAC,GAAW,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE7B,aAAa;QACb,IAAI,CAAC,MAAM,EAAE;YACZ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YACzB,IAAI,CAAC,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,CAAC;SACT;QAED,wBAAwB;QACxB,KAAK,IAAI,QAAQ,IAAI,MAAM,EAAE;YAC5B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE;gBACxC,OAAO,QAAQ,CAAC,CAAC,uBAAuB;aACxC;SACD;QAED,qCAAqC;QACrC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,EAAE,CAAC;QACT,OAAO,CAAC,CAAC;IACV,CAAC;IAEM,GAAG,CAAC,CAAI;QACd,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,OAAO,CAAC,CAAC;SACT;QACD,IAAI,CAAC,GAAW,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,EAAE;YACZ,YAAY;YACZ,OAAO,SAAS,CAAC;SACjB;QAED,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;YACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjC,OAAO,CAAC,CAAC;aACT;SACD;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAES,SAAS,CAAC,CAAI;QACvB,IAAI,IAAI,GAAW,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAW,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,4BAA4B;QAC9E,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,GAAW,uBAAU,CAAC,UAAU,EAAE,CAAC;QAC3C,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,SAAS;aACT;YACD,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,CAAC,IAAI,IAAI,EAAE;oBACd,MAAM;iBACN;gBACD,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5D;SACD;QAED,IAAI,GAAG,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,KAAK,IAAI,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;QACD,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,EAAE;YACnC,OAAO,KAAK,CAAC;SACb;QACD,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YACzB,OAAO,KAAK,CAAC;SACb;QACD,IAAI,IAAI,GAAY,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACb,CAAC;IAES,MAAM;QACf,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,WAAW,GAAW,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,IAAI,QAAQ,GAA2B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;QACzD,qEAAqE;QACnE,8BAA8B;QAC9B,IAAI,OAAO,GAAW,IAAI,CAAC,IAAI,CAAC;QAChC,KAAK,IAAI,MAAM,IAAI,GAAG,EAAE;YACvB,IAAI,CAAC,MAAM,EAAE;gBACZ,SAAS;aACT;YAED,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,CAAC,GAAW,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,SAAS,GAAoB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,SAAS,EAAE;oBACf,SAAS,GAAG,EAAE,CAAC;oBACf,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;iBAC5B;gBAED,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAClB;SACD;QAED,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC;IAC5B,CAAC;IAGM,GAAG,CAAC,CAAI;QACd,IAAI,QAAQ,GAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO,QAAQ,KAAK,CAAC,CAAC;IACvB,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,CAAC,CAAC;IACf,CAAC;IAGD,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAGM,QAAQ,CAAC,CAAM;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAEM,YAAY,CAAW,GAAM;QACnC,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAC9B,CAAC;IAGM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QACxB,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAGM,OAAO;QACb,MAAM,CAAC,GAAG,IAAI,KAAK,CAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,kEAAkE;QAClE,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,oCAAoC;QACvD,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,SAAS;aACT;YAED,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,CAAC,IAAI,IAAI,EAAE;oBACd,MAAM;iBACN;gBACD,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;aACX;SACD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAGM,WAAW,CAAC,UAA6B;QAC/C,IAAI,UAAU,YAAY,cAAc,EAAE;YACzC,IAAI,CAAC,GAAG,UAAsC,CAAC;YAC/C,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE;gBAC7B,IAAI,MAAM,IAAI,IAAI,EAAE;oBACnB,SAAS;iBACT;gBACD,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;oBACrB,IAAI,CAAC,IAAI,IAAI,EAAE;wBACd,MAAM;qBACN;oBACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;wBAC9C,OAAO,KAAK,CAAC;qBACb;iBACD;aACD;SACD;aACI;YACJ,KAAK,IAAI,CAAC,IAAI,UAAU,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC9C,OAAO,KAAK,CAAC;iBACb;aACD;SACD;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAGM,MAAM,CAAC,CAAc;QAC3B,IAAI,OAAO,GAAY,KAAK,CAAC;QAE7B,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;YAChB,IAAI,QAAQ,GAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,QAAQ,KAAK,CAAC,EAAE;gBACnB,OAAO,GAAG,IAAI,CAAC;aACf;SACD;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAGM,KAAK;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QACnD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,WAAW,CAAC,CAAC;IAC5D,CAAC;IAGM,QAAQ;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,KAAK,GAAY,IAAI,CAAC;QAC1B,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,SAAS;aACT;YACD,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,CAAC,IAAI,IAAI,EAAE;oBACd,MAAM;iBACN;gBACD,IAAI,KAAK,EAAE;oBACV,KAAK,GAAG,KAAK,CAAC;iBACd;qBAAM;oBACN,GAAG,IAAI,IAAI,CAAC;iBACZ;gBACD,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;aACpB;SACD;QACD,GAAG,IAAI,GAAG,CAAC;QACX,OAAO,GAAG,CAAC;IACZ,CAAC;IAEM,aAAa;QACnB,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,GAAG,IAAI,QAAQ,CAAC;gBAChB,SAAS;aACT;YACD,GAAG,IAAI,GAAG,CAAC;YACX,IAAI,KAAK,GAAY,IAAI,CAAC;YAC1B,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;gBACrB,IAAI,KAAK,EAAE;oBACV,KAAK,GAAG,KAAK,CAAC;iBACd;qBAAM;oBACN,GAAG,IAAI,GAAG,CAAC;iBACX;gBACD,IAAI,CAAC,IAAI,IAAI,EAAE;oBACd,GAAG,IAAI,GAAG,CAAC;iBACX;qBAAM;oBACN,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;iBACpB;aACD;YACD,GAAG,IAAI,KAAK,CAAC;SACb;QACD,OAAO,GAAG,CAAC;IACZ,CAAC;IAED;;;;;;;;;;;;OAYG;IAEO,aAAa,CAAC,CAAM;QAC7B,OAAO,CAAM,CAAC;IACf,CAAC;IAED;;;;;OAKG;IAEO,aAAa,CAAC,QAAgB;QACvC,OAAO,IAAI,KAAK,CAAM,QAAQ,CAAC,CAAC;IACjC,CAAC;CACD;AAxVA;IADC,oBAAO;kDACoC;AAiG5C;IADC,qBAAQ;8CAiBR;AAGD;IADC,qBAAQ;4CAaR;AAgCD;IADC,qBAAQ;yCAIR;AAGD;IADC,qBAAQ;0CAGR;AAGD;IADC,qBAAQ;6CAGR;AAGD;IADC,qBAAQ;8CAGR;AAED;IAAqB,WAAA,qBAAQ,CAAA;kDAM5B;AAGD;IADC,qBAAQ;6BACA,MAAM,CAAC,QAAQ,OAEvB;AAGD;IADC,qBAAQ;6CAmBR;AAGD;IADC,qBAAQ;iDA0BR;AAGD;IADC,qBAAQ;4CAWR;AAGD;IADC,qBAAQ;2CAKR;AAGD;IADC,qBAAQ;8CA0BR;AA0CD;IADC,6BAAgB,CAAC,WAAW,CAAC;mDAG7B;AASD;IADC,6BAAgB,CAAC,WAAW,CAAC;mDAG7B;AAzVF,wCA0VC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-03T02:09:41.7434086-07:00\r\n\r\nimport * as assert from \"assert\";\r\nimport { DefaultEqualityComparator } from \"./DefaultEqualityComparator\";\r\nimport { EqualityComparator } from \"./EqualityComparator\";\r\nimport { NotNull, Nullable, Override, SuppressWarnings } from \"../Decorators\";\r\nimport { JavaCollection, JavaSet } from \"./Stubs\";\r\nimport { ObjectEqualityComparator } from \"./ObjectEqualityComparator\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\n\r\n/** {@link Set} implementation with closed hashing (open addressing). */\r\n\r\n// NOTE:  JavaScript's Set interface has on significant different diffrence from Java's:\r\n// \t\t  e.g. the return type of add() differs!\r\n//        For this reason I've commented tweaked the implements clause\r\n\r\nconst INITAL_CAPACITY: number = 16; // must be power of 2\r\nconst LOAD_FACTOR: number = 0.75;\r\n\r\nexport class Array2DHashSet<T extends { toString(): string; }> implements JavaSet<T> {\r\n\t@NotNull\r\n\tprotected comparator: EqualityComparator<T>;\r\n\r\n\tprotected buckets: Array<T[] | undefined>;\r\n\r\n\t/** How many elements in set */\r\n\tprotected n: number = 0;\r\n\r\n\tprotected threshold: number = Math.floor(INITAL_CAPACITY * LOAD_FACTOR); // when to expand\r\n\r\n\tconstructor(comparator?: EqualityComparator<T>, initialCapacity?: number);\r\n\tconstructor(set: Array2DHashSet<T>);\r\n\tconstructor(\r\n\t\tcomparatorOrSet?: EqualityComparator<T> | Array2DHashSet<T>,\r\n\t\tinitialCapacity: number = INITAL_CAPACITY) {\r\n\r\n\t\tif (comparatorOrSet instanceof Array2DHashSet) {\r\n\t\t\tthis.comparator = comparatorOrSet.comparator;\r\n\t\t\tthis.buckets = comparatorOrSet.buckets.slice(0);\r\n\t\t\tfor (let i = 0; i < this.buckets.length; i++) {\r\n\t\t\t\tlet bucket = this.buckets[i];\r\n\t\t\t\tif (bucket) {\r\n\t\t\t\t\tthis.buckets[i] = bucket.slice(0);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.n = comparatorOrSet.n;\r\n\t\t\tthis.threshold = comparatorOrSet.threshold;\r\n\t\t} else {\r\n\t\t\tthis.comparator = comparatorOrSet || DefaultEqualityComparator.INSTANCE;\r\n\t\t\tthis.buckets = this.createBuckets(initialCapacity);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Add `o` to set if not there; return existing value if already\r\n\t * there. This method performs the same operation as {@link #add} aside from\r\n\t * the return value.\r\n\t */\r\n\tpublic getOrAdd(o: T): T {\r\n\t\tif (this.n > this.threshold) {\r\n\t\t\tthis.expand();\r\n\t\t}\r\n\t\treturn this.getOrAddImpl(o);\r\n\t}\r\n\r\n\tprotected getOrAddImpl(o: T): T {\r\n\t\tlet b: number = this.getBucket(o);\r\n\t\tlet bucket = this.buckets[b];\r\n\r\n\t\t// NEW BUCKET\r\n\t\tif (!bucket) {\r\n\t\t\tbucket = [o];\r\n\t\t\tthis.buckets[b] = bucket;\r\n\t\t\tthis.n++;\r\n\t\t\treturn o;\r\n\t\t}\r\n\r\n\t\t// LOOK FOR IT IN BUCKET\r\n\t\tfor (let existing of bucket) {\r\n\t\t\tif (this.comparator.equals(existing, o)) {\r\n\t\t\t\treturn existing; // found existing, quit\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// FULL BUCKET, expand and add to end\r\n\t\tbucket.push(o);\r\n\t\tthis.n++;\r\n\t\treturn o;\r\n\t}\r\n\r\n\tpublic get(o: T): T | undefined {\r\n\t\tif (o == null) {\r\n\t\t\treturn o;\r\n\t\t}\r\n\t\tlet b: number = this.getBucket(o);\r\n\t\tlet bucket = this.buckets[b];\r\n\t\tif (!bucket) {\r\n\t\t\t// no bucket\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\tfor (let e of bucket) {\r\n\t\t\tif (this.comparator.equals(e, o)) {\r\n\t\t\t\treturn e;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tprotected getBucket(o: T): number {\r\n\t\tlet hash: number = this.comparator.hashCode(o);\r\n\t\tlet b: number = hash & (this.buckets.length - 1); // assumes len is power of 2\r\n\t\treturn b;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hash: number = MurmurHash.initialize();\r\n\t\tfor (let bucket of this.buckets) {\r\n\t\t\tif (bucket == null) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tfor (let o of bucket) {\r\n\t\t\t\tif (o == null) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\thash = MurmurHash.update(hash, this.comparator.hashCode(o));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\thash = MurmurHash.finish(hash, this.size);\r\n\t\treturn hash;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (o === this) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\tif (!(o instanceof Array2DHashSet)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tif (o.size !== this.size) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tlet same: boolean = this.containsAll(o);\r\n\t\treturn same;\r\n\t}\r\n\r\n\tprotected expand(): void {\r\n\t\tlet old = this.buckets;\r\n\t\tlet newCapacity: number = this.buckets.length * 2;\r\n\t\tlet newTable: Array<T[] | undefined> = this.createBuckets(newCapacity);\r\n\t\tthis.buckets = newTable;\r\n\t\tthis.threshold = Math.floor(newCapacity * LOAD_FACTOR);\r\n//\t\tSystem.out.println(\"new size=\"+newCapacity+\", thres=\"+threshold);\r\n\t\t// rehash all existing entries\r\n\t\tlet oldSize: number = this.size;\r\n\t\tfor (let bucket of old) {\r\n\t\t\tif (!bucket) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let o of bucket) {\r\n\t\t\t\tlet b: number = this.getBucket(o);\r\n\t\t\t\tlet newBucket: T[] | undefined = this.buckets[b];\r\n\t\t\t\tif (!newBucket) {\r\n\t\t\t\t\tnewBucket = [];\r\n\t\t\t\t\tthis.buckets[b] = newBucket;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnewBucket.push(o);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tassert(this.n === oldSize);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic add(t: T): boolean {\r\n\t\tlet existing: T = this.getOrAdd(t);\r\n\t\treturn existing === t;\r\n\t}\r\n\r\n\t@Override\r\n\tget size(): number {\r\n\t\treturn this.n;\r\n\t}\r\n\r\n\t@Override\r\n\tget isEmpty(): boolean {\r\n\t\treturn this.n === 0;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic contains(o: any): boolean {\r\n\t\treturn this.containsFast(this.asElementType(o));\r\n\t}\r\n\r\n\tpublic containsFast(@Nullable obj: T): boolean {\r\n\t\tif (obj == null) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.get(obj) != null;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic *[Symbol.iterator](): IterableIterator<T> {\r\n\t\tyield* this.toArray();\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toArray(): T[] {\r\n\t\tconst a = new Array<T>(this.size);\r\n\r\n\t\t// Copy elements from the nested arrays into the destination array\r\n\t\tlet i: number = 0; // Position within destination array\r\n\t\tfor (let bucket of this.buckets) {\r\n\t\t\tif (bucket == null) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tfor (let o of bucket) {\r\n\t\t\t\tif (o == null) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\ta[i++] = o;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn a;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic containsAll(collection: JavaCollection<T>): boolean {\r\n\t\tif (collection instanceof Array2DHashSet) {\r\n\t\t\tlet s = collection as any as Array2DHashSet<T>;\r\n\t\t\tfor (let bucket of s.buckets) {\r\n\t\t\t\tif (bucket == null) {\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\tfor (let o of bucket) {\r\n\t\t\t\t\tif (o == null) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.containsFast(this.asElementType(o))) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tfor (let o of collection) {\r\n\t\t\t\tif (!this.containsFast(this.asElementType(o))) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic addAll(c: Iterable<T>): boolean {\r\n\t\tlet changed: boolean = false;\r\n\r\n\t\tfor (let o of c) {\r\n\t\t\tlet existing: T = this.getOrAdd(o);\r\n\t\t\tif (existing !== o) {\r\n\t\t\t\tchanged = true;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn changed;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic clear(): void {\r\n\t\tthis.buckets = this.createBuckets(INITAL_CAPACITY);\r\n\t\tthis.n = 0;\r\n\t\tthis.threshold = Math.floor(INITAL_CAPACITY * LOAD_FACTOR);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tif (this.size === 0) {\r\n\t\t\treturn \"{}\";\r\n\t\t}\r\n\r\n\t\tlet buf = \"{\";\r\n\t\tlet first: boolean = true;\r\n\t\tfor (let bucket of this.buckets) {\r\n\t\t\tif (bucket == null) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tfor (let o of bucket) {\r\n\t\t\t\tif (o == null) {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tif (first) {\r\n\t\t\t\t\tfirst = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbuf += \", \";\r\n\t\t\t\t}\r\n\t\t\t\tbuf += o.toString();\r\n\t\t\t}\r\n\t\t}\r\n\t\tbuf += \"}\";\r\n\t\treturn buf;\r\n\t}\r\n\r\n\tpublic toTableString(): string {\r\n\t\tlet buf = \"\";\r\n\t\tfor (let bucket of this.buckets) {\r\n\t\t\tif (bucket == null) {\r\n\t\t\t\tbuf += \"null\\n\";\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tbuf += \"[\";\r\n\t\t\tlet first: boolean = true;\r\n\t\t\tfor (let o of bucket) {\r\n\t\t\t\tif (first) {\r\n\t\t\t\t\tfirst = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbuf += \" \";\r\n\t\t\t\t}\r\n\t\t\t\tif (o == null) {\r\n\t\t\t\t\tbuf += \"_\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbuf += o.toString();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tbuf += \"]\\n\";\r\n\t\t}\r\n\t\treturn buf;\r\n\t}\r\n\r\n\t/**\r\n\t * Return `o` as an instance of the element type `T`. If\r\n\t * `o` is non-undefined but known to not be an instance of `T`, this\r\n\t * method returns `undefined`. The base implementation does not perform any\r\n\t * type checks; override this method to provide strong type checks for the\r\n\t * {@link #contains} and {@link #remove} methods to ensure the arguments to\r\n\t * the {@link EqualityComparator} for the set always have the expected\r\n\t * types.\r\n\t *\r\n\t * @param o the object to try and cast to the element type of the set\r\n\t * @returns `o` if it could be an instance of `T`, otherwise\r\n\t * `undefined`.\r\n\t */\r\n\t@SuppressWarnings(\"unchecked\")\r\n\tprotected asElementType(o: any): T {\r\n\t\treturn o as T;\r\n\t}\r\n\r\n\t/**\r\n\t * Return an array of `T[]` with length `capacity`.\r\n\t *\r\n\t * @param capacity the length of the array to return\r\n\t * @returns the newly constructed array\r\n\t */\r\n\t@SuppressWarnings(\"unchecked\")\r\n\tprotected createBuckets(capacity: number): Array<T[] | undefined> {\r\n\t\treturn new Array<T[]>(capacity);\r\n\t}\r\n}\r\n"]}