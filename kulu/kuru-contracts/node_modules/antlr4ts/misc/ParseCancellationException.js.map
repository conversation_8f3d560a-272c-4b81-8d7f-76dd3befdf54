{"version": 3, "file": "ParseCancellationException.js", "sourceRoot": "", "sources": ["../../../src/misc/ParseCancellationException.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;;;;;GAOG;AACH,MAAa,0BAA2B,SAAQ,KAAK;IAIpD,YAAmB,KAAY;QAC9B,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QADH,UAAK,GAAL,KAAK,CAAO;QAE9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEM,QAAQ;QACd,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;CACD;AAZD,gEAYC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:42.5447085-07:00\r\n\r\n/**\r\n * This exception is thrown to cancel a parsing operation. This exception does\r\n * not extend {@link RecognitionException}, allowing it to bypass the standard\r\n * error recovery mechanisms. {@link BailErrorStrategy} throws this exception in\r\n * response to a parse error.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ParseCancellationException extends Error {\r\n\t// private static serialVersionUID: number =  -3529552099366979683L;\r\n\tpublic readonly stack?: string;\r\n\r\n\tconstructor(public cause: Error) {\r\n\t\tsuper(cause.message);\r\n\t\tthis.stack = cause.stack;\r\n\t}\r\n\r\n\tpublic getCause(): Error {\r\n\t\treturn this.cause;\r\n\t}\r\n}\r\n"]}