{"version": 3, "file": "Character.js", "sourceRoot": "", "sources": ["../../../src/misc/Character.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,SAAgB,eAAe,CAAC,EAAU;IACzC,OAAO,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,CAAC;AACrC,CAAC;AAFD,0CAEC;AAED,SAAgB,cAAc,CAAC,EAAU;IACxC,OAAO,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,CAAC;AACrC,CAAC;AAFD,wCAEC;AAED,SAAgB,wBAAwB,CAAC,EAAU;IAClD,OAAO,EAAE,IAAI,OAAO,CAAC;AACtB,CAAC;AAFD,4DAEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport function isHighSurrogate(ch: number): boolean {\r\n\treturn ch >= 0xD800 && ch <= 0xDBFF;\r\n}\r\n\r\nexport function isLowSurrogate(ch: number): boolean {\r\n\treturn ch >= 0xDC00 && ch <= 0xDFFF;\r\n}\r\n\r\nexport function isSupplementaryCodePoint(ch: number): boolean {\r\n\treturn ch >= 0x10000;\r\n}\r\n"]}