{"version": 3, "file": "Array2DHashMap.js", "sourceRoot": "", "sources": ["../../../src/misc/Array2DHashMap.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,qDAAkD;AASlD,MAAM,wBAAwB;IAG7B,YAAY,aAAoC;QAC/C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACpC,CAAC;IAEM,QAAQ,CAAC,GAAiB;QAChC,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEM,MAAM,CAAC,CAAe,EAAE,CAAe;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;CACD;AAED,MAAa,cAAc;IAK1B,YAAY,WAAyD;QACpE,IAAI,WAAW,YAAY,cAAc,EAAE;YAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,+BAAc,CAAe,WAAW,CAAC,YAAY,CAAC,CAAC;SAC/E;aAAM;YACN,IAAI,CAAC,YAAY,GAAG,IAAI,+BAAc,CAAe,IAAI,wBAAwB,CAAO,WAAW,CAAC,CAAC,CAAC;SACtG;IACF,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEM,WAAW,CAAC,GAAM;QACxB,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEM,GAAG,CAAC,GAAM;QAChB,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE;YACZ,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,MAAM,CAAC,KAAK,CAAC;IACrB,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IAClC,CAAC;IAEM,GAAG,CAAC,GAAM,EAAE,KAAQ;QAC1B,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QACpD,IAAI,MAAqB,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;SACtC;aAAM;YACN,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;YACvB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,WAAW,CAAC,GAAM,EAAE,KAAQ;QAClC,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QACpD,IAAI,MAAqB,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;SACtC;aAAM;YACN,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;SACvB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAC/B,CAAC;IAEM,QAAQ;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAEM,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,EAAE;YACnC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IACjD,CAAC;CACD;AA1ED,wCA0EC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nimport { Array2DHashSet } from \"./Array2DHashSet\";\r\nimport { DefaultEqualityComparator } from \"./DefaultEqualityComparator\";\r\nimport { EqualityComparator } from \"./EqualityComparator\";\r\nimport { Equatable, JavaCollection, JavaMap, JavaSet } from \"./Stubs\";\r\n\r\n// Since `Array2DHashMap` is implemented on top of `Array2DHashSet`, we defined a bucket type which can store a\r\n// key-value pair. The value is optional since looking up values in the map by a key only needs to include the key.\r\ninterface Bucket<K, V> { key: K; value?: V; }\r\n\r\nclass MapKeyEqualityComparator<K, V> implements EqualityComparator<Bucket<K, V>> {\r\n\tprivate readonly keyComparator: EqualityComparator<K>;\r\n\r\n\tconstructor(keyComparator: EqualityComparator<K>) {\r\n\t\tthis.keyComparator = keyComparator;\r\n\t}\r\n\r\n\tpublic hashCode(obj: Bucket<K, V>): number {\r\n\t\treturn this.keyComparator.hashCode(obj.key);\r\n\t}\r\n\r\n\tpublic equals(a: Bucket<K, V>, b: Bucket<K, V>): boolean {\r\n\t\treturn this.keyComparator.equals(a.key, b.key);\r\n\t}\r\n}\r\n\r\nexport class Array2DHashMap<K, V> implements JavaMap<K, V> {\r\n\tprivate backingStore: Array2DHashSet<Bucket<K, V>>;\r\n\r\n\tconstructor(keyComparer: EqualityComparator<K>);\r\n\tconstructor(map: Array2DHashMap<K, V>);\r\n\tconstructor(keyComparer: EqualityComparator<K> | Array2DHashMap<K, V>) {\r\n\t\tif (keyComparer instanceof Array2DHashMap) {\r\n\t\t\tthis.backingStore = new Array2DHashSet<Bucket<K, V>>(keyComparer.backingStore);\r\n\t\t} else {\r\n\t\t\tthis.backingStore = new Array2DHashSet<Bucket<K, V>>(new MapKeyEqualityComparator<K, V>(keyComparer));\r\n\t\t}\r\n\t}\r\n\r\n\tpublic clear(): void {\r\n\t\tthis.backingStore.clear();\r\n\t}\r\n\r\n\tpublic containsKey(key: K): boolean {\r\n\t\treturn this.backingStore.contains({ key });\r\n\t}\r\n\r\n\tpublic get(key: K): V | undefined {\r\n\t\tlet bucket = this.backingStore.get({ key });\r\n\t\tif (!bucket) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn bucket.value;\r\n\t}\r\n\r\n\tget isEmpty(): boolean {\r\n\t\treturn this.backingStore.isEmpty;\r\n\t}\r\n\r\n\tpublic put(key: K, value: V): V | undefined {\r\n\t\tlet element = this.backingStore.get({ key, value });\r\n\t\tlet result: V | undefined;\r\n\t\tif (!element) {\r\n\t\t\tthis.backingStore.add({ key, value });\r\n\t\t} else {\r\n\t\t\tresult = element.value;\r\n\t\t\telement.value = value;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic putIfAbsent(key: K, value: V): V | undefined {\r\n\t\tlet element = this.backingStore.get({ key, value });\r\n\t\tlet result: V | undefined;\r\n\t\tif (!element) {\r\n\t\t\tthis.backingStore.add({ key, value });\r\n\t\t} else {\r\n\t\t\tresult = element.value;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tget size(): number {\r\n\t\treturn this.backingStore.size;\r\n\t}\r\n\r\n\tpublic hashCode(): number {\r\n\t\treturn this.backingStore.hashCode();\r\n\t}\r\n\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (!(o instanceof Array2DHashMap)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn this.backingStore.equals(o.backingStore);\r\n\t}\r\n}\r\n"]}