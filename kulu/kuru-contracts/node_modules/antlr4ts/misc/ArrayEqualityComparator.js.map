{"version": 3, "file": "ArrayEqualityComparator.js", "sourceRoot": "", "sources": ["../../../src/misc/ArrayEqualityComparator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAIH,8CAAyC;AAEzC,6CAA0C;AAC1C,yEAAsE;AAEtE;;;;;GAKG;AACH,MAAa,uBAAuB;IAGnC;;;;;OAKG;IAEI,QAAQ,CAAC,GAAgB;QAC/B,IAAI,GAAG,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,CAAC;SACT;QAED,OAAO,uBAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;OAQG;IAEI,MAAM,CAAC,CAAc,EAAE,CAAc;QAC3C,IAAI,CAAC,IAAI,IAAI,EAAE;YACd,OAAO,CAAC,IAAI,IAAI,CAAC;SACjB;aAAM,IAAI,CAAC,IAAI,IAAI,EAAE;YACrB,OAAO,KAAK,CAAC;SACb;QAED,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;YAC1B,OAAO,KAAK,CAAC;SACb;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,mDAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1D,OAAO,KAAK,CAAC;aACb;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;;AA7CsB,gCAAQ,GAA4B,IAAI,uBAAuB,EAAE,CAAC;AASzF;IADC,qBAAQ;uDAOR;AAYD;IADC,qBAAQ;qDAmBR;AA9CF,0DAgDC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-03T02:09:42.2127260-07:00\r\nimport { EqualityComparator } from \"./EqualityComparator\";\r\nimport { Override } from \"../Decorators\";\r\nimport { Equatable } from \"./Stubs\";\r\nimport { MurmurHash } from \"./MurmurHash\";\r\nimport { ObjectEqualityComparator } from \"./ObjectEqualityComparator\";\r\n\r\n/**\r\n * This default implementation of {@link EqualityComparator} uses object equality\r\n * for comparisons by calling {@link Object#hashCode} and {@link Object#equals}.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class ArrayEqualityComparator implements EqualityComparator<Equatable[]> {\r\n\tpublic static readonly INSTANCE: ArrayEqualityComparator = new ArrayEqualityComparator();\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation returns\r\n\t * `obj.`{@link Object#hashCode hashCode()}.\r\n\t */\r\n\t@Override\r\n\tpublic hashCode(obj: Equatable[]): number {\r\n\t\tif (obj == null) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\r\n\t\treturn MurmurHash.hashCode(obj, 0);\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This implementation relies on object equality. If both objects are\r\n\t * `undefined`, this method returns `true`. Otherwise if only\r\n\t * `a` is `undefined`, this method returns `false`. Otherwise,\r\n\t * this method returns the result of\r\n\t * `a.`{@link Object#equals equals}`(b)`.\r\n\t */\r\n\t@Override\r\n\tpublic equals(a: Equatable[], b: Equatable[]): boolean {\r\n\t\tif (a == null) {\r\n\t\t\treturn b == null;\r\n\t\t} else if (b == null) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (a.length !== b.length) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < a.length; i++) {\r\n\t\t\tif (!ObjectEqualityComparator.INSTANCE.equals(a[i], b[i])) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n}\r\n"]}