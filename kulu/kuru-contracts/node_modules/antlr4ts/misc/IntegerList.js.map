{"version": 3, "file": "IntegerList.js", "sourceRoot": "", "sources": ["../../../src/misc/IntegerList.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,qCAAkC;AAClC,8CAAkD;AAGlD,MAAM,UAAU,GAAe,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAEjD,MAAM,YAAY,GAAW,CAAC,CAAC;AAC/B,MAAM,cAAc,GAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAE3D;;;GAGG;AACH,MAAa,WAAW;IAMvB,YAAY,GAA6C;QACxD,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;YACxB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACf;aAAM,IAAI,GAAG,YAAY,WAAW,EAAE;YACtC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;SACvB;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACnC,IAAI,GAAG,KAAK,CAAC,EAAE;gBACd,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;gBACxB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aACf;iBAAM;gBACN,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aACf;SACD;aAAM;YACN,0BAA0B;YAC1B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;YACxB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAChB;SACD;IACF,CAAC;IAEM,GAAG,CAAC,KAAa;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,IAAqD;QAClE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC;SAC1B;aAAM,IAAI,IAAI,YAAY,WAAW,EAAE;YACvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;SACzB;aAAM;YACN,iCAAiC;YACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,OAAO,GAAW,CAAC,CAAC;YACxB,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;gBACtC,OAAO,EAAE,CAAC;aACV;YAED,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;SACxB;IACF,CAAC;IAEM,GAAG,CAAC,KAAa;QACvB,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;YACrC,MAAM,UAAU,EAAE,CAAC;SACnB;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEM,QAAQ,CAAC,KAAa;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;gBAC5B,OAAO,IAAI,CAAC;aACZ;SACD;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAa,EAAE,KAAa;QACtC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;YACrC,MAAM,UAAU,EAAE,CAAC;SACnB;QAED,IAAI,QAAQ,GAAW,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC1B,OAAO,QAAQ,CAAC;IACjB,CAAC;IAEM,QAAQ,CAAC,KAAa;QAC5B,IAAI,KAAK,GAAW,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,WAAW,CAAC,SAAiB,EAAE,OAAe;QACpD,IAAI,SAAS,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE;YACnF,MAAM,UAAU,EAAE,CAAC;SACnB;QAED,IAAI,SAAS,GAAG,OAAO,EAAE;YACxB,MAAM,UAAU,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEM,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE;YACrC,OAAO;SACP;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IAChB,CAAC;IAEM,OAAO;QACb,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;YACrB,OAAO,EAAE,CAAC;SACV;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACvD,CAAC;IAEM,IAAI;QACV,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IAEI,MAAM,CAAC,CAAM;QACnB,IAAI,CAAC,KAAK,IAAI,EAAE;YACf,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,CAAC,CAAC,CAAC,YAAY,WAAW,CAAC,EAAE;YAChC,OAAO,KAAK,CAAC;SACb;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE;YAC3B,OAAO,KAAK,CAAC;SACb;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBACjC,OAAO,KAAK,CAAC;aACb;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;OAQG;IAEI,QAAQ;QACd,IAAI,QAAQ,GAAW,CAAC,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,QAAQ,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACzC;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;OAEG;IAEI,QAAQ;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,YAAY,CAAC,GAAW,EAAE,SAAkB,EAAE,OAAgB;QACpE,IAAI,SAAS,KAAK,SAAS,EAAE;YAC5B,SAAS,GAAG,CAAC,CAAC;SACd;QAED,IAAI,OAAO,KAAK,SAAS,EAAE;YAC1B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;SACrB;QAED,IAAI,SAAS,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE;YACnF,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,IAAI,SAAS,GAAG,OAAO,EAAE;YACxB,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,OAAO,eAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAEO,cAAc,CAAC,QAAgB;QACtC,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,cAAc,EAAE;YAC9C,MAAM,IAAI,UAAU,EAAE,CAAC;SACvB;QAED,IAAI,SAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,SAAS,GAAG,YAAY,CAAC;SACzB;aAAM;YACN,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;SAC9B;QAED,OAAO,SAAS,GAAG,QAAQ,EAAE;YAC5B,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;YAC1B,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,cAAc,EAAE;gBAChD,SAAS,GAAG,cAAc,CAAC;aAC3B;SACD;QAED,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QACpC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,WAAW;QACjB,qFAAqF;QACrF,IAAI,WAAW,GAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,OAAO,EAAE;gBAC1C,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;gBACnC,SAAS,EAAE,CAAC;gBACZ,SAAS;aACT;YAED,0EAA0E;YAC1E,IAAI,CAAC,2BAA2B,EAAE;gBACjC,IAAI,cAAc,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC3D,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBACnC,WAAW,GAAG,cAAc,CAAC;gBAC7B,2BAA2B,GAAG,IAAI,CAAC;aACnC;YAED,iFAAiF;YACjF,IAAI,IAAI,GAAG,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAC3C,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChD,SAAS,IAAI,CAAC,CAAC;SACf;QACD,OAAO,WAAW,CAAC;IACpB,CAAC;IAEO,aAAa;QACpB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAzSA;IADC,oBAAO;0CACkB;AAkK1B;IADC,qBAAQ;yCAqBR;AAYD;IADC,qBAAQ;2CAQR;AAMD;IADC,qBAAQ;2CAGR;AAnNF,kCA2SC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:40.5099429-07:00\r\n\r\nimport { Arrays } from \"./Arrays\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { JavaCollection } from \"./Stubs\";\r\n\r\nconst EMPTY_DATA: Int32Array = new Int32Array(0);\r\n\r\nconst INITIAL_SIZE: number = 4;\r\nconst MAX_ARRAY_SIZE: number = (((1 << 31) >>> 0) - 1) - 8;\r\n\r\n/**\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class IntegerList {\r\n\t@NotNull\r\n\tprivate _data: Int32Array;\r\n\r\n\tprivate _size: number;\r\n\r\n\tconstructor(arg?: number | IntegerList | Iterable<number>) {\r\n\t\tif (!arg) {\r\n\t\t\tthis._data = EMPTY_DATA;\r\n\t\t\tthis._size = 0;\r\n\t\t} else if (arg instanceof IntegerList) {\r\n\t\t\tthis._data = arg._data.slice(0);\r\n\t\t\tthis._size = arg._size;\r\n\t\t} else if (typeof arg === \"number\") {\r\n\t\t\tif (arg === 0) {\r\n\t\t\t\tthis._data = EMPTY_DATA;\r\n\t\t\t\tthis._size = 0;\r\n\t\t\t} else {\r\n\t\t\t\tthis._data = new Int32Array(arg);\r\n\t\t\t\tthis._size = 0;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// arg is Iterable<number>\r\n\t\t\tthis._data = EMPTY_DATA;\r\n\t\t\tthis._size = 0;\r\n\t\t\tfor (let value of arg) {\r\n\t\t\t\tthis.add(value);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tpublic add(value: number): void {\r\n\t\tif (this._data.length === this._size) {\r\n\t\t\tthis.ensureCapacity(this._size + 1);\r\n\t\t}\r\n\r\n\t\tthis._data[this._size] = value;\r\n\t\tthis._size++;\r\n\t}\r\n\r\n\tpublic addAll(list: number[] | IntegerList | JavaCollection<number>): void {\r\n\t\tif (Array.isArray(list)) {\r\n\t\t\tthis.ensureCapacity(this._size + list.length);\r\n\t\t\tthis._data.subarray(this._size, this._size + list.length).set(list);\r\n\t\t\tthis._size += list.length;\r\n\t\t} else if (list instanceof IntegerList) {\r\n\t\t\tthis.ensureCapacity(this._size + list._size);\r\n\t\t\tthis._data.subarray(this._size, this._size + list.size).set(list._data);\r\n\t\t\tthis._size += list._size;\r\n\t\t} else {\r\n\t\t\t// list is JavaCollection<number>\r\n\t\t\tthis.ensureCapacity(this._size + list.size);\r\n\t\t\tlet current: number = 0;\r\n\t\t\tfor (let xi of list) {\r\n\t\t\t\tthis._data[this._size + current] = xi;\r\n\t\t\t\tcurrent++;\r\n\t\t\t}\r\n\r\n\t\t\tthis._size += list.size;\r\n\t\t}\r\n\t}\r\n\r\n\tpublic get(index: number): number {\r\n\t\tif (index < 0 || index >= this._size) {\r\n\t\t\tthrow RangeError();\r\n\t\t}\r\n\r\n\t\treturn this._data[index];\r\n\t}\r\n\r\n\tpublic contains(value: number): boolean {\r\n\t\tfor (let i = 0; i < this._size; i++) {\r\n\t\t\tif (this._data[i] === value) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\tpublic set(index: number, value: number): number {\r\n\t\tif (index < 0 || index >= this._size) {\r\n\t\t\tthrow RangeError();\r\n\t\t}\r\n\r\n\t\tlet previous: number = this._data[index];\r\n\t\tthis._data[index] = value;\r\n\t\treturn previous;\r\n\t}\r\n\r\n\tpublic removeAt(index: number): number {\r\n\t\tlet value: number = this.get(index);\r\n\t\tthis._data.copyWithin(index, index + 1, this._size);\r\n\t\tthis._data[this._size - 1] = 0;\r\n\t\tthis._size--;\r\n\t\treturn value;\r\n\t}\r\n\r\n\tpublic removeRange(fromIndex: number, toIndex: number): void {\r\n\t\tif (fromIndex < 0 || toIndex < 0 || fromIndex > this._size || toIndex > this._size) {\r\n\t\t\tthrow RangeError();\r\n\t\t}\r\n\r\n\t\tif (fromIndex > toIndex) {\r\n\t\t\tthrow RangeError();\r\n\t\t}\r\n\r\n\t\tthis._data.copyWithin(toIndex, fromIndex, this._size);\r\n\t\tthis._data.fill(0, this._size - (toIndex - fromIndex), this._size);\r\n\t\tthis._size -= (toIndex - fromIndex);\r\n\t}\r\n\r\n\tget isEmpty(): boolean {\r\n\t\treturn this._size === 0;\r\n\t}\r\n\r\n\tget size(): number {\r\n\t\treturn this._size;\r\n\t}\r\n\r\n\tpublic trimToSize(): void {\r\n\t\tif (this._data.length === this._size) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tthis._data = this._data.slice(0, this._size);\r\n\t}\r\n\r\n\tpublic clear(): void {\r\n\t\tthis._data.fill(0, 0, this._size);\r\n\t\tthis._size = 0;\r\n\t}\r\n\r\n\tpublic toArray(): number[] {\r\n\t\tif (this._size === 0) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\treturn Array.from(this._data.subarray(0, this._size));\r\n\t}\r\n\r\n\tpublic sort(): void {\r\n\t\tthis._data.subarray(0, this._size).sort();\r\n\t}\r\n\r\n\t/**\r\n\t * Compares the specified object with this list for equality.  Returns\r\n\t * `true` if and only if the specified object is also an {@link IntegerList},\r\n\t * both lists have the same size, and all corresponding pairs of elements in\r\n\t * the two lists are equal.  In other words, two lists are defined to be\r\n\t * equal if they contain the same elements in the same order.\r\n\t *\r\n\t * This implementation first checks if the specified object is this\r\n\t * list. If so, it returns `true`; if not, it checks if the\r\n\t * specified object is an {@link IntegerList}. If not, it returns `false`;\r\n\t * if so, it checks the size of both lists. If the lists are not the same size,\r\n\t * it returns `false`; otherwise it iterates over both lists, comparing\r\n\t * corresponding pairs of elements.  If any comparison returns `false`,\r\n\t * this method returns `false`.\r\n\t *\r\n\t * @param o the object to be compared for equality with this list\r\n\t * @returns `true` if the specified object is equal to this list\r\n\t */\r\n\t@Override\r\n\tpublic equals(o: any): boolean {\r\n\t\tif (o === this) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tif (!(o instanceof IntegerList)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (this._size !== o._size) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < this._size; i++) {\r\n\t\t\tif (this._data[i] !== o._data[i]) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the hash code value for this list.\r\n\t *\r\n\t * This implementation uses exactly the code that is used to define the\r\n\t * list hash function in the documentation for the {@link List#hashCode}\r\n\t * method.\r\n\t *\r\n\t * @returns the hash code value for this list\r\n\t */\r\n\t@Override\r\n\tpublic hashCode(): number {\r\n\t\tlet hashCode: number = 1;\r\n\t\tfor (let i = 0; i < this._size; i++) {\r\n\t\t\thashCode = 31 * hashCode + this._data[i];\r\n\t\t}\r\n\r\n\t\treturn hashCode;\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a string representation of this list.\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this._data.toString();\r\n\t}\r\n\r\n\tpublic binarySearch(key: number, fromIndex?: number, toIndex?: number): number {\r\n\t\tif (fromIndex === undefined) {\r\n\t\t\tfromIndex = 0;\r\n\t\t}\r\n\r\n\t\tif (toIndex === undefined) {\r\n\t\t\ttoIndex = this._size;\r\n\t\t}\r\n\r\n\t\tif (fromIndex < 0 || toIndex < 0 || fromIndex > this._size || toIndex > this._size) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\tif (fromIndex > toIndex) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\treturn Arrays.binarySearch(this._data, key, fromIndex, toIndex);\r\n\t}\r\n\r\n\tprivate ensureCapacity(capacity: number): void {\r\n\t\tif (capacity < 0 || capacity > MAX_ARRAY_SIZE) {\r\n\t\t\tthrow new RangeError();\r\n\t\t}\r\n\r\n\t\tlet newLength: number;\r\n\t\tif (this._data.length === 0) {\r\n\t\t\tnewLength = INITIAL_SIZE;\r\n\t\t} else {\r\n\t\t\tnewLength = this._data.length;\r\n\t\t}\r\n\r\n\t\twhile (newLength < capacity) {\r\n\t\t\tnewLength = newLength * 2;\r\n\t\t\tif (newLength < 0 || newLength > MAX_ARRAY_SIZE) {\r\n\t\t\t\tnewLength = MAX_ARRAY_SIZE;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet tmp = new Int32Array(newLength);\r\n\t\ttmp.set(this._data);\r\n\t\tthis._data = tmp;\r\n\t}\r\n\r\n\t/** Convert the list to a UTF-16 encoded char array. If all values are less\r\n\t *  than the 0xFFFF 16-bit code point limit then this is just a char array\r\n\t *  of 16-bit char as usual. For values in the supplementary range, encode\r\n\t * them as two UTF-16 code units.\r\n\t */\r\n\tpublic toCharArray(): Uint16Array {\r\n\t\t// Optimize for the common case (all data values are < 0xFFFF) to avoid an extra scan\r\n\t\tlet resultArray: Uint16Array = new Uint16Array(this._size);\r\n\t\tlet resultIdx = 0;\r\n\t\tlet calculatedPreciseResultSize = false;\r\n\t\tfor (let i = 0; i < this._size; i++) {\r\n\t\t\tlet codePoint = this._data[i];\r\n\t\t\tif (codePoint >= 0 && codePoint < 0x10000) {\r\n\t\t\t\tresultArray[resultIdx] = codePoint;\r\n\t\t\t\tresultIdx++;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// Calculate the precise result size if we encounter a code point > 0xFFFF\r\n\t\t\tif (!calculatedPreciseResultSize) {\r\n\t\t\t\tlet newResultArray = new Uint16Array(this.charArraySize());\r\n\t\t\t\tnewResultArray.set(resultArray, 0);\r\n\t\t\t\tresultArray = newResultArray;\r\n\t\t\t\tcalculatedPreciseResultSize = true;\r\n\t\t\t}\r\n\r\n\t\t\t// This will throw RangeError if the code point is not a valid Unicode code point\r\n\t\t\tlet pair = String.fromCodePoint(codePoint);\r\n\t\t\tresultArray[resultIdx] = pair.charCodeAt(0);\r\n\t\t\tresultArray[resultIdx + 1] = pair.charCodeAt(1);\r\n\t\t\tresultIdx += 2;\r\n\t\t}\r\n\t\treturn resultArray;\r\n\t}\r\n\r\n\tprivate charArraySize(): number {\r\n\t\tlet result = 0;\r\n\t\tfor (let i = 0; i < this._size; i++) {\r\n\t\t\tresult += this._data[i] >= 0x10000 ? 2 : 1;\r\n\t\t}\r\n\t\treturn result;\r\n\t}\r\n}\r\n"]}