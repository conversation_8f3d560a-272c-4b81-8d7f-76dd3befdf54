{"version": 3, "file": "Args.js", "sourceRoot": "", "sources": ["../../../src/misc/Args.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;;;;;GAOG;AACH,SAAgB,OAAO,CAAC,aAAqB,EAAE,KAAU;IACxD,IAAI,KAAK,IAAI,IAAI,EAAE;QAClB,MAAM,IAAI,SAAS,CAAC,aAAa,GAAG,+BAA+B,CAAC,CAAC;KACrE;AACF,CAAC;AAJD,0BAIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:39.6568608-07:00\r\n\r\n/**\r\n * Validates that an argument is not `null` or `undefined`.\r\n *\r\n * @param parameterName The name of the parameter\r\n * @param value The argument value\r\n *\r\n * @throws `TypeError` if `value` is `null` or `undefined`.\r\n */\r\nexport function notNull(parameterName: string, value: any): void {\r\n\tif (value == null) {\r\n\t\tthrow new TypeError(parameterName + \" cannot be null or undefined.\");\r\n\t}\r\n}\r\n"]}