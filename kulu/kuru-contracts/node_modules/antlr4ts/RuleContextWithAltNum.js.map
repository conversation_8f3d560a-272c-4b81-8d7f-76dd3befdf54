{"version": 3, "file": "RuleContextWithAltNum.js", "sourceRoot": "", "sources": ["../../src/RuleContextWithAltNum.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,mCAAgC;AAChC,6CAAwC;AACxC,2DAAwD;AAExD;;;;;;;;;GASG;AACH,MAAa,qBAAsB,SAAQ,qCAAiB;IAK3D,YAAY,MAA0B,EAAE,mBAA4B;QACnE,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACtC,KAAK,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;SACnC;aAAM;YACN,KAAK,EAAE,CAAC;SACR;QAED,IAAI,CAAC,UAAU,GAAG,SAAG,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED,YAAY;IACZ,IAAI,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;IAC1B,CAAC;CACD;AARA;IADC,qBAAQ;sDAGR;AAlBF,sDAwBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.4741196-07:00\r\n\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { Override } from \"./Decorators\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\n\r\n/** A handy class for use with\r\n *\r\n *  options {contextSuperClass=org.antlr.v4.runtime.RuleContextWithAltNum;}\r\n *\r\n *  that provides a backing field / impl for the outer alternative number\r\n *  matched for an internal parse tree node.\r\n *\r\n *  I'm only putting into Java runtime as I'm certain I'm the only one that\r\n *  will really every use this.\r\n */\r\nexport class RuleContextWithAltNum extends ParserRuleContext {\r\n\tprivate _altNumber: number;\r\n\r\n\tconstructor();\r\n\tconstructor(parent: ParserRuleContext | undefined, invokingStateNumber: number);\r\n\tconstructor(parent?: ParserRuleContext, invokingStateNumber?: number) {\r\n\t\tif (invokingStateNumber !== undefined) {\r\n\t\t\tsuper(parent, invokingStateNumber);\r\n\t\t} else {\r\n\t\t\tsuper();\r\n\t\t}\r\n\r\n\t\tthis._altNumber = ATN.INVALID_ALT_NUMBER;\r\n\t}\r\n\r\n\t@Override\r\n\tget altNumber(): number {\r\n\t\treturn this._altNumber;\r\n\t}\r\n\r\n\t// @Override\r\n\tset altNumber(altNum: number) {\r\n\t\tthis._altNumber = altNum;\r\n\t}\r\n}\r\n"]}