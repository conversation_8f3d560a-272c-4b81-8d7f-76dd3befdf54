{"version": 3, "file": "VocabularyImpl.js", "sourceRoot": "", "sources": ["../../src/VocabularyImpl.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,6CAAiD;AACjD,mCAAgC;AAGhC;;;;;GAKG;AACH,MAAa,cAAc;IAoB1B;;;;;;;;;;;;;;;;OAgBG;IACH,YAAY,YAAuC,EAAE,aAAwC,EAAE,YAAuC;QACrI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,sEAAsE;QACtE,IAAI,CAAC,aAAa;YACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACtE,CAAC;IAGD,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAGM,cAAc,CAAC,SAAiB;QACtC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC3D,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SACpC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAGM,eAAe,CAAC,SAAiB;QACvC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC5D,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SACrC;QAED,IAAI,SAAS,KAAK,aAAK,CAAC,GAAG,EAAE;YAC5B,OAAO,KAAK,CAAC;SACb;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAIM,cAAc,CAAC,SAAiB;QACtC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC3D,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,WAAW,EAAE;gBAChB,OAAO,WAAW,CAAC;aACnB;SACD;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,WAAW,EAAE;YAChB,OAAO,WAAW,CAAC;SACnB;QAED,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,YAAY,EAAE;YACjB,OAAO,YAAY,CAAC;SACpB;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;IAC1B,CAAC;;AA9FD;;;;;;GAMG;AAEoB,+BAAgB,GAAmB,IAAI,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAGzF;IADC,oBAAO;oDACiD;AAEzD;IADC,oBAAO;qDACkD;AAE1D;IADC,oBAAO;oDACiD;AAgCzD;IADC,qBAAQ;kDAGR;AAGD;IADC,qBAAQ;oDAOR;AAGD;IADC,qBAAQ;qDAWR;AAID;IAFC,qBAAQ;IACR,oBAAO;oDAoBP;AAtFD;IADC,oBAAO;8CACiF;AAT1F,wCAgGC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:59.5829654-07:00\r\n\r\nimport { NotNull, Override } from \"./Decorators\";\r\nimport { Token } from \"./Token\";\r\nimport { Vocabulary } from \"./Vocabulary\";\r\n\r\n/**\r\n * This class provides a default implementation of the {@link Vocabulary}\r\n * interface.\r\n *\r\n * <AUTHOR>\r\n */\r\nexport class VocabularyImpl implements Vocabulary {\r\n\t/**\r\n\t * Gets an empty {@link Vocabulary} instance.\r\n\t *\r\n\t * No literal or symbol names are assigned to token types, so\r\n\t * {@link #getDisplayName(int)} returns the numeric value for all tokens\r\n\t * except {@link Token#EOF}.\r\n\t */\r\n\t@NotNull\r\n\tpublic static readonly EMPTY_VOCABULARY: VocabularyImpl = new VocabularyImpl([], [], []);\r\n\r\n\t@NotNull\r\n\tprivate readonly literalNames: Array<string | undefined>;\r\n\t@NotNull\r\n\tprivate readonly symbolicNames: Array<string | undefined>;\r\n\t@NotNull\r\n\tprivate readonly displayNames: Array<string | undefined>;\r\n\r\n\tprivate _maxTokenType: number;\r\n\r\n\t/**\r\n\t * Constructs a new instance of {@link VocabularyImpl} from the specified\r\n\t * literal, symbolic, and display token names.\r\n\t *\r\n\t * @param literalNames The literal names assigned to tokens, or an empty array\r\n\t * if no literal names are assigned.\r\n\t * @param symbolicNames The symbolic names assigned to tokens, or\r\n\t * an empty array if no symbolic names are assigned.\r\n\t * @param displayNames The display names assigned to tokens, or an empty array\r\n\t * to use the values in `literalNames` and `symbolicNames` as\r\n\t * the source of display names, as described in\r\n\t * {@link #getDisplayName(int)}.\r\n\t *\r\n\t * @see #getLiteralName(int)\r\n\t * @see #getSymbolicName(int)\r\n\t * @see #getDisplayName(int)\r\n\t */\r\n\tconstructor(literalNames: Array<string | undefined>, symbolicNames: Array<string | undefined>, displayNames: Array<string | undefined>) {\r\n\t\tthis.literalNames = literalNames;\r\n\t\tthis.symbolicNames = symbolicNames;\r\n\t\tthis.displayNames = displayNames;\r\n\t\t// See note here on -1 part: https://github.com/antlr/antlr4/pull/1146\r\n\t\tthis._maxTokenType =\r\n\t\t\tMath.max(this.displayNames.length,\r\n\t\t\t\tMath.max(this.literalNames.length, this.symbolicNames.length)) - 1;\r\n\t}\r\n\r\n\t@Override\r\n\tget maxTokenType(): number {\r\n\t\treturn this._maxTokenType;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getLiteralName(tokenType: number): string | undefined {\r\n\t\tif (tokenType >= 0 && tokenType < this.literalNames.length) {\r\n\t\t\treturn this.literalNames[tokenType];\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getSymbolicName(tokenType: number): string | undefined {\r\n\t\tif (tokenType >= 0 && tokenType < this.symbolicNames.length) {\r\n\t\t\treturn this.symbolicNames[tokenType];\r\n\t\t}\r\n\r\n\t\tif (tokenType === Token.EOF) {\r\n\t\t\treturn \"EOF\";\r\n\t\t}\r\n\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t@Override\r\n\t@NotNull\r\n\tpublic getDisplayName(tokenType: number): string {\r\n\t\tif (tokenType >= 0 && tokenType < this.displayNames.length) {\r\n\t\t\tlet displayName = this.displayNames[tokenType];\r\n\t\t\tif (displayName) {\r\n\t\t\t\treturn displayName;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet literalName = this.getLiteralName(tokenType);\r\n\t\tif (literalName) {\r\n\t\t\treturn literalName;\r\n\t\t}\r\n\r\n\t\tlet symbolicName = this.getSymbolicName(tokenType);\r\n\t\tif (symbolicName) {\r\n\t\t\treturn symbolicName;\r\n\t\t}\r\n\r\n\t\treturn String(tokenType);\r\n\t}\r\n}\r\n"]}