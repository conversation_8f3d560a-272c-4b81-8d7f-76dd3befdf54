{"version": 3, "file": "ProxyParserErrorListener.js", "sourceRoot": "", "sources": ["../../src/ProxyParserErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAUH,6DAA0D;AAI1D,6CAAwC;AAExC;;GAEG;AACH,MAAa,wBAAyB,SAAQ,uCAA8C;IAG3F,YAAY,SAAgC;QAC3C,KAAK,CAAC,SAAS,CAAC,CAAC;IAClB,CAAC;IAGM,eAAe,CACrB,UAAkB,EAClB,GAAQ,EACR,UAAkB,EAClB,SAAiB,EACjB,KAAc,EACd,SAA6B,EAC7B,OAAqB;QACrB,IAAI,CAAC,YAAY,EAAE;aACjB,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrB,IAAI,QAAQ,CAAC,eAAe,EAAE;gBAC7B,QAAQ,CAAC,eAAe,CACvB,UAAU,EACV,GAAG,EACH,UAAU,EACV,SAAS,EACT,KAAK,EACL,SAAS,EACT,OAAO,CAAC,CAAC;aACV;QAEF,CAAC,CAAC,CAAC;IACL,CAAC;IAGM,2BAA2B,CACjC,UAAkB,EAClB,GAAQ,EACR,UAAkB,EAClB,SAAiB,EACjB,eAAmC,EACnC,aAA6B;QAC7B,IAAI,CAAC,YAAY,EAAE;aACjB,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrB,IAAI,QAAQ,CAAC,2BAA2B,EAAE;gBACzC,QAAQ,CAAC,2BAA2B,CACnC,UAAU,EACV,GAAG,EACH,UAAU,EACV,SAAS,EACT,eAAe,EACf,aAAa,CAAC,CAAC;aAChB;QACF,CAAC,CAAC,CAAC;IACL,CAAC;IAGM,wBAAwB,CAC9B,UAAkB,EAClB,GAAQ,EACR,UAAkB,EAClB,SAAiB,EACjB,UAAkB,EAClB,WAA2B;QAC3B,IAAI,CAAC,YAAY,EAAE;aACjB,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrB,IAAI,QAAQ,CAAC,wBAAwB,EAAE;gBACtC,QAAQ,CAAC,wBAAwB,CAChC,UAAU,EACV,GAAG,EACH,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,CAAC,CAAC;aACd;QACF,CAAC,CAAC,CAAC;IACL,CAAC;CACD;AAnEA;IADC,qBAAQ;+DAuBR;AAGD;IADC,qBAAQ;2EAoBR;AAGD;IADC,qBAAQ;wEAoBR;AA1EF,4DA2EC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:56.9812284-07:00\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { ATNConfigSet } from \"./atn/ATNConfigSet\";\r\nimport { BitSet } from \"./misc/BitSet\";\r\nimport { DFA } from \"./dfa/DFA\";\r\nimport { Parser } from \"./Parser\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { ProxyErrorListener } from \"./ProxyErrorListener\";\r\nimport { ParserErrorListener } from \"./ParserErrorListener\";\r\nimport { SimulatorState } from \"./atn/SimulatorState\";\r\nimport { Token } from \"./Token\";\r\nimport { Override } from \"./Decorators\";\r\n\r\n/**\r\n * <AUTHOR>\r\n */\r\nexport class ProxyParserErrorListener extends ProxyErrorListener<Token, ParserErrorListener>\r\n\timplements ParserErrorListener {\r\n\r\n\tconstructor(delegates: ParserErrorListener[]) {\r\n\t\tsuper(delegates);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportAmbiguity(\r\n\t\trecognizer: Parser,\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\texact: boolean,\r\n\t\tambigAlts: BitSet | undefined,\r\n\t\tconfigs: ATNConfigSet): void {\r\n\t\tthis.getDelegates()\r\n\t\t\t.forEach((listener) => {\r\n\t\t\t\tif (listener.reportAmbiguity) {\r\n\t\t\t\t\tlistener.reportAmbiguity(\r\n\t\t\t\t\t\trecognizer,\r\n\t\t\t\t\t\tdfa,\r\n\t\t\t\t\t\tstartIndex,\r\n\t\t\t\t\t\tstopIndex,\r\n\t\t\t\t\t\texact,\r\n\t\t\t\t\t\tambigAlts,\r\n\t\t\t\t\t\tconfigs);\r\n\t\t\t\t}\r\n\r\n\t\t\t});\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportAttemptingFullContext(\r\n\t\trecognizer: Parser,\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tconflictingAlts: BitSet | undefined,\r\n\t\tconflictState: SimulatorState): void {\r\n\t\tthis.getDelegates()\r\n\t\t\t.forEach((listener) => {\r\n\t\t\t\tif (listener.reportAttemptingFullContext) {\r\n\t\t\t\t\tlistener.reportAttemptingFullContext(\r\n\t\t\t\t\t\trecognizer,\r\n\t\t\t\t\t\tdfa,\r\n\t\t\t\t\t\tstartIndex,\r\n\t\t\t\t\t\tstopIndex,\r\n\t\t\t\t\t\tconflictingAlts,\r\n\t\t\t\t\t\tconflictState);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t}\r\n\r\n\t@Override\r\n\tpublic reportContextSensitivity(\r\n\t\trecognizer: Parser,\r\n\t\tdfa: DFA,\r\n\t\tstartIndex: number,\r\n\t\tstopIndex: number,\r\n\t\tprediction: number,\r\n\t\tacceptState: SimulatorState): void {\r\n\t\tthis.getDelegates()\r\n\t\t\t.forEach((listener) => {\r\n\t\t\t\tif (listener.reportContextSensitivity) {\r\n\t\t\t\t\tlistener.reportContextSensitivity(\r\n\t\t\t\t\t\trecognizer,\r\n\t\t\t\t\t\tdfa,\r\n\t\t\t\t\t\tstartIndex,\r\n\t\t\t\t\t\tstopIndex,\r\n\t\t\t\t\t\tprediction,\r\n\t\t\t\t\t\tacceptState);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t}\r\n}\r\n"]}