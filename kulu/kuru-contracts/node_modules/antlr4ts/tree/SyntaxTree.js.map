{"version": 3, "file": "SyntaxTree.js", "sourceRoot": "", "sources": ["../../../src/tree/SyntaxTree.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.9953174-07:00\r\n\r\nimport { Tree } from \"./Tree\";\r\nimport { Interval } from \"../misc/Interval\";\r\n\r\n/** A tree that knows about an interval in a token stream\r\n *  is some kind of syntax tree. Subinterfaces distinguish\r\n *  between parse trees and other kinds of syntax trees we might want to create.\r\n */\r\nexport interface SyntaxTree extends Tree {\r\n\t/**\r\n\t * Return an {@link Interval} indicating the index in the\r\n\t * {@link TokenStream} of the first and last token associated with this\r\n\t * subtree. If this node is a leaf, then the interval represents a single\r\n\t * token and has interval i..i for token index i.\r\n\t *\r\n\t * An interval of i..i-1 indicates an empty interval at position\r\n\t * i in the input stream, where 0 &lt;= i &lt;= the size of the input\r\n\t * token stream.  Currently, the code base can only have i=0..n-1 but\r\n\t * in concept one could have an empty interval after EOF.\r\n\t *\r\n\t * If source interval is unknown, this returns {@link Interval#INVALID}.\r\n\t *\r\n\t * As a weird special case, the source interval for rules matched after\r\n\t * EOF is unspecified.\r\n\t */\r\n\t//@NotNull\r\n\treadonly sourceInterval: Interval;\r\n}\r\n"]}