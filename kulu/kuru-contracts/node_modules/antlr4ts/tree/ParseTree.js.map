{"version": 3, "file": "ParseTree.js", "sourceRoot": "", "sources": ["../../../src/tree/ParseTree.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.5349010-07:00\r\n\r\nimport { Parser } from \"../Parser\";\r\nimport { ParseTreeVisitor } from \"./ParseTreeVisitor\";\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { SyntaxTree } from \"./SyntaxTree\";\r\n\r\n/** An interface to access the tree of {@link RuleContext} objects created\r\n *  during a parse that makes the data structure look like a simple parse tree.\r\n *  This node represents both internal nodes, rule invocations,\r\n *  and leaf nodes, token matches.\r\n *\r\n *  The payload is either a {@link Token} or a {@link RuleContext} object.\r\n */\r\nexport interface ParseTree extends SyntaxTree {\r\n\t// the following methods narrow the return type; they are not additional methods\r\n\t//@Override\r\n\treadonly parent: ParseTree | undefined;\r\n\r\n\t/**\r\n\t * Set the parent for this node.\r\n\t *\r\n\t * @since 4.7\r\n\t */\r\n\tsetParent(parent: RuleContext): void;\r\n\r\n\t//@Override\r\n\tgetChild(i: number): ParseTree;\r\n\r\n\t/** The {@link ParseTreeVisitor} needs a double dispatch method. */\r\n\taccept<T>(visitor: ParseTreeVisitor<T>): T;\r\n\r\n\t/** Return the combined text of all leaf nodes. Does not get any\r\n\t *  off-channel tokens (if any) so won't return whitespace and\r\n\t *  comments if they are sent to parser on hidden channel.\r\n\t */\r\n\treadonly text: string;\r\n\r\n\t/** Specialize toStringTree so that it can print out more information\r\n\t * \tbased upon the parser.\r\n\t */\r\n\ttoStringTree(parser?: Parser): string;\r\n}\r\n"]}