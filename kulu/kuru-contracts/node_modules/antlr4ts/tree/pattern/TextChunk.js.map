{"version": 3, "file": "TextChunk.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/TextChunk.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,mCAAgC;AAChC,iDAAqD;AAErD;;;GAGG;AACH,IAAa,SAAS,GAAtB,MAAa,SAAU,SAAQ,aAAK;IAOnC;;;;;OAKG;IACH,YAAqB,IAAY;QAChC,KAAK,EAAE,CAAC;QAER,IAAI,IAAI,IAAI,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IAEH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IAEI,QAAQ;QACd,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAC/B,CAAC;CACD,CAAA;AAtCA;IADC,oBAAO;wCACc;AAwBtB;IADC,oBAAO;qCAGP;AASD;IADC,qBAAQ;yCAGR;AA1CW,SAAS;IAaR,WAAA,oBAAO,CAAA;GAbR,SAAS,CA2CrB;AA3CY,8BAAS", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:46.2521448-07:00\r\n\r\nimport { Chunk } from \"./Chunk\";\r\nimport { NotNull, Override } from \"../../Decorators\";\r\n\r\n/**\r\n * Represents a span of raw text (concrete syntax) between tags in a tree\r\n * pattern string.\r\n */\r\nexport class TextChunk extends Chunk {\r\n\t/**\r\n\t * This is the backing field for {@link #getText}.\r\n\t */\r\n\t@NotNull\r\n\tprivate _text: string;\r\n\r\n\t/**\r\n\t * Constructs a new instance of {@link TextChunk} with the specified text.\r\n\t *\r\n\t * @param text The text of this chunk.\r\n\t * @exception IllegalArgumentException if `text` is not defined.\r\n\t */\r\n\tconstructor(@NotNull text: string) {\r\n\t\tsuper();\r\n\r\n\t\tif (text == null) {\r\n\t\t\tthrow new Error(\"text cannot be null\");\r\n\t\t}\r\n\r\n\t\tthis._text = text;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the raw text of this chunk.\r\n\t *\r\n\t * @returns The text of the chunk.\r\n\t */\r\n\t@NotNull\r\n\tget text(): string {\r\n\t\treturn this._text;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link TextChunk} returns the result of\r\n\t * `text` in single quotes.\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn \"'\" + this._text + \"'\";\r\n\t}\r\n}\r\n"]}