{"version": 3, "file": "TokenTagToken.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/TokenTagToken.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,wDAAwD;AAExD,mDAAgD;AAChD,iDAAqD;AAErD;;;;GAIG;AACH,IAAa,aAAa,GAA1B,MAAa,aAAc,SAAQ,yBAAW;IAW7C;;;;;;;;OAQG;IACH,YAAqB,SAAiB,EAAE,IAAY,EAAE,KAAc;QACnE,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IAED;;;OAGG;IAEH,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IAEH,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACxB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;SACvD;QAED,OAAO,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IAEI,QAAQ;QACd,OAAO,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC;CACD,CAAA;AAjEA;IADC,oBAAO;iDACmB;AA0B3B;IADC,oBAAO;8CAGP;AAmBD;IADC,qBAAQ;yCAOR;AASD;IADC,qBAAQ;6CAGR;AArEW,aAAa;IAoBZ,WAAA,oBAAO,CAAA;GApBR,aAAa,CAsEzB;AAtEY,sCAAa", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:46.3281988-07:00\r\n\r\nimport { CommonToken } from \"../../CommonToken\";\r\nimport { NotNull, Override } from \"../../Decorators\";\r\n\r\n/**\r\n * A {@link Token} object representing a token of a particular type; e.g.,\r\n * `<ID>`. These tokens are created for {@link TagChunk} chunks where the\r\n * tag corresponds to a lexer rule or token type.\r\n */\r\nexport class TokenTagToken extends CommonToken {\r\n\t/**\r\n\t * This is the backing field for `tokenName`.\r\n\t */\r\n\t@NotNull\r\n\tprivate _tokenName: string;\r\n\t/**\r\n\t * This is the backing field for `label`.\r\n\t */\r\n\tprivate _label: string | undefined;\r\n\r\n\t/**\r\n\t * Constructs a new instance of {@link TokenTagToken} with the specified\r\n\t * token name, type, and label.\r\n\t *\r\n\t * @param tokenName The token name.\r\n\t * @param type The token type.\r\n\t * @param label The label associated with the token tag, or `undefined` if\r\n\t * the token tag is unlabeled.\r\n\t */\r\n\tconstructor(@NotNull tokenName: string, type: number, label?: string) {\r\n\t\tsuper(type);\r\n\t\tthis._tokenName = tokenName;\r\n\t\tthis._label = label;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the token name.\r\n\t * @returns The token name.\r\n\t */\r\n\t@NotNull\r\n\tget tokenName(): string {\r\n\t\treturn this._tokenName;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the label associated with the rule tag.\r\n\t *\r\n\t * @returns The name of the label associated with the rule tag, or\r\n\t * `undefined` if this is an unlabeled rule tag.\r\n\t */\r\n\tget label(): string | undefined {\r\n\t\treturn this._label;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link TokenTagToken} returns the token tag\r\n\t * formatted with `<` and `>` delimiters.\r\n\t */\r\n\t@Override\r\n\tget text(): string {\r\n\t\tif (this._label != null) {\r\n\t\t\treturn \"<\" + this._label + \":\" + this._tokenName + \">\";\r\n\t\t}\r\n\r\n\t\treturn \"<\" + this._tokenName + \">\";\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link TokenTagToken} returns a string of the form\r\n\t * `tokenName:type`.\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this._tokenName + \":\" + this.type;\r\n\t}\r\n}\r\n"]}