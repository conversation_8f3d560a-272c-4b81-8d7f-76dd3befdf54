{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/index.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,mDAAiC;AACjC,qDAAmC;AACnC,4DAA0C;AAC1C,iDAA+B;AAC/B,kDAAgC;AAEhC,kHAAkH;AAClH,wBAAwB;AACxB,EAAE;AACF,2BAA2B;AAC3B,8BAA8B;AAC9B,+BAA+B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport * from \"./ParseTreeMatch\";\r\nexport * from \"./ParseTreePattern\";\r\nexport * from \"./ParseTreePatternMatcher\";\r\nexport * from \"./RuleTagToken\";\r\nexport * from \"./TokenTagToken\";\r\n\r\n// The following are \"package-private modules\" - exported individually but don't need to be part of the public API\r\n// exposed by this file.\r\n//\r\n// export * from \"./Chunk\";\r\n// export * from \"./TagChunk\";\r\n// export * from \"./TextChunk\";\r\n"]}