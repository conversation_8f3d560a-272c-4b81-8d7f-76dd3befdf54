{"version": 3, "file": "ParseTreePattern.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/ParseTreePattern.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA2C;AAI3C,0CAAuC;AAEvC;;;GAGG;AACH,IAAa,gBAAgB,GAA7B,MAAa,gBAAgB;IAwB5B;;;;;;;;;OASG;IACH,YACU,OAAgC,EAChC,OAAe,EACxB,gBAAwB,EACf,WAAsB;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACjC,CAAC;IAED;;;;;;;OAOG;IAEI,KAAK,CAAU,IAAe;QACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAU,IAAe;QACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC;IAClD,CAAC;IAED;;;;;;;;;;OAUG;IAEI,OAAO,CAAU,IAAe,EAAW,KAAa;QAC9D,IAAI,QAAQ,GAAmB,aAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChF,IAAI,OAAO,GAAqB,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,IAAI,QAAQ,EAAE;YACvB,IAAI,KAAK,GAAmB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,KAAK,CAAC,SAAS,EAAE;gBACpB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpB;SACD;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IAEH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;;;;OAIG;IAEH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACH,IAAI,gBAAgB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IAEH,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;CACD,CAAA;AA9HA;IADC,oBAAO;kDACiB;AAMzB;IADC,oBAAO;sDACwB;AAMhC;IADC,oBAAO;kDACkC;AAgC1C;IADC,oBAAO;IACM,WAAA,oBAAO,CAAA;6CAEpB;AASD;IAAgB,WAAA,oBAAO,CAAA;+CAEtB;AAcD;IADC,oBAAO;IACQ,WAAA,oBAAO,CAAA,EAAmB,WAAA,oBAAO,CAAA;+CAUhD;AASD;IADC,oBAAO;+CAGP;AAQD;IADC,oBAAO;+CAGP;AAqBD;IADC,oBAAO;mDAGP;AAvIW,gBAAgB;IAmC1B,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IAEP,WAAA,oBAAO,CAAA;GAtCG,gBAAgB,CAwI5B;AAxIY,4CAAgB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { NotNull } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { ParseTreeMatch } from \"./ParseTreeMatch\";\r\nimport { ParseTreePatternMatcher } from \"./ParseTreePatternMatcher\";\r\nimport { XPath } from \"../xpath/XPath\";\r\n\r\n/**\r\n * A pattern like `<ID> = <expr>;` converted to a {@link ParseTree} by\r\n * {@link ParseTreePatternMatcher#compile(String, int)}.\r\n */\r\nexport class ParseTreePattern {\r\n\t/**\r\n\t * This is the backing field for `patternRuleIndex`.\r\n\t */\r\n\tprivate _patternRuleIndex: number;\r\n\r\n\t/**\r\n\t * This is the backing field for `pattern`.\r\n\t */\r\n\t@NotNull\r\n\tprivate _pattern: string;\r\n\r\n\t/**\r\n\t * This is the backing field for `patternTree`.\r\n\t */\r\n\t@NotNull\r\n\tprivate _patternTree: ParseTree;\r\n\r\n\t/**\r\n\t * This is the backing field for `matcher`.\r\n\t */\r\n\t@NotNull\r\n\tprivate _matcher: ParseTreePatternMatcher;\r\n\r\n\t/**\r\n\t * Construct a new instance of the {@link ParseTreePattern} class.\r\n\t *\r\n\t * @param matcher The {@link ParseTreePatternMatcher} which created this\r\n\t * tree pattern.\r\n\t * @param pattern The tree pattern in concrete syntax form.\r\n\t * @param patternRuleIndex The parser rule which serves as the root of the\r\n\t * tree pattern.\r\n\t * @param patternTree The tree pattern in {@link ParseTree} form.\r\n\t */\r\n\tconstructor(\r\n\t\t@NotNull matcher: ParseTreePatternMatcher,\r\n\t\t@NotNull pattern: string,\r\n\t\tpatternRuleIndex: number,\r\n\t\t@NotNull patternTree: ParseTree) {\r\n\t\tthis._matcher = matcher;\r\n\t\tthis._patternRuleIndex = patternRuleIndex;\r\n\t\tthis._pattern = pattern;\r\n\t\tthis._patternTree = patternTree;\r\n\t}\r\n\r\n\t/**\r\n\t * Match a specific parse tree against this tree pattern.\r\n\t *\r\n\t * @param tree The parse tree to match against this tree pattern.\r\n\t * @returns A {@link ParseTreeMatch} object describing the result of the\r\n\t * match operation. The `ParseTreeMatch.succeeded` method can be\r\n\t * used to determine whether or not the match was successful.\r\n\t */\r\n\t@NotNull\r\n\tpublic match(@NotNull tree: ParseTree): ParseTreeMatch {\r\n\t\treturn this._matcher.match(tree, this);\r\n\t}\r\n\r\n\t/**\r\n\t * Determine whether or not a parse tree matches this tree pattern.\r\n\t *\r\n\t * @param tree The parse tree to match against this tree pattern.\r\n\t * @returns `true` if `tree` is a match for the current tree\r\n\t * pattern; otherwise, `false`.\r\n\t */\r\n\tpublic matches(@NotNull tree: ParseTree): boolean {\r\n\t\treturn this._matcher.match(tree, this).succeeded;\r\n\t}\r\n\r\n\t/**\r\n\t * Find all nodes using XPath and then try to match those subtrees against\r\n\t * this tree pattern.\r\n\t *\r\n\t * @param tree The {@link ParseTree} to match against this pattern.\r\n\t * @param xpath An expression matching the nodes\r\n\t *\r\n\t * @returns A collection of {@link ParseTreeMatch} objects describing the\r\n\t * successful matches. Unsuccessful matches are omitted from the result,\r\n\t * regardless of the reason for the failure.\r\n\t */\r\n\t@NotNull\r\n\tpublic findAll(@NotNull tree: ParseTree, @NotNull xpath: string): ParseTreeMatch[] {\r\n\t\tlet subtrees: Set<ParseTree> = XPath.findAll(tree, xpath, this._matcher.parser);\r\n\t\tlet matches: ParseTreeMatch[] = [];\r\n\t\tfor (let t of subtrees) {\r\n\t\t\tlet match: ParseTreeMatch = this.match(t);\r\n\t\t\tif (match.succeeded) {\r\n\t\t\t\tmatches.push(match);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn matches;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the {@link ParseTreePatternMatcher} which created this tree pattern.\r\n\t *\r\n\t * @returns The {@link ParseTreePatternMatcher} which created this tree\r\n\t * pattern.\r\n\t */\r\n\t@NotNull\r\n\tget matcher(): ParseTreePatternMatcher {\r\n\t\treturn this._matcher;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the tree pattern in concrete syntax form.\r\n\t *\r\n\t * @returns The tree pattern in concrete syntax form.\r\n\t */\r\n\t@NotNull\r\n\tget pattern(): string {\r\n\t\treturn this._pattern;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the parser rule which serves as the outermost rule for the tree\r\n\t * pattern.\r\n\t *\r\n\t * @returns The parser rule which serves as the outermost rule for the tree\r\n\t * pattern.\r\n\t */\r\n\tget patternRuleIndex(): number {\r\n\t\treturn this._patternRuleIndex;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the tree pattern as a {@link ParseTree}. The rule and token tags from\r\n\t * the pattern are present in the parse tree as terminal nodes with a symbol\r\n\t * of type {@link RuleTagToken} or {@link TokenTagToken}.\r\n\t *\r\n\t * @returns The tree pattern as a {@link ParseTree}.\r\n\t */\r\n\t@NotNull\r\n\tget patternTree(): ParseTree {\r\n\t\treturn this._patternTree;\r\n\t}\r\n}\r\n"]}