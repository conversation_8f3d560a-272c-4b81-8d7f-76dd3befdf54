{"version": 3, "file": "Chunk.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/Chunk.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD;;;;;;;;;;GAUG;AACH,MAAsB,KAAK;CAC1B;AADD,sBACC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:45.2799060-07:00\r\n\r\n/**\r\n * A chunk is either a token tag, a rule tag, or a span of literal text within a\r\n * tree pattern.\r\n *\r\n * The method {@link ParseTreePatternMatcher#split(String)} returns a list of\r\n * chunks in preparation for creating a token stream by\r\n * {@link ParseTreePatternMatcher#tokenize(String)}. From there, we get a parse\r\n * tree from with {@link ParseTreePatternMatcher#compile(String, int)}. These\r\n * chunks are converted to {@link RuleTagToken}, {@link TokenTagToken}, or the\r\n * regular tokens of the text surrounding the tags.\r\n */\r\nexport abstract class Chunk {\r\n}\r\n"]}