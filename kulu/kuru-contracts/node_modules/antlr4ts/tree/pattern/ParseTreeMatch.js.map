{"version": 3, "file": "ParseTreeMatch.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/ParseTreeMatch.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAIH,iDAAqD;AAIrD;;GAEG;AACH,IAAa,cAAc,GAA3B,MAAa,cAAc;IAqB1B;;;;;;;;;;;;;;OAcG;IACH,YACU,IAAe,EACf,OAAyB,EACzB,MAAmC,EAC5C,cAAqC;QACrC,IAAI,CAAC,IAAI,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,OAAO,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,MAAM,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,GAAG,CAAC,KAAa;QACvB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3C,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,0BAA0B;IACrE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IAEI,MAAM,CAAU,KAAa;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE;YACX,OAAO,EAAE,CAAC;SACV;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;;;;;OASG;IAEH,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACH,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,IAAI,SAAS;QACZ,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IAEH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;;;;OAIG;IAEH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;OAEG;IAEI,QAAQ;QACd,OAAO,SACN,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,WACvC,IAAI,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;IAC5B,CAAC;CACD,CAAA;AAxEA;IADC,oBAAO;IACO,WAAA,oBAAO,CAAA;4CAMrB;AAaD;IADC,oBAAO;4CAGP;AA4BD;IADC,oBAAO;6CAGP;AAQD;IADC,oBAAO;0CAGP;AAMD;IADC,qBAAQ;8CAKR;AAjLW,cAAc;IAqCxB,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;GAvCG,cAAc,CAkL1B;AAlLY,wCAAc", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { MultiMap } from \"../../misc/MultiMap\";\r\nimport { NotNull, Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { ParseTreePattern } from \"./ParseTreePattern\";\r\n\r\n/**\r\n * Represents the result of matching a {@link ParseTree} against a tree pattern.\r\n */\r\nexport class ParseTreeMatch {\r\n\t/**\r\n\t * This is the backing field for `tree`.\r\n\t */\r\n\tprivate _tree: ParseTree;\r\n\r\n\t/**\r\n\t * This is the backing field for `pattern`.\r\n\t */\r\n\tprivate _pattern: ParseTreePattern;\r\n\r\n\t/**\r\n\t * This is the backing field for `labels`.\r\n\t */\r\n\tprivate _labels: MultiMap<string, ParseTree>;\r\n\r\n\t/**\r\n\t * This is the backing field for `mismatchedNode`.\r\n\t */\r\n\tprivate _mismatchedNode?: ParseTree;\r\n\r\n\t/**\r\n\t * Constructs a new instance of {@link ParseTreeMatch} from the specified\r\n\t * parse tree and pattern.\r\n\t *\r\n\t * @param tree The parse tree to match against the pattern.\r\n\t * @param pattern The parse tree pattern.\r\n\t * @param labels A mapping from label names to collections of\r\n\t * {@link ParseTree} objects located by the tree pattern matching process.\r\n\t * @param mismatchedNode The first node which failed to match the tree\r\n\t * pattern during the matching process.\r\n\t *\r\n\t * @throws {@link Error} if `tree` is not defined\r\n\t * @throws {@link Error} if `pattern` is not defined\r\n\t * @throws {@link Error} if `labels` is not defined\r\n\t */\r\n\tconstructor(\r\n\t\t@NotNull tree: ParseTree,\r\n\t\t@NotNull pattern: ParseTreePattern,\r\n\t\t@NotNull labels: MultiMap<string, ParseTree>,\r\n\t\tmismatchedNode: ParseTree | undefined) {\r\n\t\tif (!tree) {\r\n\t\t\tthrow new Error(\"tree cannot be null\");\r\n\t\t}\r\n\r\n\t\tif (!pattern) {\r\n\t\t\tthrow new Error(\"pattern cannot be null\");\r\n\t\t}\r\n\r\n\t\tif (!labels) {\r\n\t\t\tthrow new Error(\"labels cannot be null\");\r\n\t\t}\r\n\r\n\t\tthis._tree = tree;\r\n\t\tthis._pattern = pattern;\r\n\t\tthis._labels = labels;\r\n\t\tthis._mismatchedNode = mismatchedNode;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the last node associated with a specific `label`.\r\n\t *\r\n\t * For example, for pattern `<id:ID>`, `get(\"id\")` returns the\r\n\t * node matched for that `ID`. If more than one node\r\n\t * matched the specified label, only the last is returned. If there is\r\n\t * no node associated with the label, this returns `undefined`.\r\n\t *\r\n\t * Pattern tags like `<ID>` and `<expr>` without labels are\r\n\t * considered to be labeled with `ID` and `expr`, respectively.\r\n\t *\r\n\t * @param label The label to check.\r\n\t *\r\n\t * @returns The last {@link ParseTree} to match a tag with the specified\r\n\t * label, or `undefined` if no parse tree matched a tag with the label.\r\n\t */\r\n\tpublic get(label: string): ParseTree | undefined {\r\n\t\tlet parseTrees = this._labels.get(label);\r\n\t\tif (!parseTrees || parseTrees.length === 0) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\treturn parseTrees[parseTrees.length - 1]; // return last if multiple\r\n\t}\r\n\r\n\t/**\r\n\t * Return all nodes matching a rule or token tag with the specified label.\r\n\t *\r\n\t * If the `label` is the name of a parser rule or token in the\r\n\t * grammar, the resulting list will contain both the parse trees matching\r\n\t * rule or tags explicitly labeled with the label and the complete set of\r\n\t * parse trees matching the labeled and unlabeled tags in the pattern for\r\n\t * the parser rule or token. For example, if `label` is `\"foo\"`,\r\n\t * the result will contain *all* of the following.\r\n\t *\r\n\t * * Parse tree nodes matching tags of the form `<foo:anyRuleName>` and\r\n\t *   `<foo:AnyTokenName>`.\r\n\t * * Parse tree nodes matching tags of the form `<anyLabel:foo>`.\r\n\t * * Parse tree nodes matching tags of the form `<foo>`.\r\n\t *\r\n\t * @param label The label.\r\n\t *\r\n\t * @returns A collection of all {@link ParseTree} nodes matching tags with\r\n\t * the specified `label`. If no nodes matched the label, an empty list\r\n\t * is returned.\r\n\t */\r\n\t@NotNull\r\n\tpublic getAll(@NotNull label: string): ParseTree[] {\r\n\t\tconst nodes = this._labels.get(label);\r\n\t\tif (!nodes) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\t\treturn nodes;\r\n\t}\r\n\r\n\t/**\r\n\t * Return a mapping from label &rarr; [list of nodes].\r\n\t *\r\n\t * The map includes special entries corresponding to the names of rules and\r\n\t * tokens referenced in tags in the original pattern. For additional\r\n\t * information, see the description of {@link #getAll(String)}.\r\n\t *\r\n\t * @returns A mapping from labels to parse tree nodes. If the parse tree\r\n\t * pattern did not contain any rule or token tags, this map will be empty.\r\n\t */\r\n\t@NotNull\r\n\tget labels(): MultiMap<string, ParseTree> {\r\n\t\treturn this._labels;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the node at which we first detected a mismatch.\r\n\t *\r\n\t * @returns the node at which we first detected a mismatch, or `undefined`\r\n\t * if the match was successful.\r\n\t */\r\n\tget mismatchedNode(): ParseTree | undefined {\r\n\t\treturn this._mismatchedNode;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets a value indicating whether the match operation succeeded.\r\n\t *\r\n\t * @returns `true` if the match operation succeeded; otherwise,\r\n\t * `false`.\r\n\t */\r\n\tget succeeded(): boolean {\r\n\t\treturn !this._mismatchedNode;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the tree pattern we are matching against.\r\n\t *\r\n\t * @returns The tree pattern we are matching against.\r\n\t */\r\n\t@NotNull\r\n\tget pattern(): ParseTreePattern {\r\n\t\treturn this._pattern;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the parse tree we are trying to match to a pattern.\r\n\t *\r\n\t * @returns The {@link ParseTree} we are trying to match to a pattern.\r\n\t */\r\n\t@NotNull\r\n\tget tree(): ParseTree {\r\n\t\treturn this._tree;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn `Match ${\r\n\t\t\tthis.succeeded ? \"succeeded\" : \"failed\"}; found ${\r\n\t\t\tthis.labels.size} labels`;\r\n\t}\r\n}\r\n"]}