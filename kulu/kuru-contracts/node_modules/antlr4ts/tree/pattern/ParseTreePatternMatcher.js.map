{"version": 3, "file": "ParseTreePatternMatcher.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/ParseTreePatternMatcher.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAEH,+CAA+C;AAE/C,+DAA4D;AAC5D,mDAAgD;AAEhD,+DAA4D;AAE5D,2DAAwD;AACxD,kDAA+C;AAC/C,iDAA2C;AAC3C,sFAAmF;AAEnF,+DAA4D;AAC5D,+DAA4D;AAE5D,qDAAkD;AAClD,yDAAsD;AACtD,qEAAkE;AAClE,0CAAuC;AACvC,iDAA8C;AAC9C,yCAAsC;AACtC,kDAA+C;AAC/C,2CAAwC;AACxC,uCAAoC;AACpC,mDAAgD;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0DG;AACH,MAAa,uBAAuB;IAoBnC;;;;;OAKG;IACH,YAAY,KAAY,EAAE,MAAc;QAf9B,UAAK,GAAG,GAAG,CAAC;QACZ,SAAI,GAAG,GAAG,CAAC;QACX,WAAM,GAAG,IAAI,CAAC,CAAC,oCAAoC;QAE7D;;WAEG;QACO,aAAQ,GAAG,KAAK,CAAC;QAS1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;OAUG;IACI,aAAa,CAAC,KAAa,EAAE,IAAY,EAAE,UAAkB;QACnE,IAAI,CAAC,KAAK,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,IAAI,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IACpF,CAAC;IAUM,OAAO,CAAC,IAAe,EAAE,OAAkC,EAAE,mBAA2B,CAAC;QAC/F,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAChC,IAAI,CAAC,GAAqB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SAC7B;aAAM;YACN,IAAI,MAAM,GAAG,IAAI,mBAAQ,EAAqB,CAAC;YAC/C,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACvE,OAAO,CAAC,cAAc,CAAC;SACvB;IACF,CAAC;IAiBD,0BAA0B;IAEnB,KAAK,CAAC,IAAe,EAAW,OAAkC,EAAE,mBAA2B,CAAC;QACtG,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAChC,IAAI,CAAC,GAAqB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SAC3B;aAAM;YACN,IAAI,MAAM,GAAG,IAAI,mBAAQ,EAAqB,CAAC;YAC/C,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACvE,OAAO,IAAI,+BAAc,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;SACjE;IACF,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,OAAe,EAAE,gBAAwB;QACvD,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,QAAQ,GAAG,IAAI,iCAAe,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,MAAM,GAAG,IAAI,qCAAiB,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,IAAI,YAAY,GAAG,IAAI,qCAAiB,CACvC,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,oBAAoB,EAAE,EAC7B,MAAM,CAAC,CAAC;QAET,IAAI,IAAe,CAAC;QACpB,IAAI;YACH,YAAY,CAAC,YAAY,GAAG,IAAI,qCAAiB,EAAE,CAAC;YACpD,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC/C,2EAA2E;SACxE;QAAC,OAAO,CAAC,EAAE;YACX,IAAI,CAAC,YAAY,uDAA0B,EAAE;gBAC5C,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;aACnB;iBAAM,IAAI,CAAC,YAAY,2CAAoB,EAAE;gBAC7C,MAAM,CAAC,CAAC;aACR;iBAAM,IAAI,CAAC,YAAY,KAAK,EAAE;gBAC9B,MAAM,IAAI,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;aAC3D;iBAAM;gBACN,MAAM,CAAC,CAAC;aACR;SACD;QAED,iEAAiE;QACjE,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,aAAK,CAAC,GAAG,EAAE;YAC/B,MAAM,IAAI,uBAAuB,CAAC,kCAAkC,EAAE,CAAC;SACvE;QAED,OAAO,IAAI,mCAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAED;;;OAGG;IAEH,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED;;;OAGG;IAEH,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,yBAAyB;IAEzB;;;;;;;;OAQG;IACO,SAAS,CACT,IAAe,EACf,WAAsB,EACtB,MAAmC;QAC5C,IAAI,CAAC,IAAI,EAAE;YACV,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,WAAW,EAAE;YACjB,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClD;QAED,gEAAgE;QAChE,IAAI,IAAI,YAAY,2BAAY,IAAI,WAAW,YAAY,2BAAY,EAAE;YACxE,IAAI,cAAqC,CAAC;YAC1C,0CAA0C;YAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE;gBACjD,IAAI,WAAW,CAAC,MAAM,YAAY,6BAAa,EAAE,EAAE,aAAa;oBAC/D,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC;oBACvC,oEAAoE;oBACpE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC1C,MAAM,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC;oBAC9B,IAAI,CAAC,EAAE;wBACN,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;qBACpB;iBACD;qBACI,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE;oBACxC,UAAU;iBACV;qBACI;oBACJ,UAAU;oBACV,IAAI,CAAC,cAAc,EAAE;wBACpB,cAAc,GAAG,IAAI,CAAC;qBACtB;iBACD;aACD;iBACI;gBACJ,IAAI,CAAC,cAAc,EAAE;oBACpB,cAAc,GAAG,IAAI,CAAC;iBACtB;aACD;YAED,OAAO,cAAc,CAAC;SACtB;QAED,IAAI,IAAI,YAAY,qCAAiB;eACjC,WAAW,YAAY,qCAAiB,EAAE;YAC7C,IAAI,cAAqC,CAAC;YAC1C,wBAAwB;YACxB,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,YAAY,EAAE;gBACjB,IAAI,CAAiB,CAAC;gBACtB,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE;oBACrE,mEAAmE;oBACnE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;oBACxC,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC;oBAC7B,IAAI,CAAC,EAAE;wBACN,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;qBACpB;iBACD;qBACI;oBACJ,IAAI,CAAC,cAAc,EAAE;wBACpB,cAAc,GAAG,IAAI,CAAC;qBACtB;iBACD;gBAED,OAAO,cAAc,CAAC;aACtB;YAED,4BAA4B;YAC5B,IAAI,IAAI,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU,EAAE;gBAC/C,IAAI,CAAC,cAAc,EAAE;oBACpB,cAAc,GAAG,IAAI,CAAC;iBACtB;gBAED,OAAO,cAAc,CAAC;aACtB;YAED,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;gBACnF,IAAI,UAAU,EAAE;oBACf,OAAO,UAAU,CAAC;iBAClB;aACD;YAED,OAAO,cAAc,CAAC;SACtB;QAED,8DAA8D;QAC9D,OAAO,IAAI,CAAC;IACb,CAAC;IAED,sCAAsC;IAC5B,eAAe,CAAC,CAAY;QACrC,IAAI,CAAC,YAAY,mBAAQ,EAAE;YAC1B,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,2BAAY,EAAE;gBAChE,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAiB,CAAC;gBACtC,IAAI,CAAC,CAAC,MAAM,YAAY,2BAAY,EAAE;oBAC1C,sEAAsE;oBACjE,OAAO,CAAC,CAAC,MAAM,CAAC;iBAChB;aACD;SACD;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,OAAe;QAC9B,wEAAwE;QACxE,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEjC,yCAAyC;QACzC,IAAI,MAAM,GAAY,EAAE,CAAC;QAEzB,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;YACzB,IAAI,KAAK,YAAY,mBAAQ,EAAE;gBAC9B,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5C,2DAA2D;gBAC3D,IAAI,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE;oBAC1C,IAAI,KAAK,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAC5D,IAAI,KAAK,KAAK,aAAK,CAAC,YAAY,EAAE;wBACjC,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,GAAG,GAAG,eAAe,GAAG,OAAO,CAAC,CAAC;qBAC7E;oBACD,IAAI,CAAC,GAAkB,IAAI,6BAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC9E,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACf;qBACI,IAAI,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE;oBAC/C,IAAI,SAAS,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAChE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;wBACrB,MAAM,IAAI,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,eAAe,GAAG,OAAO,CAAC,CAAC;qBAC5E;oBACD,IAAI,sBAAsB,GAAW,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;oBACpG,MAAM,CAAC,IAAI,CAAC,IAAI,2BAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,sBAAsB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;iBACpF;qBACI;oBACJ,MAAM,IAAI,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,eAAe,GAAG,OAAO,CAAC,CAAC;iBAC5E;aACD;iBACI;gBACJ,IAAI,SAAS,GAAG,KAAkB,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,yBAAW,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACjE,IAAI,CAAC,GAAU,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACvC,OAAO,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;oBAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;iBAC5B;aACD;SACD;QAEH,yCAAyC;QACvC,OAAO,MAAM,CAAC;IACf,CAAC;IAED,mFAAmF;IAC5E,KAAK,CAAC,OAAe;QAC3B,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,CAAC,GAAW,OAAO,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,GAAY,EAAE,CAAC;QACzB,IAAI,GAAO,CAAC;QACZ,sDAAsD;QACtD,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,KAAK,GAAa,EAAE,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;gBACvD,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;aAC5C;iBACI,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;gBAC3D,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;aAC3C;iBACI,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;gBAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;aACvB;iBACI,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;gBAC7C,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;aACtB;iBACI;gBACJ,CAAC,EAAE,CAAC;aACJ;SACD;QAEH,2BAA2B;QAC3B,+BAA+B;QAC/B,8BAA8B;QAC5B,IAAI,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,OAAO,CAAC,CAAC;SAC3D;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,OAAO,CAAC,CAAC;SAC5D;QAED,IAAI,KAAK,GAAW,MAAM,CAAC,MAAM,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC1B,MAAM,IAAI,KAAK,CAAC,0CAA0C,GAAG,OAAO,CAAC,CAAC;aACtE;SACD;QAED,0BAA0B;QAC1B,IAAI,KAAK,KAAK,CAAC,EAAE;YAChB,IAAI,IAAI,GAAW,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;SACjC;QAED,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,wCAAwC;YACzE,IAAI,IAAI,GAAW,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;SACjC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,uBAAuB;YACvB,IAAI,GAAG,GAAW,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,IAAI,WAAW,GAAW,GAAG,CAAC;YAC9B,IAAI,KAAyB,CAAC;YAC9B,IAAI,KAAK,GAAW,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,KAAK,IAAI,CAAC,EAAE;gBACf,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAChC,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;aACnD;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;gBAClB,0CAA0C;gBAC1C,IAAI,IAAI,GAAW,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjC;SACD;QACD,IAAI,KAAK,GAAG,CAAC,EAAE;YACd,IAAI,YAAY,GAAW,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC/D,IAAI,YAAY,GAAG,CAAC,EAAE,EAAE,wCAAwC;gBAC/D,IAAI,IAAI,GAAW,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjC;SACD;QAED,+DAA+D;QAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,GAAU,MAAM,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,YAAY,qBAAS,EAAE;gBAC3B,IAAI,SAAS,GAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAC1D,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;oBACrC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,qBAAS,CAAC,SAAS,CAAC,CAAC;iBACrC;aACD;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AA3UA;IADC,oBAAO;IACuB,WAAA,oBAAO,CAAA;oDASrC;AAiDD;IADC,oBAAO;oDAGP;AAOD;IADC,oBAAO;qDAGP;AAaD;IACE,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;IACP,WAAA,oBAAO,CAAA;wDAwFR;AA1QF,0DAwaC;AAED,WAAiB,uBAAuB;IACvC,MAAa,qBAAsB,SAAQ,KAAK;QAC/C,YAA0B,KAAY;YACrC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YADhB,UAAK,GAAL,KAAK,CAAO;QAEtC,CAAC;KACD;IAJY,6CAAqB,wBAIjC,CAAA;IAED,mDAAmD;IACnD,gEAAgE;IAChE,MAAa,kCAAmC,SAAQ,KAAK;QAC5D;YACC,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC7C,CAAC;KACD;IAJY,0DAAkC,qCAI9C,CAAA;AACF,CAAC,EAdgB,uBAAuB,GAAvB,+BAAuB,KAAvB,+BAAuB,QAcvC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\n\r\nimport { BailErrorStrategy } from \"../../BailErrorStrategy\";\r\nimport { CharStreams } from \"../../CharStreams\";\r\nimport { Chunk } from \"./Chunk\";\r\nimport { CommonTokenStream } from \"../../CommonTokenStream\";\r\nimport { Lexer } from \"../../Lexer\";\r\nimport { ListTokenSource } from \"../../ListTokenSource\";\r\nimport { MultiMap } from \"../../misc/MultiMap\";\r\nimport { NotNull } from \"../../Decorators\";\r\nimport { ParseCancellationException } from \"../../misc/ParseCancellationException\";\r\nimport { Parser } from \"../../Parser\";\r\nimport { ParserInterpreter } from \"../../ParserInterpreter\";\r\nimport { ParserRuleContext } from \"../../ParserRuleContext\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { ParseTreeMatch } from \"./ParseTreeMatch\";\r\nimport { ParseTreePattern } from \"./ParseTreePattern\";\r\nimport { RecognitionException } from \"../../RecognitionException\";\r\nimport { RuleNode } from \"../RuleNode\";\r\nimport { RuleTagToken } from \"./RuleTagToken\";\r\nimport { TagChunk } from \"./TagChunk\";\r\nimport { TerminalNode } from \"../TerminalNode\";\r\nimport { TextChunk } from \"./TextChunk\";\r\nimport { Token } from \"../../Token\";\r\nimport { TokenTagToken } from \"./TokenTagToken\";\r\n\r\n/**\r\n * A tree pattern matching mechanism for ANTLR {@link ParseTree}s.\r\n *\r\n * Patterns are strings of source input text with special tags representing\r\n * token or rule references such as:\r\n *\r\n * ```\r\n * <ID> = <expr>;\r\n * ```\r\n *\r\n * Given a pattern start rule such as `statement`, this object constructs\r\n * a {@link ParseTree} with placeholders for the `ID` and `expr`\r\n * subtree. Then the {@link #match} routines can compare an actual\r\n * {@link ParseTree} from a parse with this pattern. Tag `<ID>` matches\r\n * any `ID` token and tag `<expr>` references the result of the\r\n * `expr` rule (generally an instance of `ExprContext`.\r\n *\r\n * Pattern `x = 0;` is a similar pattern that matches the same pattern\r\n * except that it requires the identifier to be `x` and the expression to\r\n * be `0`.\r\n *\r\n * The {@link #matches} routines return `true` or `false` based\r\n * upon a match for the tree rooted at the parameter sent in. The\r\n * {@link #match} routines return a {@link ParseTreeMatch} object that\r\n * contains the parse tree, the parse tree pattern, and a map from tag name to\r\n * matched nodes (more below). A subtree that fails to match, returns with\r\n * {@link ParseTreeMatch#mismatchedNode} set to the first tree node that did not\r\n * match.\r\n *\r\n * For efficiency, you can compile a tree pattern in string form to a\r\n * {@link ParseTreePattern} object.\r\n *\r\n * See `TestParseTreeMatcher` for lots of examples.\r\n * {@link ParseTreePattern} has two static helper methods:\r\n * {@link ParseTreePattern#findAll} and {@link ParseTreePattern#match} that\r\n * are easy to use but not super efficient because they create new\r\n * {@link ParseTreePatternMatcher} objects each time and have to compile the\r\n * pattern in string form before using it.\r\n *\r\n * The lexer and parser that you pass into the {@link ParseTreePatternMatcher}\r\n * constructor are used to parse the pattern in string form. The lexer converts\r\n * the `<ID> = <expr>;` into a sequence of four tokens (assuming lexer\r\n * throws out whitespace or puts it on a hidden channel). Be aware that the\r\n * input stream is reset for the lexer (but not the parser; a\r\n * {@link ParserInterpreter} is created to parse the input.). Any user-defined\r\n * fields you have put into the lexer might get changed when this mechanism asks\r\n * it to scan the pattern string.\r\n *\r\n * Normally a parser does not accept token `<expr>` as a valid\r\n * `expr` but, from the parser passed in, we create a special version of\r\n * the underlying grammar representation (an {@link ATN}) that allows imaginary\r\n * tokens representing rules (`<expr>`) to match entire rules. We call\r\n * these *bypass alternatives*.\r\n *\r\n * Delimiters are `<`} and `>`}, with `\\` as the escape string\r\n * by default, but you can set them to whatever you want using\r\n * {@link #setDelimiters}. You must escape both start and stop strings\r\n * `\\<` and `\\>`.\r\n */\r\nexport class ParseTreePatternMatcher {\r\n\t/**\r\n\t * This is the backing field for `lexer`.\r\n\t */\r\n\tprivate _lexer: Lexer;\r\n\r\n\t/**\r\n\t * This is the backing field for `parser`.\r\n\t */\r\n\tprivate _parser: Parser;\r\n\r\n\tprotected start = \"<\";\r\n\tprotected stop = \">\";\r\n\tprotected escape = \"\\\\\"; // e.g., \\< and \\> must escape BOTH!\r\n\r\n\t/**\r\n\t * Regular expression corresponding to escape, for global replace\r\n\t */\r\n\tprotected escapeRE = /\\\\/g;\r\n\r\n\t/**\r\n\t * Constructs a {@link ParseTreePatternMatcher} or from a {@link Lexer} and\r\n\t * {@link Parser} object. The lexer input stream is altered for tokenizing\r\n\t * the tree patterns. The parser is used as a convenient mechanism to get\r\n\t * the grammar name, plus token, rule names.\r\n\t */\r\n\tconstructor(lexer: Lexer, parser: Parser) {\r\n\t\tthis._lexer = lexer;\r\n\t\tthis._parser = parser;\r\n\t}\r\n\r\n\t/**\r\n\t * Set the delimiters used for marking rule and token tags within concrete\r\n\t * syntax used by the tree pattern parser.\r\n\t *\r\n\t * @param start The start delimiter.\r\n\t * @param stop The stop delimiter.\r\n\t * @param escapeLeft The escape sequence to use for escaping a start or stop delimiter.\r\n\t *\r\n\t * @throws {@link Error} if `start` is not defined or empty.\r\n\t * @throws {@link Error} if `stop` is not defined or empty.\r\n\t */\r\n\tpublic setDelimiters(start: string, stop: string, escapeLeft: string): void {\r\n\t\tif (!start) {\r\n\t\t\tthrow new Error(\"start cannot be null or empty\");\r\n\t\t}\r\n\r\n\t\tif (!stop) {\r\n\t\t\tthrow new Error(\"stop cannot be null or empty\");\r\n\t\t}\r\n\r\n\t\tthis.start = start;\r\n\t\tthis.stop = stop;\r\n\t\tthis.escape = escapeLeft;\r\n\t\tthis.escapeRE = new RegExp(escapeLeft.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"g\");\r\n\t}\r\n\r\n\t/** Does `pattern` matched as rule `patternRuleIndex` match `tree`? */\r\n\tpublic matches(tree: ParseTree, pattern: string, patternRuleIndex: number): boolean;\r\n\r\n\t/** Does `pattern` matched as rule patternRuleIndex match tree? Pass in a\r\n\t *  compiled pattern instead of a string representation of a tree pattern.\r\n\t */\r\n\tpublic matches(tree: ParseTree, pattern: ParseTreePattern): boolean;\r\n\r\n\tpublic matches(tree: ParseTree, pattern: string | ParseTreePattern, patternRuleIndex: number = 0): boolean {\r\n\t\tif (typeof pattern === \"string\") {\r\n\t\t\tlet p: ParseTreePattern = this.compile(pattern, patternRuleIndex);\r\n\t\t\treturn this.matches(tree, p);\r\n\t\t} else {\r\n\t\t\tlet labels = new MultiMap<string, ParseTree>();\r\n\t\t\tlet mismatchedNode = this.matchImpl(tree, pattern.patternTree, labels);\r\n\t\t\treturn !mismatchedNode;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Compare `pattern` matched as rule `patternRuleIndex` against\r\n\t * `tree` and return a {@link ParseTreeMatch} object that contains the\r\n\t * matched elements, or the node at which the match failed.\r\n\t */\r\n\tpublic match(tree: ParseTree, pattern: string, patternRuleIndex: number): ParseTreeMatch;\r\n\r\n\t/**\r\n\t * Compare `pattern` matched against `tree` and return a\r\n\t * {@link ParseTreeMatch} object that contains the matched elements, or the\r\n\t * node at which the match failed. Pass in a compiled pattern instead of a\r\n\t * string representation of a tree pattern.\r\n\t */\r\n\tpublic match(tree: ParseTree, pattern: ParseTreePattern): ParseTreeMatch;\r\n\r\n\t// Implementation of match\r\n\t@NotNull\r\n\tpublic match(tree: ParseTree, @NotNull pattern: string | ParseTreePattern, patternRuleIndex: number = 0): ParseTreeMatch {\r\n\t\tif (typeof pattern === \"string\") {\r\n\t\t\tlet p: ParseTreePattern = this.compile(pattern, patternRuleIndex);\r\n\t\t\treturn this.match(tree, p);\r\n\t\t} else {\r\n\t\t\tlet labels = new MultiMap<string, ParseTree>();\r\n\t\t\tlet mismatchedNode = this.matchImpl(tree, pattern.patternTree, labels);\r\n\t\t\treturn new ParseTreeMatch(tree, pattern, labels, mismatchedNode);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * For repeated use of a tree pattern, compile it to a\r\n\t * {@link ParseTreePattern} using this method.\r\n\t */\r\n\tpublic compile(pattern: string, patternRuleIndex: number): ParseTreePattern {\r\n\t\tlet tokenList = this.tokenize(pattern);\r\n\t\tlet tokenSrc = new ListTokenSource(tokenList);\r\n\t\tlet tokens = new CommonTokenStream(tokenSrc);\r\n\t\tconst parser = this._parser;\r\n\r\n\t\tlet parserInterp = new ParserInterpreter(\r\n\t\t\tparser.grammarFileName,\r\n\t\t\tparser.vocabulary,\r\n\t\t\tparser.ruleNames,\r\n\t\t\tparser.getATNWithBypassAlts(),\r\n\t\t\ttokens);\r\n\r\n\t\tlet tree: ParseTree;\r\n\t\ttry {\r\n\t\t\tparserInterp.errorHandler = new BailErrorStrategy();\r\n\t\t\ttree = parserInterp.parse(patternRuleIndex);\r\n//\t\t\tSystem.out.println(\"pattern tree = \"+tree.toStringTree(parserInterp));\r\n\t\t} catch (e) {\r\n\t\t\tif (e instanceof ParseCancellationException) {\r\n\t\t\t\tthrow e.getCause();\r\n\t\t\t} else if (e instanceof RecognitionException) {\r\n\t\t\t\tthrow e;\r\n\t\t\t} else if (e instanceof Error) {\r\n\t\t\t\tthrow new ParseTreePatternMatcher.CannotInvokeStartRule(e);\r\n\t\t\t} else {\r\n\t\t\t\tthrow e;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Make sure tree pattern compilation checks for a complete parse\r\n\t\tif (tokens.LA(1) !== Token.EOF) {\r\n\t\t\tthrow new ParseTreePatternMatcher.StartRuleDoesNotConsumeFullPattern();\r\n\t\t}\r\n\r\n\t\treturn new ParseTreePattern(this, pattern, patternRuleIndex, tree);\r\n\t}\r\n\r\n\t/**\r\n\t * Used to convert the tree pattern string into a series of tokens. The\r\n\t * input stream is reset.\r\n\t */\r\n\t@NotNull\r\n\tget lexer(): Lexer {\r\n\t\treturn this._lexer;\r\n\t}\r\n\r\n\t/**\r\n\t * Used to collect to the grammar file name, token names, rule names for\r\n\t * used to parse the pattern into a parse tree.\r\n\t */\r\n\t@NotNull\r\n\tget parser(): Parser {\r\n\t\treturn this._parser;\r\n\t}\r\n\r\n\t// ---- SUPPORT CODE ----\r\n\r\n\t/**\r\n\t * Recursively walk `tree` against `patternTree`, filling\r\n\t * `match.`{@link ParseTreeMatch#labels labels}.\r\n\t *\r\n\t * @returns the first node encountered in `tree` which does not match\r\n\t * a corresponding node in `patternTree`, or `undefined` if the match\r\n\t * was successful. The specific node returned depends on the matching\r\n\t * algorithm used by the implementation, and may be overridden.\r\n\t */\r\n\tprotected matchImpl(\r\n\t\t@NotNull tree: ParseTree,\r\n\t\t@NotNull patternTree: ParseTree,\r\n\t\t@NotNull labels: MultiMap<string, ParseTree>): ParseTree | undefined {\r\n\t\tif (!tree) {\r\n\t\t\tthrow new TypeError(\"tree cannot be null\");\r\n\t\t}\r\n\r\n\t\tif (!patternTree) {\r\n\t\t\tthrow new TypeError(\"patternTree cannot be null\");\r\n\t\t}\r\n\r\n\t\t// x and <ID>, x and y, or x and x; or could be mismatched types\r\n\t\tif (tree instanceof TerminalNode && patternTree instanceof TerminalNode) {\r\n\t\t\tlet mismatchedNode: ParseTree | undefined;\r\n\t\t\t// both are tokens and they have same type\r\n\t\t\tif (tree.symbol.type === patternTree.symbol.type) {\r\n\t\t\t\tif (patternTree.symbol instanceof TokenTagToken) { // x and <ID>\r\n\t\t\t\t\tlet tokenTagToken = patternTree.symbol;\r\n\t\t\t\t\t// track label->list-of-nodes for both token name and label (if any)\r\n\t\t\t\t\tlabels.map(tokenTagToken.tokenName, tree);\r\n\t\t\t\t\tconst l = tokenTagToken.label;\r\n\t\t\t\t\tif (l) {\r\n\t\t\t\t\t\tlabels.map(l, tree);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse if (tree.text === patternTree.text) {\r\n\t\t\t\t\t// x and x\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\t// x and y\r\n\t\t\t\t\tif (!mismatchedNode) {\r\n\t\t\t\t\t\tmismatchedNode = tree;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tif (!mismatchedNode) {\r\n\t\t\t\t\tmismatchedNode = tree;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn mismatchedNode;\r\n\t\t}\r\n\r\n\t\tif (tree instanceof ParserRuleContext\r\n\t\t\t&& patternTree instanceof ParserRuleContext) {\r\n\t\t\tlet mismatchedNode: ParseTree | undefined;\r\n\t\t\t// (expr ...) and <expr>\r\n\t\t\tlet ruleTagToken = this.getRuleTagToken(patternTree);\r\n\t\t\tif (ruleTagToken) {\r\n\t\t\t\tlet m: ParseTreeMatch;\r\n\t\t\t\tif (tree.ruleContext.ruleIndex === patternTree.ruleContext.ruleIndex) {\r\n\t\t\t\t\t// track label->list-of-nodes for both rule name and label (if any)\r\n\t\t\t\t\tlabels.map(ruleTagToken.ruleName, tree);\r\n\t\t\t\t\tconst l = ruleTagToken.label;\r\n\t\t\t\t\tif (l) {\r\n\t\t\t\t\t\tlabels.map(l, tree);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tif (!mismatchedNode) {\r\n\t\t\t\t\t\tmismatchedNode = tree;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn mismatchedNode;\r\n\t\t\t}\r\n\r\n\t\t\t// (expr ...) and (expr ...)\r\n\t\t\tif (tree.childCount !== patternTree.childCount) {\r\n\t\t\t\tif (!mismatchedNode) {\r\n\t\t\t\t\tmismatchedNode = tree;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn mismatchedNode;\r\n\t\t\t}\r\n\r\n\t\t\tlet n: number = tree.childCount;\r\n\t\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\t\tlet childMatch = this.matchImpl(tree.getChild(i), patternTree.getChild(i), labels);\r\n\t\t\t\tif (childMatch) {\r\n\t\t\t\t\treturn childMatch;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn mismatchedNode;\r\n\t\t}\r\n\r\n\t\t// if nodes aren't both tokens or both rule nodes, can't match\r\n\t\treturn tree;\r\n\t}\r\n\r\n\t/** Is `t` `(expr <expr>)` subtree? */\r\n\tprotected getRuleTagToken(t: ParseTree): RuleTagToken | undefined {\r\n\t\tif (t instanceof RuleNode) {\r\n\t\t\tif (t.childCount === 1 && t.getChild(0) instanceof TerminalNode) {\r\n\t\t\t\tlet c = t.getChild(0) as TerminalNode;\r\n\t\t\t\tif (c.symbol instanceof RuleTagToken) {\r\n//\t\t\t\t\tSystem.out.println(\"rule tag subtree \"+t.toStringTree(parser));\r\n\t\t\t\t\treturn c.symbol;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\tpublic tokenize(pattern: string): Token[] {\r\n\t\t// split pattern into chunks: sea (raw input) and islands (<ID>, <expr>)\r\n\t\tlet chunks = this.split(pattern);\r\n\r\n\t\t// create token stream from text and tags\r\n\t\tlet tokens: Token[] = [];\r\n\r\n\t\tfor (let chunk of chunks) {\r\n\t\t\tif (chunk instanceof TagChunk) {\r\n\t\t\t\tlet tagChunk = chunk;\r\n\t\t\t\tconst firstChar = tagChunk.tag.substr(0, 1);\r\n\t\t\t\t// add special rule token or conjure up new token from name\r\n\t\t\t\tif (firstChar === firstChar.toUpperCase()) {\r\n\t\t\t\t\tlet ttype: number = this._parser.getTokenType(tagChunk.tag);\r\n\t\t\t\t\tif (ttype === Token.INVALID_TYPE) {\r\n\t\t\t\t\t\tthrow new Error(\"Unknown token \" + tagChunk.tag + \" in pattern: \" + pattern);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet t: TokenTagToken = new TokenTagToken(tagChunk.tag, ttype, tagChunk.label);\r\n\t\t\t\t\ttokens.push(t);\r\n\t\t\t\t}\r\n\t\t\t\telse if (firstChar === firstChar.toLowerCase()) {\r\n\t\t\t\t\tlet ruleIndex: number = this._parser.getRuleIndex(tagChunk.tag);\r\n\t\t\t\t\tif (ruleIndex === -1) {\r\n\t\t\t\t\t\tthrow new Error(\"Unknown rule \" + tagChunk.tag + \" in pattern: \" + pattern);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet ruleImaginaryTokenType: number = this._parser.getATNWithBypassAlts().ruleToTokenType[ruleIndex];\r\n\t\t\t\t\ttokens.push(new RuleTagToken(tagChunk.tag, ruleImaginaryTokenType, tagChunk.label));\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tthrow new Error(\"invalid tag: \" + tagChunk.tag + \" in pattern: \" + pattern);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlet textChunk = chunk as TextChunk;\r\n\t\t\t\tthis._lexer.inputStream = CharStreams.fromString(textChunk.text);\r\n\t\t\t\tlet t: Token = this._lexer.nextToken();\r\n\t\t\t\twhile (t.type !== Token.EOF) {\r\n\t\t\t\t\ttokens.push(t);\r\n\t\t\t\t\tt = this._lexer.nextToken();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n//\t\tSystem.out.println(\"tokens=\"+tokens);\r\n\t\treturn tokens;\r\n\t}\r\n\r\n\t/** Split `<ID> = <e:expr> ;` into 4 chunks for tokenizing by {@link #tokenize}. */\r\n\tpublic split(pattern: string): Chunk[] {\r\n\t\tlet p: number = 0;\r\n\t\tlet n: number = pattern.length;\r\n\t\tlet chunks: Chunk[] = [];\r\n\t\tlet buf: \"\";\r\n\t\t// find all start and stop indexes first, then collect\r\n\t\tlet starts: number[] = [];\r\n\t\tlet stops: number[] = [];\r\n\t\twhile (p < n) {\r\n\t\t\tif (p === pattern.indexOf(this.escape + this.start, p)) {\r\n\t\t\t\tp += this.escape.length + this.start.length;\r\n\t\t\t}\r\n\t\t\telse if (p === pattern.indexOf(this.escape + this.stop, p)) {\r\n\t\t\t\tp += this.escape.length + this.stop.length;\r\n\t\t\t}\r\n\t\t\telse if (p === pattern.indexOf(this.start, p)) {\r\n\t\t\t\tstarts.push(p);\r\n\t\t\t\tp += this.start.length;\r\n\t\t\t}\r\n\t\t\telse if (p === pattern.indexOf(this.stop, p)) {\r\n\t\t\t\tstops.push(p);\r\n\t\t\t\tp += this.stop.length;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tp++;\r\n\t\t\t}\r\n\t\t}\r\n\r\n//\t\tSystem.out.println(\"\");\r\n//\t\tSystem.out.println(starts);\r\n//\t\tSystem.out.println(stops);\r\n\t\tif (starts.length > stops.length) {\r\n\t\t\tthrow new Error(\"unterminated tag in pattern: \" + pattern);\r\n\t\t}\r\n\r\n\t\tif (starts.length < stops.length) {\r\n\t\t\tthrow new Error(\"missing start tag in pattern: \" + pattern);\r\n\t\t}\r\n\r\n\t\tlet ntags: number = starts.length;\r\n\t\tfor (let i = 0; i < ntags; i++) {\r\n\t\t\tif (starts[i] >= stops[i]) {\r\n\t\t\t\tthrow new Error(\"tag delimiters out of order in pattern: \" + pattern);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// collect into chunks now\r\n\t\tif (ntags === 0) {\r\n\t\t\tlet text: string = pattern.substring(0, n);\r\n\t\t\tchunks.push(new TextChunk(text));\r\n\t\t}\r\n\r\n\t\tif (ntags > 0 && starts[0] > 0) { // copy text up to first tag into chunks\r\n\t\t\tlet text: string = pattern.substring(0, starts[0]);\r\n\t\t\tchunks.push(new TextChunk(text));\r\n\t\t}\r\n\t\tfor (let i = 0; i < ntags; i++) {\r\n\t\t\t// copy inside of <tag>\r\n\t\t\tlet tag: string = pattern.substring(starts[i] + this.start.length, stops[i]);\r\n\t\t\tlet ruleOrToken: string = tag;\r\n\t\t\tlet label: string | undefined;\r\n\t\t\tlet colon: number = tag.indexOf(\":\");\r\n\t\t\tif (colon >= 0) {\r\n\t\t\t\tlabel = tag.substring(0, colon);\r\n\t\t\t\truleOrToken = tag.substring(colon + 1, tag.length);\r\n\t\t\t}\r\n\t\t\tchunks.push(new TagChunk(ruleOrToken, label));\r\n\t\t\tif (i + 1 < ntags) {\r\n\t\t\t\t// copy from end of <tag> to start of next\r\n\t\t\t\tlet text: string = pattern.substring(stops[i] + this.stop.length, starts[i + 1]);\r\n\t\t\t\tchunks.push(new TextChunk(text));\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (ntags > 0) {\r\n\t\t\tlet afterLastTag: number = stops[ntags - 1] + this.stop.length;\r\n\t\t\tif (afterLastTag < n) { // copy text from end of last tag to end\r\n\t\t\t\tlet text: string = pattern.substring(afterLastTag, n);\r\n\t\t\t\tchunks.push(new TextChunk(text));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// strip out the escape sequences from text chunks but not tags\r\n\t\tfor (let i = 0; i < chunks.length; i++) {\r\n\t\t\tlet c: Chunk = chunks[i];\r\n\t\t\tif (c instanceof TextChunk) {\r\n\t\t\t\tlet unescaped: string = c.text.replace(this.escapeRE, \"\");\r\n\t\t\t\tif (unescaped.length < c.text.length) {\r\n\t\t\t\t\tchunks[i] = new TextChunk(unescaped);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn chunks;\r\n\t}\r\n}\r\n\r\nexport namespace ParseTreePatternMatcher {\r\n\texport class CannotInvokeStartRule extends Error {\r\n\t\tpublic constructor(public error: Error) {\r\n\t\t\tsuper(`CannotInvokeStartRule: ${error}`);\r\n\t\t}\r\n\t}\r\n\r\n\t// Fixes https://github.com/antlr/antlr4/issues/413\r\n\t// \"Tree pattern compilation doesn't check for a complete parse\"\r\n\texport class StartRuleDoesNotConsumeFullPattern extends Error {\r\n\t\tconstructor() {\r\n\t\t\tsuper(\"StartRuleDoesNotConsumeFullPattern\");\r\n\t\t}\r\n\t}\r\n}\r\n"]}