{"version": 3, "file": "RuleTagToken.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/RuleTagToken.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,iDAAqD;AACrD,uCAAoC;AAGpC;;;;GAIG;AACH,IAAa,YAAY,GAAzB,MAAa,YAAY;IAexB;;;;;;;;;;;OAWG;IACH,YAAqB,QAAgB,EAAE,eAAuB,EAAE,KAAc;QAC7E,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IAED;;;;OAIG;IAEH,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED;;;;OAIG;IAEH,IAAI,OAAO;QACV,OAAO,aAAK,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IAEH,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACxB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;SACtD;QAED,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IAEH,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IAEH,IAAI,IAAI;QACP,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;OAIG;IAEH,IAAI,kBAAkB;QACrB,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IAEH,IAAI,UAAU;QACb,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IAEH,IAAI,UAAU;QACb,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IAEH,IAAI,SAAS;QACZ,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IAEH,IAAI,WAAW;QACd,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;OAIG;IAEH,IAAI,WAAW;QACd,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IAEI,QAAQ;QACd,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;IACpD,CAAC;CACD,CAAA;AAlIA;IADC,oBAAO;4CAGP;AAkBD;IADC,qBAAQ;2CAGR;AASD;IADC,qBAAQ;wCAOR;AASD;IADC,qBAAQ;wCAGR;AAQD;IADC,qBAAQ;wCAGR;AAQD;IADC,qBAAQ;sDAGR;AAQD;IADC,qBAAQ;8CAGR;AAQD;IADC,qBAAQ;8CAGR;AAQD;IADC,qBAAQ;6CAGR;AAQD;IADC,qBAAQ;+CAGR;AAQD;IADC,qBAAQ;+CAGR;AASD;IADC,qBAAQ;4CAGR;AA5KW,YAAY;IA2BX,WAAA,oBAAO,CAAA;GA3BR,YAAY,CA6KxB;AA7KY,oCAAY", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:46.0343500-07:00\r\n\r\nimport { CharStream } from \"../../CharStream\";\r\nimport { NotNull, Override } from \"../../Decorators\";\r\nimport { Token } from \"../../Token\";\r\nimport { TokenSource } from \"../../TokenSource\";\r\n\r\n/**\r\n * A {@link Token} object representing an entire subtree matched by a parser\r\n * rule; e.g., `<expr>`. These tokens are created for {@link TagChunk}\r\n * chunks where the tag corresponds to a parser rule.\r\n */\r\nexport class RuleTagToken implements Token {\r\n\t/**\r\n\t * This is the backing field for `ruleName`.\r\n\t */\r\n\tprivate _ruleName: string;\r\n\t/**\r\n\t * The token type for the current token. This is the token type assigned to\r\n\t * the bypass alternative for the rule during ATN deserialization.\r\n\t */\r\n\tprivate bypassTokenType: number;\r\n\t/**\r\n\t * This is the backing field for `label`.\r\n\t */\r\n\tprivate _label?: string;\r\n\r\n\t/**\r\n\t * Constructs a new instance of {@link RuleTagToken} with the specified rule\r\n\t * name, bypass token type, and label.\r\n\t *\r\n\t * @param ruleName The name of the parser rule this rule tag matches.\r\n\t * @param bypassTokenType The bypass token type assigned to the parser rule.\r\n\t * @param label The label associated with the rule tag, or `undefined` if\r\n\t * the rule tag is unlabeled.\r\n\t *\r\n\t * @exception IllegalArgumentException if `ruleName` is not defined\r\n\t * or empty.\r\n\t */\r\n\tconstructor(@NotNull ruleName: string, bypassTokenType: number, label?: string) {\r\n\t\tif (ruleName == null || ruleName.length === 0) {\r\n\t\t\tthrow new Error(\"ruleName cannot be null or empty.\");\r\n\t\t}\r\n\r\n\t\tthis._ruleName = ruleName;\r\n\t\tthis.bypassTokenType = bypassTokenType;\r\n\t\tthis._label = label;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the name of the rule associated with this rule tag.\r\n\t *\r\n\t * @returns The name of the parser rule associated with this rule tag.\r\n\t */\r\n\t@NotNull\r\n\tget ruleName(): string {\r\n\t\treturn this._ruleName;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the label associated with the rule tag.\r\n\t *\r\n\t * @returns The name of the label associated with the rule tag, or\r\n\t * `undefined` if this is an unlabeled rule tag.\r\n\t */\r\n\tget label(): string | undefined {\r\n\t\treturn this._label;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * Rule tag tokens are always placed on the {@link #DEFAULT_CHANNEL}.\r\n\t */\r\n\t@Override\r\n\tget channel(): number {\r\n\t\treturn Token.DEFAULT_CHANNEL;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * This method returns the rule tag formatted with `<` and `>`\r\n\t * delimiters.\r\n\t */\r\n\t@Override\r\n\tget text(): string {\r\n\t\tif (this._label != null) {\r\n\t\t\treturn \"<\" + this._label + \":\" + this._ruleName + \">\";\r\n\t\t}\r\n\r\n\t\treturn \"<\" + this._ruleName + \">\";\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * Rule tag tokens have types assigned according to the rule bypass\r\n\t * transitions created during ATN deserialization.\r\n\t */\r\n\t@Override\r\n\tget type(): number {\r\n\t\treturn this.bypassTokenType;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns 0.\r\n\t */\r\n\t@Override\r\n\tget line(): number {\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns -1.\r\n\t */\r\n\t@Override\r\n\tget charPositionInLine(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns -1.\r\n\t */\r\n\t@Override\r\n\tget tokenIndex(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns -1.\r\n\t */\r\n\t@Override\r\n\tget startIndex(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns -1.\r\n\t */\r\n\t@Override\r\n\tget stopIndex(): number {\r\n\t\treturn -1;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns `undefined`.\r\n\t */\r\n\t@Override\r\n\tget tokenSource(): TokenSource | undefined {\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} always returns `undefined`.\r\n\t */\r\n\t@Override\r\n\tget inputStream(): CharStream | undefined {\r\n\t\treturn undefined;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The implementation for {@link RuleTagToken} returns a string of the form\r\n\t * `ruleName:bypassTokenType`.\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\treturn this._ruleName + \":\" + this.bypassTokenType;\r\n\t}\r\n}\r\n"]}