{"version": 3, "file": "TagChunk.js", "sourceRoot": "", "sources": ["../../../../src/tree/pattern/TagChunk.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,mCAAgC;AAChC,iDAAqD;AAErD;;;;;;;;;;;GAWG;AACH,MAAa,QAAS,SAAQ,aAAK;IAUlC;;;;;;;;;;;OAWG;IACH,YAAY,GAAW,EAAE,KAAc;QACtC,KAAK,EAAE,CAAC;QAER,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IAED;;;;OAIG;IAEH,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED;;;;OAIG;IAEI,QAAQ;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;SACrC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;CACD;AA3BA;IADC,oBAAO;mCAGP;AAkBD;IADC,qBAAQ;wCAOR;AAjEF,4BAkEC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:46.1670669-07:00\r\n\r\nimport { Chunk } from \"./Chunk\";\r\nimport { NotNull, Override } from \"../../Decorators\";\r\n\r\n/**\r\n * Represents a placeholder tag in a tree pattern. A tag can have any of the\r\n * following forms.\r\n *\r\n * * `expr`: An unlabeled placeholder for a parser rule `expr`.\r\n * * `ID`: An unlabeled placeholder for a token of type `ID`.\r\n * * `e:expr`: A labeled placeholder for a parser rule `expr`.\r\n * * `id:ID`: A labeled placeholder for a token of type `ID`.\r\n *\r\n * This class does not perform any validation on the tag or label names aside\r\n * from ensuring that the tag is a defined, non-empty string.\r\n */\r\nexport class TagChunk extends Chunk {\r\n\t/**\r\n\t * This is the backing field for `tag`.\r\n\t */\r\n\tprivate _tag: string;\r\n\t/**\r\n\t * This is the backing field for `label`.\r\n\t */\r\n\tprivate _label?: string;\r\n\r\n\t/**\r\n\t * Construct a new instance of {@link TagChunk} using the specified label\r\n\t * and tag.\r\n\t *\r\n\t * @param label The label for the tag. If this is `undefined`, the\r\n\t * {@link TagChunk} represents an unlabeled tag.\r\n\t * @param tag The tag, which should be the name of a parser rule or token\r\n\t * type.\r\n\t *\r\n\t * @exception IllegalArgumentException if `tag` is not defined or\r\n\t * empty.\r\n\t */\r\n\tconstructor(tag: string, label?: string) {\r\n\t\tsuper();\r\n\r\n\t\tif (tag == null || tag.length === 0) {\r\n\t\t\tthrow new Error(\"tag cannot be null or empty\");\r\n\t\t}\r\n\r\n\t\tthis._tag = tag;\r\n\t\tthis._label = label;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the tag for this chunk.\r\n\t *\r\n\t * @returns The tag for the chunk.\r\n\t */\r\n\t@NotNull\r\n\tget tag(): string {\r\n\t\treturn this._tag;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the label, if any, assigned to this chunk.\r\n\t *\r\n\t * @returns The label assigned to this chunk, or `undefined` if no label is\r\n\t * assigned to the chunk.\r\n\t */\r\n\tget label(): string | undefined {\r\n\t\treturn this._label;\r\n\t}\r\n\r\n\t/**\r\n\t * This method returns a text representation of the tag chunk. Labeled tags\r\n\t * are returned in the form `label:tag`, and unlabeled tags are\r\n\t * returned as just the tag name.\r\n\t */\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tif (this._label != null) {\r\n\t\t\treturn this._label + \":\" + this._tag;\r\n\t\t}\r\n\r\n\t\treturn this._tag;\r\n\t}\r\n}\r\n"]}