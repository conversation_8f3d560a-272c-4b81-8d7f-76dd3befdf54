{"version": 3, "file": "RuleNode.js", "sourceRoot": "", "sources": ["../../../src/tree/RuleNode.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAUH,MAAsB,QAAQ;CAqB7B;AArBD,4BAqBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.9232756-07:00\r\n\r\nimport { RuleContext } from \"../RuleContext\";\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { ParseTreeVisitor } from \"./ParseTreeVisitor\";\r\nimport { Parser } from \"../Parser\";\r\nimport { Interval } from \"../misc/Interval\";\r\n\r\nexport abstract class RuleNode implements ParseTree {\r\n\tpublic abstract readonly ruleContext: RuleContext;\r\n\r\n\t//@Override\r\n\tpublic abstract readonly parent: RuleNode | undefined;\r\n\r\n\tpublic abstract setParent(parent: RuleContext): void;\r\n\r\n\tpublic abstract getChild(i: number): ParseTree;\r\n\r\n\tpublic abstract accept<T>(visitor: ParseTreeVisitor<T>): T;\r\n\r\n\tpublic abstract readonly text: string;\r\n\r\n\tpublic abstract toStringTree(parser?: Parser | undefined): string;\r\n\r\n\tpublic abstract readonly sourceInterval: Interval;\r\n\r\n\tpublic abstract readonly payload: any;\r\n\r\n\tpublic abstract readonly childCount: number;\r\n}\r\n"]}