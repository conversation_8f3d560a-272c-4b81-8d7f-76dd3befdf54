{"version": 3, "file": "ParseTreeProperty.js", "sourceRoot": "", "sources": ["../../../src/tree/ParseTreeProperty.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAMH;;;;;;;;;;;;;;;GAeG;AACH,MAAa,iBAAiB;IAG7B,YAAY,OAAe,mBAAmB;QAC7C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,GAAG,CAAC,IAAe;QACzB,OAAQ,IAAY,CAAC,IAAI,CAAC,OAAO,CAAM,CAAC;IACzC,CAAC;IAEM,GAAG,CAAC,IAAe,EAAE,KAAQ;QAClC,IAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAEM,UAAU,CAAC,IAAe;QAChC,IAAI,MAAM,GAAI,IAAY,CAAC,IAAI,CAAC,OAAO,CAAM,CAAC;QAC9C,OAAQ,IAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AApBD,8CAoBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.6782223-07:00\r\n\r\nimport { ParseTree } from \"./ParseTree\";\r\n\r\n/**\r\n * Associate a property with a parse tree node. Useful with parse tree listeners\r\n * that need to associate values with particular tree nodes, kind of like\r\n * specifying a return value for the listener event method that visited a\r\n * particular node. Example:\r\n *\r\n * ```\r\n * ParseTreeProperty<Integer> values = new ParseTreeProperty<Integer>();\r\n * values.put(tree, 36);\r\n * int x = values.get(tree);\r\n * values.removeFrom(tree);\r\n * ```\r\n *\r\n * You would make one decl (values here) in the listener and use lots of times\r\n * in your event methods.\r\n */\r\nexport class ParseTreeProperty<V> {\r\n\tprivate _symbol: symbol;\r\n\r\n\tconstructor(name: string = \"ParseTreeProperty\") {\r\n\t\tthis._symbol = Symbol(name);\r\n\t}\r\n\r\n\tpublic get(node: ParseTree): V {\r\n\t\treturn (node as any)[this._symbol] as V;\r\n\t}\r\n\r\n\tpublic set(node: ParseTree, value: V): void {\r\n\t\t(node as any)[this._symbol] = value;\r\n\t}\r\n\r\n\tpublic removeFrom(node: ParseTree): V {\r\n\t\tlet result = (node as any)[this._symbol] as V;\r\n\t\tdelete (node as any)[this._symbol];\r\n\t\treturn result;\r\n\t}\r\n}\r\n"]}