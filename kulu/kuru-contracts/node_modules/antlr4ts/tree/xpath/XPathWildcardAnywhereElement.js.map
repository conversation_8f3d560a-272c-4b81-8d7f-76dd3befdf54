{"version": 3, "file": "XPathWildcardAnywhereElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathWildcardAnywhereElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA4C;AAG5C,oCAAiC;AACjC,mCAAgC;AAChC,iDAA8C;AAE9C,MAAa,4BAA6B,SAAQ,2BAAY;IAC7D;QACC,KAAK,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAC;IACvB,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,gCAAgC;YAChC,OAAO,EAAE,CAAC;SACV;QACD,OAAO,aAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;CACD;AAPA;IADC,qBAAQ;4DAOR;AAZF,oEAaC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { TerminalNode } from \"../TerminalNode\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPath } from \"./XPath\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\nexport class XPathWildcardAnywhereElement extends XPathElement {\r\n\tconstructor() {\r\n\t\tsuper(XPath.WILDCARD);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\tif (this.invert) {\r\n\t\t\t// !* is weird but valid (empty)\r\n\t\t\treturn [];\r\n\t\t}\r\n\t\treturn Trees.getDescendants(t);\r\n\t}\r\n}\r\n"]}