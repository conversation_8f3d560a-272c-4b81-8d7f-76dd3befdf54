{"version": 3, "file": "XPathTokenElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathTokenElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA4C;AAE5C,kDAA+C;AAC/C,oCAAiC;AACjC,iDAA8C;AAE9C,MAAa,iBAAkB,SAAQ,2BAAY;IAElD,YAAY,SAAiB,EAAE,SAAiB;QAC/C,KAAK,CAAC,SAAS,CAAC,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,+CAA+C;QAC/C,IAAI,KAAK,GAAgB,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,YAAY,2BAAY,EAAE;gBAC9B,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;oBACrD,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;oBACnD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACd;aACD;SACD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAbA;IADC,qBAAQ;iDAaR;AApBF,8CAqBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { TerminalNode } from \"../TerminalNode\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\nexport class XPathTokenElement extends XPathElement {\r\n\tprotected tokenType: number;\r\n\tconstructor(tokenName: string, tokenType: number) {\r\n\t\tsuper(tokenName);\r\n\t\tthis.tokenType = tokenType;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\t// return all children of t that match nodeName\r\n\t\tlet nodes: ParseTree[] = [];\r\n\t\tfor (let c of Trees.getChildren(t)) {\r\n\t\t\tif (c instanceof TerminalNode) {\r\n\t\t\t\tif ((c.symbol.type === this.tokenType && !this.invert) ||\r\n\t\t\t\t\t(c.symbol.type !== this.tokenType && this.invert)) {\r\n\t\t\t\t\tnodes.push(c);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn nodes;\r\n\t}\r\n}\r\n"]}