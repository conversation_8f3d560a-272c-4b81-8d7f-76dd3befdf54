{"version": 3, "file": "XPathWildcardElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathWildcardElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA4C;AAG5C,oCAAiC;AACjC,mCAAgC;AAChC,iDAA8C;AAE9C,MAAa,oBAAqB,SAAQ,2BAAY;IACrD;QACC,KAAK,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAC;IACvB,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,IAAI,IAAI,GAAgB,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,gCAAgC;YAChC,OAAO,IAAI,CAAC;SACZ;QACD,KAAK,IAAI,CAAC,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACb;QACD,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAXA;IADC,qBAAQ;oDAWR;AAhBF,oDAiBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { TerminalNode } from \"../TerminalNode\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPath } from \"./XPath\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\nexport class XPathWildcardElement extends XPathElement {\r\n\tconstructor() {\r\n\t\tsuper(XPath.WILDCARD);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\tlet kids: ParseTree[] = [];\r\n\t\tif (this.invert) {\r\n\t\t\t// !* is weird but valid (empty)\r\n\t\t\treturn kids;\r\n\t\t}\r\n\t\tfor (let c of Trees.getChildren(t)) {\r\n\t\t\tkids.push(c);\r\n\t\t}\r\n\t\treturn kids;\r\n\t}\r\n}\r\n"]}