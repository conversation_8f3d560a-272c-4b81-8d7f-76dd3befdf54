{"version": 3, "file": "XPathRuleElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathRuleElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,+DAA4D;AAC5D,iDAA4C;AAE5C,oCAAiC;AACjC,iDAA8C;AAE9C,MAAa,gBAAiB,SAAQ,2BAAY;IAEjD,YAAY,QAAgB,EAAE,SAAiB;QAC9C,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,+CAA+C;QAC/C,IAAI,KAAK,GAAgB,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,YAAY,qCAAiB,EAAE;gBACnC,IAAI,CAAC,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;oBACnD,CAAC,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;oBACjD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACd;aACD;SACD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAbA;IADC,qBAAQ;gDAaR;AApBF,4CAqBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { ParserRuleContext } from \"../../ParserRuleContext\";\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\nexport class XPathRuleElement extends XPathElement {\r\n\tprotected ruleIndex: number;\r\n\tconstructor(ruleName: string, ruleIndex: number) {\r\n\t\tsuper(ruleName);\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\t// return all children of t that match nodeName\r\n\t\tlet nodes: ParseTree[] = [];\r\n\t\tfor (let c of Trees.getChildren(t)) {\r\n\t\t\tif (c instanceof ParserRuleContext) {\r\n\t\t\t\tif ((c.ruleIndex === this.ruleIndex && !this.invert) ||\r\n\t\t\t\t\t(c.ruleIndex !== this.ruleIndex && this.invert)) {\r\n\t\t\t\t\tnodes.push(c);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn nodes;\r\n\t}\r\n}\r\n"]}