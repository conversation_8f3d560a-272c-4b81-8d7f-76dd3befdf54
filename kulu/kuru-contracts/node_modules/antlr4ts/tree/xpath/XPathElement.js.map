{"version": 3, "file": "XPathElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA4C;AAG5C,MAAsB,YAAY;IAIjC;;OAEG;IACH,YAAY,QAAgB;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IASM,QAAQ;QACd,IAAI,GAAG,GAAW,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACzC,IAAI,SAAS,GAAW,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;QAChD,OAAO,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;IACpD,CAAC;CACD;AALA;IADC,qBAAQ;4CAKR;AAvBF,oCAwBC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\n\r\nexport abstract class XPathElement {\r\n\tprotected nodeName: string;\r\n\tpublic invert: boolean;\r\n\r\n\t/** Construct element like `/ID` or `ID` or `/*` etc...\r\n\t *  op is null if just node\r\n\t */\r\n\tconstructor(nodeName: string) {\r\n\t\tthis.nodeName = nodeName;\r\n\t\tthis.invert = false;\r\n\t}\r\n\r\n\t/**\r\n\t * Given tree rooted at `t` return all nodes matched by this path\r\n\t * element.\r\n\t */\r\n\tpublic abstract evaluate(t: ParseTree): ParseTree[];\r\n\r\n\t@Override\r\n\tpublic toString(): string {\r\n\t\tlet inv: string = this.invert ? \"!\" : \"\";\r\n\t\tlet className: string = Object.constructor.name;\r\n\t\treturn className + \"[\" + inv + this.nodeName + \"]\";\r\n\t}\r\n}\r\n"]}