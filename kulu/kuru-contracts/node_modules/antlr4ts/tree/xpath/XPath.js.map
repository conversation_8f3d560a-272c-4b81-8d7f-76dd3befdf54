{"version": 3, "file": "XPath.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPath.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,mDAAgD;AAChD,+DAA4D;AAC5D,+EAA4E;AAE5E,+DAA4D;AAE5D,uCAAoC;AAEpC,6CAA0C;AAC1C,uEAAoE;AACpE,yEAAsE;AACtE,yDAAsD;AACtD,2EAAwE;AACxE,2DAAwD;AACxD,iFAA8E;AAC9E,iEAA8D;AAE9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAa,KAAK;IAQjB,YAAY,MAAc,EAAE,IAAY;QACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,yCAAyC;IAC1C,CAAC;IAED,uDAAuD;IAEhD,KAAK,CAAC,IAAY;QACxB,IAAI,KAAK,GAAG,IAAI,uBAAU,CAAC,yBAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD,KAAK,CAAC,OAAO,GAAG,CAAC,CAA4B,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,KAAK,CAAC,gBAAgB,CAAC,IAAI,iDAAuB,EAAE,CAAC,CAAC;QACtD,IAAI,WAAW,GAAG,IAAI,qCAAiB,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI;YACH,WAAW,CAAC,IAAI,EAAE,CAAC;SACnB;QACD,OAAO,CAAC,EAAE;YACT,IAAI,CAAC,YAAY,qDAAyB,EAAE;gBAC3C,IAAI,GAAG,GAAW,KAAK,CAAC,kBAAkB,CAAC;gBAC3C,IAAI,GAAG,GAAW,wCAAwC,GAAG,GAAG,GAAG,YAAY,GAAG,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC7G,MAAM,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;aAC1B;YACD,MAAM,CAAC,CAAC;SACR;QAED,IAAI,MAAM,GAAY,WAAW,CAAC,SAAS,EAAE,CAAC;QAC9C,+CAA+C;QAC/C,IAAI,QAAQ,GAAmB,EAAE,CAAC;QAClC,IAAI,CAAC,GAAW,MAAM,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,IAAI,EACJ,OAAO,CAAC,GAAG,CAAC,EAAE;YACb,IAAI,EAAE,GAAU,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,IAAuB,CAAC;YAC5B,QAAQ,EAAE,CAAC,IAAI,EAAE;gBAChB,KAAK,uBAAU,CAAC,IAAI,CAAC;gBACrB,KAAK,uBAAU,CAAC,QAAQ;oBACvB,IAAI,QAAQ,GAAY,EAAE,CAAC,IAAI,KAAK,uBAAU,CAAC,QAAQ,CAAC;oBACxD,CAAC,EAAE,CAAC;oBACJ,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACjB,IAAI,MAAM,GAAY,IAAI,CAAC,IAAI,KAAK,uBAAU,CAAC,IAAI,CAAC;oBACpD,IAAI,MAAM,EAAE;wBACX,CAAC,EAAE,CAAC;wBACJ,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;qBACjB;oBACD,IAAI,WAAW,GAAiB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBACrE,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC3B,CAAC,EAAE,CAAC;oBACJ,MAAM;gBAEP,KAAK,uBAAU,CAAC,SAAS,CAAC;gBAC1B,KAAK,uBAAU,CAAC,QAAQ,CAAC;gBACzB,KAAK,uBAAU,CAAC,QAAQ;oBACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;oBAC/C,CAAC,EAAE,CAAC;oBACJ,MAAM;gBAEP,KAAK,aAAK,CAAC,GAAG;oBACb,MAAM,IAAI,CAAC;gBAEZ;oBACC,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAC;aAChD;SACD;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACO,eAAe,CAAC,SAAgB,EAAE,QAAiB;QAC5D,IAAI,SAAS,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC5D;QAED,IAAI,KAAK,GAAW,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,SAAS,GAAW,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACvD,QAAQ,SAAS,CAAC,IAAI,EAAE;YACvB,KAAK,uBAAU,CAAC,QAAQ;gBACvB,OAAO,QAAQ,CAAC,CAAC;oBAChB,IAAI,2DAA4B,EAAE,CAAC,CAAC;oBACpC,IAAI,2CAAoB,EAAE,CAAC;YAC7B,KAAK,uBAAU,CAAC,SAAS,CAAC;YAC1B,KAAK,uBAAU,CAAC,MAAM;gBACrB,IAAI,KAAK,KAAK,aAAK,CAAC,YAAY,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,YAAY;wBAClC,SAAS,CAAC,UAAU;wBACpB,2BAA2B,CAAC,CAAC;iBAC9B;gBACD,OAAO,QAAQ,CAAC,CAAC;oBAChB,IAAI,qDAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;oBAC5C,IAAI,qCAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACrC;gBACC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;oBACrB,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,YAAY;wBAClC,SAAS,CAAC,UAAU;wBACpB,0BAA0B,CAAC,CAAC;iBAC7B;gBACD,OAAO,QAAQ,CAAC,CAAC;oBAChB,IAAI,mDAAwB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC/C,IAAI,mCAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACxC;IACF,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,IAAe,EAAE,KAAa,EAAE,MAAc;QACnE,IAAI,CAAC,GAAU,IAAI,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,CAAY;QAC3B,IAAI,SAAS,GAAG,IAAI,qCAAiB,EAAE,CAAC;QACxC,SAAS,CAAC,QAAQ,CAAC,CAAsB,CAAC,CAAC;QAE3C,IAAI,IAAI,GAAG,IAAI,GAAG,CAAY,CAAC,SAAS,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAChC,IAAI,IAAI,GAAG,IAAI,GAAG,EAAa,CAAC;YAChC,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;gBACtB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;oBACxB,oDAAoD;oBACpD,wDAAwD;oBACxD,sCAAsC;oBACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC/C,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;iBACjC;aACD;YACD,CAAC,EAAE,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC;SACZ;QAED,OAAO,IAAI,CAAC;IACb,CAAC;;AA3JF,sBA4JC;AA3JuB,cAAQ,GAAW,GAAG,CAAC,CAAC,8BAA8B;AACtD,SAAG,GAAW,GAAG,CAAC,CAAK,2BAA2B", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:46.4373888-07:00\r\n\r\nimport { CharStreams } from \"../../CharStreams\";\r\nimport { CommonTokenStream } from \"../../CommonTokenStream\";\r\nimport { LexerNoViableAltException } from \"../../LexerNoViableAltException\";\r\nimport { Parser } from \"../../Parser\";\r\nimport { ParserRuleContext } from \"../../ParserRuleContext\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { Token } from \"../../Token\";\r\nimport { XPathElement } from \"./XPathElement\";\r\nimport { XPathLexer } from \"./XPathLexer\";\r\nimport { XPathLexerErrorListener } from \"./XPathLexerErrorListener\";\r\nimport { XPathRuleAnywhereElement } from \"./XPathRuleAnywhereElement\";\r\nimport { XPathRuleElement } from \"./XPathRuleElement\";\r\nimport { XPathTokenAnywhereElement } from \"./XPathTokenAnywhereElement\";\r\nimport { XPathTokenElement } from \"./XPathTokenElement\";\r\nimport { XPathWildcardAnywhereElement } from \"./XPathWildcardAnywhereElement\";\r\nimport { XPathWildcardElement } from \"./XPathWildcardElement\";\r\n\r\n/**\r\n * Represent a subset of XPath XML path syntax for use in identifying nodes in\r\n * parse trees.\r\n *\r\n * Split path into words and separators `/` and `//` via ANTLR\r\n * itself then walk path elements from left to right. At each separator-word\r\n * pair, find set of nodes. Next stage uses those as work list.\r\n *\r\n * The basic interface is\r\n * {@link XPath#findAll ParseTree.findAll}`(tree, pathString, parser)`.\r\n * But that is just shorthand for:\r\n *\r\n * ```\r\n * let p = new XPath(parser, pathString);\r\n * return p.evaluate(tree);\r\n * ```\r\n *\r\n * See `TestXPath` for descriptions. In short, this\r\n * allows operators:\r\n *\r\n * | | |\r\n * | --- | --- |\r\n * | `/` | root |\r\n * | `//` | anywhere |\r\n * | `!` | invert; this much appear directly after root or anywhere operator |\r\n *\r\n * and path elements:\r\n *\r\n * | | |\r\n * | --- | --- |\r\n * | `ID` | token name |\r\n * | `'string'` | any string literal token from the grammar |\r\n * | `expr` | rule name |\r\n * | `*` | wildcard matching any node |\r\n *\r\n * Whitespace is not allowed.\r\n */\r\nexport class XPath {\r\n\tpublic static readonly WILDCARD: string = \"*\"; // word not operator/separator\r\n\tpublic static readonly NOT: string = \"!\"; \t   // word for invert operator\r\n\r\n\tprotected path: string;\r\n\tprotected elements: XPathElement[];\r\n\tprotected parser: Parser;\r\n\r\n\tconstructor(parser: Parser, path: string) {\r\n\t\tthis.parser = parser;\r\n\t\tthis.path = path;\r\n\t\tthis.elements = this.split(path);\r\n\t\t// console.log(this.elements.toString());\r\n\t}\r\n\r\n\t// TODO: check for invalid token/rule names, bad syntax\r\n\r\n\tpublic split(path: string): XPathElement[] {\r\n\t\tlet lexer = new XPathLexer(CharStreams.fromString(path));\r\n\t\tlexer.recover = (e: LexerNoViableAltException) => { throw e; };\r\n\r\n\t\tlexer.removeErrorListeners();\r\n\t\tlexer.addErrorListener(new XPathLexerErrorListener());\r\n\t\tlet tokenStream = new CommonTokenStream(lexer);\r\n\t\ttry {\r\n\t\t\ttokenStream.fill();\r\n\t\t}\r\n\t\tcatch (e) {\r\n\t\t\tif (e instanceof LexerNoViableAltException) {\r\n\t\t\t\tlet pos: number = lexer.charPositionInLine;\r\n\t\t\t\tlet msg: string = \"Invalid tokens or characters at index \" + pos + \" in path '\" + path + \"' -- \" + e.message;\r\n\t\t\t\tthrow new RangeError(msg);\r\n\t\t\t}\r\n\t\t\tthrow e;\r\n\t\t}\r\n\r\n\t\tlet tokens: Token[] = tokenStream.getTokens();\r\n\t\t// console.log(\"path=\" + path + \"=>\" + tokens);\r\n\t\tlet elements: XPathElement[] = [];\r\n\t\tlet n: number = tokens.length;\r\n\t\tlet i: number = 0;\r\n\t\tloop:\r\n\t\twhile (i < n) {\r\n\t\t\tlet el: Token = tokens[i];\r\n\t\t\tlet next: Token | undefined;\r\n\t\t\tswitch (el.type) {\r\n\t\t\t\tcase XPathLexer.ROOT:\r\n\t\t\t\tcase XPathLexer.ANYWHERE:\r\n\t\t\t\t\tlet anywhere: boolean = el.type === XPathLexer.ANYWHERE;\r\n\t\t\t\t\ti++;\r\n\t\t\t\t\tnext = tokens[i];\r\n\t\t\t\t\tlet invert: boolean = next.type === XPathLexer.BANG;\r\n\t\t\t\t\tif (invert) {\r\n\t\t\t\t\t\ti++;\r\n\t\t\t\t\t\tnext = tokens[i];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet pathElement: XPathElement = this.getXPathElement(next, anywhere);\r\n\t\t\t\t\tpathElement.invert = invert;\r\n\t\t\t\t\telements.push(pathElement);\r\n\t\t\t\t\ti++;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase XPathLexer.TOKEN_REF:\r\n\t\t\t\tcase XPathLexer.RULE_REF:\r\n\t\t\t\tcase XPathLexer.WILDCARD:\r\n\t\t\t\t\telements.push(this.getXPathElement(el, false));\r\n\t\t\t\t\ti++;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase Token.EOF:\r\n\t\t\t\t\tbreak loop;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthrow new Error(\"Unknowth path element \" + el);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn elements;\r\n\t}\r\n\r\n\t/**\r\n\t * Convert word like `*` or `ID` or `expr` to a path\r\n\t * element. `anywhere` is `true` if `//` precedes the\r\n\t * word.\r\n\t */\r\n\tprotected getXPathElement(wordToken: Token, anywhere: boolean): XPathElement {\r\n\t\tif (wordToken.type === Token.EOF) {\r\n\t\t\tthrow new Error(\"Missing path element at end of path\");\r\n\t\t}\r\n\r\n\t\tlet word = wordToken.text;\r\n\t\tif (word == null) {\r\n\t\t\tthrow new Error(\"Expected wordToken to have text content.\");\r\n\t\t}\r\n\r\n\t\tlet ttype: number = this.parser.getTokenType(word);\r\n\t\tlet ruleIndex: number = this.parser.getRuleIndex(word);\r\n\t\tswitch (wordToken.type) {\r\n\t\t\tcase XPathLexer.WILDCARD:\r\n\t\t\t\treturn anywhere ?\r\n\t\t\t\t\tnew XPathWildcardAnywhereElement() :\r\n\t\t\t\t\tnew XPathWildcardElement();\r\n\t\t\tcase XPathLexer.TOKEN_REF:\r\n\t\t\tcase XPathLexer.STRING:\r\n\t\t\t\tif (ttype === Token.INVALID_TYPE) {\r\n\t\t\t\t\tthrow new Error(word + \" at index \" +\r\n\t\t\t\t\t\twordToken.startIndex +\r\n\t\t\t\t\t\t\" isn't a valid token name\");\r\n\t\t\t\t}\r\n\t\t\t\treturn anywhere ?\r\n\t\t\t\t\tnew XPathTokenAnywhereElement(word, ttype) :\r\n\t\t\t\t\tnew XPathTokenElement(word, ttype);\r\n\t\t\tdefault:\r\n\t\t\t\tif (ruleIndex === -1) {\r\n\t\t\t\t\tthrow new Error(word + \" at index \" +\r\n\t\t\t\t\t\twordToken.startIndex +\r\n\t\t\t\t\t\t\" isn't a valid rule name\");\r\n\t\t\t\t}\r\n\t\t\t\treturn anywhere ?\r\n\t\t\t\t\tnew XPathRuleAnywhereElement(word, ruleIndex) :\r\n\t\t\t\t\tnew XPathRuleElement(word, ruleIndex);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic static findAll(tree: ParseTree, xpath: string, parser: Parser): Set<ParseTree> {\r\n\t\tlet p: XPath = new XPath(parser, xpath);\r\n\t\treturn p.evaluate(tree);\r\n\t}\r\n\r\n\t/**\r\n\t * Return a list of all nodes starting at `t` as root that satisfy the\r\n\t * path. The root `/` is relative to the node passed to {@link evaluate}.\r\n\t */\r\n\tpublic evaluate(t: ParseTree): Set<ParseTree> {\r\n\t\tlet dummyRoot = new ParserRuleContext();\r\n\t\tdummyRoot.addChild(t as ParserRuleContext);\r\n\r\n\t\tlet work = new Set<ParseTree>([dummyRoot]);\r\n\r\n\t\tlet i: number = 0;\r\n\t\twhile (i < this.elements.length) {\r\n\t\t\tlet next = new Set<ParseTree>();\r\n\t\t\tfor (let node of work) {\r\n\t\t\t\tif (node.childCount > 0) {\r\n\t\t\t\t\t// only try to match next element if it has children\r\n\t\t\t\t\t// e.g., //func/*/stat might have a token node for which\r\n\t\t\t\t\t// we can't go looking for stat nodes.\r\n\t\t\t\t\tlet matching = this.elements[i].evaluate(node);\r\n\t\t\t\t\tmatching.forEach(next.add, next);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ti++;\r\n\t\t\twork = next;\r\n\t\t}\r\n\r\n\t\treturn work;\r\n\t}\r\n}\r\n"]}