{"version": 3, "file": "XPathRuleAnywhereElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathRuleAnywhereElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAIH,iDAA4C;AAE5C,oCAAiC;AACjC,iDAA8C;AAE9C;;GAEG;AACH,MAAa,wBAAyB,SAAQ,2BAAY;IAEzD,YAAY,QAAgB,EAAE,SAAiB;QAC9C,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,OAAO,aAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;CACD;AAHA;IADC,qBAAQ;wDAGR;AAVF,4DAWC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { ParserRuleContext } from \"../../ParserRuleContext\";\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\n/**\r\n * Either `ID` at start of path or `...//ID` in middle of path.\r\n */\r\nexport class XPathRuleAnywhereElement extends XPathElement {\r\n\tprotected ruleIndex: number;\r\n\tconstructor(ruleName: string, ruleIndex: number) {\r\n\t\tsuper(ruleName);\r\n\t\tthis.ruleIndex = ruleIndex;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\treturn Trees.findAllRuleNodes(t, this.ruleIndex);\r\n\t}\r\n}\r\n"]}