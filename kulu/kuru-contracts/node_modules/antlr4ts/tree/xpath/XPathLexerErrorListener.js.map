{"version": 3, "file": "XPathLexerErrorListener.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathLexerErrorListener.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAKH,iDAA4C;AAI5C,MAAa,uBAAuB;IAE5B,WAAW,CACjB,UAA8B,EAAE,eAA8B,EAC9D,IAAY,EAAE,kBAA0B,EAAE,GAAW,EACrD,CAAmC;QACnC,sBAAsB;IACvB,CAAC;CACD;AANA;IADC,qBAAQ;0DAMR;AAPF,0DAQC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\n\r\nimport { ANTLRErrorListener } from \"../../ANTLRErrorListener\";\r\nimport { Override } from \"../../Decorators\";\r\nimport { Recognizer } from \"../../Recognizer\";\r\nimport { RecognitionException } from \"../../RecognitionException\";\r\n\r\nexport class XPathLexerErrorListener implements ANTLRErrorListener<number> {\r\n\t@Override\r\n\tpublic syntaxError<T extends number>(\r\n\t\trecognizer: Recognizer<T, any>, offendingSymbol: T | undefined,\r\n\t\tline: number, charPositionInLine: number, msg: string,\r\n\t\te: RecognitionException | undefined): void {\r\n\t\t// intentionally empty\r\n\t}\r\n}\r\n"]}