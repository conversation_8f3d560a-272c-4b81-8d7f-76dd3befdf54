{"version": 3, "file": "XPathTokenAnywhereElement.js", "sourceRoot": "", "sources": ["../../../../src/tree/xpath/XPathTokenAnywhereElement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,+CAA+C;AAC/C,iDAA4C;AAE5C,oCAAiC;AACjC,iDAA8C;AAE9C,MAAa,yBAA0B,SAAQ,2BAAY;IAE1D,YAAY,SAAiB,EAAE,SAAiB;QAC/C,KAAK,CAAC,SAAS,CAAC,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAGM,QAAQ,CAAC,CAAY;QAC3B,OAAO,aAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;CACD;AAHA;IADC,qBAAQ;yDAGR;AAVF,8DAWC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// CONVERSTION complete, <PERSON> 10/14/2016\r\nimport { Override } from \"../../Decorators\";\r\nimport { ParseTree } from \"../ParseTree\";\r\nimport { Trees } from \"../Trees\";\r\nimport { XPathElement } from \"./XPathElement\";\r\n\r\nexport class XPathTokenAnywhereElement extends XPathElement {\r\n\tprotected tokenType: number;\r\n\tconstructor(tokenName: string, tokenType: number) {\r\n\t\tsuper(tokenName);\r\n\t\tthis.tokenType = tokenType;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic evaluate(t: ParseTree): ParseTree[] {\r\n\t\treturn Trees.findAllTokenNodes(t, this.tokenType);\r\n\t}\r\n}\r\n"]}