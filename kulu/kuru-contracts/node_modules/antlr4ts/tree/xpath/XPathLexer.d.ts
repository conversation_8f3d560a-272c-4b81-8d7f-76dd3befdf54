import { ATN } from "../../atn/ATN";
import { CharStream } from "../../CharStream";
import { Lexer } from "../../Lexer";
import { RuleContext } from "../../RuleContext";
import { Vocabulary } from "../../Vocabulary";
export declare class XPathLexer extends Lexer {
    static readonly TOKEN_REF = 1;
    static readonly RULE_REF = 2;
    static readonly ANYWHERE = 3;
    static readonly ROOT = 4;
    static readonly WILDCARD = 5;
    static readonly BANG = 6;
    static readonly ID = 7;
    static readonly STRING = 8;
    static readonly channelNames: string[];
    static readonly modeNames: string[];
    static readonly ruleNames: string[];
    private static readonly _LITERAL_NAMES;
    private static readonly _SYMBOLIC_NAMES;
    static readonly VOCABULARY: Vocabulary;
    get vocabulary(): Vocabulary;
    constructor(input: CharStream);
    get grammarFileName(): string;
    get ruleNames(): string[];
    get serializedATN(): string;
    get channelNames(): string[];
    get modeNames(): string[];
    action(_localctx: RuleContext, ruleIndex: number, actionIndex: number): void;
    private ID_action;
    private static readonly _serializedATNSegments;
    private static readonly _serializedATNSegment0;
    private static readonly _serializedATNSegment1;
    static readonly _serializedATN: string;
    static __ATN: ATN;
    static get _ATN(): ATN;
}
