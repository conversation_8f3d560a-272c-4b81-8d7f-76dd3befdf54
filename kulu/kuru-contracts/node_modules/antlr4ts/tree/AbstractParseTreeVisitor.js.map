{"version": 3, "file": "AbstractParseTreeVisitor.js", "sourceRoot": "", "sources": ["../../../src/tree/AbstractParseTreeVisitor.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;AAKH,8CAAkD;AAMlD,MAAsB,wBAAwB;IAC7C;;;;;OAKG;IAEI,KAAK,CAAU,IAAe;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;;;;;;;;;;;;;OAcG;IAEI,aAAa,CAAU,IAAc;QAC3C,IAAI,MAAM,GAAW,IAAI,CAAC,aAAa,EAAE,CAAC;QAC1C,IAAI,CAAC,GAAW,IAAI,CAAC,UAAU,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;gBAC7C,MAAM;aACN;YAED,IAAI,CAAC,GAAc,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,WAAW,GAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;SACnD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;OAKG;IAEI,aAAa,CAAU,IAAkB;QAC/C,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IAEI,cAAc,CAAU,IAAe;QAC7C,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAC7B,CAAC;IAaD;;;;;;;;;;;;;;;;;;OAkBG;IACO,eAAe,CAAC,SAAiB,EAAE,UAAkB;QAC9D,OAAO,UAAU,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACO,oBAAoB,CAAU,IAAc,EAAE,aAAqB;QAC5E,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAvHA;IADC,qBAAQ;IACK,WAAA,oBAAO,CAAA;qDAEpB;AAkBD;IADC,qBAAQ;IACa,WAAA,oBAAO,CAAA;6DAc5B;AASD;IADC,qBAAQ;IACa,WAAA,oBAAO,CAAA;6DAE5B;AASD;IADC,qBAAQ;IACc,WAAA,oBAAO,CAAA;8DAE7B;AA4DD;IAAgC,WAAA,oBAAO,CAAA;oEAEtC;AA9HF,4DA+HC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.3092279-07:00\r\n\r\nimport { ErrorNode } from \"./ErrorNode\";\r\nimport { NotNull, Override } from \"../Decorators\";\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { ParseTreeVisitor } from \"./ParseTreeVisitor\";\r\nimport { RuleNode } from \"./RuleNode\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\n\r\nexport abstract class AbstractParseTreeVisitor<Result> implements ParseTreeVisitor<Result> {\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation calls {@link ParseTree#accept} on the\r\n\t * specified tree.\r\n\t */\r\n\t@Override\r\n\tpublic visit(@NotNull tree: ParseTree): Result {\r\n\t\treturn tree.accept(this);\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation initializes the aggregate result to\r\n\t * {@link #defaultResult defaultResult()}. Before visiting each child, it\r\n\t * calls {@link #shouldVisitNextChild shouldVisitNextChild}; if the result\r\n\t * is `false` no more children are visited and the current aggregate\r\n\t * result is returned. After visiting a child, the aggregate result is\r\n\t * updated by calling {@link #aggregateResult aggregateResult} with the\r\n\t * previous aggregate result and the result of visiting the child.\r\n\t *\r\n\t * The default implementation is not safe for use in visitors that modify\r\n\t * the tree structure. Visitors that modify the tree should override this\r\n\t * method to behave properly in respect to the specific algorithm in use.\r\n\t */\r\n\t@Override\r\n\tpublic visitChildren(@NotNull node: RuleNode): Result {\r\n\t\tlet result: Result = this.defaultResult();\r\n\t\tlet n: number = node.childCount;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tif (!this.shouldVisitNextChild(node, result)) {\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\r\n\t\t\tlet c: ParseTree = node.getChild(i);\r\n\t\t\tlet childResult: Result = c.accept(this);\r\n\t\t\tresult = this.aggregateResult(result, childResult);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation returns the result of\r\n\t * {@link #defaultResult defaultResult}.\r\n\t */\r\n\t@Override\r\n\tpublic visitTerminal(@NotNull node: TerminalNode): Result {\r\n\t\treturn this.defaultResult();\r\n\t}\r\n\r\n\t/**\r\n\t * {@inheritDoc}\r\n\t *\r\n\t * The default implementation returns the result of\r\n\t * {@link #defaultResult defaultResult}.\r\n\t */\r\n\t@Override\r\n\tpublic visitErrorNode(@NotNull node: ErrorNode): Result {\r\n\t\treturn this.defaultResult();\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the default value returned by visitor methods. This value is\r\n\t * returned by the default implementations of\r\n\t * {@link #visitTerminal visitTerminal}, {@link #visitErrorNode visitErrorNode}.\r\n\t * The default implementation of {@link #visitChildren visitChildren}\r\n\t * initializes its aggregate result to this value.\r\n\t *\r\n\t * @returns The default value returned by visitor methods.\r\n\t */\r\n\tprotected abstract defaultResult(): Result;\r\n\r\n\t/**\r\n\t * Aggregates the results of visiting multiple children of a node. After\r\n\t * either all children are visited or {@link #shouldVisitNextChild} returns\r\n\t * `false`, the aggregate value is returned as the result of\r\n\t * {@link #visitChildren}.\r\n\t *\r\n\t * The default implementation returns `nextResult`, meaning\r\n\t * {@link #visitChildren} will return the result of the last child visited\r\n\t * (or return the initial value if the node has no children).\r\n\t *\r\n\t * @param aggregate The previous aggregate value. In the default\r\n\t * implementation, the aggregate value is initialized to\r\n\t * {@link #defaultResult}, which is passed as the `aggregate` argument\r\n\t * to this method after the first child node is visited.\r\n\t * @param nextResult The result of the immediately preceeding call to visit\r\n\t * a child node.\r\n\t *\r\n\t * @returns The updated aggregate result.\r\n\t */\r\n\tprotected aggregateResult(aggregate: Result, nextResult: Result): Result {\r\n\t\treturn nextResult;\r\n\t}\r\n\r\n\t/**\r\n\t * This method is called after visiting each child in\r\n\t * {@link #visitChildren}. This method is first called before the first\r\n\t * child is visited; at that point `currentResult` will be the initial\r\n\t * value (in the default implementation, the initial value is returned by a\r\n\t * call to {@link #defaultResult}. This method is not called after the last\r\n\t * child is visited.\r\n\t *\r\n\t * The default implementation always returns `true`, indicating that\r\n\t * `visitChildren` should only return after all children are visited.\r\n\t * One reason to override this method is to provide a \"short circuit\"\r\n\t * evaluation option for situations where the result of visiting a single\r\n\t * child has the potential to determine the result of the visit operation as\r\n\t * a whole.\r\n\t *\r\n\t * @param node The {@link RuleNode} whose children are currently being\r\n\t * visited.\r\n\t * @param currentResult The current aggregate result of the children visited\r\n\t * to the current point.\r\n\t *\r\n\t * @returns `true` to continue visiting children. Otherwise return\r\n\t * `false` to stop visiting children and immediately return the\r\n\t * current aggregate result from {@link #visitChildren}.\r\n\t */\r\n\tprotected shouldVisitNextChild(@NotNull node: RuleNode, currentResult: Result): boolean {\r\n\t\treturn true;\r\n\t}\r\n}\r\n"]}