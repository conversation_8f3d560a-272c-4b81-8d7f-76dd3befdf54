{"version": 3, "file": "ErrorNode.js", "sourceRoot": "", "sources": ["../../../src/tree/ErrorNode.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAEH,wDAAwD;AAExD,8CAAyC;AAEzC,iDAA8C;AAG9C;;;;;GAKG;AACH,MAAa,SAAU,SAAQ,2BAAY;IAC1C,YAAY,KAAY;QACvB,KAAK,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;IAGM,MAAM,CAAI,OAA4B;QAC5C,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;CACD;AAHA;IADC,qBAAQ;uCAGR;AARF,8BASC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.4646355-07:00\r\n\r\nimport { Override } from \"../Decorators\";\r\nimport { ParseTreeVisitor } from \"./ParseTreeVisitor\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\nimport { Token } from \"../Token\";\r\n\r\n/** Represents a token that was consumed during resynchronization\r\n *  rather than during a valid match operation. For example,\r\n *  we will create this kind of a node during single token insertion\r\n *  and deletion as well as during \"consume until error recovery set\"\r\n *  upon no viable alternative exceptions.\r\n */\r\nexport class ErrorNode extends TerminalNode {\r\n\tconstructor(token: Token) {\r\n\t\tsuper(token);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic accept<T>(visitor: ParseTreeVisitor<T>): T {\r\n\t\treturn visitor.visitErrorNode(this);\r\n\t}\r\n}\r\n"]}