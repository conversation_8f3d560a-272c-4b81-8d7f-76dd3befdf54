{"version": 3, "file": "ParseTreeVisitor.js", "sourceRoot": "", "sources": ["../../../src/tree/ParseTreeVisitor.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:47.7512217-07:00\r\n\r\nimport { ErrorNode } from \"./ErrorNode\";\r\nimport { ParseTree } from \"./ParseTree\";\r\nimport { RuleNode } from \"./RuleNode\";\r\nimport { TerminalNode } from \"./TerminalNode\";\r\n\r\n/**\r\n * This interface defines the basic notion of a parse tree visitor. Generated\r\n * visitors implement this interface and the `XVisitor` interface for\r\n * grammar `X`.\r\n *\r\n * <AUTHOR>\r\n * @param <Result> The return type of the visit operation. Use {@link Void} for\r\n * operations with no return type.\r\n */\r\nexport interface ParseTreeVisitor<Result> {\r\n\r\n\t/**\r\n\t * Visit a parse tree, and return a user-defined result of the operation.\r\n\t *\r\n\t * @param tree The {@link ParseTree} to visit.\r\n\t * @returns The result of visiting the parse tree.\r\n\t */\r\n\tvisit(/*@NotNull*/ tree: ParseTree): Result;\r\n\r\n\t/**\r\n\t * Visit the children of a node, and return a user-defined result\r\n\t * of the operation.\r\n\t *\r\n\t * @param node The {@link RuleNode} whose children should be visited.\r\n\t * @returns The result of visiting the children of the node.\r\n\t */\r\n\tvisitChildren(/*@NotNull*/ node: RuleNode): Result;\r\n\r\n\t/**\r\n\t * Visit a terminal node, and return a user-defined result of the operation.\r\n\t *\r\n\t * @param node The {@link TerminalNode} to visit.\r\n\t * @returns The result of visiting the node.\r\n\t */\r\n\tvisitTerminal(/*@NotNull*/ node: TerminalNode): Result;\r\n\r\n\t/**\r\n\t * Visit an error node, and return a user-defined result of the operation.\r\n\t *\r\n\t * @param node The {@link ErrorNode} to visit.\r\n\t * @returns The result of visiting the node.\r\n\t */\r\n\tvisitErrorNode(/*@NotNull*/ node: ErrorNode): Result;\r\n\r\n}\r\n"]}