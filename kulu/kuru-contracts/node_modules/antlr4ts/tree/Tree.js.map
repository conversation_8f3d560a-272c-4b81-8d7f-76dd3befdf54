{"version": 3, "file": "Tree.js", "sourceRoot": "", "sources": ["../../../src/tree/Tree.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-02T21:58:18.5966470-07:00\r\n\r\n/** The basic notion of a tree has a parent, a payload, and a list of children.\r\n *  It is the most abstract interface for all the trees used by ANTLR.\r\n */\r\nexport interface Tree {\r\n\t/** The parent of this node. If the return value is `undefined`, then this\r\n\t *  node is the root of the tree.\r\n\t */\r\n\treadonly parent: Tree | undefined;\r\n\r\n\t/**\r\n\t * This method returns whatever object represents the data at this node. For\r\n\t * example, for parse trees, the payload can be a {@link Token} representing\r\n\t * a leaf node or a {@link RuleContext} object representing a rule\r\n\t * invocation. For abstract syntax trees (ASTs), this is a {@link Token}\r\n\t * object.\r\n\t */\r\n\treadonly payload: { text?: string };\r\n\r\n\t/**\r\n\t * If there are children, get the `i`th value indexed from 0. Throws a `RangeError` if `i` is less than zero, or\r\n\t * greater than or equal to `childCount`.\r\n\t */\r\n\tgetChild(i: number): Tree;\r\n\r\n\t/** How many children are there? If there is none, then this\r\n\t *  node represents a leaf node.\r\n\t */\r\n\treadonly childCount: number;\r\n\r\n\t/** Print out a whole tree, not just a node, in LISP format\r\n\t *  `(root child1 .. childN)`. Print just a node if this is a leaf.\r\n\t */\r\n\ttoStringTree(): string;\r\n}\r\n"]}