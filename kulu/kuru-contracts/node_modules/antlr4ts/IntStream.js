"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntStream = void 0;
// ConvertTo-TS run at 2016-10-04T11:26:51.6934376-07:00
var IntStream;
(function (IntStream) {
    /**
     * The value returned by {@link #LA LA()} when the end of the stream is
     * reached.
     */
    IntStream.EOF = -1;
    /**
     * The value returned by {@link #getSourceName} when the actual name of the
     * underlying source is not known.
     */
    IntStream.UNKNOWN_SOURCE_NAME = "<unknown>";
})(IntStream = exports.IntStream || (exports.IntStream = {}));
//# sourceMappingURL=IntStream.js.map