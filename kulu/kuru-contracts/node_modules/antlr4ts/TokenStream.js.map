{"version": 3, "file": "TokenStream.js", "sourceRoot": "", "sources": ["../../src/TokenStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:58.0433998-07:00\r\n\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/**\r\n * An {@link IntStream} whose symbols are {@link Token} instances.\r\n */\r\nexport interface TokenStream extends IntStream {\r\n\t/**\r\n\t * Get the `Token` instance associated with the value returned by `LA(k)`. This method has the same pre- and\r\n\t * post-conditions as `IntStream.LA`. In addition, when the preconditions of this method are met, the return value\r\n\t * is non-undefined and the value of `LT(k).type === LA(k)`.\r\n\t *\r\n\t * A `RangeError` is thrown if `k<0` and fewer than `-k` calls to `consume()` have occurred from the beginning of\r\n\t * the stream before calling this method.\r\n\t *\r\n\t * See `IntStream.LA`\r\n\t */\r\n\tLT(k: number): Token;\r\n\r\n\t/**\r\n\t * Get the `Token` instance associated with the value returned by `LA(k)`. This method has the same pre- and\r\n\t * post-conditions as `IntStream.LA`. In addition, when the preconditions of this method are met, the return value\r\n\t * is non-undefined and the value of `tryLT(k).type === LA(k)`.\r\n\t *\r\n\t * The return value is `undefined` if `k<0` and fewer than `-k` calls to `consume()` have occurred from the\r\n\t * beginning of the stream before calling this method.\r\n\t *\r\n\t * See `IntStream.LA`\r\n\t */\r\n\ttryLT(k: number): Token | undefined;\r\n\r\n\t/**\r\n\t * Gets the {@link Token} at the specified `index` in the stream. When\r\n\t * the preconditions of this method are met, the return value is non-undefined.\r\n\t *\r\n\t * The preconditions for this method are the same as the preconditions of\r\n\t * {@link IntStream#seek}. If the behavior of `seek(index)` is\r\n\t * unspecified for the current state and given `index`, then the\r\n\t * behavior of this method is also unspecified.\r\n\t *\r\n\t * The symbol referred to by `index` differs from `seek()` only\r\n\t * in the case of filtering streams where `index` lies before the end\r\n\t * of the stream. Unlike `seek()`, this method does not adjust\r\n\t * `index` to point to a non-ignored symbol.\r\n\t *\r\n\t * @throws IllegalArgumentException if {code index} is less than 0\r\n\t * @throws UnsupportedOperationException if the stream does not support\r\n\t * retrieving the token at the specified index\r\n\t */\r\n\t//@NotNull\r\n\tget(i: number): Token;\r\n\r\n\t/**\r\n\t * Gets the underlying {@link TokenSource} which provides tokens for this\r\n\t * stream.\r\n\t */\r\n\t//@NotNull\r\n\treadonly tokenSource: TokenSource;\r\n\r\n\t/**\r\n\t * Return the text of all tokens within the specified `interval`. This\r\n\t * method behaves like the following code (including potential exceptions\r\n\t * for violating preconditions of {@link #get}, but may be optimized by the\r\n\t * specific implementation.\r\n\t *\r\n\t * ```\r\n\t * TokenStream stream = ...;\r\n\t * String text = \"\";\r\n\t * for (int i = interval.a; i <= interval.b; i++) {\r\n\t *   text += stream.get(i).text;\r\n\t * }\r\n\t * ```\r\n\t *\r\n\t * @param interval The interval of tokens within this stream to get text\r\n\t * for.\r\n\t * @returns The text of all tokens within the specified interval in this\r\n\t * stream.\r\n\t *\r\n\t * @throws NullPointerException if `interval` is `undefined`\r\n\t */\r\n\t//@NotNull\r\n\tgetText(/*@NotNull*/ interval: Interval): string;\r\n\r\n\t/**\r\n\t * Return the text of all tokens in the stream. This method behaves like the\r\n\t * following code, including potential exceptions from the calls to\r\n\t * {@link IntStream#size} and {@link #getText(Interval)}, but may be\r\n\t * optimized by the specific implementation.\r\n\t *\r\n\t * ```\r\n\t * TokenStream stream = ...;\r\n\t * String text = stream.getText(new Interval(0, stream.size));\r\n\t * ```\r\n\t *\r\n\t * @returns The text of all tokens in the stream.\r\n\t */\r\n\t//@NotNull\r\n\tgetText(): string;\r\n\r\n\t/**\r\n\t * Return the text of all tokens in the source interval of the specified\r\n\t * context. This method behaves like the following code, including potential\r\n\t * exceptions from the call to {@link #getText(Interval)}, but may be\r\n\t * optimized by the specific implementation.\r\n\t *\r\n\t * If `ctx.sourceInterval` does not return a valid interval of\r\n\t * tokens provided by this stream, the behavior is unspecified.\r\n\t *\r\n\t * ```\r\n\t * TokenStream stream = ...;\r\n\t * String text = stream.getText(ctx.sourceInterval);\r\n\t * ```\r\n\t *\r\n\t * @param ctx The context providing the source interval of tokens to get\r\n\t * text for.\r\n\t * @returns The text of all tokens within the source interval of `ctx`.\r\n\t */\r\n\t//@NotNull\r\n\tgetText(/*@NotNull*/ ctx: RuleContext): string;\r\n\r\n\t/**\r\n\t * Return the text of all tokens in this stream between `start` and\r\n\t * `stop` (inclusive).\r\n\t *\r\n\t * If the specified `start` or `stop` token was not provided by\r\n\t * this stream, or if the `stop` occurred before the `start`}\r\n\t * token, the behavior is unspecified.\r\n\t *\r\n\t * For streams which ensure that the `Token.tokenIndex` method is\r\n\t * accurate for all of its provided tokens, this method behaves like the\r\n\t * following code. Other streams may implement this method in other ways\r\n\t * provided the behavior is consistent with this at a high level.\r\n\t *\r\n\t * ```\r\n\t * TokenStream stream = ...;\r\n\t * String text = \"\";\r\n\t * for (int i = start.tokenIndex; i <= stop.tokenIndex; i++) {\r\n\t *   text += stream.get(i).text;\r\n\t * }\r\n\t * ```\r\n\t *\r\n\t * @param start The first token in the interval to get text for.\r\n\t * @param stop The last token in the interval to get text for (inclusive).\r\n\t * @returns The text of all tokens lying between the specified `start`\r\n\t * and `stop` tokens.\r\n\t *\r\n\t * @throws UnsupportedOperationException if this stream does not support\r\n\t * this method for the specified tokens\r\n\t */\r\n\t//@NotNull\r\n\tgetTextFromRange(start: any, stop: any): string;\r\n}\r\n"]}