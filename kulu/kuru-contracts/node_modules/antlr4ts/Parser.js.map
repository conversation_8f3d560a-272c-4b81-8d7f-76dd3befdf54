{"version": 3, "file": "Parser.js", "sourceRoot": "", "sources": ["../../src/Parser.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;AAKH,sCAAsC;AAKtC,+EAA4E;AAC5E,2DAAwD;AAGxD,iEAA8D;AAE9D,gDAA6C;AAC7C,sDAAmD;AAGnD,mCAAgC;AAChC,6CAA2D;AAC3D,+CAA4C;AAC5C,iEAA8D;AAK9D,yEAAsE;AAEtE,6CAA0C;AAG1C,sDAAmD;AACnD,mCAAgC;AAKhC,MAAM,aAAa;IAClB,YAAoB,SAAmB,EAAU,WAAwB;QAArD,cAAS,GAAT,SAAS,CAAU;QAAU,gBAAW,GAAX,WAAW,CAAa;IACzE,CAAC;IAGM,cAAc,CAAC,GAAsB;QAC3C,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;YACrD,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAGM,aAAa,CAAC,GAAsB;QAC1C,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;YACrD,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAGM,cAAc,CAAC,IAAe;QACpC,sBAAsB;IACvB,CAAC;IAGM,aAAa,CAAC,IAAkB;QACtC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC,WAAW,CAAC;QACtC,IAAI,KAAK,GAAU,IAAI,CAAC,MAAM,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/E,CAAC;CACD;AAtBA;IADC,qBAAQ;mDAIR;AAGD;IADC,qBAAQ;kDAIR;AAGD;IADC,qBAAQ;mDAGR;AAGD;IADC,qBAAQ;kDAKR;AAGF,4FAA4F;AAC5F,MAAsB,MAAO,SAAQ,uBAAqC;IAsEzE,YAAY,KAAkB;QAC7B,KAAK,EAAE,CAAC;QA9DT;;;;;;WAMG;QAEO,gBAAW,GAAuB,IAAI,2CAAoB,EAAE,CAAC;QAUpD,qBAAgB,GAAiB,IAAI,2BAAY,EAAE,CAAC;QASvE;;;;;WAKG;QACK,qBAAgB,GAAY,IAAI,CAAC;QAWzC;;;;;WAKG;QACO,oBAAe,GAAwB,EAAE,CAAC;QAEpD;;;WAGG;QACO,kBAAa,GAAW,CAAC,CAAC;QAEpC,yEAAyE;QAC/D,eAAU,GAAY,KAAK,CAAC;QAIrC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC1B,CAAC;IAKM,KAAK,CAAC,UAAoB;QAChC,wEAAwE;QACxE,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE;YAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACzB;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,SAAgB,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,WAAW,GAAiB,IAAI,CAAC,WAAW,CAAC;QACjD,IAAI,WAAW,IAAI,IAAI,EAAE;YACxB,WAAW,CAAC,KAAK,EAAE,CAAC;SACpB;IACF,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IAEI,KAAK,CAAC,KAAa;QACzB,IAAI,CAAC,GAAU,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;YACrB,IAAI,KAAK,KAAK,aAAK,CAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACvB;YACD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;SACf;aACI;YACJ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;gBACjD,qEAAqE;gBACrE,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aAC3D;SACD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IAEI,aAAa;QACnB,IAAI,CAAC,GAAU,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;SACf;aACI;YACJ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;gBACjD,qEAAqE;gBACrE,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aAC3D;SACD;QAED,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAI,cAAc,CAAC,eAAwB;QAC1C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACH,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAGM,iBAAiB;QACvB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACI,gBAAgB,CAAU,QAA2B;QAC3D,IAAI,QAAQ,IAAI,IAAI,EAAE;YACrB,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;OASG;IACI,mBAAmB,CAAC,QAA2B;QACrD,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;QAClE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACjB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACtC;IACF,CAAC;IAGD;;;;OAIG;IACI,oBAAoB;QAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACO,qBAAqB;QAC9B,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;YAC1C,IAAI,QAAQ,CAAC,cAAc,EAAE;gBAC5B,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnC;YAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC9B;IACF,CAAC;IAED;;;;OAIG;IACO,oBAAoB;QAC7B,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1D,IAAI,QAAQ,GAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7B,IAAI,QAAQ,CAAC,aAAa,EAAE;gBAC3B,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClC;SACD;IACF,CAAC;IAED;;;;;OAKG;IACH,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IAEI,oBAAoB;QAC1B,IAAI,aAAa,GAAW,IAAI,CAAC,aAAa,CAAC;QAC/C,IAAI,aAAa,IAAI,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;SACxF;QAED,IAAI,MAAM,GAAG,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,IAAI,sBAAsB,GAA8B,IAAI,qDAAyB,EAAE,CAAC;YACxF,sBAAsB,CAAC,+BAA+B,GAAG,IAAI,CAAC;YAC9D,MAAM,GAAG,IAAI,iCAAe,CAAC,sBAAsB,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;YACnG,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;SACrD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAqBY,uBAAuB,CAAC,OAAe,EAAE,gBAAwB,EAAE,KAAa;;YAC5F,IAAI,CAAC,KAAK,EAAE;gBACX,IAAI,IAAI,CAAC,WAAW,EAAE;oBACrB,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;oBAC/C,IAAI,WAAW,YAAY,aAAK,EAAE;wBACjC,KAAK,GAAG,WAAW,CAAC;qBACpB;iBACD;gBAED,IAAI,CAAC,KAAK,EAAE;oBACX,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;iBACxD;aACD;YAED,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,GAAG,2CAAa,wCAAwC,EAAC,CAAC;YAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,uBAAuB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChE,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACnD,CAAC;KAAA;IAGD,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,IAAI,YAAY,CAAU,OAA2B;QACpD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;IAC5B,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED,iDAAiD;IACjD,IAAI,WAAW,CAAC,KAAkB;QACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACrB,CAAC;IAED;;OAEG;IAEH,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAKM,oBAAoB,CAAC,GAAW,EAAE,cAA6B,EAAE,CAAoC;QAC3G,IAAI,cAAc,KAAK,SAAS,EAAE;YACjC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC;SACnC;aAAM,IAAI,cAAc,KAAK,IAAI,EAAE;YACnC,cAAc,GAAG,SAAS,CAAC;SAC3B;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC,CAAC;QACtB,IAAI,kBAAkB,GAAW,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,IAAI,IAAI,EAAE;YAC3B,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;YAC3B,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;SACvD;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC/C,IAAI,QAAQ,CAAC,WAAW,EAAE;YACzB,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SAC7E;IACF,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,OAAO;QACb,IAAI,CAAC,GAAU,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC3B;QACD,IAAI,WAAW,GAAY,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC;QAC7D,IAAI,IAAI,CAAC,gBAAgB,IAAI,WAAW,EAAE;YACzC,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;gBAC/C,IAAI,IAAI,GAAc,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjF,IAAI,WAAW,EAAE;oBAChB,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;wBAC1C,IAAI,QAAQ,CAAC,cAAc,EAAE;4BAC5B,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;yBAC9B;qBACD;iBACD;aACD;iBACI;gBACJ,IAAI,IAAI,GAAiB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,WAAW,EAAE;oBAChB,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;wBAC1C,IAAI,QAAQ,CAAC,aAAa,EAAE;4BAC3B,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;yBAC7B;qBACD;iBACD;aACD;SACD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,MAAyB,EAAE,CAAQ;QAC5D,OAAO,IAAI,2BAAY,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,MAAyB,EAAE,CAAQ;QACzD,OAAO,IAAI,qBAAS,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAES,qBAAqB;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAwC,CAAC;QAChE,oDAAoD;QACpD,IAAI,MAAM,IAAI,IAAI,EAAE;YACnB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3B;IACF,CAAC;IAED;;;OAGG;IACI,SAAS,CAAU,QAA2B,EAAE,KAAa,EAAE,SAAiB;QACtF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC7B;QACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC9B,CAAC;IAEM,qBAAqB,CAAC,QAA2B,EAAE,KAAa,EAAE,SAAiB;QACzF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAsB,CAAC;YACxF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5B,eAAe,CAAC,OAAO,GAAG,QAAQ,CAAC;YACnC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC9B,CAAC;IAEM,QAAQ;QACd,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,0EAA0E;YAC1E,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B;SACjE;aACI;YACJ,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;SAC7E;QACD,qDAAqD;QACrD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAA4B,CAAC;IACpD,CAAC;IAEM,aAAa,CAAC,QAA2B,EAAE,MAAc;QAC/D,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC;QAC5B,6DAA6D;QAC7D,uCAAuC;QACvC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YACpD,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAwC,CAAC;YAChE,IAAI,MAAM,IAAI,IAAI,EAAE;gBACnB,MAAM,CAAC,eAAe,EAAE,CAAC;gBACzB,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAC1B;SACD;QACD,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;YAClC,OAAO,CAAC,CAAC,CAAC;SACV;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAEM,kBAAkB,CAAC,QAA2B,EAAE,KAAa,EAAE,SAAiB,EAAE,UAAkB;QAC1G,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,gDAAgD;IAC/E,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,QAA2B,EAAE,KAAa,EAAE,SAAiB;QAC3F,IAAI,QAAQ,GAAsB,IAAI,CAAC,IAAI,CAAC;QAC5C,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC;QAC5B,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;QAC/B,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QACnC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,gDAAgD;IAC/E,CAAC;IAEM,uBAAuB,CAAC,UAA6B;QAC3D,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,MAAM,GAAsB,IAAI,CAAC,IAAI,CAAC,CAAC,kCAAkC;QAE7E,8DAA8D;QAC9D,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;gBAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAA4B,CAAC;aACnD;SACD;aACI;YACJ,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;SACvB;QAED,iBAAiB;QACjB,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,IAAI,UAAU,IAAI,IAAI,EAAE;YAChD,2CAA2C;YAC3C,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SAC5B;IACF,CAAC;IAEM,kBAAkB,CAAC,SAAiB;QAC1C,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,SAAS,EAAE;YACtC,CAAC,GAAG,CAAC,CAAC,OAA4B,CAAC;SACnC;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,IAAI,OAAO,CAAC,GAAsB;QACjC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IACjB,CAAC;IAGM,QAAQ,CAAW,QAAqB,EAAE,UAAkB;QAClE,OAAO,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAGM,wBAAwB;QAC9B,OAAO,IAAI,mDAAwB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC/D,CAAC;IAEM,SAAS,CAAC,OAAe;QAC/B,0BAA0B;QAC1B,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,eAAe,CAAC,MAAc;QACtC,+CAA+C;QAC7C,IAAI,GAAG,GAAQ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACpC,IAAI,GAAG,GAAsB,IAAI,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,GAAa,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,SAAS,GAAgB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC;SACZ;QACH,2DAA2D;QACzD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,KAAK,CAAC;SACb;QAED,OAAO,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,aAAa,IAAI,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,EAAE;YAClF,IAAI,aAAa,GAAa,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC5D,IAAI,EAAE,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,CAAmB,CAAC;YACvD,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YAC3C,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC/B,OAAO,IAAI,CAAC;aACZ;YAED,GAAG,GAAG,GAAG,CAAC,OAA4B,CAAC;SACvC;QAED,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAK,CAAC,OAAO,CAAC,IAAI,MAAM,KAAK,aAAK,CAAC,GAAG,EAAE;YAC9D,OAAO,IAAI,CAAC;SACZ;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IAEI,iBAAiB;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7D,CAAC;IAGM,kCAAkC;QACxC,IAAI,GAAG,GAAQ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,GAAa,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,OAAO,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,2EAA2E;IACpE,YAAY,CAAC,QAAgB;QACnC,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,OAAO,SAAS,CAAC;SACjB;QACD,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAED,IAAI,WAAW,KAAwB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1D;;;;;;OAMG;IAEI,sBAAsB,CAAC,MAAmB,IAAI,CAAC,IAAI;QACzD,IAAI,CAAC,GAA4B,GAAG,CAAC,CAAI,4CAA4C;QACrF,IAAI,SAAS,GAAa,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,KAAK,GAAa,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,IAAI,EAAE;YACjB,sCAAsC;YACtC,IAAI,SAAS,GAAW,CAAC,CAAC,SAAS,CAAC;YACpC,IAAI,SAAS,GAAG,CAAC,EAAE;gBAClB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;iBAAM;gBACN,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;aACjC;YACD,CAAC,GAAG,CAAC,CAAC,OAAsB,CAAC;SAC7B;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAED,wCAAwC;IACjC,aAAa;QACnB,IAAI,CAAC,GAAa,EAAE,CAAC;QACrB,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YAC/C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACtD;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED,wCAAwC;IACjC,OAAO;QACb,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YAC/C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;gBACjB,IAAI,OAAO,EAAE;oBACZ,OAAO,CAAC,GAAG,EAAE,CAAC;iBACd;gBACD,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;gBAC9C,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpE,OAAO,GAAG,IAAI,CAAC;aACf;SACD;IACF,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAC/B,CAAC;IAGD,IAAI,SAAS;QACZ,OAAO,qCAAO,6BAA6B,GAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACvD,IAAI,MAAM,GAAuB,IAAI,CAAC,WAAW,CAAC;YAClD,IAAI,MAAM,YAAY,CAAC,CAAC,qBAAqB,EAAE;gBAC9C,OAAO,IAAI,qBAAS,CAAC,MAAM,CAAC,CAAC;aAC7B;YAED,OAAO,SAAS,CAAC;QAClB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACU,UAAU,CAAC,OAAgB;;YACvC,IAAI,CAAC,GAAG,2CAAa,6BAA6B,EAAC,CAAC;YACpD,IAAI,MAAM,GAAuB,IAAI,CAAC,WAAW,CAAC;YAClD,IAAI,OAAO,EAAE;gBACZ,IAAI,CAAC,CAAC,MAAM,YAAY,CAAC,CAAC,qBAAqB,CAAC,EAAE;oBACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;iBACrD;aACD;iBAAM,IAAI,MAAM,YAAY,CAAC,CAAC,qBAAqB,EAAE;gBACrD,IAAI,CAAC,WAAW,GAAG,IAAI,uCAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;aAC1D;YAED,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAChE,CAAC;KAAA;IAED;;OAEG;IACH,IAAI,OAAO,CAAC,KAAc;QACzB,IAAI,CAAC,KAAK,EAAE;YACX,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;aACzB;SACD;aACI;YACJ,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACvC;iBAAM;gBACN,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9D;YAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACpC;IACF,CAAC;IAED;;;OAGG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;IAC7B,CAAC;;AA11BD;;;;;GAKG;AACqB,yBAAkB,GAAG,IAAI,GAAG,EAAe,CAAC;AAUpE;IADC,oBAAO;2CAC+D;AAqGvE;IADC,oBAAO;mCAmBP;AAqBD;IADC,oBAAO;2CAiBP;AAiCD;IADC,oBAAO;+CAGP;AA6BD;IAAyB,WAAA,oBAAO,CAAA;8CAM/B;AAkFD;IADC,oBAAO;kDAgBP;AA0CD;IADC,oBAAO;IAKU,WAAA,oBAAO,CAAA;0CAFxB;AAOD;IADC,qBAAQ;yCAGR;AAYD;IADC,oBAAO;0CAGP;AA+GD;IAAkB,WAAA,oBAAO,CAAA;uCAQxB;AAkID;IADC,qBAAQ;IACQ,WAAA,qBAAQ,CAAA;sCAExB;AAGD;IADC,qBAAQ;sDAGR;AAiED;IADC,oBAAO;+CAGP;AAGD;IADC,oBAAO;gEAKP;AAmED;IADC,qBAAQ;uCAUR;AA7yBF,wBA41BC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:52.4399193-07:00\r\n\r\nimport * as assert from \"assert\";\r\nimport * as Utils from \"./misc/Utils\";\r\n\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { ANTLRErrorStrategy } from \"./ANTLRErrorStrategy\";\r\nimport { ATN } from \"./atn/ATN\";\r\nimport { ATNDeserializationOptions } from \"./atn/ATNDeserializationOptions\";\r\nimport { ATNDeserializer } from \"./atn/ATNDeserializer\";\r\nimport { ATNSimulator } from \"./atn/ATNSimulator\";\r\nimport { ATNState } from \"./atn/ATNState\";\r\nimport { DefaultErrorStrategy } from \"./DefaultErrorStrategy\";\r\nimport { DFA } from \"./dfa/DFA\";\r\nimport { ErrorNode } from \"./tree/ErrorNode\";\r\nimport { IntegerStack } from \"./misc/IntegerStack\";\r\nimport { IntervalSet } from \"./misc/IntervalSet\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { Lexer } from \"./Lexer\";\r\nimport { Override, NotNull, Nullable } from \"./Decorators\";\r\nimport { ParseInfo } from \"./atn/ParseInfo\";\r\nimport { ParserATNSimulator } from \"./atn/ParserATNSimulator\";\r\nimport { ParserErrorListener } from \"./ParserErrorListener\";\r\nimport { ParserRuleContext } from \"./ParserRuleContext\";\r\nimport { ParseTreeListener } from \"./tree/ParseTreeListener\";\r\nimport { ParseTreePattern } from \"./tree/pattern/ParseTreePattern\";\r\nimport { ProxyParserErrorListener } from \"./ProxyParserErrorListener\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { RuleContext } from \"./RuleContext\";\r\nimport { RuleTransition } from \"./atn/RuleTransition\";\r\nimport { TerminalNode } from \"./tree/TerminalNode\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\nimport { TokenSource } from \"./TokenSource\";\r\nimport { TokenStream } from \"./TokenStream\";\r\n\r\nclass TraceListener implements ParseTreeListener {\r\n\tconstructor(private ruleNames: string[], private tokenStream: TokenStream) {\r\n\t}\r\n\r\n\t@Override\r\n\tpublic enterEveryRule(ctx: ParserRuleContext): void {\r\n\t\tconsole.log(\"enter   \" + this.ruleNames[ctx.ruleIndex] +\r\n\t\t\t\", LT(1)=\" + this.tokenStream.LT(1).text);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic exitEveryRule(ctx: ParserRuleContext): void {\r\n\t\tconsole.log(\"exit    \" + this.ruleNames[ctx.ruleIndex] +\r\n\t\t\t\", LT(1)=\" + this.tokenStream.LT(1).text);\r\n\t}\r\n\r\n\t@Override\r\n\tpublic visitErrorNode(node: ErrorNode): void {\r\n\t\t// intentionally empty\r\n\t}\r\n\r\n\t@Override\r\n\tpublic visitTerminal(node: TerminalNode): void {\r\n\t\tlet parent = node.parent!.ruleContext;\r\n\t\tlet token: Token = node.symbol;\r\n\t\tconsole.log(\"consume \" + token + \" rule \" + this.ruleNames[parent.ruleIndex]);\r\n\t}\r\n}\r\n\r\n/** This is all the parsing support code essentially; most of it is error recovery stuff. */\r\nexport abstract class Parser extends Recognizer<Token, ParserATNSimulator> {\r\n\t/**\r\n\t * This field maps from the serialized ATN string to the deserialized {@link ATN} with\r\n\t * bypass alternatives.\r\n\t *\r\n\t * @see ATNDeserializationOptions.isGenerateRuleBypassTransitions\r\n\t */\r\n\tprivate static readonly bypassAltsAtnCache = new Map<string, ATN>();\r\n\r\n\t/**\r\n\t * The error handling strategy for the parser. The default value is a new\r\n\t * instance of {@link DefaultErrorStrategy}.\r\n\t *\r\n\t * @see #getErrorHandler\r\n\t * @see #setErrorHandler\r\n\t */\r\n\t@NotNull\r\n\tprotected _errHandler: ANTLRErrorStrategy = new DefaultErrorStrategy();\r\n\r\n\t/**\r\n\t * The input stream.\r\n\t *\r\n\t * @see #getInputStream\r\n\t * @see #setInputStream\r\n\t */\r\n\tprotected _input!: TokenStream;\r\n\r\n\tprotected readonly _precedenceStack: IntegerStack = new IntegerStack();\r\n\r\n\t/**\r\n\t * The {@link ParserRuleContext} object for the currently executing rule.\r\n\t *\r\n\t * This is always non-undefined during the parsing process.\r\n\t */\r\n\tprotected _ctx!: ParserRuleContext;\r\n\r\n\t/**\r\n\t * Specifies whether or not the parser should construct a parse tree during\r\n\t * the parsing process. The default value is `true`.\r\n\t *\r\n\t * @see `buildParseTree`\r\n\t */\r\n\tprivate _buildParseTrees: boolean = true;\r\n\r\n\t/**\r\n\t * When {@link #setTrace}`(true)` is called, a reference to the\r\n\t * {@link TraceListener} is stored here so it can be easily removed in a\r\n\t * later call to {@link #setTrace}`(false)`. The listener itself is\r\n\t * implemented as a parser listener so this field is not directly used by\r\n\t * other parser methods.\r\n\t */\r\n\tprivate _tracer: TraceListener | undefined;\r\n\r\n\t/**\r\n\t * The list of {@link ParseTreeListener} listeners registered to receive\r\n\t * events during the parse.\r\n\t *\r\n\t * @see #addParseListener\r\n\t */\r\n\tprotected _parseListeners: ParseTreeListener[] = [];\r\n\r\n\t/**\r\n\t * The number of syntax errors reported during parsing. This value is\r\n\t * incremented each time {@link #notifyErrorListeners} is called.\r\n\t */\r\n\tprotected _syntaxErrors: number = 0;\r\n\r\n\t/** Indicates parser has match()ed EOF token. See {@link #exitRule()}. */\r\n\tprotected matchedEOF: boolean = false;\r\n\r\n\tconstructor(input: TokenStream) {\r\n\t\tsuper();\r\n\t\tthis._precedenceStack.push(0);\r\n\t\tthis.inputStream = input;\r\n\t}\r\n\r\n\t/** reset the parser's state */\r\n\tpublic reset(): void;\r\n\tpublic reset(resetInput: boolean): void;\r\n\tpublic reset(resetInput?: boolean): void {\r\n\t\t// Note: this method executes when not parsing, so _ctx can be undefined\r\n\t\tif (resetInput === undefined || resetInput) {\r\n\t\t\tthis.inputStream.seek(0);\r\n\t\t}\r\n\r\n\t\tthis._errHandler.reset(this);\r\n\t\tthis._ctx = undefined as any;\r\n\t\tthis._syntaxErrors = 0;\r\n\t\tthis.matchedEOF = false;\r\n\t\tthis.isTrace = false;\r\n\t\tthis._precedenceStack.clear();\r\n\t\tthis._precedenceStack.push(0);\r\n\t\tlet interpreter: ATNSimulator = this.interpreter;\r\n\t\tif (interpreter != null) {\r\n\t\t\tinterpreter.reset();\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Match current input symbol against `ttype`. If the symbol type\r\n\t * matches, {@link ANTLRErrorStrategy#reportMatch} and {@link #consume} are\r\n\t * called to complete the match process.\r\n\t *\r\n\t * If the symbol type does not match,\r\n\t * {@link ANTLRErrorStrategy#recoverInline} is called on the current error\r\n\t * strategy to attempt recovery. If {@link #getBuildParseTree} is\r\n\t * `true` and the token index of the symbol returned by\r\n\t * {@link ANTLRErrorStrategy#recoverInline} is -1, the symbol is added to\r\n\t * the parse tree by calling {@link #createErrorNode(ParserRuleContext, Token)} then\r\n\t * {@link ParserRuleContext#addErrorNode(ErrorNode)}.\r\n\t *\r\n\t * @param ttype the token type to match\r\n\t * @returns the matched symbol\r\n\t * @ if the current input symbol did not match\r\n\t * `ttype` and the error strategy could not recover from the\r\n\t * mismatched symbol\r\n\t */\r\n\t@NotNull\r\n\tpublic match(ttype: number): Token {\r\n\t\tlet t: Token = this.currentToken;\r\n\t\tif (t.type === ttype) {\r\n\t\t\tif (ttype === Token.EOF) {\r\n\t\t\t\tthis.matchedEOF = true;\r\n\t\t\t}\r\n\t\t\tthis._errHandler.reportMatch(this);\r\n\t\t\tthis.consume();\r\n\t\t}\r\n\t\telse {\r\n\t\t\tt = this._errHandler.recoverInline(this);\r\n\t\t\tif (this._buildParseTrees && t.tokenIndex === -1) {\r\n\t\t\t\t// we must have conjured up a new token during single token insertion\r\n\t\t\t\t// if it's not the current symbol\r\n\t\t\t\tthis._ctx.addErrorNode(this.createErrorNode(this._ctx, t));\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn t;\r\n\t}\r\n\r\n\t/**\r\n\t * Match current input symbol as a wildcard. If the symbol type matches\r\n\t * (i.e. has a value greater than 0), {@link ANTLRErrorStrategy#reportMatch}\r\n\t * and {@link #consume} are called to complete the match process.\r\n\t *\r\n\t * If the symbol type does not match,\r\n\t * {@link ANTLRErrorStrategy#recoverInline} is called on the current error\r\n\t * strategy to attempt recovery. If {@link #getBuildParseTree} is\r\n\t * `true` and the token index of the symbol returned by\r\n\t * {@link ANTLRErrorStrategy#recoverInline} is -1, the symbol is added to\r\n\t * the parse tree by calling {@link Parser#createErrorNode(ParserRuleContext, Token)} then\r\n\t * {@link ParserRuleContext#addErrorNode(ErrorNode)}.\r\n\t *\r\n\t * @returns the matched symbol\r\n\t * @ if the current input symbol did not match\r\n\t * a wildcard and the error strategy could not recover from the mismatched\r\n\t * symbol\r\n\t */\r\n\t@NotNull\r\n\tpublic matchWildcard(): Token {\r\n\t\tlet t: Token = this.currentToken;\r\n\t\tif (t.type > 0) {\r\n\t\t\tthis._errHandler.reportMatch(this);\r\n\t\t\tthis.consume();\r\n\t\t}\r\n\t\telse {\r\n\t\t\tt = this._errHandler.recoverInline(this);\r\n\t\t\tif (this._buildParseTrees && t.tokenIndex === -1) {\r\n\t\t\t\t// we must have conjured up a new token during single token insertion\r\n\t\t\t\t// if it's not the current symbol\r\n\t\t\t\tthis._ctx.addErrorNode(this.createErrorNode(this._ctx, t));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn t;\r\n\t}\r\n\r\n\t/**\r\n\t * Track the {@link ParserRuleContext} objects during the parse and hook\r\n\t * them up using the {@link ParserRuleContext#children} list so that it\r\n\t * forms a parse tree. The {@link ParserRuleContext} returned from the start\r\n\t * rule represents the root of the parse tree.\r\n\t *\r\n\t * Note that if we are not building parse trees, rule contexts only point\r\n\t * upwards. When a rule exits, it returns the context but that gets garbage\r\n\t * collected if nobody holds a reference. It points upwards but nobody\r\n\t * points at it.\r\n\t *\r\n\t * When we build parse trees, we are adding all of these contexts to\r\n\t * {@link ParserRuleContext#children} list. Contexts are then not candidates\r\n\t * for garbage collection.\r\n\t */\r\n\tset buildParseTree(buildParseTrees: boolean) {\r\n\t\tthis._buildParseTrees = buildParseTrees;\r\n\t}\r\n\r\n\t/**\r\n\t * Gets whether or not a complete parse tree will be constructed while\r\n\t * parsing. This property is `true` for a newly constructed parser.\r\n\t *\r\n\t * @returns `true` if a complete parse tree will be constructed while\r\n\t * parsing, otherwise `false`\r\n\t */\r\n\tget buildParseTree(): boolean {\r\n\t\treturn this._buildParseTrees;\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getParseListeners(): ParseTreeListener[] {\r\n\t\treturn this._parseListeners;\r\n\t}\r\n\r\n\t/**\r\n\t * Registers `listener` to receive events during the parsing process.\r\n\t *\r\n\t * To support output-preserving grammar transformations (including but not\r\n\t * limited to left-recursion removal, automated left-factoring, and\r\n\t * optimized code generation), calls to listener methods during the parse\r\n\t * may differ substantially from calls made by\r\n\t * {@link ParseTreeWalker#DEFAULT} used after the parse is complete. In\r\n\t * particular, rule entry and exit events may occur in a different order\r\n\t * during the parse than after the parser. In addition, calls to certain\r\n\t * rule entry methods may be omitted.\r\n\t *\r\n\t * With the following specific exceptions, calls to listener events are\r\n\t * *deterministic*, i.e. for identical input the calls to listener\r\n\t * methods will be the same.\r\n\t *\r\n\t * * Alterations to the grammar used to generate code may change the\r\n\t *   behavior of the listener calls.\r\n\t * * Alterations to the command line options passed to ANTLR 4 when\r\n\t *   generating the parser may change the behavior of the listener calls.\r\n\t * * Changing the version of the ANTLR Tool used to generate the parser\r\n\t *   may change the behavior of the listener calls.\r\n\t *\r\n\t * @param listener the listener to add\r\n\t *\r\n\t * @throws {@link TypeError} if `listener` is `undefined`\r\n\t */\r\n\tpublic addParseListener(@NotNull listener: ParseTreeListener): void {\r\n\t\tif (listener == null) {\r\n\t\t\tthrow new TypeError(\"listener cannot be null\");\r\n\t\t}\r\n\r\n\t\tthis._parseListeners.push(listener);\r\n\t}\r\n\r\n\t/**\r\n\t * Remove `listener` from the list of parse listeners.\r\n\t *\r\n\t * If `listener` is `undefined` or has not been added as a parse\r\n\t * listener, this method does nothing.\r\n\t *\r\n\t * @see #addParseListener\r\n\t *\r\n\t * @param listener the listener to remove\r\n\t */\r\n\tpublic removeParseListener(listener: ParseTreeListener): void {\r\n\t\tlet index = this._parseListeners.findIndex((l) => l === listener);\r\n\t\tif (index !== -1) {\r\n\t\t\tthis._parseListeners.splice(index, 1);\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * Remove all parse listeners.\r\n\t *\r\n\t * @see #addParseListener\r\n\t */\r\n\tpublic removeParseListeners(): void {\r\n\t\tthis._parseListeners.length = 0;\r\n\t}\r\n\r\n\t/**\r\n\t * Notify any parse listeners of an enter rule event.\r\n\t *\r\n\t * @see #addParseListener\r\n\t */\r\n\tprotected triggerEnterRuleEvent(): void {\r\n\t\tfor (let listener of this._parseListeners) {\r\n\t\t\tif (listener.enterEveryRule) {\r\n\t\t\t\tlistener.enterEveryRule(this._ctx);\r\n\t\t\t}\r\n\r\n\t\t\tthis._ctx.enterRule(listener);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Notify any parse listeners of an exit rule event.\r\n\t *\r\n\t * @see #addParseListener\r\n\t */\r\n\tprotected triggerExitRuleEvent(): void {\r\n\t\t// reverse order walk of listeners\r\n\t\tfor (let i = this._parseListeners.length - 1; i >= 0; i--) {\r\n\t\t\tlet listener: ParseTreeListener = this._parseListeners[i];\r\n\t\t\tthis._ctx.exitRule(listener);\r\n\t\t\tif (listener.exitEveryRule) {\r\n\t\t\t\tlistener.exitEveryRule(this._ctx);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Gets the number of syntax errors reported during parsing. This value is\r\n\t * incremented each time {@link #notifyErrorListeners} is called.\r\n\t *\r\n\t * @see #notifyErrorListeners\r\n\t */\r\n\tget numberOfSyntaxErrors(): number {\r\n\t\treturn this._syntaxErrors;\r\n\t}\r\n\r\n\tget tokenFactory(): TokenFactory {\r\n\t\treturn this._input.tokenSource.tokenFactory;\r\n\t}\r\n\r\n\t/**\r\n\t * The ATN with bypass alternatives is expensive to create so we create it\r\n\t * lazily.\r\n\t *\r\n\t * @ if the current parser does not\r\n\t * implement the `serializedATN` property.\r\n\t */\r\n\t@NotNull\r\n\tpublic getATNWithBypassAlts(): ATN {\r\n\t\tlet serializedAtn: string = this.serializedATN;\r\n\t\tif (serializedAtn == null) {\r\n\t\t\tthrow new Error(\"The current parser does not support an ATN with bypass alternatives.\");\r\n\t\t}\r\n\r\n\t\tlet result = Parser.bypassAltsAtnCache.get(serializedAtn);\r\n\t\tif (result == null) {\r\n\t\t\tlet deserializationOptions: ATNDeserializationOptions = new ATNDeserializationOptions();\r\n\t\t\tdeserializationOptions.isGenerateRuleBypassTransitions = true;\r\n\t\t\tresult = new ATNDeserializer(deserializationOptions).deserialize(Utils.toCharArray(serializedAtn));\r\n\t\t\tParser.bypassAltsAtnCache.set(serializedAtn, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\t/**\r\n\t * The preferred method of getting a tree pattern. For example, here's a\r\n\t * sample use:\r\n\t *\r\n\t * ```\r\n\t * let t: ParseTree = parser.expr();\r\n\t * let p: ParseTreePattern = await parser.compileParseTreePattern(\"<ID>+0\", MyParser.RULE_expr);\r\n\t * let m: ParseTreeMatch = p.match(t);\r\n\t * let id: string = m.get(\"ID\");\r\n\t * ```\r\n\t */\r\n\tpublic compileParseTreePattern(pattern: string, patternRuleIndex: number): Promise<ParseTreePattern>;\r\n\r\n\t/**\r\n\t * The same as {@link #compileParseTreePattern(String, int)} but specify a\r\n\t * {@link Lexer} rather than trying to deduce it from this parser.\r\n\t */\r\n\tpublic compileParseTreePattern(pattern: string, patternRuleIndex: number, lexer?: Lexer): Promise<ParseTreePattern>;\r\n\r\n\tpublic async compileParseTreePattern(pattern: string, patternRuleIndex: number, lexer?: Lexer): Promise<ParseTreePattern> {\r\n\t\tif (!lexer) {\r\n\t\t\tif (this.inputStream) {\r\n\t\t\t\tlet tokenSource = this.inputStream.tokenSource;\r\n\t\t\t\tif (tokenSource instanceof Lexer) {\r\n\t\t\t\t\tlexer = tokenSource;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (!lexer) {\r\n\t\t\t\tthrow new Error(\"Parser can't discover a lexer to use\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet currentLexer = lexer;\r\n\t\tlet m = await import(\"./tree/pattern/ParseTreePatternMatcher\");\r\n\t\tlet matcher = new m.ParseTreePatternMatcher(currentLexer, this);\r\n\t\treturn matcher.compile(pattern, patternRuleIndex);\r\n\t}\r\n\r\n\t@NotNull\r\n\tget errorHandler(): ANTLRErrorStrategy {\r\n\t\treturn this._errHandler;\r\n\t}\r\n\r\n\tset errorHandler(@NotNull handler: ANTLRErrorStrategy) {\r\n\t\tthis._errHandler = handler;\r\n\t}\r\n\r\n\t@Override\r\n\tget inputStream(): TokenStream {\r\n\t\treturn this._input;\r\n\t}\r\n\r\n\t/** Set the token stream and reset the parser. */\r\n\tset inputStream(input: TokenStream) {\r\n\t\tthis.reset(false);\r\n\t\tthis._input = input;\r\n\t}\r\n\r\n\t/** Match needs to return the current input symbol, which gets put\r\n\t *  into the label for the associated token ref; e.g., x=ID.\r\n\t */\r\n\t@NotNull\r\n\tget currentToken(): Token {\r\n\t\treturn this._input.LT(1);\r\n\t}\r\n\r\n\tpublic notifyErrorListeners(/*@NotNull*/ msg: string): void;\r\n\tpublic notifyErrorListeners(/*@NotNull*/ msg: string, /*@NotNull*/ offendingToken: Token | null, e: RecognitionException | undefined): void;\r\n\r\n\tpublic notifyErrorListeners(msg: string, offendingToken?: Token | null, e?: RecognitionException | undefined): void {\r\n\t\tif (offendingToken === undefined) {\r\n\t\t\toffendingToken = this.currentToken;\r\n\t\t} else if (offendingToken === null) {\r\n\t\t\toffendingToken = undefined;\r\n\t\t}\r\n\r\n\t\tthis._syntaxErrors++;\r\n\t\tlet line: number = -1;\r\n\t\tlet charPositionInLine: number = -1;\r\n\t\tif (offendingToken != null) {\r\n\t\t\tline = offendingToken.line;\r\n\t\t\tcharPositionInLine = offendingToken.charPositionInLine;\r\n\t\t}\r\n\r\n\t\tlet listener = this.getErrorListenerDispatch();\r\n\t\tif (listener.syntaxError) {\r\n\t\t\tlistener.syntaxError(this, offendingToken, line, charPositionInLine, msg, e);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Consume and return the [current symbol](`currentToken`).\r\n\t *\r\n\t * E.g., given the following input with `A` being the current\r\n\t * lookahead symbol, this function moves the cursor to `B` and returns\r\n\t * `A`.\r\n\t *\r\n\t * ```\r\n\t * A B\r\n\t * ^\r\n\t * ```\r\n\t *\r\n\t * If the parser is not in error recovery mode, the consumed symbol is added\r\n\t * to the parse tree using {@link ParserRuleContext#addChild(TerminalNode)}, and\r\n\t * {@link ParseTreeListener#visitTerminal} is called on any parse listeners.\r\n\t * If the parser *is* in error recovery mode, the consumed symbol is\r\n\t * added to the parse tree using {@link #createErrorNode(ParserRuleContext, Token)} then\r\n\t * {@link ParserRuleContext#addErrorNode(ErrorNode)} and\r\n\t * {@link ParseTreeListener#visitErrorNode} is called on any parse\r\n\t * listeners.\r\n\t */\r\n\tpublic consume(): Token {\r\n\t\tlet o: Token = this.currentToken;\r\n\t\tif (o.type !== Parser.EOF) {\r\n\t\t\tthis.inputStream.consume();\r\n\t\t}\r\n\t\tlet hasListener: boolean = this._parseListeners.length !== 0;\r\n\t\tif (this._buildParseTrees || hasListener) {\r\n\t\t\tif (this._errHandler.inErrorRecoveryMode(this)) {\r\n\t\t\t\tlet node: ErrorNode = this._ctx.addErrorNode(this.createErrorNode(this._ctx, o));\r\n\t\t\t\tif (hasListener) {\r\n\t\t\t\t\tfor (let listener of this._parseListeners) {\r\n\t\t\t\t\t\tif (listener.visitErrorNode) {\r\n\t\t\t\t\t\t\tlistener.visitErrorNode(node);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlet node: TerminalNode = this.createTerminalNode(this._ctx, o);\r\n\t\t\t\tthis._ctx.addChild(node);\r\n\t\t\t\tif (hasListener) {\r\n\t\t\t\t\tfor (let listener of this._parseListeners) {\r\n\t\t\t\t\t\tif (listener.visitTerminal) {\r\n\t\t\t\t\t\t\tlistener.visitTerminal(node);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn o;\r\n\t}\r\n\r\n\t/**\r\n\t * How to create a token leaf node associated with a parent.\r\n\t * Typically, the terminal node to create is not a function of the parent.\r\n\t *\r\n\t * @since 4.7\r\n\t */\r\n\tpublic createTerminalNode(parent: ParserRuleContext, t: Token): TerminalNode {\r\n\t\treturn new TerminalNode(t);\r\n\t}\r\n\r\n\t/**\r\n\t * How to create an error node, given a token, associated with a parent.\r\n\t * Typically, the error node to create is not a function of the parent.\r\n\t *\r\n\t * @since 4.7\r\n\t */\r\n\tpublic createErrorNode(parent: ParserRuleContext, t: Token): ErrorNode {\r\n\t\treturn new ErrorNode(t);\r\n\t}\r\n\r\n\tprotected addContextToParseTree(): void {\r\n\t\tlet parent = this._ctx._parent as ParserRuleContext | undefined;\r\n\t\t// add current context to parent if we have a parent\r\n\t\tif (parent != null) {\r\n\t\t\tparent.addChild(this._ctx);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Always called by generated parsers upon entry to a rule. Access field\r\n\t * {@link #_ctx} get the current context.\r\n\t */\r\n\tpublic enterRule(@NotNull localctx: ParserRuleContext, state: number, ruleIndex: number): void {\r\n\t\tthis.state = state;\r\n\t\tthis._ctx = localctx;\r\n\t\tthis._ctx._start = this._input.LT(1);\r\n\t\tif (this._buildParseTrees) {\r\n\t\t\tthis.addContextToParseTree();\r\n\t\t}\r\n\t\tthis.triggerEnterRuleEvent();\r\n\t}\r\n\r\n\tpublic enterLeftFactoredRule(localctx: ParserRuleContext, state: number, ruleIndex: number): void {\r\n\t\tthis.state = state;\r\n\t\tif (this._buildParseTrees) {\r\n\t\t\tlet factoredContext = this._ctx.getChild(this._ctx.childCount - 1) as ParserRuleContext;\r\n\t\t\tthis._ctx.removeLastChild();\r\n\t\t\tfactoredContext._parent = localctx;\r\n\t\t\tlocalctx.addChild(factoredContext);\r\n\t\t}\r\n\r\n\t\tthis._ctx = localctx;\r\n\t\tthis._ctx._start = this._input.LT(1);\r\n\t\tif (this._buildParseTrees) {\r\n\t\t\tthis.addContextToParseTree();\r\n\t\t}\r\n\r\n\t\tthis.triggerEnterRuleEvent();\r\n\t}\r\n\r\n\tpublic exitRule(): void {\r\n\t\tif (this.matchedEOF) {\r\n\t\t\t// if we have matched EOF, it cannot consume past EOF so we use LT(1) here\r\n\t\t\tthis._ctx._stop = this._input.LT(1); // LT(1) will be end of file\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis._ctx._stop = this._input.tryLT(-1); // stop node is what we just matched\r\n\t\t}\r\n\t\t// trigger event on _ctx, before it reverts to parent\r\n\t\tthis.triggerExitRuleEvent();\r\n\t\tthis.state = this._ctx.invokingState;\r\n\t\tthis._ctx = this._ctx._parent as ParserRuleContext;\r\n\t}\r\n\r\n\tpublic enterOuterAlt(localctx: ParserRuleContext, altNum: number): void {\r\n\t\tlocalctx.altNumber = altNum;\r\n\t\t// if we have new localctx, make sure we replace existing ctx\r\n\t\t// that is previous child of parse tree\r\n\t\tif (this._buildParseTrees && this._ctx !== localctx) {\r\n\t\t\tlet parent = this._ctx._parent as ParserRuleContext | undefined;\r\n\t\t\tif (parent != null) {\r\n\t\t\t\tparent.removeLastChild();\r\n\t\t\t\tparent.addChild(localctx);\r\n\t\t\t}\r\n\t\t}\r\n\t\tthis._ctx = localctx;\r\n\t}\r\n\r\n\t/**\r\n\t * Get the precedence level for the top-most precedence rule.\r\n\t *\r\n\t * @returns The precedence level for the top-most precedence rule, or -1 if\r\n\t * the parser context is not nested within a precedence rule.\r\n\t */\r\n\tget precedence(): number {\r\n\t\tif (this._precedenceStack.isEmpty) {\r\n\t\t\treturn -1;\r\n\t\t}\r\n\r\n\t\treturn this._precedenceStack.peek();\r\n\t}\r\n\r\n\tpublic enterRecursionRule(localctx: ParserRuleContext, state: number, ruleIndex: number, precedence: number): void {\r\n\t\tthis.state = state;\r\n\t\tthis._precedenceStack.push(precedence);\r\n\t\tthis._ctx = localctx;\r\n\t\tthis._ctx._start = this._input.LT(1);\r\n\t\tthis.triggerEnterRuleEvent(); // simulates rule entry for left-recursive rules\r\n\t}\r\n\r\n\t/** Like {@link #enterRule} but for recursive rules.\r\n\t *  Make the current context the child of the incoming localctx.\r\n\t */\r\n\tpublic pushNewRecursionContext(localctx: ParserRuleContext, state: number, ruleIndex: number): void {\r\n\t\tlet previous: ParserRuleContext = this._ctx;\r\n\t\tprevious._parent = localctx;\r\n\t\tprevious.invokingState = state;\r\n\t\tprevious._stop = this._input.tryLT(-1);\r\n\r\n\t\tthis._ctx = localctx;\r\n\t\tthis._ctx._start = previous._start;\r\n\t\tif (this._buildParseTrees) {\r\n\t\t\tthis._ctx.addChild(previous);\r\n\t\t}\r\n\r\n\t\tthis.triggerEnterRuleEvent(); // simulates rule entry for left-recursive rules\r\n\t}\r\n\r\n\tpublic unrollRecursionContexts(_parentctx: ParserRuleContext): void {\r\n\t\tthis._precedenceStack.pop();\r\n\t\tthis._ctx._stop = this._input.tryLT(-1);\r\n\t\tlet retctx: ParserRuleContext = this._ctx; // save current ctx (return value)\r\n\r\n\t\t// unroll so _ctx is as it was before call to recursive method\r\n\t\tif (this._parseListeners.length > 0) {\r\n\t\t\twhile (this._ctx !== _parentctx) {\r\n\t\t\t\tthis.triggerExitRuleEvent();\r\n\t\t\t\tthis._ctx = this._ctx._parent as ParserRuleContext;\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis._ctx = _parentctx;\r\n\t\t}\r\n\r\n\t\t// hook into tree\r\n\t\tretctx._parent = _parentctx;\r\n\r\n\t\tif (this._buildParseTrees && _parentctx != null) {\r\n\t\t\t// add return ctx into invoking rule's tree\r\n\t\t\t_parentctx.addChild(retctx);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic getInvokingContext(ruleIndex: number): ParserRuleContext | undefined {\r\n\t\tlet p = this._ctx;\r\n\t\twhile (p && p.ruleIndex !== ruleIndex) {\r\n\t\t\tp = p._parent as ParserRuleContext;\r\n\t\t}\r\n\t\treturn p;\r\n\t}\r\n\r\n\tget context(): ParserRuleContext {\r\n\t\treturn this._ctx;\r\n\t}\r\n\r\n\tset context(ctx: ParserRuleContext) {\r\n\t\tthis._ctx = ctx;\r\n\t}\r\n\r\n\t@Override\r\n\tpublic precpred(@Nullable localctx: RuleContext, precedence: number): boolean {\r\n\t\treturn precedence >= this._precedenceStack.peek();\r\n\t}\r\n\r\n\t@Override\r\n\tpublic getErrorListenerDispatch(): ParserErrorListener {\r\n\t\treturn new ProxyParserErrorListener(this.getErrorListeners());\r\n\t}\r\n\r\n\tpublic inContext(context: string): boolean {\r\n\t\t// TODO: useful in parser?\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Checks whether or not `symbol` can follow the current state in the\r\n\t * ATN. The behavior of this method is equivalent to the following, but is\r\n\t * implemented such that the complete context-sensitive follow set does not\r\n\t * need to be explicitly constructed.\r\n\t *\r\n\t * ```\r\n\t * return getExpectedTokens().contains(symbol);\r\n\t * ```\r\n\t *\r\n\t * @param symbol the symbol type to check\r\n\t * @returns `true` if `symbol` can follow the current state in\r\n\t * the ATN, otherwise `false`.\r\n\t */\r\n\tpublic isExpectedToken(symbol: number): boolean {\r\n//   \t\treturn interpreter.atn.nextTokens(_ctx);\r\n\t\tlet atn: ATN = this.interpreter.atn;\r\n\t\tlet ctx: ParserRuleContext = this._ctx;\r\n\t\tlet s: ATNState = atn.states[this.state];\r\n\t\tlet following: IntervalSet = atn.nextTokens(s);\r\n\t\tif (following.contains(symbol)) {\r\n\t\t\treturn true;\r\n\t\t}\r\n//        System.out.println(\"following \"+s+\"=\"+following);\r\n\t\tif (!following.contains(Token.EPSILON)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\twhile (ctx != null && ctx.invokingState >= 0 && following.contains(Token.EPSILON)) {\r\n\t\t\tlet invokingState: ATNState = atn.states[ctx.invokingState];\r\n\t\t\tlet rt = invokingState.transition(0) as RuleTransition;\r\n\t\t\tfollowing = atn.nextTokens(rt.followState);\r\n\t\t\tif (following.contains(symbol)) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\tctx = ctx._parent as ParserRuleContext;\r\n\t\t}\r\n\r\n\t\tif (following.contains(Token.EPSILON) && symbol === Token.EOF) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\treturn false;\r\n\t}\r\n\r\n\tget isMatchedEOF(): boolean {\r\n\t\treturn this.matchedEOF;\r\n\t}\r\n\r\n\t/**\r\n\t * Computes the set of input symbols which could follow the current parser\r\n\t * state and context, as given by {@link #getState} and {@link #getContext},\r\n\t * respectively.\r\n\t *\r\n\t * @see ATN#getExpectedTokens(int, RuleContext)\r\n\t */\r\n\t@NotNull\r\n\tpublic getExpectedTokens(): IntervalSet {\r\n\t\treturn this.atn.getExpectedTokens(this.state, this.context);\r\n\t}\r\n\r\n\t@NotNull\r\n\tpublic getExpectedTokensWithinCurrentRule(): IntervalSet {\r\n\t\tlet atn: ATN = this.interpreter.atn;\r\n\t\tlet s: ATNState = atn.states[this.state];\r\n\t\treturn atn.nextTokens(s);\r\n\t}\r\n\r\n\t/** Get a rule's index (i.e., `RULE_ruleName` field) or -1 if not found. */\r\n\tpublic getRuleIndex(ruleName: string): number {\r\n\t\tlet ruleIndex = this.getRuleIndexMap().get(ruleName);\r\n\t\tif (ruleIndex != null) {\r\n\t\t\treturn ruleIndex;\r\n\t\t}\r\n\t\treturn -1;\r\n\t}\r\n\r\n\tget ruleContext(): ParserRuleContext { return this._ctx; }\r\n\r\n\t/** Return List&lt;String&gt; of the rule names in your parser instance\r\n\t *  leading up to a call to the current rule.  You could override if\r\n\t *  you want more details such as the file/line info of where\r\n\t *  in the ATN a rule is invoked.\r\n\t *\r\n\t *  This is very useful for error messages.\r\n\t */\r\n\r\n\tpublic getRuleInvocationStack(ctx: RuleContext = this._ctx): string[] {\r\n\t\tlet p: RuleContext | undefined = ctx;  \t\t// Workaround for Microsoft/TypeScript#14487\r\n\t\tlet ruleNames: string[] = this.ruleNames;\r\n\t\tlet stack: string[] = [];\r\n\t\twhile (p != null) {\r\n\t\t\t// compute what follows who invoked us\r\n\t\t\tlet ruleIndex: number = p.ruleIndex;\r\n\t\t\tif (ruleIndex < 0) {\r\n\t\t\t\tstack.push(\"n/a\");\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(ruleNames[ruleIndex]);\r\n\t\t\t}\r\n\t\t\tp = p._parent as RuleContext;\r\n\t\t}\r\n\t\treturn stack;\r\n\t}\r\n\r\n\t/** For debugging and other purposes. */\r\n\tpublic getDFAStrings(): string[] {\r\n\t\tlet s: string[] = [];\r\n\t\tfor (let dfa of this._interp.atn.decisionToDFA) {\r\n\t\t\ts.push(dfa.toString(this.vocabulary, this.ruleNames));\r\n\t\t}\r\n\t\treturn s;\r\n\t}\r\n\r\n\t/** For debugging and other purposes. */\r\n\tpublic dumpDFA(): void {\r\n\t\tlet seenOne: boolean = false;\r\n\t\tfor (let dfa of this._interp.atn.decisionToDFA) {\r\n\t\t\tif (!dfa.isEmpty) {\r\n\t\t\t\tif (seenOne) {\r\n\t\t\t\t\tconsole.log();\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(\"Decision \" + dfa.decision + \":\");\r\n\t\t\t\tprocess.stdout.write(dfa.toString(this.vocabulary, this.ruleNames));\r\n\t\t\t\tseenOne = true;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tget sourceName(): string {\r\n\t\treturn this._input.sourceName;\r\n\t}\r\n\r\n\t@Override\r\n\tget parseInfo(): Promise<ParseInfo | undefined> {\r\n\t\treturn import(\"./atn/ProfilingATNSimulator\").then((m) => {\r\n\t\t\tlet interp: ParserATNSimulator = this.interpreter;\r\n\t\t\tif (interp instanceof m.ProfilingATNSimulator) {\r\n\t\t\t\treturn new ParseInfo(interp);\r\n\t\t\t}\r\n\r\n\t\t\treturn undefined;\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * @since 4.3\r\n\t */\r\n\tpublic async setProfile(profile: boolean): Promise<void> {\r\n\t\tlet m = await import(\"./atn/ProfilingATNSimulator\");\r\n\t\tlet interp: ParserATNSimulator = this.interpreter;\r\n\t\tif (profile) {\r\n\t\t\tif (!(interp instanceof m.ProfilingATNSimulator)) {\r\n\t\t\t\tthis.interpreter = new m.ProfilingATNSimulator(this);\r\n\t\t\t}\r\n\t\t} else if (interp instanceof m.ProfilingATNSimulator) {\r\n\t\t\tthis.interpreter = new ParserATNSimulator(this.atn, this);\r\n\t\t}\r\n\r\n\t\tthis.interpreter.setPredictionMode(interp.getPredictionMode());\r\n\t}\r\n\r\n\t/** During a parse is sometimes useful to listen in on the rule entry and exit\r\n\t *  events as well as token matches. This is for quick and dirty debugging.\r\n\t */\r\n\tset isTrace(trace: boolean) {\r\n\t\tif (!trace) {\r\n\t\t\tif (this._tracer) {\r\n\t\t\t\tthis.removeParseListener(this._tracer);\r\n\t\t\t\tthis._tracer = undefined;\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tif (this._tracer) {\r\n\t\t\t\tthis.removeParseListener(this._tracer);\r\n\t\t\t} else {\r\n\t\t\t\tthis._tracer = new TraceListener(this.ruleNames, this._input);\r\n\t\t\t}\r\n\r\n\t\t\tthis.addParseListener(this._tracer);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Gets whether a {@link TraceListener} is registered as a parse listener\r\n\t * for the parser.\r\n\t */\r\n\tget isTrace(): boolean {\r\n\t\treturn this._tracer != null;\r\n\t}\r\n}\r\n"]}