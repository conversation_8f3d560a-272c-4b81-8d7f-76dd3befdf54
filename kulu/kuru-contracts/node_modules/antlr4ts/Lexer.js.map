{"version": 3, "file": "Lexer.js", "sourceRoot": "", "sources": ["../../src/Lexer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;AAMH,6DAA0D;AAC1D,sDAAmD;AACnD,8CAA2C;AAC3C,2CAAwC;AACxC,+DAA4D;AAC5D,2EAAwE;AACxE,6CAAwC;AAExC,6CAA0C;AAC1C,mCAAgC;AAIhC;;;;GAIG;AACH,MAAsB,KAAM,SAAQ,uBAAqC;IAiExE,YAAY,KAAiB;QAC5B,KAAK,EAAE,CAAC;QA7CT,kCAAkC;QACxB,aAAQ,GAAiB,uCAAkB,CAAC,OAAO,CAAC;QAY9D;;;WAGG;QACI,yBAAoB,GAAW,CAAC,CAAC,CAAC;QAEzC,iEAAiE;QAC1D,oBAAe,GAAW,CAAC,CAAC;QAEnC,gEAAgE;QACzD,kCAA6B,GAAW,CAAC,CAAC;QAEjD;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC,+CAA+C;QACxC,aAAQ,GAAW,CAAC,CAAC;QAE5B,2CAA2C;QACpC,UAAK,GAAW,CAAC,CAAC;QAET,eAAU,GAAiB,IAAI,2BAAY,EAAE,CAAC;QACvD,UAAK,GAAW,KAAK,CAAC,YAAY,CAAC;QASzC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IAChE,CAAC;IA/DD,MAAM,KAAK,qBAAqB;QAC/B,OAAO,aAAK,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,MAAM,KAAK,MAAM;QAChB,OAAO,aAAK,CAAC,cAAc,CAAC;IAC7B,CAAC;IA6DM,KAAK,CAAC,UAAoB;QAChC,6BAA6B;QAC7B,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;SACxC;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,aAAK,CAAC,YAAY,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,aAAK,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAEvB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEI,SAAS;QACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAC/D;QAED,+DAA+D;QAC/D,iDAAiD;QACjD,IAAI,gBAAgB,GAAW,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAClD,IAAI;YACH,KAAK,EACL,OAAO,IAAI,EAAE;gBACZ,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;iBACtB;gBAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBACxB,IAAI,CAAC,QAAQ,GAAG,aAAK,CAAC,eAAe,CAAC;gBACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC9C,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;gBACzE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC7C,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;gBACvB,GAAG;oBACF,IAAI,CAAC,KAAK,GAAG,aAAK,CAAC,YAAY,CAAC;oBACrC,qFAAqF;oBACrF,8BAA8B;oBAC9B,uCAAuC;oBAClC,IAAI,KAAa,CAAC;oBAClB,IAAI;wBACH,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;qBACxD;oBACD,OAAO,CAAC,EAAE;wBACT,IAAI,CAAC,YAAY,qDAAyB,EAAE;4BAC3C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAE,eAAe;4BACzC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;4BAChB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;yBACnB;6BAAM;4BACN,MAAM,CAAC,CAAC;yBACR;qBACD;oBACD,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;wBACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;qBACpB;oBACD,IAAI,IAAI,CAAC,KAAK,KAAK,aAAK,CAAC,YAAY,EAAE;wBACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;qBACnB;oBACD,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;wBAC9B,SAAS,KAAK,CAAC;qBACf;iBACD,QAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;gBACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;oBACxB,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;iBACnB;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC;aACnB;SACD;gBACO;YACP,6CAA6C;YAC7C,6CAA6C;YAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SACtC;IACF,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACV,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAEM,IAAI;QACV,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAEM,IAAI,CAAC,CAAS;QACpB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IAChB,CAAC;IAEM,QAAQ,CAAC,CAAS;QACxB,IAAI,qCAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;IAEM,OAAO;QACb,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACvC;QACD,IAAI,qCAAiB,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAGD,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED,YAAY;IACZ,IAAI,YAAY,CAAC,OAAqB;QACrC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAGD,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED,8CAA8C;IAC9C,IAAI,WAAW,CAAC,KAAiB;QAChC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;IACtE,CAAC;IAGD,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAC/B,CAAC;IAkBM,IAAI,CAAC,KAAa;QACxB,IAAI,CAAC,KAAK,EAAE;YACX,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC3B,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EACnE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,eAAe,EACnE,IAAI,CAAC,6BAA6B,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,OAAO;QACb,IAAI,IAAI,GAAW,IAAI,CAAC,kBAAkB,CAAC;QAC3C,IAAI,IAAI,GAAW,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,GAAG,GAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CACpC,IAAI,CAAC,uBAAuB,EAAE,aAAK,CAAC,GAAG,EAAE,SAAS,EAClD,aAAK,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAC/D,IAAI,EAAE,IAAI,CAAC,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,OAAO,GAAG,CAAC;IACZ,CAAC;IAGD,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IAAI,IAAI,CAAC,IAAY;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;IAC9B,CAAC;IAGD,IAAI,kBAAkB;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;IAC5C,CAAC;IAED,IAAI,kBAAkB,CAAC,kBAA0B;QAChD,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC1D,CAAC;IAED,+DAA+D;IAC/D,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACP,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAI,IAAI,CAAC,IAAY;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,4CAA4C;IAC5C,IAAI,KAAK,KAAwB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAEtD,IAAI,KAAK,CAAC,MAAyB;QAClC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,CAAC;IAED,IAAI,IAAI,CAAC,KAAa;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,CAAC,OAAe;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAMD;;OAEG;IACI,YAAY;QAClB,IAAI,MAAM,GAAY,EAAE,CAAC;QACzB,IAAI,CAAC,GAAU,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,OAAO,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;YAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;SACrB;QACD,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,eAAe,CAAC,CAA4B;QAClD,IAAI,IAAI,GAAW,IAAI,CAAC,MAAM,CAAC,OAAO,CACrC,mBAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5D,IAAI,GAAG,GAAW,+BAA+B;YAChD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;QAElC,IAAI,QAAQ,GAA+B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC3E,IAAI,QAAQ,CAAC,WAAW,EAAE;YACzB,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SACxG;IACF,CAAC;IAEM,eAAe,CAAC,CAAkB;QACxC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC1B,QAAQ,CAAC,EAAE;gBACX,KAAK,aAAK,CAAC,GAAG;oBACb,OAAO,OAAO,CAAC;gBAChB,KAAK,IAAI;oBACR,OAAO,KAAK,CAAC;gBACd,KAAK,IAAI;oBACR,OAAO,KAAK,CAAC;gBACd,KAAK,IAAI;oBACR,OAAO,KAAK,CAAC;aACb;YACD,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SAC9B;QACD,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aAC5B,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzB,CAAC;IAEM,mBAAmB,CAAC,CAAS;QACnC,IAAI,CAAC,GAAW,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACxC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACtB,CAAC;IASM,OAAO,CAAC,EAAwB;QACtC,IAAI,EAAE,YAAY,qDAAyB,EAAE;YAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,qBAAS,CAAC,GAAG,EAAE;gBACxC,4BAA4B;gBAC5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtC;SACD;aAAM;YACN,6EAA6E;YAC7E,uBAAuB;YACvB,2DAA2D;YAC3D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;SACtB;IACF,CAAC;;AA3YsB,kBAAY,GAAW,CAAC,CAAC;AACzB,UAAI,GAAW,CAAC,CAAC,CAAC;AAClB,UAAI,GAAW,CAAC,CAAC,CAAC;AAUlB,oBAAc,GAAW,MAAM,CAAC;AAChC,oBAAc,GAAW,QAAQ,CAAC;AAmFzD;IADC,qBAAQ;sCA6DR;AAwCD;IADC,qBAAQ;yCAGR;AAQD;IADC,qBAAQ;wCAGR;AAUD;IADC,qBAAQ;uCAGR;AAyCD;IADC,qBAAQ;iCAGR;AAOD;IADC,qBAAQ;+CAGR;AAlRF,sBA8YC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.7913318-07:00\r\n\r\nimport { ANTLRErrorListener } from \"./ANTLRErrorListener\";\r\nimport { CharStream } from \"./CharStream\";\r\nimport { CommonTokenFactory } from \"./CommonTokenFactory\";\r\nimport { IntegerStack } from \"./misc/IntegerStack\";\r\nimport { Interval } from \"./misc/Interval\";\r\nimport { IntStream } from \"./IntStream\";\r\nimport { LexerATNSimulator } from \"./atn/LexerATNSimulator\";\r\nimport { LexerNoViableAltException } from \"./LexerNoViableAltException\";\r\nimport { Override } from \"./Decorators\";\r\nimport { RecognitionException } from \"./RecognitionException\";\r\nimport { Recognizer } from \"./Recognizer\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenFactory } from \"./TokenFactory\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/** A lexer is recognizer that draws input symbols from a character stream.\r\n *  lexer grammars result in a subclass of this object. A Lexer object\r\n *  uses simplified match() and error recovery mechanisms in the interest\r\n *  of speed.\r\n */\r\nexport abstract class Lexer extends Recognizer<number, LexerATNSimulator>\r\n\timplements TokenSource {\r\n\tpublic static readonly DEFAULT_MODE: number = 0;\r\n\tpublic static readonly MORE: number = -2;\r\n\tpublic static readonly SKIP: number = -3;\r\n\r\n\tstatic get DEFAULT_TOKEN_CHANNEL(): number {\r\n\t\treturn Token.DEFAULT_CHANNEL;\r\n\t}\r\n\r\n\tstatic get HIDDEN(): number {\r\n\t\treturn Token.HIDDEN_CHANNEL;\r\n\t}\r\n\r\n\tpublic static readonly MIN_CHAR_VALUE: number = 0x0000;\r\n\tpublic static readonly MAX_CHAR_VALUE: number = 0x10FFFF;\r\n\r\n\tpublic _input: CharStream;\r\n\r\n\tprotected _tokenFactorySourcePair: { source: TokenSource, stream: CharStream };\r\n\r\n\t/** How to create token objects */\r\n\tprotected _factory: TokenFactory = CommonTokenFactory.DEFAULT;\r\n\r\n\t/** The goal of all lexer rules/methods is to create a token object.\r\n\t *  This is an instance variable as multiple rules may collaborate to\r\n\t *  create a single token.  nextToken will return this object after\r\n\t *  matching lexer rule(s).  If you subclass to allow multiple token\r\n\t *  emissions, then set this to the last token to be matched or\r\n\t *  something non-undefined so that the auto token emit mechanism will not\r\n\t *  emit another token.\r\n\t */\r\n\tpublic _token: Token | undefined;\r\n\r\n\t/** What character index in the stream did the current token start at?\r\n\t *  Needed, for example, to get the text for current token.  Set at\r\n\t *  the start of nextToken.\r\n\t */\r\n\tpublic _tokenStartCharIndex: number = -1;\r\n\r\n\t/** The line on which the first character of the token resides */\r\n\tpublic _tokenStartLine: number = 0;\r\n\r\n\t/** The character position of first character within the line */\r\n\tpublic _tokenStartCharPositionInLine: number = 0;\r\n\r\n\t/** Once we see EOF on char stream, next token will be EOF.\r\n\t *  If you have DONE : EOF ; then you see DONE EOF.\r\n\t */\r\n\tpublic _hitEOF: boolean = false;\r\n\r\n\t/** The channel number for the current token */\r\n\tpublic _channel: number = 0;\r\n\r\n\t/** The token type for the current token */\r\n\tpublic _type: number = 0;\r\n\r\n\tpublic readonly _modeStack: IntegerStack = new IntegerStack();\r\n\tpublic _mode: number = Lexer.DEFAULT_MODE;\r\n\r\n\t/** You can set the text for the current token to override what is in\r\n\t *  the input char buffer.  Set `text` or can set this instance var.\r\n\t */\r\n\tpublic _text: string | undefined;\r\n\r\n\tconstructor(input: CharStream) {\r\n\t\tsuper();\r\n\t\tthis._input = input;\r\n\t\tthis._tokenFactorySourcePair = { source: this, stream: input };\r\n\t}\r\n\r\n\tpublic reset(): void;\r\n\tpublic reset(resetInput: boolean): void;\r\n\tpublic reset(resetInput?: boolean): void {\r\n\t\t// wack Lexer state variables\r\n\t\tif (resetInput === undefined || resetInput) {\r\n\t\t\tthis._input.seek(0); // rewind the input\r\n\t\t}\r\n\r\n\t\tthis._token = undefined;\r\n\t\tthis._type = Token.INVALID_TYPE;\r\n\t\tthis._channel = Token.DEFAULT_CHANNEL;\r\n\t\tthis._tokenStartCharIndex = -1;\r\n\t\tthis._tokenStartCharPositionInLine = -1;\r\n\t\tthis._tokenStartLine = -1;\r\n\t\tthis._text = undefined;\r\n\r\n\t\tthis._hitEOF = false;\r\n\t\tthis._mode = Lexer.DEFAULT_MODE;\r\n\t\tthis._modeStack.clear();\r\n\r\n\t\tthis.interpreter.reset();\r\n\t}\r\n\r\n\t/** Return a token from this source; i.e., match a token on the char\r\n\t *  stream.\r\n\t */\r\n\t@Override\r\n\tpublic nextToken(): Token {\r\n\t\tif (this._input == null) {\r\n\t\t\tthrow new Error(\"nextToken requires a non-null input stream.\");\r\n\t\t}\r\n\r\n\t\t// Mark start location in char stream so unbuffered streams are\r\n\t\t// guaranteed at least have text of current token\r\n\t\tlet tokenStartMarker: number = this._input.mark();\r\n\t\ttry {\r\n\t\t\touter:\r\n\t\t\twhile (true) {\r\n\t\t\t\tif (this._hitEOF) {\r\n\t\t\t\t\treturn this.emitEOF();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._token = undefined;\r\n\t\t\t\tthis._channel = Token.DEFAULT_CHANNEL;\r\n\t\t\t\tthis._tokenStartCharIndex = this._input.index;\r\n\t\t\t\tthis._tokenStartCharPositionInLine = this.interpreter.charPositionInLine;\r\n\t\t\t\tthis._tokenStartLine = this.interpreter.line;\r\n\t\t\t\tthis._text = undefined;\r\n\t\t\t\tdo {\r\n\t\t\t\t\tthis._type = Token.INVALID_TYPE;\r\n//\t\t\t\tSystem.out.println(\"nextToken line \"+tokenStartLine+\" at \"+((char)input.LA(1))+\r\n//\t\t\t\t\t\t\t\t   \" in mode \"+mode+\r\n//\t\t\t\t\t\t\t\t   \" at index \"+input.index);\r\n\t\t\t\t\tlet ttype: number;\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tttype = this.interpreter.match(this._input, this._mode);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcatch (e) {\r\n\t\t\t\t\t\tif (e instanceof LexerNoViableAltException) {\r\n\t\t\t\t\t\t\tthis.notifyListeners(e);\t\t// report error\r\n\t\t\t\t\t\t\tthis.recover(e);\r\n\t\t\t\t\t\t\tttype = Lexer.SKIP;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow e;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this._input.LA(1) === IntStream.EOF) {\r\n\t\t\t\t\t\tthis._hitEOF = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this._type === Token.INVALID_TYPE) {\r\n\t\t\t\t\t\tthis._type = ttype;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this._type === Lexer.SKIP) {\r\n\t\t\t\t\t\tcontinue outer;\r\n\t\t\t\t\t}\r\n\t\t\t\t} while (this._type === Lexer.MORE);\r\n\t\t\t\tif (this._token == null) {\r\n\t\t\t\t\treturn this.emit();\r\n\t\t\t\t}\r\n\t\t\t\treturn this._token;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfinally {\r\n\t\t\t// make sure we release marker after match or\r\n\t\t\t// unbuffered char stream will keep buffering\r\n\t\t\tthis._input.release(tokenStartMarker);\r\n\t\t}\r\n\t}\r\n\r\n\t/** Instruct the lexer to skip creating a token for current lexer rule\r\n\t *  and look for another token.  nextToken() knows to keep looking when\r\n\t *  a lexer rule finishes with token set to SKIP_TOKEN.  Recall that\r\n\t *  if token==undefined at end of any token rule, it creates one for you\r\n\t *  and emits it.\r\n\t */\r\n\tpublic skip(): void {\r\n\t\tthis._type = Lexer.SKIP;\r\n\t}\r\n\r\n\tpublic more(): void {\r\n\t\tthis._type = Lexer.MORE;\r\n\t}\r\n\r\n\tpublic mode(m: number): void {\r\n\t\tthis._mode = m;\r\n\t}\r\n\r\n\tpublic pushMode(m: number): void {\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(\"pushMode \" + m);\r\n\t\t}\r\n\t\tthis._modeStack.push(this._mode);\r\n\t\tthis.mode(m);\r\n\t}\r\n\r\n\tpublic popMode(): number {\r\n\t\tif (this._modeStack.isEmpty) {\r\n\t\t\tthrow new Error(\"EmptyStackException\");\r\n\t\t}\r\n\t\tif (LexerATNSimulator.debug) {\r\n\t\t\tconsole.log(\"popMode back to \" + this._modeStack.peek());\r\n\t\t}\r\n\t\tthis.mode(this._modeStack.pop());\r\n\t\treturn this._mode;\r\n\t}\r\n\r\n\t@Override\r\n\tget tokenFactory(): TokenFactory {\r\n\t\treturn this._factory;\r\n\t}\r\n\r\n\t// @Override\r\n\tset tokenFactory(factory: TokenFactory) {\r\n\t\tthis._factory = factory;\r\n\t}\r\n\r\n\t@Override\r\n\tget inputStream(): CharStream {\r\n\t\treturn this._input;\r\n\t}\r\n\r\n\t/** Set the char stream and reset the lexer */\r\n\tset inputStream(input: CharStream) {\r\n\t\tthis.reset(false);\r\n\t\tthis._input = input;\r\n\t\tthis._tokenFactorySourcePair = { source: this, stream: this._input };\r\n\t}\r\n\r\n\t@Override\r\n\tget sourceName(): string {\r\n\t\treturn this._input.sourceName;\r\n\t}\r\n\r\n\r\n\t/** The standard method called to automatically emit a token at the\r\n\t *  outermost lexical rule.  The token object should point into the\r\n\t *  char buffer start..stop.  If there is a text override in 'text',\r\n\t *  use that to set the token's text.  Override this method to emit\r\n\t *  custom Token objects or provide a new factory.\r\n\t */\r\n\tpublic emit(token: Token): Token;\r\n\r\n\t/** By default does not support multiple emits per nextToken invocation\r\n\t *  for efficiency reasons.  Subclass and override this method, nextToken,\r\n\t *  and getToken (to push tokens into a list and pull from that list\r\n\t *  rather than a single variable as this implementation does).\r\n\t */\r\n\tpublic emit(): Token;\r\n\r\n\tpublic emit(token?: Token): Token {\r\n\t\tif (!token) {\r\n\t\t\ttoken = this._factory.create(\r\n\t\t\t\tthis._tokenFactorySourcePair, this._type, this._text, this._channel,\r\n\t\t\t\tthis._tokenStartCharIndex, this.charIndex - 1, this._tokenStartLine,\r\n\t\t\t\tthis._tokenStartCharPositionInLine);\r\n\t\t}\r\n\t\tthis._token = token;\r\n\t\treturn token;\r\n\t}\r\n\r\n\tpublic emitEOF(): Token {\r\n\t\tlet cpos: number = this.charPositionInLine;\r\n\t\tlet line: number = this.line;\r\n\t\tlet eof: Token = this._factory.create(\r\n\t\t\tthis._tokenFactorySourcePair, Token.EOF, undefined,\r\n\t\t\tToken.DEFAULT_CHANNEL, this._input.index, this._input.index - 1,\r\n\t\t\tline, cpos);\r\n\t\tthis.emit(eof);\r\n\t\treturn eof;\r\n\t}\r\n\r\n\t@Override\r\n\tget line(): number {\r\n\t\treturn this.interpreter.line;\r\n\t}\r\n\r\n\tset line(line: number) {\r\n\t\tthis.interpreter.line = line;\r\n\t}\r\n\r\n\t@Override\r\n\tget charPositionInLine(): number {\r\n\t\treturn this.interpreter.charPositionInLine;\r\n\t}\r\n\r\n\tset charPositionInLine(charPositionInLine: number) {\r\n\t\tthis.interpreter.charPositionInLine = charPositionInLine;\r\n\t}\r\n\r\n\t/** What is the index of the current character of lookahead? */\r\n\tget charIndex(): number {\r\n\t\treturn this._input.index;\r\n\t}\r\n\r\n\t/** Return the text matched so far for the current token or any\r\n\t *  text override.\r\n\t */\r\n\tget text(): string {\r\n\t\tif (this._text != null) {\r\n\t\t\treturn this._text;\r\n\t\t}\r\n\t\treturn this.interpreter.getText(this._input);\r\n\t}\r\n\r\n\t/** Set the complete text of this token; it wipes any previous\r\n\t *  changes to the text.\r\n\t */\r\n\tset text(text: string) {\r\n\t\tthis._text = text;\r\n\t}\r\n\r\n\t/** Override if emitting multiple tokens. */\r\n\tget token(): Token | undefined { return this._token; }\r\n\r\n\tset token(_token: Token | undefined) {\r\n\t\tthis._token = _token;\r\n\t}\r\n\r\n\tset type(ttype: number) {\r\n\t\tthis._type = ttype;\r\n\t}\r\n\r\n\tget type(): number {\r\n\t\treturn this._type;\r\n\t}\r\n\r\n\tset channel(channel: number) {\r\n\t\tthis._channel = channel;\r\n\t}\r\n\r\n\tget channel(): number {\r\n\t\treturn this._channel;\r\n\t}\r\n\r\n\tpublic abstract readonly channelNames: string[];\r\n\r\n\tpublic abstract readonly modeNames: string[];\r\n\r\n\t/** Return a list of all Token objects in input char stream.\r\n\t *  Forces load of all tokens. Does not include EOF token.\r\n\t */\r\n\tpublic getAllTokens(): Token[] {\r\n\t\tlet tokens: Token[] = [];\r\n\t\tlet t: Token = this.nextToken();\r\n\t\twhile (t.type !== Token.EOF) {\r\n\t\t\ttokens.push(t);\r\n\t\t\tt = this.nextToken();\r\n\t\t}\r\n\t\treturn tokens;\r\n\t}\r\n\r\n\tpublic notifyListeners(e: LexerNoViableAltException): void {\r\n\t\tlet text: string = this._input.getText(\r\n\t\t\tInterval.of(this._tokenStartCharIndex, this._input.index));\r\n\t\tlet msg: string = \"token recognition error at: '\" +\r\n\t\t\tthis.getErrorDisplay(text) + \"'\";\r\n\r\n\t\tlet listener: ANTLRErrorListener<number> = this.getErrorListenerDispatch();\r\n\t\tif (listener.syntaxError) {\r\n\t\t\tlistener.syntaxError(this, undefined, this._tokenStartLine, this._tokenStartCharPositionInLine, msg, e);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic getErrorDisplay(s: string | number): string {\r\n\t\tif (typeof s === \"number\") {\r\n\t\t\tswitch (s) {\r\n\t\t\tcase Token.EOF:\r\n\t\t\t\treturn \"<EOF>\";\r\n\t\t\tcase 0x0a:\r\n\t\t\t\treturn \"\\\\n\";\r\n\t\t\tcase 0x09:\r\n\t\t\t\treturn \"\\\\t\";\r\n\t\t\tcase 0x0d:\r\n\t\t\t\treturn \"\\\\r\";\r\n\t\t\t}\r\n\t\t\treturn String.fromCharCode(s);\r\n\t\t}\r\n\t\treturn s.replace(/\\n/g, \"\\\\n\")\r\n\t\t\t.replace(/\\t/g, \"\\\\t\")\r\n\t\t\t.replace(/\\r/g, \"\\\\r\");\r\n\t}\r\n\r\n\tpublic getCharErrorDisplay(c: number): string {\r\n\t\tlet s: string = this.getErrorDisplay(c);\r\n\t\treturn \"'\" + s + \"'\";\r\n\t}\r\n\r\n\t/** Lexers can normally match any char in it's vocabulary after matching\r\n\t *  a token, so do the easy thing and just kill a character and hope\r\n\t *  it all works out.  You can instead use the rule invocation stack\r\n\t *  to do sophisticated error recovery if you are in a fragment rule.\r\n\t */\r\n\tpublic recover(re: RecognitionException): void;\r\n\tpublic recover(re: LexerNoViableAltException): void;\r\n\tpublic recover(re: RecognitionException): void {\r\n\t\tif (re instanceof LexerNoViableAltException) {\r\n\t\t\tif (this._input.LA(1) !== IntStream.EOF) {\r\n\t\t\t\t// skip a char and try again\r\n\t\t\t\tthis.interpreter.consume(this._input);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t//System.out.println(\"consuming char \"+(char)input.LA(1)+\" during recovery\");\r\n\t\t\t//re.printStackTrace();\r\n\t\t\t// TODO: Do we lose character or line position information?\r\n\t\t\tthis._input.consume();\r\n\t\t}\r\n\t}\r\n}\r\n"]}