"use strict";
/*!
 * Copyright 2016 The ANTLR Project. All rights reserved.
 * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleVersion = void 0;
/**
 *
 * <AUTHOR>
 */
function RuleVersion(version) {
    return (target, propertyKey, propertyDescriptor) => {
        // intentionally empty
    };
}
exports.RuleVersion = RuleVersion;
//# sourceMappingURL=RuleVersion.js.map