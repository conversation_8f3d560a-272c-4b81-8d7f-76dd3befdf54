{"version": 3, "file": "TokenFactory.js", "sourceRoot": "", "sources": ["../../src/TokenFactory.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:57.8783640-07:00\r\n\r\nimport { CharStream } from \"./CharStream\";\r\nimport { Token } from \"./Token\";\r\nimport { TokenSource } from \"./TokenSource\";\r\n\r\n/** The default mechanism for creating tokens. It's used by default in Lexer and\r\n *  the error handling strategy (to create missing tokens).  Notifying the parser\r\n *  of a new factory means that it notifies its token source and error strategy.\r\n */\r\nexport interface TokenFactory {\r\n\t/** This is the method used to create tokens in the lexer and in the\r\n\t *  error handling strategy. If text!=undefined, than the start and stop positions\r\n\t *  are wiped to -1 in the text override is set in the CommonToken.\r\n\t */\r\n\t//@NotNull\r\n\tcreate(\r\n\t\t/*@NotNull*/\r\n\t\tsource: { source?: TokenSource, stream?: CharStream },\r\n\t\ttype: number,\r\n\t\ttext: string | undefined,\r\n\t\tchannel: number,\r\n\t\tstart: number,\r\n\t\tstop: number,\r\n\t\tline: number,\r\n\t\tcharPositionInLine: number): Token;\r\n\r\n\t/** Generically useful */\r\n\t//@NotNull\r\n\tcreateSimple(type: number, text: string): Token;\r\n}\r\n"]}