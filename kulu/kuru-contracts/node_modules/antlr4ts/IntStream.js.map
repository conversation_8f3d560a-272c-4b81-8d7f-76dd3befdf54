{"version": 3, "file": "IntStream.js", "sourceRoot": "", "sources": ["../../src/IntStream.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAwD;AAExD,IAAiB,SAAS,CAYzB;AAZD,WAAiB,SAAS;IACzB;;;OAGG;IACU,aAAG,GAAW,CAAC,CAAC,CAAC;IAE9B;;;OAGG;IACU,6BAAmB,GAAW,WAAW,CAAC;AACxD,CAAC,EAZgB,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAYzB", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\n// ConvertTo-TS run at 2016-10-04T11:26:51.6934376-07:00\r\n\r\nexport namespace IntStream {\r\n\t/**\r\n\t * The value returned by {@link #LA LA()} when the end of the stream is\r\n\t * reached.\r\n\t */\r\n\texport const EOF: number = -1;\r\n\r\n\t/**\r\n\t * The value returned by {@link #getSourceName} when the actual name of the\r\n\t * underlying source is not known.\r\n\t */\r\n\texport const UNKNOWN_SOURCE_NAME: string = \"<unknown>\";\r\n}\r\n\r\n/**\r\n * A simple stream of symbols whose values are represented as integers. This\r\n * interface provides *marked ranges* with support for a minimum level\r\n * of buffering necessary to implement arbitrary lookahead during prediction.\r\n * For more information on marked ranges, see {@link #mark}.\r\n *\r\n * **Initializing Methods:** Some methods in this interface have\r\n * unspecified behavior if no call to an initializing method has occurred after\r\n * the stream was constructed. The following is a list of initializing methods:\r\n *\r\n * * {@link #LA}\r\n * * {@link #consume}\r\n * * {@link #size}\r\n */\r\nexport interface IntStream {\r\n\t/**\r\n\t * Consumes the current symbol in the stream. This method has the following\r\n\t * effects:\r\n\t *\r\n\t * * **Forward movement:** The value of `index`\r\n\t *   before calling this method is less than the value of `index`\r\n\t *   after calling this method.\r\n\t * * **Ordered lookahead:** The value of `LA(1)` before\r\n\t *   calling this method becomes the value of `LA(-1)` after calling\r\n\t *   this method.\r\n\t *\r\n\t * Note that calling this method does not guarantee that `index` is\r\n\t * incremented by exactly 1, as that would preclude the ability to implement\r\n\t * filtering streams (e.g. {@link CommonTokenStream} which distinguishes\r\n\t * between \"on-channel\" and \"off-channel\" tokens).\r\n\t *\r\n\t * @throws IllegalStateException if an attempt is made to consume the\r\n\t * end of the stream (i.e. if `LA(1)==`{@link #EOF EOF} before calling\r\n\t * `consume`).\r\n\t */\r\n\tconsume(): void;\r\n\r\n\t/**\r\n\t * Gets the value of the symbol at offset `i` from the current\r\n\t * position. When `i==1`, this method returns the value of the current\r\n\t * symbol in the stream (which is the next symbol to be consumed). When\r\n\t * `i==-1`, this method returns the value of the previously read\r\n\t * symbol in the stream. It is not valid to call this method with\r\n\t * `i==0`, but the specific behavior is unspecified because this\r\n\t * method is frequently called from performance-critical code.\r\n\t *\r\n\t * This method is guaranteed to succeed if any of the following are true:\r\n\t *\r\n\t * * `i>0`\r\n\t * * `i==-1` and `index` returns a value greater\r\n\t *   than the value of `index` after the stream was constructed\r\n\t *   and `LA(1)` was called in that order. Specifying the current\r\n\t *   `index` relative to the index after the stream was created\r\n\t *   allows for filtering implementations that do not return every symbol\r\n\t *   from the underlying source. Specifying the call to `LA(1)`\r\n\t *   allows for lazily initialized streams.\r\n\t * * `LA(i)` refers to a symbol consumed within a marked region\r\n\t *   that has not yet been released.\r\n\t *\r\n\t * If `i` represents a position at or beyond the end of the stream,\r\n\t * this method returns {@link #EOF}.\r\n\t *\r\n\t * The return value is unspecified if `i<0` and fewer than `-i`\r\n\t * calls to {@link #consume consume()} have occurred from the beginning of\r\n\t * the stream before calling this method.\r\n\t *\r\n\t * @throws UnsupportedOperationException if the stream does not support\r\n\t * retrieving the value of the specified symbol\r\n\t */\r\n\tLA(i: number): number;\r\n\r\n\t/**\r\n\t * A mark provides a guarantee that {@link #seek seek()} operations will be\r\n\t * valid over a \"marked range\" extending from the index where `mark()`\r\n\t * was called to the current `index`. This allows the use of\r\n\t * streaming input sources by specifying the minimum buffering requirements\r\n\t * to support arbitrary lookahead during prediction.\r\n\t *\r\n\t * The returned mark is an opaque handle (type `int`) which is passed\r\n\t * to {@link #release release()} when the guarantees provided by the marked\r\n\t * range are no longer necessary. When calls to\r\n\t * `mark()`/`release()` are nested, the marks must be released\r\n\t * in reverse order of which they were obtained. Since marked regions are\r\n\t * used during performance-critical sections of prediction, the specific\r\n\t * behavior of invalid usage is unspecified (i.e. a mark is not released, or\r\n\t * a mark is released twice, or marks are not released in reverse order from\r\n\t * which they were created).\r\n\t *\r\n\t * The behavior of this method is unspecified if no call to an\r\n\t * {@link IntStream initializing method} has occurred after this stream was\r\n\t * constructed.\r\n\t *\r\n\t * This method does not change the current position in the input stream.\r\n\t *\r\n\t * The following example shows the use of {@link #mark mark()},\r\n\t * {@link #release release(mark)}, `index`, and\r\n\t * {@link #seek seek(index)} as part of an operation to safely work within a\r\n\t * marked region, then restore the stream position to its original value and\r\n\t * release the mark.\r\n\t *\r\n\t * ```\r\n\t * IntStream stream = ...;\r\n\t * int index = -1;\r\n\t * int mark = stream.mark();\r\n\t * try {\r\n\t *   index = stream.index;\r\n\t *   // perform work here...\r\n\t * } finally {\r\n\t *   if (index != -1) {\r\n\t *     stream.seek(index);\r\n\t *   }\r\n\t *   stream.release(mark);\r\n\t * }\r\n\t * ```\r\n\t *\r\n\t * @returns An opaque marker which should be passed to\r\n\t * {@link #release release()} when the marked range is no longer required.\r\n\t */\r\n\tmark(): number;\r\n\r\n\t/**\r\n\t * This method releases a marked range created by a call to\r\n\t * {@link #mark mark()}. Calls to `release()` must appear in the\r\n\t * reverse order of the corresponding calls to `mark()`. If a mark is\r\n\t * released twice, or if marks are not released in reverse order of the\r\n\t * corresponding calls to `mark()`, the behavior is unspecified.\r\n\t *\r\n\t * For more information and an example, see {@link #mark}.\r\n\t *\r\n\t * @param marker A marker returned by a call to `mark()`.\r\n\t * @see #mark\r\n\t */\r\n\trelease(marker: number): void;\r\n\r\n\t/**\r\n\t * Return the index into the stream of the input symbol referred to by\r\n\t * `LA(1)`.\r\n\t *\r\n\t * The behavior of this method is unspecified if no call to an\r\n\t * {@link IntStream initializing method} has occurred after this stream was\r\n\t * constructed.\r\n\t */\r\n\treadonly index: number;\r\n\r\n\t/**\r\n\t * Set the input cursor to the position indicated by `index`. If the\r\n\t * specified index lies past the end of the stream, the operation behaves as\r\n\t * though `index` was the index of the EOF symbol. After this method\r\n\t * returns without throwing an exception, then at least one of the following\r\n\t * will be true.\r\n\t *\r\n\t * * `index` will return the index of the first symbol\r\n\t *   appearing at or after the specified `index`. Specifically,\r\n\t *   implementations which filter their sources should automatically\r\n\t *   adjust `index` forward the minimum amount required for the\r\n\t *   operation to target a non-ignored symbol.\r\n\t * * `LA(1)` returns {@link #EOF}\r\n\t *\r\n\t * This operation is guaranteed to not throw an exception if `index`\r\n\t * lies within a marked region. For more information on marked regions, see\r\n\t * {@link #mark}. The behavior of this method is unspecified if no call to\r\n\t * an {@link IntStream initializing method} has occurred after this stream\r\n\t * was constructed.\r\n\t *\r\n\t * @param index The absolute index to seek to.\r\n\t *\r\n\t * @throws IllegalArgumentException if `index` is less than 0\r\n\t * @throws UnsupportedOperationException if the stream does not support\r\n\t * seeking to the specified index\r\n\t */\r\n\tseek(index: number): void;\r\n\r\n\t/**\r\n\t * Returns the total number of symbols in the stream, including a single EOF\r\n\t * symbol.\r\n\t *\r\n\t * @throws UnsupportedOperationException if the size of the stream is\r\n\t * unknown.\r\n\t */\r\n\treadonly size: number;\r\n\r\n\t/**\r\n\t * Gets the name of the underlying symbol source. This method returns a\r\n\t * non-undefined, non-empty string. If such a name is not known, this method\r\n\t * returns {@link #UNKNOWN_SOURCE_NAME}.\r\n\t */\r\n\t//@NotNull\r\n\treadonly sourceName: string;\r\n}\r\n"]}