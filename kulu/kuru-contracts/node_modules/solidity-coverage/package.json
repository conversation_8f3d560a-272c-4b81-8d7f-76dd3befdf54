{"name": "solidity-coverage", "version": "0.8.2", "description": "", "main": "plugins/nomiclabs.plugin.js", "bin": {"solidity-coverage": "./plugins/bin.js"}, "directories": {"test": "test"}, "scripts": {"nyc": "SILENT=true  nyc --exclude '**/sc_temp/**' --exclude '**/test/**'", "test": "SILENT=true node --max-old-space-size=4096 ./node_modules/.bin/nyc --exclude '**/sc_temp/**' --exclude '**/test/**/' -- mocha test/units/* --timeout 100000 --no-warnings --exit", "test:ci": "SILENT=true node --max-old-space-size=4096 ./node_modules/.bin/nyc --reporter=lcov --exclude '**/sc_temp/**' --exclude '**/test/**/' --exclude 'plugins/resources/matrix.js' -- mocha test/units/* --timeout 100000 --no-warnings --exit", "test:debug": "node --max-old-space-size=4096 ./node_modules/.bin/mocha test/units/* --timeout 100000 --no-warnings --exit", "netlify": "./scripts/run-netlify.sh"}, "homepage": "https://github.com/sc-forks/solidity-coverage", "repository": {"type": "git", "url": "https://github.com/sc-forks/solidity-coverage.git"}, "author": "", "license": "ISC", "dependencies": {"@ethersproject/abi": "^5.0.9", "@solidity-parser/parser": "^0.14.1", "chalk": "^2.4.2", "death": "^1.1.0", "detect-port": "^1.3.0", "difflib": "^0.2.4", "fs-extra": "^8.1.0", "ghost-testrpc": "^0.0.2", "global-modules": "^2.0.0", "globby": "^10.0.1", "jsonschema": "^1.2.4", "lodash": "^4.17.15", "mocha": "7.1.2", "node-emoji": "^1.10.0", "pify": "^4.0.1", "recursive-readdir": "^2.2.2", "sc-istanbul": "^0.4.5", "semver": "^7.3.4", "shelljs": "^0.8.3", "web3-utils": "^1.3.6"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.4", "@nomiclabs/hardhat-truffle5": "^2.0.0", "@nomiclabs/hardhat-waffle": "^2.0.1", "@nomiclabs/hardhat-web3": "^2.0.0", "@truffle/contract": "4.0.36", "chai": "^4.3.4", "decache": "^4.5.1", "ethereum-waffle": "^3.4.0", "ethers": "^5.5.3", "ganache-cli": "6.12.2", "hardhat": "^2.11.0", "hardhat-gas-reporter": "^1.0.1", "nyc": "^14.1.1", "solc": "^0.7.5"}, "peerDependencies": {"hardhat": "^2.11.0"}}