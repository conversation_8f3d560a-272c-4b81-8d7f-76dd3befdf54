{"name": "yargs-unparser", "description": "Converts back a yargs argv object to its original array form", "version": "1.6.0", "keywords": ["yargs", "unparse", "expand", "inverse", "argv"], "author": "<PERSON> <<EMAIL>>", "engines": {"node": ">=6"}, "homepage": "https://github.com/yargs/yargs-unparser", "repository": {"type": "git", "url": "**************:yargs/yargs-unparser.git"}, "license": "MIT", "main": "index.js", "files": [], "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "prerelease": "npm t && npm run lint", "release": "standard-version", "precommit": "lint-staged", "commitmsg": "commitlint -e $GIT_PARAMS"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "eslint": "^6.1.0", "eslint-config-moxy": "^7.1.0", "husky": "^3.0.1", "jest": "^24.8.0", "lint-staged": "^9.2.1", "minimist": "^1.2.0", "standard-version": "^7.0.0", "yargs-parser": "^13.1.1"}, "dependencies": {"flat": "^4.1.0", "lodash": "^4.17.15", "yargs": "^13.3.0"}}