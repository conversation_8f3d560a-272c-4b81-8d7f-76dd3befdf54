{"name": "readdirp", "description": "Recursive version of fs.readdir with streaming api.", "version": "3.2.0", "homepage": "https://github.com/paulmillr/readdirp", "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "author": "<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "<PERSON> (https://paulmillr.com)"], "engines": {"node": ">= 8"}, "files": ["index.js", "index.d.ts"], "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "scripts": {"test": "nyc mocha && dtslint"}, "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"@types/chai": "^4.1", "@types/mocha": "^5.2", "@types/node": "^12", "chai": "^4.2", "chai-subset": "^1.6", "dtslint": "^0.9.8", "mocha": "~6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3"}, "license": "MIT"}