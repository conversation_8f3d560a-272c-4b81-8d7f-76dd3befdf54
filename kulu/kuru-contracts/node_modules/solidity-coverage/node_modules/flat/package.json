{"name": "flat", "version": "4.1.1", "main": "index.js", "bin": "cli.js", "scripts": {"test": "mocha -u tdd --reporter spec && standard index.js test/index.js"}, "license": "BSD-3-<PERSON><PERSON>", "description": "Take a nested Javascript object and flatten it, or unflatten an object with delimited keys", "devDependencies": {"mocha": "~5.2.0", "standard": "^11.0.1"}, "directories": {"test": "test"}, "dependencies": {"is-buffer": "~2.0.3"}, "repository": {"type": "git", "url": "git://github.com/hughsk/flat.git"}, "keywords": ["flat", "json", "flatten", "unflatten", "split", "object", "nested"], "author": "<PERSON> <<EMAIL>> (http://hughskennedy.com)", "bugs": {"url": "https://github.com/hughsk/flat/issues"}, "homepage": "https://github.com/hughsk/flat"}