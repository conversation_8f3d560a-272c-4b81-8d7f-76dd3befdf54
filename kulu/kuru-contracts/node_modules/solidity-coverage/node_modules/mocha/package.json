{"name": "mocha", "version": "7.1.2", "description": "simple, flexible, fun test framework", "keywords": ["mocha", "test", "bdd", "tdd", "tap"], "funding": {"type": "opencollective", "url": "https://opencollective.com/mochajs"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mochajs/mocha.git"}, "bugs": {"url": "https://github.com/mochajs/mocha/issues/"}, "homepage": "https://mochajs.org/", "logo": "https://cldup.com/S9uQ-cOLYz.svg", "notifyLogo": "https://ibin.co/4QuRuGjXvl36.png", "bin": {"mocha": "./bin/mocha", "_mocha": "./bin/_mocha"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": ">= 8.10.0"}, "scripts": {"prepublishOnly": "nps test clean build", "start": "nps", "test": "nps test", "version": "nps version"}, "dependencies": {"ansi-colors": "3.2.3", "browser-stdout": "1.3.1", "chokidar": "3.3.0", "debug": "3.2.6", "diff": "3.5.0", "escape-string-regexp": "1.0.5", "find-up": "3.0.0", "glob": "7.1.3", "growl": "1.10.5", "he": "1.2.0", "js-yaml": "3.13.1", "log-symbols": "3.0.0", "minimatch": "3.0.4", "mkdirp": "0.5.5", "ms": "2.1.1", "node-environment-flags": "1.0.6", "object.assign": "4.1.0", "strip-json-comments": "2.0.1", "supports-color": "6.0.0", "which": "1.3.1", "wide-align": "1.1.3", "yargs": "13.3.2", "yargs-parser": "13.1.2", "yargs-unparser": "1.6.0"}, "devDependencies": {"@11ty/eleventy": "^0.8.3", "@mocha/docdash": "^2.1.2", "acorn": "^7.0.0", "assetgraph-builder": "^7.0.0", "autoprefixer": "^9.6.0", "babel-eslint": "^10.0.3", "browserify": "^16.2.3", "browserify-package-json": "^1.0.1", "chai": "^4.2.0", "coffee-script": "^1.12.7", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "cross-spawn": "^6.0.5", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-config-semistandard": "^15.0.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "fs-extra": "^8.0.1", "husky": "^1.3.1", "hyperlink": "^4.3.1", "jsdoc": "^3.6.3", "karma": "^4.1.0", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^2.0.2", "lint-staged": "^8.1.7", "markdown-it": "^8.4.2", "markdown-it-anchor": "^5.2.4", "markdown-it-attrs": "^2.4.1", "markdown-it-prism": "^2.0.2", "markdown-magic": "^0.1.25", "markdown-magic-package-json": "^2.0.0", "markdown-toc": "^1.2.0", "markdownlint-cli": "^0.14.1", "nps": "^5.9.12", "nyc": "^14.1.1", "prettier": "^1.17.1", "remark": "^10.0.1", "remark-github": "^7.0.6", "remark-inline-links": "^3.1.2", "rewiremock": "^3.13.7", "rimraf": "^2.6.3", "sinon": "^7.3.2", "strip-ansi": "^5.2.0", "svgo": "^1.2.2", "through2": "^3.0.1", "to-vfile": "^5.0.3", "unexpected": "^10.40.2", "unexpected-eventemitter": "^1.1.3", "unexpected-sinon": "^10.11.2", "uslug": "^1.0.4", "watchify": "^3.11.1"}, "files": ["bin/*mocha", "assets/growl/*.png", "lib/**/*.{js,html,json}", "index.js", "mocha.css", "mocha.js", "browser-entry.js"], "browserify": {"transform": ["./scripts/package-json-cullify"]}, "browser": {"./index.js": "./browser-entry.js", "./lib/growl.js": "./lib/browser/growl.js", "tty": "./lib/browser/tty.js", "./lib/cli/*.js": false, "chokidar": false, "fs": false, "glob": false, "path": false, "supports-color": false}, "prettier": {"singleQuote": true, "bracketSpacing": false, "endOfLine": "auto"}, "gitter": "https://gitter.im/mochajs/mocha", "husky": {"hooks": {"pre-commit": "lint-staged"}}}