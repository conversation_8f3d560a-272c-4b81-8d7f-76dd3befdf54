{"name": "request-promise-native", "version": "1.0.9", "description": "The simplified HTTP request client 'request' with Promise support. Powered by native ES6 promises.", "keywords": ["xhr", "http", "https", "promise", "request", "then", "thenable", "native"], "main": "./lib/rp.js", "scripts": {"test": "./node_modules/.bin/gulp ci", "test-publish": "./node_modules/.bin/gulp ci-no-cov", "publish-please": "publish-please", "prepublish": "publish-please guard"}, "repository": {"type": "git", "url": "git+https://github.com/request/request-promise-native.git"}, "author": "<PERSON><PERSON> (https://github.com/analog-nico)", "license": "ISC", "bugs": {"url": "https://github.com/request/request-promise-native/issues"}, "homepage": "https://github.com/request/request-promise-native#readme", "engines": {"node": ">=0.12.0"}, "dependencies": {"request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "peerDependencies": {"request": "^2.34"}, "devDependencies": {"body-parser": "~1.15.2", "chai": "~3.5.0", "chalk": "~1.1.3", "gulp": "~3.9.1", "gulp-coveralls": "~0.1.4", "gulp-eslint": "~2.1.0", "gulp-istanbul": "~1.0.0", "gulp-mocha": "~2.2.0", "lodash": "~4.13.1", "publish-please": "~2.1.4", "request": "^2.34.0", "rimraf": "~2.5.3", "run-sequence": "~1.2.2"}}