[![npm version](https://badge.fury.io/js/hardhat-gas-reporter.svg)](https://badge.fury.io/js/hardhat-gas-reporter)
[![Build Status](https://travis-ci.org/cgewecke/hardhat-gas-reporter.svg?branch=master)](https://travis-ci.org/cgewecke/hardhat-gas-reporter)
[![Codechecks](https://raw.githubusercontent.com/codechecks/docs/master/images/badges/badge-default.svg?sanitize=true)](https://codechecks.io)
[![buidler](https://hardhat.org/buidler-plugin-badge.svg?1)](https://github.com/cgewecke/hardhat-gas-reporter)


# hardhat-gas-reporter

[eth-gas-reporter](https://github.com/cgewecke/eth-gas-reporter) plugin for [hardhat](http://gethardhat.com).

## What

**A Mocha reporter for Ethereum test suites:**

- Gas usage per unit test.
- Metrics for method calls and deployments.
- National currency costs of deploying and using your contract system.

### Example report

![Screen Shot 2019-06-23 at 2 10 19 PM](https://user-images.githubusercontent.com/7332026/59982003-c30a4380-95c0-11e9-9d93-e3af979df227.png)

## Installation

```bash
npm install hardhat-gas-reporter --save-dev
```

And add the following to your `hardhat.config.js`:
```js
require("hardhat-gas-reporter");
```

Or, if you are using TypeScript, add this to your hardhat.config.ts:
```ts
import "hardhat-gas-reporter"
```

**Looking for buidler-gas-reporter docs?** [They moved here...][1]

## Configuration
Configuration is optional.
```js
module.exports = {
  gasReporter: {
    currency: 'CHF',
    gasPrice: 21
  }
}
```
:bulb: **Pro Tips**

**Turning the plugin on/off**

The options include an `enabled` key that lets you toggle gas reporting on and off using shell
environment variables. When `enabled` is false, mocha's (faster) default spec reporter is used.
Example:

```js
module.exports = {
  gasReporter: {
    enabled: (process.env.REPORT_GAS) ? true : false
  }
}
```

**Migrating from Truffle**

If you already have `eth-gas-reporter` installed in your project, make sure you uninstall it before adding this plugin.
`hardhat-gas-reporter` manages `eth-gas-reporter` as a dependency and having multiple versions in your lockfile can stop the reporter
from working correctly.

## Usage

This plugin overrides the built-in `test` task. Gas reports are generated by default with:
```
npx hardhat test
```

### Options

:warning: **CoinMarketCap API change** :warning:

Beginning March 2020, CoinMarketCap requires an API key to access currency market
price data. The reporter uses an unprotected free tier key by default (10k reqs/mo). You can get
your own API key [here][55] and set it with the `coinmarketcap` option.

In order to retrieve the gas price of a particular blockchain, you can configure the `token` and `gasPriceApi` (API key rate limit may apply).

**NOTE**: HardhatEVM and ganache-cli implement the Ethereum blockchain. To get accurate gas measurements for other chains you may need to run your tests against development clients developed specifically for those networks.

| Option            | Type                   | Default                                                                    | Description                                                                                                                                                                                                                                  |
| ----------------- | ---------------------- | -------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| enabled          | _Boolean_               | true                    | Always generate gas reports when running the hardhat test command.                                                                                                                                                           |
| currency          | _String_               | 'EUR'                                                                      | National currency to represent gas costs in. Exchange rates loaded at runtime from the `coinmarketcap` api. Available currency codes can be found [here](https://coinmarketcap.com/api/documentation/v1/#section/Standards-and-Conventions). |
| coinmarketcap     | _String_               | (unprotected API key)                                                      | [API key][55] to use when fetching current market price data. (Use this if you stop seeing price data)                                                                                                                                       |
| gasPrice          | _Number_               | (varies)                                                                   | Denominated in `gwei`. Default is loaded at runtime from the `eth gas station` api                                                                                                                                                           |
| token             | _String_               | 'ETH'                                                                      | The reference token for gas price                                                                                                                                                                                                            |
| gasPriceApi       | _String_               | [Etherscan](https://api.etherscan.io/api?module=proxy&action=eth_gasPrice) | The API endpoint to retrieve the gas price. Find below other networks.                                                                                                                                                                       |
| outputFile        | _String_               | stdout                                                                     | File path to write report output to                                                                                                                                                                                                          |
| noColors          | _Boolean_              | false                                                                      | Suppress report color. Useful if you are printing to file b/c terminal colorization corrupts the text.                                                                                                                                       |
| onlyCalledMethods | _Boolean_              | true                                                                       | Omit methods that are never called from report.                                                                                                                                                                                              |
| rst               | _Boolean_              | false                                                                      | Output with a reStructured text code-block directive. Useful if you want to include report in RTD                                                                                                                                            |
| rstTitle          | _String_               | ""                                                                         | Title for reStructured text header (See Travis for example output)                                                                                                                                                                           |
| showTimeSpent     | _Boolean_              | false                                                                      | Show the amount of time spent as well as the gas consumed                                                                                                                                                                                    |
| excludeContracts  | _String[]_             | []                          | Contracts (or folders) to exclude from report. Ex: `['Migrations.sol', 'Wallets/']`. (See [v1.0.3 release notes][45] for additional usage help)                                                               |
| src               | _String_               | "contracts"                                                                | Folder in root directory to begin search for `.sol` files. This can also be a path to a subfolder relative to the root, e.g. "planets/annares/contracts"                                                                                     |
| url               | _String_               | `web3.currentProvider.host`                                                | RPC client url (ex: "http://localhost:8545")                                                                                                                                                                                                 |
| proxyResolver     | _Function_             | none                                                                       | Custom method to resolve identity of methods managed by a proxy contract.                                                                                                                                                                    |
| artifactType      | _Function_ or _String_ | "truffle-v5"                                                               | Compilation artifact format to consume. (See [advanced use](https://github.com/cgewecke/eth-gas-reporter/blob/master/docs/advanced.md).)                                                                                                     |
| showMethodSig     | _Boolean_              | false                                                                      | Display complete method signatures. Useful when you have overloaded methods you can't tell apart.                                                                                                                                            |
| maxMethodDiff     | _Number_               | undefined                                                                  | Codechecks failure threshold, triggered when the % diff for any method is greater than `number` (integer)                                                                                                                                    |
| maxDeploymentDiff | _Number_               | undefined                                                                  | Codechecks failure threshold, triggered when the % diff for any deployment is greater than `number` (integer)                                                                                                                                |
| remoteContracts | _RemoteContract[]_               | `[]`                  | Contracts pre-deployed to a (forked) network which the reporter should collect gas usage data for. (See [RemoteContract type][44] and [usage example][47])                                |

[44]: https://github.com/cgewecke/hardhat-gas-reporter/blob/master/src/types.ts#L27
[45]: https://github.com/cgewecke/hardhat-gas-reporter/releases/tag/v1.0.3
[47]: https://github.com/cgewecke/hardhat-gas-reporter/issues/46#issuecomment-728639165
[55]: https://coinmarketcap.com/api/pricing/

#### `token` and `gasPriceApi` options example

| Network            | token | gasPriceApi                                                            |
| ------------------ | ----- | ---------------------------------------------------------------------- |
| Ethereum (default) | ETH   | https://api.etherscan.io/api?module=proxy&action=eth_gasPrice          |
| Binance            | BNB   | https://api.bscscan.com/api?module=proxy&action=eth_gasPrice           |
| Polygon            | MATIC | https://api.polygonscan.com/api?module=proxy&action=eth_gasPrice       |
| Avalanche          | AVAX  | https://api.snowtrace.io/api?module=proxy&action=eth_gasPrice          |
| Heco               | HT    | https://api.hecoinfo.com/api?module=proxy&action=eth_gasPrice          |
| Moonriver          | MOVR  | https://api-moonriver.moonscan.io/api?module=proxy&action=eth_gasPrice |

These APIs have [rate limits](https://docs.etherscan.io/support/rate-limits). Depending on the usage, it might require an [API Key](https://docs.etherscan.io/getting-started/viewing-api-usage-statistics).

> NB: Any gas price API call which returns a JSON-RPC response formatted like this is supported: `{"jsonrpc":"2.0","id":73,"result":"0x6fc23ac00"}`.


## Documentation

Other useful documentation can be found at [eth-gas-reporter](https://github.com/cgewecke/eth-gas-reporter)

[1]: https://github.com/cgewecke/buidler-gas-reporter/tree/buidler-final#installation

## Parallelization

This plugin also adds a Hardhat Task for merging several `gasReporterOutput.json` files, which are generated by [eth-gas-reporter](https://github.com/cgewecke/eth-gas-reporter) when [running your tests with in parallelized jobs in CI](https://github.com/cgewecke/eth-gas-reporter/blob/master/docs/gasReporterOutput.md).

To use the task you just have to give it the filepaths or a glob pattern pointing to all of the reports:
```bash
npx hardhat gas-reporter:merge 'gasReporterOutput-*.json'
```

Here is an example `config.yml` file used by CircleCI to run the tests on parallel first, and then merge the reports

```yaml
version: 2.1

jobs:
  test:
    docker:
      - image: circleci/node:14.15.1-stretch
    parallelism: 8
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Run tests
          command: |
            circleci tests glob 'test/**/*.spec.ts' |
            circleci tests split |
            xargs npx hardhat test
      - run:
          name: Save gas report
          command: |
            mv gasReporterOutput.json ./gasReporterOutput-$CIRCLE_NODE_INDEX.json
      - persist_to_workspace:
          root: .
          paths:
            - gasReporterOutput-*.json
  test-gas-report:
    docker:
      - image: circleci/node:14.15.1-stretch
    steps:
      - checkout
      - attach_workspace:
          at: .
      - run:
          name: Upload gas reports
          command: |
            npx hardhat gas-reporter:merge gasReporterOutput-*.json
            npx codechecks codechecks.unit.yml
      - store_artifacts:
          path: gasReporterOutput.json

workflows:
  workflow-all:
    jobs:
      - test
      - test-gas-report:
          requires:
            - test
```
