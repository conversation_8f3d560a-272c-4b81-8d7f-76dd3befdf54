{"version": 3, "file": "providers.js", "sourceRoot": "", "sources": ["../../src/providers.ts"], "names": [], "mappings": ";;;AAAA,qEAAyE;AAGzE;;GAEG;AACH,MAAa,yBAA0B,SAAQ,yBAAe;IAG5D,YAAY,QAAyB,EAAE,WAAW;QAChD,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;IAChC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,UAAU;QACV,IAAI,IAAI,CAAC,MAAM,KAAK,2BAA2B,EAAE;YAC/C,MAAM,OAAO,GAAQ,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,MAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,CAAA,EAAC;gBAC9C,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBAC7C,MAAM,EAAE,0BAA0B;oBAClC,MAAM,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;iBAClC,CAAC,CAAC;gBACH,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aACnE;YACD,OAAO,OAAO,CAAC;YAEjB,uFAAuF;SACtF;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,0BAA0B,EAAC;YACpD,MAAM,OAAO,GAAQ,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACvD,MAAM,EAAE,2BAA2B;gBACnC,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAA;YACF,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAC;gBAClB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;aAClE;YACD,OAAO,EAAE,CAAC;YAEZ,uFAAuF;YACvF,0EAA0E;SACzE;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,EAAE;YACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAC;gBAC7B,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBAC7C,MAAM,EAAE,0BAA0B;oBAClC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACjB,CAAC,CAAC;gBACH,MAAM,OAAO,GAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACtD,MAAM,EAAE,2BAA2B;oBACnC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACnB,CAAC,CAAC;gBAEH,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAC;oBAClB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;iBAClE;aACF;YACD,OAAO,MAAM,CAAC;SACf;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF;AAxDD,8DAwDC;AAED;;;GAGG;AACH,MAAa,mBAAmB;IAG9B,YAAY,QAA0B;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAe;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAW;QAChC,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;IAChE,CAAC;CACF;AApCD,kDAoCC"}