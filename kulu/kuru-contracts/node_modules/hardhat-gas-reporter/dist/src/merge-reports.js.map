{"version": 3, "file": "merge-reports.js", "sourceRoot": "", "sources": ["../../src/merge-reports.ts"], "names": [], "mappings": ";;;AAEA;;;;GAIG;AACH,SAAgB,YAAY,CAAC,OAA+B;IAC1D,MAAM,MAAM,GAAyB;QACnC,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE;YACJ,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,IAAI;SACjB;KACF,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;SACrC;QAED,IAAI,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QAED,mEAAmE;QACnE,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC9C,IACE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ;gBAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAC/C;gBACA,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;aACjD;SACF;aAAM;YACL,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;SACjD;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;YAC9D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;SACjD;aAAM,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5D,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QAED,6BAA6B;QAC7B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBACjC,OAAO;aACR;YAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG;gBACjC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO;gBACnC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO;aACpC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAExB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;SACxE;QAED,iCAAiC;QACjC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;YAE9E,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACrF;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAhFD,oCAgFC"}