{"name": "supports-color", "version": "3.2.3", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbna.nl)", "<PERSON><PERSON> <<EMAIL>> (github.com/qix-)"], "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "xo && mocha", "travis": "mocha"}, "files": ["index.js", "browser.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}}