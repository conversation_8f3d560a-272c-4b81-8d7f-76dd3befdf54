{"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "1.1.7", "repository": {"type": "git", "url": "git://github.com/substack/node-resolve.git"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^3.5.0", "tap": "0.4.13"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}}