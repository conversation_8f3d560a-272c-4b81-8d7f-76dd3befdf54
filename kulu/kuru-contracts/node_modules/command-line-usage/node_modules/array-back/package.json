{"name": "array-back", "author": "<PERSON> <<EMAIL>>", "version": "4.0.2", "description": "Guarantees an array back", "repository": "https://github.com/75lb/array-back.git", "license": "MIT", "main": "dist/index.js", "keywords": ["to", "convert", "return", "array", "arrayify"], "engines": {"node": ">=8"}, "files": ["index.mjs", "dist/index.js"], "scripts": {"test": "npm run dist && npm run test:esm && npm run test:web", "test:esm": "esm-runner test.mjs", "test:web": "web-runner test.mjs", "docs": "jsdoc2md -t README.hbs index.mjs -c jsdoc.conf > README.md", "dist": "rollup -f umd -n arrayBack -o dist/index.js index.mjs", "cover": "c8 npm run test:esm && c8 report --reporter=text-lcov | coveralls"}, "dependencies": {}, "devDependencies": {"@test-runner/web": "^0.2.1", "c8": "^6.0.1", "coveralls": "^3.0.7", "esm-runner": "^0.2.0", "isomorphic-assert": "^0.1.1", "jsdoc-to-markdown": "^5.0.2", "rollup": "^1.26.5"}, "standard": {"ignore": ["dist"]}}