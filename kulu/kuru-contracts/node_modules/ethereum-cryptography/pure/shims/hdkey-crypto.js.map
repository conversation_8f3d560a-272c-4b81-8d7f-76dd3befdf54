{"version": 3, "file": "hdkey-crypto.js", "sourceRoot": "", "sources": ["../../src/pure/shims/hdkey-crypto.ts"], "names": [], "mappings": ";;AAAA,0CAAyC;AACzC,oCAAmC;AAEtB,QAAA,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AACpC,QAAA,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAElD;IAGE,cAA6B,YAAqC;QAArC,iBAAY,GAAZ,YAAY,CAAyB;QAF1D,YAAO,GAAa,EAAE,CAAC;IAEsC,CAAC;IAE/D,qBAAM,GAAb,UAAc,MAAc;QAC1B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,qBAAM,GAAb,UAAc,KAAU;QACtB,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACxD,CAAC;IACH,WAAC;AAAD,CAAC,AAtBD,IAsBC;AAED,qEAAqE;AACxD,QAAA,UAAU,GAAG,UAAC,IAAY;IACrC,IAAI,IAAI,KAAK,WAAW,EAAE;QACxB,OAAO,IAAI,IAAI,CAAC,qBAAS,CAAC,CAAC;KAC5B;IAED,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,OAAO,IAAI,IAAI,CAAC,eAAM,CAAC,CAAC;KACzB;IAED,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACnD,CAAC,CAAC"}