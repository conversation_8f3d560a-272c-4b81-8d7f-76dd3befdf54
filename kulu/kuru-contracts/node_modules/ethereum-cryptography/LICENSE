bip39-without-wordlist.js is based on bip39, which is released under the
ISC license by the <PERSON> Lu and <PERSON>. Its license complete license can be found here: https://github.com/bitcoinjs/bip39/blob/0a0e74eac9f4b753939dd6368984900fdba1975b/LICENSE

hdkey-without-crypto.js is based on hdkey, which is released under the
MIT license by the cryptocoinjs. Its license complete license can be found here: https://github.com/cryptocoinjs/hdkey/blob/87d5495ac8e4dfe9917ddcebf6788ce69c0695ff/LICENSE

The rest of the project is released under this license:

Copyright 2019 The ethereum-cryptography contributors

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
