{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/internal/solidity/compiler/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAyC;AACzC,uCAAyB;AACzB,sDAAyB;AACzB,0DAA6B;AAC7B,+CAAiC;AAEjC,8CAAiD;AACjD,wDAAgD;AAMhD,MAAa,QAAQ;IACnB,YAAoB,aAAqB;QAArB,kBAAa,GAAb,aAAa,CAAQ;IAAG,CAAC;IAEtC,KAAK,CAAC,OAAO,CAAC,KAAoB;QACvC,MAAM,UAAU,GAAG,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAW,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3D,IAAI;gBACF,MAAM,UAAU,GAAG,IAAA,wBAAQ,EACzB,OAAO,CAAC,QAAQ,EAChB,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,EAChC;oBACE,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG;iBAC7B,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBACd,IAAI,GAAG,KAAK,IAAI,EAAE;wBAChB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB;oBACD,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CACF,CAAC;gBAEF,UAAU,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/C,UAAU,CAAC,KAAM,CAAC,GAAG,EAAE,CAAC;aACzB;YAAC,OAAO,CAAM,EAAE;gBACf,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,IAAI,CAAC,YAAY,EACxB,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,EACpB,CAAC,CACF,CAAC;aACH;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACF;AAnCD,4BAmCC;AAED,MAAa,cAAc;IACzB,YAAoB,WAAmB,EAAU,YAAqB;QAAlD,gBAAW,GAAX,WAAW,CAAQ;QAAU,iBAAY,GAAZ,YAAY,CAAS;IAAG,CAAC;IAEnE,KAAK,CAAC,OAAO,CAAC,KAAoB;QACvC,MAAM,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEjC,0EAA0E;QAC1E,oEAAoE;QACpE,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;gBAC3C,oBAAoB;gBACpB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACnC;iBAAM,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE;gBACjD,mBAAmB;gBACnB,MAAM,SAAS,GAAG,mBAAI,CAAC,IAAI,CAAC,iBAAE,CAAC,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC;gBACzD,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtB;SACF;QAED,MAAM,MAAM,GAAW,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3D,IAAI;gBACF,MAAM,OAAO,GAAG,IAAA,wBAAQ,EACtB,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ;oBACE,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG;iBAC7B,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBACd,IAAI,GAAG,KAAK,IAAI,EAAE;wBAChB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB;oBACD,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CACF,CAAC;gBAEF,OAAO,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC5C,OAAO,CAAC,KAAM,CAAC,GAAG,EAAE,CAAC;aACtB;YAAC,OAAO,CAAM,EAAE;gBACf,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;aACrE;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACF;AA9CD,wCA8CC"}