{"version": 3, "file": "compiler-input.js", "sourceRoot": "", "sources": ["../../../src/internal/solidity/compiler/compiler-input.ts"], "names": [], "mappings": ";;;AAEA,SAAgB,0BAA0B,CACxC,cAA8B;IAE9B,MAAM,OAAO,GAAkD,EAAE,CAAC;IAElE,qEAAqE;IACrE,MAAM,aAAa,GAAG,cAAc;SACjC,gBAAgB,EAAE;SAClB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;IAE5D,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;QAChC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;SACjC,CAAC;KACH;IAED,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,aAAa,EAAE,CAAC;IAEpD,OAAO;QACL,QAAQ,EAAE,UAAU;QACpB,OAAO;QACP,QAAQ;KACT,CAAC;AACJ,CAAC;AAvBD,gEAuBC"}