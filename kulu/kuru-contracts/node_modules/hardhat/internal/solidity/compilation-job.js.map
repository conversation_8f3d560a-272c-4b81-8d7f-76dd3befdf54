{"version": 3, "file": "compilation-job.js", "sourceRoot": "", "sources": ["../../src/internal/solidity/compilation-job.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,oDAA4B;AAI5B,6DAImC;AACnC,2CAAwD;AAIxD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,8BAA8B,CAAC,CAAC;AAElD,0DAA0D;AAC1D,MAAM,sBAAsB,GAAG,QAAQ,CAAC;AAExC,SAAS,6BAA6B,CACpC,CAGc;IAEd,OAAO,QAAQ,IAAI,CAAC,CAAC;AACvB,CAAC;AAED,MAAa,cAAc;IAMzB,YAAmB,cAA0B;QAA1B,mBAAc,GAAd,cAAc,CAAY;QALrC,oBAAe,GAGnB,IAAI,GAAG,EAAE,CAAC;IAEkC,CAAC;IAE1C,gBAAgB,CAAC,IAAkB,EAAE,cAAuB;QACjE,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhE,uCAAuC;QACvC,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI,aAAa,KAAK,SAAS,IAAI,cAAc,EAAE;YACjD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;SACrE;IACH,CAAC;IAEM,cAAc;QACnB,OAAO,CACL,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI;YAC1D,gBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,sBAAsB,CAAC,CACtE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,GAA6B;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAA4B,CAAC;QAErE,IAAA,+BAAsB,EACpB,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,aAAa,EAAE,CAAC,EACjD,qDAAqD,CACtD,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;QAC3D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAC1C,UAAU,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9D;QACD,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,gBAAgB,EAAE,EAAE;YACzC,UAAU,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,CAAC;IACzC,CAAC;IAEM,gBAAgB;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAkB;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAA,+BAAsB,EACpB,aAAa,KAAK,SAAS,EAC3B,SAAS,IAAI,CAAC,UAAU,0CAA0C,CACnE,CAAC;QAEF,OAAO,aAAa,CAAC,cAAc,CAAC;IACtC,CAAC;CACF;AAtED,wCAsEC;AAED,SAAS,oBAAoB,CAC3B,IAAgC,EAChC,WAAuD;IAEvD,MAAM,OAAO,GAAgD,IAAI,GAAG,EAAE,CAAC;IAEvE,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;QACpD,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACzC;iBAAM,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAClC,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;aAC5C;iBAAM;gBACL,IAAA,+BAAsB,EACpB,KAAK,EACL,kEAAkE,CACnE,CAAC;aACH;SACF;aAAM;YACL,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;aACxD;SACF;KACF;IAED,mDAAmD;IACnD,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,OAAS,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,2CAA2C,CAC/D,kBAA6C,EAC7C,WAEoE;IAEpE,MAAM,eAAe,GAA+B,EAAE,CAAC;IACvD,MAAM,MAAM,GAAkC,EAAE,CAAC;IAEjD,KAAK,MAAM,IAAI,IAAI,kBAAkB,CAAC,gBAAgB,EAAE,EAAE;QACxD,MAAM,qBAAqB,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC;QAEtD,IAAI,6BAA6B,CAAC,qBAAqB,CAAC,EAAE;YACxD,GAAG,CACD,IAAI,IAAI,CAAC,YAAY,oCACnB,qBACF,GAAG,CACJ,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACnC,SAAS;SACV;QAED,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;KAC7C;IAED,MAAM,IAAI,GAAG,2BAA2B,CAAC,eAAe,CAAC,CAAC;IAE1D,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1B,CAAC;AA5BD,kGA4BC;AAEM,KAAK,UAAU,4BAA4B,CAChD,eAA0C,EAC1C,IAAkB,EAClB,cAA8B;IAE9B,MAAM,kBAAkB,GAAG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjE,MAAM,sBAAsB,GAC1B,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;IAElD,MAAM,cAAc,GAAG,wBAAwB,CAC7C,IAAI,EACJ,kBAAkB,EAClB,sBAAsB,EACtB,cAAc,CACf,CAAC;IAEF,+DAA+D;IAC/D,IAAI,6BAA6B,CAAC,cAAc,CAAC,EAAE;QACjD,OAAO,cAAc,CAAC;KACvB;IACD,GAAG,CACD,SAAS,IAAI,CAAC,YAAY,oCAAoC,cAAc,CAAC,OAAO,GAAG,CACxF,CAAC;IAEF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,cAAc,CAAC,CAAC;IAE1D,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,sBAAsB,EAAE;QACnD,GAAG,CACD,SAAS,UAAU,CAAC,YAAY,6BAA6B,IAAI,CAAC,YAAY,GAAG,CAClF,CAAC;QACF,cAAc,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KACpD;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAnCD,oEAmCC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CACzC,eAA2C;IAE3C,OAAO,oBAAoB,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC;AAC9E,CAAC;AAJD,kEAIC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,eAA2C;IAE3C,OAAO,oBAAoB,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC;AAC/E,CAAC;AAJD,wEAIC;AAED;;;GAGG;AACH,SAAS,wBAAwB,CAC/B,IAAkB,EAClB,kBAAkC,EAClC,sBAAwD,EACxD,cAA8B;IAE9B,MAAM,oCAAoC,GAAG,sBAAsB;SAChE,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC;SAC1D,IAAI,EAAE,CAAC;IACV,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAC7B,IAAI,GAAG,CAAC;QACN,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;QAC9B,GAAG,oCAAoC;KACxC,CAAC,CACH,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC;IAEjD,MAAM,kBAAkB,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEtD,6CAA6C;IAC7C,IAAI,kBAAkB,KAAK,SAAS,EAAE;QACpC,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE;YAC/D,OAAO,8BAA8B,CACnC,IAAI,EACJ,kBAAkB,EAClB,sBAAsB,EACtB,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAC5B,IAAI,CACL,CAAC;SACH;QAED,OAAO,kBAAkB,CAAC;KAC3B;IAED,4EAA4E;IAC5E,MAAM,gBAAgB,GAAG,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACxE,MAAM,eAAe,GAAG,gBAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAE7E,IAAI,eAAe,KAAK,IAAI,EAAE;QAC5B,OAAO,8BAA8B,CACnC,IAAI,EACJ,kBAAkB,EAClB,sBAAsB,EACtB,gBAAgB,EAChB,KAAK,CACN,CAAC;KACH;IAED,MAAM,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAClD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,eAAe,CACpC,CAAC;IAEH,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,8BAA8B,CACrC,IAAkB,EAClB,kBAAkC,EAClC,sBAAwD,EACxD,gBAA0B,EAC1B,SAAkB;IAElB,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/D,IAAI,gBAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,IAAI,EAAE;QACrE,MAAM,MAAM,GAAG,SAAS;YACtB,CAAC,CAAC,iDAAiC,CAAC,mCAAmC;YACvE,CAAC,CAAC,iDAAiC,CAAC,gCAAgC,CAAC;QACvE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;KACzB;IAED,MAAM,yBAAyB,GAAmB,EAAE,CAAC;IACrD,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE;QAC3C,MAAM,sBAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,EAAE;YAChE,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC5C;KACF;IAED,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE;QACxC,OAAO;YACL,MAAM,EACJ,iDAAiC,CAAC,kCAAkC;YACtE,IAAI;YACJ,KAAK,EAAE;gBACL,yBAAyB;aAC1B;SACF,CAAC;KACH;IAED,MAAM,2BAA2B,GAAqC,EAAE,CAAC;IACzE,KAAK,MAAM,oBAAoB,IAAI,sBAAsB,EAAE;QACzD,MAAM,EAAE,UAAU,EAAE,GAAG,oBAAoB,CAAC;QAC5C,MAAM,sBAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,EAAE;YAChE,2BAA2B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACxD;KACF;IAED,IAAI,2BAA2B,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1C,OAAO;YACL,MAAM,EACJ,iDAAiC,CAAC,oCAAoC;YACxE,IAAI;YACJ,KAAK,EAAE;gBACL,2BAA2B;aAC5B;SACF,CAAC;KACH;IAED,OAAO,EAAE,MAAM,EAAE,iDAAiC,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;AACzE,CAAC"}