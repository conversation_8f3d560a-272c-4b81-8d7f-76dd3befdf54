/**
 * Returns the plural form of a word.
 *
 * @param n The number of things to represent. This dictates whether to return
 * the singular or plural form of the word.
 * @param singular The singular form of the word.
 * @param plural An optional plural form of the word. If non is given, the
 * plural form is constructed by appending an "s" to the singular form.
 */
export declare function pluralize(n: number, singular: string, plural?: string): string;
/**
 * Replaces all the instances of [[toReplace]] by [[replacement]] in [[str]].
 */
export declare function replaceAll(str: string, toReplace: string, replacement: string): string;
//# sourceMappingURL=strings.d.ts.map