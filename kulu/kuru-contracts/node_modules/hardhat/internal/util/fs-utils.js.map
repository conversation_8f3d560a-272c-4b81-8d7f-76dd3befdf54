{"version": 3, "file": "fs-utils.js", "sourceRoot": "", "sources": ["../../src/internal/util/fs-utils.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAAqC;AACrC,4CAAoB;AACpB,gDAAwB;AACxB,2CAA6C;AAE7C,6EAA6E;AAC7E,8DAA8D;AAC9D,MAAa,qBAAsB,SAAQ,oBAAW;CAAG;AAAzD,sDAAyD;AAEzD,MAAa,iBAAkB,SAAQ,oBAAW;IAChD,YAAY,QAAgB,EAAE,MAAc;QAC1C,KAAK,CAAC,QAAQ,QAAQ,YAAY,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,8CAIC;AACD,MAAa,qBAAsB,SAAQ,oBAAW;IACpD,YAAY,QAAgB,EAAE,MAAa;QACzC,KAAK,CAAC,qBAAqB,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;CACF;AAJD,sDAIC;AAED;;;;GAIG;AACI,KAAK,UAAU,WAAW,CAAC,YAAoB;IACpD,IAAI;QACF,yCAAyC;QACzC,2CAA2C;QAC3C,OAAO,MAAM,kBAAU,CAAC,QAAQ,CAAC,cAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;KAChE;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACvB,sFAAsF;YACtF,MAAM,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;SAC9C;QAED,sFAAsF;QACtF,MAAM,IAAI,qBAAqB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;KAC/C;AACH,CAAC;AAdD,kCAcC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,YAAoB;IAClD,IAAI;QACF,yCAAyC;QACzC,2CAA2C;QAC3C,OAAO,YAAE,CAAC,YAAY,CAAC,MAAM,CAAC,cAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;KAC7D;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACvB,sFAAsF;YACtF,MAAM,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;SAC9C;QAED,sFAAsF;QACtF,MAAM,IAAI,qBAAqB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;KAC/C;AACH,CAAC;AAdD,0CAcC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,mBAAmB,CACvC,iBAAyB,EACzB,OAAiD;IAEjD,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAE7C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,MAAM,kBAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;YACvB,MAAM,KAAK,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACrE,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;SACrB;aAAM,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAC/D,OAAO,kBAAkB,CAAC;SAC3B;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;AACxB,CAAC;AAtBD,kDAsBC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CACrC,iBAAyB,EACzB,OAAiD;IAEjD,MAAM,GAAG,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;IAE3C,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC/B,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO,uBAAuB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;SACpE;aAAM,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAC/D,OAAO,kBAAkB,CAAC;SAC3B;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;AACxB,CAAC;AAnBD,0DAmBC;AAED;;;GAGG;AACI,KAAK,UAAU,eAAe,CACnC,IAAY,EACZ,YAAoB;IAEpB,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAEvC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAEhD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;QACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,gBAAgB,EAAE;YAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,OAAO,QAAQ,CAAC;aACjB;YAED,OAAO,cAAI,CAAC,IAAI,CACd,QAAQ,EACR,MAAM,eAAe,CACnB,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EACzB,cAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CACtC,CACF,CAAC;SACH;KACF;IAED,sFAAsF;IACtF,MAAM,IAAI,iBAAiB,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;AAC7D,CAAC;AA3BD,0CA2BC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CACjC,IAAY,EACZ,YAAoB;IAEpB,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAEhD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;QACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,gBAAgB,EAAE;YAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,OAAO,QAAQ,CAAC;aACjB;YAED,OAAO,cAAI,CAAC,IAAI,CACd,QAAQ,EACR,mBAAmB,CACjB,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EACzB,cAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CACtC,CACF,CAAC;SACH;KACF;IAED,sFAAsF;IACtF,MAAM,IAAI,iBAAiB,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;AAC7D,CAAC;AA3BD,kDA2BC;AAED,KAAK,UAAU,OAAO,CAAC,iBAAyB;IAC9C,IAAI;QACF,OAAO,MAAM,kBAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;KACpD;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACvB,OAAO,EAAE,CAAC;SACX;QAED,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;YACxB,sFAAsF;YACtF,MAAM,IAAI,qBAAqB,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;SACvD;QAED,sFAAsF;QACtF,MAAM,IAAI,qBAAqB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;KAC/C;AACH,CAAC;AAED,SAAS,WAAW,CAAC,iBAAyB;IAC5C,IAAI;QACF,OAAO,YAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;KAC1C;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACvB,OAAO,EAAE,CAAC;SACX;QAED,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;YACxB,sFAAsF;YACtF,MAAM,IAAI,qBAAqB,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;SACvD;QAED,sFAAsF;QACtF,MAAM,IAAI,qBAAqB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;KAC/C;AACH,CAAC"}