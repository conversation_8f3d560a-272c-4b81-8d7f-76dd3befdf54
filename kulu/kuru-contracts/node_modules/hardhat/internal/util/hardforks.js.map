{"version": 3, "file": "hardforks.js", "sourceRoot": "", "sources": ["../../src/internal/util/hardforks.ts"], "names": [], "mappings": ";;;AACA,4CAAmE;AACnE,2CAAwD;AACxD,qDAAyD;AAEzD,+EAA+E;AAE/E,IAAY,YAkBX;AAlBD,WAAY,YAAY;IACtB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,2BAAW,CAAA;IACX,sDAAsC,CAAA;IACtC,kDAAkC,CAAA;IAClC,uCAAuB,CAAA;IACvB,iDAAiC,CAAA;IACjC,yCAAyB,CAAA;IACzB,qCAAqB,CAAA;IACrB,4CAA4B,CAAA;IAC5B,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,8CAA8B,CAAA;IAC9B,4CAA4B,CAAA;IAC5B,+BAAe,CAAA;IACf,qCAAqB,CAAA;IACrB,iCAAiB,CAAA;AACnB,CAAC,EAlBW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAkBvB;AAED,MAAM,eAAe,GAAmB;IACtC,YAAY,CAAC,QAAQ;IACrB,YAAY,CAAC,SAAS;IACtB,YAAY,CAAC,GAAG;IAChB,YAAY,CAAC,iBAAiB;IAC9B,YAAY,CAAC,eAAe;IAC5B,YAAY,CAAC,SAAS;IACtB,YAAY,CAAC,cAAc;IAC3B,YAAY,CAAC,UAAU;IACvB,YAAY,CAAC,QAAQ;IACrB,YAAY,CAAC,YAAY;IACzB,YAAY,CAAC,MAAM;IACnB,YAAY,CAAC,MAAM;IACnB,YAAY,CAAC,aAAa;IAC1B,YAAY,CAAC,YAAY;IACzB,YAAY,CAAC,KAAK;IAClB,YAAY,CAAC,QAAQ;IACrB,YAAY,CAAC,MAAM;CACpB,CAAC;AAEF,SAAgB,eAAe,CAAC,IAAY;IAC1C,MAAM,YAAY,GAChB,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CACzB,MAAM,CAAC,MAAM,CAAS,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAClD,CAAC;IAEJ,IAAA,+BAAsB,EACpB,YAAY,KAAK,SAAS,EAC1B,wBAAwB,IAAI,EAAE,CAC/B,CAAC;IAEF,OAAO,YAAY,CAAC;AACtB,CAAC;AAZD,0CAYC;AAED;;;GAGG;AACH,SAAgB,WAAW,CACzB,SAAuB,EACvB,SAAuB;IAEvB,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAM,MAAM,GAAG,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACtD,MAAM,MAAM,GAAG,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAEtD,OAAO,MAAM,IAAI,MAAM,CAAC;AAC1B,CAAC;AAVD,kCAUC;AAED,SAAgB,cAAc,CAC5B,eAAmC,EACnC,eAAuB,EACvB,mBAAsD,EACtD,WAAmB;IAEnB,IAAI,eAAe,KAAK,SAAS,IAAI,WAAW,GAAG,eAAe,EAAE;QAClE,OAAO,eAAe,CAAC;KACxB;IAED,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,CAAC,IAAI,KAAK,CAAC,EAAE;QACvE,MAAM,IAAI,sBAAa,CACrB,uDAAuD,WAAW,CAAC,QAAQ,EAAE,mCAAmC,eAAe,oHAAoH,CACpP,CAAC;KACH;IAED;;mCAE+B;IAC/B,MAAM,eAAe,GAAyC,KAAK,CAAC,IAAI,CACtE,mBAAmB,CAAC,OAAO,EAAE,CAC9B,CAAC;IACF,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,eAAe,CAAC,MAAM,CACxD,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,EAAE,CAC7D,SAAS,GAAG,YAAY,IAAI,SAAS,IAAI,WAAW;QAClD,CAAC,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,CACtC,CAAC;IACF,IAAI,QAAQ,KAAK,SAAS,IAAI,WAAW,GAAG,eAAe,EAAE;QAC3D,MAAM,IAAI,sBAAa,CACrB,8CAA8C,WAAW,CAAC,QAAQ,EAAE,gFAAgF,IAAI,CAAC,SAAS,CAChK,eAAe,CAChB,mFAAmF,CACrF,CAAC;KACH;IAED,IAAI,CAAC,+CAAmC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3D,MAAM,IAAI,sBAAa,CACrB,mFAAmF,QAAQ,iEAAiE,+CAAmC,CAAC,IAAI,CAClM,IAAI,CACL,EAAE,CACJ,CAAC;KACH;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AA7CD,wCA6CC"}