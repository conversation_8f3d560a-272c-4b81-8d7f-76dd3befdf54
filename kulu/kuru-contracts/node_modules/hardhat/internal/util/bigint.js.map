{"version": 3, "file": "bigint.js", "sourceRoot": "", "sources": ["../../src/internal/util/bigint.ts"], "names": [], "mappings": ";;;AAAA,2CAAwD;AAExD,SAAgB,GAAG,CAAC,CAAS,EAAE,CAAS;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAFD,kBAEC;AAED,SAAgB,GAAG,CAAC,CAAS,EAAE,CAAS;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAFD,kBAEC;AAED,SAAgB,QAAQ,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AAFD,4BAEC;AAED,SAAgB,KAAK,CAAC,CAAS,EAAE,CAAS;IACxC,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IAEnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE;QAChB,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AARD,sBAQC;AAED,SAAgB,GAAG,CAAC,CAAS,EAAE,CAAS;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;AAFD,kBAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,CAAkB;IAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC1C,CAAC;AAFD,8BAEC;AAED,SAAS,cAAc,CAAC,CAAa;IACnC,MAAM,GAAG,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;IAClD,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzC,CAAC;AAED,SAAgB,cAAc,CAC5B,CAAoD;IAEpD,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QAC5C,OAAO,CAAC,CAAC;KACV;IACD,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;KAClB;IACD,IAAI,CAAC,YAAY,UAAU,EAAE;QAC3B,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;KAC1B;IAED,MAAM,eAAe,GAAU,CAAC,CAAC;IACjC,OAAO,eAAe,CAAC;AACzB,CAAC;AAfD,wCAeC;AAED,SAAgB,KAAK,CAAC,CAAkB;IACtC,IAAA,+BAAsB,EACpB,CAAC,IAAI,CAAC,EACN,kEAAkE,CAAC,EAAE,CACtE,CAAC;IAEF,OAAO,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAC/B,CAAC;AAPD,sBAOC"}