{"version": 3, "file": "event-emitter.js", "sourceRoot": "", "sources": ["../../src/internal/util/event-emitter.ts"], "names": [], "mappings": ";;;AAEA,6EAA6E;AAC7E,gFAAgF;AAChF,6EAA6E;AAC7E,+EAA+E;AAC/E,uDAAuD;AACvD,EAAE;AACF,4EAA4E;AAC5E,4EAA4E;AAC5E,+EAA+E;AAC/E,qEAAqE;AACrE,MAAa,mBAAmB;IAC9B,YAA6B,QAAsB;QAAtB,aAAQ,GAAR,QAAQ,CAAc;IAAG,CAAC;IAEhD,WAAW,CAChB,KAAsB,EACtB,QAAkC;QAElC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,EAAE,CAAC,KAAsB,EAAE,QAAkC;QAClE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,IAAI,CACT,KAAsB,EACtB,QAAkC;QAElC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CACpB,KAAsB,EACtB,QAAkC;QAElC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,mBAAmB,CACxB,KAAsB,EACtB,QAAkC;QAElC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,cAAc,CACnB,KAAsB,EACtB,QAAkC;QAElC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAsB,EAAE,QAAkC;QACnE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,kBAAkB,CAAC,KAAmC;QAC3D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CAAC,CAAS;QAC9B,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;IACzC,CAAC;IAED,wDAAwD;IACjD,SAAS,CAAC,KAAsB;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,wDAAwD;IACjD,YAAY,CAAC,KAAsB;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAEM,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5C,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAEM,aAAa,CAAC,IAAqB;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;CACF;AAxFD,kDAwFC"}