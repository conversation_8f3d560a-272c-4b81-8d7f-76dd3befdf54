{"version": 3, "file": "subprocess.js", "sourceRoot": "", "sources": ["../../src/internal/sentry/subprocess.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAuC;AACvC,kDAA0B;AAE1B,6CAA0C;AAC1C,yCAAwC;AAExC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,2BAA2B,CAAC,CAAC;AAE/C,KAAK,UAAU,IAAI;IACjB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM,CAAC;IAE9D,IAAI,OAAO,EAAE;QACX,eAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KAC1B;IAED,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAE3B,IAAI;QACF,MAAM,CAAC,IAAI,CAAC;YACV,GAAG,EAAE,qBAAU;SAChB,CAAC,CAAC;KACJ;IAAC,OAAO,KAAK,EAAE;QACd,GAAG,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;IACzD,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QAC7D,MAAM,CAAC,cAAc,CACnB,mFAAmF,CACpF,CAAC;QACF,OAAO;KACR;IAED,IAAI,KAAU,CAAC;IACf,IAAI;QACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;KACrC;IAAC,MAAM;QACN,GAAG,CACD,0EAA0E,EAC1E,eAAe,CAChB,CAAC;QACF,MAAM,CAAC,cAAc,CACnB,kGAAkG,CACnG,CAAC;QACF,OAAO;KACR;IAED,IAAI;QACF,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAE1D,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,UAAU,CAAC,CAAC;QAC9C,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEpD,IAAI,eAAe,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;gBACrD,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,MAAM,CAAC,cAAc,CACnB,4CAA4C,eAAe,CAAC,KAAK,EAAE,CACpE,CAAC;SACH;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,GAAG,CAAC,yCAAyC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,CAAC,cAAc,CACnB,0CAA0C,KAAK,CAAC,OAAO,EAAE,CAC1D,CAAC;QACF,OAAO;KACR;IAED,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAC/B,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}