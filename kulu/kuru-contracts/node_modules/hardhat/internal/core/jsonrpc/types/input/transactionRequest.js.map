{"version": 3, "file": "transactionRequest.js", "sourceRoot": "", "sources": ["../../../../../src/internal/core/jsonrpc/types/input/transactionRequest.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAE3B,kDAA4D;AAC5D,gDAA+C;AAC/C,8CAA0E;AAE1E,mCAAmC;AACtB,QAAA,qBAAqB,GAAG,CAAC,CAAC,IAAI,CACzC;IACE,IAAI,EAAE,uBAAU;IAChB,EAAE,EAAE,IAAA,0BAAkB,EAAC,uBAAU,CAAC;IAClC,GAAG,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACpC,QAAQ,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACzC,KAAK,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACtC,KAAK,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACtC,IAAI,EAAE,IAAA,0BAAkB,EAAC,oBAAO,CAAC;IACjC,UAAU,EAAE,IAAA,0BAAkB,EAAC,2BAAa,CAAC;IAC7C,OAAO,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACxC,YAAY,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IAC7C,oBAAoB,EAAE,IAAA,0BAAkB,EAAC,wBAAW,CAAC;IACrD,KAAK,EAAE,IAAA,0BAAkB,EAAC,CAAC,CAAC,KAAK,CAAC,oBAAO,CAAC,CAAC;IAC3C,mBAAmB,EAAE,IAAA,0BAAkB,EAAC,CAAC,CAAC,KAAK,CAAC,oBAAO,CAAC,CAAC;CAC1D,EACD,uBAAuB,CACxB,CAAC"}