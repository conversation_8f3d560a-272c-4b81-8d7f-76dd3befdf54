/// <reference types="node" />
import * as t from "io-ts";
export declare const rpcLogTopics: t.Array<PERSON><t.UnionC<[t.<PERSON>ullC, t.Type<Buffer, Buffer, unknown>, t.<PERSON>rray<PERSON><t.UnionC<[t.<PERSON>ull<PERSON>, t.Type<Buffer, Buffer, unknown>]>>]>>;
export type RpcLogTopics = t.TypeOf<typeof rpcLogTopics>;
export declare const optionalRpcLogTopics: t.Type<(Buffer | (Buffer | null)[] | null)[] | undefined, (Buffer | (Buffer | null)[] | null)[] | undefined, unknown>;
export type OptionalRpcLogTopics = t.TypeOf<typeof optionalRpcLogTopics>;
//# sourceMappingURL=logTopics.d.ts.map