{"version": 3, "file": "config-env.js", "sourceRoot": "", "sources": ["../../../src/internal/core/config/config-env.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,2CAA+C;AAC/C,sCAAyC;AACzC,gDAAwC;AACxC,uEAAyD;AAkCzD,SAAgB,IAAI,CAClB,IAAY,EACZ,mBAAyD,EACzD,MAAmC;IAEnC,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IAEzB,IAAI,mBAAmB,KAAK,SAAS,EAAE;QACrC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACvB;IAED,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;QAC3C,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;KAC5C;IAED,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;AACrD,CAAC;AAjBD,oBAiBC;AAmCD,SAAgB,OAAO,CACrB,IAAY,EACZ,mBAAyD,EACzD,MAAmC;IAEnC,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IAEzB,IAAI,mBAAmB,KAAK,SAAS,EAAE;QACrC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;QAC3C,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;KAC/C;IAED,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;AACxD,CAAC;AAjBD,0BAiBC;AAED,gCAAgC;AACnB,QAAA,YAAY,GAAG,OAAO,CAAC;AAEpC,SAAgB,KAAK,CACnB,IAAY,EACZ,WAAoB;IAEpB,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IAEzB,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACtC,CAAC;AARD,sBAQC;AAEY,QAAA,KAAK,GAAG,aAAa,CAAC;AAEnC;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,QAA6B;IAC7D,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1C,CAAC;AAHD,8CAGC;AAED;;;;;;GAMG;AACH,SAAgB,YAAY,CAAC,QAAwB;IACnD,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAHD,oCAGC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,QAA0B;IACvD,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvC,CAAC;AAHD,wCAGC;AAED,0EAA0E;AAC1E,iCAAiC;AACjC,SAAgB,6CAA6C,CAC3D,IAAgD;IAEhD,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,GAAG,CAAC,2CAA2C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7D,CAAC;AALD,sGAKC;AAED;;GAEG;AACU,QAAA,IAAI,GAAG;IAClB,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;CACZ,CAAC;AAEF;;;;;;;;;GASG;AACH,SAAS,MAAM,CAAC,OAAe;IAC7B,uHAAuH;IACvH,OAAO,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,MAAM,CAAC,OAAe,EAAE,YAAqB;IACpD,uHAAuH;IACvH,MAAM,KAAK,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,GAAG,CAC9D,OAAO,EACP,YAAY,EACZ,IAAI,CACL,CAAC;IAEF,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAEtC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;QAC1D,KAAK,EAAE,OAAO;KACf,CAAC,CAAC;AACL,CAAC"}