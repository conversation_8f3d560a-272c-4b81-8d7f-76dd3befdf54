{"version": 3, "file": "default-config.js", "sourceRoot": "", "sources": ["../../../src/internal/core/config/default-config.ts"], "names": [], "mappings": ";;;AACA,oDAAoD;AACpD,+CAAuD;AAE1C,QAAA,oBAAoB,GAAG,OAAO,CAAC;AAC/B,QAAA,iCAAiC,GAAG,MAAM,CAAC;AAC3C,QAAA,gDAAgD,GAAG,GAAG,CAAC;AACvD,QAAA,gDAAgD,GAAG,GAAG,CAAC;AACvD,QAAA,wBAAwB,GACnC,6DAA6D,CAAC;AACnD,QAAA,+BAA+B,GAAG,yBAAyB,CAAC;AAE5D,QAAA,qBAAqB,GAAG,gCAAoB,CAAC;AAE7C,QAAA,6BAA6B,GAAG;IAC3C,GAAG,EAAE,uBAAuB;IAC5B,OAAO,EAAE,KAAK;CACf,CAAC;AAEW,QAAA,6BAA6B,GAAG;IAC3C,YAAY,EAAE,CAAC;IACf,KAAK,EAAE,EAAE;IACT,IAAI,EAAE,gBAAgB;IACtB,UAAU,EAAE,EAAE;CACf,CAAC;AAEW,QAAA,2CAA2C,GAAG;IACzD,GAAG,qCAA6B;IAChC,QAAQ,EAAE,gCAAwB;IAClC,eAAe,EAAE,uCAA+B;CACjD,CAAC;AAEW,QAAA,sBAAsB,GAAG,CAAC,CAAC;AAE3B,QAAA,2BAA2B,GAGpC;IACF,QAAQ,EAAE,wBAAY,CAAC,MAAM;IAC7B,aAAa,EAAE,QAAU;IACzB,QAAQ,EAAE,yCAAiC;IAC3C,OAAO,EAAE,KAAK;IACd,0BAA0B,EAAE,IAAI;IAChC,mBAAmB,EAAE,IAAI;IACzB,0BAA0B,EAAE,KAAK;IACjC,MAAM,EAAE;QACN,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,KAAK,EAAE,UAAU;SAClB;KACF;IACD,QAAQ,EAAE,mDAA2C;IACrD,cAAc,EAAE,KAAK;IACrB,aAAa,EAAE,8BAAsB;IACrC,WAAW,EAAE,EAAE;IACf,MAAM,EAAE,IAAI,GAAG,CAAC;QACd;YACE,+HAA+H;YAC/H,CAAC;YACD;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC1B,CAAC,wBAAY,CAAC,SAAS,EAAE,OAAS,CAAC;oBACnC,CAAC,wBAAY,CAAC,GAAG,EAAE,OAAS,CAAC;oBAC7B,CAAC,wBAAY,CAAC,iBAAiB,EAAE,OAAS,CAAC;oBAC3C,CAAC,wBAAY,CAAC,eAAe,EAAE,OAAS,CAAC;oBACzC,CAAC,wBAAY,CAAC,SAAS,EAAE,OAAS,CAAC;oBACnC,CAAC,wBAAY,CAAC,cAAc,EAAE,OAAS,CAAC;oBACxC,CAAC,wBAAY,CAAC,UAAU,EAAE,OAAS,CAAC;oBACpC,CAAC,wBAAY,CAAC,QAAQ,EAAE,OAAS,CAAC;oBAClC,CAAC,wBAAY,CAAC,YAAY,EAAE,OAAS,CAAC;oBACtC,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAU,CAAC;oBACjC,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAU,CAAC;oBACjC,CAAC,wBAAY,CAAC,aAAa,EAAE,QAAU,CAAC;oBACxC,CAAC,wBAAY,CAAC,YAAY,EAAE,QAAU,CAAC;oBACvC,CAAC,wBAAY,CAAC,KAAK,EAAE,QAAU,CAAC;oBAChC,CAAC,wBAAY,CAAC,QAAQ,EAAE,QAAU,CAAC;oBACnC,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAU,CAAC;iBAClC,CAAC;aACH;SACF;QACD;YACE,CAAC;YACD;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,SAAS,EAAE,OAAO,CAAC;oBACjC,CAAC,wBAAY,CAAC,cAAc,EAAE,OAAO,CAAC;oBACtC,CAAC,wBAAY,CAAC,UAAU,EAAE,OAAO,CAAC;oBAClC,CAAC,wBAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;oBAChC,CAAC,wBAAY,CAAC,YAAY,EAAE,OAAO,CAAC;oBACpC,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAO,CAAC;oBAC9B,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;iBAChC,CAAC;aACH;SACF;QACD;YACE,CAAC;YACD;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,SAAS,EAAE,OAAO,CAAC;oBACjC,CAAC,wBAAY,CAAC,cAAc,EAAE,OAAO,CAAC;oBACtC,CAAC,wBAAY,CAAC,UAAU,EAAE,OAAO,CAAC;oBAClC,CAAC,wBAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;oBAChC,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAO,CAAC;oBAC9B,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAO,CAAC;iBAC/B,CAAC;aACH;SACF;QACD;YACE,CAAC;YACD;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,QAAQ,EAAE,OAAO,CAAC;oBAChC,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAO,CAAC;oBAC9B,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAO,CAAC;iBAC/B,CAAC;aACH;SACF;QACD;YACE,EAAE;YACF;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,SAAS,EAAE,OAAO,CAAC;oBACjC,CAAC,wBAAY,CAAC,cAAc,EAAE,OAAO,CAAC;oBACtC,CAAC,wBAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;oBACnC,CAAC,wBAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;oBACjC,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;oBAC/B,CAAC,wBAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;iBAChC,CAAC;aACH;SACF;QACD;YACE,QAAQ;YACR;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC;oBACvB,CAAC,wBAAY,CAAC,YAAY,EAAE,CAAC,CAAC;oBAC9B,CAAC,wBAAY,CAAC,KAAK,EAAE,OAAS,CAAC;oBAC/B,CAAC,wBAAY,CAAC,QAAQ,EAAE,OAAS,CAAC;oBAClC,CAAC,wBAAY,CAAC,MAAM,EAAE,OAAS,CAAC;iBACjC,CAAC;aACH;SACF;QACD,2DAA2D;QAC3D,wDAAwD;QACxD;YACE,EAAE;YACF;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,wBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;aACvD;SACF;QACD;YACE,QAAQ;YACR;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,wBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;aACvD;SACF;QACD;YACE,KAAK;YACL;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,wBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;aACvD;SACF;QACD;YACE,MAAM;YACN;gBACE,eAAe,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,wBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;aACvD;SACF;KACF,CAAC;CACH,CAAC;AAEW,QAAA,wBAAwB,GAAG;IACtC,QAAQ,EAAE,QAAoB;IAC9B,GAAG,EAAE,MAAgB;IACrB,QAAQ,EAAE,MAAgB;IAC1B,aAAa,EAAE,8BAAsB;IACrC,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,KAAK;CACf,CAAC;AAEW,QAAA,mBAAmB,GAAuB;IACrD,OAAO,EAAE,KAAK;CACf,CAAC;AAEW,QAAA,0BAA0B,GAAG;IACxC,GAAG,EAAE;QACH,GAAG,EAAE;YACH,KAAK;YACL,cAAc;YACd,sBAAsB;YACtB,uBAAuB;YACvB,UAAU;SACX;QACD,EAAE,EAAE,CAAC,KAAK,CAAC;KACZ;CACF,CAAC"}