{"version": 3, "file": "config-loading.js", "sourceRoot": "", "sources": ["../../../src/internal/core/config/config-loading.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,kDAA0B;AAC1B,wDAA+B;AAC/B,gDAAwB;AACxB,oDAA4B;AAQ5B,2CAA+C;AAC/C,wDAAgE;AAChE,sCAAyC;AACzC,gDAAwC;AACxC,4DAAyD;AAEzD,4EAAgG;AAChG,2DAAoD;AACpD,qDAAwD;AAExD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,qBAAqB,CAAC,CAAC;AAEzC,SAAgB,mBAAmB,CAAC,QAAgB;IAClD,IAAI;QACF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;KACrE;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,EAAE;YAChC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,8BAA8B,EAC7C,EAAE,EACF,CAAC,CACF,CAAC;SACH;QAED,sFAAsF;QACtF,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAhBD,kDAgBC;AAED,SAAgB,iBAAiB,CAAC,UAA8B;IAC9D,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,UAAU,GAAG,IAAA,qCAAiB,GAAE,CAAC;KAClC;SAAM;QACL,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAChC,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;YAClD,UAAU,GAAG,cAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACzC;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAVD,8CAUC;AAED,SAAgB,kBAAkB,CAChC,gBAA4C,EAC5C,EACE,sBAAsB,GAAG,KAAK,EAC9B,0BAA0B,GAAG,KAAK,MAIhC;IACF,sBAAsB,EAAE,KAAK;IAC7B,0BAA0B,EAAE,KAAK;CAClC;IAED,MAAM,EAAE,cAAc,EAAE,sBAAsB,EAAE,GAC9C,OAAO,CAAC,qBAAqB,CAAyC,CAAC;IACzE,IAAI,UAAU,GACZ,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvE,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC3C,GAAG,CAAC,+BAA+B,UAAU,EAAE,CAAC,CAAC;IACjD,4EAA4E;IAC5E,uCAAuC;IACvC,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;IAE1C,MAAM,WAAW,GAAQ,MAAM,CAAC;IAEhC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAC/B,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAC7C,CAAC;IAEF,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAE/C,GAAG,CAAC,yBAAyB,EAAE,CAAC;IAEhC,IAAI,UAAU,CAAC;IAEf,IAAI;QACF,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAClC,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;KAC9C;IAAC,OAAO,CAAC,EAAE;QACV,0BAA0B,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAE1C,sFAAsF;QACtF,MAAM,CAAC,CAAC;KACT;YAAS;QACR,GAAG,CAAC,0BAA0B,EAAE,CAAC;KAClC;IAED,IAAI,sBAAsB,EAAE;QAC1B,gBAAgB,CAAC,UAAU,EAAE,EAAE,0BAA0B,EAAE,CAAC,CAAC;KAC9D;IAED,cAAc,CAAC,UAAU,CAAC,CAAC;IAE3B,IAAI,0BAA0B,EAAE;QAC9B,0BAA0B,CAAC,UAAU,CAAC,CAAC;KACxC;IAED,iEAAiE;IACjE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;IAExE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,IAAA,iCAAa,EAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAEvD,KAAK,MAAM,QAAQ,IAAI,wBAAc,CAAC,iBAAiB,EAAE,CAAC,eAAe,EAAE;QACzE,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;KACtC;IAED,sBAAsB,CAAC,QAAQ,CAAC,CAAC;IAEjC,IAAI,0BAA0B,EAAE;QAC9B,8BAA8B,CAAC,QAAQ,CAAC,CAAC;QACzC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;KACtC;IAED,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC;AACpE,CAAC;AA7ED,gDA6EC;AAED,SAAS,oBAAoB,CAC3B,MAAW,EACX,eAAgD,EAAE;IAElD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;QACjD,OAAO,MAAM,CAAC;KACf;IAED,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;QACvB,GAAG,CAAC,MAAW,EAAE,QAAkC,EAAE,QAAa;YAChE,OAAO,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBACnE,GAAG,YAAY;gBACf,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAED,GAAG,CACD,MAAW,EACX,QAAkC,EAClC,MAAW,EACX,SAAc;YAEd,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBAC1D,IAAI,EAAE,CAAC,GAAG,YAAY,EAAE,QAAQ,CAAC;qBAC9B,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;qBACtC,IAAI,CAAC,GAAG,CAAC;aACb,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,0BAA0B,CAAC,KAAU,EAAE,UAAkB;IACvE,MAAM,gBAAgB,GACpB,OAAO,CAAC,mBAAmB,CAA6B,CAAC;IAE3D,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACrC,OAAO;KACR;IACD,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvD,MAAM,YAAY,GAAG,UAAU;SAC5B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC;SAC9B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAK,CAAC;QACpB,8CAA8C;SAC7C,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,oBAAoB,CAAC,CAAC,CAAC;SACzE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnC,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE;QACvD,OAAO;KACR;IAED,6EAA6E;IAC7E,iCAAiC;IACjC,IAAI,YAAY,KAAK,UAAU,EAAE;QAC/B,OAAO;KACR;IAED,MAAM,eAAe,GAAG,IAAA,oCAAsB,EAAC,YAAY,CAAC,CAAC;IAE7D,IAAI,eAAe,KAAK,IAAI,EAAE;QAC5B,OAAO;KACR;IAED,MAAM,WAAW,GAAG,kBAAO,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;IAC1D,MAAM,gBAAgB,GACpB,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;IAErC,IAAI,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,SAAS,EAAE;QACxD,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,cAAc,EAAE;YACpD,MAAM,EAAE,WAAW,CAAC,IAAI;SACzB,CAAC,CAAC;KACJ;IAED,kEAAkE;IAClE,IAAI,gBAAgB,CAAC,OAAO,KAAK,SAAS,EAAE;QAC1C,OAAO;KACR;IAED,MAAM,uBAAuB,GAA+B,EAAE,CAAC;IAC/D,KAAK,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QACxE,MAAM,yBAAyB,GAAG,eAAe,CAC/C,cAAc,EACd,UAAU,CACX,CAAC;QACF,IAAI,yBAAyB,KAAK,SAAS,EAAE;YAC3C,uBAAuB,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;SACnD;KACF;IAED,MAAM,4BAA4B,GAAG,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAC1E,IAAI,4BAA4B,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE;YAC1D,MAAM,EAAE,WAAW,CAAC,IAAI;YACxB,mBAAmB,EAAE,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5D,2BAA2B,EAAE,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC;iBACjE,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,IAAI,OAAO,GAAG,CAAC;iBAChD,IAAI,CAAC,GAAG,CAAC;SACb,CAAC,CAAC;KACJ;AACH,CAAC;AAnED,gEAmEC;AAUD,SAAS,eAAe,CACtB,WAAmB,EACnB,UAAkB;IAElB,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAA6B,CAAC;IAE/D,IAAI;QACF,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAClC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EACtC;YACE,OAAO,EAAE,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC;SAClC,CACF,CAAC;QAEF,OAAO,OAAO,CAAC,eAAe,CAAC,CAAC;KACjC;IAAC,MAAM;QACN,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAe,EACf,EAAE,0BAA0B,EAA2C;IAEvE,IAAI,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QACpE,IAAI,OAAO,GAAG,oHAAoH,CAAC;QAEnI,4EAA4E;QAC5E,iDAAiD;QACjD,IAAI,CAAC,0BAA0B,EAAE;YAC/B,OAAO,IAAI,wEAAwE,CAAC;SACrF;QAED,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;KACrC;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,UAAe;IACjD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE;QACrC,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,gDAAgD,qCAAoB;;;CAG3E,CACM,CACF,CAAC;KACH;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,cAA6B;IACnE,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,YAAY,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAE/D,MAAM,mBAAmB,GAAa,EAAE,CAAC;IACzC,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;QACtC,IACE,CAAC,gBAAM,CAAC,SAAS,CAAC,WAAW,EAAE,4CAAgC,CAAC;YAChE,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAC1C;YACA,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACvC;KACF;IAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;QAClC,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,YAAY,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IACxC,mBAAmB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAC5C;;;CAGP,CACM,CACF,CAAC;KACH;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,EAAE,QAAQ,EAAiB;IAC7D,MAAM,WAAW,GAAG;QAClB,GAAG,QAAQ,CAAC,SAAS;QACrB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;KACrC,CAAC;IACF,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CACnC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,CACpD,CAAC;IAEF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV;;;CAGP,CACM,CACF,CAAC;KACH;AACH,CAAC;AAED,SAAgB,sBAAsB,CACpC,cAAyC;IAEzC,MAAM,gBAAgB,GAAG,cAAc,CAAC,SAAS,CAAC;IAClD,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,gBAAgB,EAAE,GAAG,gBAAgB,CAAC,CAAC;AACpD,CAAC;AAND,wDAMC"}