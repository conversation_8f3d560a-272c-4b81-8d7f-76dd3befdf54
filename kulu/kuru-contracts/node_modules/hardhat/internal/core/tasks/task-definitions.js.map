{"version": 3, "file": "task-definitions.js", "sourceRoot": "", "sources": ["../../../src/internal/core/tasks/task-definitions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,sCAAyC;AACzC,gDAAyD;AACzD,+DAAiD;AACjD,6DAAqE;AACrE,iCAA6C;AAE7C,SAAS,iBAAiB,CACxB,IAAuB;IAEvB,OAAO,OAAO,IAAI,IAAI,CAAC;AACzB,CAAC;AACD;;;;;;;GAOG;AACH,MAAa,oBAAoB;IAC/B,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACD,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACD,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAYD;;;;;;;OAOG;IACH,YACE,cAA8B,EACd,YAAqB,KAAK;QAA1B,cAAS,GAAT,SAAS,CAAiB;QArB5B,qBAAgB,GAAwB,EAAE,CAAC;QAC3C,+BAA0B,GAAgC,EAAE,CAAC;QAsB3E,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,0BAAmB,EAAC,cAAc,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YACjB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE;gBAC7D,QAAQ,EAAE,IAAI,CAAC,KAAK;aACrB,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,WAAmB;QACvC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,SAAS,CACd,MAAkC;QAElC,yEAAyE;QACzE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG;IACI,QAAQ,CACb,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB,EACtB,aAAsB,YAAY,KAAK,SAAS;QAEhD,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,OAAO,IAAI,CAAC,QAAQ,CAClB,IAAI,EACJ,WAAW,EACX,SAAS,EACT,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;aACH;YAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAChD;oBACE,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;iBACpB,CACF,CAAC;aACH;YAED,OAAO,IAAI,CAAC,QAAQ,CAClB,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;SACH;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,wCAAwC,CAC3C,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;QACF,IAAI,CAAC,yCAAyC,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG;YAC5B,IAAI;YACJ,YAAY;YACZ,IAAI;YACJ,WAAW;YACX,UAAU;YACV,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;OASG;IACI,gBAAgB,CACrB,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB;QAEtB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;OASG;IACI,OAAO,CAAC,IAAY,EAAE,WAAoB;QAC/C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG;YAC5B,IAAI;YACJ,YAAY,EAAE,KAAK;YACnB,IAAI,EAAE,KAAK,CAAC,OAAO;YACnB,WAAW;YACX,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,kBAAkB,CACvB,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB,EACtB,UAAU,GAAG,YAAY,KAAK,SAAS;QAEvC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,EACJ,WAAW,EACX,SAAS,EACT,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;aACH;YAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAChD;oBACE,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;iBACpB,CACF,CAAC;aACH;YAED,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;SACH;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,0CAA0C,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClE,IAAI,CAAC,wCAAwC,CAC3C,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;QACF,IAAI,CAAC,yCAAyC,CAAC,IAAI,CAAC,CAAC;QAErD,MAAM,UAAU,GAAG;YACjB,IAAI;YACJ,YAAY;YACZ,IAAI;YACJ,WAAW;YACX,UAAU,EAAE,KAAK;YACjB,UAAU;YACV,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAC/B,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB;QAEtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAC/B,IAAY,EACZ,WAAoB,EACpB,YAAsB,EACtB,IAAsB,EACtB,UAAU,GAAG,YAAY,KAAK,SAAS;QAEvC,IAAI,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC9D,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;SAC/B;QAED,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,OAAO,IAAI,CAAC,0BAA0B,CACpC,IAAI,EACJ,WAAW,EACX,SAAS,EACT,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;aACH;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE;gBACtC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAChD;oBACE,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;iBACpB,CACF,CAAC;aACH;YAED,OAAO,IAAI,CAAC,0BAA0B,CACpC,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,KAAK,CAAC,MAAM,EACZ,UAAU,CACX,CAAC;SACH;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,0CAA0C,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClE,IAAI,CAAC,wCAAwC,CAC3C,YAAY,EACZ,UAAU,EACV,IAAI,CACL,CAAC;QACF,IAAI,CAAC,yCAAyC,CAAC,IAAI,CAAC,CAAC;QAErD,MAAM,UAAU,GAAG;YACjB,IAAI;YACJ,YAAY;YACZ,IAAI;YACJ,WAAW;YACX,UAAU,EAAE,IAAI;YAChB,UAAU;YACV,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;OAUG;IACI,kCAAkC,CACvC,IAAY,EACZ,WAAoB,EACpB,YAAsB,EACtB,IAAsB;QAEtB,OAAO,IAAI,CAAC,0BAA0B,CACpC,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,IAAI,EACJ,IAAI,CACL,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,6BAA6B,CAAC,UAAgC;QACpE,IAAI,UAAU,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,UAAU,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;SACzC;QAED,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACK,8BAA8B,CAAC,IAAY;QACjD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE;gBACnE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;;;;OAMG;IACK,oBAAoB,CAAC,IAAY;QACvC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC/B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;gBACpE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CAAC,CAAC;SACJ;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,0CAAyB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzD,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,gCAAgC,EACxD;gBACE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CACF,CAAC;SACH;IACH,CAAC;IAED;;;OAGG;IACK,gBAAgB,CAAC,IAAY;QACnC,OAAO,CACL,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,SAAS;YACzC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CACrC,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACK,0CAA0C,CAChD,IAAY,EACZ,UAAmB;QAEnB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,2BAA2B,EAAE;YACnD,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,8BAA8B,EACtD;gBACE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CACF,CAAC;SACH;IACH,CAAC;IAEO,wBAAwB,CAAC,IAAY;QAC3C,MAAM,OAAO,GAAG,wBAAwB,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,yBAAyB,EACjD;gBACE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CACF,CAAC;SACH;IACH,CAAC;IAEO,wCAAwC,CAC9C,YAA6B,EAC7B,UAAmB,EACnB,IAAY;QAEZ,IAAI,YAAY,KAAK,SAAS,IAAI,CAAC,UAAU,EAAE;YAC7C,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAClD;gBACE,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CACF,CAAC;SACH;IACH,CAAC;IAEO,cAAc,CAAC,MAAW;QAChC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAEO,yCAAyC,CAAC,IAAuB;QACvE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAClD;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CACF,CAAC;SACH;IACH,CAAC;CACF;AA7gBD,oDA6gBC;AAED;;;;;;;;GAQG;AACH,MAAa,wBAAwB;IAInC,YACkB,oBAAoC,EACpC,YAAqB,KAAK;QAD1B,yBAAoB,GAApB,oBAAoB,CAAgB;QACpC,cAAS,GAAT,SAAS,CAAiB;QAE1C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,WAAmB;QACvC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,SAAS,CACd,MAAkC;QAElC,yEAAyE;QACzE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,IAAW,WAAW;QACpB,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACf,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC9B,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACzB,OAAO,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAW,0BAA0B;QACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,QAAQ,CACb,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB,EACtB,UAAoB;QAEpB,IAAI,UAAU,KAAK,SAAS,IAAI,CAAC,UAAU,EAAE;YAC3C,OAAO,IAAI,CAAC,2BAA2B,CACrC,oBAAM,CAAC,gBAAgB,CAAC,4BAA4B,CACrD,CAAC;SACH;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,gBAAgB,CACrB,IAAY,EACZ,WAAoB,EACpB,YAAgB,EAChB,IAAsB;QAEtB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CACxC,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,IAAI,CACL,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,kBAAkB,CACvB,KAAa,EACb,YAAqB,EACrB,aAAiB,EACjB,KAAuB,EACvB,WAAqB;QAErB,OAAO,IAAI,CAAC,2BAA2B,CACrC,oBAAM,CAAC,gBAAgB,CAAC,6BAA6B,CACtD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,0BAA0B,CAC/B,KAAa,EACb,YAAqB,EACrB,aAAiB,EACjB,KAAuB;QAEvB,OAAO,IAAI,CAAC,2BAA2B,CACrC,oBAAM,CAAC,gBAAgB,CAAC,6BAA6B,CACtD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,0BAA0B,CAC/B,KAAa,EACb,YAAqB,EACrB,aAAmB,EACnB,KAAuB,EACvB,WAAqB;QAErB,OAAO,IAAI,CAAC,2BAA2B,CACrC,oBAAM,CAAC,gBAAgB,CAAC,2BAA2B,CACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kCAAkC,CACvC,KAAa,EACb,YAAqB,EACrB,aAAmB,EACnB,KAAuB;QAEvB,OAAO,IAAI,CAAC,2BAA2B,CACrC,oBAAM,CAAC,gBAAgB,CAAC,2BAA2B,CACpD,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,IAAY,EAAE,WAAoB;QAC/C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,2BAA2B,CAAC,eAAgC;QAClE,MAAM,IAAI,qBAAY,CAAC,eAAe,EAAE;YACtC,QAAQ,EAAE,IAAI,CAAC,IAAI;SACpB,CAAC,CAAC;IACL,CAAC;CACF;AAlMD,4DAkMC;AAQD,MAAa,qBAAqB;IAGhC,YACkB,IAAY,EACpB,YAAgC,EAChC,QAAyB,EACzB,WAA4B;QAHpB,SAAI,GAAJ,IAAI,CAAQ;QACpB,iBAAY,GAAZ,YAAY,CAAoB;QAChC,aAAQ,GAAR,QAAQ,CAAiB;QACzB,gBAAW,GAAX,WAAW,CAAiB;QAN/B,UAAK,GAAa,EAAE,CAAC;IAOzB,CAAC;IAEJ,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEM,cAAc,CAAC,WAAmB;QACvC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAWM,IAAI,CACT,IAAY,EACZ,mBAAyD,EACzD,MAAmC;QAEnC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAE9D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAExB,OAAO,IAAI,CAAC;IACd,CAAC;IAWM,OAAO,CACZ,IAAY,EACZ,mBAAyD,EACzD,MAAmC;QAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAEpE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;QAE3B,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA5DD,sDA4DC"}