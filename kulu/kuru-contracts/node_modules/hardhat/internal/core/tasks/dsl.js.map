{"version": 3, "file": "dsl.js", "sourceRoot": "", "sources": ["../../../src/internal/core/tasks/dsl.ts"], "names": [], "mappings": ";;;AASA,sCAAiE;AACjE,gDAAwC;AAExC,yDAI4B;AAC5B,iCAA6C;AAE7C;;;GAGG;AACH,MAAa,QAAQ;IAArB;QACkB,iBAAY,GAAG,IAAI,CAAC,OAAO,CAAC;QAE3B,WAAM,GAAa,EAAE,CAAC;QACtB,YAAO,GAAc,EAAE,CAAC;IAqN3C,CAAC;IAnLQ,IAAI,CACT,IAAY,EACZ,mBAAyD,EACzD,MAAmC;QAEnC,iEAAiE;QACjE,wCAAwC;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAkCM,OAAO,CACZ,IAAY,EACZ,mBAAyD,EACzD,MAAmC;QAEnC,iEAAiE;QACjE,wCAAwC;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAEM,KAAK,CAAC,IAAY,EAAE,WAAoB;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YACnC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;gBAC/D,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;SACJ;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,uDAAuD;YACvD,kCAAkC;YAClC,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;aAC7C;YAED,OAAO,eAAe,CAAC;SACxB;QAED,MAAM,KAAK,GAAG,IAAI,wCAAqB,CACrC,IAAI,EACJ,WAAW,EACX,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,EAAE;QACxC,gEAAgE;QAChE,IAAI,CAAC,QAAQ,CACX,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,EAC/B,mBAAmB,EACnB,MAAM,EACN,KAAK,CACN,EACH,CAAC,WAAW,EAAE,mBAAmB,EAAE,MAAM,EAAE,EAAE;QAC3C,mEAAmE;QACnE,IAAI,CAAC,QAAQ,CACX,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,EAClC,mBAAmB,EACnB,MAAM,EACN,IAAI,CACL,CACJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE3B,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,kBAAkB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEM,iBAAiB,CACtB,KAAyB,EACzB,IAAY;QAEZ,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;SAC3C;IACH,CAAC;IAEO,QAAQ,CACd,cAA8B,EAC9B,mBAAyD,EACzD,MAAmC,EACnC,SAAmB;QAEnB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,0BAAmB,EAAC,cAAc,CAAC,CAAC;QAE5D,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YAC3D,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;gBAC/D,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;SACJ;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEjE,IAAI,cAA8B,CAAC;QAEnC,IAAI,oBAAoB,KAAK,SAAS,EAAE;YACtC,cAAc,GAAG,IAAI,2CAAwB,CAC3C,oBAAoB,EACpB,SAAS,CACV,CAAC;SACH;aAAM;YACL,cAAc,GAAG,IAAI,uCAAoB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SACtE;QAED,IAAI,mBAAmB,YAAY,QAAQ,EAAE;YAC3C,MAAM,GAAG,mBAAmB,CAAC;YAC7B,mBAAmB,GAAG,SAAS,CAAC;SACjC;QAED,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,cAAc,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;SACpD;QAED,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAClC;QAED,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;SACpC;aAAM;YACL,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAA,+BAAsB,EACpB,eAAe,KAAK,SAAS,EAC7B,yEAAyE,CAC1E,CAAC;YACF,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;SAC9C;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAzND,4BAyNC"}