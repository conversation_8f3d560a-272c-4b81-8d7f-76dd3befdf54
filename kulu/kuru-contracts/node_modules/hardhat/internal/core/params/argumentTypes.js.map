{"version": 3, "file": "argumentTypes.js", "sourceRoot": "", "sources": ["../../../src/internal/core/params/argumentTypes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,wDAA+B;AAG/B,sCAAyC;AACzC,gDAAwC;AAExC;;;;GAIG;AACU,QAAA,MAAM,GAA4B;IAC7C,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ;IACtC;;;;;;;OAOG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,cAAM,CAAC,IAAI;aAClB,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEF;;;;;GAKG;AACU,QAAA,OAAO,GAA6B;IAC/C,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC3B,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;YACrC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE;YACtC,OAAO,KAAK,CAAC;SACd;QAED,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;YAC9D,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;IACL,CAAC;IACD;;;;;;;OAOG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,MAAM,SAAS,GAAG,OAAO,KAAK,KAAK,SAAS,CAAC;QAE7C,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,eAAO,CAAC,IAAI;aACnB,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEF;;;;GAIG;AACU,QAAA,GAAG,GAA4B;IAC1C,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC3B,MAAM,cAAc,GAAG,mBAAmB,CAAC;QAC3C,MAAM,UAAU,GAAG,wBAAwB,CAAC;QAE5C,IACE,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI;YACvC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,EACnC;YACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,WAAG,CAAC,IAAI;aACf,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IACD;;;;;;;OAOG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,WAAG,CAAC,IAAI;aACf,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEF;;;;GAIG;AACU,QAAA,MAAM,GAA4B;IAC7C,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC3B,MAAM,cAAc,GAAG,aAAa,CAAC;QACrC,MAAM,UAAU,GAAG,wBAAwB,CAAC;QAE5C,IACE,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI;YACvC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,EACnC;YACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,cAAM,CAAC,IAAI;aAClB,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD;;;;;;;OAOG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC;QAC3C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,cAAM,CAAC,IAAI;aAClB,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEF;;;;GAIG;AACU,QAAA,KAAK,GAA4B;IAC5C,IAAI,EAAE,OAAO;IACb,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC3B,MAAM,cAAc,GAAG,uCAAuC,CAAC;QAC/D,MAAM,UAAU,GAAG,wBAAwB,CAAC;QAE5C,IACE,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI;YACvC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,EACnC;YACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,aAAK,CAAC,IAAI;aACjB,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IACD;;;;;;;;OAQG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,MAAM,gBAAgB,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEpE,IAAI,CAAC,gBAAgB,EAAE;YACrB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,aAAK,CAAC,IAAI;aACjB,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEF;;;;GAIG;AACU,QAAA,SAAS,GAA4B;IAChD,IAAI,EAAE,WAAW;IACjB,KAAK,CAAC,OAAe,EAAE,QAAgB;QACrC,IAAI;YACF,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;gBACvB,sDAAsD;gBACtD,sFAAsF;gBACtF,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,6BAA6B,CAAC,CAAC;aAC3D;SACF;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,SAAS,CAAC,kBAAkB,EACnC;oBACE,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,QAAQ;iBAChB,EACD,KAAK,CACN,CAAC;aACH;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;OAQG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,IAAI;YACF,iBAAS,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACd,sDAAsD;YACtD,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EACvC;oBACE,KAAK;oBACL,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,iBAAS,CAAC,IAAI;iBACrB,EACD,KAAK,CACN,CAAC;aACH;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;CACF,CAAC;AAEW,QAAA,IAAI,GAAyB;IACxC,IAAI,EAAE,MAAM;IACZ,KAAK,CAAC,OAAe,EAAE,QAAgB;QACrC,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,SAAS,CAAC,qBAAqB,EACtC;oBACE,KAAK,EAAE,OAAO;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,EACD,KAAK,CACN,CAAC;aACH;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,QAAQ,EAAE,CAAC,OAAe,EAAE,KAAU,EAAQ,EAAE;QAC9C,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,KAAK;gBACL,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,YAAI,CAAC,IAAI;aAChB,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAC;AAEW,QAAA,GAAG,GAAsB;IACpC,IAAI,EAAE,KAAK;IACX,QAAQ,CAAC,QAAgB,EAAE,cAAmB,IAAG,CAAC;CACnD,CAAC"}