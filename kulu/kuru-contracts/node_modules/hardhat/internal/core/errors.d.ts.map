{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/internal/core/errors.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,eAAe,EAAwB,MAAM,eAAe,CAAC;AAEtE,QAAA,MAAM,OAAO,eAA2C,CAAC;AAEzD,qBAAa,WAAY,SAAQ,KAAK;aAGS,MAAM,CAAC;IAFpD,OAAO,CAAC,MAAM,CAAS;gBAEX,OAAO,EAAE,MAAM,EAAkB,MAAM,CAAC,mBAAO;IAsBpD,CAAC,OAAO,CAAC,IAAI,MAAM;CAoB3B;AAED,qBAAa,YAAa,SAAQ,WAAW;WAC7B,cAAc,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,YAAY;WAMjD,kBAAkB,CAC9B,KAAK,EAAE,GAAG,EACV,UAAU,EAAE,eAAe,GAC1B,KAAK,IAAI,YAAY;IAOxB,SAAgB,eAAe,EAAE,eAAe,CAAC;IACjD,SAAgB,MAAM,EAAE,MAAM,CAAC;IAC/B,SAAgB,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAEtD,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAU;gBAGxC,eAAe,EAAE,eAAe,EAChC,gBAAgB,GAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAM,EACtD,WAAW,CAAC,EAAE,KAAK;CAkBtB;AAED;;GAEG;AACH,qBAAa,kBAAmB,SAAQ,WAAW;WACnC,oBAAoB,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,kBAAkB;IAQ3E,SAAgB,UAAU,EAAE,MAAM,CAAC;IAEnC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAU;IAEhD;;;;;;OAMG;gBACS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK;IAE/D;;;;;;;;OAQG;gBACS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK;CAkB5C;AAED,qBAAa,2BAA4B,SAAQ,kBAAkB;IAqBxD,gBAAgB;WApBX,6BAA6B,CACzC,KAAK,EAAE,GAAG,GACT,KAAK,IAAI,2BAA2B;IAQvC,OAAO,CAAC,QAAQ,CAAC,8BAA8B,CAAU;IAEzD;;;OAGG;gBAED,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,MAAM,EACf,MAAM,CAAC,EAAE,KAAK,EACP,gBAAgB,UAAQ;CAOlC;AAED;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,yBAAyB,CACvC,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE;IAAE,CAAC,WAAW,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,GACrC,MAAM,CAER;AA8DD,wBAAgB,sBAAsB,CACpC,SAAS,EAAE,OAAO,EAClB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAInB"}