{"version": 3, "file": "backwards-compatibility.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/backwards-compatibility.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AASxB,4DAA+D;AAE/D;;;;;;GAMG;AACH,MAAa,qCACX,SAAQ,mCAAmB;IAG3B,YAA6B,SAA0B;QACrD,KAAK,CAAC,SAAS,CAAC,CAAC;QADU,cAAS,GAAT,SAAS,CAAiB;QAErD,kEAAkE;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAQ,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAQ,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAQ,CAAC;IACxE,CAAC;IAEM,OAAO,CAAC,IAAsB;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,MAAc;QACxC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,SAAS,CACd,OAAuB,EACvB,QAAyD;QAEzD,cAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,OAAuB;QAEvB,MAAM,QAAQ,GAAoB;YAChC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,IAAI;YACF,QAAQ,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC5B,sFAAsF;gBACtF,MAAM,KAAK,CAAC;aACb;YAED,QAAQ,CAAC,KAAK,GAAG;gBACf,kEAAkE;gBAClE,mDAAmD;gBACnD,yEAAyE;gBACzE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB;aACF,CAAC;SACH;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA7DD,sFA6DC"}