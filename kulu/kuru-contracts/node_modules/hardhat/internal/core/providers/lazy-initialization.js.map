{"version": 3, "file": "lazy-initialization.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/lazy-initialization.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAOtC,sCAAyC;AACzC,gDAAwC;AAKxC;;;GAGG;AACH,MAAa,iCAAiC;IAK5C,YAAoB,gBAAiC;QAAjC,qBAAgB,GAAhB,gBAAgB,CAAiB;QAH7C,aAAQ,GAAiB,IAAI,qBAAY,EAAE,CAAC;IAGI,CAAC;IAEzD;;;;OAIG;IACH,IAAW,QAAQ;QACjB,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;SAC/D;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;gBAC3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACrD;YACD,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;SACjD;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,mBAAmB;IAEZ,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,MAAc;QAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,SAAS,CACd,OAAuB,EACvB,QAAyD;QAEzD,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAC5B,CAAC,QAAQ,EAAE,EAAE;YACX,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC,EACD,CAAC,CAAC,EAAE,EAAE;YACJ,QAAQ,CAAC,CAAC,EAAE,IAAW,CAAC,CAAC;QAC3B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,uBAAuB;IAEhB,WAAW,CAAC,KAAsB,EAAE,QAAuB;QAChE,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,EAAE,CAAC,KAAsB,EAAE,QAAuB;QACvD,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,IAAI,CAAC,KAAsB,EAAE,QAAkB;QACpD,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CAAC,KAAsB,EAAE,QAAkB;QAC/D,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,mBAAmB,CAAC,KAAsB,EAAE,QAAkB;QACnE,IAAI,CAAC,WAAW,EAAE,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,cAAc,CAAC,KAAsB,EAAE,QAAkB;QAC9D,IAAI,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAsB,EAAE,QAAkB;QACnD,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,kBAAkB,CAAC,KAAmC;QAC3D,IAAI,CAAC,WAAW,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CAAC,CAAS;QAC9B,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,EAAE,CAAC;IAC9C,CAAC;IAED,0DAA0D;IAC1D,wDAAwD;IACjD,SAAS,CAAC,KAAsB;QACrC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,0DAA0D;IAC1D,wDAAwD;IACjD,YAAY,CAAC,KAAsB;QACxC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAEM,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;QAChD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,CAAC;IACzC,CAAC;IAEM,aAAa,CAAC,IAAqB;QACxC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,2FAA2F;QAC3F,4DAA4D;QAC5D,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,MAAM,IAAI,CAAC,oBAAoB,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAElC,2EAA2E;YAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YAElD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;gBAClC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAe,CAAC;gBAClE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;oBAChC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;oBAClC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;iBAC/C;aACF;YAED,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;SAChE;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAhKD,8EAgKC"}