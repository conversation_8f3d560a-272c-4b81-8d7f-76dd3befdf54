{"version": 3, "file": "accounts.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/accounts.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAcnE,OAAO,EAAE,0BAA0B,EAAE,MAAM,WAAW,CAAC;AAEvD,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AAE5C,MAAM,WAAW,sBAAsB;IACrC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC3B,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;CACzB;AAED,qBAAa,qBAAsB,SAAQ,0BAA0B;IACnE,OAAO,CAAC,oBAAoB,CAAkC;gBAG5D,QAAQ,EAAE,eAAe,EACzB,2BAA2B,EAAE,MAAM,EAAE;IAO1B,OAAO,CAAC,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC;IAsJ9D,OAAO,CAAC,sBAAsB;IAiB9B,OAAO,CAAC,wBAAwB;IAchC,OAAO,CAAC,8BAA8B;YAQxB,SAAS;YAaT,qBAAqB;CAsDpC;AAED,qBAAa,gBAAiB,SAAQ,qBAAqB;gBAEvD,QAAQ,EAAE,eAAe,EACzB,QAAQ,EAAE,MAAM,EAChB,MAAM,GAAE,MAA0B,EAClC,YAAY,GAAE,MAAU,EACxB,KAAK,GAAE,MAAW,EAClB,UAAU,GAAE,MAAW;CAmB1B;AAED,uBAAe,cAAe,SAAQ,eAAe;IACtC,OAAO,CAAC,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC;IA0B9D,SAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;CAC7D;AAED,qBAAa,uBAAwB,SAAQ,cAAc;IACzD,OAAO,CAAC,aAAa,CAAqB;cAE1B,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;CAW1D;AAED,qBAAa,mBAAoB,SAAQ,cAAc;IACd,OAAO,CAAC,QAAQ,CAAC,OAAO;gBAAnD,QAAQ,EAAE,eAAe,EAAmB,OAAO,EAAE,MAAM;cAIvD,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;CAG1D"}