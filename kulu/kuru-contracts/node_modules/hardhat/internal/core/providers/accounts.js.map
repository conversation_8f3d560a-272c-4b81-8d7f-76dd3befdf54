{"version": 3, "file": "accounts.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/accounts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAE3B,yDAA6E;AAC7E,kEAA6E;AAE7E,sCAAyC;AACzC,gDAAwC;AACxC,4DAIqC;AACrC,kFAGmD;AACnD,kEAAmE;AAEnE,uCAAuD;AACvD,iCAA2C;AAC3C,uCAA4C;AAY5C,MAAa,qBAAsB,SAAQ,oCAA0B;IAGnE,YACE,QAAyB,EACzB,2BAAqC;QAErC,KAAK,CAAC,QAAQ,CAAC,CAAC;QANV,yBAAoB,GAAwB,IAAI,GAAG,EAAE,CAAC;QAQ5D,IAAI,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,MAAM,EACJ,MAAM,EACN,mBAAmB,EACnB,QAAQ,EACR,OAAO,EACP,UAAU,EAAE,WAAW,GACxB,GAAG,wDAAa,kCAAkC,GAAC,CAAC;QAErD,IACE,IAAI,CAAC,MAAM,KAAK,cAAc;YAC9B,IAAI,CAAC,MAAM,KAAK,qBAAqB,EACrC;YACA,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC;SAC9C;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,uBAAU,EAAE,oBAAO,CAAC,CAAC;gBAEpE,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;qBACnE;oBAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAC1D,MAAM,WAAW,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBAClD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;iBACxD;aACF;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACnC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,oBAAO,EAAE,uBAAU,CAAC,CAAC;gBAEpE,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,IAAI,OAAO,KAAK,SAAS,EAAE;wBACzB,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,kCAAkC,CAClD,CAAC;qBACH;oBAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAC1D,MAAM,WAAW,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBAClD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;iBACxD;aACF;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,sBAAsB,EAAE;YAC1C,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,uBAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAElE,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;aACnE;YAED,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,IAAI;oBACF,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACjC;gBAAC,MAAM;oBACN,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,wCAAwC,CACxD,CAAC;iBACH;aACF;YAED,0DAA0D;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,OAAO,IAAA,4BAAa,EAAC;oBACnB,UAAU;oBACV,OAAO,EAAE,mCAAoB,CAAC,EAAE;oBAChC,IAAI,EAAE,YAAY;iBACnB,CAAC,CAAC;aACJ;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9D,MAAM,CAAC,SAAS,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,0CAAqB,CAAC,CAAC;YAElE,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,EAAE;gBAC/B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAC;aACH;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;aACH;YAED,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC;YACrD,MAAM,gBAAgB,GACpB,SAAS,CAAC,YAAY,KAAK,SAAS;gBACpC,SAAS,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAE/C,IAAI,CAAC,WAAW,IAAI,CAAC,gBAAgB,EAAE;gBACrC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;aACjE;YAED,IAAI,WAAW,IAAI,gBAAgB,EAAE;gBACnC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;aACtE;YAED,IAAI,gBAAgB,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE;gBAC5D,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,cAAc,EAAE,CAC1B,CAAC;aACH;YAED,IAAI,gBAAgB,IAAI,SAAS,CAAC,oBAAoB,KAAK,SAAS,EAAE;gBACpE,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAClC,CAAC;aACH;YAED,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE;gBACjC,SAAS,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACxD;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,IAAK,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAEzC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACrD,SAAS,EACT,OAAO,EACP,UAAU,CACX,CAAC;YAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACnC,MAAM,EAAE,wBAAwB;gBAChC,MAAM,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;aACtC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEO,sBAAsB,CAAC,2BAAqC;QAClE,MAAM,EACJ,UAAU,EAAE,WAAW,EACvB,OAAO,EACP,gBAAgB,GACjB,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAEhD,MAAM,WAAW,GAAa,2BAA2B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAClE,OAAO,CAAC,CAAC,CAAC,CACX,CAAC;QAEF,KAAK,MAAM,EAAE,IAAI,WAAW,EAAE;YAC5B,MAAM,OAAO,GAAW,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACxE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SAC5C;IACH,CAAC;IAEO,wBAAwB,CAAC,OAAe;QAC9C,MAAM,EACJ,UAAU,EAAE,WAAW,GACxB,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE;gBACvD,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC;aAC9B,CAAC,CAAC;SACJ;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,8BAA8B,CAAC,OAAe;QACpD,IAAI;YACF,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;SAC/C;QAAC,MAAM;YACN,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAe;QACrC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,wDAClC,kCAAkC,GACnC,CAAC;QAEF,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACpD,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC;SAC1C,CAAC,CAAW,CAAC;QAEd,OAAO,IAAA,gCAAmB,EAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,kBAAyC,EACzC,OAAe,EACf,UAAkB;QAElB,MAAM,EAAE,4BAA4B,EAAE,iBAAiB,EAAE,GAAG,wDAC1D,gCAAgC,GACjC,CAAC;QAEF,MAAM,EAAE,MAAM,EAAE,GAAG,wDAAa,oCAAoC,GAAC,CAAC;QAEtE,MAAM,MAAM,GAAG;YACb,GAAG,kBAAkB;YACrB,QAAQ,EAAE,kBAAkB,CAAC,GAAG;SACjC,CAAC;QAEF,uEAAuE;QACvE,8CAA8C;QAC9C,oEAAoE;QACpE,kDAAkD;QAClD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QAE9D,yCAAyC;QACzC,4CAA4C;QAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,GAAG,CACvC,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,WAAW,CAAuB,CAC3E,CAAC;QAEF,IAAI,WAAW,CAAC;QAChB,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;YACrC,WAAW,GAAG,2CAA2B,CAAC,UAAU,CAClD;gBACE,GAAG,MAAM;gBACT,UAAU;gBACV,QAAQ,EAAE,SAAS;aACpB,EACD,EAAE,MAAM,EAAE,CACX,CAAC;SACH;aAAM,IAAI,UAAU,KAAK,SAAS,EAAE;YACnC,WAAW,GAAG,4BAA4B,CAAC,UAAU,CACnD;gBACE,GAAG,MAAM;gBACT,UAAU;aACX,EACD,EAAE,MAAM,EAAE,CACX,CAAC;SACH;aAAM;YACL,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;SAChE;QAED,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEvD,OAAO,iBAAiB,CAAC,SAAS,EAAE,CAAC;IACvC,CAAC;CACF;AA5QD,sDA4QC;AAED,MAAa,gBAAiB,SAAQ,qBAAqB;IACzD,YACE,QAAyB,EACzB,QAAgB,EAChB,SAAiB,iBAAiB,EAClC,eAAuB,CAAC,EACxB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,sFAAsF;QACtF,gFAAgF;QAChF,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,MAAM,WAAW,GAAG,IAAA,wBAAiB,EACnC,eAAe,EACf,MAAM,EACN,YAAY,EACZ,KAAK,EACL,UAAU,CACX,CAAC;QAEF,MAAM,EACJ,UAAU,EAAE,WAAW,GACxB,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACpC,CAAC;CACF;AA1BD,4CA0BC;AAED,MAAe,cAAe,SAAQ,yBAAe;IAC5C,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErC,IACE,MAAM,KAAK,qBAAqB;YAChC,MAAM,KAAK,UAAU;YACrB,MAAM,KAAK,iBAAiB,EAC5B;YACA,sCAAsC;YACtC,MAAM,EAAE,GAA2B,MAAM,CAAC,CAAC,CAAC,CAAC;YAE7C,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC7C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAE9C,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC/B,EAAE,CAAC,IAAI,GAAG,aAAa,CAAC;iBACzB;qBAAM,IAAI,MAAM,KAAK,qBAAqB,EAAE;oBAC3C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;iBACpE;aACF;SACF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CAGF;AAED,MAAa,uBAAwB,SAAQ,cAAc;IAG/C,KAAK,CAAC,UAAU;QACxB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YACpC,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACpD,MAAM,EAAE,cAAc;aACvB,CAAC,CAAa,CAAC;YAEhB,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;CACF;AAdD,0DAcC;AAED,MAAa,mBAAoB,SAAQ,cAAc;IACrD,YAAY,QAAyB,EAAmB,OAAe;QACrE,KAAK,CAAC,QAAQ,CAAC,CAAC;QADsC,YAAO,GAAP,OAAO,CAAQ;IAEvE,CAAC;IAES,KAAK,CAAC,UAAU;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF;AARD,kDAQC"}