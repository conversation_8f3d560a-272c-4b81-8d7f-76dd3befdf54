{"version": 3, "file": "debug.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/debug.ts"], "names": [], "mappings": ";;;;;;AAAA,sEAA6E;AAC7E,kDAA0B;AAE1B,mDAQyB;AACzB,mCAAmC;AACnC,uCAAmD;AACnD,iEAIgC;AAEhC,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,SAAgB,iBAAiB,CAAC,KAAmB,EAAE,KAAK,GAAG,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,IAAI,IAAA,6BAAa,EAAC,KAAK,CAAC,EAAE;QACxB,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAChC;SAAM,IAAI,IAAA,iCAAiB,EAAC,KAAK,CAAC,EAAE;QACnC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KACpC;SAAM;QACL,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAC9B;IAED,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC;AAZD,8CAYC;AAED,SAAgB,gBAAgB,CAAC,KAAyB,EAAE,KAAa;IACvE,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,cAAc,CAAC,CAAC;IAErC,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;QAChC,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,wBAAwB,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CACpH,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,UAAU,IAAA,4BAAW,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3D;SAAM;QACL,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,kCAAkC,IAAA,4BAAW,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACrE,CAAC;KACH;IAED,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE5D,IAAI,KAAK,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACxC,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,sBAAsB,IAAA,4BAAW,EAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CACrE,CAAC;KACH;IAED,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;QACxB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE1D,sFAAsF;QACtF,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,gBAAgB,IAAA,4BAAW,EAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;KACvE;IAED,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAhCD,4CAgCC;AAED,SAAgB,oBAAoB,CAClC,KAA6B,EAC7B,KAAa;IAEb,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,kBAAkB,CAAC,CAAC;IAEzC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,uBAAuB,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5D,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,cAAc,IAAA,4BAAW,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAElE,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;QACxB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KAC3D;IAED,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,gBAAgB,IAAA,4BAAW,EAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AACxE,CAAC;AAhBD,oDAgBC;AAED,SAAgB,cAAc,CAAC,KAAuB,EAAE,KAAa;IACnE,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC;IAEnC,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;QAChC,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,sBAAsB,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAClH,CAAC;KACH;SAAM;QACL,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,gCAAgC,IAAA,4BAAW,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACnE,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,cAAc,IAAA,4BAAW,EAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAClE;IAED,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5D,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,cAAc,IAAA,4BAAW,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAElE,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;QACxB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KAC3D;IAED,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,gBAAgB,IAAA,4BAAW,EAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAEtE,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAzBD,wCAyBC;AAED,SAAS,UAAU,CACjB,KAA4C,EAC5C,KAAa;IAEb,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,CAAC;IAEjD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,SAAS,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;QAC9B,IAAI,IAAA,yBAAS,EAAC,IAAI,CAAC,EAAE;YACnB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE7D,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEpD,IAAI,QAAQ,GAAW,EAAE,CAAC;gBAE1B,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;oBAC/B,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;oBAE1C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;oBACnD,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,QAAQ,IAAI,IACV,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAC5C,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;qBACjB;oBAED,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACrE;gBAED,IAAI,IAAA,gBAAM,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACvB,MAAM,IAAI,GACR,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,QAAQ;wBACjC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,gBAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;wBAC5C,CAAC,CAAC,EAAE,CAAC;oBAET,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,KAAK,EAAE,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAC9D,QAAQ,CACT,CAAC;iBACH;qBAAM,IAAI,IAAA,gBAAM,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC9B,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,KAAK,EAAE,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAA,4BAAW,EACtD,IAAI,CAAC,QAAS,CACf,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EACd,QAAQ,CACT,CAAC;iBACH;qBAAM;oBACL,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,KAAK,EAAE,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EACtD,QAAQ,CACT,CAAC;iBACH;aACF;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,KAAK,EAAE,EAAE,CAAC,CAAC;aACjC;SACF;aAAM;YACL,iBAAiB,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;SACpC;KACF;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,eAAiC;IAC/D,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO;QACL,GAAG,eAAe;QAClB,IAAI,EAAE,eAAe,CAAC,UAAU;KACjC,CAAC;AACJ,CAAC;AAED,SAAgB,eAAe,CAAC,KAAyB;IACvD,MAAM,mBAAmB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAC9C,KAAK,CAAC,IAAI,KAAK,0CAAmB,CAAC,YAAY;QAC7C,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;QACpD,CAAC,CAAC,KAAK,CACV,CAAC;IAEF,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACvD,SAAS,IAAI,KAAK;QAChB,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,IAAA,4BAAW,EAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QACnD,CAAC,CAAC,KAAK,CACV,CAAC;IAEF,MAAM,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACrD,GAAG,KAAK;QACR,IAAI,EAAE,0CAAmB,CAAC,KAAK,CAAC,IAAI,CAAC;KACtC,CAAC,CAAC,CAAC;IAEJ,MAAM,6BAA6B,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACpE,GAAG,KAAK;QACR,eAAe,EAAE,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC;KAC/D,CAAC,CAAC,CAAC;IAEJ,OAAO,CAAC,GAAG,CACT,IAAI,CAAC,SAAS,CACZ,6BAA6B,EAC7B,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EACtE,CAAC,CACF,CACF,CAAC;AACJ,CAAC;AA9BD,0CA8BC"}