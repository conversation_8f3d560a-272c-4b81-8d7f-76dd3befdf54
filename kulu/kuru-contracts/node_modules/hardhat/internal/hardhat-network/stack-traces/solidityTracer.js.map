{"version": 3, "file": "solidityTracer.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/solidityTracer.ts"], "names": [], "mappings": ";;;AAAA,sEAA+D;AAE/D,yDAAqD;AACrD,8CAA+C;AAE/C,qDAI0B;AAC1B,iHAGwD;AACxD,mDAayB;AACzB,mCAAgD;AAChD,uCAAmC;AACnC,iEAIgC;AAEhC,MAAa,cAAc;IAA3B;QACU,mBAAc,GAAG,IAAI,8BAAa,EAAE,CAAC;IAyN/C,CAAC;IAvNQ,aAAa,CAClB,wBAAsC;QAEtC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YAC5C,OAAO,EAAE,CAAC;SACX;QAED,IAAI,IAAA,iCAAiB,EAAC,wBAAwB,CAAC,EAAE;YAC/C,OAAO,IAAI,CAAC,+BAA+B,CAAC,wBAAwB,CAAC,CAAC;SACvE;QAED,IAAI,IAAA,oCAAoB,EAAC,wBAAwB,CAAC,EAAE;YAClD,OAAO,IAAI,CAAC,2BAA2B,CAAC,wBAAwB,CAAC,CAAC;SACnE;QAED,IAAI,IAAA,kCAAkB,EAAC,wBAAwB,CAAC,EAAE;YAChD,OAAO,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,CAAC;SACjE;QAED,OAAO,IAAI,CAAC,iCAAiC,CAAC,wBAAwB,CAAC,CAAC;IAC1E,CAAC;IAEO,yBAAyB,CAC/B,KAA8B;QAE9B,MAAM,aAAa,GACjB,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAE3D,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,OAAO,aAAa,CAAC;SACtB;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAEO,iCAAiC,CACvC,KAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,yFAAyF;YACzF,8DAA8D;YAC9D,IACE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvB,IAAA,6BAAW,EAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,EAClD;gBACA,IAAI,iBAA0C,CAAC;gBAE/C,IAAI,IAAA,6BAAa,EAAC,KAAK,CAAC,EAAE;oBACxB,iBAAiB,GAAG;wBAClB,IAAI,EAAE,0CAAmB,CAAC,mCAAmC;qBAC9D,CAAC;iBACH;qBAAM;oBACL,iBAAiB,GAAG;wBAClB,IAAI,EAAE,0CAAmB,CAAC,qCAAqC;wBAC/D,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC;iBACH;gBAED,OAAO,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC7D;SACF;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,eAAQ,CAAC,wBAAwB,EAAE;YACzD,OAAO;gBACL;oBACE,IAAI,EAAE,0CAAmB,CAAC,wBAAwB;iBACnD;aACF,CAAC;SACH;QAED,MAAM,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,eAAQ,CAAC,cAAc,CAAC;QAEzE,IAAI,IAAA,6BAAa,EAAC,KAAK,CAAC,EAAE;YACxB,OAAO;gBACL;oBACE,IAAI,EAAE,0CAAmB,CAAC,yBAAyB;oBACnD,OAAO,EAAE,IAAI,wBAAU,CAAC,KAAK,CAAC,UAAU,CAAC;oBACzC,oBAAoB;iBACrB;aACF,CAAC;SACH;QAED,OAAO;YACL;gBACE,IAAI,EAAE,0CAAmB,CAAC,2BAA2B;gBACrD,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,IAAI,wBAAU,CAAC,KAAK,CAAC,UAAU,CAAC;gBACzC,oBAAoB;aACrB;SACF,CAAC;IACJ,CAAC;IAEO,2BAA2B,CACjC,KAAgC;QAEhC,MAAM,aAAa,GACjB,IAAI,CAAC,cAAc,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,OAAO,aAAa,CAAC;SACtB;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAEO,+BAA+B,CACrC,KAA6B;QAE7B,OAAO;YACL;gBACE,IAAI,EAAE,0CAAmB,CAAC,gBAAgB;gBAC1C,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B;SACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,KAA6B;QAE7B,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAErD,IAAI,IAAA,8EAA+B,EAAC,UAAU,EAAE,KAAK,CAAC,EAAE;YACtD,OAAO,IAAA,+DAAgB,EAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC5C;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,qBAAqB,CAC3B,KAA6B;QAE7B,MAAM,UAAU,GAAuB,EAAE,CAAC;QAE1C,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,+DAA+D;QAC/D,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAE/B,MAAM,iBAAiB,GAAkB,EAAE,CAAC;QAE5C,IAAI,kBAA8C,CAAC;QAEnD,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YACnE,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAE5C,IAAI,IAAA,yBAAS,EAAC,IAAI,CAAC,EAAE;gBACnB,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEpD,IACE,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,aAAa;oBACxC,QAAQ,KAAK,SAAS,EACtB;oBACA,MAAM,WAAW,GAAG,QAAmB,CAAC,CAAC,yCAAyC;oBAClF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBAE/D,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAM,CAAC,QAAQ,EAAE;wBACjE,UAAU,CAAC,IAAI,CACb,IAAA,sDAAqC,EAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAC5D,CAAC;wBACF,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;4BACnC,kBAAkB,GAAG,IAAI,CAAC;yBAC3B;wBACD,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAClC;iBACF;qBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,cAAc,EAAE;oBACpD,UAAU,CAAC,GAAG,EAAE,CAAC;oBACjB,iBAAiB,CAAC,GAAG,EAAE,CAAC;iBACzB;aACF;iBAAM;gBACL,aAAa,IAAI,CAAC,CAAC;gBAEnB,uEAAuE;gBACvE,IAAI,aAAa,GAAG,KAAK,CAAC,iBAAiB,EAAE;oBAC3C,SAAS;iBACV;gBAED,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAEjD,kBAAkB,GAAG;oBACnB,YAAY,EAAE,IAAI;oBAClB,SAAS;oBACT,UAAU,EAAE,eAAe;iBAC5B,CAAC;aACH;SACF;QAED,MAAM,2BAA2B,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACvE,KAAK,EACL,UAAU,EACV,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,CACnB,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAC9C,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,KAAsB;QAC7C,IAAI,KAAK,CAAC,iBAAiB,GAAG,CAAC,EAAE;YAC/B,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/B,OAAO,IAAA,yBAAS,EAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAChC,CAAC,IAAI,CAAC,CAAC;SACR;QAED,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAiB,CAAC;IACxC,CAAC;CACF;AA1ND,wCA0NC"}