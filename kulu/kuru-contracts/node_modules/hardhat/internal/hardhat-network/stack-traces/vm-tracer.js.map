{"version": 3, "file": "vm-tracer.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/vm-tracer.ts"], "names": [], "mappings": ";;;AAOA,sEAAiE;AAEjE,8CAA2D;AAC3D,8CAAqD;AAErD,mDASyB;AAEzB,+EAA+E;AAE/E,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,MAAM,cAAc,GAAG,EAAE,CAAC;AAE1B;;;GAGG;AACH,MAAa,QAAQ;IAOnB;QANO,iBAAY,GAAkB,EAAE,CAAC;QAEhC,mBAAc,GAAmB,EAAE,CAAC;QAK1C,0FAA0F;QAC1F,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IACjC,CAAC;IAEM,2BAA2B;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC;IACvC,CAAC;IAEM,gBAAgB,CAAC,OAAuB;QAC7C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;YAC9B,OAAO;SACR;QAED,IAAI;YACF,IAAI,KAAmB,CAAC;YAExB,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;gBACvB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;aACxB;YAED,IAAI,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE;gBAC5B,MAAM,WAAW,GAAuB;oBACtC,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,IAAI,EAAE,IAAI,WAAI,CAAC,eAAQ,CAAC,OAAO,CAAC;oBAChC,UAAU,EAAE,iBAAiB;oBAC7B,iBAAiB,EAAE,CAAC;oBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,gBAAgB,EAAE,SAAS;oBAC3B,OAAO,EAAE,cAAc;iBACxB,CAAC;gBAEF,KAAK,GAAG,WAAW,CAAC;aACrB;iBAAM;gBACL,MAAM,UAAU,GAAG,IAAA,+BAAa,EAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAE7C,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC7D,MAAM,eAAe,GAA2B;wBAC9C,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;wBAC9B,QAAQ,EAAE,OAAO,CAAC,IAAI;wBACtB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,IAAI,EAAE,IAAI,WAAI,CAAC,eAAQ,CAAC,OAAO,CAAC;wBAChC,UAAU,EAAE,iBAAiB;wBAC7B,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,OAAO,EAAE,cAAc;qBACxB,CAAC;oBAEF,KAAK,GAAG,eAAe,CAAC;iBACzB;qBAAM;oBACL,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;oBAExC,0DAA0D;oBAC1D,6CAA6C;oBAC7C,IAAA,+BAAsB,EACpB,WAAW,KAAK,SAAS,EACzB,+BAA+B,CAChC,CAAC;oBACF,IAAA,+BAAsB,EACpB,OAAO,CAAC,IAAI,KAAK,SAAS,EAC1B,wBAAwB,CACzB,CAAC;oBAEF,MAAM,SAAS,GAAqB;wBAClC,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,QAAQ,EAAE,OAAO,CAAC,IAAI;wBACtB,KAAK,EAAE,EAAE;wBACT,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,IAAI,EAAE,IAAI,WAAI,CAAC,eAAQ,CAAC,OAAO,CAAC;wBAChC,UAAU,EAAE,iBAAiB;wBAC7B,OAAO,EAAE,OAAO,CAAC,EAAE;wBACnB,iBAAiB,EAAE,CAAC;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,OAAO,EAAE,cAAc;wBACvB,WAAW;qBACZ,CAAC;oBAEF,KAAK,GAAG,SAAS,CAAC;iBACnB;aACF;YAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAExE,IAAI,IAAA,iCAAiB,EAAC,WAAW,CAAC,EAAE;oBAClC,MAAM,IAAI,KAAK,CACb,oFAAoF,CACrF,CAAC;iBACH;gBAED,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,WAAW,CAAC,iBAAiB,IAAI,CAAC,CAAC;aACpC;YAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,KAAc,CAAC;SAClC;IACH,CAAC;IAEM,OAAO,CAAC,IAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;YAC9B,OAAO;SACR;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI;YACF,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAElE,IAAI,IAAA,iCAAiB,EAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;aACH;YAED,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SAC3C;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,KAAc,CAAC;SAClC;IACH,CAAC;IAEM,eAAe,CAAC,MAAuB;QAC5C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;YAC9B,OAAO;SACR;QAED,IAAI;YACF,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClE,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAEtC,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC;YACtC,IAAI,IAAA,+BAAe,EAAC,eAAe,CAAC,EAAE;gBACpC,KAAK,CAAC,IAAI,GAAG,WAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAC/D,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;gBAEtD,IAAI,IAAA,6BAAa,EAAC,KAAK,CAAC,EAAE;oBACxB,KAAK,CAAC,gBAAgB,GACpB,eAAe,CAAC,MACjB,CAAC,OAAO,CAAC;iBACX;aACF;iBAAM,IAAI,IAAA,4BAAY,EAAC,eAAe,CAAC,EAAE;gBACxC,KAAK,CAAC,IAAI,GAAG,WAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEjE,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACpC;iBAAM;gBACL,KAAK,CAAC,IAAI,GAAG,IAAI,WAAI,CAAC,eAAQ,CAAC,MAAM,CAAC,CAAC;gBAEvC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;aAC3C;YAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;aAC3B;SACF;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,KAAc,CAAC;SAClC;IACH,CAAC;CACF;AA/KD,4BA+KC"}