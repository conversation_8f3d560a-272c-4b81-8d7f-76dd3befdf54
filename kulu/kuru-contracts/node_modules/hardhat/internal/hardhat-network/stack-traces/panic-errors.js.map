{"version": 3, "file": "panic-errors.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/panic-errors.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,uBAAuB,CAAC,SAAiB;IACvD,MAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAEjD,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO,8BAA8B,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM,GAAG,CAAC;KAC3E;IAED,OAAO,sCAAsC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AACxE,CAAC;AARD,0DAQC;AAED,SAAS,sBAAsB,CAAC,SAAiB;IAC/C,QAAQ,SAAS,EAAE;QACjB,KAAK,IAAI;YACP,OAAO,iBAAiB,CAAC;QAC3B,KAAK,KAAK;YACR,OAAO,+DAA+D,CAAC;QACzE,KAAK,KAAK;YACR,OAAO,qCAAqC,CAAC;QAC/C,KAAK,KAAK;YACR,OAAO,8EAA8E,CAAC;QACxF,KAAK,KAAK;YACR,OAAO,wCAAwC,CAAC;QAClD,KAAK,KAAK;YACR,OAAO,qCAAqC,CAAC;QAC/C,KAAK,KAAK;YACR,OAAO,sDAAsD,CAAC;QAChE,KAAK,KAAK;YACR,OAAO,0EAA0E,CAAC;QACpF,KAAK,KAAK;YACR,OAAO,8DAA8D,CAAC;KACzE;AACH,CAAC"}