export declare const Int256Ty = "Int256";
export declare const Uint256Ty = "Uint256";
export declare const StringTy = "String";
export declare const BoolTy = "Bool";
export declare const AddressTy = "Address";
export declare const BytesTy = "Bytes";
export declare const Bytes1Ty = "Bytes1";
export declare const Bytes2Ty = "Bytes2";
export declare const Bytes3Ty = "Bytes3";
export declare const Bytes4Ty = "Bytes4";
export declare const Bytes5Ty = "Bytes5";
export declare const Bytes6Ty = "Bytes6";
export declare const Bytes7Ty = "Bytes7";
export declare const Bytes8Ty = "Bytes8";
export declare const Bytes9Ty = "Bytes9";
export declare const Bytes10Ty = "Bytes10";
export declare const Bytes11Ty = "Bytes11";
export declare const Bytes12Ty = "Bytes12";
export declare const Bytes13Ty = "Bytes13";
export declare const Bytes14Ty = "Bytes14";
export declare const Bytes15Ty = "Bytes15";
export declare const Bytes16Ty = "Bytes16";
export declare const Bytes17Ty = "Bytes17";
export declare const Bytes18Ty = "Bytes18";
export declare const Bytes19Ty = "Bytes19";
export declare const Bytes20Ty = "Bytes20";
export declare const Bytes21Ty = "Bytes21";
export declare const Bytes22Ty = "Bytes22";
export declare const Bytes23Ty = "Bytes23";
export declare const Bytes24Ty = "Bytes24";
export declare const Bytes25Ty = "Bytes25";
export declare const Bytes26Ty = "Bytes26";
export declare const Bytes27Ty = "Bytes27";
export declare const Bytes28Ty = "Bytes28";
export declare const Bytes29Ty = "Bytes29";
export declare const Bytes30Ty = "Bytes30";
export declare const Bytes31Ty = "Bytes31";
export declare const Bytes32Ty = "Bytes32";
/** Maps from a 4-byte function selector to a signature (argument types) */
export declare const CONSOLE_LOG_SIGNATURES: Record<number, string[]>;
//# sourceMappingURL=logger.d.ts.map