{"version": 3, "file": "compiler-to-model.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/compiler-to-model.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAQ1B,mDAGyB;AACzB,mCAUiB;AACjB,+CAAmD;AAEnD,MAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAEtC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,gDAAgD,CAAC,CAAC;AAWpE,SAAgB,8BAA8B,CAC5C,WAAmB,EACnB,aAA4B,EAC5B,cAA8B;IAE9B,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAsB,CAAC;IACzD,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEzD,yBAAyB,CACvB,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,oBAAoB,CACrB,CAAC;IAEF,MAAM,SAAS,GAAG,eAAe,CAC/B,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,oBAAoB,CACrB,CAAC;IAEF,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAE5C,OAAO,SAAS,CAAC;AACnB,CAAC;AAzBD,wEAyBC;AAED,SAAS,yBAAyB,CAChC,cAA8B,EAC9B,aAA4B,EAC5B,kBAA2C,EAC3C,oBAA2C;IAE3C,MAAM,qCAAqC,GAAG,IAAI,GAAG,EAAoB,CAAC;IAE1E,mDAAmD;IACnD,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAAoC,CAAC;IAC9E,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAClD,cAAc,CAAC,SAAS,CACzB,EAAE;QACD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;QACrD,yBAAyB,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACzD,KAAK,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAChE,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SAC/C;KACF;IAED,KAAK,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;QACzE,MAAM,aAAa,GAAG,yBAAyB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChE,MAAM,IAAI,GAAG,IAAI,kBAAU,CACzB,UAAU,EACV,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAC1C,CAAC;QAEF,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAExC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE;YACnC,IAAI,IAAI,CAAC,QAAQ,KAAK,oBAAoB,EAAE;gBAC1C,MAAM,YAAY,GAAG,0BAA0B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEnE,IAAI,YAAY,KAAK,SAAS,EAAE;oBAC9B,SAAS;iBACV;gBAED,MAAM,WAAW,GAAG,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAElD,sBAAsB,CACpB,IAAI,EACJ,IAAI,EACJ,kBAAkB,EAClB,YAAY,EACZ,oBAAoB,EACpB,qCAAqC,EACrC,WAAW,CACZ,CAAC;aACH;YAED,sBAAsB;YACtB,IAAI,IAAI,CAAC,QAAQ,KAAK,oBAAoB,EAAE;gBAC1C,gCAAgC,CAC9B,IAAI,EACJ,kBAAkB,EAClB,SAAS,EACT,IAAI,CACL,CAAC;aACH;SACF;KACF;IAED,yBAAyB,CACvB,oBAAoB,EACpB,qCAAqC,CACtC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAC7B,IAAgB,EAChB,YAAiB,EACjB,kBAA2C,EAC3C,YAA0B,EAC1B,oBAA2C,EAC3C,qCAA4D,EAC5D,WAAyB;IAEzB,MAAM,gBAAgB,GAAG,sBAAsB,CAC7C,YAAY,CAAC,GAAG,EAChB,kBAAkB,CAClB,CAAC;IAEH,MAAM,QAAQ,GAAG,IAAI,gBAAQ,CAC3B,YAAY,CAAC,IAAI,EACjB,YAAY,EACZ,gBAAgB,CACjB,CAAC;IAEF,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IACpD,qCAAqC,CAAC,GAAG,CACvC,YAAY,CAAC,EAAE,EACf,YAAY,CAAC,uBAAuB,CACrC,CAAC;IAEF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAE3B,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,KAAK,EAAE;QACrC,IAAI,IAAI,CAAC,QAAQ,KAAK,oBAAoB,EAAE;YAC1C,MAAM,YAAY,GAAG,WAAW,EAAE,MAAM,CACtC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAC1C,CAAC;YAEF,gCAAgC,CAC9B,IAAI,EACJ,kBAAkB,EAClB,QAAQ,EACR,IAAI,EACJ,YAAY,CACb,CAAC;SACH;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,oBAAoB,EAAE;YACjD,gCAAgC,CAC9B,IAAI,EACJ,kBAAkB,EAClB,QAAQ,EACR,IAAI,CACL,CAAC;SACH;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,qBAAqB,EAAE;YAClD,MAAM,SAAS,GAAG,WAAW,EAAE,IAAI,CACjC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAC1C,CAAC;YACF,iCAAiC,CAC/B,IAAI,EACJ,kBAAkB,EAClB,QAAQ,EACR,IAAI,EACJ,SAAS,CACV,CAAC;SACH;KACF;AACH,CAAC;AAED,SAAS,gCAAgC,CACvC,sBAA2B,EAC3B,kBAA2C,EAC3C,QAA8B,EAC9B,IAAgB,EAChB,YAAiC;IAEjC,IAAI,sBAAsB,CAAC,WAAW,KAAK,KAAK,EAAE;QAChD,OAAO;KACR;IAED,MAAM,YAAY,GAAG,oCAAoC,CACvD,sBAAsB,CAAC,IAAI,CAC5B,CAAC;IACF,MAAM,gBAAgB,GAAG,sBAAsB,CAC7C,sBAAsB,CAAC,GAAG,EAC1B,kBAAkB,CAClB,CAAC;IACH,MAAM,UAAU,GAAG,yBAAyB,CAC1C,sBAAsB,CAAC,UAAU,CAClC,CAAC;IAEF,IAAI,QAA4B,CAAC;IACjC,IACE,YAAY,KAAK,4BAAoB,CAAC,QAAQ;QAC9C,CAAC,UAAU,KAAK,kCAA0B,CAAC,QAAQ;YACjD,UAAU,KAAK,kCAA0B,CAAC,MAAM,CAAC,EACnD;QACA,QAAQ,GAAG,+BAA+B,CAAC,sBAAsB,CAAC,CAAC;KACpE;IAED,4DAA4D;IAC5D,MAAM,mBAAmB,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;QAC7D,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;QAED,MAAM,mBAAmB,GAAG,GAAG,CAAC,QAAQ,CACtC,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CACrD,CAAC;QAEF,IAAI,QAAQ,KAAK,SAAS,IAAI,mBAAmB,KAAK,SAAS,EAAE;YAC/D,OAAO,KAAK,CAAC;SACd;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,mBAAmB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE3E,MAAM,EAAE,GAAG,IAAI,wBAAgB,CAC7B,sBAAsB,CAAC,IAAI,EAC3B,YAAY,EACZ,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,sBAAsB,CAAC,eAAe,KAAK,SAAS,EACpD,QAAQ,EACR,UAAU,CACX,CAAC;IAEF,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;KAC/B;IAED,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,gCAAgC,CACvC,sBAA2B,EAC3B,kBAA2C,EAC3C,QAAkB,EAClB,IAAgB;IAEhB,MAAM,gBAAgB,GAAG,sBAAsB,CAC7C,sBAAsB,CAAC,GAAG,EAC1B,kBAAkB,CAClB,CAAC;IAEH,MAAM,EAAE,GAAG,IAAI,wBAAgB,CAC7B,sBAAsB,CAAC,IAAI,EAC3B,4BAAoB,CAAC,QAAQ,EAC7B,gBAAgB,EAChB,QAAQ,CACT,CAAC;IAEF,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,+CAA+C,CAAC,OAAY;IACnE,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE;QAC7B,OAAO,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACzC;IAED,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;QACvB,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;QAC3B,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,+CAA+C,CACtD,mBAAwB;IAExB,IAAI,mBAAmB,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACtD,OAAO,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;KACjE;IAED,MAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,mFAAmF;IACnF,sCAAsC;IACtC,IAAI,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;IAC5C,OAAO,IAAI,EAAE;QACX,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;YACnC,MAAM,aAAa,GAAG,+CAA+C,CACnE,QAAQ,CAAC,OAAO,CACjB,CAAC;YACF,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE/B,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;SAC/B;aAAM;YACL,IAAI,QAAQ,CAAC,QAAQ,KAAK,eAAe,EAAE;gBACzC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5B;YAED,MAAM;SACP;KACF;IAED,OAAO,GAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,iCAAiC,CACxC,uBAA4B,EAC5B,kBAA2C,EAC3C,QAAkB,EAClB,IAAgB,EAChB,SAA4B;IAE5B,MAAM,UAAU,GAAG,yBAAyB,CAC1C,uBAAuB,CAAC,UAAU,CACnC,CAAC;IAEF,8BAA8B;IAC9B,IAAI,UAAU,KAAK,kCAA0B,CAAC,MAAM,EAAE;QACpD,OAAO;KACR;IAED,MAAM,gBAAgB,GAAG,sBAAsB,CAC7C,uBAAuB,CAAC,GAAG,EAC3B,kBAAkB,CAClB,CAAC;IAEH,MAAM,UAAU,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAEjE,MAAM,EAAE,GAAG,IAAI,wBAAgB,CAC7B,uBAAuB,CAAC,IAAI,EAC5B,4BAAoB,CAAC,MAAM,EAC3B,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,KAAK,EAAE,yBAAyB;IAChC,+CAA+C,CAAC,uBAAuB,CAAC,EACxE,UAAU,CACX,CAAC;IAEF,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,yBAAyB,CAChC,oBAA2C,EAC3C,qCAA4D;IAE5D,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,EAAE;QAC5D,MAAM,cAAc,GAAG,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;QAEvE,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE;YACnC,MAAM,YAAY,GAAG,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,qDAAqD;gBACrD,SAAS;aACV;YAED,QAAQ,CAAC,6BAA6B,CAAC,YAAY,CAAC,CAAC;SACtD;KACF;AACH,CAAC;AAED,SAAS,eAAe,CACtB,WAAmB,EACnB,cAA8B,EAC9B,kBAA2C,EAC3C,oBAA2C;IAE3C,MAAM,SAAS,GAAe,EAAE,CAAC;IAEjC,KAAK,MAAM,QAAQ,IAAI,oBAAoB,CAAC,MAAM,EAAE,EAAE;QACpD,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;QACvD,MAAM,iBAAiB,GACrB,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QAC5D,MAAM,iBAAiB,GACrB,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QAE5D,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE;YACvC,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC5B,MAAM,WAAW,GAAG,mBAAW,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAEtE,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC7B,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;iBACtC;qBAAM;oBACL,GAAG,CAAC,yCAAyC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;iBAC/D;aACF;SACF;QAED,+BAA+B;QAC/B,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,KAAK,EAAE,EAAE;YAC5C,SAAS;SACV;QAED,MAAM,kBAAkB,GAAG,iBAAiB,CAC1C,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,iBAAiB,CAAC,QAAQ,EAC1B,kBAAkB,CACnB,CAAC;QAEF,MAAM,eAAe,GAAG,iBAAiB,CACvC,QAAQ,EACR,WAAW,EACX,KAAK,EACL,iBAAiB,CAAC,gBAAgB,EAClC,kBAAkB,CACnB,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACjC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,iBAAiB,CACxB,QAAkB,EAClB,WAAmB,EACnB,YAAqB,EACrB,gBAAwC,EACxC,kBAA2C;IAE3C,MAAM,uBAAuB,GAAG,IAAA,0CAA0B,EAAC,gBAAgB,CAAC,CAAC;IAE7E,MAAM,mBAAmB,GACvB,gBAAgB,CAAC,mBAAmB,KAAK,SAAS;QAChD,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,MAAM,CACxD,CAAC,aAAa,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,YAAY,CAAC,EACpE,EAAE,CACH;QACH,CAAC,CAAC,EAAE,CAAC;IAET,MAAM,cAAc,GAAG,IAAA,+CAA+B,EACpD,gBAAgB,CAAC,MAAM,EACvB,uBAAuB,CACxB,CAAC;IAEF,MAAM,YAAY,GAAG,IAAA,gCAAkB,EACrC,cAAc,EACd,gBAAgB,CAAC,SAAS,EAC1B,kBAAkB,EAClB,YAAY,CACb,CAAC;IAEF,OAAO,IAAI,gBAAQ,CACjB,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,uBAAuB,EACvB,mBAAmB,EACnB,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAC7B,GAAW,EACX,kBAA2C;IAE3C,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,IAAI,GAAG,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE5C,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,IAAI,sBAAc,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,0BAA0B,CACjC,YAAqB;IAErB,IAAI,YAAY,KAAK,SAAS,EAAE;QAC9B,OAAO,oBAAY,CAAC,OAAO,CAAC;KAC7B;IAED,IAAI,YAAY,KAAK,UAAU,EAAE;QAC/B,OAAO,oBAAY,CAAC,QAAQ,CAAC;KAC9B;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAkB;IAElB,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO,kCAA0B,CAAC,OAAO,CAAC;KAC3C;IAED,IAAI,UAAU,KAAK,UAAU,EAAE;QAC7B,OAAO,kCAA0B,CAAC,QAAQ,CAAC;KAC5C;IAED,IAAI,UAAU,KAAK,QAAQ,EAAE;QAC3B,OAAO,kCAA0B,CAAC,MAAM,CAAC;KAC1C;IAED,OAAO,kCAA0B,CAAC,QAAQ,CAAC;AAC7C,CAAC;AAED,SAAS,oCAAoC,CAC3C,IAAwB;IAExB,IAAI,IAAI,KAAK,aAAa,EAAE;QAC1B,OAAO,4BAAoB,CAAC,WAAW,CAAC;KACzC;IAED,IAAI,IAAI,KAAK,UAAU,EAAE;QACvB,OAAO,4BAAoB,CAAC,QAAQ,CAAC;KACtC;IAED,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,4BAAoB,CAAC,OAAO,CAAC;KACrC;IAED,IAAI,IAAI,KAAK,cAAc,EAAE;QAC3B,OAAO,4BAAoB,CAAC,aAAa,CAAC;KAC3C;IAED,OAAO,4BAAoB,CAAC,QAAQ,CAAC;AACvC,CAAC;AAED,SAAS,+BAA+B,CAAC,kBAAuB;IAC9D,MAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,8DAA8D;IAC9D,IAAI,kBAAkB,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACrD,OAAO,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;KAChE;IAED,KAAK,MAAM,KAAK,IAAI,kBAAkB,CAAC,UAAU,CAAC,UAAU,EAAE;QAC5D,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;YACzB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,SAAS;SACV;QAED,wCAAwC;QACxC,6DAA6D;QAC7D,8DAA8D;QAC9D,6BAA6B;QAC7B,gCAAgC;QAChC,cAAc;QACd,IAAI;QAEJ,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;YACrB,uFAAuF;YACvF,qFAAqF;YACrF,gDAAgD;YAChD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,SAAS;SACV;QAED,8EAA8E;QAC9E,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IACE,QAAQ,CAAC,QAAQ,KAAK,eAAe;YACrC,QAAQ,CAAC,QAAQ,KAAK,kBAAkB;YACxC,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAC/B;YACA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACtD,SAAS;SACV;QAED,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;KACpD;IAED,OAAO,GAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,cAAc,CAAC,KAAU;IAChC,OAAO,CACL,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,KAAK,qBAAqB;QACjD,KAAK,EAAE,QAAQ,KAAK,qBAAqB,CAAC;QAC5C,KAAK,CAAC,gBAAgB,EAAE,UAAU,KAAK,SAAS;QAChD,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAC1D,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,KAAU;IAC5B,OAAO,CACL,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,KAAK,qBAAqB;QACjD,KAAK,EAAE,QAAQ,KAAK,qBAAqB,CAAC;QAC5C,KAAK,CAAC,gBAAgB,EAAE,UAAU,KAAK,SAAS;QAChD,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CACtD,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAU;IAClC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,oBAAoB;QACnC,KAAK,CAAC,QAAQ,KAAK,oBAAoB,CACxC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAY;IACtC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC3B,OAAO,SAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KACjC;IAED,IAAI,IAAI,KAAK,KAAK,EAAE;QAClB,OAAO,QAAQ,CAAC;KACjB;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5B,OAAO,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KAClC;IAED,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC7B,OAAO,eAAe,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KACvC;IAED,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,cAAc,CAAC;KACvB;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;QAC9B,OAAO,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KACxC;IAED,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,OAAO,eAAe,CAAC;KACxB;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CACvB,SAAqB,EACrB,cAA8B;IAE9B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,IAAI,QAAQ,CAAC,YAAY,EAAE;YACzB,SAAS;SACV;QAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACnC,MAAM,iBAAiB,GACrB,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;aACvE,GAAG,CAAC,iBAAiB,CAAC;QAE3B,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACxE,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAEjD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAEpE,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,SAAS;aACV;YAED,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEvE,IAAI,CAAC,aAAa,EAAE;gBAClB,sFAAsF;gBACtF,MAAM,IAAI,KAAK,CACb,iEAAiE,QAAQ,CAAC,IAAI,IAAI,YAAY,6FAA6F,CAC5L,CAAC;aACH;SACF;KACF;AACH,CAAC"}