{"version": 3, "file": "source-maps.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/source-maps.ts"], "names": [], "mappings": ";;;AAAA,mCAA4E;AAC5E,uCAMmB;AAanB,SAAS,oBAAoB,CAAC,MAAc;IAC1C,IAAI,MAAM,KAAK,GAAG,EAAE;QAClB,OAAO,gBAAQ,CAAC,aAAa,CAAC;KAC/B;IAED,IAAI,MAAM,KAAK,GAAG,EAAE;QAClB,OAAO,gBAAQ,CAAC,cAAc,CAAC;KAChC;IACD,OAAO,gBAAQ,CAAC,QAAQ,CAAC;AAC3B,CAAC;AAED,SAAS,oBAAoB,CAAC,mBAA2B;IACvD,MAAM,QAAQ,GAAgB,EAAE,CAAC;IAEjC,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClD,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE/C,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAC5D,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAC5D,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAC5D,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAE5D,MAAM,YAAY,GAAG,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,CAAC;QAEtE,uDAAuD;QACvD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;YAC5B,QAAQ,CAAC,IAAI,CAAC;gBACZ,QAAQ,EAAE,gBAAQ,CAAC,QAAQ;gBAC3B,QAAQ,EAAE;oBACR,IAAI,EAAE,CAAC,CAAC;oBACR,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;iBACV;aACF,CAAC,CAAC;YAEH,SAAS;SACV;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,QAAQ,EAAE;gBACR,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM;gBAC/D,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM;gBAC/D,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI;aAC5D;YACD,QAAQ,EAAE,SAAS;gBACjB,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;SAC7B,CAAC,CAAC;KACJ;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,uBAAuB,CAC9B,YAA2B,EAC3B,QAAgB;IAEhB,MAAM,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,IAAI,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;IAEjC,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,gBAAM,CAAC,OAAO,EAAE;QAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpC,IAAI,QAA4B,CAAC;QAEjC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,IAAA,gBAAM,EAAC,MAAM,CAAC,EAAE;YAClB,aAAa,GAAG,IAAA,uBAAa,EAAC,MAAM,CAAC,CAAC;YACtC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;SAC3E;QAED,MAAM,QAAQ,GAAG,IAAA,gBAAM,EAAC,MAAM,CAAC;YAC7B,CAAC,CAAC,gBAAQ,CAAC,aAAa;YACxB,CAAC,CAAC,gBAAQ,CAAC,QAAQ,CAAC;QAEtB,MAAM,WAAW,GAAG,IAAI,mBAAW,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC5E,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/B,UAAU,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,CAAC;KACvC;AACH,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAgB,EAChB,oBAA4B,EAC5B,kBAA2C,EAC3C,YAAqB;IAErB,MAAM,UAAU,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;IAE9D,MAAM,YAAY,GAAkB,EAAE,CAAC;IAEvC,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,qEAAqE;IACrE,2EAA2E;IAC3E,6DAA6D;IAC7D,OAAO,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE;QAC9C,MAAM,EAAE,GAAG,UAAU,CAAC;QACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,QAA4B,CAAC;QACjC,IAAI,QAAoC,CAAC;QAEzC,MAAM,QAAQ,GACZ,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,KAAK,gBAAQ,CAAC,QAAQ;YACxD,CAAC,CAAC,gBAAQ,CAAC,aAAa;YACxB,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzB,IAAI,IAAA,gBAAM,EAAC,MAAM,CAAC,EAAE;YAClB,MAAM,MAAM,GAAG,IAAA,uBAAa,EAAC,MAAM,CAAC,CAAC;YACrC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;SACpE;QAED,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE;YAClC,MAAM,IAAI,GAAG,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE7D,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,QAAQ,GAAG,IAAI,sBAAc,CAC3B,IAAI,EACJ,SAAS,CAAC,QAAQ,CAAC,MAAM,EACzB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAC1B,CAAC;aACH;SACF;QAED,MAAM,WAAW,GAAG,IAAI,mBAAW,CACjC,EAAE,EACF,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT,CAAC;QAEF,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/B,UAAU,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,CAAC;KACvC;IAED,wDAAwD;IACxD,IAAI,YAAY,EAAE;QAChB,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;KACjD;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AA/DD,gDA+DC"}