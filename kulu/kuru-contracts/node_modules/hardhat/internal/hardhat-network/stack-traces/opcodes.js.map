{"version": 3, "file": "opcodes.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/opcodes.ts"], "names": [], "mappings": ";;;AAAA,IAAY,MAkTX;AAlTD,WAAY,MAAM;IAChB,wBAAwB;IACxB,mCAAW,CAAA;IACX,iCAAU,CAAA;IACV,iCAAU,CAAA;IACV,iCAAU,CAAA;IACV,iCAAU,CAAA;IACV,mCAAW,CAAA;IACX,iCAAU,CAAA;IACV,mCAAW,CAAA;IACX,uCAAa,CAAA;IACb,uCAAa,CAAA;IACb,kCAAU,CAAA;IACV,gDAAiB,CAAA;IAEjB,cAAc;IACd,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IAEtB,oCAAoC;IACpC,gCAAS,CAAA;IACT,gCAAS,CAAA;IACT,kCAAU,CAAA;IACV,kCAAU,CAAA;IACV,gCAAS,CAAA;IACT,wCAAa,CAAA;IACb,kCAAU,CAAA;IACV,gCAAS,CAAA;IACT,kCAAU,CAAA;IACV,kCAAU,CAAA;IACV,oCAAW,CAAA;IACX,kCAAU,CAAA;IACV,kCAAU,CAAA;IACV,kCAAU,CAAA;IAEV,cAAc;IACd,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IAEtB,2BAA2B;IAC3B,oCAAW,CAAA;IAEX,cAAc;IACd,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IAEtB,0BAA0B;IAC1B,0CAAc,CAAA;IACd,0CAAc,CAAA;IACd,wCAAa,CAAA;IACb,wCAAa,CAAA;IACb,8CAAgB,CAAA;IAChB,oDAAmB,CAAA;IACnB,oDAAmB,CAAA;IACnB,oDAAmB,CAAA;IACnB,4CAAe,CAAA;IACf,4CAAe,CAAA;IACf,4CAAe,CAAA;IACf,kDAAkB,CAAA;IAClB,kDAAkB,CAAA;IAClB,wDAAqB,CAAA;IACrB,wDAAqB,CAAA;IACrB,kDAAkB,CAAA;IAElB,wBAAwB;IACxB,8CAAgB,CAAA;IAChB,4CAAe,CAAA;IACf,8CAAgB,CAAA;IAChB,wCAAa,CAAA;IACb,gDAAiB,CAAA;IACjB,4CAAe,CAAA;IAEf,mBAAmB;IACnB,0CAAc,CAAA;IACd,kDAAkB,CAAA;IAElB,iBAAiB;IACjB,0CAAc,CAAA;IAEd,cAAc;IACd,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IAEtB,wCAAwC;IACxC,kCAAU,CAAA;IACV,sCAAY,CAAA;IACZ,wCAAa,CAAA;IACb,0CAAc,CAAA;IACd,sCAAY,CAAA;IACZ,wCAAa,CAAA;IACb,oCAAW,CAAA;IACX,sCAAY,CAAA;IACZ,gCAAS,CAAA;IACT,sCAAY,CAAA;IACZ,kCAAU,CAAA;IACV,4CAAe,CAAA;IAEf,eAAe;IACf,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IACtB,0DAAsB,CAAA;IAEtB,kBAAkB;IAClB,sCAAY,CAAA;IACZ,sCAAY,CAAA;IACZ,sCAAY,CAAA;IACZ,sCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IAEb,iBAAiB;IACjB,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IAEZ,kBAAkB;IAClB,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,uCAAY,CAAA;IACZ,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IACb,yCAAa,CAAA;IAEb,iBAAiB;IACjB,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IACX,qCAAW,CAAA;IAEX,cAAc;IACd,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,kBAAkB;IAClB,yCAAa,CAAA;IACb,qCAAW,CAAA;IACX,6CAAe,CAAA;IACf,yCAAa,CAAA;IACb,qDAAmB,CAAA;IACnB,2CAAc,CAAA;IAEd,cAAc;IACd,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,mBAAmB;IACnB,iDAAiB,CAAA;IAEjB,cAAc;IACd,2DAAsB,CAAA;IACtB,2DAAsB,CAAA;IAEtB,mBAAmB;IACnB,yCAAa,CAAA;IACb,2CAAc,CAAA;IACd,qDAAmB,CAAA;AACrB,CAAC,EAlTW,MAAM,GAAN,cAAM,KAAN,cAAM,QAkTjB;AAED,SAAgB,UAAU,CAAC,MAAc;IACvC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,wBAAwB,MAAM,GAAG,CAAC;AAC7D,CAAC;AAFD,gCAEC;AAED,SAAgB,MAAM,CAAC,MAAc;IACnC,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;AAC3D,CAAC;AAFD,wBAEC;AAED,SAAgB,MAAM,CAAC,MAAc;IACnC,OAAO,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC;AAC3D,CAAC;AAFD,wBAEC;AAED,SAAgB,aAAa,CAAC,MAAc;IAC1C,OAAO,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACnC,CAAC;AAFD,sCAEC;AAED,SAAgB,eAAe,CAAC,MAAc;IAC5C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACnB,OAAO,CAAC,CAAC;KACV;IAED,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC;AAND,0CAMC;AAED,SAAgB,MAAM,CAAC,MAAc;IACnC,OAAO,CACL,MAAM,KAAK,MAAM,CAAC,IAAI;QACtB,MAAM,KAAK,MAAM,CAAC,QAAQ;QAC1B,MAAM,KAAK,MAAM,CAAC,YAAY;QAC9B,MAAM,KAAK,MAAM,CAAC,UAAU,CAC7B,CAAC;AACJ,CAAC;AAPD,wBAOC;AAED,SAAgB,QAAQ,CAAC,MAAc;IACrC,OAAO,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC;AAC/D,CAAC;AAFD,4BAEC"}