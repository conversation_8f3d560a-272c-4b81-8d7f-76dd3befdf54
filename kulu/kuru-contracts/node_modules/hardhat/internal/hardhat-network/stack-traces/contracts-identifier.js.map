{"version": 3, "file": "contracts-identifier.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/contracts-identifier.ts"], "names": [], "mappings": ";;;AAAA,sEAA8D;AAE9D,mDAIyB;AAEzB,uCAAoD;AAEpD;;;;GAIG;AACH,MAAM,YAAY;IACT,MAAM,CAAC,cAAc,CAAC,CAAM;QACjC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;YACjC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,YAAY,IAAI,CAAC,CAAC;IAC3B,CAAC;IAMD,YAA4B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAJzB,eAAU,GAA8B,IAAI,GAAG,EAAE,CAAC;QAClD,gBAAW,GAAe,EAAE,CAAC;IAGD,CAAC;IAEtC,GAAG,CAAC,QAAkB;QAC3B,4DAA4D;QAC5D,IAAI,QAAQ,GAAiB,IAAI,CAAC;QAClC,KACE,IAAI,eAAe,GAAG,CAAC,EACvB,eAAe,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,EACjD,eAAe,IAAI,CAAC,EACpB;YACA,IAAI,eAAe,KAAK,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE;gBACtD,yFAAyF;gBACzF,4FAA4F;gBAC5F,mEAAmE;gBACnE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;gBAC1B,OAAO;aACR;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YACtD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEpC,IAAI,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,SAAS,GAAG,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9C,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aAC1C;YAED,QAAQ,GAAG,SAAS,CAAC;SACtB;IACH,CAAC;IAED;;;;OAIG;IACI,MAAM,CACX,IAAgB,EAChB,kBAA0B,CAAC;QAE3B,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE;YACjC,OAAO,SAAS,CAAC;SAClB;QAED,4DAA4D;QAC5D,IAAI,QAAQ,GAAiB,IAAI,CAAC;QAClC,OAAO,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,IAAI,CAAC,EAAE;YAC3D,IAAI,eAAe,KAAK,IAAI,CAAC,MAAM,EAAE;gBACnC,OAAO,QAAQ,CAAC,KAAK,CAAC;aACvB;YAED,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAEjE,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,OAAO,QAAQ,CAAC;aACjB;YAED,QAAQ,GAAG,SAAS,CAAC;SACtB;IACH,CAAC;CACF;AAED,MAAa,mBAAmB;IAI9B,YAA6B,eAAe,IAAI;QAAnB,iBAAY,GAAZ,YAAY,CAAO;QAHxC,UAAK,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,WAAM,GAA0B,IAAI,GAAG,EAAE,CAAC;IAEC,CAAC;IAE7C,WAAW,CAAC,QAAkB;QACnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAEM,kBAAkB,CACvB,IAAgB,EAChB,QAAiB;QAEjB,MAAM,cAAc,GAAG,IAAA,0DAA0C,EAAC,IAAI,CAAC,CAAC;QAExE,IAAI,iBAAqC,CAAC;QAC1C,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,iBAAiB,GAAG,IAAA,4BAAU,EAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAElD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,OAAO,MAAM,CAAC;aACf;SACF;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAkB,EAAE,MAAM,CAAC,CAAC;aAC7C;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CACrB,QAAiB,EACjB,IAAgB,EAChB,kBAAkB,GAAG,IAAI,EACzB,IAAI,GAAG,IAAI,CAAC,KAAK,EACjB,iBAAiB,GAAG,CAAC;QAErB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAE1D,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE;YAC9C,OAAO,YAAY,CAAC;SACrB;QAED,mFAAmF;QACnF,EAAE;QACF,uFAAuF;QACvF,kEAAkE;QAClE,EAAE;QACF,qFAAqF;QACrF,uBAAuB;QACvB,EAAE;QACF,6FAA6F;QAC7F,2FAA2F;QAC3F,EAAE;QACF,wFAAwF;QACxF,kDAAkD;QAClD,IACE,QAAQ;YACR,YAAY,CAAC,KAAK,KAAK,SAAS;YAChC,YAAY,CAAC,KAAK,CAAC,YAAY,EAC/B;YACA,OAAO,YAAY,CAAC,KAAK,CAAC;SAC3B;QAED,IAAI,kBAAkB,EAAE;YACtB,KAAK,MAAM,qBAAqB,IAAI,YAAY,CAAC,WAAW,EAAE;gBAC5D,IACE,qBAAqB,CAAC,uBAAuB,CAAC,MAAM,KAAK,CAAC;oBAC1D,qBAAqB,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EACtD;oBACA,SAAS;iBACV;gBAED,MAAM,uBAAuB,GAAG,IAAA,gCAAgB,EAC9C,IAAI,EACJ,qBAAqB,CAAC,uBAAuB,CAC9C,CAAC;gBAEF,MAAM,cAAc,GAAG,IAAA,6BAAa,EAClC,uBAAuB,EACvB,qBAAqB,CAAC,mBAAmB,CAC1C,CAAC;gBAEF,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAC3C,QAAQ,EACR,cAAc,EACd,KAAK,EACL,YAAY,EACZ,YAAY,CAAC,KAAK,GAAG,CAAC,CACvB,CAAC;gBAEF,IAAI,gBAAgB,KAAK,SAAS,EAAE;oBAClC,OAAO,gBAAgB,CAAC;iBACzB;aACF;SACF;QAED,qFAAqF;QACrF,EAAE;QACF,yFAAyF;QACzF,yFAAyF;QACzF,2DAA2D;QAC3D,EAAE;QACF,sFAAsF;QACtF,6DAA6D;QAC7D,IACE,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC;YAClD,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EACnC;YACA,OAAO,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACtE;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAgB,EAAE,QAAgB;QAC5D,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,QAAQ,GAAI;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1B,iEAAiE;YACjE,IAAI,MAAM,KAAK,gBAAM,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,gBAAM,CAAC,OAAO,EAAE;gBACjE,OAAO,IAAI,CAAC;aACb;YAED,IAAI,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,CAAC;SACjC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAhJD,kDAgJC"}