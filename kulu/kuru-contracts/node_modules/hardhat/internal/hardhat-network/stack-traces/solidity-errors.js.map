{"version": 3, "file": "solidity-errors.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/solidity-errors.ts"], "names": [], "mappings": ";;;AAAA,sEAA6E;AAE7E,iDAAyD;AACzD,iEAUgC;AAEhC,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;AAEzD,SAAgB,eAAe;IAC7B,MAAM,yBAAyB,GAAG,KAAK,CAAC,iBAAiB,CAAC;IAE1D,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAEtC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;IAC1B,MAAM,KAAK,GAAsB,KAAK,CAAC,KAAY,CAAC;IAEpD,KAAK,CAAC,iBAAiB,GAAG,yBAAyB,CAAC;IAEpD,OAAO,KAAK,CAAC;AACf,CAAC;AAXD,0CAWC;AAEM,KAAK,UAAU,gCAAgC,CACpD,CAAqB,EACrB,mBAA2B;IAE3B,MAAM,gBAAgB,GAAG,eAAe,EAAE,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAEtE,IAAI;QACF,OAAO,MAAM,CAAC,EAAE,CAAC;KAClB;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YAClC,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;QAED,sFAAsF;QACtF,MAAM,wBAAwB,CAC5B,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,UAAU,EAChB,gBAAgB,CACjB,CAAC;KACH;AACH,CAAC;AArBD,4EAqBC;AAED,SAAgB,wBAAwB,CACtC,eAAuB,EACvB,UAA8B,EAC9B,aAAiC;IAEjC,IAAI,KAAK,CAAC,iBAAiB,KAAK,SAAS,EAAE;QACzC,yDAAyD;QACzD,OAAO,CAAC,6BAA6B,CAAC,CAAC;KACxC;IAED,MAAM,yBAAyB,GAAG,KAAK,CAAC,iBAAiB,CAAC;IAC1D,KAAK,CAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACzC,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,KAAK,GAAG,aAAa,CAAC;SACvB;aAAM;YACL,kDAAkD;YAClD,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACpB;QAED,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;YAC9B,MAAM,QAAQ,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,SAAS;aACV;YAED,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACzB;QAED,OAAO,yBAA0B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF,MAAM,GAAG,GAAG,iCAAiC,CAC3C,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAClC,CAAC;IAEF,MAAM,aAAa,GAAG,IAAI,aAAa,CACrC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,EACzC,UAAU,CACX,CAAC;IAEF,kDAAkD;IAClD,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;IAE1C,KAAK,CAAC,iBAAiB,GAAG,yBAAyB,CAAC;IAEpD,OAAO,aAAa,CAAC;AACvB,CAAC;AA9CD,4DA8CC;AAED,SAAS,qBAAqB,CAC5B,eAAwC;IAExC,QAAQ,eAAe,CAAC,IAAI,EAAE;QAC5B,KAAK,0CAAmB,CAAC,4CAA4C,CAAC;QACtE,KAAK,0CAAmB,CAAC,iCAAiC;YACxD,OAAO,iCAAiC,CAAC;gBACvC,GAAG,eAAe,CAAC,eAAe;gBAClC,QAAQ,EAAE,iDAA0B;aACrC,CAAC,CAAC;QAEL,KAAK,0CAAmB,CAAC,eAAe,CAAC;QACzC,KAAK,0CAAmB,CAAC,YAAY,CAAC;QACtC,KAAK,0CAAmB,CAAC,YAAY,CAAC;QACtC,KAAK,0CAAmB,CAAC,0BAA0B,CAAC;QACpD,KAAK,0CAAmB,CAAC,oBAAoB,CAAC;QAC9C,KAAK,0CAAmB,CAAC,0BAA0B,CAAC;QACpD,KAAK,0CAAmB,CAAC,yCAAyC,CAAC;QACnE,KAAK,0CAAmB,CAAC,qBAAqB,CAAC;QAC/C,KAAK,0CAAmB,CAAC,gCAAgC,CAAC;QAC1D,KAAK,0CAAmB,CAAC,iBAAiB,CAAC;QAC3C,KAAK,0CAAmB,CAAC,yBAAyB;YAChD,OAAO,iCAAiC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAE5E,KAAK,0CAAmB,CAAC,mCAAmC;YAC1D,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,iDAA0B,EAC1B,gDAAyB,EACzB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,qCAAqC;YAC5D,OAAO,IAAI,gBAAgB,CACzB,IAAA,4BAAW,EAAC,eAAe,CAAC,OAAO,CAAC,EACpC,iDAA0B,EAC1B,4CAAqB,EACrB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,gBAAgB;YACvC,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,uBAAuB,eAAe,CAAC,UAAU,GAAG,EACpD,+CAAwB,EACxB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,yBAAyB;YAChD,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,iDAA0B,EAC1B,gDAAyB,EACzB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,2BAA2B;YAClD,OAAO,IAAI,gBAAgB,CACzB,IAAA,4BAAW,EAAC,eAAe,CAAC,OAAO,CAAC,EACpC,iDAA0B,EAC1B,4CAAqB,EACrB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,iCAAiC;YACxD,OAAO,IAAI,gBAAgB,CACzB,eAAe,CAAC,eAAe,CAAC,UAAU,EAC1C,eAAe,CAAC,eAAe,CAAC,QAAQ,EACxC,YAAY,eAAe,CAAC,EAAE,EAAE,EAChC,SAAS,CACV,CAAC;QACJ,KAAK,0CAAmB,CAAC,kCAAkC;YACzD,IAAI,eAAe,CAAC,eAAe,KAAK,SAAS,EAAE;gBACjD,OAAO,iCAAiC,CACtC,eAAe,CAAC,eAAe,CAChC,CAAC;aACH;YAED,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,iDAA0B,EAC1B,4CAAqB,EACrB,SAAS,CACV,CAAC;QAEJ,KAAK,0CAAmB,CAAC,qBAAqB,CAAC;QAC/C,KAAK,0CAAmB,CAAC,wBAAwB,CAAC;QAClD,KAAK,0CAAmB,CAAC,WAAW,CAAC;QACrC,KAAK,0CAAmB,CAAC,gCAAgC;YACvD,IAAI,eAAe,CAAC,eAAe,KAAK,SAAS,EAAE;gBACjD,OAAO,IAAI,gBAAgB,CACzB,SAAS,EACT,iDAA0B,EAC1B,4CAAqB,EACrB,SAAS,CACV,CAAC;aACH;YAED,OAAO,iCAAiC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;KAC7E;AACH,CAAC;AAED,SAAS,iCAAiC,CACxC,eAAgC;IAEhC,OAAO,IAAI,gBAAgB,CACzB,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,QAAQ,KAAK,SAAS;QACpC,CAAC,CAAC,eAAe,CAAC,QAAQ;QAC1B,CAAC,CAAC,4CAAqB,EACzB,eAAe,CAAC,IAAI,CACrB,CAAC;AACJ,CAAC;AAED,SAAS,iCAAiC,CACxC,eAAwC;IAExC,QAAQ,eAAe,CAAC,IAAI,EAAE;QAC5B,KAAK,0CAAmB,CAAC,gBAAgB;YACvC,OAAO,4CAA4C,eAAe,CAAC,UAAU,SAAS,CAAC;QAEzF,KAAK,0CAAmB,CAAC,0BAA0B;YACjD,OAAO,oEAAoE,eAAe,CAAC,KAAK,CAAC,QAAQ,CACvG,EAAE,CACH,EAAE,CAAC;QAEN,KAAK,0CAAmB,CAAC,oBAAoB;YAC3C,OAAO,qEAAqE,CAAC;QAE/E,KAAK,0CAAmB,CAAC,0BAA0B;YACjD,OAAO,oFAAoF,eAAe,CAAC,KAAK,CAAC,QAAQ,CACvH,EAAE,CACH,EAAE,CAAC;QAEN,KAAK,0CAAmB,CAAC,yCAAyC;YAChE,OAAO,iHAAiH,eAAe,CAAC,KAAK,CAAC,QAAQ,CACpJ,EAAE,CACH,EAAE,CAAC;QAEN,KAAK,0CAAmB,CAAC,4CAA4C;YACnE,OAAO,6FAA6F,CAAC;QAEvG,KAAK,0CAAmB,CAAC,iCAAiC;YACxD,OAAO,yGAAyG,CAAC;QAEnH,KAAK,0CAAmB,CAAC,qBAAqB;YAC5C,OAAO,sEAAsE,CAAC;QAEhF,KAAK,0CAAmB,CAAC,gCAAgC;YACvD,OAAO,+DAA+D,CAAC;QAEzE,KAAK,0CAAmB,CAAC,iBAAiB;YACxC,OAAO,uDAAuD,CAAC;QAEjE,KAAK,0CAAmB,CAAC,yBAAyB;YAChD,OAAO,mDAAmD,CAAC;QAE7D,KAAK,0CAAmB,CAAC,yBAAyB,CAAC;QACnD,KAAK,0CAAmB,CAAC,2BAA2B;YAClD,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE;gBAC/C,OAAO,2EAA2E,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;aAC5H;YAED,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE;gBAC/C,MAAM,OAAO,GAAG,IAAA,sCAAuB,EACrC,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,CACtC,CAAC;gBACF,OAAO,8CAA8C,OAAO,EAAE,CAAC;aAChE;YAED,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;gBACtC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CACpE,KAAK,CACN,CAAC;gBAEF,OAAO,yGAAyG,UAAU,GAAG,CAAC;aAC/H;YAED,IAAI,eAAe,CAAC,oBAAoB,EAAE;gBACxC,OAAO,2DAA2D,CAAC;aACpE;YAED,OAAO,8CAA8C,CAAC;QAExD,KAAK,0CAAmB,CAAC,YAAY;YACnC,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE;gBAC/C,OAAO,2EAA2E,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;aAC5H;YAED,IAAI,eAAe,CAAC,oBAAoB,EAAE;gBACxC,OAAO,2DAA2D,CAAC;aACpE;YAED,OAAO,8CAA8C,CAAC;QAExD,KAAK,0CAAmB,CAAC,WAAW;YAClC,MAAM,YAAY,GAAG,IAAA,sCAAuB,EAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACxE,OAAO,8CAA8C,YAAY,EAAE,CAAC;QAEtE,KAAK,0CAAmB,CAAC,YAAY;YACnC,OAAO,8CAA8C,eAAe,CAAC,OAAO,EAAE,CAAC;QAEjF,KAAK,0CAAmB,CAAC,qBAAqB;YAC5C,sCAAsC;YACtC,OAAO,6DAA6D,CAAC;QAEvE,KAAK,0CAAmB,CAAC,gCAAgC;YACvD,OAAO,0MAA0M,CAAC;QAEpN,KAAK,0CAAmB,CAAC,wBAAwB;YAC/C,OAAO,2EAA2E,CAAC;QAErF,KAAK,0CAAmB,CAAC,kCAAkC;YACzD,OAAO,oFAAoF,CAAC;KAC/F;AACH,CAAC;AAED,qEAAqE;AACrE,+EAA+E;AAC/E,gCAAgC;AAChC,MAAa,aAAc,SAAQ,KAAK;IAGtC,YAAY,OAAe,EAAE,UAA8B;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAEM,CAAC,OAAO,CAAC;QACd,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS;YAC7B,CAAC,CAAC,IAAI,CAAC,KAAK;YACZ,CAAC,CAAC,4CAA4C,CAAC;IACnD,CAAC;CACF;AAjBD,sCAiBC;AAED,MAAM,gBAAgB;IACpB,YACU,WAA+B,EAC/B,SAA6B,EAC7B,aAAiC,EACjC,KAAyB;QAHzB,gBAAW,GAAX,WAAW,CAAoB;QAC/B,cAAS,GAAT,SAAS,CAAoB;QAC7B,kBAAa,GAAb,aAAa,CAAoB;QACjC,UAAK,GAAL,KAAK,CAAoB;IAChC,CAAC;IAEG,eAAe;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa;QAClB,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,WAAW,IAAI,SAAS,CAAC;IACvC,CAAC;IAEM,WAAW;QAChB,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,eAAe;QACpB,kDAAkD;QAClD,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;SACnC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACtD,CAAC;IAEM,aAAa;QAClB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;SACnC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,WAAW;QAChB,OAAO,CAAC,CAAC;IACX,CAAC;IAEM,eAAe;QACpB,OAAO,CAAC,CAAC;IACX,CAAC;IAEM,wBAAwB;QAC7B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,OAAO;QACZ,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;IAChC,CAAC;IAEM,OAAO;QACZ,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,aAAa;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,MAAM;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,QAAQ;QACb,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,UAAU;QACf,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,aAAa;QAClB,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,wBAAwB;QAC7B,OAAO,CAAC,CAAC;IACX,CAAC;IAEM,sBAAsB;QAC3B,OAAO,CAAC,CAAC;IACX,CAAC;IAEM,QAAQ;QACb,OAAO,oBAAoB,CAAC;IAC9B,CAAC;CACF"}