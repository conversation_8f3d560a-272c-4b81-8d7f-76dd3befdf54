{"version": 3, "file": "model.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/model.ts"], "names": [], "mappings": ";;;AAAA,sEAA6E;AAE7E,wDAAoD;AAIpD,+EAA+E;AAE/E,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,+CAAQ,CAAA;IACR,yDAAa,CAAA;IACb,2DAAc,CAAA;IACd,yDAAa,CAAA;AACf,CAAC,EALW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAKnB;AAED,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,uDAAQ,CAAA;IACR,qDAAO,CAAA;AACT,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB;AAED,IAAY,oBAQX;AARD,WAAY,oBAAoB;IAC9B,6EAAW,CAAA;IACX,uEAAQ,CAAA;IACR,uEAAQ,CAAA;IACR,qEAAO,CAAA;IACP,mEAAM,CAAA;IACN,uEAAQ,CAAA;IACR,iFAAa,CAAA;AACf,CAAC,EARW,oBAAoB,GAApB,4BAAoB,KAApB,4BAAoB,QAQ/B;AAED,IAAY,0BAKX;AALD,WAAY,0BAA0B;IACpC,iFAAO,CAAA;IACP,mFAAQ,CAAA;IACR,+EAAM,CAAA;IACN,mFAAQ,CAAA;AACV,CAAC,EALW,0BAA0B,GAA1B,kCAA0B,KAA1B,kCAA0B,QAKrC;AAED,MAAa,UAAU;IAIrB,YACkB,UAAkB,EAClB,OAAe;QADf,eAAU,GAAV,UAAU,CAAQ;QAClB,YAAO,GAAP,OAAO,CAAQ;QALjB,cAAS,GAAe,EAAE,CAAC;QAC3B,cAAS,GAAuB,EAAE,CAAC;IAKhD,CAAC;IAEG,WAAW,CAAC,QAAkB;QACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAEM,WAAW,CAAC,IAAsB;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEM,qBAAqB,CAC1B,QAAwB;QAExB,+DAA+D;QAE/D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACpC,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAtCD,gCAsCC;AAED,MAAa,cAAc;IAGzB,YACkB,IAAgB,EAChB,MAAc,EACd,MAAc;QAFd,SAAI,GAAJ,IAAI,CAAY;QAChB,WAAM,GAAN,MAAM,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;IAC7B,CAAC;IAEG,qBAAqB;QAC1B,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YAEf,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;gBACvD,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;iBACjB;aACF;SACF;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEM,qBAAqB;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAEM,QAAQ,CAAC,KAAqB;QACnC,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;YAC5B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QAED,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAClE,CAAC;IAEM,MAAM,CAAC,KAAqB;QACjC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;YACxB,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAC7B,CAAC;IACJ,CAAC;CACF;AA9CD,wCA8CC;AAED,MAAa,QAAQ;IAUnB,YACkB,IAAY,EACZ,IAAkB,EAClB,QAAwB;QAFxB,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAc;QAClB,aAAQ,GAAR,QAAQ,CAAgB;QAZ1B,mBAAc,GAAuB,EAAE,CAAC;QACxC,iBAAY,GAAkB,EAAE,CAAC;QAKhC,2BAAsB,GACrC,IAAI,GAAG,EAAE,CAAC;IAMT,CAAC;IAEJ,IAAW,mBAAmB;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,gBAAgB,CAAC,IAAsB;QAC5C,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;QAED,IACE,IAAI,CAAC,UAAU,KAAK,0BAA0B,CAAC,MAAM;YACrD,IAAI,CAAC,UAAU,KAAK,0BAA0B,CAAC,QAAQ,EACvD;YACA,IACE,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC,QAAQ;gBAC3C,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC,MAAM,EACzC;gBACA,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAA,4BAAW,EAAC,IAAI,CAAC,QAAS,CAAC,EAAE,IAAI,CAAC,CAAC;aACpE;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC,WAAW,EAAE;gBACzD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC1B;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC,QAAQ,EAAE;gBACtD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC,OAAO,EAAE;gBACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;aACtB;SACF;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEM,cAAc,CAAC,WAAwB;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAEM,6BAA6B,CAAC,YAAsB;QACzD,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;YACxE,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;SACzC;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE;YACtE,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;SACvC;QAED,KAAK,MAAM,oBAAoB,IAAI,YAAY,CAAC,cAAc,EAAE;YAC9D,IACE,oBAAoB,CAAC,IAAI,KAAK,oBAAoB,CAAC,MAAM;gBACzD,oBAAoB,CAAC,IAAI,KAAK,oBAAoB,CAAC,QAAQ,EAC3D;gBACA,SAAS;aACV;YAED,IACE,oBAAoB,CAAC,UAAU,KAAK,0BAA0B,CAAC,MAAM;gBACrE,oBAAoB,CAAC,UAAU,KAAK,0BAA0B,CAAC,QAAQ,EACvE;gBACA,SAAS;aACV;YAED,MAAM,WAAW,GAAG,IAAA,4BAAW,EAAC,oBAAoB,CAAC,QAAS,CAAC,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBACjD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;aACpE;SACF;IACH,CAAC;IAEM,uBAAuB,CAC5B,QAAoB;QAEpB,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAA,4BAAW,EAAC,QAAQ,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,eAAe,CAAC,YAAoB,EAAE,QAAgB;QAC3D,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CACvE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CACjC,CAAC;QAEF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QAED,MAAM,iBAAiB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,iBAAiB,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC5C,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAChC,IAAA,4BAAW,EAAC,iBAAiB,CAAC,QAAQ,CAAC,CACxC,CAAC;SACH;QAED,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAA,4BAAW,EAAC,QAAQ,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAhID,4BAgIC;AAED,MAAa,gBAAgB;IAC3B,YACkB,IAAY,EACZ,IAA0B,EAC1B,QAAwB,EACxB,QAAmB,EACnB,UAAuC,EACvC,SAAmB,EAC5B,QAAqB,EACZ,UAAkB;QAPlB,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAsB;QAC1B,aAAQ,GAAR,QAAQ,CAAgB;QACxB,aAAQ,GAAR,QAAQ,CAAW;QACnB,eAAU,GAAV,UAAU,CAA6B;QACvC,cAAS,GAAT,SAAS,CAAU;QAC5B,aAAQ,GAAR,QAAQ,CAAa;QACZ,eAAU,GAAV,UAAU,CAAQ;QAElC,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACnE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;IACH,CAAC;IAEM,eAAe,CAAC,QAAoB;QACzC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,0EAA0E;YAC1E,OAAO,IAAI,CAAC;SACb;QAED,OAAO,wBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;CACF;AAxBD,4CAwBC;AAED,MAAa,WAAW;IACtB;;;;OAIG;IACI,MAAM,CAAC,OAAO,CAAC,IAAY,EAAE,MAAa;QAC/C,MAAM,QAAQ,GAAG,wBAAU,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE1D,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SAChD;IACH,CAAC;IAED,YACkB,QAAoB,EACpB,IAAY,EACZ,UAAiB;QAFjB,aAAQ,GAAR,QAAQ,CAAY;QACpB,SAAI,GAAJ,IAAI,CAAQ;QACZ,eAAU,GAAV,UAAU,CAAO;IAChC,CAAC;CACL;AAnBD,kCAmBC;AAED,MAAa,WAAW;IACtB,YACkB,EAAU,EACV,MAAc,EACd,QAAkB,EAClB,QAAiB,EACjB,QAAyB;QAJzB,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAAQ;QACd,aAAQ,GAAR,QAAQ,CAAU;QAClB,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAiB;IACxC,CAAC;IAEJ;;OAEG;IACI,MAAM,CAAC,KAAkB;QAC9B,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;YACxB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;YAChC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EAAE;YACpC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aACd;SACF;aAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aACd;SACF;aAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAnDD,kCAmDC;AAOD,MAAa,QAAQ;IAGnB,YACkB,QAAkB,EAClB,YAAqB,EACrB,cAAsB,EACtB,YAA2B,EAC3B,uBAAiC,EACjC,mBAAyC,EACzC,eAAuB;QANvB,aAAQ,GAAR,QAAQ,CAAU;QAClB,iBAAY,GAAZ,YAAY,CAAS;QACrB,mBAAc,GAAd,cAAc,CAAQ;QACtB,iBAAY,GAAZ,YAAY,CAAe;QAC3B,4BAAuB,GAAvB,uBAAuB,CAAU;QACjC,wBAAmB,GAAnB,mBAAmB,CAAsB;QACzC,oBAAe,GAAf,eAAe,CAAQ;QATxB,qBAAgB,GAA6B,IAAI,GAAG,EAAE,CAAC;QAWtE,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC/B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAC1C;IACH,CAAC;IAEM,cAAc,CAAC,EAAU;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,cAAc,CAAC,EAAU;QAC9B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAe;QAC3B,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC9D,OAAO,KAAK,CAAC;SACd;QAED,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9C,MAAM,QAAQ,GAAG,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjD,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;gBACnD,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAhDD,4BAgDC"}