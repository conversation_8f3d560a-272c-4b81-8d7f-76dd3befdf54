{"version": 3, "file": "mapped-inlined-internal-functions-heuristics.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/mapped-inlined-internal-functions-heuristics.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;;;;;;AAEH,oDAA4B;AAE5B,mDAKyB;AACzB,uCAAmC;AACnC,iEAGgC;AAEhC,MAAM,uDAAuD,GAAG,OAAO,CAAC;AAExE,SAAgB,+BAA+B,CAC7C,UAA8B,EAC9B,YAAoC;IAEpC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,KAAK,CAAC;KACd;IAED,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEpD,OAAO,CACL,SAAS,CAAC,IAAI,KAAK,0CAAmB,CAAC,YAAY;QACnD,CAAC,SAAS,CAAC,oBAAoB;QAC/B,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;QAC3B,gBAAM,CAAC,GAAG,CACR,YAAY,CAAC,QAAQ,CAAC,eAAe,EACrC,uDAAuD,CACxD,CACF,CAAC;AACJ,CAAC;AAnBD,0EAmBC;AAED,SAAgB,gBAAgB,CAC9B,UAA8B,EAC9B,YAAoC;IAEpC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtC,IAAI,+BAA+B,CAAC,YAAY,CAAC,EAAE;QACjD,OAAO;YACL,GAAG,KAAK;YACR;gBACE,IAAI,EAAE,0CAAmB,CAAC,gCAAgC;gBAC1D,eAAe,EAAE,MAAM,CAAC,eAAgB;aACzC;SACF,CAAC;KACH;IAED,IAAI,+BAA+B,CAAC,YAAY,CAAC,EAAE;QACjD,OAAO;YACL,GAAG,KAAK;YACR;gBACE,IAAI,EAAE,0CAAmB,CAAC,oBAAoB;gBAC9C,eAAe,EAAE,MAAM,CAAC,eAAgB;aACzC;SACF,CAAC;KACH;IAED,IAAI,wBAAwB,CAAC,YAAY,CAAC,EAAE;QAC1C,OAAO;YACL,GAAG,KAAK;YACR;gBACE,IAAI,EAAE,0CAAmB,CAAC,oBAAoB;gBAC9C,eAAe,EAAE,MAAM,CAAC,eAAgB;aACzC;SACF,CAAC;KACH;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAtCD,4CAsCC;AAED,SAAS,+BAA+B,CACtC,YAAoC;IAEpC,OAAO,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE;QACpC,gBAAM,CAAC,WAAW;QAClB,gBAAM,CAAC,MAAM;QACb,gBAAM,CAAC,IAAI;QACX,gBAAM,CAAC,MAAM;KACd,CAAC,CAAC;AACL,CAAC;AAED,SAAS,+BAA+B,CAAC,YAAoC;IAC3E,IAAI,CAAC,IAAA,oCAAoB,EAAC,YAAY,CAAC,EAAE;QACvC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAM,CAAC,QAAQ,CAAC,CAAC;QAClD,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAM,CAAC,QAAQ,CAAC,CAAC;QAClD,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,gBAAM,CAAC,EAAE,EAAE,gBAAM,CAAC,MAAM,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAAC,YAAoC;IACpE,IAAI,CAAC,IAAA,kCAAkB,EAAC,YAAY,CAAC,EAAE;QACrC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAM,CAAC,YAAY,CAAC,CAAC;QACtD,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,gBAAM,CAAC,EAAE,EAAE,gBAAM,CAAC,MAAM,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAClB,YAAoC,EACpC,SAAiB,EACjB,MAAc;IAEd,MAAM,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;IAElE,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,IAAA,yBAAS,EAAC,IAAI,CAAC,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IAED,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAElE,OAAO,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC;AACvC,CAAC;AAED,SAAS,YAAY,CACnB,YAAoC,EACpC,cAAsB,EACtB,OAAiB;IAEjB,IAAI,KAAK,GAAG,cAAc,CAAC;IAC3B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SACd;QAED,KAAK,IAAI,CAAC,CAAC;KACZ;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}