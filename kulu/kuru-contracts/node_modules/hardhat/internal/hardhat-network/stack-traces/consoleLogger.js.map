{"version": 3, "file": "consoleLogger.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/consoleLogger.ts"], "names": [], "mappings": ";;;;;;AAAA,sEAK0C;AAC1C,gDAAwB;AAExB,qCAwCkB;AAElB,MAAM,aAAa,GAAG,EAAE,CAAC;AAMzB,MAAa,aAAa;IACxB;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,QAAkB;QAC7C,MAAM,IAAI,GAAa,EAAE,CAAC;QAE1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,MAAM,GAAG,GAAG,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACtC;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,MAAM,CAAC,OAAuB,EAAE;QAC5C,OAAO,cAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAC7B,QAAgB;QAEhB,MAAM,QAAQ,GAAG,IAAA,4BAAU,EAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAErC,MAAM,QAAQ,GAAG,+BAAsB,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO;SACR;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEhE;;;;;;;;;;;;WAYG;QACH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CACrC,yBAAyB,EACzB,MAAM,CACP,CAAC;SACH;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,4FAA4F;IACpF,MAAM,CAAC,OAAO,CAAC,IAAY,EAAE,KAAe;QAClD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,MAAM,QAAQ,GAAW,CAAC,GAAG,EAAE,CAAC;YAChC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE;gBAChB,KAAK,kBAAS;oBACZ,OAAO,IAAA,+BAAa,EAClB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,aAAa,CAAC,CAC/C,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAEjB,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAU,EACf,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,aAAa,CAAC,CAC/C,CAAC,QAAQ,EAAE,CAAC;gBAEf,KAAK,eAAM;oBACT,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE;wBAC7B,OAAO,MAAM,CAAC;qBACf;oBACD,OAAO,OAAO,CAAC;gBAEjB,KAAK,iBAAQ;oBACX,MAAM,MAAM,GAAG,IAAA,4BAAU,EACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,aAAa,CAAC,CAC/C,CAAC;oBACF,MAAM,IAAI,GAAG,IAAA,4BAAU,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC;oBACpE,OAAO,IAAI;yBACR,KAAK,CAAC,MAAM,GAAG,aAAa,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;yBAC5D,QAAQ,EAAE,CAAC;gBAEhB,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAChB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,EAAE,QAAQ,GAAG,aAAa,CAAC,CACpD,CAAC;gBAEJ,KAAK,gBAAO;oBACV,MAAM,MAAM,GAAG,IAAA,4BAAU,EACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,aAAa,CAAC,CAC/C,CAAC;oBACF,MAAM,IAAI,GAAG,IAAA,4BAAU,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC;oBACpE,OAAO,IAAA,4BAAW,EAChB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,CAClE,CAAC;gBAEJ,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,iBAAQ;oBACX,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1D,KAAK,kBAAS;oBACZ,OAAO,IAAA,4BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;gBAE1D;oBACE,OAAO,EAAE,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAlLD,sCAkLC"}