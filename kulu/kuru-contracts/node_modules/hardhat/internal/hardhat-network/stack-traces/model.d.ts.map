{"version": 3, "file": "model.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/stack-traces/model.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAInC,oBAAY,QAAQ;IAClB,QAAQ,IAAA;IACR,aAAa,IAAA;IACb,cAAc,IAAA;IACd,aAAa,IAAA;CACd;AAED,oBAAY,YAAY;IACtB,QAAQ,IAAA;IACR,OAAO,IAAA;CACR;AAED,oBAAY,oBAAoB;IAC9B,WAAW,IAAA;IACX,QAAQ,IAAA;IACR,QAAQ,IAAA;IACR,OAAO,IAAA;IACP,MAAM,IAAA;IACN,QAAQ,IAAA;IACR,aAAa,IAAA;CACd;AAED,oBAAY,0BAA0B;IACpC,OAAO,IAAA;IACP,QAAQ,IAAA;IACR,MAAM,IAAA;IACN,QAAQ,IAAA;CACT;AAED,qBAAa,UAAU;aAKH,UAAU,EAAE,MAAM;aAClB,OAAO,EAAE,MAAM;IALjC,SAAgB,SAAS,EAAE,QAAQ,EAAE,CAAM;IAC3C,SAAgB,SAAS,EAAE,gBAAgB,EAAE,CAAM;gBAGjC,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,MAAM;IAG1B,WAAW,CAAC,QAAQ,EAAE,QAAQ;IAQ9B,WAAW,CAAC,IAAI,EAAE,gBAAgB;IAQlC,qBAAqB,CAC1B,QAAQ,EAAE,cAAc,GACvB,gBAAgB,GAAG,SAAS;CAWhC;AAED,qBAAa,cAAc;aAIP,IAAI,EAAE,UAAU;aAChB,MAAM,EAAE,MAAM;aACd,MAAM,EAAE,MAAM;IALhC,OAAO,CAAC,KAAK,CAAqB;gBAGhB,IAAI,EAAE,UAAU,EAChB,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM;IAGzB,qBAAqB,IAAI,MAAM;IAc/B,qBAAqB,IAAI,gBAAgB,GAAG,SAAS;IAIrD,QAAQ,CAAC,KAAK,EAAE,cAAc;IAY9B,MAAM,CAAC,KAAK,EAAE,cAAc;CAOpC;AAED,qBAAa,QAAQ;aAWD,IAAI,EAAE,MAAM;aACZ,IAAI,EAAE,YAAY;aAClB,QAAQ,EAAE,cAAc;IAZ1C,SAAgB,cAAc,EAAE,gBAAgB,EAAE,CAAM;IACxD,SAAgB,YAAY,EAAE,WAAW,EAAE,CAAM;IAEjD,OAAO,CAAC,YAAY,CAA+B;IACnD,OAAO,CAAC,SAAS,CAA+B;IAChD,OAAO,CAAC,QAAQ,CAA+B;IAC/C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAC3B;gBAGM,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,cAAc;IAG1C,IAAW,mBAAmB,IAAI,gBAAgB,GAAG,SAAS,CAE7D;IAED,IAAW,QAAQ,IAAI,gBAAgB,GAAG,SAAS,CAElD;IAED,IAAW,OAAO,IAAI,gBAAgB,GAAG,SAAS,CAEjD;IAEM,gBAAgB,CAAC,IAAI,EAAE,gBAAgB;IA0BvC,cAAc,CAAC,WAAW,EAAE,WAAW;IAIvC,6BAA6B,CAAC,YAAY,EAAE,QAAQ;IA8BpD,uBAAuB,CAC5B,QAAQ,EAAE,UAAU,GACnB,gBAAgB,GAAG,SAAS;IAI/B;;;;;;;;;;;;OAYG;IACI,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO;CAqBxE;AAED,qBAAa,gBAAgB;aAET,IAAI,EAAE,MAAM;aACZ,IAAI,EAAE,oBAAoB;aAC1B,QAAQ,EAAE,cAAc;aACxB,QAAQ,CAAC;aACT,UAAU,CAAC;aACX,SAAS,CAAC;IACnB,QAAQ,CAAC;aACA,UAAU,CAAC;gBAPX,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,oBAAoB,EAC1B,QAAQ,EAAE,cAAc,EACxB,QAAQ,CAAC,sBAAU,EACnB,UAAU,CAAC,wCAA4B,EACvC,SAAS,CAAC,qBAAS,EAC5B,QAAQ,CAAC,wBAAY,EACZ,UAAU,CAAC,mBAAO;IAO7B,eAAe,CAAC,QAAQ,EAAE,UAAU,GAAG,OAAO;CAQtD;AAED,qBAAa,WAAW;aAeJ,QAAQ,EAAE,UAAU;aACpB,IAAI,EAAE,MAAM;aACZ,UAAU,EAAE,GAAG,EAAE;IAhBnC;;;;OAIG;WACW,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,WAAW,GAAG,SAAS;IAQ3E,OAAO;CAKR;AAED,qBAAa,WAAW;aAEJ,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,MAAM;aACd,QAAQ,EAAE,QAAQ;aAClB,QAAQ,CAAC;aACT,QAAQ,CAAC;gBAJT,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,QAAQ,EAClB,QAAQ,CAAC,oBAAQ,EACjB,QAAQ,CAAC,4BAAgB;IAG3C;;OAEG;IACI,MAAM,CAAC,KAAK,EAAE,WAAW,GAAG,OAAO;CAuC3C;AAED,UAAU,kBAAkB;IAC1B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,QAAQ;aAID,QAAQ,EAAE,QAAQ;aAClB,YAAY,EAAE,OAAO;aACrB,cAAc,EAAE,MAAM;aACtB,YAAY,EAAE,WAAW,EAAE;aAC3B,uBAAuB,EAAE,MAAM,EAAE;aACjC,mBAAmB,EAAE,kBAAkB,EAAE;aACzC,eAAe,EAAE,MAAM;IATzC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAuC;gBAGtD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,OAAO,EACrB,cAAc,EAAE,MAAM,EACtB,YAAY,EAAE,WAAW,EAAE,EAC3B,uBAAuB,EAAE,MAAM,EAAE,EACjC,mBAAmB,EAAE,kBAAkB,EAAE,EACzC,eAAe,EAAE,MAAM;IAOlC,cAAc,CAAC,EAAE,EAAE,MAAM,GAAG,WAAW;IAUvC,cAAc,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO;IAI1C;;OAEG;IACI,MAAM,CAAC,KAAK,EAAE,QAAQ,GAAG,OAAO;CAcxC"}