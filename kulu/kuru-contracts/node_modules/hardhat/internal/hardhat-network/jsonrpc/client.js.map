{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/jsonrpc/client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sEAG0C;AAC1C,wDAA+B;AAC/B,yCAA2B;AAC3B,gDAAwB;AAExB,oEAI6C;AAC7C,iEAK+C;AAC/C,iGAA8F;AAC9F,6DAA6D;AAC7D,qEAAgF;AAChF,6EAA6E;AAE7E,0CAA4E;AAC5E,4CAA4C;AAE5C,MAAa,aAAa;IAGxB,YACU,aAA2B,EAC3B,UAAkB,EAClB,4BAAoC,EACpC,SAAiB,EACjB,cAAuB;QAJvB,kBAAa,GAAb,aAAa,CAAc;QAC3B,eAAU,GAAV,UAAU,CAAQ;QAClB,iCAA4B,GAA5B,4BAA4B,CAAQ;QACpC,cAAS,GAAT,SAAS,CAAQ;QACjB,mBAAc,GAAd,cAAc,CAAS;QAPzB,WAAM,GAAqB,IAAI,GAAG,EAAE,CAAC;IAQ1C,CAAC;IAEG,YAAY;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,eAAuB;QAC3D,OAAO,IAAI,CAAC,QAAQ,CAClB,wBAAwB,EACxB,CAAC,IAAA,4BAAW,EAAC,eAAe,CAAC,CAAC,EAC9B,CAAC,CAAC,MAAM,EACR,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;IACJ,CAAC;IAED,oCAAoC;IAC7B,KAAK,CAAC,YAAY,CACvB,OAAgB,EAChB,QAAgB,EAChB,WAAmB;QAEnB,OAAO,IAAI,CAAC,QAAQ,CAClB,kBAAkB,EAClB;YACE,OAAO,CAAC,QAAQ,EAAE;YAClB,IAAA,gCAAmB,EAAC,QAAQ,CAAC;YAC7B,IAAA,gCAAmB,EAAC,WAAW,CAAC;SACjC,EACD,oBAAO,EACP,GAAG,EAAE,CAAC,WAAW,CAClB,CAAC;IACJ,CAAC;IAYM,KAAK,CAAC,gBAAgB,CAC3B,WAAmB,EACnB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,mBAAmB,EAAE;YACvB,OAAO,IAAI,CAAC,QAAQ,CAClB,sBAAsB,EACtB,CAAC,IAAA,gCAAmB,EAAC,WAAW,CAAC,EAAE,IAAI,CAAC,EACxC,IAAA,gBAAQ,EAAC,gCAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,SAAS,CACtC,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAClB,sBAAsB,EACtB,CAAC,IAAA,gCAAmB,EAAC,WAAW,CAAC,EAAE,KAAK,CAAC,EACzC,IAAA,gBAAQ,EAAC,gBAAQ,CAAC,EAClB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,SAAS,CACtC,CAAC;IACJ,CAAC;IAYM,KAAK,CAAC,cAAc,CACzB,SAAiB,EACjB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,mBAAmB,EAAE;YACvB,OAAO,IAAI,CAAC,QAAQ,CAClB,oBAAoB,EACpB,CAAC,IAAA,4BAAW,EAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAC9B,IAAA,gBAAQ,EAAC,gCAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,SAAS,CACtC,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAClB,oBAAoB,EACpB,CAAC,IAAA,4BAAW,EAAC,SAAS,CAAC,EAAE,KAAK,CAAC,EAC/B,IAAA,gBAAQ,EAAC,gBAAQ,CAAC,EAClB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,SAAS,CACtC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,eAAuB;QACvD,OAAO,IAAI,CAAC,QAAQ,CAClB,0BAA0B,EAC1B,CAAC,IAAA,4BAAW,EAAC,eAAe,CAAC,CAAC,EAC9B,IAAA,gBAAQ,EAAC,4BAAc,CAAC,EACxB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,WAAW,IAAI,SAAS,CACrC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,OAAmB,EAAE,WAAmB;QACvE,OAAO,IAAI,CAAC,QAAQ,CAClB,yBAAyB,EACzB,CAAC,IAAA,4BAAW,EAAC,OAAO,CAAC,EAAE,IAAA,gCAAmB,EAAC,WAAW,CAAC,CAAC,EACxD,wBAAW,EACX,GAAG,EAAE,CAAC,WAAW,CAClB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,eAAuB;QACxD,OAAO,IAAI,CAAC,QAAQ,CAClB,2BAA2B,EAC3B,CAAC,IAAA,4BAAW,EAAC,eAAe,CAAC,CAAC,EAC9B,IAAA,gBAAQ,EAAC,+BAAqB,CAAC,EAC/B,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,WAAW,IAAI,SAAS,CACrC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,OAKpB;QACC,IAAI,OAAsC,CAAC;QAC3C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACjC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gBACtC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,4BAAW,EAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,IAAA,4BAAW,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAClC;QACD,IAAI,MAAsD,CAAC;QAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;YAChC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACpC,KAAK,KAAK,IAAI;gBACZ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,IAAA,4BAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,CAAC,CAAC,IAAI,CACT,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,EACb;YACE;gBACE,SAAS,EAAE,IAAA,gCAAmB,EAAC,OAAO,CAAC,SAAS,CAAC;gBACjD,OAAO,EAAE,IAAA,gCAAmB,EAAC,OAAO,CAAC,OAAO,CAAC;gBAC7C,OAAO;gBACP,MAAM;aACP;SACF,EACD,CAAC,CAAC,KAAK,CAAC,YAAM,EAAE,cAAc,CAAC,EAC/B,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CACtB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,OAAgB,EAChB,WAAmB;QAEnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CACtC;YACE;gBACE,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAA,gCAAmB,EAAC,WAAW,CAAC,CAAC;gBAC9D,KAAK,EAAE,oBAAO;aACf;YACD;gBACE,MAAM,EAAE,yBAAyB;gBACjC,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAA,gCAAmB,EAAC,WAAW,CAAC,CAAC;gBAC9D,KAAK,EAAE,wBAAW;aACnB;YACD;gBACE,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAA,gCAAmB,EAAC,WAAW,CAAC,CAAC;gBAC9D,KAAK,EAAE,wBAAW;aACnB;SACF,EACD,GAAG,EAAE,CAAC,WAAW,CAClB,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YAChB,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC;YAC5B,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;SACpB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAClB,iBAAiB,EACjB,EAAE,EACF,wBAAW,EACX,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAC7B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,QAAQ,CACpB,MAAc,EACd,MAAa,EACb,KAAgB,EAChB,yBAAmE;QAEnE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,OAAO,YAAY,CAAC;SACrB;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACnD,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,KAAK,CACN,CAAC;YACF,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;gBAC/C,OAAO,gBAAgB,CAAC;aACzB;SACF;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnD,MAAM,aAAa,GAAG,IAAA,6CAAqB,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;YAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAE5C,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;gBACrC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;aACxE;SACF;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,KAIE,EACF,yBAAwE;QAExE,iDAAiD;QACjD,qEAAqE;QACrE,mEAAmE;QACnE,sBAAsB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE/C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,OAAO,YAAY,CAAC;SACrB;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAC1B,CAAC;YAEF,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;gBAC/C,OAAO,gBAAgB,CAAC;aACzB;SACF;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAChD,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAClD,IAAA,6CAAqB,EAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAC9C,CAAC;QAEF,MAAM,WAAW,GAAG,yBAAyB,CAAC,cAAc,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;YAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAE7C,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;gBACrC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;aACzE;SACF;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,KAAK,CACjB,MAAc,EACd,MAAa,EACb,WAAW,GAAG,KAAK;QAEnB,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;SAC7D;QAAC,OAAO,GAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aACzC;YAED,qGAAqG;YACrG,MAAM,UAAU,GAAW,GAAG,CAAC,OAAO,CAAC;YACvC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;gBAC3D,OAAO,IAAI,CAAC;aACb;YAED,sFAAsF;YACtF,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,KAA+C,EAC/C,WAAW,GAAG,KAAK;QAEnB,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAClD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aACrC;YACD,sFAAsF;YACtF,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAEO,YAAY,CAAC,WAAoB,EAAE,GAAQ;QACjD,MAAM,UAAU,GAAW,GAAG,CAAC,OAAO,CAAC;QAEvC,MAAM,gBAAgB,GACpB,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YACvC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QAE3C,MAAM,YAAY,GAChB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAEhD,OAAO,CACL,CAAC,WAAW,IAAI,YAAY,IAAI,GAAG,YAAY,KAAK,IAAI,gBAAgB,CACzE,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,MAAc,EAAE,MAAa;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,MAAM,YAAY,GAAG,GAAG,SAAS,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAExE,MAAM,MAAM,GAAG,IAAA,gDAAyC,EACtD,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAClC,CAAC;QAEF,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAEO,iBAAiB,CAAC,KAA+C;QACvE,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;YACzB,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC;YAC3B,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACnD,CAAC;IAEO,aAAa,CAAC,QAAgB;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAEO,aAAa,CAAC,QAAgB,EAAE,aAAkB;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC3C,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,aAAqB,EACrB,QAAgB,EAChB,KAAkB;QAElB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAE3E,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,OAAO,IAAA,6CAAqB,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAChD;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,aAAqB,EACrB,QAAgB,EAChB,MAA0B;QAE1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAE5E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAA,6CAAqB,EAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,aAAqB,EACrB,QAAgB;QAEhB,IAAI;YACF,OAAO,MAAM,kBAAO,CAAC,QAAQ,CAC3B,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,QAAQ,CAAC,EACrD;gBACE,QAAQ,EAAE,MAAM;aACjB,CACF,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3B,OAAO,SAAS,CAAC;aAClB;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,aAAqB,EACrB,QAAgB,EAChB,SAAc;QAEd,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAE1E,MAAM,kBAAO,CAAC,SAAS,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACnD,MAAM,kBAAO,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,EAAE;YAC9C,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,aAAqB,EAAE,GAAW;QAChE,OAAO,cAAI,CAAC,IAAI,CACd,aAAa,EACb,WAAW,IAAI,CAAC,UAAW,EAAE,EAC7B,WAAW,GAAG,OAAO,CACtB,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,WAA+B;QAClD,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAEO,gBAAgB,CAAC,WAAmB;QAC1C,MAAM,kBAAkB,GACtB,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,SAAS,CAAC;QACrD,OAAO,WAAW,GAAG,kBAAkB,CAAC;IAC1C,CAAC;CACF;AA/cD,sCA+cC"}