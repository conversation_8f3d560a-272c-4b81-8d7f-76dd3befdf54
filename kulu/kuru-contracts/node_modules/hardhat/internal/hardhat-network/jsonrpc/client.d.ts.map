{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/jsonrpc/client.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,OAAO,EAER,MAAM,kCAAkC,CAAC;AAU1C,OAAO,EAEL,QAAQ,EAER,wBAAwB,EACzB,MAAM,uCAAuC,CAAC;AAK/C,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAIzD,qBAAa,aAAa;IAItB,OAAO,CAAC,aAAa;IACrB,OAAO,CAAC,UAAU;IAClB,OAAO,CAAC,4BAA4B;IACpC,OAAO,CAAC,SAAS;IACjB,OAAO,CAAC,cAAc,CAAC;IAPzB,OAAO,CAAC,MAAM,CAA+B;gBAGnC,aAAa,EAAE,YAAY,EAC3B,UAAU,EAAE,MAAM,EAClB,4BAA4B,EAAE,MAAM,EACpC,SAAS,EAAE,MAAM,EACjB,cAAc,CAAC,oBAAQ;IAG1B,YAAY,IAAI,MAAM;IAIhB,wBAAwB,CAAC,eAAe,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAU/D,YAAY,CACvB,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC;IAaL,gBAAgB,CAC3B,WAAW,EAAE,MAAM,EACnB,mBAAmB,CAAC,EAAE,KAAK,GAC1B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAEd,gBAAgB,CAC3B,WAAW,EAAE,MAAM,EACnB,mBAAmB,EAAE,IAAI,GACxB,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;IAuB9B,cAAc,CACzB,SAAS,EAAE,MAAM,EACjB,mBAAmB,CAAC,EAAE,KAAK,GAC1B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAEd,cAAc,CACzB,SAAS,EAAE,MAAM,EACjB,mBAAmB,EAAE,IAAI,GACxB,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;IAuB9B,oBAAoB,CAAC,eAAe,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;IAS5C,mBAAmB,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM;IAS5D,qBAAqB,CAAC,eAAe,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;IAS7C,OAAO,CAAC,OAAO,EAAE;QAC5B,SAAS,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,CAAC,EAAE,UAAU,GAAG,UAAU,EAAE,CAAC;QACpC,MAAM,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACjD;;;;;;;;;;IA+BY,cAAc,CACzB,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,gBAAgB,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,CAAC;IA6B1D,oBAAoB,IAAI,OAAO,CAAC,MAAM,CAAC;YAStC,QAAQ;YAwCR,aAAa;YAiDb,KAAK;YAuBL,UAAU;IAexB,OAAO,CAAC,YAAY;IAgBpB,OAAO,CAAC,YAAY;IAWpB,OAAO,CAAC,iBAAiB;IAYzB,OAAO,CAAC,aAAa;IAIrB,OAAO,CAAC,aAAa;YAIP,iBAAiB;YAYjB,sBAAsB;YActB,oBAAoB;YAqBpB,iBAAiB;IAa/B,OAAO,CAAC,uBAAuB;IAQ/B,OAAO,CAAC,YAAY;IAQpB,OAAO,CAAC,gBAAgB;CAKzB"}