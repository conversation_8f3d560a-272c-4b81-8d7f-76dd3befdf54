{"version": 3, "file": "provider.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/provider/provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,kDAA0B;AAC1B,kDAA0B;AAC1B,mCAAsC;AACtC,wDAA+B;AAC/B,yCAA2B;AAC3B,oDAA4B;AAE5B,qDAA8D;AAC9D,+CAGyB;AACzB,8DAG6C;AAC7C,0EAA2E;AAC3E,wDAIqC;AACrC,oDAA4D;AAC5D,oDAAuD;AACvD,yEAAmF;AACnF,iEAA8D;AAC9D,+EAA2E;AAC3E,uEAG0C;AAC1C,yDAAyE;AACzE,qEAA2E;AAE3E,mEAAgE;AAChE,yDAAqD;AAErD,wDAAwD;AASxD,uDAQ8B;AAC9B,mDAAgD;AAChD,6CAA4E;AAC5E,gDAA8E;AAE9E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,uCAAuC,CAAC,CAAC;AAE3D,+EAA+E;AAElE,QAAA,gBAAgB,GAAG,4CAA4C,CAAC;AAC7E,IAAI,iBAAyC,CAAC;AAE9C,0CAA0C;AAC1C,SAAgB,mBAAmB;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,6BAAmB,EACxC,sBAAsB,CACkB,CAAC;IAE3C,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,+BAA+B;QAC/B,iBAAiB,GAAG,IAAI,UAAU,EAAE,CAAC;KACtC;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAXD,kDAWC;AA4BD,SAAgB,aAAa,CAC3B,MAAoC,EACpC,aAA6B;IAE7B,OAAO;QACL,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,0BAA0B,EAAE,MAAM,CAAC,0BAA0B;QAC7D,aAAa;QACb,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;QACjD,YAAY,EAAE,MAAM,CAAC,YAAY;QACjC,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;QAC7B,aAAa,EACX,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;QACpE,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,wBAAgB;QAC7C,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,4BAA4B,EAAE,MAAM,CAAC,4BAA4B;QACjE,sBAAsB,EAAE,MAAM,CAAC,sBAAsB;KACtD,CAAC;AACJ,CAAC;AAzBD,sCAyBC;AAED,MAAM,uBAAwB,SAAQ,qBAAY;CAAG;AASrD,MAAa,kBACX,SAAQ,qBAAY;IAWpB,YACmB,SAAuB;IACxC,8EAA8E;IAC7D,KAEhB,EACgB,eAA+B;IAChD,8GAA8G;IAC7F,OAAe,EAChC,aAA6B;QAE7B,KAAK,EAAE,CAAC;QAVS,cAAS,GAAT,SAAS,CAAc;QAEvB,UAAK,GAAL,KAAK,CAErB;QACgB,oBAAe,GAAf,eAAe,CAAgB;QAE/B,YAAO,GAAP,OAAO,CAAQ;QAhB1B,uBAAkB,GAAG,CAAC,CAAC;QAqB7B,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,IAAA,2CAAwB,EAAC,IAAI,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;SAC/D;IACH,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,MAAM,CACxB,MAAoC,EACpC,YAA0B,EAC1B,aAA6B;QAE7B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAA,6BAAmB,EACtC,sBAAsB,CACkB,CAAC;QAE3C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,wBAAgB,CAAC;QAErD,IAAI,IAAI,CAAC;QACT,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,IAAI,GAAG;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,UAAU;gBACxC,WAAW,EACT,MAAM,CAAC,UAAU,CAAC,WAAW,KAAK,SAAS;oBACzC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;oBACvC,CAAC,CAAC,SAAS;aAChB,CAAC;SACH;QAED,MAAM,WAAW,GACf,MAAM,CAAC,WAAW,KAAK,SAAS;YAC9B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC,SAAS,CAAC;QAEhB,4EAA4E;QAC5E,0DAA0D;QAC1D,MAAM,YAAY,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEnD,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,IAAI,kBAAS,CAAC;QAC1D,MAAM,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,IAAI,wBAAe,CAAC;QAE5E,MAAM,mBAAmB,GAAG,IAAI,0CAAmB,EAAE,CAAC;QACtD,MAAM,cAAc,GAAG,IAAI,iCAAc,CAAC,mBAAmB,CAAC,CAAC;QAE/D,MAAM,YAAY,GAAG,IAAA,2BAAe,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEtD,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,UAAU,CACxC,mBAAmB,EAAE,EACrB;YACE,4BAA4B,EAC1B,MAAM,CAAC,4BAA4B,IAAI,KAAK;YAC9C,0BAA0B,EAAE,MAAM,CAAC,0BAA0B;YAC7D,iBAAiB,EAAE,MAAM,CAAC,mBAAmB;YAC7C,wBAAwB,EAAE,MAAM,CAAC,0BAA0B;YAC3D,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAC3C,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAC/B,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,EAAE;gBAC9D,OAAO;oBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;oBACxB,SAAS,EAAE,KAAK,CAAC,IAAI,CACnB,cAAc,CAAC,eAAe,EAC9B,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE;wBAC1B,OAAO;4BACL,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC;4BAChC,MAAM,EAAE,IAAA,6CAA8B,EACpC,IAAA,2BAAe,EAAC,QAAQ,CAAC,CAC1B;yBACF,CAAC;oBACJ,CAAC,CACF;iBACF,CAAC;YACJ,CAAC,CAAC;YACF,QAAQ,EAAE,MAAM,CAAC,aAAa;YAC9B,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;YAC/C,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,IAAI;YACJ,QAAQ,EAAE,IAAA,6CAA8B,EAAC,YAAY,CAAC;YACtD,eAAe,EAAE,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBACtD,OAAO;oBACL,SAAS,EAAE,OAAO,CAAC,UAAU;oBAC7B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;iBACjC,CAAC;YACJ,CAAC,CAAC;YACF,WAAW;YACX,oBAAoB,EAClB,MAAM,CAAC,oBAAoB,KAAK,SAAS;gBACvC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAqB,CAAC;gBACtC,CAAC,CAAC,SAAS;YACf,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,MAAM,EAAE;gBACN,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,IAAA,kDAAmC,EAAC,MAAM,CAAC,cAAc,CAAC;gBACpE,OAAO,EAAE;oBACP,KAAK,EAAE,IAAA,sDAAuC,EAAC,MAAM,CAAC,YAAY,CAAC;iBACpE;aACF;YACD,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;SACpC,EACD;YACE,MAAM,EAAE,YAAY,CAAC,OAAO;YAC5B,8BAA8B,EAAE,6BAAa,CAAC,cAAc;YAC5D,kCAAkC,EAAE,CAClC,IAAY,EACZ,QAAiB,EACjB,EAAE;gBACF,OAAO,cAAc,CAAC,kCAAkC,CACtD,IAAI,EACJ,QAAQ,CACT,CAAC;YACJ,CAAC;YACD,iBAAiB,EAAE,CAAC,OAAe,EAAE,OAAgB,EAAE,EAAE;gBACvD,IAAI,OAAO,EAAE;oBACX,iBAAiB,CAAC,OAAO,CAAC,CAAC;iBAC5B;qBAAM;oBACL,WAAW,CAAC,OAAO,CAAC,CAAC;iBACtB;YACH,CAAC;SACF,EACD,CAAC,KAAwB,EAAE,EAAE;YAC3B,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;QAEF,MAAM,qBAAqB,GAAG;YAC5B,GAAG,EAAE,IAAA,mCAAsB,EAAC,QAAQ,CAAC;SACtC,CAAC;QAEF,MAAM,MAAM,GAAG,IAAA,uBAAU,EAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,kBAAkB,CACpC,QAAQ,EACR,qBAAqB,EACrB,cAAc,EACd,MAAM,EACN,aAAa,CACd,CAAC;QAEF,4CAA4C;QAC5C,YAAY,CAAC,WAAW,CACtB,UAAU,EACV,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CACxC,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC5D,MAAM,IAAI,0BAAiB,CACzB,mEAAmE,CACpE,CAAC;SACH;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,KAAK,8BAA8B,EAAE;YAClD,OAAO,IAAI,CAAC,2BAA2B,CACrC,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAC5C,CAAC;SACH;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,oCAAoC,EAAE;YAC/D,OAAO,IAAI,CAAC,iCAAiC,CAC3C,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAClD,CAAC;SACH;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,cAAc,GAAa,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CACjE,eAAe,CAChB,CAAC;QAEF,IAAI,QAAQ,CAAC;QACb,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC3C,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC;SAChC;QAED,MAAM,WAAW,GACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,CAAC;YAC7C,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;QAE/B,IAAI,WAAW,EAAE;YACf,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC;YACxC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAE/B,iBAAiB;gBACjB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACxC;gBAED,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE;oBAC7B,aAAa;oBACb,IAAI,IAAI,IAAI,SAAS,EAAE;wBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;4BACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAC5B,MAAM,EACN,IAAA,qDAAsC,EAAC,SAAS,CAAC,CAClD,CAAC;yBACH;wBAED,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;qBACpC;oBACD,qBAAqB;yBAChB,IAAI,iBAAiB,IAAI,SAAS,EAAE;wBACvC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;4BAC/D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAC5B,cAAc,EACd,IAAA,wDAAyC,EAAC,SAAS,CAAC,CACrD,CAAC;yBACH;wBAED,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;qBAC5D;oBACD,sBAAsB;yBACjB;wBACH,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;4BAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAC5B,eAAe,EACf,IAAA,gDAAiC,EAAC,SAAS,CAAC,CAC7C,CAAC;yBACH;wBAED,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;qBAC7C;iBACF;gBAED,gBAAgB;gBAChB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;oBACtD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACvC;aACF;SACF;QAED,IAAI,IAAA,sBAAe,EAAC,QAAQ,CAAC,EAAE;YAC7B,IAAI,KAAK,CAAC;YAEV,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;YACnD,IAAI,UAA0C,CAAC;YAC/C,IAAI,aAAa,KAAK,IAAI,EAAE;gBAC1B,UAAU,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;aACtE;YAED,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,KAAK,GAAG,IAAA,0CAAwB,EAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBACrE,yDAAyD;gBACxD,KAAa,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS,CAAC;gBAC5D,KAAa,CAAC,eAAe;oBAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,IAAI,SAAS,CAAC;aACrD;iBAAM;gBACL,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,8BAAqB,CAAC,IAAI,EAAE;oBACtD,KAAK,GAAG,IAAI,8BAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBAC3D;qBAAM;oBACL,KAAK,GAAG,IAAI,sBAAa,CACvB,QAAQ,CAAC,KAAK,CAAC,OAAO,EACtB,QAAQ,CAAC,KAAK,CAAC,IAAI,CACpB,CAAC;iBACH;gBACD,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;aAClC;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,uCAA2B,CAAC,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE;YACvC,IAAI,CAAC,IAAI,CAAC,iDAAqC,CAAC,CAAC;SAClD;QAED,4EAA4E;QAC5E,8DAA8D;QAC9D,IAAI,IAAI,CAAC,MAAM,KAAK,oBAAoB,EAAE;YACxC,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACvC;aAAM,IACL,IAAI,CAAC,MAAM,KAAK,wBAAwB;YACxC,IAAI,CAAC,MAAM,KAAK,iBAAiB,EACjC;YACA,OAAO,IAAA,wCAAyB,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACnD;aAAM;YACL,OAAO,QAAQ,CAAC,MAAM,CAAC;SACxB;IACH,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAmB;QACpC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,mDAAmD;IAC3C,wBAAwB,CAAC,QAA8B;QAC7D,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;QAEtC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CACpC,KAAK,EAAE,OAAe,EAAE,IAAY,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,OAAgB;QACzC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEO,iBAAiB,CAAC,KAAwB;QAChD,MAAM,YAAY,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACxD,IAAI,CAAC,6BAA6B,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SAC1D;IACH,CAAC;IAEO,4BAA4B,CAAC,YAAoB,EAAE,MAAW;QACpE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,YAAY;YACZ,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAEO,6BAA6B,CAAC,YAAoB,EAAE,MAAe;QACzE,MAAM,OAAO,GAAoB;YAC/B,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE;gBACJ,YAAY;gBACZ,MAAM;aACP;SACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAEO,2BAA2B,CACjC,MAAa;QAEb,OAAO,IAAA,2BAAc,EACnB,MAAM,EACN,CAAC,CAAC,MAAM,EACR,uBAAgB,EAChB,wBAAiB,CAClB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACvC,WAAmB,EACnB,aAA4B,EAC5B,cAA8B;QAE9B,IAAI,SAAS,CAAC;QACd,IAAI;YACF,SAAS,GAAG,IAAA,kDAA8B,EACxC,WAAW,EACX,aAAa,EACb,cAAc,CACf,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,oGAAoG,CACrG,CACF,CAAC;YAEF,GAAG,CACD,4FAA4F,EAC5F,KAAK,CACN,CAAC;YAEF,OAAO,KAAK,CAAC;SACd;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAC5C;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iCAAiC,CAAC,MAAa;QACrD,OAAO,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAEO,iCAAiC;QACvC,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,6BAA6B,CACzC,QAAkB;QAElB,MAAM,QAAQ,GAAG,IAAI,oBAAQ,EAAE,CAAC;QAEhC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC/B,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE;YAC7B,IAAI,IAAI,IAAI,SAAS,EAAE;gBACrB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC7B;iBAAM,IAAI,iBAAiB,IAAI,SAAS,EAAE;gBACzC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;aACrD;iBAAM;gBACL,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;aACtC;SACF;QAED,IAAI,OAAO,GAAG,QAAQ,CAAC,2BAA2B,EAAE,CAAC;QACrD,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAE9C,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;SACjE;QAED,IAAI;YACF,IAAI,OAAO,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,EAAE;gBACxD,MAAM,aAAa,CAAC;aACrB;YAED,MAAM,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;YAC5C,OAAO,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC9C;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;YAC7B,GAAG,CACD,kFAAkF,EAClF,GAAG,CACJ,CAAC;SACH;IACH,CAAC;CACF;AAvcD,gDAucC;AAED,KAAK,UAAU,aAAa,CAAC,gBAAwB;IACnD,MAAM,cAAc,GAAG,MAAM,IAAA,4BAAc,GAAE,CAAC;IAC9C,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,OAAO,kBAAkB,cAAc,CAAC,OAAO,yBAAyB,UAAU,EAAE,CAAC;AACvF,CAAC;AAEM,KAAK,UAAU,4BAA4B,CAChD,4BAA0D,EAC1D,YAA0B,EAC1B,SAAqB;IAErB,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC7B,MAAM,aAAa,GAAG,MAAM,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACzD,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC7B,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CACxC,4BAA4B,EAC5B,YAAY,EACZ,aAAa,CACd,CAAC;IACF,GAAG,CAAC,sBAAsB,CAAC,CAAC;IAE5B,OAAO,QAAQ,CAAC;AAClB,CAAC;AAhBD,oEAgBC;AAED,KAAK,UAAU,iBAAiB,CAC9B,SAAgC;IAEhC,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAE3D,IAAI;YACF,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;gBAC1C,MAAM,SAAS,GAAG,MAAM,kBAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxD,IAAI,gBAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,wCAA4B,CAAC,EAAE;oBACnE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC5B;aACF;YAED,OAAO;gBACL,UAAU;aACX,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,yFAAyF,CAC1F,CACF,CAAC;YAEF,GAAG,CACD,gIAAgI,EAChI,KAAK,CACN,CAAC;SACH;KACF;AACH,CAAC"}