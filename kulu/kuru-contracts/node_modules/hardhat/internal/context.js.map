{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../src/internal/context.ts"], "names": [], "mappings": ";;;AAQA,0CAAqE;AACrE,oDAA4C;AAE5C,2DAAuD;AACvD,0CAA4C;AAC5C,kDAAoD;AACpD,8CAAwD;AAMxD,MAAa,cAAc;IACzB;QAiCgB,aAAQ,GAAG,IAAI,cAAQ,EAAE,CAAC;QAC1B,yBAAoB,GAA0B,EAAE,CAAC;QAEjD,sBAAiB,GAAuB,EAAE,CAAC;QAG3C,oBAAe,GAAqB,EAAE,CAAC;QAEvD,6EAA6E;QAC7E,8BAA8B;QACd,gDAA2C,GACzD,EAAE,CAAC;QA3CH,IAAI,CAAC,WAAW,GAAG,IAAI,0BAAW,CAAC,IAAA,4BAAe,GAAE,CAAC,CAAC;IACxD,CAAC;IAEM,MAAM,CAAC,SAAS;QACrB,MAAM,wBAAwB,GAAG,MAAkC,CAAC;QACpE,OAAO,wBAAwB,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACjE,CAAC;IAEM,MAAM,CAAC,oBAAoB;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;SAChE;QACD,MAAM,wBAAwB,GAAG,MAAkC,CAAC;QACpE,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,wBAAwB,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAChD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,MAAM,CAAC,iBAAiB;QAC7B,MAAM,wBAAwB,GAAG,MAAkC,CAAC;QACpE,MAAM,GAAG,GAAG,wBAAwB,CAAC,gBAAgB,CAAC;QACtD,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SAC5D;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,MAAM,CAAC,oBAAoB;QAChC,MAAM,WAAW,GAAG,MAAa,CAAC;QAClC,WAAW,CAAC,gBAAgB,GAAG,SAAS,CAAC;IAC3C,CAAC;IAiBM,4BAA4B,CAAC,GAA8B;QAChE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YAClC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;IACzB,CAAC;IAEM,4BAA4B;QACjC,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YAClC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;SAChE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,yBAAyB;QAC9B,IAAI,CAAC,wBAAwB,GAAG,IAAA,gCAAqB,GAAE,CAAC;IAC1D,CAAC;IAEM,0BAA0B;QAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAA,gCAAqB,GAAE,CAAC;IACzD,CAAC;IAEM,0BAA0B;QAC/B,uBAAuB;QACvB,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE;YAC/C,OAAO,EAAE,CAAC;SACX;QAED,IAAA,+BAAsB,EACpB,IAAI,CAAC,uBAAuB,KAAK,SAAS,EAC1C,oDAAoD,CACrD,CAAC;QAEF,OAAO,gBAAgB,CACrB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,wBAAwB,CAC9B,CAAC;IACJ,CAAC;CACF;AAvFD,wCAuFC;AAED,SAAS,gBAAgB,CAAI,CAAM,EAAE,CAAM;IACzC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC"}