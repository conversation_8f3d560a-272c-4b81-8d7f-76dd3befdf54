{"version": 3, "file": "HelpPrinter.js", "sourceRoot": "", "sources": ["../../src/internal/cli/HelpPrinter.ts"], "names": [], "mappings": ";;;AASA,2CAA8C;AAC9C,qDAA6C;AAE7C,uDAAoD;AAEpD,MAAa,WAAW;IACtB,YACmB,YAAoB,EACpB,eAAuB,EACvB,QAAgB,EAChB,wBAAiD,EACjD,MAAgB,EAChB,OAAkB;QALlB,iBAAY,GAAZ,YAAY,CAAQ;QACpB,oBAAe,GAAf,eAAe,CAAQ;QACvB,aAAQ,GAAR,QAAQ,CAAQ;QAChB,6BAAwB,GAAxB,wBAAwB,CAAyB;QACjD,WAAM,GAAN,MAAM,CAAU;QAChB,YAAO,GAAP,OAAO,CAAW;IAClC,CAAC;IAEG,eAAe,CAAC,eAAe,GAAG,KAAK;QAC5C,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QAE/D,OAAO,CAAC,GAAG,CACT,UAAU,IAAI,CAAC,eAAe,mDAAmD,CAClF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEjC,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEpE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QAEtC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAEhE,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACxC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAE5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACzC;QAED,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,OAAO,CAAC,GAAG,CACT,4CAA4C,IAAI,CAAC,eAAe,wBAAwB,CACzF,CAAC;IACJ,CAAC;IAEM,cAAc,CACnB,eAAgC,EAChC,eAAe,GAAG,KAAK;QAEvB,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;QAClC,MAAM,WAAW,GAAG,eAAe,CAAC,WAAW,IAAI,EAAE,CAAC;QAEtD,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CACT,qCAAqC,IAAI,wBAAwB,CAClE,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YACpC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE;gBAC1D,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QAE5D,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,WAAW,EAAE,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CACT,kCAAkC,IAAI,CAAC,eAAe,SAAS,CAChE,CAAC;IACJ,CAAC;IAEM,aAAa,CAAC,cAA8B;QACjD,MAAM,EACJ,WAAW,GAAG,EAAE,EAChB,IAAI,EACJ,gBAAgB,EAChB,0BAA0B,GAC3B,GAAG,cAAc,CAAC;QAEnB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QAE/D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACzD,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CACxD,0BAA0B,CAC3B,CAAC;QAEF,MAAM,KAAK,GACT,cAAc,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvE,OAAO,CAAC,GAAG,CACT,UAAU,IAAI,CAAC,eAAe,qBAAqB,KAAK,GAAG,IAAI,GAAG,UAAU,GAAG,oBAAoB,IAAI,CACxG,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAE1B,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACjB;QAED,IAAI,0BAA0B,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAEvC,IAAI,CAAC,4BAA4B,CAAC,0BAA0B,CAAC,CAAC;YAE9D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACjB;QAED,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,eAAe,SAAS,CAAC,CAAC;IAC7E,CAAC;IAEO,WAAW,CACjB,QAAkB,EAClB,eAAwB,EACxB,SAAiB,CAAC;QAElB,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;aAC1C,MAAM,CACL,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,EAAE,CAAC,eAAe,IAAI,CAAC,cAAc,CAAC,SAAS,CACrE;aACA,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC;aAC7B,IAAI,EAAE,CAAC;QAEV,MAAM,UAAU,GAAG,YAAY;aAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;aACpB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAE5C,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC/B,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE5C,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC,CAAC;SAC7D;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,YAAY,CAAC,SAAoB,EAAE,MAAc;QACvD,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;aAC7C,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC;aAC/B,IAAI,EAAE,CAAC;QAEV,MAAM,UAAU,GAAG,cAAc;aAC9B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;aACpB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAE5C,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;YACjC,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC,CAAC;SAC7D;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,yBAAyB,CAAI,eAAmC;QACtE,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;IACxD,CAAC;IAEO,cAAc,CAAC,gBAAqC;QAC1D,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,EAAE;YACvD,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;YAE1C,UAAU,IAAI,GAAG,CAAC;YAElB,IAAI,UAAU,EAAE;gBACd,UAAU,IAAI,GAAG,CAAC;aACnB;YAED,UAAU,IAAI,GAAG,iCAAe,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAExD,IAAI,CAAC,MAAM,EAAE;gBACX,UAAU,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,CAAC;aAChE;YAED,IAAI,UAAU,EAAE;gBACd,UAAU,IAAI,GAAG,CAAC;aACnB;SACF;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,wBAAwB,CAC9B,0BAAuD;QAEvD,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,UAAU,IAAI,0BAA0B,EAAE;YACnD,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;YAEpD,UAAU,IAAI,GAAG,CAAC;YAElB,IAAI,UAAU,EAAE;gBACd,UAAU,IAAI,GAAG,CAAC;aACnB;YAED,IAAI,UAAU,EAAE;gBACd,UAAU,IAAI,KAAK,CAAC;aACrB;YAED,UAAU,IAAI,IAAI,CAAC;YAEnB,IAAI,UAAU,EAAE;gBACd,UAAU,IAAI,GAAG,CAAC;aACnB;SACF;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,kBAAkB,CAAC,gBAAqC;QAC9D,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;aACnD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;aACpD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,EAAE;YACvD,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,GACrD,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAEzB,IAAI,GAAG,GAAG,KAAK,iCAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,MAAM,CACxD,gBAAgB,CACjB,IAAI,CAAC;YAEN,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC;aAC1B;YAED,IAAI,UAAU,IAAI,YAAY,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE;gBACvD,GAAG,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC;aACrD;YAED,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,4BAA4B,CAClC,0BAAuD;QAEvD,MAAM,gBAAgB,GAAG,0BAA0B;aAChD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;aACzB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvC,KAAK,MAAM,UAAU,IAAI,0BAA0B,EAAE;YACnD,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,UAAU,CAAC;YAEnE,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAEjD,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC;aAC1B;YAED,IAAI,UAAU,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC5C,GAAG,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC;aACrD;YAED,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAClB;IACH,CAAC;CACF;AAvQD,kCAuQC"}