{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/internal/cli/cli.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,kDAA0B;AAC1B,uCAAqC;AAErC,+DAIwC;AAExC,4CAA4C;AAC5C,wCAA4C;AAC5C,kEAGuC;AACvC,2CAIwB;AACxB,qDAA2D;AAC3D,2DAA2E;AAC3E,gEAAsE;AACtE,kEAA0E;AAC1E,iEAGmC;AACnC,qEAA0D;AAC1D,mEAA+E;AAC/E,iDAA8C;AAC9C,uDAA2D;AAC3D,mDAI4B;AAC5B,qDAAqD;AACrD,mDAAoD;AACpD,2CAAiE;AACjE,uDAAoD;AACpD,mCAAsC;AACtC,yDAA8E;AAC9E,qCAAuD;AACvD,+EAIuC;AACvC,iCAAoC;AAEpC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEtC,MAAM,6BAA6B,GAAG,GAAG,CAAC;AAC1C,MAAM,mCAAmC,GAAG,IAAA,kCAAmB,GAAE,CAAC;AAElE,KAAK,UAAU,mBAAmB;IAChC,MAAM,WAAW,GAAG,MAAM,IAAA,4BAAc,GAAE,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC;AAED,KAAK,UAAU,8BAA8B;IAC3C,MAAM,eAAe,GAAG,IAAA,mCAAsB,GAAE,CAAC;IACjD,IAAI,eAAe,EAAE;QACnB,OAAO;KACR;IAED,MAAM,WAAW,GAAG,IAAA,sDAAwB,GAAE,CAAC;IAC/C,IAAA,qCAAwB,GAAE,CAAC;IAE3B,IAAI,WAAW,KAAK,+CAAiB,CAAC,uBAAuB,EAAE;QAC7D,OAAO;KACR;IAED,MAAM,mBAAmB,GAAG,MAAM,IAAA,oCAA2B,GAAE,CAAC;IAEhE,IAAI,mBAAmB,KAAK,IAAI,EAAE;QAChC,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,IAAA,kDAAoB,GAAE,CAAC;QAEzC,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;SAC1E;aAAM;YACL,OAAO,CAAC,GAAG,CACT,wHAAwH,CACzH,CAAC;SACH;KACF;SAAM;QACL,OAAO,CAAC,GAAG,CACT,8FAA8F,CAC/F,CAAC;KACH;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,cAA6B;IACrD,MAAM,mBAAmB,GAAG,IAAA,uCAAsB,EAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,CAC3C,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,KAAK,IAAI,CAChD,CAAC;IAEF,IAAI,YAAY,EAAE;QAChB,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV;;6CAEqC,CACtC,CACF,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,IAAI;IACjB,uEAAuE;IACvE,oDAAoD;IACpD,IAAI,eAAe,GACjB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QAC5C,mCAAmC,CAAC;IAEtC,IAAI;QACF,MAAM,oBAAoB,GAAG,IAAA,sCAAsB,EACjD,0CAAyB,EACzB,OAAO,CAAC,GAAG,CACZ,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE9C,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,GAC1D,eAAe,CAAC,qBAAqB,CACnC,0CAAyB,EACzB,oBAAoB,EACpB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACtB,CAAC;QAEJ,IAAI,gBAAgB,CAAC,OAAO,EAAE;YAC5B,mBAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1B,eAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAC1B;QAED,IAAI,gBAAgB,CAAC,KAAK,EAAE;YAC1B,IAAA,mBAAW,GAAE,CAAC;SACf;QAED,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAC;QAEnD,8BAA8B;QAC9B,IAAI,gBAAgB,CAAC,OAAO,EAAE;YAC5B,MAAM,mBAAmB,EAAE,CAAC;YAC5B,OAAO;SACR;QAED,8BAA8B;QAC9B,yGAAyG;QACzG,qCAAqC;QACrC,+EAA+E;QAE/E,+BAA+B;QAC/B,IAAI,eAAe,KAAK,MAAM,EAAE;YAC9B,OAAO,MAAM,gBAAgB,EAAE,CAAC;SACjC;QACD,uCAAuC;aAClC;YACH,IACE,eAAe,KAAK,SAAS;gBAC7B,gBAAgB,CAAC,MAAM,KAAK,SAAS;gBACrC,CAAC,IAAA,sCAAkB,GAAE,EACrB;gBACA,MAAM,gBAAgB,EAAE,CAAC;gBAEzB,qCAAqC;gBACrC,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,EAChD,eAAK,CAAC,MAAM,CACV,+BAA+B,eAAK,CAAC,KAAK,CAAC,MAAM,CAC/C,aAAa,CACd,qDAAqD,CACvD,EACD,eAAK,CAAC,MAAM,CACV,cAAc,eAAK,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,eAAe,CACpE,CACF,CAAC;gBAEF,OAAO;aACR;SACF;QACD,qCAAqC;QAErC,yEAAyE;QACzE,IAAI,gBAAgB,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,IAAA,sCAAkB,GAAE,EAAE;YAClE,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SAC3D;QAED,IACE,OAAO,CAAC,GAAG,CAAC,iDAAiD;YAC3D,MAAM;YACR,CAAC,IAAA,kDAAiC,GAAE,EACpC;YACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;SAC/D;QAED,IAAI,IAAA,0CAAqB,EAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAClD,IAAA,+BAAU,EAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;SACnE;aAAM;YACL,IAAI,gBAAgB,CAAC,SAAS,KAAK,IAAI,EAAE;gBACvC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,SAAS,CAAC,oCAAoC,CACtD,CAAC;aACH;SACF;QAED,MAAM,GAAG,GAAG,wBAAc,CAAC,oBAAoB,EAAE,CAAC;QAElD,IAAI,eAAe,KAAK,MAAM,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5D,OAAO,CAAC,IAAI,CAAC,MAAM,IAAA,iBAAU,EAAC,eAAe,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;SAC1E;QAED,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,IAAA,mCAAkB,EACvD,gBAAgB,EAChB;YACE,sBAAsB,EAAE,IAAI;YAC5B,0BAA0B,EAAE,eAAe,KAAK,yBAAY;SAC7D,CACF,CAAC;QAEF,MAAM,YAAY,GAAG,GAAG,CAAC,oBAAoB,CAAC;QAC9C,MAAM,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,CAAC;QAChD,MAAM,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAC1D,MAAM,iBAAiB,GAAG,GAAG,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QAE9D,MAAM,GAAG,GAAG,IAAI,iCAAW,CACzB,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,YAAY,EACZ,GAAG,CAAC,2CAA2C,EAC/C,UAAU,EACV,iBAAiB,CAClB,CAAC;QAEF,GAAG,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC;QAEtC,wCAAwC;QACxC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,GACvC,eAAe,CAAC,sBAAsB,CACpC,eAAe,EACf,eAAe,EACf,iBAAiB,CAClB,CAAC;QAEJ,IAAI,gBAAgB,GAAwB,IAAA,kCAAqB,GAAE,CAAC;QAEpE,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,IAAI,QAAQ,KAAK,sBAAS,CAAC;QACtE,IACE,gBAAgB,KAAK,SAAS;YAC9B,CAAC,aAAa;YACd,CAAC,IAAA,kCAAmB,GAAE;YACtB,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI;YAC7B,OAAO,CAAC,GAAG,CAAC,gCAAgC,KAAK,MAAM,EACvD;YACA,gBAAgB,GAAG,MAAM,IAAA,mCAAuB,GAAE,CAAC;SACpD;QAED,MAAM,SAAS,GAAG,MAAM,qBAAS,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QAEhE,mBAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,gBAAgB,KAAK,IAAI,EAAE;YAC7B,mBAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC3B;QAED,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,MAAM,SAAS,CAAC,WAAW,CAC9D,SAAS,EACT,QAAQ,CACT,CAAC;QAEF,IAAI,aAA4B,CAAC;QAEjC,gCAAgC;QAChC,IAAI,gBAAgB,CAAC,IAAI,IAAI,QAAQ,KAAK,sBAAS,EAAE;YACnD,4DAA4D;YAC5D,wBAAwB;YACxB,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,aAAa,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;aAC5D;iBAAM;gBACL,aAAa,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;aAC3C;YACD,QAAQ,GAAG,sBAAS,CAAC;YACrB,SAAS,GAAG,SAAS,CAAC;SACvB;aAAM;YACL,MAAM,cAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CACnD,SAAS,EACT,QAAQ,CACT,CAAC;YAEF,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,IAAI,SAAS,KAAK,SAAS,EAAE;oBAC3B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE;wBAChE,KAAK,EAAE,SAAS;wBAChB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;iBACJ;gBACD,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;oBACzD,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;aACJ;YAED,IAAI,cAAc,CAAC,SAAS,EAAE;gBAC5B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE;oBAChE,IAAI,EAAE,cAAc,CAAC,IAAI;iBAC1B,CAAC,CAAC;aACJ;YAED,aAAa,GAAG,eAAe,CAAC,kBAAkB,CAChD,cAAc,EACd,YAAY,CACb,CAAC;SACH;QAED,IAAI;YACF,MAAM,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YAEhD,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;YAEnE,MAAM,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YAE/C,IACE,iBAAiB,GAAG,kBAAkB;gBACpC,6BAA6B;gBAC/B,QAAQ,KAAK,yBAAY,EACzB;gBACA,MAAM,UAAU,CAAC;aAClB;iBAAM;gBACL,cAAc,EAAE,CAAC;aAClB;SACF;gBAAS;YACR,IAAI,gBAAgB,CAAC,UAAU,KAAK,IAAI,EAAE;gBACxC,IAAA,+BAAsB,EACpB,GAAG,CAAC,gBAAgB,KAAK,SAAS,EAClC,0DAA0D,CAC3D,CAAC;gBAEF,MAAM,cAAc,GAAG,IAAA,2BAAc,EAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC;aACxD;SACF;QAED,2CAA2C;QAC3C,IACE,QAAQ,KAAK,sBAAS;YACtB,CAAC,IAAA,kCAAmB,GAAE;YACtB,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,EAC7B;YACA,MAAM,8BAA8B,EAAE,CAAC;YAEvC,mEAAmE;YACnE,kBAAkB;YAClB,IACE,OAAO,CAAC,QAAQ,KAAK,CAAC;gBACtB,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM;gBACtB,OAAO,CAAC,GAAG,CAAC,oCAAoC,KAAK,MAAM,EAC3D;gBACA,IAAA,4CAAyB,GAAE,CAAC;aAC7B;YAED,qDAAqD;YACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;gBAC1B,gBAAgB,CAAC,cAAc,CAAC,CAAC;aAClC;YAED,qDAAqD;YACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;gBAC1B,IAAI;oBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG,wDACrC,oBAAoB,GACrB,CAAC;oBACF,MAAM,0BAA0B,EAAE,CAAC;iBACpC;gBAAC,MAAM;oBACN,0CAA0C;iBAC3C;aACF;SACF;QAED,GAAG,CAAC,mDAAmD,QAAQ,EAAE,CAAC,CAAC;KACpE;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,IAAI,qBAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACtC,cAAc,GAAG,IAAI,CAAC;YACtB,OAAO,CAAC,KAAK,CACX,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EACvB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC;SACH;aAAM,IAAI,2BAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;YACzD,cAAc,GAAG,IAAI,CAAC;YACtB,OAAO,CAAC,KAAK,CACX,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,KAAK,CAAC,UAAU,GAAG,CAAC,EACtD,KAAK,CAAC,OAAO,CACd,CAAC;SACH;aAAM,IAAI,KAAK,YAAY,KAAK,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC1D,eAAe,GAAG,IAAI,CAAC;SACxB;aAAM;YACL,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC1D,eAAe,GAAG,IAAI,CAAC;SACxB;QAED,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,IAAI;YACF,mBAAQ,CAAC,WAAW,CAAC,KAAc,CAAC,CAAC;SACtC;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,qCAAqC,EAAE,CAAC,CAAC,CAAC;SAC/C;QAED,IAAI,eAAe,IAAI,mCAAmC,EAAE;YAC1D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,cAAc,EAAE;gBACnB,OAAO,CAAC,KAAK,CACX,8FAA8F,CAC/F,CAAC;aACH;YAED,IAAI,qBAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBACtC,MAAM,IAAI,GAAG,uBAAuB,IAAA,0BAAY,EAC9C,KAAK,CAAC,eAAe,CACtB,EAAE,CAAC;gBAEJ,OAAO,CAAC,KAAK,CACX,uBAAuB,IAAI,WAAW,wBAAY,2BAA2B,CAC9E,CAAC;aACH;iBAAM;gBACL,OAAO,CAAC,KAAK,CACX,qBAAqB,wBAAY,2BAA2B,CAC7D,CAAC;aACH;SACF;QAED,MAAM,mBAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC;AAED,KAAK,UAAU,gBAAgB;IAC7B,IAAI,IAAA,sCAAkB,GAAE,EAAE;QACxB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE;YACrE,sBAAsB,EAAE,IAAA,qCAAiB,GAAE;SAC5C,CAAC,CAAC;KACJ;IAED,IACE,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI;QAC7B,OAAO,CAAC,GAAG,CAAC,+CAA+C,KAAK,SAAS;QACzE,OAAO,CAAC,GAAG,CAAC,+CAA+C,KAAK,SAAS;QACzE,OAAO,CAAC,GAAG,CAAC,oDAAoD;YAC9D,SAAS,EACX;QACA,MAAM,IAAA,gCAAa,GAAE,CAAC;QACtB,OAAO;KACR;IAED,qEAAqE;IACrE,oEAAoE;IACpE,wEAAwE;IACxE,wCAAwC;IACxC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;KACtE;IAED,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAClE,CAAC;AAED,IAAI,EAAE;KACH,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1C,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}