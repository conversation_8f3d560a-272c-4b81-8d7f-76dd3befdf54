{"version": 3, "file": "ArgumentsParser.d.ts", "sourceRoot": "", "sources": ["../../src/internal/cli/ArgumentsParser.ts"], "names": [], "mappings": "AACA,OAAO,EAEL,gBAAgB,EAChB,uBAAuB,EAGvB,SAAS,EACT,aAAa,EACb,cAAc,EACd,QAAQ,EACT,MAAM,aAAa,CAAC;AAIrB,qBAAa,eAAe;IAC1B,gBAAuB,YAAY,QAAQ;WAE7B,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;WAUzC,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAqB1C,qBAAqB,CAC1B,uBAAuB,EAAE,uBAAuB,EAChD,oBAAoB,EAAE,gBAAgB,EACtC,OAAO,EAAE,MAAM,EAAE,GAChB;QACD,gBAAgB,EAAE,gBAAgB,CAAC;QACnC,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,eAAe,EAAE,MAAM,EAAE,CAAC;KAC3B;IAwDM,sBAAsB,CAC3B,eAAe,EAAE,MAAM,EAAE,EACzB,eAAe,EAAE,QAAQ,EACzB,gBAAgB,EAAE,SAAS,GAC1B;QACD,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB,YAAY,EAAE,MAAM,EAAE,CAAC;KACxB;IAsDM,kBAAkB,CACvB,cAAc,EAAE,cAAc,EAC9B,OAAO,EAAE,MAAM,EAAE,GAChB,aAAa;IAYhB,OAAO,CAAC,wBAAwB;IAmChC,OAAO,CAAC,2BAA2B;IAWnC,OAAO,CAAC,wBAAwB;IAqBhC,OAAO,CAAC,eAAe;IASvB,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,gBAAgB;IAuCxB,OAAO,CAAC,yBAAyB;CAgDlC"}