{"version": 3, "file": "version-notifier.js", "sourceRoot": "", "sources": ["../../src/internal/cli/version-notifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,kDAA0B;AAC1B,wDAA+B;AAC/B,yCAAiC;AACjC,+CAA8D;AAC9D,oDAA4B;AAE5B,mDAAiD;AACjD,qDAAwD;AAExD,MAAM,cAAc,GAAG,wBAAwB,CAAC;AAChD,MAAM,YAAY,GAAG,iBAAiB,CAAC;AACvC,MAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,MAAM,cAAc,GAAG,eAAe,CAAC;AACvC,MAAM,sCAAsC,GAAG,8BAA8B,CAAC;AAC9E,MAAM,0BAA0B,GAAG,CAAC,CAAC;AACrC,MAAM,6BAA6B,GAAG,CAAC,CAAC;AAExC,MAAM,YAAY,GAAG;IACnB,OAAO,EAAE,CAAC;IACV,WAAW,EAAE,OAAO;IACpB,WAAW,EAAE,QAAQ;CACb,CAAC;AAuBX,wDAAwD;AAEjD,KAAK,UAAU,0BAA0B;IAC9C,MAAM,KAAK,GAAG,MAAM,SAAS,EAAE,CAAC;IAEhC,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAEnC,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE;QACpD,OAAO;KACR;IAED,MAAM,cAAc,GAAG,IAAA,+BAAiB,GAAE,CAAC;IAE3C,MAAM,QAAQ,GAAG,MAAM,WAAW,EAAE,CAAC;IAErC,MAAM,gBAAgB,GAAG,QAAQ;QAC/B,sCAAsC;SACrC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QACnB,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAErE,MAAM,cAAc,GAAG,gBAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAEvD,2BAA2B;QAC3B,oCAAoC;QACpC,iCAAiC;QACjC,2BAA2B;QAC3B,+CAA+C;QAC/C,IACE,WAAW,KAAK,WAAW;YAC3B,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,UAAU;YAClB,cAAc,KAAK,IAAI;YACvB,gBAAM,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,6BAA6B,EAC9D;YACA,OAAO,EAAE,CAAC;SACX;QAED,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1B,CAAC,CAAC;QACF,sCAAsC;SACrC,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE,EAAE;QACzC,OAAO,gBAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEL,MAAM,eAAe,GAAuB,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEhE,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,MAAM,YAAY,EAAE,CAAC,CAAC;IAE5D,IAAI,eAAe,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE;QAC5D,yDAAyD;QACzD,OAAO;KACR;IAED,IACE,eAAe,KAAK,SAAS;QAC7B,gBAAM,CAAC,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC,EAC1C;QACA,IAAI,mBAAmB,GAAG,aAAa,CAAC;QACxC,IAAI,MAAM,kBAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACzC,mBAAmB,GAAG,UAAU,CAAC;SAClC;aAAM,IAAI,MAAM,kBAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;YACrD,mBAAmB,GAAG,cAAc,CAAC;SACtC;QAED,OAAO,CAAC,GAAG,CACT,IAAA,eAAK,EACH,kCAAkC,eAAK,CAAC,GAAG,CACzC,cAAc,CACf,OAAO,eAAK,CAAC,KAAK,CAAC,eAAe,CAAC;;yCAEH,eAAe;;OAEjD,mBAAmB,6BAA6B,EAC/C,YAAY,CACb,CACF,CAAC;KACH;IAED,IACE,SAAS,KAAK,SAAS;QACvB,KAAK,CAAC,YAAY,GAAG,0BAA0B,EAC/C;QACA,MAAM,cAAc,GAAG,gBAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtE,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,KAAK,CAAC,gBAAgB,KAAK,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,KAAK,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBACxC,OAAO,CAAC,GAAG,CAAC,IAAA,eAAK,EAAC,KAAK,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;gBACzD,KAAK,CAAC,YAAY,EAAE,CAAC;aACtB;SACF;KACF;IAED,MAAM,UAAU,CAAC;QACf,GAAG,KAAK;QACR,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE;QAC5B,SAAS;KACV,CAAC,CAAC;AACL,CAAC;AAlGD,gEAkGC;AAED,KAAK,UAAU,SAAS;IACtB,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAW,GAAE,CAAC;IACrC,MAAM,wBAAwB,GAAG,IAAA,gBAAI,EAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;IAEzE,IAAI,KAAK,GAAyB;QAChC,SAAS,EAAE,CAAC;QACZ,YAAY,EAAE,CAAC;KAChB,CAAC;IACF,IAAI;QACF,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAQ,EAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;QACvE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAE7D,KAAK,GAAG;YACN,SAAS,EAAE,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACxD,YAAY,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SAClE,CAAC;KACH;IAAC,OAAO,KAAU,EAAE;QACnB,4BAA4B;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,KAA2B;IACnD,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAW,GAAE,CAAC;IACrC,MAAM,wBAAwB,GAAG,IAAA,gBAAI,EAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;IAEzE,IAAI;QACF,MAAM,IAAA,gBAAK,EAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3C,MAAM,IAAA,oBAAS,EAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3E;IAAC,OAAO,KAAK,EAAE;QACd,4BAA4B;KAC7B;AACH,CAAC;AAED,KAAK,UAAU,WAAW;IACxB,MAAM,EAAE,OAAO,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAC3C,IAAI,QAAQ,GAAc,EAAE,CAAC;IAE7B,IAAI;QACF,MAAM,cAAc,GAAG,MAAM,OAAO,CAClC,GAAG,cAAc,UAAU,YAAY,IAAI,WAAW,WAAW,EACjE;YACE,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,YAAY,EAAE,SAAS;gBACvB,sBAAsB,EAAE,YAAY;aACrC;YACD,KAAK,EAAE;gBACL,QAAQ,EAAE,GAAG;aACd;SACF,CACF,CAAC;QACF,QAAQ,GAAG,CAAC,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAc,CAAC;KAC5D;IAAC,OAAO,KAAU,EAAE;QACnB,4BAA4B;KAC7B;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,KAAK,UAAU,YAAY;IACzB,MAAM,EAAE,OAAO,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAC3C,IAAI,SAA8B,CAAC;IAEnC,IAAI;QACF,MAAM,cAAc,GAAG,MAAM,OAAO,CAClC,GAAG,cAAc,UAAU,YAAY,IAAI,WAAW,kBAAkB,cAAc,EAAE,EACxF;YACE,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,YAAY,EAAE,SAAS;gBACvB,sBAAsB,EAAE,YAAY;aACrC;SACF,CACF,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAQ,CAAC;QAC/D,IAAI,YAAY,CAAC,OAAO,KAAK,WAAW,EAAE;YACxC,sFAAsF;YACtF,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;SAC9B;QAED,SAAS,GAAG,YAAuB,CAAC;KACrC;IAAC,OAAO,KAAU,EAAE;QACnB,4BAA4B;KAC7B;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,KAAK,UAAU,mBAAmB,CAChC,SAAkB;IAElB,MAAM,EAAE,OAAO,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAE3C,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAChD,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,sCAAsC,CAC9D,CAAC;IAEF,IAAI,oBAAoB,KAAK,SAAS,EAAE;QACtC,OAAO;KACR;IAED,IAAI,gBAAgB,CAAC;IACrB,IAAI;QACF,MAAM,cAAc,GAAG,MAAM,OAAO,CAClC,oBAAoB,CAAC,oBAAoB,EACzC;YACE,MAAM,EAAE,KAAK;YACb,eAAe,EAAE,EAAE;SACpB,CACF,CAAC;QAEF,gBAAgB,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KACrD;IAAC,OAAO,KAAU,EAAE;QACnB,4BAA4B;KAC7B;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC"}