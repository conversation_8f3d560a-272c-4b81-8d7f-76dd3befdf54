{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/node.ts"], "names": [], "mappings": ";;;;;AAEA,kDAA0B;AAC1B,kDAA0B;AAC1B,wDAA+B;AAE/B,qDAA6D;AAC7D,mEAA0E;AAC1E,oDAAuD;AACvD,8DAAsD;AACtD,0EAAyE;AACzE,0DAAwF;AACxF,uEAGoD;AACpD,0DAAuD;AAOvD,2EAAkF;AAClF,6CAMsB;AACtB,yCAA6D;AAE7D,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,yBAAyB,CAAC,CAAC;AAE7C,SAAS,yBAAyB;IAChC,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,IAAI,CACR,sEAAsE,CACvE,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,IAAI,CACR,2EAA2E,CAC5E,CACF,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAAC,aAAmC;IACpE,MAAM,eAAe,GACnB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtC,aAAa,CAAC,QAAQ,CAAC,QAAQ,KAAK,yCAAwB,CAAC;IAE/D,MAAM,EACJ,UAAU,EAAE,WAAW,EACvB,gBAAgB,EAChB,OAAO,EACP,iBAAiB,GAClB,GAAG,OAAO,CAAC,kCAAkC,CAA2B,CAAC;IAE1E,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAExB,IAAI,eAAe,EAAE;QACnB,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,yBAAyB,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,MAAM,QAAQ,GAAG,IAAA,4CAAqC,EACpD,aAAa,CAAC,QAAQ,CACvB,CAAC;IAEF,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,OAAO,GAAG,iBAAiB,CAC/B,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAC3D,CAAC;QAEF,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEpE,IAAI,KAAK,GAAG,YAAY,KAAK,KAAK,OAAO,KAAK,OAAO,OAAO,CAAC;QAE7D,IAAI,eAAe,EAAE;YACnB,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5D,KAAK,IAAI;eACA,UAAU,EAAE,CAAC;SACvB;QAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,IAAI,eAAe,EAAE;QACnB,yBAAyB,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;AACH,CAAC;AAED,IAAA,oBAAO,EAAC,mCAAsB,CAAC;KAC5B,gBAAgB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,MAAM,CAAC;KAC/D,gBAAgB,CAAC,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACpE,SAAS,CACR,KAAK,EACH,EACE,eAAe,EAAE,oBAAoB,EACrC,OAAO,EAAE,YAAY,GAItB,EACD,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,EACf,EAAE;IAC7B,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAEhC,IAAI,OAAO,CAAC,IAAI,KAAK,gCAAoB,EAAE;QACzC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QACrD,QAAQ,GAAG,MAAM,IAAA,6BAAc,EAC7B,MAAM,EACN,gCAAoB,EACpB,SAAS,CACV,CAAC;KACH;IAED,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,gCAAoB,CAAC,CAAC;IAEnE,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,EAAE,GAAG,CAAC;IACxD,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,OAAO,EAAE,WAAW,CAAC;IAExE,MAAM,OAAO,GAAG,YAAY,IAAI,aAAa,CAAC;IAC9C,MAAM,eAAe,GAAG,oBAAoB,IAAI,qBAAqB,CAAC;IAEtE,sEAAsE;IACtE,UAAU;IACV,IAAI,eAAe,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE;QAC1D,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,aAAa,CAAC,kCAAkC,CACxD,CAAC;KACH;IAED,wEAAwE;IACxE,uCAAuC;IACvC,IACE,OAAO,KAAK,aAAa;QACzB,eAAe,KAAK,qBAAqB,EACzC;QACA,MAAM,QAAQ,CAAC,OAAO,CAAC;YACrB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE;gBACN;oBACE,OAAO,EAAE;wBACP,UAAU,EAAE,OAAO;wBACnB,WAAW,EAAE,eAAe;qBAC7B;iBACF;aACF;SACF,CAAC,CAAC;KACJ;IAED,MAAM,wBAAwB,GAC5B,UAAU,CAAC,QAAQ,EAAE,CAAC,gCAAoB,CAAC,IAAI,EAAE,CAAC;IAEpD,iBAAiB;IACjB,MAAM,QAAQ,CAAC,OAAO,CAAC;QACrB,MAAM,EAAE,2BAA2B;QACnC,MAAM,EAAE,CAAC,wBAAwB,CAAC,cAAc,IAAI,IAAI,CAAC;KAC1D,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC,CACF,CAAC;AAEJ,IAAA,oBAAO,EAAC,oCAAuB,CAAC;KAC7B,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,MAAM,CAAC;KACxD,QAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACjD,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACrD,SAAS,CACR,KAAK,EAAE,EACL,QAAQ,EACR,IAAI,EACJ,QAAQ,GAKT,EAA0B,EAAE;IAC3B,MAAM,YAAY,GAAwB;QACxC,QAAQ;QACR,IAAI;QACJ,QAAQ;KACT,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,sBAAiB,CAAC,YAAY,CAAC,CAAC;IAEnD,OAAO,MAAM,CAAC;AAChB,CAAC,CACF,CAAC;AAEJ;;;GAGG;AACH,IAAA,oBAAO,EAAC,qCAAwB,CAAC;KAC9B,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,MAAM,CAAC;KACxD,QAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACjD,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACrD,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACnD,SAAS,CACR,KAAK,EAAE,EAKN,EAAE,EAAE;IACH,uDAAuD;AACzD,CAAC,CACF,CAAC;AAEJ;;GAEG;AACH,IAAA,oBAAO,EAAC,mCAAsB,CAAC;KAC5B,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,MAAM,CAAC;KACvD,QAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACjD,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACrD,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KACnD,SAAS,CACR,KAAK,EACH,EACE,OAAO,EACP,IAAI,GAML,EACD,EAAE,MAAM,EAAE,EACV,EAAE;IACF,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,KAAK,CACT,wDAAwD,OAAO,IAAI,IAAI,GAAG,CAC3E,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,gCAAoB,CAAC,CAAC;IAC5D,yBAAyB,CAAC,aAAa,CAAC,CAAC;AAC3C,CAAC,CACF,CAAC;AAEJ,IAAA,iBAAI,EAAC,sBAAS,EAAE,oDAAoD,CAAC;KAClE,gBAAgB,CACf,UAAU,EACV,iHAAiH,EACjH,SAAS,EACT,kBAAK,CAAC,MAAM,CACb;KACA,gBAAgB,CACf,MAAM,EACN,iDAAiD,EACjD,IAAI,EACJ,kBAAK,CAAC,GAAG,CACV;KACA,gBAAgB,CACf,MAAM,EACN,6CAA6C,EAC7C,SAAS,EACT,kBAAK,CAAC,MAAM,CACb;KACA,gBAAgB,CACf,iBAAiB,EACjB,+BAA+B,EAC/B,SAAS,EACT,kBAAK,CAAC,GAAG,CACV;KACA,SAAS,CACR,KAAK,EACH,EACE,eAAe,EACf,IAAI,EAAE,OAAO,EACb,QAAQ,EAAE,aAAa,EACvB,IAAI,GAML,EACD,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,GAAG,EAAE,EAC1C,EAAE;IACF,yEAAyE;IACzE,IACE,OAAO,CAAC,IAAI,KAAK,gCAAoB;QACrC,gBAAgB,CAAC,OAAO,KAAK,SAAS,EACtC;QACA,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,aAAa,CAAC,2BAA2B,CACjD,CAAC;KACH;IAED,IAAI;QACF,MAAM,QAAQ,GAAqB,MAAM,GAAG,CAAC,mCAAsB,EAAE;YACnE,eAAe;YACf,OAAO;SACR,CAAC,CAAC;QAEH,oEAAoE;QACpE,2CAA2C;QAC3C,IAAI,QAAgB,CAAC;QACrB,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,QAAQ,GAAG,aAAa,CAAC;SAC1B;aAAM;YACL,MAAM,YAAY,GAAG,kBAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YACvD,IAAI,YAAY,EAAE;gBAChB,QAAQ,GAAG,SAAS,CAAC;aACtB;iBAAM;gBACL,QAAQ,GAAG,WAAW,CAAC;aACxB;SACF;QAED,MAAM,MAAM,GAAkB,MAAM,GAAG,CAAC,oCAAuB,EAAE;YAC/D,QAAQ;YACR,IAAI;YACJ,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,qCAAwB,EAAE;YAClC,QAAQ;YACR,IAAI;YACJ,QAAQ;YACR,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;QAE5D,IAAI,OAA4B,CAAC;QACjC,IAAI;YACF,OAAO,GAAG,MAAM,IAAA,2BAAmB,EAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SAC7D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,iKAAiK,CAClK,CACF,CAAC;YAEF,GAAG,CACD,uFAAuF,EACvF,KAAK,CACN,CAAC;YAEF,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,mBAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC7B;SACF;QAED,MAAM,GAAG,CAAC,mCAAsB,EAAE;YAChC,OAAO;YACP,IAAI,EAAE,UAAU;YAChB,QAAQ;YACR,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;QAC/B,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC;KACxB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,qBAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACtC,MAAM,KAAK,CAAC;SACb;QAED,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,aAAa,CAAC,oBAAoB,EACzC;gBACE,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,EACD,KAAK,CACN,CAAC;SACH;QAED,sFAAsF;QACtF,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CACF,CAAC"}