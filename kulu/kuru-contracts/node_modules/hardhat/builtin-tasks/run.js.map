{"version": 3, "file": "run.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/run.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,wDAA+B;AAE/B,mEAA0D;AAC1D,oDAAuD;AACvD,8DAAsD;AACtD,oEAAuE;AAEvE,6CAAsD;AAEtD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,wBAAwB,CAAC,CAAC;AAE5C,IAAA,iBAAI,EAAC,qBAAQ,EAAE,wDAAwD,CAAC;KACrE,kBAAkB,CACjB,QAAQ,EACR,kDAAkD,CACnD;KACA,OAAO,CAAC,WAAW,EAAE,wCAAwC,CAAC;KAC9D,SAAS,CACR,KAAK,EACH,EAAE,MAAM,EAAE,SAAS,EAA0C,EAC7D,EAAE,GAAG,EAAE,gBAAgB,EAAE,EACzB,EAAE;IACF,IAAI,CAAC,CAAC,MAAM,kBAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;QACvC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE;YAC9D,MAAM;SACP,CAAC,CAAC;KACJ;IAED,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,GAAG,CAAC,yBAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1C;IAED,GAAG,CACD,kBAAkB,MAAM,oDAAoD,CAC7E,CAAC;IAEF,IAAI;QACF,OAAO,CAAC,QAAQ,GAAG,MAAM,IAAA,qCAAoB,EAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;KACzE;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,aAAa,CAAC,gBAAgB,EACrC;gBACE,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,EACD,KAAK,CACN,CAAC;SACH;QAED,sFAAsF;QACtF,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CACF,CAAC"}