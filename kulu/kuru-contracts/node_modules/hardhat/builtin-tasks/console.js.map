{"version": 3, "file": "console.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/console.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,wDAA+B;AAC/B,2CAA6B;AAC7B,+CAAiC;AAEjC,mEAA0D;AAC1D,oEAAuE;AAEvE,6CAA0D;AAE1D,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,4BAA4B,CAAC,CAAC;AAEhD,IAAA,iBAAI,EAAC,yBAAY,EAAE,yBAAyB,CAAC;KAC1C,OAAO,CAAC,WAAW,EAAE,wCAAwC,CAAC;KAC9D,SAAS,CACR,KAAK,EACH,EAAE,SAAS,EAA0B,EACrC,EAAE,MAAM,EAAE,GAAG,EAAE,gBAAgB,EAAE,EACjC,EAAE;IACF,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,GAAG,CAAC,yBAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1C;IAED,MAAM,kBAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;IAEzE,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;QACzC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;KAC5C;IAED,GAAG,CACD,yFAAyF,CAC1F,CAAC;IAEF,wEAAwE;IACxE,MAAM,IAAA,qCAAoB,EAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;QAC7D,iBAAiB,EAAE,WAAW;KAC/B,CAAC,CAAC;AACL,CAAC,CACF,CAAC"}