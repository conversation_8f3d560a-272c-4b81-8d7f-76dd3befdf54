{"name": "ethereum-cryptography", "version": "1.2.0", "description": "All the cryptographic primitives used in Ethereum", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "repository": "https://github.com/ethereum/js-ethereum-cryptography", "license": "MIT", "main": "./index.js", "files": ["bip39/*.js", "bip39/*.d.ts", "bip39/wordlists/*.js", "bip39/wordlists/*.d.ts", "*.js", "*.d.ts"], "dependencies": {"@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@scure/bip32": "1.1.5", "@scure/bip39": "1.1.1"}, "browser": {"crypto": false}, "sideEffects": false, "scripts": {"prepare": "npm run build", "build": "npm-run-all build:tsc", "build:tsc": "tsc --project tsconfig.prod.json", "test": "npm-run-all test:node", "test:node": "mocha", "clean": "rimraf test-builds bip39 '*.js' '*.js.map' '*.d.ts' '*.d.ts.map' 'src/**/*.js'", "lint": "eslint", "lint:fix": "eslint --fix", "browser-tests": "npm-run-all browser-tests:build browser-tests:test", "browser-tests:build": "bash -x ./scripts/build-browser-tests.sh", "browser-tests:test": "npm-run-all browser-tests:test-parcel browser-tests:test-browserify browser-tests:test-webpack browser-tests:test-rollup", "browser-tests:test-parcel": "karma start --single-run --browsers ChromeHeadless test/karma.parcel.conf.js", "browser-tests:test-browserify": "karma start --single-run --browsers ChromeHeadless test/karma.browserify.conf.js", "browser-tests:test-webpack": "karma start --single-run --browsers ChromeHeadless test/karma.webpack.conf.js", "browser-tests:test-rollup": "karma start --single-run --browsers ChromeHeadless test/karma.rollup.conf.js"}, "devDependencies": {"@rollup/plugin-commonjs": "22.0.1", "@rollup/plugin-node-resolve": "13.3.0", "@types/estree": "0.0.47", "@types/mocha": "9.1.1", "@types/node": "18.0.4", "@typescript-eslint/eslint-plugin": "5.30.6", "@typescript-eslint/parser": "5.30.6", "browserify": "17.0.0", "eslint": "8.19.0", "eslint-plugin-prettier": "4.2.1", "karma": "6.4.0", "karma-chrome-launcher": "3.1.1", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "mocha": "10.0.0", "npm-run-all": "4.1.5", "parcel": "2.6.2", "prettier": "2.7.1", "rimraf": "~3.0.2", "rollup": "2.76.0", "ts-node": "10.9.1", "typescript": "4.7.3", "webpack": "5.73.0", "webpack-cli": "4.10"}, "keywords": ["ethereum", "cryptography", "digital signature", "hash", "encryption", "prng", "keccak", "scrypt", "pbkdf2", "sha-256", "ripemd-160", "blake2b", "aes", "advanced encryption standar", "secp256k1", "ecdsa", "bip32", "hierarchical deterministic keys", "hdwallet", "hdkeys"], "targets": {"parcel_tests": {"context": "browser"}}}