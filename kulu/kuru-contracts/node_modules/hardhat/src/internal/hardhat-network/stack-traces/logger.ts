// ------------------------------------
// This code was autogenerated using
// scripts/console-library-generator.ts
// ------------------------------------

export const Int256Ty = "Int256";
export const Uint256Ty = "Uint256";
export const StringTy = "String";
export const BoolTy = "Bool";
export const AddressTy = "Address";
export const BytesTy = "Bytes";
export const Bytes1Ty = "Bytes1";
export const Bytes2Ty = "Bytes2";
export const Bytes3Ty = "Bytes3";
export const Bytes4Ty = "Bytes4";
export const Bytes5Ty = "Bytes5";
export const Bytes6Ty = "Bytes6";
export const Bytes7Ty = "Bytes7";
export const Bytes8Ty = "Bytes8";
export const Bytes9Ty = "Bytes9";
export const Bytes10Ty = "Bytes10";
export const Bytes11Ty = "Bytes11";
export const Bytes12Ty = "Bytes12";
export const Bytes13Ty = "Bytes13";
export const Bytes14Ty = "Bytes14";
export const Bytes15Ty = "Bytes15";
export const Bytes16Ty = "Bytes16";
export const Bytes17Ty = "Bytes17";
export const Bytes18Ty = "Bytes18";
export const Bytes19Ty = "Bytes19";
export const Bytes20Ty = "Bytes20";
export const Bytes21Ty = "Bytes21";
export const Bytes22Ty = "Bytes22";
export const Bytes23Ty = "Bytes23";
export const Bytes24Ty = "Bytes24";
export const Bytes25Ty = "Bytes25";
export const Bytes26Ty = "Bytes26";
export const Bytes27Ty = "Bytes27";
export const Bytes28Ty = "Bytes28";
export const Bytes29Ty = "Bytes29";
export const Bytes30Ty = "Bytes30";
export const Bytes31Ty = "Bytes31";
export const Bytes32Ty = "Bytes32";

/** Maps from a 4-byte function selector to a signature (argument types) */
export const CONSOLE_LOG_SIGNATURES: Record<number, string[]> = {
  1368866505: [],
  760966329: [Int256Ty],
  1309416733: [Int256Ty],
  4163653873: [Uint256Ty],
  4122065833: [Uint256Ty],
  1093685164: [StringTy],
  843419373: [BoolTy],
  741264322: [AddressTy],
  199720790: [BytesTy],
  1847107880: [Bytes1Ty],
  3921027734: [Bytes2Ty],
  763578662: [Bytes3Ty],
  3764340945: [Bytes4Ty],
  2793701517: [Bytes5Ty],
  2927928721: [Bytes6Ty],
  1322614312: [Bytes7Ty],
  1334060334: [Bytes8Ty],
  2428341456: [Bytes9Ty],
  20780939: [Bytes10Ty],
  67127854: [Bytes11Ty],
  2258660029: [Bytes12Ty],
  2488442420: [Bytes13Ty],
  2456219775: [Bytes14Ty],
  3667227872: [Bytes15Ty],
  1717330180: [Bytes16Ty],
  866084666: [Bytes17Ty],
  3302112666: [Bytes18Ty],
  1584093747: [Bytes19Ty],
  1367925737: [Bytes20Ty],
  3923391840: [Bytes21Ty],
  3589990556: [Bytes22Ty],
  2879508237: [Bytes23Ty],
  4055063348: [Bytes24Ty],
  193248344: [Bytes25Ty],
  4172368369: [Bytes26Ty],
  976705501: [Bytes27Ty],
  3358255854: [Bytes28Ty],
  1265222613: [Bytes29Ty],
  3994207469: [Bytes30Ty],
  3263516050: [Bytes31Ty],
  666357637: [Bytes32Ty],
  4133908826: [Uint256Ty, Uint256Ty],
  1812949376: [Uint256Ty, Uint256Ty],
  1681903839: [Uint256Ty, StringTy],
  262402885: [Uint256Ty, StringTy],
  480083635: [Uint256Ty, BoolTy],
  510514412: [Uint256Ty, BoolTy],
  1764191366: [Uint256Ty, AddressTy],
  1491830284: [Uint256Ty, AddressTy],
  3054400204: [StringTy, Uint256Ty],
  2534451664: [StringTy, Uint256Ty],
  1264337527: [StringTy, StringTy],
  3283441205: [StringTy, BoolTy],
  832238387: [StringTy, AddressTy],
  965833939: [BoolTy, Uint256Ty],
  910912146: [BoolTy, Uint256Ty],
  2414527781: [BoolTy, StringTy],
  705760899: [BoolTy, BoolTy],
  2235320393: [BoolTy, AddressTy],
  2198464680: [AddressTy, Uint256Ty],
  574869411: [AddressTy, Uint256Ty],
  1973388987: [AddressTy, StringTy],
  1974863315: [AddressTy, BoolTy],
  3673216170: [AddressTy, AddressTy],
  3522001468: [Uint256Ty, Uint256Ty, Uint256Ty],
  3884059252: [Uint256Ty, Uint256Ty, Uint256Ty],
  1909476082: [Uint256Ty, Uint256Ty, StringTy],
  2104037094: [Uint256Ty, Uint256Ty, StringTy],
  1197922930: [Uint256Ty, Uint256Ty, BoolTy],
  1733758967: [Uint256Ty, Uint256Ty, BoolTy],
  1553380145: [Uint256Ty, Uint256Ty, AddressTy],
  3191032091: [Uint256Ty, Uint256Ty, AddressTy],
  933920076: [Uint256Ty, StringTy, Uint256Ty],
  1533929535: [Uint256Ty, StringTy, Uint256Ty],
  2970968351: [Uint256Ty, StringTy, StringTy],
  1062716053: [Uint256Ty, StringTy, StringTy],
  1290643290: [Uint256Ty, StringTy, BoolTy],
  1185403086: [Uint256Ty, StringTy, BoolTy],
  2063255897: [Uint256Ty, StringTy, AddressTy],
  529592906: [Uint256Ty, StringTy, AddressTy],
  537493524: [Uint256Ty, BoolTy, Uint256Ty],
  1515034914: [Uint256Ty, BoolTy, Uint256Ty],
  2239189025: [Uint256Ty, BoolTy, StringTy],
  2332955902: [Uint256Ty, BoolTy, StringTy],
  544310864: [Uint256Ty, BoolTy, BoolTy],
  3587091680: [Uint256Ty, BoolTy, BoolTy],
  889741179: [Uint256Ty, BoolTy, AddressTy],
  1112473535: [Uint256Ty, BoolTy, AddressTy],
  1520131797: [Uint256Ty, AddressTy, Uint256Ty],
  2286109610: [Uint256Ty, AddressTy, Uint256Ty],
  1674265081: [Uint256Ty, AddressTy, StringTy],
  3464692859: [Uint256Ty, AddressTy, StringTy],
  2607726658: [Uint256Ty, AddressTy, BoolTy],
  2060456590: [Uint256Ty, AddressTy, BoolTy],
  3170737120: [Uint256Ty, AddressTy, AddressTy],
  2104993307: [Uint256Ty, AddressTy, AddressTy],
  3393701099: [StringTy, Uint256Ty, Uint256Ty],
  2526862595: [StringTy, Uint256Ty, Uint256Ty],
  1500569737: [StringTy, Uint256Ty, StringTy],
  2750793529: [StringTy, Uint256Ty, StringTy],
  3396809649: [StringTy, Uint256Ty, BoolTy],
  4043501061: [StringTy, Uint256Ty, BoolTy],
  478069832: [StringTy, Uint256Ty, AddressTy],
  3817119609: [StringTy, Uint256Ty, AddressTy],
  1478619041: [StringTy, StringTy, Uint256Ty],
  4083337817: [StringTy, StringTy, Uint256Ty],
  753761519: [StringTy, StringTy, StringTy],
  2967534005: [StringTy, StringTy, BoolTy],
  2515337621: [StringTy, StringTy, AddressTy],
  3378075862: [StringTy, BoolTy, Uint256Ty],
  689682896: [StringTy, BoolTy, Uint256Ty],
  3801674877: [StringTy, BoolTy, StringTy],
  2232122070: [StringTy, BoolTy, BoolTy],
  2469116728: [StringTy, BoolTy, AddressTy],
  220641573: [StringTy, AddressTy, Uint256Ty],
  130552343: [StringTy, AddressTy, Uint256Ty],
  3773410639: [StringTy, AddressTy, StringTy],
  3374145236: [StringTy, AddressTy, BoolTy],
  4243355104: [StringTy, AddressTy, AddressTy],
  923808615: [BoolTy, Uint256Ty, Uint256Ty],
  995886048: [BoolTy, Uint256Ty, Uint256Ty],
  3288086896: [BoolTy, Uint256Ty, StringTy],
  3359211184: [BoolTy, Uint256Ty, StringTy],
  3906927529: [BoolTy, Uint256Ty, BoolTy],
  464374251: [BoolTy, Uint256Ty, BoolTy],
  143587794: [BoolTy, Uint256Ty, AddressTy],
  3302110471: [BoolTy, Uint256Ty, AddressTy],
  278130193: [BoolTy, StringTy, Uint256Ty],
  3224906412: [BoolTy, StringTy, Uint256Ty],
  2960557183: [BoolTy, StringTy, StringTy],
  3686056519: [BoolTy, StringTy, BoolTy],
  2509355347: [BoolTy, StringTy, AddressTy],
  317855234: [BoolTy, BoolTy, Uint256Ty],
  2954061243: [BoolTy, BoolTy, Uint256Ty],
  626391622: [BoolTy, BoolTy, StringTy],
  1349555864: [BoolTy, BoolTy, BoolTy],
  276362893: [BoolTy, BoolTy, AddressTy],
  1601936123: [BoolTy, AddressTy, Uint256Ty],
  3950005167: [BoolTy, AddressTy, Uint256Ty],
  3734671984: [BoolTy, AddressTy, StringTy],
  415876934: [BoolTy, AddressTy, BoolTy],
  3530962535: [BoolTy, AddressTy, AddressTy],
  3063663350: [AddressTy, Uint256Ty, Uint256Ty],
  2273710942: [AddressTy, Uint256Ty, Uint256Ty],
  2717051050: [AddressTy, Uint256Ty, StringTy],
  3136907337: [AddressTy, Uint256Ty, StringTy],
  1736575400: [AddressTy, Uint256Ty, BoolTy],
  3846889796: [AddressTy, Uint256Ty, BoolTy],
  2076235848: [AddressTy, Uint256Ty, AddressTy],
  2548867988: [AddressTy, Uint256Ty, AddressTy],
  1742565361: [AddressTy, StringTy, Uint256Ty],
  484110986: [AddressTy, StringTy, Uint256Ty],
  4218888805: [AddressTy, StringTy, StringTy],
  3473018801: [AddressTy, StringTy, BoolTy],
  4035396840: [AddressTy, StringTy, AddressTy],
  2622462459: [AddressTy, BoolTy, Uint256Ty],
  742821141: [AddressTy, BoolTy, Uint256Ty],
  555898316: [AddressTy, BoolTy, StringTy],
  3951234194: [AddressTy, BoolTy, BoolTy],
  4044790253: [AddressTy, BoolTy, AddressTy],
  402547077: [AddressTy, AddressTy, Uint256Ty],
  1815506290: [AddressTy, AddressTy, Uint256Ty],
  7426238: [AddressTy, AddressTy, StringTy],
  4070990470: [AddressTy, AddressTy, BoolTy],
  25986242: [AddressTy, AddressTy, AddressTy],
  423606272: [Uint256Ty, Uint256Ty, Uint256Ty, Uint256Ty],
  1554033982: [Uint256Ty, Uint256Ty, Uint256Ty, Uint256Ty],
  1506790371: [Uint256Ty, Uint256Ty, Uint256Ty, StringTy],
  2024634892: [Uint256Ty, Uint256Ty, Uint256Ty, StringTy],
  3315126661: [Uint256Ty, Uint256Ty, Uint256Ty, BoolTy],
  1683143115: [Uint256Ty, Uint256Ty, Uint256Ty, BoolTy],
  4202792367: [Uint256Ty, Uint256Ty, Uint256Ty, AddressTy],
  3766828905: [Uint256Ty, Uint256Ty, Uint256Ty, AddressTy],
  1570936811: [Uint256Ty, Uint256Ty, StringTy, Uint256Ty],
  949229117: [Uint256Ty, Uint256Ty, StringTy, Uint256Ty],
  668512210: [Uint256Ty, Uint256Ty, StringTy, StringTy],
  2080582194: [Uint256Ty, Uint256Ty, StringTy, StringTy],
  2062986021: [Uint256Ty, Uint256Ty, StringTy, BoolTy],
  2989403910: [Uint256Ty, Uint256Ty, StringTy, BoolTy],
  1121066423: [Uint256Ty, Uint256Ty, StringTy, AddressTy],
  1127384482: [Uint256Ty, Uint256Ty, StringTy, AddressTy],
  3950997458: [Uint256Ty, Uint256Ty, BoolTy, Uint256Ty],
  1818524812: [Uint256Ty, Uint256Ty, BoolTy, Uint256Ty],
  2780101785: [Uint256Ty, Uint256Ty, BoolTy, StringTy],
  4024028142: [Uint256Ty, Uint256Ty, BoolTy, StringTy],
  2869451494: [Uint256Ty, Uint256Ty, BoolTy, BoolTy],
  2495495089: [Uint256Ty, Uint256Ty, BoolTy, BoolTy],
  2592172675: [Uint256Ty, Uint256Ty, BoolTy, AddressTy],
  3776410703: [Uint256Ty, Uint256Ty, BoolTy, AddressTy],
  2297881778: [Uint256Ty, Uint256Ty, AddressTy, Uint256Ty],
  1628154048: [Uint256Ty, Uint256Ty, AddressTy, Uint256Ty],
  1826504888: [Uint256Ty, Uint256Ty, AddressTy, StringTy],
  3600994782: [Uint256Ty, Uint256Ty, AddressTy, StringTy],
  365610102: [Uint256Ty, Uint256Ty, AddressTy, BoolTy],
  2833785006: [Uint256Ty, Uint256Ty, AddressTy, BoolTy],
  1453707697: [Uint256Ty, Uint256Ty, AddressTy, AddressTy],
  3398671136: [Uint256Ty, Uint256Ty, AddressTy, AddressTy],
  2193775476: [Uint256Ty, StringTy, Uint256Ty, Uint256Ty],
  3221501959: [Uint256Ty, StringTy, Uint256Ty, Uint256Ty],
  3082360010: [Uint256Ty, StringTy, Uint256Ty, StringTy],
  2730232985: [Uint256Ty, StringTy, Uint256Ty, StringTy],
  1763348340: [Uint256Ty, StringTy, Uint256Ty, BoolTy],
  2270850606: [Uint256Ty, StringTy, Uint256Ty, BoolTy],
  992115124: [Uint256Ty, StringTy, Uint256Ty, AddressTy],
  2877020669: [Uint256Ty, StringTy, Uint256Ty, AddressTy],
  2955463101: [Uint256Ty, StringTy, StringTy, Uint256Ty],
  1995203422: [Uint256Ty, StringTy, StringTy, Uint256Ty],
  564987523: [Uint256Ty, StringTy, StringTy, StringTy],
  1474103825: [Uint256Ty, StringTy, StringTy, StringTy],
  3014047421: [Uint256Ty, StringTy, StringTy, BoolTy],
  310782872: [Uint256Ty, StringTy, StringTy, BoolTy],
  3582182914: [Uint256Ty, StringTy, StringTy, AddressTy],
  3432549024: [Uint256Ty, StringTy, StringTy, AddressTy],
  3472922752: [Uint256Ty, StringTy, BoolTy, Uint256Ty],
  2763295359: [Uint256Ty, StringTy, BoolTy, Uint256Ty],
  3537118157: [Uint256Ty, StringTy, BoolTy, StringTy],
  2370346144: [Uint256Ty, StringTy, BoolTy, StringTy],
  3126025628: [Uint256Ty, StringTy, BoolTy, BoolTy],
  1371286465: [Uint256Ty, StringTy, BoolTy, BoolTy],
  2922300801: [Uint256Ty, StringTy, BoolTy, AddressTy],
  2037328032: [Uint256Ty, StringTy, BoolTy, AddressTy],
  3906142605: [Uint256Ty, StringTy, AddressTy, Uint256Ty],
  2565338099: [Uint256Ty, StringTy, AddressTy, Uint256Ty],
  2621104033: [Uint256Ty, StringTy, AddressTy, StringTy],
  4170733439: [Uint256Ty, StringTy, AddressTy, StringTy],
  2428701270: [Uint256Ty, StringTy, AddressTy, BoolTy],
  4181720887: [Uint256Ty, StringTy, AddressTy, BoolTy],
  1634266465: [Uint256Ty, StringTy, AddressTy, AddressTy],
  2141537675: [Uint256Ty, StringTy, AddressTy, AddressTy],
  3333212072: [Uint256Ty, BoolTy, Uint256Ty, Uint256Ty],
  1451396516: [Uint256Ty, BoolTy, Uint256Ty, Uint256Ty],
  3724797812: [Uint256Ty, BoolTy, Uint256Ty, StringTy],
  3906845782: [Uint256Ty, BoolTy, Uint256Ty, StringTy],
  2443193898: [Uint256Ty, BoolTy, Uint256Ty, BoolTy],
  3534472445: [Uint256Ty, BoolTy, Uint256Ty, BoolTy],
  2295029825: [Uint256Ty, BoolTy, Uint256Ty, AddressTy],
  1329595790: [Uint256Ty, BoolTy, Uint256Ty, AddressTy],
  740099910: [Uint256Ty, BoolTy, StringTy, Uint256Ty],
  2438978344: [Uint256Ty, BoolTy, StringTy, Uint256Ty],
  1757984957: [Uint256Ty, BoolTy, StringTy, StringTy],
  2754870525: [Uint256Ty, BoolTy, StringTy, StringTy],
  3952250239: [Uint256Ty, BoolTy, StringTy, BoolTy],
  879671495: [Uint256Ty, BoolTy, StringTy, BoolTy],
  4015165464: [Uint256Ty, BoolTy, StringTy, AddressTy],
  1231956916: [Uint256Ty, BoolTy, StringTy, AddressTy],
  1952763427: [Uint256Ty, BoolTy, BoolTy, Uint256Ty],
  3173363033: [Uint256Ty, BoolTy, BoolTy, Uint256Ty],
  3722155361: [Uint256Ty, BoolTy, BoolTy, StringTy],
  831186331: [Uint256Ty, BoolTy, BoolTy, StringTy],
  3069540257: [Uint256Ty, BoolTy, BoolTy, BoolTy],
  1315722005: [Uint256Ty, BoolTy, BoolTy, BoolTy],
  1768164185: [Uint256Ty, BoolTy, BoolTy, AddressTy],
  1392910941: [Uint256Ty, BoolTy, BoolTy, AddressTy],
  125994997: [Uint256Ty, BoolTy, AddressTy, Uint256Ty],
  1102442299: [Uint256Ty, BoolTy, AddressTy, Uint256Ty],
  2917159623: [Uint256Ty, BoolTy, AddressTy, StringTy],
  2721084958: [Uint256Ty, BoolTy, AddressTy, StringTy],
  1162695845: [Uint256Ty, BoolTy, AddressTy, BoolTy],
  2449150530: [Uint256Ty, BoolTy, AddressTy, BoolTy],
  2716814523: [Uint256Ty, BoolTy, AddressTy, AddressTy],
  2263728396: [Uint256Ty, BoolTy, AddressTy, AddressTy],
  211605953: [Uint256Ty, AddressTy, Uint256Ty, Uint256Ty],
  3399106228: [Uint256Ty, AddressTy, Uint256Ty, Uint256Ty],
  3719324961: [Uint256Ty, AddressTy, Uint256Ty, StringTy],
  1054063912: [Uint256Ty, AddressTy, Uint256Ty, StringTy],
  1601452668: [Uint256Ty, AddressTy, Uint256Ty, BoolTy],
  435581801: [Uint256Ty, AddressTy, Uint256Ty, BoolTy],
  364980149: [Uint256Ty, AddressTy, Uint256Ty, AddressTy],
  4256361684: [Uint256Ty, AddressTy, Uint256Ty, AddressTy],
  1182952285: [Uint256Ty, AddressTy, StringTy, Uint256Ty],
  2697204968: [Uint256Ty, AddressTy, StringTy, Uint256Ty],
  1041403043: [Uint256Ty, AddressTy, StringTy, StringTy],
  2373420580: [Uint256Ty, AddressTy, StringTy, StringTy],
  3425872647: [Uint256Ty, AddressTy, StringTy, BoolTy],
  581204390: [Uint256Ty, AddressTy, StringTy, BoolTy],
  2629472255: [Uint256Ty, AddressTy, StringTy, AddressTy],
  3420819197: [Uint256Ty, AddressTy, StringTy, AddressTy],
  1522374954: [Uint256Ty, AddressTy, BoolTy, Uint256Ty],
  2064181483: [Uint256Ty, AddressTy, BoolTy, Uint256Ty],
  2432370346: [Uint256Ty, AddressTy, BoolTy, StringTy],
  1676730946: [Uint256Ty, AddressTy, BoolTy, StringTy],
  3813741583: [Uint256Ty, AddressTy, BoolTy, BoolTy],
  2116501773: [Uint256Ty, AddressTy, BoolTy, BoolTy],
  4017276179: [Uint256Ty, AddressTy, BoolTy, AddressTy],
  3056677012: [Uint256Ty, AddressTy, BoolTy, AddressTy],
  1936653238: [Uint256Ty, AddressTy, AddressTy, Uint256Ty],
  2587672470: [Uint256Ty, AddressTy, AddressTy, Uint256Ty],
  52195187: [Uint256Ty, AddressTy, AddressTy, StringTy],
  2034490470: [Uint256Ty, AddressTy, AddressTy, StringTy],
  153090805: [Uint256Ty, AddressTy, AddressTy, BoolTy],
  22350596: [Uint256Ty, AddressTy, AddressTy, BoolTy],
  612938772: [Uint256Ty, AddressTy, AddressTy, AddressTy],
  1430734329: [Uint256Ty, AddressTy, AddressTy, AddressTy],
  2812835923: [StringTy, Uint256Ty, Uint256Ty, Uint256Ty],
  149837414: [StringTy, Uint256Ty, Uint256Ty, Uint256Ty],
  2236298390: [StringTy, Uint256Ty, Uint256Ty, StringTy],
  2773406909: [StringTy, Uint256Ty, Uint256Ty, StringTy],
  1982258066: [StringTy, Uint256Ty, Uint256Ty, BoolTy],
  4147936829: [StringTy, Uint256Ty, Uint256Ty, BoolTy],
  3793609336: [StringTy, Uint256Ty, Uint256Ty, AddressTy],
  3201771711: [StringTy, Uint256Ty, Uint256Ty, AddressTy],
  3330189777: [StringTy, Uint256Ty, StringTy, Uint256Ty],
  2697245221: [StringTy, Uint256Ty, StringTy, Uint256Ty],
  1522028063: [StringTy, Uint256Ty, StringTy, StringTy],
  1821956834: [StringTy, Uint256Ty, StringTy, StringTy],
  2099530013: [StringTy, Uint256Ty, StringTy, BoolTy],
  3919545039: [StringTy, Uint256Ty, StringTy, BoolTy],
  2084975268: [StringTy, Uint256Ty, StringTy, AddressTy],
  3144824297: [StringTy, Uint256Ty, StringTy, AddressTy],
  3827003247: [StringTy, Uint256Ty, BoolTy, Uint256Ty],
  1427009269: [StringTy, Uint256Ty, BoolTy, Uint256Ty],
  2885106328: [StringTy, Uint256Ty, BoolTy, StringTy],
  1993105508: [StringTy, Uint256Ty, BoolTy, StringTy],
  894187222: [StringTy, Uint256Ty, BoolTy, BoolTy],
  3816813520: [StringTy, Uint256Ty, BoolTy, BoolTy],
  3773389720: [StringTy, Uint256Ty, BoolTy, AddressTy],
  3847527825: [StringTy, Uint256Ty, BoolTy, AddressTy],
  1325727174: [StringTy, Uint256Ty, AddressTy, Uint256Ty],
  1481210622: [StringTy, Uint256Ty, AddressTy, Uint256Ty],
  2684039059: [StringTy, Uint256Ty, AddressTy, StringTy],
  844415720: [StringTy, Uint256Ty, AddressTy, StringTy],
  2182163010: [StringTy, Uint256Ty, AddressTy, BoolTy],
  285649143: [StringTy, Uint256Ty, AddressTy, BoolTy],
  1587722158: [StringTy, Uint256Ty, AddressTy, AddressTy],
  3939013249: [StringTy, Uint256Ty, AddressTy, AddressTy],
  4099767596: [StringTy, StringTy, Uint256Ty, Uint256Ty],
  3587119056: [StringTy, StringTy, Uint256Ty, Uint256Ty],
  1562023706: [StringTy, StringTy, Uint256Ty, StringTy],
  2366909661: [StringTy, StringTy, Uint256Ty, StringTy],
  3282609748: [StringTy, StringTy, Uint256Ty, BoolTy],
  3864418506: [StringTy, StringTy, Uint256Ty, BoolTy],
  270792626: [StringTy, StringTy, Uint256Ty, AddressTy],
  1565476480: [StringTy, StringTy, Uint256Ty, AddressTy],
  2393878571: [StringTy, StringTy, StringTy, Uint256Ty],
  2681211381: [StringTy, StringTy, StringTy, Uint256Ty],
  3731419658: [StringTy, StringTy, StringTy, StringTy],
  739726573: [StringTy, StringTy, StringTy, BoolTy],
  1834430276: [StringTy, StringTy, StringTy, AddressTy],
  3601791698: [StringTy, StringTy, BoolTy, Uint256Ty],
  2256636538: [StringTy, StringTy, BoolTy, Uint256Ty],
  1585754346: [StringTy, StringTy, BoolTy, StringTy],
  1081628777: [StringTy, StringTy, BoolTy, BoolTy],
  3279013851: [StringTy, StringTy, BoolTy, AddressTy],
  2093204999: [StringTy, StringTy, AddressTy, Uint256Ty],
  1250010474: [StringTy, StringTy, AddressTy, Uint256Ty],
  3944480640: [StringTy, StringTy, AddressTy, StringTy],
  1556958775: [StringTy, StringTy, AddressTy, BoolTy],
  1134328815: [StringTy, StringTy, AddressTy, AddressTy],
  1689631591: [StringTy, BoolTy, Uint256Ty, Uint256Ty],
  1572859960: [StringTy, BoolTy, Uint256Ty, Uint256Ty],
  1949134567: [StringTy, BoolTy, Uint256Ty, StringTy],
  1119461927: [StringTy, BoolTy, Uint256Ty, StringTy],
  2331496330: [StringTy, BoolTy, Uint256Ty, BoolTy],
  1019590099: [StringTy, BoolTy, Uint256Ty, BoolTy],
  2472413631: [StringTy, BoolTy, Uint256Ty, AddressTy],
  1909687565: [StringTy, BoolTy, Uint256Ty, AddressTy],
  620303461: [StringTy, BoolTy, StringTy, Uint256Ty],
  885731469: [StringTy, BoolTy, StringTy, Uint256Ty],
  2821114603: [StringTy, BoolTy, StringTy, StringTy],
  1066037277: [StringTy, BoolTy, StringTy, BoolTy],
  3764542249: [StringTy, BoolTy, StringTy, AddressTy],
  2386524329: [StringTy, BoolTy, BoolTy, Uint256Ty],
  2155164136: [StringTy, BoolTy, BoolTy, Uint256Ty],
  2636305885: [StringTy, BoolTy, BoolTy, StringTy],
  2304440517: [StringTy, BoolTy, BoolTy, BoolTy],
  1905304873: [StringTy, BoolTy, BoolTy, AddressTy],
  1560853253: [StringTy, BoolTy, AddressTy, Uint256Ty],
  685723286: [StringTy, BoolTy, AddressTy, Uint256Ty],
  764294052: [StringTy, BoolTy, AddressTy, StringTy],
  2508990662: [StringTy, BoolTy, AddressTy, BoolTy],
  870964509: [StringTy, BoolTy, AddressTy, AddressTy],
  4176812830: [StringTy, AddressTy, Uint256Ty, Uint256Ty],
  3668153533: [StringTy, AddressTy, Uint256Ty, Uint256Ty],
  1514632754: [StringTy, AddressTy, Uint256Ty, StringTy],
  1280700980: [StringTy, AddressTy, Uint256Ty, StringTy],
  4232594928: [StringTy, AddressTy, Uint256Ty, BoolTy],
  1522647356: [StringTy, AddressTy, Uint256Ty, BoolTy],
  1677429701: [StringTy, AddressTy, Uint256Ty, AddressTy],
  2741431424: [StringTy, AddressTy, Uint256Ty, AddressTy],
  2446397742: [StringTy, AddressTy, StringTy, Uint256Ty],
  2405583849: [StringTy, AddressTy, StringTy, Uint256Ty],
  609847026: [StringTy, AddressTy, StringTy, StringTy],
  1595265676: [StringTy, AddressTy, StringTy, BoolTy],
  2864486961: [StringTy, AddressTy, StringTy, AddressTy],
  1050642026: [StringTy, AddressTy, BoolTy, Uint256Ty],
  3318856587: [StringTy, AddressTy, BoolTy, Uint256Ty],
  72663161: [StringTy, AddressTy, BoolTy, StringTy],
  2038975531: [StringTy, AddressTy, BoolTy, BoolTy],
  573965245: [StringTy, AddressTy, BoolTy, AddressTy],
  2398352281: [StringTy, AddressTy, AddressTy, Uint256Ty],
  1857524797: [StringTy, AddressTy, AddressTy, Uint256Ty],
  2148146279: [StringTy, AddressTy, AddressTy, StringTy],
  3047013728: [StringTy, AddressTy, AddressTy, BoolTy],
  3985582326: [StringTy, AddressTy, AddressTy, AddressTy],
  927708338: [BoolTy, Uint256Ty, Uint256Ty, Uint256Ty],
  853517604: [BoolTy, Uint256Ty, Uint256Ty, Uint256Ty],
  2389310301: [BoolTy, Uint256Ty, Uint256Ty, StringTy],
  3657852616: [BoolTy, Uint256Ty, Uint256Ty, StringTy],
  3197649747: [BoolTy, Uint256Ty, Uint256Ty, BoolTy],
  2753397214: [BoolTy, Uint256Ty, Uint256Ty, BoolTy],
  14518201: [BoolTy, Uint256Ty, Uint256Ty, AddressTy],
  4049711649: [BoolTy, Uint256Ty, Uint256Ty, AddressTy],
  1779538402: [BoolTy, Uint256Ty, StringTy, Uint256Ty],
  1098907931: [BoolTy, Uint256Ty, StringTy, Uint256Ty],
  4122747465: [BoolTy, Uint256Ty, StringTy, StringTy],
  3542771016: [BoolTy, Uint256Ty, StringTy, StringTy],
  3857124139: [BoolTy, Uint256Ty, StringTy, BoolTy],
  2446522387: [BoolTy, Uint256Ty, StringTy, BoolTy],
  4275904511: [BoolTy, Uint256Ty, StringTy, AddressTy],
  2781285673: [BoolTy, Uint256Ty, StringTy, AddressTy],
  2140912802: [BoolTy, Uint256Ty, BoolTy, Uint256Ty],
  3554563475: [BoolTy, Uint256Ty, BoolTy, Uint256Ty],
  2437143473: [BoolTy, Uint256Ty, BoolTy, StringTy],
  3067439572: [BoolTy, Uint256Ty, BoolTy, StringTy],
  3468031191: [BoolTy, Uint256Ty, BoolTy, BoolTy],
  2650928961: [BoolTy, Uint256Ty, BoolTy, BoolTy],
  2597139990: [BoolTy, Uint256Ty, BoolTy, AddressTy],
  1114097656: [BoolTy, Uint256Ty, BoolTy, AddressTy],
  355982471: [BoolTy, Uint256Ty, AddressTy, Uint256Ty],
  3399820138: [BoolTy, Uint256Ty, AddressTy, Uint256Ty],
  464760986: [BoolTy, Uint256Ty, AddressTy, StringTy],
  403247937: [BoolTy, Uint256Ty, AddressTy, StringTy],
  3032683775: [BoolTy, Uint256Ty, AddressTy, BoolTy],
  1705899016: [BoolTy, Uint256Ty, AddressTy, BoolTy],
  653615272: [BoolTy, Uint256Ty, AddressTy, AddressTy],
  2318373034: [BoolTy, Uint256Ty, AddressTy, AddressTy],
  679886795: [BoolTy, StringTy, Uint256Ty, Uint256Ty],
  2387273838: [BoolTy, StringTy, Uint256Ty, Uint256Ty],
  450457062: [BoolTy, StringTy, Uint256Ty, StringTy],
  2007084013: [BoolTy, StringTy, Uint256Ty, StringTy],
  1796103507: [BoolTy, StringTy, Uint256Ty, BoolTy],
  549177775: [BoolTy, StringTy, Uint256Ty, BoolTy],
  362193358: [BoolTy, StringTy, Uint256Ty, AddressTy],
  1529002296: [BoolTy, StringTy, Uint256Ty, AddressTy],
  2078327787: [BoolTy, StringTy, StringTy, Uint256Ty],
  1574643090: [BoolTy, StringTy, StringTy, Uint256Ty],
  392356650: [BoolTy, StringTy, StringTy, StringTy],
  508266469: [BoolTy, StringTy, StringTy, BoolTy],
  2547225816: [BoolTy, StringTy, StringTy, AddressTy],
  369533843: [BoolTy, StringTy, BoolTy, Uint256Ty],
  2372902053: [BoolTy, StringTy, BoolTy, Uint256Ty],
  1211958294: [BoolTy, StringTy, BoolTy, StringTy],
  3697185627: [BoolTy, StringTy, BoolTy, BoolTy],
  1401816747: [BoolTy, StringTy, BoolTy, AddressTy],
  2781534868: [BoolTy, StringTy, AddressTy, Uint256Ty],
  453743963: [BoolTy, StringTy, AddressTy, Uint256Ty],
  316065672: [BoolTy, StringTy, AddressTy, StringTy],
  1842623690: [BoolTy, StringTy, AddressTy, BoolTy],
  724244700: [BoolTy, StringTy, AddressTy, AddressTy],
  196087467: [BoolTy, BoolTy, Uint256Ty, Uint256Ty],
  1181212302: [BoolTy, BoolTy, Uint256Ty, Uint256Ty],
  2111099104: [BoolTy, BoolTy, Uint256Ty, StringTy],
  1348569399: [BoolTy, BoolTy, Uint256Ty, StringTy],
  1637764366: [BoolTy, BoolTy, Uint256Ty, BoolTy],
  2874982852: [BoolTy, BoolTy, Uint256Ty, BoolTy],
  1420274080: [BoolTy, BoolTy, Uint256Ty, AddressTy],
  201299213: [BoolTy, BoolTy, Uint256Ty, AddressTy],
  3819555375: [BoolTy, BoolTy, StringTy, Uint256Ty],
  395003525: [BoolTy, BoolTy, StringTy, Uint256Ty],
  1830717265: [BoolTy, BoolTy, StringTy, StringTy],
  3092715066: [BoolTy, BoolTy, StringTy, BoolTy],
  4188875657: [BoolTy, BoolTy, StringTy, AddressTy],
  1836074433: [BoolTy, BoolTy, BoolTy, Uint256Ty],
  3259532109: [BoolTy, BoolTy, BoolTy, Uint256Ty],
  719587540: [BoolTy, BoolTy, BoolTy, StringTy],
  992632032: [BoolTy, BoolTy, BoolTy, BoolTy],
  2352126746: [BoolTy, BoolTy, BoolTy, AddressTy],
  1276263767: [BoolTy, BoolTy, AddressTy, Uint256Ty],
  1620281063: [BoolTy, BoolTy, AddressTy, Uint256Ty],
  2695133539: [BoolTy, BoolTy, AddressTy, StringTy],
  3231908568: [BoolTy, BoolTy, AddressTy, BoolTy],
  4102557348: [BoolTy, BoolTy, AddressTy, AddressTy],
  2079424929: [BoolTy, AddressTy, Uint256Ty, Uint256Ty],
  2617143996: [BoolTy, AddressTy, Uint256Ty, Uint256Ty],
  1374724088: [BoolTy, AddressTy, Uint256Ty, StringTy],
  2691192883: [BoolTy, AddressTy, Uint256Ty, StringTy],
  3590430492: [BoolTy, AddressTy, Uint256Ty, BoolTy],
  4002252402: [BoolTy, AddressTy, Uint256Ty, BoolTy],
  325780957: [BoolTy, AddressTy, Uint256Ty, AddressTy],
  1760647349: [BoolTy, AddressTy, Uint256Ty, AddressTy],
  3256837319: [BoolTy, AddressTy, StringTy, Uint256Ty],
  194640930: [BoolTy, AddressTy, StringTy, Uint256Ty],
  2805734838: [BoolTy, AddressTy, StringTy, StringTy],
  3804222987: [BoolTy, AddressTy, StringTy, BoolTy],
  1870422078: [BoolTy, AddressTy, StringTy, AddressTy],
  126031106: [BoolTy, AddressTy, BoolTy, Uint256Ty],
  1287000017: [BoolTy, AddressTy, BoolTy, Uint256Ty],
  1248250676: [BoolTy, AddressTy, BoolTy, StringTy],
  1788626827: [BoolTy, AddressTy, BoolTy, BoolTy],
  474063670: [BoolTy, AddressTy, BoolTy, AddressTy],
  208064958: [BoolTy, AddressTy, AddressTy, Uint256Ty],
  1384430956: [BoolTy, AddressTy, AddressTy, Uint256Ty],
  3625099623: [BoolTy, AddressTy, AddressTy, StringTy],
  1180699616: [BoolTy, AddressTy, AddressTy, BoolTy],
  487903233: [BoolTy, AddressTy, AddressTy, AddressTy],
  888202806: [AddressTy, Uint256Ty, Uint256Ty, Uint256Ty],
  1024368100: [AddressTy, Uint256Ty, Uint256Ty, Uint256Ty],
  1244184599: [AddressTy, Uint256Ty, Uint256Ty, StringTy],
  2301889963: [AddressTy, Uint256Ty, Uint256Ty, StringTy],
  1727118439: [AddressTy, Uint256Ty, Uint256Ty, BoolTy],
  3964381346: [AddressTy, Uint256Ty, Uint256Ty, BoolTy],
  551786573: [AddressTy, Uint256Ty, Uint256Ty, AddressTy],
  519451700: [AddressTy, Uint256Ty, Uint256Ty, AddressTy],
  3204577425: [AddressTy, Uint256Ty, StringTy, Uint256Ty],
  4111650715: [AddressTy, Uint256Ty, StringTy, Uint256Ty],
  2292761606: [AddressTy, Uint256Ty, StringTy, StringTy],
  2119616147: [AddressTy, Uint256Ty, StringTy, StringTy],
  3474460764: [AddressTy, Uint256Ty, StringTy, BoolTy],
  2751614737: [AddressTy, Uint256Ty, StringTy, BoolTy],
  1547898183: [AddressTy, Uint256Ty, StringTy, AddressTy],
  3698927108: [AddressTy, Uint256Ty, StringTy, AddressTy],
  586594713: [AddressTy, Uint256Ty, BoolTy, Uint256Ty],
  1770996626: [AddressTy, Uint256Ty, BoolTy, Uint256Ty],
  3316483577: [AddressTy, Uint256Ty, BoolTy, StringTy],
  2391690869: [AddressTy, Uint256Ty, BoolTy, StringTy],
  1005970743: [AddressTy, Uint256Ty, BoolTy, BoolTy],
  4272018778: [AddressTy, Uint256Ty, BoolTy, BoolTy],
  2736520652: [AddressTy, Uint256Ty, BoolTy, AddressTy],
  602229106: [AddressTy, Uint256Ty, BoolTy, AddressTy],
  269444366: [AddressTy, Uint256Ty, AddressTy, Uint256Ty],
  2782496616: [AddressTy, Uint256Ty, AddressTy, Uint256Ty],
  497649386: [AddressTy, Uint256Ty, AddressTy, StringTy],
  1567749022: [AddressTy, Uint256Ty, AddressTy, StringTy],
  2713504179: [AddressTy, Uint256Ty, AddressTy, BoolTy],
  4051804649: [AddressTy, Uint256Ty, AddressTy, BoolTy],
  1200430178: [AddressTy, Uint256Ty, AddressTy, AddressTy],
  3961816175: [AddressTy, Uint256Ty, AddressTy, AddressTy],
  499704248: [AddressTy, StringTy, Uint256Ty, Uint256Ty],
  2764647008: [AddressTy, StringTy, Uint256Ty, Uint256Ty],
  1149776040: [AddressTy, StringTy, Uint256Ty, StringTy],
  1561552329: [AddressTy, StringTy, Uint256Ty, StringTy],
  251125840: [AddressTy, StringTy, Uint256Ty, BoolTy],
  2116357467: [AddressTy, StringTy, Uint256Ty, BoolTy],
  1662531192: [AddressTy, StringTy, Uint256Ty, AddressTy],
  3755464715: [AddressTy, StringTy, Uint256Ty, AddressTy],
  362776871: [AddressTy, StringTy, StringTy, Uint256Ty],
  2706362425: [AddressTy, StringTy, StringTy, Uint256Ty],
  1560462603: [AddressTy, StringTy, StringTy, StringTy],
  900007711: [AddressTy, StringTy, StringTy, BoolTy],
  2689478535: [AddressTy, StringTy, StringTy, AddressTy],
  1365129398: [AddressTy, StringTy, BoolTy, Uint256Ty],
  3877655068: [AddressTy, StringTy, BoolTy, Uint256Ty],
  3154862590: [AddressTy, StringTy, BoolTy, StringTy],
  1595759775: [AddressTy, StringTy, BoolTy, BoolTy],
  542667202: [AddressTy, StringTy, BoolTy, AddressTy],
  1166009295: [AddressTy, StringTy, AddressTy, Uint256Ty],
  2350461865: [AddressTy, StringTy, AddressTy, Uint256Ty],
  4158874181: [AddressTy, StringTy, AddressTy, StringTy],
  233909110: [AddressTy, StringTy, AddressTy, BoolTy],
  221706784: [AddressTy, StringTy, AddressTy, AddressTy],
  946861556: [AddressTy, BoolTy, Uint256Ty, Uint256Ty],
  3255869470: [AddressTy, BoolTy, Uint256Ty, Uint256Ty],
  178704301: [AddressTy, BoolTy, Uint256Ty, StringTy],
  2606272204: [AddressTy, BoolTy, Uint256Ty, StringTy],
  3294903840: [AddressTy, BoolTy, Uint256Ty, BoolTy],
  2244855215: [AddressTy, BoolTy, Uint256Ty, BoolTy],
  3438776481: [AddressTy, BoolTy, Uint256Ty, AddressTy],
  227337758: [AddressTy, BoolTy, Uint256Ty, AddressTy],
  2162598411: [AddressTy, BoolTy, StringTy, Uint256Ty],
  2652011374: [AddressTy, BoolTy, StringTy, Uint256Ty],
  1197235251: [AddressTy, BoolTy, StringTy, StringTy],
  1353532957: [AddressTy, BoolTy, StringTy, BoolTy],
  436029782: [AddressTy, BoolTy, StringTy, AddressTy],
  2353946086: [AddressTy, BoolTy, BoolTy, Uint256Ty],
  3484780374: [AddressTy, BoolTy, BoolTy, Uint256Ty],
  3754205928: [AddressTy, BoolTy, BoolTy, StringTy],
  3401856121: [AddressTy, BoolTy, BoolTy, BoolTy],
  3476636805: [AddressTy, BoolTy, BoolTy, AddressTy],
  2807847390: [AddressTy, BoolTy, AddressTy, Uint256Ty],
  3698398930: [AddressTy, BoolTy, AddressTy, Uint256Ty],
  769095910: [AddressTy, BoolTy, AddressTy, StringTy],
  2801077007: [AddressTy, BoolTy, AddressTy, BoolTy],
  1711502813: [AddressTy, BoolTy, AddressTy, AddressTy],
  3193255041: [AddressTy, AddressTy, Uint256Ty, Uint256Ty],
  1425929188: [AddressTy, AddressTy, Uint256Ty, Uint256Ty],
  4256496016: [AddressTy, AddressTy, Uint256Ty, StringTy],
  2647731885: [AddressTy, AddressTy, Uint256Ty, StringTy],
  2604815586: [AddressTy, AddressTy, Uint256Ty, BoolTy],
  3270936812: [AddressTy, AddressTy, Uint256Ty, BoolTy],
  2376523509: [AddressTy, AddressTy, Uint256Ty, AddressTy],
  3603321462: [AddressTy, AddressTy, Uint256Ty, AddressTy],
  4011651047: [AddressTy, AddressTy, StringTy, Uint256Ty],
  69767936: [AddressTy, AddressTy, StringTy, Uint256Ty],
  566079269: [AddressTy, AddressTy, StringTy, StringTy],
  1863997774: [AddressTy, AddressTy, StringTy, BoolTy],
  2406706454: [AddressTy, AddressTy, StringTy, AddressTy],
  963766156: [AddressTy, AddressTy, BoolTy, Uint256Ty],
  2513854225: [AddressTy, AddressTy, BoolTy, Uint256Ty],
  2858762440: [AddressTy, AddressTy, BoolTy, StringTy],
  752096074: [AddressTy, AddressTy, BoolTy, BoolTy],
  2669396846: [AddressTy, AddressTy, BoolTy, AddressTy],
  2485456247: [AddressTy, AddressTy, AddressTy, Uint256Ty],
  3982404743: [AddressTy, AddressTy, AddressTy, Uint256Ty],
  4161329696: [AddressTy, AddressTy, AddressTy, StringTy],
  238520724: [AddressTy, AddressTy, AddressTy, BoolTy],
  1717301556: [AddressTy, AddressTy, AddressTy, AddressTy],
};
