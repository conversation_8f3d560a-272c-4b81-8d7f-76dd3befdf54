{"name": "@nomicfoundation/hardhat-network-helpers", "version": "1.0.8", "description": "Hardhat utils for testing", "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-network-helpers", "repository": "github:nomicfoundation/hardhat", "author": "Nomic Foundation", "license": "MIT", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "testing"], "scripts": {"lint": "yarn prettier --check && yarn eslint", "lint:fix": "yarn prettier --write && yarn eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "prepublishOnly": "yarn build", "clean": "rimraf dist internal types *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo"}, "files": ["dist/src/", "src/", "internal/", "types/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "dependencies": {"ethereumjs-util": "^7.1.4"}, "devDependencies": {"@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": "^14.0.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "4.29.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^7.29.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.1", "eslint-plugin-no-only-tests": "3.0.0", "eslint-plugin-prettier": "3.4.0", "ethers": "^5.0.0", "hardhat": "^2.9.5", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^8.1.0", "typescript": "~4.5.2"}, "peerDependencies": {"hardhat": "^2.9.5"}}