{"version": 3, "file": "impersonateAccount.js", "sourceRoot": "", "sources": ["../../../src/helpers/impersonateAccount.ts"], "names": [], "mappings": ";;;AAAA,oCAAkE;AAElE;;;;GAIG;AACI,KAAK,UAAU,kBAAkB,CAAC,OAAe;IACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,0BAAkB,GAAE,CAAC;IAE5C,IAAA,0BAAkB,EAAC,OAAO,CAAC,CAAC;IAE5B,MAAM,QAAQ,CAAC,OAAO,CAAC;QACrB,MAAM,EAAE,4BAA4B;QACpC,MAAM,EAAE,CAAC,OAAO,CAAC;KAClB,CAAC,CAAC;AACL,CAAC;AATD,gDASC"}