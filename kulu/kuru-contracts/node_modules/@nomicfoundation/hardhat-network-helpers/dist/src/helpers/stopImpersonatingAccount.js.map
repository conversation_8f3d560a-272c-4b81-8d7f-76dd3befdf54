{"version": 3, "file": "stopImpersonatingAccount.js", "sourceRoot": "", "sources": ["../../../src/helpers/stopImpersonatingAccount.ts"], "names": [], "mappings": ";;;AAAA,oCAAkE;AAElE;;;;GAIG;AACI,KAAK,UAAU,wBAAwB,CAAC,OAAe;IAC5D,MAAM,QAAQ,GAAG,MAAM,IAAA,0BAAkB,GAAE,CAAC;IAE5C,IAAA,0BAAkB,EAAC,OAAO,CAAC,CAAC;IAE5B,MAAM,QAAQ,CAAC,OAAO,CAAC;QACrB,MAAM,EAAE,kCAAkC;QAC1C,MAAM,EAAE,CAAC,OAAO,CAAC;KAClB,CAAC,CAAC;AACL,CAAC;AATD,4DASC"}