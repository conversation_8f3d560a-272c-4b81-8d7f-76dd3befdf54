"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.toPaddedRpcQuantity = exports.assertLargerThan = exports.assertNonNegativeNumber = exports.assertTxHash = exports.assertHexString = exports.assertValidAddress = exports.toRpcQuantity = exports.toBigInt = exports.toNumber = exports.getHardhatProvider = void 0;
const errors_1 = require("./errors");
let cachedIsHardhatNetwork;
async function checkIfHardhatNetwork(provider, networkName) {
    let version;
    if (cachedIsHardhatNetwork === undefined) {
        try {
            version = (await provider.request({
                method: "web3_clientVersion",
            }));
            cachedIsHardhatNetwork = version
                .toLowerCase()
                .startsWith("hardhatnetwork");
        }
        catch (e) {
            cachedIsHardhatNetwork = false;
        }
    }
    if (!cachedIsHardhatNetwork) {
        throw new errors_1.OnlyHardhatNetworkError(networkName, version);
    }
    return cachedIsHardhatNetwork;
}
async function getHardhatProvider() {
    const hre = await Promise.resolve().then(() => __importStar(require("hardhat")));
    const provider = hre.network.provider;
    await checkIfHardhatNetwork(provider, hre.network.name);
    return hre.network.provider;
}
exports.getHardhatProvider = getHardhatProvider;
function toNumber(x) {
    return Number(toRpcQuantity(x));
}
exports.toNumber = toNumber;
function toBigInt(x) {
    return BigInt(toRpcQuantity(x));
}
exports.toBigInt = toBigInt;
function toRpcQuantity(x) {
    let hex;
    if (typeof x === "number" || typeof x === "bigint") {
        // TODO: check that number is safe
        hex = `0x${x.toString(16)}`;
    }
    else if (typeof x === "string") {
        if (!x.startsWith("0x")) {
            throw new errors_1.HardhatNetworkHelpersError("Only 0x-prefixed hex-encoded strings are accepted");
        }
        hex = x;
    }
    else if ("toHexString" in x) {
        hex = x.toHexString();
    }
    else if ("toString" in x) {
        hex = x.toString(16);
    }
    else {
        throw new errors_1.HardhatNetworkHelpersError(`${x} cannot be converted to an RPC quantity`);
    }
    if (hex === "0x0")
        return hex;
    return hex.startsWith("0x") ? hex.replace(/0x0+/, "0x") : `0x${hex}`;
}
exports.toRpcQuantity = toRpcQuantity;
function assertValidAddress(address) {
    const { isValidChecksumAddress, isValidAddress } = require("ethereumjs-util");
    if (!isValidAddress(address)) {
        throw new errors_1.HardhatNetworkHelpersError(`${address} is not a valid address`);
    }
    const hasChecksum = address !== address.toLowerCase();
    if (hasChecksum && !isValidChecksumAddress(address)) {
        throw new errors_1.HardhatNetworkHelpersError(`Address ${address} has an invalid checksum`);
    }
}
exports.assertValidAddress = assertValidAddress;
function assertHexString(hexString) {
    if (typeof hexString !== "string" || !/^0x[0-9a-fA-F]+$/.test(hexString)) {
        throw new errors_1.HardhatNetworkHelpersError(`${hexString} is not a valid hex string`);
    }
}
exports.assertHexString = assertHexString;
function assertTxHash(hexString) {
    assertHexString(hexString);
    if (hexString.length !== 66) {
        throw new errors_1.HardhatNetworkHelpersError(`${hexString} is not a valid transaction hash`);
    }
}
exports.assertTxHash = assertTxHash;
function assertNonNegativeNumber(n) {
    if (n < BigInt(0)) {
        throw new errors_1.HardhatNetworkHelpersError(`Invalid input: expected a non-negative number but ${n} was given.`);
    }
}
exports.assertNonNegativeNumber = assertNonNegativeNumber;
function assertLargerThan(a, b, type) {
    if (a <= b) {
        throw new errors_1.HardhatNetworkHelpersError(`Invalid ${type} ${a} is not larger than current ${type} ${b}`);
    }
}
exports.assertLargerThan = assertLargerThan;
function toPaddedRpcQuantity(x, bytesLength) {
    let rpcQuantity = toRpcQuantity(x);
    if (rpcQuantity.length < 2 + 2 * bytesLength) {
        const rpcQuantityWithout0x = rpcQuantity.slice(2);
        rpcQuantity = `0x${rpcQuantityWithout0x.padStart(2 * bytesLength, "0")}`;
    }
    return rpcQuantity;
}
exports.toPaddedRpcQuantity = toPaddedRpcQuantity;
//# sourceMappingURL=utils.js.map