{"version": 3, "file": "revertedWithCustomError.js", "sourceRoot": "", "sources": ["../../src/internal/reverted/revertedWithCustomError.ts"], "names": [], "mappings": ";;;AAAA,+BAAsC;AAEtC,uCAAgD;AAChD,mCAAmE;AAEtD,QAAA,iCAAiC,GAAG,4BAA4B,CAAC;AAQ9E,SAAgB,8BAA8B,CAC5C,SAA+B,EAC/B,KAAqB;IAErB,SAAS,CAAC,SAAS,CACjB,yBAAyB,EACzB,UAAqB,QAAa,EAAE,uBAA+B;QACjE,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,sEAAsE;QACtE,WAAW;QACX,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,EAAE,SAAS,KAAK,SAAS,EAAE;YACrE,MAAM,IAAI,SAAS,CACjB,mGAAmG,CACpG,CAAC;SACH;QAED,6BAA6B;QAC7B,IAAI,OAAO,uBAAuB,KAAK,QAAQ,EAAE;YAC/C,MAAM,IAAI,SAAS,CAAC,+CAA+C,CAAC,CAAC;SACtE;QAED,MAAM,KAAK,GAAQ,QAAQ,CAAC,SAAS,CAAC;QAEtC,MAAM,mBAAmB,GAAG,qBAAqB,CAC/C,KAAK,EACL,uBAAuB,CACxB,CAAC;QAEF,uDAAuD;QACvD,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,MAAM,IAAI,KAAK,CACb,yDAAyD,uBAAuB,GAAG,CACpF,CAAC;SACH;QAED,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE/C,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,yBAAyB,CAC3G,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,CAAC,KAAU,EAAE,EAAE;YAC7B,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE7C,MAAM,UAAU,GAAG,IAAA,8BAAsB,EAAC,KAAK,CAAC,CAAC;YACjD,MAAM,iBAAiB,GAAG,IAAA,wBAAgB,EAAC,UAAU,CAAC,CAAC;YAEvD,IAAI,iBAAiB,CAAC,IAAI,KAAK,OAAO,EAAE;gBACtC,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,qCAAqC,CACvH,CAAC;aACH;iBAAM,IAAI,iBAAiB,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC7C,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,mCAAmC,iBAAiB,CAAC,MAAM,GAAG,CAChJ,CAAC;aACH;iBAAM,IAAI,iBAAiB,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC7C,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,sCAAsC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,KACzJ,iBAAiB,CAAC,WACpB,GAAG,CACJ,CAAC;aACH;iBAAM,IAAI,iBAAiB,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC9C,IAAI,iBAAiB,CAAC,EAAE,KAAK,mBAAmB,CAAC,EAAE,EAAE;oBACnD,8CAA8C;oBAC9C,MAAM,wBAAwB,GAA6B;wBACzD,iBAAiB,EAAE,KAAK;wBACxB,WAAW,EAAE,mBAAmB;wBAChC,UAAU;qBACX,CAAC;oBACF,IAAI,CAAC,eAAe,GAAG,wBAAwB,CAAC;oBAEhD,MAAM,CACJ,IAAI,EACJ,SAAS,EACT,8DAA8D,uBAAuB,eAAe,CACrG,CAAC;iBACH;qBAAM;oBACL,wCAAwC;oBACxC,mEAAmE;oBACnE,MAAM,iBAAiB,GAAG,mBAAmB,CAC3C,KAAK,EACL,iBAAiB,CAAC,EAAE,CACrB,CAAC;oBAEF,IAAI,iBAAiB,KAAK,SAAS,EAAE;wBACnC,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,kDAAkD,CACpI,CAAC;qBACH;yBAAM;wBACL,MAAM,CACJ,KAAK,EACL,0DAA0D,uBAAuB,yCAAyC,iBAAiB,CAAC,IAAI,GAAG,CACpJ,CAAC;qBACH;iBACF;aACF;iBAAM;gBACL,MAAM,gBAAgB,GAAU,iBAAiB,CAAC;aACnD;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CACpD,SAAS,EACT,OAAO,CACR,CAAC;QAEF,uBAAuB;QACvB,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,yCAAiC,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAE9B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AA5HD,wEA4HC;AAEM,KAAK,UAAU,+BAA+B,CACnD,OAAY,EACZ,SAA+B,EAC/B,KAAqB,EACrB,YAAmB,EACnB,IAAU;IAEV,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,8BAA8B;IACrD,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAE1C,MAAM,wBAAwB,GAC5B,OAAO,CAAC,eAAe,CAAC;IAE1B,IAAI,wBAAwB,KAAK,SAAS,EAAE;QAC1C,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAC;KACH;IAED,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,EAAE,GAClD,wBAAwB,CAAC;IAE3B,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACtE,4EAA4E;IAC5E,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAC3B,iBAAiB,CAAC,iBAAiB,CAAC,aAAa,EAAE,UAAU,CAAC,CAC/D,CAAC;IAEF,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAC3C,YAAY,CAAC,MAAM,EACnB,YAAY,YAAY,CAAC,MAAM,iBAAiB,UAAU,CAAC,MAAM,EAAE,CACpE,CAAC;IAEF,KAAK,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;YACrC,MAAM,WAAW,GAAG,sDAAsD,CAAC,EAAE,CAAC;YAC9E,IAAI;gBACF,MAAM,CACJ,WAAW,CAAC,SAAS,CAAC,EACtB,GAAG,WAAW,iBAAiB;gBAC/B,qEAAqE;gBACrE,YAAY;iBACb,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,YAAY,qBAAc,EAAE;oBAC/B,MAAM,CACJ,KAAK,EACL,GAAG,WAAW,6BAA6B,CAAC,CAAC,OAAO,EAAE;oBACtD,qEAAqE;oBACrE,YAAY;qBACb,CAAC;iBACH;gBACD,MAAM,CAAC,CAAC;aACT;SACF;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACrC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SACrD;aAAM;YACL,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAChD;KACF;AACH,CAAC;AA7DD,0EA6DC;AAQD,SAAS,qBAAqB,CAC5B,KAAU,EACV,IAAY;IAEZ,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAEjC,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CACxD,CAAC,CAAC,EAAE,QAAQ,CAAM,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,CAC9C,CAAC;IAEF,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,CAAC,oBAAoB,CAAC,GAAG,gBAAgB,CAAC;IAChD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEzE,OAAO;QACL,EAAE,EAAE,aAAa;QACjB,IAAI;QACJ,SAAS,EAAE,oBAAoB;KAChC,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAU,EAAE,EAAU;IACjD,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAEjC,MAAM,gBAAgB,GAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAC7D,CAAC,CAAC,SAAS,CAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,CACrE,CAAC;IAEF,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO;QACL,EAAE;QACF,IAAI,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI;QAC9B,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC;KAC/B,CAAC;AACJ,CAAC"}