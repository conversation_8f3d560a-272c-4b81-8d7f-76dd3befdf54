{"version": 3, "file": "changeTokenBalance.js", "sourceRoot": "", "sources": ["../src/internal/changeTokenBalance.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA,oCAAuC;AACvC,oDAAkD;AAClD,4CAAuD;AAQvD,SAAgB,yBAAyB,CAAC,SAA+B;IACvE,SAAS,CAAC,SAAS,CACjB,oBAAoB,EACpB,UAEE,KAAY,EACZ,OAAyB,EACzB,aAAmC;QAEnC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QAEnD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,GAAG,OAAO,EAAE,CAAC;SACrB;QAED,UAAU,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAExC,MAAM,kBAAkB,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAInE,EAAE,EAAE;YACH,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAExD,MAAM,CACJ,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EACrD,2BAA2B,gBAAgB,gBAAgB,OAAO,kBAAkB,aAAa,CAAC,QAAQ,EAAE,uBAAuB,YAAY,CAAC,QAAQ,EAAE,EAAE,EAC5J,2BAA2B,gBAAgB,gBAAgB,OAAO,sBAAsB,aAAa,CAAC,QAAQ,EAAE,cAAc,CAC/H,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;YACzC,IAAA,sBAAY,EAAC,OAAO,CAAC;YACrB,mBAAmB,CAAC,KAAK,CAAC;SAC3B,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE5B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;IAEF,SAAS,CAAC,SAAS,CACjB,qBAAqB,EACrB,UAEE,KAAY,EACZ,QAAiC,EACjC,cAAsC;QAEtC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QAEnD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,GAAG,OAAO,EAAE,CAAC;SACrB;QAED,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;QAEzC,IAAI,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE;YAC7C,MAAM,IAAI,KAAK,CACb,2BAA2B,QAAQ,CAAC,MAAM,+DAA+D,cAAc,CAAC,MAAM,GAAG,CAClI,CAAC;SACH;QAED,MAAM,qBAAqB,GAAG,OAAO,CAAC,GAAG,CACvC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CACrE,CAAC;QACF,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,sBAAY,CAAC,CAAC,CAAC;QAEjE,MAAM,mBAAmB,GAAG,CAAC,CAC3B,aAAa,EACb,SAAS,EACT,gBAAgB,EACwB,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;YAEzD,MAAM,CACJ,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAClC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CACtD,EACD,4BAA4B,gBAAgB,eAC1C,SACF,iBACE,cACF,uCAAuC,aAAoB,EAAE,EAC7D,4BAA4B,gBAAgB,eAC1C,SACF,qBACE,cACF,8BAA8B,CAC/B,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,qBAAqB;YACrB,gBAAgB;YAChB,mBAAmB,CAAC,KAAK,CAAC;SAC3B,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AAnHD,8DAmHC;AAED,SAAS,UAAU,CAAC,KAAc,EAAE,MAAc;IAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,WAAW,IAAI,KAAK,CAAC,EAAE;QAC1E,MAAM,IAAI,KAAK,CACb,yBAAyB,MAAM,6CAA6C,CAC7E,CAAC;KACH;SAAM,IAAK,KAAa,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;AACH,CAAC;AAEM,KAAK,UAAU,gBAAgB,CACpC,WAA+D,EAC/D,KAAY,EACZ,OAAyB;IAEzB,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;IACnD,MAAM,GAAG,GAAG,wDAAa,SAAS,GAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;IAEtC,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;IAErC,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;IAC1C,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;IAE5C,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE;QACtD,SAAS,CAAC,SAAS;QACnB,KAAK;KACN,CAAC,CAAC;IAEH,IAAA,cAAM,EACJ,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAC/B,KAAK,EACL,sCAAsC,CACvC,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QAClD,QAAQ,EAAE,aAAa;KACxB,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QACnD,QAAQ,EAAE,aAAa,GAAG,CAAC;KAC5B,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAChE,CAAC;AApCD,4CAoCC;AAED,IAAI,sBAAsB,GAA2B,EAAE,CAAC;AACxD;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAAC,KAAY;IAC7C,IAAI,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,SAAS,EAAE;QACvD,IAAI,gBAAgB,GAAG,aAAa,KAAK,CAAC,OAAO,GAAG,CAAC;QACrD,IAAI;YACF,gBAAgB,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;SACzC;QAAC,OAAO,CAAC,EAAE;YACV,IAAI;gBACF,gBAAgB,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;aACvC;YAAC,OAAO,EAAE,EAAE,GAAE;SAChB;QAED,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC;KAC1D;IAED,OAAO,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,CAAC;AAED,qBAAqB;AACrB,SAAgB,2BAA2B;IACzC,sBAAsB,GAAG,EAAE,CAAC;AAC9B,CAAC;AAFD,kEAEC"}