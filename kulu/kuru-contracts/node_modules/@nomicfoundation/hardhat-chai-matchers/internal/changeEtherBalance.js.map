{"version": 3, "file": "changeEtherBalance.js", "sourceRoot": "", "sources": ["../src/internal/changeEtherBalance.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA,oCAAuC;AACvC,oDAAkD;AAClD,4CAAuD;AAGvD,SAAgB,yBAAyB,CAAC,SAA+B;IACvE,SAAS,CAAC,SAAS,CACjB,oBAAoB,EACpB,UAEE,OAAyB,EACzB,aAA2B,EAC3B,OAA8B;QAE9B,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAExC,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAE1B,MAAM,kBAAkB,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,CAGjD,EAAE,EAAE;YACH,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAExD,MAAM,CACJ,YAAY,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAC9C,kCAAkC,OAAO,kBAAkB,aAAa,CAAC,QAAQ,EAAE,2BAA2B,YAAY,CAAC,QAAQ,EAAE,MAAM,EAC3I,kCAAkC,OAAO,sBAAsB,aAAa,CAAC,QAAQ,EAAE,kBAAkB,CAC1G,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;YAC3C,IAAA,sBAAY,EAAC,OAAO,CAAC;SACtB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AAtCD,8DAsCC;AAEM,KAAK,UAAU,gBAAgB,CACpC,WAKsC,EACtC,OAAyB,EACzB,OAA8B;IAE9B,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAC7C,MAAM,GAAG,GAAG,wDAAa,SAAS,GAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;IAEtC,IAAI,UAAyC,CAAC;IAE9C,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QACrC,UAAU,GAAG,MAAM,WAAW,EAAE,CAAC;KAClC;SAAM;QACL,UAAU,GAAG,MAAM,WAAW,CAAC;KAChC;IAED,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;IAC1C,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;IAE5C,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE;QACtD,SAAS,CAAC,SAAS;QACnB,KAAK;KACN,CAAC,CAAC;IAEH,IAAA,cAAM,EACJ,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAC/B,KAAK,EACL,sCAAsC,CACvC,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACzD,OAAO;QACP,KAAK,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;KAClC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAC1D,OAAO;QACP,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;KACxC,CAAC,CAAC;IAEH,IAAI,OAAO,EAAE,UAAU,KAAK,IAAI,IAAI,OAAO,KAAK,UAAU,CAAC,IAAI,EAAE;QAC/D,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,IAAI,UAAU,CAAC,QAAQ,CAAC;QACpE,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEpC,OAAO,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KACnE;SAAM;QACL,OAAO,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KACxD;AACH,CAAC;AAzDD,4CAyDC"}