/// <reference types="chai" />
import type { BigNumber, providers } from "ethers";
import { Account } from "./misc/account";
import { BalanceChangeOptions } from "./misc/balance";
export declare function supportChangeEtherBalances(Assertion: Chai.AssertionStatic): void;
export declare function getBalanceChanges(transaction: providers.TransactionResponse | Promise<providers.TransactionResponse>, accounts: Array<Account | string>, options?: BalanceChangeOptions): Promise<BigNumber[]>;
//# sourceMappingURL=changeEtherBalances.d.ts.map