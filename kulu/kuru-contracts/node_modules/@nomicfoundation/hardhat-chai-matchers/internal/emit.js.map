{"version": 3, "file": "emit.js", "sourceRoot": "", "sources": ["../src/internal/emit.ts"], "names": [], "mappings": ";;;;;;AAMA,+BAAsC;AACtC,gDAAwB;AACxB,sDAA8B;AAE9B,oCAA6D;AAMhD,QAAA,WAAW,GAAG,qBAAqB,CAAC;AAEjD,KAAK,UAAU,yBAAyB,CACtC,EAA+C,EAC/C,QAAkB;IAElB,IAAI,IAAwB,CAAC;IAC7B,IAAI,EAAE,YAAY,OAAO,EAAE;QACzB,CAAC,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;KACvB;SAAM,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QACjC,IAAI,GAAG,EAAE,CAAC;KACX;SAAM;QACL,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;KACjB;IACD,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,CAAC;KACrE;IACD,OAAO,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,WAAW,CACzB,SAA+B,EAC/B,SAAyB;IAEzB,SAAS,CAAC,SAAS,CACjB,MAAM,EACN,UAAqB,QAAkB,EAAE,SAAiB;QACxD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAErB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnE,MAAM,SAAS,GAAG,CAAC,OAAqC,EAAE,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE/C,IAAI,aAAwC,CAAC;YAC7C,IAAI;gBACF,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;aACxD;YAAC,OAAO,CAAC,EAAE;gBACV,eAAe;aAChB;YAED,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,MAAM,IAAI,qBAAc,CACtB,UAAU,SAAS,iCAAiC,CACrD,CAAC;aACH;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;iBACrB,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAC3C,MAAM,CACL,CAAC,GAAG,EAAE,EAAE,CACN,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAC/D,CAAC;YAEJ,MAAM,CACJ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACpB,mBAAmB,SAAS,gCAAgC,EAC5D,mBAAmB,SAAS,iCAAiC,CAC9D,CAAC;YACF,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7C,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO;aAC3B,IAAI,CAAC,GAAG,EAAE,CAAC,yBAAyB,CAAC,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAC5D,IAAI,CAAC,SAAS,CAAC,CAAC;QAEnB,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAW,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AA1DD,kCA0DC;AAEM,KAAK,UAAU,YAAY,CAChC,OAAY,EACZ,SAA+B,EAC/B,SAAyB,EACzB,YAAmB,EACnB,IAAU;IAEV,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,8BAA8B;IACrD,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAE1C,wBAAwB,CACtB,OAAO,EACP,SAAS,EACT,SAAS,EACT,YAAY,EACZ,OAAO,CAAC,IAAI,EACZ,MAAM,EACN,IAAI,CACL,CAAC;AACJ,CAAC;AAnBD,oCAmBC;AAED,SAAS,qBAAqB,CAC5B,OAAY,EACZ,SAA+B,EAC/B,SAAyB,EACzB,YAAmB,EACnB,GAAQ,EACR,MAAsB,EACtB,IAAU;IAEV,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAkC,CAAC;IAErE,MAAM,UAAU,GACd,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,SACrC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;IACrB,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACvD,MAAM,CACJ,UAAU,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EACzC,aAAa,SAAS,mBAAmB,YAAY,CAAC,MAAM,4BAA4B,UAAU,CAAC,MAAM,EAAE,CAC5G,CAAC;IACF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACxD,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE;YAC7C,MAAM,WAAW,GAAG,yBAAyB,IAAA,iBAAO,EAClD,KAAK,GAAG,CAAC,CACV,iBAAiB,CAAC;YACnB,IAAI;gBACF,MAAM,CACJ,YAAY,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EACtC,GAAG,WAAW,iBAAiB;gBAC/B,qEAAqE;gBACrE,YAAY;iBACb,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,YAAY,qBAAc,EAAE;oBAC/B,MAAM,CACJ,KAAK,EACL,GAAG,WAAW,6BAA6B,CAAC,CAAC,OAAO,EAAE;oBACtD,qEAAqE;oBACrE,YAAY;qBACb,CAAC;iBACH;gBACD,MAAM,CAAC,CAAC;aACT;SACF;aAAM,IAAI,YAAY,CAAC,KAAK,CAAC,YAAY,UAAU,EAAE;YACpD,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,CAC3D,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CACnC,CAAC;SACH;aAAM,IACL,YAAY,CAAC,KAAK,CAAC,EAAE,MAAM,KAAK,SAAS;YACzC,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,QAAQ,EACvC;YACA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,CAC9D,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACvB,CAAC;aACH;SACF;aAAM;YACL,IACE,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,SAAS;gBACpC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,KAAK,IAAI,EACrC;gBACA,IAAI,SAAS,CACX,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,EACtB,SAAS,EACT,IAAI,EACJ,IAAI,CACL,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CACZ,YAAY,CAAC,KAAK,CAAC,EACnB,sSAAsS,CACvS,CAAC;gBACF,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7D,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBACrC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC3C,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;gBACvD,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CACnE,YAAY,EACZ,8IAA8I,YAAY,uDAAuD,CAClN,CAAC;aACH;iBAAM;gBACL,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,CAC3D,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC;aACH;SACF;KACF;AACH,CAAC;AAED,MAAM,wBAAwB,GAAG,CAC/B,OAAY,EACZ,SAA+B,EAC/B,SAAyB,EACzB,YAAmB,EACnB,IAAW,EACX,MAAsB,EACtB,IAAU,EACV,EAAE;IACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QACnB,OAAO,qBAAqB,CAC1B,OAAO,EACP,SAAS,EACT,SAAS,EACT,YAAY,EACZ,IAAI,CAAC,CAAC,CAAC,EACP,MAAM,EACN,IAAI,CACL,CAAC;IACJ,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;QACxB,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM;SACP;aAAM;YACL,IAAI;gBACF,qBAAqB,CACnB,OAAO,EACP,SAAS,EACT,SAAS,EACT,YAAY,EACZ,IAAI,CAAC,KAAK,CAAC,EACX,MAAM,EACN,IAAI,CACL,CAAC;gBACF,OAAO;aACR;YAAC,MAAM,GAAE;SACX;KACF;IACD,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACvD,MAAM,CACJ,KAAK,EACL,4BAA4B,cAAI,CAAC,OAAO,CACtC,YAAY,CACb,qCACC,OAAO,CAAC,IAAI,CAAC,MACf,aAAa,SAAS,UAAU,CACjC,CAAC;AACJ,CAAC,CAAC"}