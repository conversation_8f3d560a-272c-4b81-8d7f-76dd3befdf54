"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supportWithArgs = exports.anyUint = exports.anyValue = void 0;
const chai_1 = require("chai");
const common_1 = require("hardhat/common");
const emit_1 = require("./emit");
const revertedWithCustomError_1 = require("./reverted/revertedWithCustomError");
/**
 * A predicate for use with .withArgs(...), to induce chai to accept any value
 * as a positive match with the argument.
 *
 * Example: expect(contract.emitInt()).to.emit(contract, "Int").withArgs(anyValue)
 */
function anyValue() {
    return true;
}
exports.anyValue = anyValue;
/**
 * A predicate for use with .withArgs(...), to induce chai to accept any
 * unsigned integer as a positive match with the argument.
 *
 * Example: expect(contract.emitUint()).to.emit(contract, "Uint").withArgs(anyUint)
 */
function anyUint(i) {
    if (typeof i === "number") {
        if (i < 0) {
            throw new chai_1.AssertionError(`anyUint expected its argument to be an unsigned integer, but it was negative, with value ${i}`);
        }
        return true;
    }
    else if ((0, common_1.isBigNumber)(i)) {
        const bigInt = (0, common_1.normalizeToBigInt)(i);
        if (bigInt < 0) {
            throw new chai_1.AssertionError(`anyUint expected its argument to be an unsigned integer, but it was negative, with value ${bigInt}`);
        }
        return true;
    }
    throw new chai_1.AssertionError(`anyUint expected its argument to be an integer, but its type was '${typeof i}'`);
}
exports.anyUint = anyUint;
function supportWithArgs(Assertion, utils) {
    Assertion.addMethod("withArgs", function (...expectedArgs) {
        if (Boolean(this.__flags.negate)) {
            throw new Error("Do not combine .not. with .withArgs()");
        }
        const emitCalled = utils.flag(this, emit_1.EMIT_CALLED) === true;
        const revertedWithCustomErrorCalled = utils.flag(this, revertedWithCustomError_1.REVERTED_WITH_CUSTOM_ERROR_CALLED) === true;
        if (!emitCalled && !revertedWithCustomErrorCalled) {
            throw new Error("withArgs can only be used in combination with a previous .emit or .revertedWithCustomError assertion");
        }
        if (emitCalled && revertedWithCustomErrorCalled) {
            throw new Error("withArgs called with both .emit and .revertedWithCustomError, but these assertions cannot be combined");
        }
        const promise = this.then === undefined ? Promise.resolve() : this;
        const onSuccess = () => {
            if (emitCalled) {
                return (0, emit_1.emitWithArgs)(this, Assertion, utils, expectedArgs, onSuccess);
            }
            else {
                return (0, revertedWithCustomError_1.revertedWithCustomErrorWithArgs)(this, Assertion, utils, expectedArgs, onSuccess);
            }
        };
        const derivedPromise = promise.then(onSuccess);
        this.then = derivedPromise.then.bind(derivedPromise);
        this.catch = derivedPromise.catch.bind(derivedPromise);
        return this;
    });
}
exports.supportWithArgs = supportWithArgs;
//# sourceMappingURL=withArgs.js.map