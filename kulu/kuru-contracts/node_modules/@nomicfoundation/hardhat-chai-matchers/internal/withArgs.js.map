{"version": 3, "file": "withArgs.js", "sourceRoot": "", "sources": ["../src/internal/withArgs.ts"], "names": [], "mappings": ";;;AAAA,+BAAsC;AAEtC,2CAAgE;AAEhE,iCAAmD;AACnD,gFAG4C;AAE5C;;;;;GAKG;AACH,SAAgB,QAAQ;IACtB,OAAO,IAAI,CAAC;AACd,CAAC;AAFD,4BAEC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,CAAM;IAC5B,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,MAAM,IAAI,qBAAc,CACtB,4FAA4F,CAAC,EAAE,CAChG,CAAC;SACH;QACD,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,IAAA,oBAAW,EAAC,CAAC,CAAC,EAAE;QACzB,MAAM,MAAM,GAAG,IAAA,0BAAiB,EAAC,CAAC,CAAC,CAAC;QACpC,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,MAAM,IAAI,qBAAc,CACtB,4FAA4F,MAAM,EAAE,CACrG,CAAC;SACH;QACD,OAAO,IAAI,CAAC;KACb;IACD,MAAM,IAAI,qBAAc,CACtB,qEAAqE,OAAO,CAAC,GAAG,CACjF,CAAC;AACJ,CAAC;AApBD,0BAoBC;AAED,SAAgB,eAAe,CAC7B,SAA+B,EAC/B,KAAqB;IAErB,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,UAAqB,GAAG,YAAmB;QACzE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,kBAAW,CAAC,KAAK,IAAI,CAAC;QAC1D,MAAM,6BAA6B,GACjC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,2DAAiC,CAAC,KAAK,IAAI,CAAC;QAE/D,IAAI,CAAC,UAAU,IAAI,CAAC,6BAA6B,EAAE;YACjD,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAC;SACH;QACD,IAAI,UAAU,IAAI,6BAA6B,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAC;SACH;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnE,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,UAAU,EAAE;gBACd,OAAO,IAAA,mBAAY,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;aACtE;iBAAM;gBACL,OAAO,IAAA,yDAA+B,EACpC,IAAI,EACJ,SAAS,EACT,KAAK,EACL,YAAY,EACZ,SAAS,CACV,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AA9CD,0CA8CC"}