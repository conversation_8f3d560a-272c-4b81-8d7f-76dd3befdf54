{"name": "@nomicfoundation/hardhat-chai-matchers", "version": "1.0.6", "description": "Hardhat utils for testing", "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-chai-matchers", "repository": "github:nomicfoundation/hardhat", "author": "Nomic Foundation", "license": "MIT", "main": "index.js", "types": "index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "testing"], "scripts": {"lint": "yarn prettier --check && yarn eslint", "lint:fix": "yarn prettier --write && yarn eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "test": "mocha --recursive \"test/**/*.ts\" --exit --reporter dot", "test:ci": "yarn test && node scripts/check-subpath-exports.js", "build": "tsc --build .", "prepublishOnly": "yarn build", "clean": "rimraf dist internal types *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo"}, "files": ["src/", "internal/", "types/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.0", "@types/bn.js": "^5.1.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": "^14.0.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "4.29.2", "bignumber.js": "^9.0.2", "bn.js": "^5.1.0", "chai": "^4.2.0", "eslint": "^7.29.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.1", "eslint-plugin-no-only-tests": "3.0.0", "eslint-plugin-prettier": "3.4.0", "ethers": "^5.0.0", "get-port": "^5.1.1", "hardhat": "^2.9.4", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^8.1.0", "typescript": "~4.5.2"}, "peerDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.0", "chai": "^4.2.0", "ethers": "^5.0.0", "hardhat": "^2.9.4"}, "dependencies": {"@ethersproject/abi": "^5.1.2", "@types/chai-as-promised": "^7.1.3", "chai-as-promised": "^7.1.1", "deep-eql": "^4.0.1", "ordinal": "^1.0.3"}}