{"name": "@nomicfoundation/ethereumjs-tx", "version": "5.0.4", "description": "Implementation of the various Ethereum Transaction Types", "keywords": ["ethereum", "transactions"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/tx#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+tx%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "mjbecze <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "additions": 27562, "contributions": 22, "deletions": 42613, "hireable": true}], "type": "commonjs", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "DEBUG=ethjs npx vitest run --coverage.enabled --coverage.reporter=lcov", "docs:build": "typedoc --options typedoc.cjs", "examples": "tsx ../../scripts/examples-runner.ts -- tx", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "test": "npm run test:node && npm run test:browser", "test:browser": "npx vitest run --config=./vitest.config.browser.ts --browser.name=chrome --browser.headless", "test:node": "npx vitest run", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@nomicfoundation/ethereumjs-common": "4.0.4", "@nomicfoundation/ethereumjs-rlp": "5.0.4", "@nomicfoundation/ethereumjs-util": "9.0.4", "ethereum-cryptography": "0.1.3"}, "peerDependencies": {"c-kzg": "^2.1.2"}, "peerDependenciesMeta": {"c-kzg": {"optional": true}}, "devDependencies": {"@types/minimist": "^1.2.0", "@types/node-dir": "^0.0.34", "minimist": "^1.2.0", "node-dir": "^0.1.16"}, "engines": {"node": ">=18"}}