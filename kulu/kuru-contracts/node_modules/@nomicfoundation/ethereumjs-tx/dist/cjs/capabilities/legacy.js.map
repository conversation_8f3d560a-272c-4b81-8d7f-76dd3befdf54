{"version": 3, "file": "legacy.js", "sourceRoot": "", "sources": ["../../../src/capabilities/legacy.ts"], "names": [], "mappings": ";;;AAAA,sEAIyC;AACzC,+DAA8E;AAE9E,8DAAuD;AACvD,0CAAwC;AAIxC,SAAS,SAAS,CAAC,GAAe;IAChC,OAAO,IAAI,UAAU,CAAC,IAAA,qBAAe,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AAC1D,CAAC;AAED,SAAgB,QAAQ,CAAC,EAAqB,EAAE,GAAW;IACzD,OAAO,GAAG,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAA;AACpC,CAAC;AAFD,4BAEC;AAED,SAAgB,QAAQ,CAAC,EAAqB;IAC5C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAA;IACtB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;SAAM;QACL,OAAO,IAAI,CAAA;KACZ;AACH,CAAC;AAPD,4BAOC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,EAAqB,EAAE,SAAkB;IAClE,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;QAC1E,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;KAC9B;IAED,MAAM,IAAI,GAAG,oCAAe,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAA;IAEhF,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACvB,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG;YACjB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE;SAC/B,CAAA;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAfD,gCAeC;AAED,SAAgB,IAAI,CAAC,EAAqB;IACxC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;QAClB,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,EAAE,sDAAsD,CAAC,CAAA;QAChF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;KACrB;IAED,MAAM,cAAc,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,SAAS,CAAA;IAEpE,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACvB,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE;YAClB,EAAE,CAAC,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAA;SAC/C;QACD,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAA;KACrB;IAED,OAAO,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAA;AACvC,CAAC;AAhBD,oBAgBC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,EAAqB;IACjD,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAA;IAChB,IAAI,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,GAAG,uCAAqB,EAAE;QACtF,MAAM,GAAG,GAAG,QAAQ,CAClB,EAAE,EACF,8EAA8E,CAC/E,CAAA;QACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;KACrB;AACH,CAAC;AATD,sCASC;AAED,SAAgB,kBAAkB,CAAC,EAAqB;IACtD,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,KAAK,SAAS,EAAE;QACvC,OAAO,EAAE,CAAC,KAAK,CAAC,YAAY,CAAA;KAC7B;IAED,MAAM,OAAO,GAAG,EAAE,CAAC,2BAA2B,EAAE,CAAA;IAEhD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAA;IAEtB,aAAa,CAAC,EAAE,CAAC,CAAA;IAEjB,IAAI;QACF,MAAM,iBAAiB,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,2BAAS,CAAA;QACvE,MAAM,MAAM,GAAG,iBAAiB,CAC9B,OAAO,EACP,CAAE,EACF,IAAA,uCAAqB,EAAC,CAAE,CAAC,EACzB,IAAA,uCAAqB,EAAC,CAAE,CAAC,EACzB,EAAE,CAAC,QAAQ,CAAC,qBAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CACjF,CAAA;QACD,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;YACvB,EAAE,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAA;SAC/B;QACD,OAAO,MAAM,CAAA;KACd;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAA;QAC7C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;KACrB;AACH,CAAC;AA5BD,gDA4BC"}