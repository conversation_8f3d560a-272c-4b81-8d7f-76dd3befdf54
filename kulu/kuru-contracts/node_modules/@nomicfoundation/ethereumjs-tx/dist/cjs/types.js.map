{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAAA,sEAAyE;AAoBzE;;;GAGG;AACH,IAAY,UAwBX;AAxBD,WAAY,UAAU;IACpB;;;OAGG;IACH,iFAA4B,CAAA;IAE5B;;;OAGG;IACH,sEAAuB,CAAA;IAEvB;;;OAGG;IACH,oFAA8B,CAAA;IAE9B;;;OAGG;IACH,0EAAyB,CAAA;AAC3B,CAAC,EAxBW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAwBrB;AAqCD,SAAgB,iBAAiB,CAAC,KAAmC;IACnE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,CAAA;KACZ;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AATD,8CASC;AAED,SAAgB,YAAY,CAAC,KAAmC;IAC9D,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA,CAAC,iEAAiE;AACpG,CAAC;AAFD,oCAEC;AAYD;;GAEG;AACH,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,yDAAU,CAAA;IACV,+EAAqB,CAAA;IACrB,6EAAoB,CAAA;IACpB,mEAAe,CAAA;AACjB,CAAC,EALW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAK1B;AAWD,SAAgB,UAAU,CAAC,EAAoB;IAC7C,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,CAAA;AAC3C,CAAC;AAFD,gCAEC;AAED,SAAgB,qBAAqB,CAAC,EAAoB;IACxD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACtD,CAAC;AAFD,sDAEC;AAED,SAAgB,oBAAoB,CAAC,EAAoB;IACvD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACrD,CAAC;AAFD,oDAEC;AAED,SAAgB,eAAe,CAAC,EAAoB;IAClD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,WAAW,CAAA;AAChD,CAAC;AAFD,0CAEC;AA6ED,SAAgB,cAAc,CAAC,MAAmB;IAChD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,+BAAa,EAAC,IAAA,yBAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,MAAM,CAAA;AAC1C,CAAC;AAHD,wCAGC;AAED,SAAgB,yBAAyB,CAAC,MAAmB;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,+BAAa,EAAC,IAAA,yBAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACrD,CAAC;AAHD,8DAGC;AAED,SAAgB,wBAAwB,CAAC,MAAmB;IAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,+BAAa,EAAC,IAAA,yBAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACpD,CAAC;AAHD,4DAGC;AAED,SAAgB,mBAAmB,CAAC,MAAmB;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,+BAAa,EAAC,IAAA,yBAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,WAAW,CAAA;AAC/C,CAAC;AAHD,kDAGC"}