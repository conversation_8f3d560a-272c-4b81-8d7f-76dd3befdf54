{"version": 3, "file": "legacyTransaction.js", "sourceRoot": "", "sources": ["../../src/legacyTransaction.ts"], "names": [], "mappings": ";;;AAAA,oEAAqD;AACrD,sEAUyC;AACzC,+DAA8E;AAE9E,6DAAsD;AACtD,mDAAkD;AAClD,yCAAwD;AAUxD,SAAS,SAAS,CAAC,GAAe;IAChC,OAAO,IAAI,UAAU,CAAC,IAAA,qBAAe,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AAC1D,CAAC;AAKD,SAAS,WAAW,CAAC,EAAU,EAAE,OAAe;IAC9C,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAA;IACpB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAC1C,OAAO,CAAC,KAAK,cAAc,GAAG,EAAE,IAAI,CAAC,KAAK,cAAc,GAAG,EAAE,CAAA;AAC/D,CAAC;AAED;;GAEG;AACH,MAAa,iBAAkB,SAAQ,oCAAuC;IAmE5E;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,0BAAe,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAA;QAExD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,SAAS,CAAA;QACrE,IAAI,CAAC,QAAQ,GAAG,IAAA,+BAAa,EAAC,IAAA,yBAAO,EAAC,MAAM,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEvF,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,6BAAW,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAA;YACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACjE,oCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAU,CAAC,sBAAsB,CAAC,CAAA;aAChE;iBAAM;gBACL,eAAe;gBACf,kFAAkF;gBAClF,sFAAsF;gBACtF,mGAAmG;gBACnG,oEAAoE;gBACpE,yCAAyC;gBACzC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;oBAC/C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAU,CAAC,sBAAsB,CAAC,CAAA;iBAChE;aACF;SACF;QAED,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAvGD;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QACzE,MAAM,MAAM,GAAG,oBAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QAErC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAuB,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,2GAA2G;QAC3G,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;SACF;QAED,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEpE,IAAA,yCAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,OAAO,IAAI,iBAAiB,CAC1B;YACE,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,CAAC;YACD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA8CD;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,uCAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAA,uCAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAA,uCAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,IAAA,uCAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,uCAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,uCAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,uCAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SACzE,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,SAAS;QACP,OAAO,oBAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC/B,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB;QACd,MAAM,OAAO,GAAG;YACd,IAAA,uCAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAA,uCAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAA,uCAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,IAAA,uCAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;SACV,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,OAAO,CAAC,IAAI,CAAC,IAAA,uCAAqB,EAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YAC1D,OAAO,CAAC,IAAI,CAAC,IAAA,4BAAU,EAAC,IAAA,yBAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACpC,OAAO,CAAC,IAAI,CAAC,IAAA,4BAAU,EAAC,IAAA,yBAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACrC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvC,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;IACnD,CAAC;IAED;;;;;OAKG;IACH,IAAI;QACF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,CAAS,EAAE,CAAa,EAAE,CAAa;QACjE,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,0BAAQ,GAAG,0BAAQ,CAAA;SACjD;QAED,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,iBAAiB,CAAC,UAAU,CACjC;YACE,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,CAAC;YACD,CAAC,EAAE,IAAA,+BAAa,EAAC,CAAC,CAAC;YACnB,CAAC,EAAE,IAAA,+BAAa,EAAC,CAAC,CAAC;SACpB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QAC/B,OAAO;YACL,GAAG,QAAQ;YACX,QAAQ,EAAE,IAAA,6BAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;SACrC,CAAA;IACH,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,EAAW,EAAE,MAAe;QACjD,IAAI,aAAa,CAAA;QACjB,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACnD,8DAA8D;QAC9D,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;gBAClC,MAAM,IAAI,KAAK,CACb,oFAAoF,CAAC,EAAE,CACxF,CAAA;aACF;SACF;QAED,6DAA6D;QAC7D,IACE,CAAC,KAAK,SAAS;YACf,CAAC,KAAK,CAAC;YACP,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACjD,CAAC,KAAK,EAAE;YACR,CAAC,KAAK,EAAE,EACR;YACA,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;oBAC7C,MAAM,IAAI,KAAK,CACb,+BAA+B,CAAC,iBAAiB,MAAM,CAAC,OAAO,EAAE,gFAAgF,CAClJ,CAAA;iBACF;aACF;iBAAM;gBACL,+BAA+B;gBAC/B,IAAI,MAAM,CAAA;gBACV,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACtB,MAAM,GAAG,EAAE,CAAA;iBACZ;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,iDAAiD;gBACjD,aAAa,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,0BAAQ,CAAA;aAC9C;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAA;QACxC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;CACF;AA/UD,8CA+UC"}