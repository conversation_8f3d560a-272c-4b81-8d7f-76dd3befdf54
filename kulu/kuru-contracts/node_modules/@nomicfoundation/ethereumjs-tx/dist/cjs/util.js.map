{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": ";;;AAAA,sEAAwF;AAExF,yCAAyC;AAKzC,SAAgB,oBAAoB,CAAC,MAAc,EAAE,MAAc;IACjE,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;IAC7D,IAAI,eAAe,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE;QACvD,MAAM,IAAI,KAAK,CACb,6DAA6D,MAAM,qBAAqB,MAAM,CAAC,KAAK,CAClG,IAAI,EACJ,iBAAiB,CAClB,EAAE,CACJ,CAAA;KACF;AACH,CAAC;AAVD,oDAUC;AAED,MAAa,WAAW;IACf,MAAM,CAAC,iBAAiB,CAAC,UAAwC;QACtE,IAAI,cAAc,CAAA;QAClB,IAAI,gBAAgB,CAAA;QACpB,IAAI,IAAA,uBAAY,EAAC,UAAU,CAAC,EAAE;YAC5B,cAAc,GAAG,UAAU,CAAA;YAC3B,MAAM,aAAa,GAAoB,EAAE,CAAA;YAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,IAAI,GAAmB,UAAU,CAAC,CAAC,CAAC,CAAA;gBAC1C,MAAM,YAAY,GAAG,IAAA,4BAAU,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7C,MAAM,YAAY,GAAiB,EAAE,CAAA;gBACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC5D,YAAY,CAAC,IAAI,CAAC,IAAA,4BAAU,EAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;iBACvD;gBACD,aAAa,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;aACjD;YACD,gBAAgB,GAAG,aAAa,CAAA;SACjC;aAAM;YACL,gBAAgB,GAAG,UAAU,IAAI,EAAE,CAAA;YACnC,iBAAiB;YACjB,MAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;gBAChC,MAAM,OAAO,GAAG,IAAA,4BAAU,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,WAAW,GAAa,EAAE,CAAA;gBAChC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;oBAChD,WAAW,CAAC,IAAI,CAAC,IAAA,4BAAU,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAC5C;gBACD,MAAM,QAAQ,GAAmB;oBAC/B,OAAO;oBACP,WAAW;iBACZ,CAAA;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACpB;YACD,cAAc,GAAG,IAAI,CAAA;SACtB;QAED,OAAO;YACL,cAAc;YACd,UAAU,EAAE,gBAAgB;SAC7B,CAAA;IACH,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,UAA2B;QACxD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAChD,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;YACtC,MAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;YACjC,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;YACtC,IAAU,cAAe,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC1C,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAA;aACF;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;YACD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBAC1E,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;oBAC3C,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAA;iBACxF;aACF;SACF;IACH,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA2B;QACzD,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAQ,UAAU,CAAC,KAAK,CAAC,CAAA;YACnC,MAAM,QAAQ,GAAQ;gBACpB,OAAO,EAAE,IAAA,4BAAU,EAAC,IAAA,+BAAa,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC/C,WAAW,EAAE,EAAE;aAChB,CAAA;YACD,MAAM,YAAY,GAAiB,IAAI,CAAC,CAAC,CAAC,CAAA;YAC1C,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACrD,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACtC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAA,4BAAU,EAAC,IAAA,+BAAa,EAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;aACtE;YACD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC9B;QACD,OAAO,cAAc,CAAA;IACvB,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA2B,EAAE,MAAc;QACzE,MAAM,wBAAwB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAA;QACtF,MAAM,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;QAEhF,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC5B,KAAK,IAAI,YAAY,CAAC,MAAM,CAAA;SAC7B;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAA;QACnC,OAAO,SAAS,GAAG,MAAM,CAAC,qBAAqB,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAA;IAC7F,CAAC;CACF;AAjGD,kCAiGC;AAED,SAAgB,WAAW,CAAC,MAAuB;IACjD,OAAO,IAAA,4BAAU,EAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;AAChE,CAAC;AAFD,kCAEC"}