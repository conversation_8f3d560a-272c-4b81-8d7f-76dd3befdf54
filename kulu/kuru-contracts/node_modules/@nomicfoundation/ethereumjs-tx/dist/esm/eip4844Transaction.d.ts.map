{"version": 3, "file": "eip4844Transaction.d.ts", "sourceRoot": "", "sources": ["../../src/eip4844Transaction.ts"], "names": [], "mappings": "AAoBA,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAA;AAMtD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAG5C,OAAO,KAAK,EACV,UAAU,EACV,eAAe,EACf,MAAM,IAAI,cAAc,EACxB,aAAa,IAAI,qBAAqB,EAEtC,MAAM,EACN,SAAS,EACV,MAAM,YAAY,CAAA;AACnB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oCAAoC,CAAA;AAEhE,aAAK,MAAM,GAAG,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;AACzD,aAAK,aAAa,GAAG,qBAAqB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;AAkCvE;;;;;GAKG;AACH,qBAAa,sBAAuB,SAAQ,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC;IACtF,SAAgB,OAAO,EAAE,MAAM,CAAA;IAC/B,SAAgB,UAAU,EAAE,eAAe,CAAA;IAC3C,SAAgB,cAAc,EAAE,UAAU,CAAA;IAC1C,SAAgB,oBAAoB,EAAE,MAAM,CAAA;IAC5C,SAAgB,YAAY,EAAE,MAAM,CAAA;IACpC,SAAgB,gBAAgB,EAAE,MAAM,CAAA;IAExC,SAAgB,MAAM,EAAE,MAAM,CAAA;IACvB,mBAAmB,EAAE,UAAU,EAAE,CAAA;IACxC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAA;IACpB,cAAc,CAAC,EAAE,UAAU,EAAE,CAAA;IAC7B,SAAS,CAAC,EAAE,UAAU,EAAE,CAAA;IAExB;;;;;;OAMG;gBACS,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,SAAc;WAwFlC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,SAAS;IA4BzD;;;;;;OAMG;WACW,yBAAyB,CACrC,MAAM,EAAE,sBAAsB,EAC9B,IAAI,CAAC,EAAE,SAAS,GACf,sBAAsB;IAWzB;;;;;OAKG;WACW,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,GAAE,SAAc;IAoB3E;;;;;OAKG;WACW,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,GAAE,SAAc;IA0DzE;;;;;OAKG;WAEW,kCAAkC,CAC9C,UAAU,EAAE,UAAU,EACtB,IAAI,CAAC,EAAE,SAAS,GACf,sBAAsB;IAoDzB;;OAEG;IACH,UAAU,IAAI,MAAM;IAIpB;;;OAGG;IACH,cAAc,CAAC,OAAO,GAAE,MAAiB,GAAG,MAAM;IAIlD;;;;;;;;;;;;OAYG;IACH,GAAG,IAAI,aAAa;IAmBpB;;;;;;;;;OASG;IACH,SAAS,IAAI,UAAU;IAIvB;;OAEG;IACH,uBAAuB,IAAI,UAAU;IAcrC;;;;;;;;;;OAUG;IACH,gBAAgB,IAAI,UAAU;IAI9B;;;;;;OAMG;IACH,sBAAsB,IAAI,UAAU;IAIpC;;;;;OAKG;IACI,IAAI,IAAI,UAAU;IAIzB,2BAA2B,IAAI,UAAU;IAIzC;;OAEG;IACI,mBAAmB,IAAI,UAAU;IAIxC,MAAM,IAAI,MAAM;IAehB,SAAS,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,GAAG,sBAAsB;IA0B5F;;OAEG;IACI,QAAQ;IAMf;;;;;OAKG;IACH,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM;IAI/B;;OAEG;IACI,QAAQ,IAAI,MAAM;CAG1B"}