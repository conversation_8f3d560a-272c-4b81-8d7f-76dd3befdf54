{"version": 3, "file": "eip1559Transaction.js", "sourceRoot": "", "sources": ["../../src/eip1559Transaction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iCAAiC,CAAA;AACrD,OAAO,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,qBAAqB,EACrB,aAAa,EACb,UAAU,EACV,WAAW,EACX,OAAO,EACP,uBAAuB,GACxB,MAAM,kCAAkC,CAAA;AAEzC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAA;AACtD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,MAAM,MAAM,0BAA0B,CAAA;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAC5C,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,WAAW,CAAA;AAepD;;;;;GAKG;AACH,MAAM,OAAO,2BAA4B,SAAQ,eAAiD;IAqGhG;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,CAAA;QAClE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,MAAM,CAAA;QAE1E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAE5E,kCAAkC;QAClC,MAAM,cAAc,GAAG,WAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;QACrF,IAAI,CAAC,oBAAoB,GAAG,aAAa,CACvC,OAAO,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CACnE,CAAA;QAED,IAAI,CAAC,+BAA+B,CAAC;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAChD,CAAC,CAAA;QAEF,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,WAAW,EAAE;YACnD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,6DAA6D,CAAC,CAAA;YACzF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,iGAAiG,CAClG,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAE1B,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IApJD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QACzE,IACE,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACrF,KAAK,EACL;YACA,MAAM,IAAI,KAAK,CACb,sFACE,eAAe,CAAC,gBAClB,eAAe,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CACvD,CAAA;SACF;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAEjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,2BAA2B,CAAC,eAAe,CAAC,MAAuB,EAAE,IAAI,CAAC,CAAA;IACnF,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,MAAM,CAAA;QAEV,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,uBAAuB,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEhG,OAAO,IAAI,2BAA2B,CACpC;YACE,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC;YAC/B,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,EAAE;YAC5B,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACjD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA6DD;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,UAAkB,QAAQ;QACvC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC;YACnC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAChD,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC;YACxC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SACzE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gBAAgB;QACd,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACxD,CAAC;IAED;;;;;;OAMG;IACH,sBAAsB;QACpB,OAAO,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAES,iBAAiB,CAAC,CAAS,EAAE,CAAa,EAAE,CAAa;QACjE,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,2BAA2B,CAAC,UAAU,CAC3C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,CAAC,GAAG,SAAS;YAChB,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;SACpB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QAE/B,OAAO;YACL,GAAG,QAAQ;YACX,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,oBAAoB,EAAE,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5D,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;YAC5C,UAAU,EAAE,cAAc;SAC3B,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,iBAAiB,IAAI,CAAC,YAAY,yBAAyB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClG,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;CACF"}