{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAA;AAoBzE;;;GAGG;AACH,MAAM,CAAN,IAAY,UAwBX;AAxBD,WAAY,UAAU;IACpB;;;OAGG;IACH,iFAA4B,CAAA;IAE5B;;;OAGG;IACH,sEAAuB,CAAA;IAEvB;;;OAGG;IACH,oFAA8B,CAAA;IAE9B;;;OAGG;IACH,0EAAyB,CAAA;AAC3B,CAAC,EAxBW,UAAU,KAAV,UAAU,QAwBrB;AAqCD,MAAM,UAAU,iBAAiB,CAAC,KAAmC;IACnE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,CAAA;KACZ;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,KAAmC;IAC9D,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA,CAAC,iEAAiE;AACpG,CAAC;AAYD;;GAEG;AACH,MAAM,CAAN,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,yDAAU,CAAA;IACV,+EAAqB,CAAA;IACrB,6EAAoB,CAAA;IACpB,mEAAe,CAAA;AACjB,CAAC,EALW,eAAe,KAAf,eAAe,QAK1B;AAWD,MAAM,UAAU,UAAU,CAAC,EAAoB;IAC7C,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,CAAA;AAC3C,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,EAAoB;IACxD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACtD,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,EAAoB;IACvD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACrD,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,EAAoB;IAClD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,WAAW,CAAA;AAChD,CAAC;AA6ED,MAAM,UAAU,cAAc,CAAC,MAAmB;IAChD,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,MAAM,CAAA;AAC1C,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,MAAmB;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACrD,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,MAAmB;IAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACpD,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,MAAmB;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,WAAW,CAAA;AAC/C,CAAC"}