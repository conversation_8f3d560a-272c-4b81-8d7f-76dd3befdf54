{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAA;AAE3F,MAAM,WAAW,WAAW;IAC1B,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;;;;OAKG;IACH,OAAO,EAAE;QACP,CAAC,GAAG,EAAE,MAAM,GAAG;YACb,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;YAClB,KAAK,EAAE,MAAM,CAAA;SACd,CAAA;KACF,CAAA;IACD;;;OAGG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAA;CACvB;AAED,oBAAY,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,aAAa,GAAG,UAAU,CAAC,CAAC,CAAA;AAEpG,oBAAY,YAAY,GAAG;IACzB,GAAG,EAAE,iBAAiB,CAAA;IACtB,KAAK,EAAE,iBAAiB,EAAE,CAAA;IAC1B,KAAK,EAAE,iBAAiB,CAAA;CACzB,CAAA;AAED,oBAAY,KAAK,GAAG;IAClB,OAAO,EAAE,iBAAiB,CAAA;IAC1B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,QAAQ,EAAE,iBAAiB,CAAA;IAC3B,KAAK,EAAE,iBAAiB,CAAA;IACxB,WAAW,EAAE,iBAAiB,CAAA;IAC9B,YAAY,EAAE,iBAAiB,EAAE,CAAA;IACjC,YAAY,EAAE,YAAY,EAAE,CAAA;CAC7B,CAAA;AAMD,oBAAY,cAAc,GAAG;IAC3B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,WAAW,EAAE,iBAAiB,EAAE,CAAA;CACjC,CAAA;AAKD,oBAAY,mBAAmB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,CAAA;AAC5D,oBAAY,eAAe,GAAG,mBAAmB,EAAE,CAAA;AACnD,oBAAY,UAAU,GAAG,cAAc,EAAE,CAAA;AAEzC,MAAM,WAAW,qBAAqB;IACpC,UAAU,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,CAAA;IAC1D,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9D,aAAa,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9C,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAClF,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACnE,eAAe,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;IACtD,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;IAC1E,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACvF,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACrD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACvB,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACvB,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,CAAA;IACnC,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACxE,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;IACvE,YAAY,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAChD,WAAW,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,qBAAqB,CAAA;CAC9D;AAED,MAAM,WAAW,wBAAyB,SAAQ,qBAAqB;IACrE,oBAAoB,EAAE;QACpB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAC3D,KAAK,IAAI,IAAI,CAAA;KACd,CAAA;IAED,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAA;IACnD,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;IAC1F,wBAAwB,CAAC,SAAS,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACvD,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;IAEvE,WAAW,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,wBAAwB,CAAA;CACjE"}