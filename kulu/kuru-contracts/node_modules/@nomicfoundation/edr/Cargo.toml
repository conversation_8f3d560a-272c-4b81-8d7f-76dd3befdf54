[package]
name = "edr_napi"
version = "0.3.5"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
ansi_term = { version = "0.12.1", default-features = false }
crossbeam-channel = { version = "0.5.6", default-features = false }
itertools = { version = "0.12.0", default-features = false }
k256 = { version = "0.13.1", default-features = false, features = ["arithmetic", "ecdsa", "pkcs8", "precomputed-tables", "std"] }
log = { version = "0.4.20", default-features = false }
# when napi is pinned, be sure to pin napi-derive to the same version
# The `async` feature ensures that a tokio runtime is available
napi = { version = "2.16.0", default-features = false, features = ["async", "error_anyhow", "napi8", "serde-json"] }
napi-derive = "2.16.0"
edr_defaults = { version = "0.3.5", path = "../edr_defaults" }
edr_evm = { version = "0.3.5", path = "../edr_evm", features = ["tracing"]}
edr_eth = { version = "0.3.5", path = "../edr_eth" }
edr_provider = { version = "0.3.5", path = "../edr_provider" }
edr_rpc_eth = { version = "0.3.5", path = "../edr_rpc_eth" }
serde_json = { version = "1.0.85", default-features = false, features = ["alloc"] }
thiserror = { version = "1.0.37", default-features = false }
tracing = { version = "0.1.37", default-features = false, features = ["std"] }
tracing-flame = { version = "0.2.0", default-features = false, features = ["smallvec"] }
tracing-subscriber = { version = "0.3.18", default-features = false, features = ["ansi", "env-filter", "fmt", "parking_lot", "smallvec", "std"] }
parking_lot = { version = "0.12.1", default-features = false }
lazy_static = { version = "1.4.0", features = [] }
rand = { version = "0.8.4", optional = true }
serde = { version = "1.0.189", features = ["derive"] }
mimalloc = { version = "0.1.39", default-features = false, features = ["local_dynamic_tls"] }

[target.x86_64-unknown-linux-gnu.dependencies]
openssl-sys = { version = "0.9.93", features = ["vendored"] }

[target.x86_64-unknown-linux-musl.dependencies]
openssl-sys = { version = "0.9.93", features = ["vendored"] }

[target.aarch64-unknown-linux-gnu.dependencies]
openssl-sys = { version = "0.9.93", features = ["vendored"] }

[target.aarch64-unknown-linux-musl.dependencies]
openssl-sys = { version = "0.9.93", features = ["vendored"] }

[build-dependencies]
napi-build = "2.0.1"

[features]
tracing = ["edr_evm/tracing", "edr_provider/tracing"]
scenarios = ["rand"]

[profile.release]
lto = true

[lints]
workspace = true
