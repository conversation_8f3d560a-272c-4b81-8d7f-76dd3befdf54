{"name": "@nomicfoundation/edr", "version": "0.5.2", "main": "index.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts", "Cargo.toml", "build.rs", "src/"], "repository": {"url": "https://github.com/NomicFoundation/edr.git", "type": "git"}, "napi": {"name": "edr", "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "x86_64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-pc-windows-msvc"]}}, "license": "MIT", "devDependencies": {"@napi-rs/cli": "^2.18.1", "@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.8", "@types/mocha": ">=9.1.0", "@types/node": "^18.0.0", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "json-stream-stringify": "^3.1.4", "mocha": "^10.0.0", "ts-node": "^10.8.0", "typescript": "~4.5.2"}, "engines": {"node": ">= 18"}, "dependencies": {"@nomicfoundation/edr-darwin-arm64": "0.5.2", "@nomicfoundation/edr-darwin-x64": "0.5.2", "@nomicfoundation/edr-linux-arm64-gnu": "0.5.2", "@nomicfoundation/edr-linux-arm64-musl": "0.5.2", "@nomicfoundation/edr-linux-x64-gnu": "0.5.2", "@nomicfoundation/edr-linux-x64-musl": "0.5.2", "@nomicfoundation/edr-win32-x64-msvc": "0.5.2"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "build:tracing": "napi build --platform --release --features tracing", "build:scenarios": "napi build --platform --release --features scenarios", "universal": "napi universal", "version": "napi version", "pretest": "pnpm build", "test": "pnpm tsc && node --max-old-space-size=8192 node_modules/mocha/bin/_mocha --recursive \"test/**/*.ts\"", "testNoBuild": "pnpm tsc && node --max-old-space-size=8192 node_modules/mocha/bin/_mocha --recursive \"test/**/*.ts\"", "clean": "rm -rf @nomicfoundation/edr.node"}}