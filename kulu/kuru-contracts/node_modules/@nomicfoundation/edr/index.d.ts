/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

/** An account that needs to be created during the genesis block. */
export interface GenesisAccount {
  /** Account secret key */
  secretKey: string
  /** Account balance */
  balance: bigint
}
export interface BlockOptions {
  /** The parent block's hash */
  parentHash?: Buffer
  /** The block's beneficiary */
  beneficiary?: Buffer
  /** The state's root hash */
  stateRoot?: Buffer
  /** The block's difficulty */
  difficulty?: bigint
  /** The block's number */
  number?: bigint
  /** The block's gas limit */
  gasLimit?: bigint
  /** The block's timestamp */
  timestamp?: bigint
  /** The block's extra data */
  extraData?: Buffer
  /** The block's mix hash (or prevrandao) */
  mixHash?: Buffer
  /** The block's nonce */
  nonce?: Buffer
  /** The block's base gas fee */
  baseFee?: bigint
  /** The block's withdrawals */
  withdrawals?: Array<Withdrawal>
  /** Blob gas was added by EIP-4844 and is ignored in older headers. */
  blobGas?: BlobGas
  /**
   * The hash tree root of the parent beacon block for the given execution
   * block (EIP-4788).
   */
  parentBeaconBlockRoot?: Buffer
}
/** Information about the blob gas used in a block. */
export interface BlobGas {
  /**
   * The total amount of blob gas consumed by the transactions within the
   * block.
   */
  gasUsed: bigint
  /**
   * The running total of blob gas consumed in excess of the target, prior to
   * the block. Blocks with above-target blob gas consumption increase this
   * value, blocks with below-target blob gas consumption decrease it
   * (bounded at 0).
   */
  excessGas: bigint
}
/** The result of executing a call override. */
export interface CallOverrideResult {
  result: Buffer
  shouldRevert: boolean
}
/** Identifier for the Ethereum spec. */
export const enum SpecId {
  /** Frontier */
  Frontier = 0,
  /** Frontier Thawing */
  FrontierThawing = 1,
  /** Homestead */
  Homestead = 2,
  /** DAO Fork */
  DaoFork = 3,
  /** Tangerine */
  Tangerine = 4,
  /** Spurious Dragon */
  SpuriousDragon = 5,
  /** Byzantium */
  Byzantium = 6,
  /** Constantinople */
  Constantinople = 7,
  /** Petersburg */
  Petersburg = 8,
  /** Istanbul */
  Istanbul = 9,
  /** Muir Glacier */
  MuirGlacier = 10,
  /** Berlin */
  Berlin = 11,
  /** London */
  London = 12,
  /** Arrow Glacier */
  ArrowGlacier = 13,
  /** Gray Glacier */
  GrayGlacier = 14,
  /** Merge */
  Merge = 15,
  /** Shanghai */
  Shanghai = 16,
  /** Cancun */
  Cancun = 17,
  /** Latest */
  Latest = 18
}
export interface DebugTraceResult {
  pass: boolean
  gasUsed: bigint
  output?: Buffer
  structLogs: Array<DebugTraceLogItem>
}
export interface DebugTraceLogItem {
  /** Program Counter */
  pc: bigint
  op: number
  /** Gas left before executing this operation as hex number. */
  gas: string
  /** Gas cost of this operation as hex number. */
  gasCost: string
  /** Array of all values (hex numbers) on the stack */
  stack?: Array<string>
  /** Depth of the call stack */
  depth: bigint
  /** Size of memory array */
  memSize: bigint
  /** Name of the operation */
  opName: string
  /** Description of an error as a hex string. */
  error?: string
  /** Array of all allocated values as hex strings. */
  memory?: Array<string>
  /** Map of all stored values with keys and values encoded as hex strings. */
  storage?: Record<string, string>
}
/** Ethereum execution log. */
export interface ExecutionLog {
  address: Buffer
  topics: Array<Buffer>
  data: Buffer
}
export interface ContractAndFunctionName {
  /** The contract name. */
  contractName: string
  /** The function name. Only present for calls. */
  functionName?: string
}
export interface LoggerConfig {
  /** Whether to enable the logger. */
  enable: boolean
  decodeConsoleLogInputsCallback: (inputs: Buffer[]) => string[]
  getContractAndFunctionNameCallback: (code: Buffer, calldata?: Buffer) => ContractAndFunctionName
  printLineCallback: (message: string, replace: boolean) => void
}
/** Configuration for a chain */
export interface ChainConfig {
  /** The chain ID */
  chainId: bigint
  /** The chain's supported hardforks */
  hardforks: Array<HardforkActivation>
}
/** Configuration for forking a blockchain */
export interface ForkConfig {
  /** The URL of the JSON-RPC endpoint to fork from */
  jsonRpcUrl: string
  /**
   * The block number to fork from. If not provided, the latest safe block is
   * used.
   */
  blockNumber?: bigint
  /** The HTTP headers to use when making requests to the JSON-RPC endpoint */
  httpHeaders?: Array<HttpHeader>
}
export interface HttpHeader {
  name: string
  value: string
}
/** Configuration for a hardfork activation */
export interface HardforkActivation {
  /** The block number at which the hardfork is activated */
  blockNumber: bigint
  /** The activated hardfork */
  specId: SpecId
}
/**The type of ordering to use when selecting blocks to mine. */
export const enum MineOrdering {
  /**Insertion order */
  Fifo = 'Fifo',
  /**Effective miner fee */
  Priority = 'Priority'
}
/** Configuration for the provider's mempool. */
export interface MemPoolConfig {
  order: MineOrdering
}
export interface IntervalRange {
  min: bigint
  max: bigint
}
/** Configuration for the provider's miner. */
export interface MiningConfig {
  autoMine: boolean
  interval?: bigint | IntervalRange
  memPool: MemPoolConfig
}
/** Configuration for a provider */
export interface ProviderConfig {
  /** Whether to allow blocks with the same timestamp */
  allowBlocksWithSameTimestamp: boolean
  /** Whether to allow unlimited contract size */
  allowUnlimitedContractSize: boolean
  /** Whether to return an `Err` when `eth_call` fails */
  bailOnCallFailure: boolean
  /** Whether to return an `Err` when a `eth_sendTransaction` fails */
  bailOnTransactionFailure: boolean
  /** The gas limit of each block */
  blockGasLimit: bigint
  /** The directory to cache remote JSON-RPC responses */
  cacheDir?: string
  /** The chain ID of the blockchain */
  chainId: bigint
  /** The configuration for chains */
  chains: Array<ChainConfig>
  /** The address of the coinbase */
  coinbase: Buffer
  /** Enables RIP-7212 */
  enableRip7212: boolean
  /**
   * The configuration for forking a blockchain. If not provided, a local
   * blockchain will be created
   */
  fork?: ForkConfig
  /** The genesis accounts of the blockchain */
  genesisAccounts: Array<GenesisAccount>
  /** The hardfork of the blockchain */
  hardfork: SpecId
  /**
   * The initial base fee per gas of the blockchain. Required for EIP-1559
   * transactions and later
   */
  initialBaseFeePerGas?: bigint
  /** The initial blob gas of the blockchain. Required for EIP-4844 */
  initialBlobGas?: BlobGas
  /** The initial date of the blockchain, in seconds since the Unix epoch */
  initialDate?: bigint
  /**
   * The initial parent beacon block root of the blockchain. Required for
   * EIP-4788
   */
  initialParentBeaconBlockRoot?: Buffer
  /** The minimum gas price of the next block. */
  minGasPrice: bigint
  /** The configuration for the miner */
  mining: MiningConfig
  /** The network ID of the blockchain */
  networkId: bigint
}
/** The possible reasons for successful termination of the EVM. */
export const enum SuccessReason {
  /** The opcode `STOP` was called */
  Stop = 0,
  /** The opcode `RETURN` was called */
  Return = 1,
  /** The opcode `SELFDESTRUCT` was called */
  SelfDestruct = 2,
  EofReturnContract = 3
}
export interface CallOutput {
  /** Return value */
  returnValue: Buffer
}
export interface CreateOutput {
  /** Return value */
  returnValue: Buffer
  /** Optionally, a 160-bit address */
  address?: Buffer
}
/** The result when the EVM terminates successfully. */
export interface SuccessResult {
  /** The reason for termination */
  reason: SuccessReason
  /** The amount of gas used */
  gasUsed: bigint
  /** The amount of gas refunded */
  gasRefunded: bigint
  /** The logs */
  logs: Array<ExecutionLog>
  /** The transaction output */
  output: CallOutput | CreateOutput
}
/** The result when the EVM terminates due to a revert. */
export interface RevertResult {
  /** The amount of gas used */
  gasUsed: bigint
  /** The transaction output */
  output: Buffer
}
/**
 * Indicates that the EVM has experienced an exceptional halt. This causes
 * execution to immediately end with all gas being consumed.
 */
export const enum ExceptionalHalt {
  OutOfGas = 0,
  OpcodeNotFound = 1,
  InvalidFEOpcode = 2,
  InvalidJump = 3,
  NotActivated = 4,
  StackUnderflow = 5,
  StackOverflow = 6,
  OutOfOffset = 7,
  CreateCollision = 8,
  PrecompileError = 9,
  NonceOverflow = 10,
  /** Create init code size exceeds limit (runtime). */
  CreateContractSizeLimit = 11,
  /** Error on created contract that begins with EF */
  CreateContractStartingWithEF = 12,
  /** EIP-3860: Limit and meter initcode. Initcode size limit exceeded. */
  CreateInitCodeSizeLimit = 13,
  /** Aux data overflow, new aux data is larger tha u16 max size. */
  EofAuxDataOverflow = 14,
  /** Aud data is smaller then already present data size. */
  EofAuxDataTooSmall = 15,
  /** EOF Subroutine stack overflow */
  EOFFunctionStackOverflow = 16
}
/** The result when the EVM terminates due to an exceptional halt. */
export interface HaltResult {
  /** The exceptional halt that occurred */
  reason: ExceptionalHalt
  /**
   * Halting will spend all the gas and will thus be equal to the specified
   * gas limit
   */
  gasUsed: bigint
}
/** The result of executing a transaction. */
export interface ExecutionResult {
  /** The transaction result */
  result: SuccessResult | RevertResult | HaltResult
  /** Optional contract address if the transaction created a new contract. */
  contractAddress?: Buffer
}
export interface SubscriptionEvent {
  filterId: bigint
  result: any
}
export interface TracingMessage {
  /** Sender address */
  readonly caller: Buffer
  /** Recipient address. None if it is a Create message. */
  readonly to?: Buffer
  /** Whether it's a static call */
  readonly isStaticCall: boolean
  /** Transaction gas limit */
  readonly gasLimit: bigint
  /** Depth of the message */
  readonly depth: number
  /** Input data of the message */
  readonly data: Buffer
  /** Value sent in the message */
  readonly value: bigint
  /**
   * Address of the code that is being executed. Can be different from `to`
   * if a delegate call is being done.
   */
  readonly codeAddress?: Buffer
  /** Code of the contract that is being executed. */
  readonly code?: Buffer
}
export interface TracingStep {
  /** Call depth */
  readonly depth: number
  /** The program counter */
  readonly pc: bigint
  /** The executed op code */
  readonly opcode: string
  /**
   * The entries on the stack. It only contains the top element unless
   * verbose tracing is enabled. The vector is empty if there are no elements
   * on the stack.
   */
  readonly stack: Array<bigint>
  /** The memory at the step. None if verbose tracing is disabled. */
  readonly memory?: Buffer
}
export interface TracingMessageResult {
  /** Execution result */
  readonly executionResult: ExecutionResult
}
export interface Withdrawal {
  /** The index of withdrawal */
  index: bigint
  /** The index of the validator that generated the withdrawal */
  validatorIndex: bigint
  /** The recipient address for withdrawal value */
  address: Buffer
  /** The value contained in withdrawal */
  amount: bigint
}
export class EdrContext {
  /**Creates a new [`EdrContext`] instance. Should only be called once! */
  constructor()
}
/** A JSON-RPC provider for Ethereum. */
export class Provider {
  /**Constructs a new provider with the provided configuration. */
  static withConfig(context: EdrContext, config: ProviderConfig, loggerConfig: LoggerConfig, subscriberCallback: (event: SubscriptionEvent) => void): Promise<Provider>
  /**Handles a JSON-RPC request and returns a JSON-RPC response. */
  handleRequest(jsonRequest: string): Promise<Response>
  setCallOverrideCallback(callOverrideCallback: (contract_address: Buffer, data: Buffer) => Promise<CallOverrideResult | undefined>): void
  /**
   * Set to `true` to make the traces returned with `eth_call`,
   * `eth_estimateGas`, `eth_sendRawTransaction`, `eth_sendTransaction`,
   * `evm_mine`, `hardhat_mine` include the full stack and memory. Set to
   * `false` to disable this.
   */
  setVerboseTracing(verboseTracing: boolean): void
}
export class Response {
  /** Returns the response data as a JSON string or a JSON object. */
  get data(): string | any
  get json(): string | any
  get solidityTrace(): RawTrace | null
  get traces(): Array<RawTrace>
}
export class RawTrace {
  trace(): Array<TracingMessage | TracingStep | TracingMessageResult>
}
