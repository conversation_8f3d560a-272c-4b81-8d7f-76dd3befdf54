{"name": "@nomicfoundation/hardhat-foundry", "version": "1.1.4", "description": "Hardhat plugin that adds Hardhat support to Foundry projects", "repository": "github:nomicfoundation/hardhat", "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-foundry", "author": "Nomic Foundation", "license": "MIT", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "foundry"], "files": ["dist/src/", "src/", "LICENSE", "README.md"], "devDependencies": {"@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "chai": "^4.2.0", "eslint": "^8.44.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "3.4.0", "hardhat": "^2.17.2", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^10.8.0", "typescript": "~5.0.0", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2", "@nomicfoundation/eslint-plugin-slow-imports": "^1.0.0"}, "peerDependencies": {"hardhat": "^2.17.2"}, "dependencies": {"picocolors": "^1.1.0"}, "scripts": {"lint": "pnpm prettier --check && pnpm eslint", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "eslint": "eslint 'src/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "pretest": "cd ../.. && pnpm build", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "clean": "<PERSON><PERSON><PERSON> dist"}}