{"version": 3, "file": "foundry.js", "sourceRoot": "", "sources": ["../../src/foundry.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAoC;AACpC,iDAA+D;AAC/D,yDAA2E;AAC3E,+BAAiC;AAEjC,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,oBAAY,CAAC,CAAC;AAIrC,IAAI,gBAAiD,CAAC;AAEtD,MAAa,mBAAoB,SAAQ,oCAA2B;IAClE,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;CACF;AAJD,kDAIC;AAED,MAAM,iBAAkB,SAAQ,mBAAmB;IACjD,YAAY,UAAkB,EAAE,MAAa;QAC3C,KAAK,CACH,qBAAqB,UAAU;;EAEnC,MAAM,CAAC,OAAO;CACf,EACK,MAAM,CACP,CAAC;IACJ,CAAC;CACF;AAED,SAAgB,cAAc;IAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC;AACvD,CAAC;AAFD,wCAEC;AAED,SAAgB,eAAe,CAAC,aAAqB;IACnD,MAAM,UAAU,GAAe,EAAE,CAAC;IAClC,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACzD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;QAC1C,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,SAAS;SACV;QAED,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC/B,MAAM,IAAI,mBAAmB,CAC3B,sBAAsB,aAAa,uCAAuC,CAC3E,CAAC;SACH;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChC,MAAM,IAAI,mBAAmB,CAC3B,sBAAsB,aAAa,gDAAgD,CACpF,CAAC;SACH;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAExC,2EAA2E;QAC3E,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACvC,SAAS;SACV;QAED,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACnC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AA/BD,0CA+BC;AAEM,KAAK,UAAU,aAAa;IACjC,2BAA2B;IAC3B,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACrE;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAPD,sCAOC;AAEM,KAAK,UAAU,iBAAiB,CAAC,UAAkB;IACxD,6EAA6E;IAC7E,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;IACtD,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IAEzD,MAAM,GAAG,GAAG,iBACV,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EACpC,IAAI,UAAU,EAAE,CAAC;IAEjB,OAAO,CAAC,GAAG,CAAC,YAAY,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEjD,IAAI;QACF,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;KACjB;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,IAAI,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAChD;AACH,CAAC;AAhBD,8CAgBC;AAED,SAAS,UAAU,CAAC,GAAW;IAC7B,IAAI;QACF,OAAO,IAAA,wBAAQ,EAAC,GAAG,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;KACpD;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,WAAW,GAAG,wBAAwB,CAC1C,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CACxB,CAAC;QAEF,MAAM,WAAW,CAAC;KACnB;AACH,CAAC;AAED,KAAK,UAAU,MAAM,CAAC,GAAW;IAC/B,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;KAC3D;AACH,CAAC;AAED,SAAS,wBAAwB,CAC/B,QAA4B,EAC5B,OAAe;IAEf,QAAQ,QAAQ,EAAE;QAChB,KAAK,GAAG;YACN,OAAO,IAAI,mBAAmB,CAC5B,+EAA+E,CAChF,CAAC;QACJ,KAAK,GAAG;YACN,OAAO,IAAI,mBAAmB,CAC5B,8EAA8E,CAC/E,CAAC;QACJ;YACE,OAAO,IAAI,mBAAmB,CAC5B,6CAA6C,OAAO,EAAE,CACvD,CAAC;KACL;AACH,CAAC"}