{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;AAAA,2CAAkE;AAElE,iEAG0C;AAE1C,2BAA+C;AAC/C,gDAAwB;AACxB,4DAAoC;AACpC,uCAKmB;AAEnB,MAAM,iBAAiB,GAAG,cAAc,CAAC;AAEzC,IAAI,eAAe,GAAG,KAAK,CAAC;AAE5B,IAAA,qBAAY,EAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;IAClC,mFAAmF;IACnF,IAAI,CAAC,IAAA,eAAU,EAAC,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,EAAE;QAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YAC7C,OAAO,CAAC,GAAG,CACT,oBAAU,CAAC,MAAM,CACf,2HAA2H,iBAAiB,kBAAkB,CAC/J,CACF,CAAC;SACH;QACD,OAAO;KACR;IAED,sBAAsB;IACtB,MAAM,aAAa,GAAG,IAAA,wBAAc,GAAE,CAAC;IAEvC,6BAA6B;IAC7B,IACE,aAAa,EAAE,GAAG,KAAK,SAAS;QAChC,aAAa,EAAE,UAAU,KAAK,SAAS,EACvC;QACA,MAAM,IAAI,6BAAmB,CAC3B,qFAAqF,CACtF,CAAC;KACH;IAED,+DAA+D;IAC/D,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC;IAClD,MAAM,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;IAE7C,IACE,eAAe,KAAK,SAAS;QAC7B,cAAI,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,cAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAClE;QACA,MAAM,IAAI,6BAAmB,CAC3B,iCAAiC,eAAe,+CAA+C,kBAAkB,GAAG,CACrH,CAAC;KACH;IAED,mBAAmB;IACnB,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;IAE3E,2DAA2D;IAC3D,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CACnC,MAAM,CAAC,KAAK,CAAC,IAAI,EACjB,aAAa,CAAC,UAAU,CACzB,CAAC;IACF,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,KAAK,gBAAgB,EAAE;QAC3C,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC;KACtC;IAED,eAAe,GAAG,IAAI,CAAC;AACzB,CAAC,CAAC,CAAC;AAEH,4DAA4D;AAC5D,IAAA,qBAAY,EAAC,+CAAkC,CAAC,CAAC,SAAS,CACxD,KAAK,EACH,EACE,UAAU,EACV,gBAAgB,GACkC,EACpD,IAAI,EACa,EAAE;IACnB,8FAA8F;IAC9F,IAAI,gBAAgB,EAAE;QACpB,OAAO,UAAU,CAAC;KACnB;IACD,MAAM,IAAI,6BAAmB,CAC3B,sEAAsE,CACvE,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,IAAA,qBAAY,EAAC,wCAA2B,CAAC,CAAC,SAAS,CACjD,KAAK,IAAqC,EAAE;IAC1C,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IAED,OAAO,IAAA,uBAAa,GAAE,CAAC;AACzB,CAAC,CACF,CAAC;AAEF,IAAA,aAAI,EACF,iBAAiB,EACjB,qDAAqD,EACrD,KAAK,EAAE,CAAC,EAAE,GAA8B,EAAE,EAAE;IAC1C,MAAM,iBAAiB,GAAG,cAAI,CAAC,OAAO,CACpC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EACrB,cAAc,CACf,CAAC;IAEF,IAAI,IAAA,eAAU,EAAC,iBAAiB,CAAC,EAAE;QACjC,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CAAC,6CAA6C,CAAC,CACjE,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAE7C,IAAA,kBAAa,EACX,iBAAiB,EACjB;QACE,mBAAmB;QACnB,UAAU,cAAI,CAAC,QAAQ,CACrB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EACrB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,GAAG;QACJ,aAAa;QACb,gCAAgC;QAChC,WAAW,cAAI,CAAC,QAAQ,CACtB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EACrB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CACvB,GAAG;QACJ,6BAA6B;KAC9B,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IAEF,MAAM,IAAA,2BAAiB,EAAC,sBAAsB,CAAC,CAAC;AAClD,CAAC,CACF,CAAC"}