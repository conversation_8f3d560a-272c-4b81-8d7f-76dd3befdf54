{"name": "@nomicfoundation/ethereumjs-util", "version": "9.0.4", "description": "A collection of utility functions for Ethereum", "keywords": ["ethereum", "utilities", "utils"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/util#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+util%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "EthereumJS Team", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter"}, {"name": "<PERSON>", "url": "https://github.com/SilentCicero"}, {"name": "Mr. <PERSON>", "url": "https://github.com/MrChico"}, {"name": "Dũng Trần", "email": "<EMAIL>", "url": "https://github.com/tad88dev"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic"}, {"name": "<PERSON>", "url": "https://github.com/tgerring"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/asinyagin"}], "type": "commonjs", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "DEBUG=ethjs npx vitest run --coverage.enabled --coverage.reporter=lcov", "docs:build": "npx typedoc --options typedoc.cjs", "examples": "tsx ../../scripts/examples-runner.ts -- util", "examples:build": "npx embedme README.md", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "test": "npm run test:node && npm run test:browser", "test:browser": "npx vitest run --config=./vitest.config.browser.ts --browser.name=chrome --browser.headless", "test:node": "npx vitest run", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@nomicfoundation/ethereumjs-rlp": "5.0.4", "ethereum-cryptography": "0.1.3"}, "devDependencies": {}, "peerDependencies": {"c-kzg": "^2.1.2"}, "peerDependenciesMeta": {"c-kzg": {"optional": true}}, "engines": {"node": ">=18"}}