{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../../src/signature.ts"], "names": [], "mappings": ";;;AAAA,+DAA2D;AAC3D,+DAA2F;AAE3F,yCAOmB;AACnB,iDAOuB;AACvB,6CAA4C;AAQ5C;;;;;GAKG;AACH,SAAgB,MAAM,CACpB,OAAmB,EACnB,UAAsB,EACtB,OAAgB;IAEhB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,qBAAS,EAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IAErE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC7C,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;IAE9C,MAAM,CAAC,GACL,OAAO,KAAK,SAAS;QACnB,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;QACvB,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;IAEzD,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;AACpB,CAAC;AAhBD,wBAgBC;AAED,SAAgB,oBAAoB,CAAC,CAAS,EAAE,OAAgB;IAC9D,IAAI,CAAC,KAAK,uBAAQ,IAAI,CAAC,KAAK,uBAAQ;QAAE,OAAO,CAAC,CAAA;IAE9C,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,GAAG,wBAAS,CAAA;KACrB;IACD,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,uBAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9C,CAAC;AAPD,oDAOC;AAED,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,OAAO,QAAQ,KAAK,uBAAQ,IAAI,QAAQ,KAAK,uBAAQ,CAAA;AACvD,CAAC;AAED;;;;GAIG;AACI,MAAM,SAAS,GAAG,UACvB,OAAmB,EACnB,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,SAAS,GAAG,IAAA,sBAAW,EAAC,IAAA,wBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,wBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACzE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,MAAM,YAAY,GAAG,IAAA,wBAAY,EAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAA;IACvE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,4BAAgB,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AACpE,CAAC,CAAA;AAfY,QAAA,SAAS,aAerB;AAED;;;;GAIG;AACI,MAAM,QAAQ,GAAG,UACtB,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,6EAA6E;IAE7E,OAAO,IAAA,qBAAU,EAAC,IAAA,sBAAW,EAAC,IAAA,wBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,wBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,kBAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAdY,QAAA,QAAQ,YAcpB;AAED;;;;GAIG;AACI,MAAM,YAAY,GAAG,UAC1B,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAClC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,uBAAQ,KAAK,uBAAQ,CAAC,IAAI,CAAC,KAAK,uBAAQ,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE;QACvF,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACd;IAED,OAAO,IAAA,qBAAU,EAAC,IAAA,sBAAW,EAAC,IAAA,wBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,wBAAa,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;AAC7E,CAAC,CAAA;AAjBY,QAAA,YAAY,gBAiBxB;AAED;;;;;;;GAOG;AACI,MAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,MAAM,KAAK,GAAe,IAAA,kBAAO,EAAC,GAAG,CAAC,CAAA;IAEtC,IAAI,CAAa,CAAA;IACjB,IAAI,CAAa,CAAA;IACjB,IAAI,CAAS,CAAA;IACb,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE;QACtB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAC1B,CAAC,GAAG,IAAA,wBAAa,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;KACtC;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC9B,6EAA6E;QAC7E,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAC1B,CAAC,GAAG,MAAM,CAAC,IAAA,qBAAU,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACnD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACb;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,gDAAgD;IAChD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,CAAC,GAAG,CAAC,GAAG,wBAAS,CAAA;KAClB;IAED,OAAO;QACL,CAAC;QACD,CAAC;QACD,CAAC;KACF,CAAA;AACH,CAAC,CAAA;AA9BY,QAAA,UAAU,cA8BtB;AAED;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,UAC9B,CAAS,EACT,CAAa,EACb,CAAa,EACb,mBAA4B,IAAI,EAChC,OAAgB;IAEhB,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;QACtC,OAAO,KAAK,CAAA;KACb;IAED,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;IAED,MAAM,OAAO,GAAG,IAAA,wBAAa,EAAC,CAAC,CAAC,CAAA;IAChC,MAAM,OAAO,GAAG,IAAA,wBAAa,EAAC,CAAC,CAAC,CAAA;IAEhC,IACE,OAAO,KAAK,uBAAQ;QACpB,OAAO,IAAI,8BAAe;QAC1B,OAAO,KAAK,uBAAQ;QACpB,OAAO,IAAI,8BAAe,EAC1B;QACA,OAAO,KAAK,CAAA;KACb;IAED,IAAI,gBAAgB,IAAI,OAAO,IAAI,oCAAqB,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAhCY,QAAA,gBAAgB,oBAgC5B;AAED;;;;;GAKG;AACI,MAAM,mBAAmB,GAAG,UAAU,OAAmB;IAC9D,IAAA,0BAAa,EAAC,OAAO,CAAC,CAAA;IACtB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAA;IACxF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AACjE,CAAC,CAAA;AAJY,QAAA,mBAAmB,uBAI/B"}