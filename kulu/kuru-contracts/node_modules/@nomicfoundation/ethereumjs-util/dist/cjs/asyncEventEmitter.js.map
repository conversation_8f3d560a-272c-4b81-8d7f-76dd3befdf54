{"version": 3, "file": "asyncEventEmitter.js", "sourceRoot": "", "sources": ["../../src/asyncEventEmitter.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAEH,mCAAqC;AAQrC,KAAK,UAAU,WAAW,CACxB,OAAY,EACZ,KAAyE,EACzE,IAAa;IAEb,IAAI,KAAwB,CAAA;IAC5B,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,KAAK,EAAE;QAC9B,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,MAAM;gBACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;aACzB;iBAAM;gBACL,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;wBACjC,IAAI,KAAK,EAAE;4BACT,MAAM,CAAC,KAAK,CAAC,CAAA;yBACd;6BAAM;4BACL,OAAO,EAAE,CAAA;yBACV;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;aACH;SACF;QAAC,OAAO,CAAU,EAAE;YACnB,KAAK,GAAG,CAAU,CAAA;SACnB;KACF;IACD,IAAI,KAAK,EAAE;QACT,MAAM,KAAK,CAAA;KACZ;AACH,CAAC;AAED,MAAa,iBAAsC,SAAQ,qBAAY;IACrE,IAAI,CAAoB,KAAiB,EAAE,GAAG,IAAsB;QAClE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAA;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAA;QAEjB,IAAI,SAAS,GAAI,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QAElD,yBAAyB;QACzB,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;YACxD,QAAQ,GAAG,IAAI,CAAA;YACf,IAAI,GAAG,SAAS,CAAA;SACjB;QAED,sEAAsE;QACtE,IAAI,KAAK,KAAK,aAAa,IAAI,KAAK,KAAK,gBAAgB,EAAE;YACzD,IAAI,GAAG;gBACL,KAAK,EAAE,IAAI;gBACX,EAAE,EAAE,QAAQ;aACb,CAAA;YAED,QAAQ,GAAG,SAAS,CAAA;SACrB;QAED,uDAAuD;QACvD,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QAC9D,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAEzE,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACtC,CAAC;IAED,IAAI,CAAoB,KAAiB,EAAE,QAAc;QACvD,MAAM,IAAI,GAAG,IAAI,CAAA;QACjB,IAAI,CAA2B,CAAA;QAE/B,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;SACnD;QAED,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;YACxB,CAAC,GAAG,UAAU,CAAI,EAAE,IAAS;gBAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAS,CAAC,CAAA;gBACrC,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACxB,CAAC,CAAA;SACF;aAAM;YACL,CAAC,GAAG,UAAU,CAAI;gBAChB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAS,CAAC,CAAA;gBACrC,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YACrB,CAAC,CAAA;SACF;QAED,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAS,CAAC,CAAA;QAEzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAoB,KAAiB,EAAE,QAAc;QACxD,IAAI,SAAS,GAAI,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QAElD,WAAW;QACX,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;SACnD;QAED,oCAAoC;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7B,CAAC;YAAC,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;SACxD;QAED,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAE3B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAoB,KAAiB,EAAE,MAAY,EAAE,QAAc;QACvE,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAoB,KAAiB,EAAE,MAAY,EAAE,QAAc;QACtE,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC7D,CAAC;IAEO,aAAa,CACnB,KAAiB,EACjB,MAAY,EACZ,QAAc,EACd,aAAsB;QAEtB,IAAI,SAAS,GAAI,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QAClD,IAAI,CAAC,CAAA;QACL,IAAI,KAAK,CAAA;QACT,MAAM,GAAG,GAAG,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAE7C,WAAW;QACX,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;SACnD;QACD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAA;SACjD;QAED,oCAAoC;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7B,CAAC;YAAC,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;SACxD;QAED,KAAK,GAAG,SAAS,CAAC,MAAM,CAAA;QAExB,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAI;YAChC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;gBAC3B,KAAK,GAAG,CAAC,GAAG,GAAG,CAAA;gBACf,MAAK;aACN;SACF;QAED,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAA;QAEpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,EAAE,CAAoB,KAAiB,EAAE,QAAc;QACrD,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED,WAAW,CAAoB,KAAiB,EAAE,QAAc;QAC9D,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC3C,CAAC;IAED,eAAe,CAAoB,KAAiB,EAAE,QAAc;QAClE,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED,mBAAmB,CAAoB,KAAiB,EAAE,QAAc;QACtE,OAAO,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IACnD,CAAC;IAED,kBAAkB,CAAC,KAAwB;QACzC,OAAO,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;IACxC,CAAC;IAED,cAAc,CAAoB,KAAiB,EAAE,QAAc;QACjE,OAAO,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC9C,CAAC;IAED,UAAU;QACR,OAAO,KAAK,CAAC,UAAU,EAAwB,CAAA;IACjD,CAAC;IAED,SAAS,CAAoB,KAAiB;QAC5C,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,CAAW,CAAA;IACzC,CAAC;IAED,aAAa,CAAC,KAAuB;QACnC,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IACnC,CAAC;IAED,eAAe;QACb,OAAO,KAAK,CAAC,eAAe,EAAE,CAAA;IAChC,CAAC;IAED,eAAe,CAAC,YAAoB;QAClC,OAAO,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;IAC5C,CAAC;CACF;AAnKD,8CAmKC"}