[package]
edition = "2021"
name = "nomicfoundation_solidity-analyzer"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
solang-parser = "=0.1.13"
# Default enable napi4 feature, see https://nodejs.org/api/n-api.html#node-api-version-matrix
napi = { version = "=2.11.2", default-features = false, features = ["napi4"] }
napi-derive = "=2.11.1"

[build-dependencies]
napi-build = "=2.0.1"

[profile.release]
lto = true
