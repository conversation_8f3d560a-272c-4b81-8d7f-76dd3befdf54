"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class UnreachableCaseError extends Error {
    constructor(value) {
        super(`Unreachable case: ${value}`);
    }
}
exports.UnreachableCaseError = UnreachableCaseError;
function assert(condition, msg = "no additional info provided") {
    if (!condition) {
        throw new Error("Assertion Error: " + msg);
    }
}
exports.assert = assert;
function noop(..._args) { }
exports.noop = noop;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZnVuY3Rpb25zLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vbGliL2Z1bmN0aW9ucy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQUFBLE1BQWEsb0JBQXFCLFNBQVEsS0FBSztJQUM3QyxZQUFZLEtBQVk7UUFDdEIsS0FBSyxDQUFDLHFCQUFxQixLQUFLLEVBQUUsQ0FBQyxDQUFDO0lBQ3RDLENBQUM7Q0FDRjtBQUpELG9EQUlDO0FBRUQsU0FBZ0IsTUFBTSxDQUFDLFNBQWMsRUFBRSxNQUFjLDZCQUE2QjtJQUNoRixJQUFJLENBQUMsU0FBUyxFQUFFO1FBQ2QsTUFBTSxJQUFJLEtBQUssQ0FBQyxtQkFBbUIsR0FBRyxHQUFHLENBQUMsQ0FBQztLQUM1QztBQUNILENBQUM7QUFKRCx3QkFJQztBQUVELFNBQWdCLElBQUksQ0FBQyxHQUFHLEtBQWdCLElBQVMsQ0FBQztBQUFsRCxvQkFBa0QiLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgVW5yZWFjaGFibGVDYXNlRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlOiBuZXZlcikge1xuICAgIHN1cGVyKGBVbnJlYWNoYWJsZSBjYXNlOiAke3ZhbHVlfWApO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhc3NlcnQoY29uZGl0aW9uOiBhbnksIG1zZzogc3RyaW5nID0gXCJubyBhZGRpdGlvbmFsIGluZm8gcHJvdmlkZWRcIik6IGFzc2VydHMgY29uZGl0aW9uIHtcbiAgaWYgKCFjb25kaXRpb24pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJBc3NlcnRpb24gRXJyb3I6IFwiICsgbXNnKTtcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gbm9vcCguLi5fYXJnczogdW5rbm93bltdKTogdm9pZCB7fVxuIl19