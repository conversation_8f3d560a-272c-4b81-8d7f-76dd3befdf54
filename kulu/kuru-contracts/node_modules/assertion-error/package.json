{"name": "assertion-error", "version": "1.1.0", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": "<PERSON> <<EMAIL>> (http://qualiancy.com)", "license": "MIT", "types": "./index.d.ts", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "**************:chaijs/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*", "typescript": "^2.6.1"}}