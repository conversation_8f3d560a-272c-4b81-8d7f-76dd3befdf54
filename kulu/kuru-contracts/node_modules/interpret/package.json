{"name": "interpret", "version": "1.4.0", "description": "A dictionary of file extensions and associated module loaders.", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://goingslowly.com/)"], "repository": "gulpjs/interpret", "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "files": ["LICENSE", "index.js", "mjs-stub.js"], "scripts": {"lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.6.2", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "keywords": ["cirru-script", "cjsx", "co", "coco", "coffee", "coffee-script", "coffee.md", "coffeescript", "csv", "<PERSON><PERSON><PERSON>", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "wisp", "xml", "yaml", "yml"]}