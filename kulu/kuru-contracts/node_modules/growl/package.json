{"name": "growl", "version": "1.10.5", "description": "Growl unobtrusive notifications", "author": "<PERSON><PERSON> <<EMAIL>>", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/tj/node-growl.git"}, "main": "./lib/growl.js", "license": "MIT", "devDependencies": {"eslint": "^4.8.0", "eslint-config-airbnb-base": "^12.0.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0"}, "scripts": {"test": "node test.js", "lint": "eslint --ext js lib "}, "engines": {"node": ">=4.x"}}