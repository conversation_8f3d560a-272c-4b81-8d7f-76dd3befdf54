1.10.5 / 2018-04-04
==================

* Fix callbacks not receiving errors (#72) [ch<PERSON><PERSON><PERSON>]

1.10.4 / 2018-01-29
==================

* Fix notifications on linux when using notify-send (#70) [hmshwt]

1.9.3 / 2016-09-05
==================

  * fixed command injection vulnerability

1.7.0 / 2012-12-30 
==================

  * support transient notifications in Gnome

1.6.1 / 2012-09-25 
==================

  * restore compatibility with node < 0.8 [fgnass]

1.6.0 / 2012-09-06 
==================

  * add notification center support [drudge]

1.5.1 / 2012-04-08 
==================

  * Merge pull request #16 from KyleAMathews/patch-1
  * Fixes #15

1.5.0 / 2012-02-08 
==================

  * Added windows support [perfusorius]

1.4.1 / 2011-12-28 
==================

  * Fixed: dont exit(). Closes #9

1.4.0 / 2011-12-17 
==================

  * Changed API: `growl.notify()` -> `growl()`

1.3.0 / 2011-12-17 
==================

  * Added support for Ubuntu/Debian/Linux users [niftylettuce]
  * Fixed: send notifications even if title not specified [alessioalex]

1.2.0 / 2011-10-06 
==================

  * Add support for priority.

1.1.0 / 2011-03-15 
==================

  * Added optional callbacks
  * Added parsing of version

1.0.1 / 2010-03-26
==================

  * Fixed; sys.exec -> child_process.exec to support latest node

1.0.0 / 2010-03-19
==================
  
  * Initial release
