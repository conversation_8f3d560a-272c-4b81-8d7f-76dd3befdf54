{"version": 3, "file": "instrument.js", "sourceRoot": "", "sources": ["../src/instrument.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,QAAQ,CAAC;AACzC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAElE,IAAM,MAAM,GAAG,eAAe,EAAU,CAAC;AAkBzC;;;;;;;;;GASG;AAEH,IAAM,QAAQ,GAAqE,EAAE,CAAC;AACtF,IAAM,YAAY,GAAiD,EAAE,CAAC;AAEtE,4BAA4B;AAC5B,SAAS,UAAU,CAAC,IAA2B;IAC7C,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;QACtB,OAAO;KACR;IAED,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAE1B,QAAQ,IAAI,EAAE;QACZ,KAAK,SAAS;YACZ,iBAAiB,EAAE,CAAC;YACpB,MAAM;QACR,KAAK,KAAK;YACR,aAAa,EAAE,CAAC;YAChB,MAAM;QACR,KAAK,KAAK;YACR,aAAa,EAAE,CAAC;YAChB,MAAM;QACR,KAAK,OAAO;YACV,eAAe,EAAE,CAAC;YAClB,MAAM;QACR,KAAK,SAAS;YACZ,iBAAiB,EAAE,CAAC;YACpB,MAAM;QACR,KAAK,OAAO;YACV,eAAe,EAAE,CAAC;YAClB,MAAM;QACR,KAAK,oBAAoB;YACvB,4BAA4B,EAAE,CAAC;YAC/B,MAAM;QACR;YACE,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;KACtD;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,yBAAyB,CAAC,OAA0B;IAClE,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE;QAC1F,OAAO;KACR;IACD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACrD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAiC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC/E,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,YAAY;AACZ,SAAS,eAAe,CAAC,IAA2B,EAAE,IAAS;;IAC7D,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO;KACR;;QAED,KAAsB,IAAA,KAAA,SAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA,gBAAA,4BAAE;YAAvC,IAAM,OAAO,WAAA;YAChB,IAAI;gBACF,OAAO,CAAC,IAAI,CAAC,CAAC;aACf;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,KAAK,CACV,4DAA0D,IAAI,gBAAW,eAAe,CACtF,OAAO,CACR,iBAAY,CAAG,CACjB,CAAC;aACH;SACF;;;;;;;;;AACH,CAAC;AAED,YAAY;AACZ,SAAS,iBAAiB;IACxB,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE;QAC1B,OAAO;KACR;IAED,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAS,KAAa;QAChF,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,UAAS,oBAA+B;YAClE,OAAO;gBAAS,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAC5B,eAAe,CAAC,SAAS,EAAE,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;gBAE5C,mCAAmC;gBACnC,IAAI,oBAAoB,EAAE;oBACxB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBAC3E;YACH,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,YAAY;AACZ,SAAS,eAAe;IACtB,IAAI,CAAC,mBAAmB,EAAE,EAAE;QAC1B,OAAO;KACR;IAED,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,UAAS,aAAyB;QACtD,OAAO;YAAS,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC5B,IAAM,WAAW,GAAG;gBAClB,IAAI,MAAA;gBACJ,SAAS,EAAE;oBACT,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC;oBAC5B,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC;iBACvB;gBACD,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;aAC3B,CAAC;YAEF,eAAe,CAAC,OAAO,eAClB,WAAW,EACd,CAAC;YAEH,sEAAsE;YACtE,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAC3C,UAAC,QAAkB;gBACjB,eAAe,CAAC,OAAO,wBAClB,WAAW,KACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,EACxB,QAAQ,UAAA,IACR,CAAC;gBACH,OAAO,QAAQ,CAAC;YAClB,CAAC,EACD,UAAC,KAAY;gBACX,eAAe,CAAC,OAAO,wBAClB,WAAW,KACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,EACxB,KAAK,OAAA,IACL,CAAC;gBACH,uEAAuE;gBACvE,2FAA2F;gBAC3F,sFAAsF;gBACtF,MAAM,KAAK,CAAC;YACd,CAAC,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAeD,+DAA+D;AAC/D,iDAAiD;AACjD,SAAS,cAAc,CAAC,SAAqB;IAArB,0BAAA,EAAA,cAAqB;IAC3C,IAAI,SAAS,IAAI,MAAM,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;QACrF,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;KAClD;IACD,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;QACvC,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;KAClD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,8CAA8C;AAC9C,SAAS,WAAW,CAAC,SAAqB;IAArB,0BAAA,EAAA,cAAqB;IACxC,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;KACrB;IACD,IAAI,SAAS,IAAI,MAAM,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;QAC9D,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;KACzB;IACD,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC;AACD,8DAA8D;AAE9D,YAAY;AACZ,SAAS,aAAa;IACpB,IAAI,CAAC,CAAC,gBAAgB,IAAI,MAAM,CAAC,EAAE;QACjC,OAAO;KACR;IAED,iGAAiG;IACjG,IAAM,WAAW,GAAqB,EAAE,CAAC;IACzC,IAAM,aAAa,GAAiB,EAAE,CAAC;IACvC,IAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC;IAE1C,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAS,YAAwB;QACtD,OAAO;YAA4C,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC/D,4DAA4D;YAC5D,IAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,GAAG,CAAC,cAAc,GAAG;gBACnB,sEAAsE;gBACtE,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC3D,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;aACb,CAAC;YAEF,8DAA8D;YAC9D,sEAAsE;YACtE,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,cAAc,CAAC,MAAM,KAAK,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACpF,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC;aACnC;YAED,IAAM,yBAAyB,GAAG;gBAChC,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;oBACxB,IAAI;wBACF,+CAA+C;wBAC/C,eAAe;wBACf,IAAI,GAAG,CAAC,cAAc,EAAE;4BACtB,GAAG,CAAC,cAAc,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC;yBAC7C;qBACF;oBAAC,OAAO,CAAC,EAAE;wBACV,gBAAgB;qBACjB;oBAED,IAAI;wBACF,IAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;wBAC5C,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;4BACrB,0DAA0D;4BAC1D,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;4BAC/B,IAAM,MAAI,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;4BACjD,IAAI,GAAG,CAAC,cAAc,IAAI,MAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gCAC/C,GAAG,CAAC,cAAc,CAAC,IAAI,GAAG,MAAI,CAAC,CAAC,CAAiB,CAAC;6BACnD;yBACF;qBACF;oBAAC,OAAO,CAAC,EAAE;wBACV,gBAAgB;qBACjB;oBAED,eAAe,CAAC,KAAK,EAAE;wBACrB,IAAI,MAAA;wBACJ,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;wBACxB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;wBAC1B,GAAG,KAAA;qBACJ,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC;YAEF,IAAI,oBAAoB,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,kBAAkB,KAAK,UAAU,EAAE;gBAC/E,IAAI,CAAC,GAAG,EAAE,oBAAoB,EAAE,UAAS,QAAyB;oBAChE,OAAO;wBAAS,wBAAwB;6BAAxB,UAAwB,EAAxB,qBAAwB,EAAxB,IAAwB;4BAAxB,mCAAwB;;wBACtC,yBAAyB,EAAE,CAAC;wBAC5B,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;oBAC7C,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;aACrE;YAED,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAS,YAAwB;QACtD,OAAO;YAA4C,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzB,eAAe,CAAC,KAAK,EAAE;gBACrB,IAAI,MAAA;gBACJ,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;gBAC1B,GAAG,EAAE,IAAI;aACV,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,QAAgB,CAAC;AAErB,YAAY;AACZ,SAAS,iBAAiB;IACxB,IAAI,CAAC,eAAe,EAAE,EAAE;QACtB,OAAO;KACR;IAED,IAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC;IACxC,MAAM,CAAC,UAAU,GAAG;QAAoC,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACpE,IAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAChC,mFAAmF;QACnF,IAAM,IAAI,GAAG,QAAQ,CAAC;QACtB,QAAQ,GAAG,EAAE,CAAC;QACd,eAAe,CAAC,SAAS,EAAE;YACzB,IAAI,MAAA;YACJ,EAAE,IAAA;SACH,CAAC,CAAC;QACH,IAAI,aAAa,EAAE;YACjB,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACxC;IACH,CAAC,CAAC;IAEF,cAAc;IACd,SAAS,0BAA0B,CAAC,uBAAmC;QACrE,OAAO;YAAwB,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAClD,IAAI,GAAG,EAAE;gBACP,iDAAiD;gBACjD,IAAM,IAAI,GAAG,QAAQ,CAAC;gBACtB,IAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,mFAAmF;gBACnF,QAAQ,GAAG,EAAE,CAAC;gBACd,eAAe,CAAC,SAAS,EAAE;oBACzB,IAAI,MAAA;oBACJ,EAAE,IAAA;iBACH,CAAC,CAAC;aACJ;YACD,OAAO,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,0BAA0B,CAAC,CAAC;IAC9D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,EAAE,0BAA0B,CAAC,CAAC;AACnE,CAAC;AAED,YAAY;AACZ,SAAS,aAAa;IACpB,IAAI,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,EAAE;QAC3B,OAAO;KACR;IAED,gFAAgF;IAChF,kEAAkE;IAClE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9G,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAE7G,yHAAyH;IACzH,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,MAAc;QAC7C,+DAA+D;QAC/D,IAAM,KAAK,GAAI,MAAc,CAAC,MAAM,CAAC,IAAK,MAAc,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;QAE3E,iDAAiD;QACjD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE;YAChF,OAAO;SACR;QACD,8DAA8D;QAE9D,IAAI,CAAC,KAAK,EAAE,kBAAkB,EAAE,UAC9B,QAAoB;YAMpB,OAAO,UAEL,SAAiB,EACjB,EAAsC,EACtC,OAA2C;gBAE3C,IAAI,EAAE,IAAK,EAA0B,CAAC,WAAW,EAAE;oBACjD,IAAI,SAAS,KAAK,OAAO,EAAE;wBACzB,IAAI,CAAC,EAAE,EAAE,aAAa,EAAE,UAAS,aAAyB;4BACxD,OAAO,UAAoB,KAAY;gCACrC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gCACnE,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BACzC,CAAC,CAAC;wBACJ,CAAC,CAAC,CAAC;qBACJ;oBACD,IAAI,SAAS,KAAK,UAAU,EAAE;wBAC5B,IAAI,CAAC,EAAE,EAAE,aAAa,EAAE,UAAS,aAAyB;4BACxD,OAAO,UAAoB,KAAY;gCACrC,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gCAC/D,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BACzC,CAAC,CAAC;wBACJ,CAAC,CAAC,CAAC;qBACJ;iBACF;qBAAM;oBACL,IAAI,SAAS,KAAK,OAAO,EAAE;wBACzB,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;qBACzE;oBACD,IAAI,SAAS,KAAK,UAAU,EAAE;wBAC5B,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;qBAC/D;iBACF;gBAED,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;YACrD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,qBAAqB,EAAE,UACjC,QAAoB;YAOpB,OAAO,UAEL,SAAiB,EACjB,EAAsC,EACtC,OAAwC;gBAExC,IAAI;oBACF,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAI,EAAkC,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;iBAClG;gBAAC,OAAO,CAAC,EAAE;oBACV,gFAAgF;iBACjF;gBACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;YACrD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAM,gBAAgB,GAAW,IAAI,CAAC;AACtC,IAAI,aAAa,GAAW,CAAC,CAAC;AAC9B,IAAI,eAAmC,CAAC;AACxC,IAAI,iBAAoC,CAAC;AAEzC;;;;;;;GAOG;AACH,SAAS,eAAe,CAAC,IAAY,EAAE,OAAiB,EAAE,QAAyB;IAAzB,yBAAA,EAAA,gBAAyB;IACjF,OAAO,UAAC,KAAY;QAClB,0DAA0D;QAC1D,8DAA8D;QAC9D,oCAAoC;QACpC,eAAe,GAAG,SAAS,CAAC;QAC5B,uEAAuE;QACvE,yEAAyE;QACzE,8BAA8B;QAC9B,IAAI,CAAC,KAAK,IAAI,iBAAiB,KAAK,KAAK,EAAE;YACzC,OAAO;SACR;QAED,iBAAiB,GAAG,KAAK,CAAC;QAE1B,IAAI,aAAa,EAAE;YACjB,YAAY,CAAC,aAAa,CAAC,CAAC;SAC7B;QAED,IAAI,QAAQ,EAAE;YACZ,aAAa,GAAG,UAAU,CAAC;gBACzB,OAAO,CAAC,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,CAAC,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CAAC,OAAiB;IAC7C,wDAAwD;IACxD,4DAA4D;IAC5D,gEAAgE;IAChE,OAAO,UAAC,KAAY;QAClB,IAAI,MAAM,CAAC;QAEX,IAAI;YACF,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;SACvB;QAAC,OAAO,CAAC,EAAE;YACV,oFAAoF;YACpF,wDAAwD;YACxD,OAAO;SACR;QAED,IAAM,OAAO,GAAG,MAAM,IAAK,MAAsB,CAAC,OAAO,CAAC;QAE1D,yDAAyD;QACzD,8DAA8D;QAC9D,kCAAkC;QAClC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,UAAU,IAAI,CAAE,MAAsB,CAAC,iBAAiB,CAAC,EAAE;YAC7G,OAAO;SACR;QAED,2DAA2D;QAC3D,mCAAmC;QACnC,IAAI,CAAC,eAAe,EAAE;YACpB,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC;SAC1C;QACD,YAAY,CAAC,eAAe,CAAC,CAAC;QAE9B,eAAe,GAAI,UAAU,CAAC;YAC5B,eAAe,GAAG,SAAS,CAAC;QAC9B,CAAC,EAAE,gBAAgB,CAAmB,CAAC;IACzC,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,kBAAkB,GAAwB,IAAI,CAAC;AACnD,YAAY;AACZ,SAAS,eAAe;IACtB,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC;IAEpC,MAAM,CAAC,OAAO,GAAG,UAAS,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,MAAW,EAAE,KAAU;QAC9E,eAAe,CAAC,OAAO,EAAE;YACvB,MAAM,QAAA;YACN,KAAK,OAAA;YACL,IAAI,MAAA;YACJ,GAAG,KAAA;YACH,GAAG,KAAA;SACJ,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE;YACtB,8CAA8C;YAC9C,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAClD;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,+BAA+B,GAA8B,IAAI,CAAC;AACtE,YAAY;AACZ,SAAS,4BAA4B;IACnC,+BAA+B,GAAG,MAAM,CAAC,oBAAoB,CAAC;IAE9D,MAAM,CAAC,oBAAoB,GAAG,UAAS,CAAM;QAC3C,eAAe,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAEzC,IAAI,+BAA+B,EAAE;YACnC,8CAA8C;YAC9C,OAAO,+BAA+B,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC/D;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport { WrappedFunction } from '@sentry/types';\n\nimport { isInstanceOf, isString } from './is';\nimport { logger } from './logger';\nimport { getGlobalObject } from './misc';\nimport { fill } from './object';\nimport { getFunctionName } from './stacktrace';\nimport { supportsHistory, supportsNativeFetch } from './supports';\n\nconst global = getGlobalObject<Window>();\n\n/** Object describing handler that will be triggered for a given `type` of instrumentation */\ninterface InstrumentHandler {\n  type: InstrumentHandlerType;\n  callback: InstrumentHandlerCallback;\n}\ntype InstrumentHandlerType =\n  | 'console'\n  | 'dom'\n  | 'fetch'\n  | 'history'\n  | 'sentry'\n  | 'xhr'\n  | 'error'\n  | 'unhandledrejection';\ntype InstrumentHandlerCallback = (data: any) => void;\n\n/**\n * Instrument native APIs to call handlers that can be used to create breadcrumbs, APM spans etc.\n *  - Console API\n *  - Fetch API\n *  - XHR API\n *  - History API\n *  - DOM API (click/typing)\n *  - Error API\n *  - UnhandledRejection API\n */\n\nconst handlers: { [key in InstrumentHandlerType]?: InstrumentHandlerCallback[] } = {};\nconst instrumented: { [key in InstrumentHandlerType]?: boolean } = {};\n\n/** Instruments given API */\nfunction instrument(type: InstrumentHandlerType): void {\n  if (instrumented[type]) {\n    return;\n  }\n\n  instrumented[type] = true;\n\n  switch (type) {\n    case 'console':\n      instrumentConsole();\n      break;\n    case 'dom':\n      instrumentDOM();\n      break;\n    case 'xhr':\n      instrumentXHR();\n      break;\n    case 'fetch':\n      instrumentFetch();\n      break;\n    case 'history':\n      instrumentHistory();\n      break;\n    case 'error':\n      instrumentError();\n      break;\n    case 'unhandledrejection':\n      instrumentUnhandledRejection();\n      break;\n    default:\n      logger.warn('unknown instrumentation type:', type);\n  }\n}\n\n/**\n * Add handler that will be called when given type of instrumentation triggers.\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addInstrumentationHandler(handler: InstrumentHandler): void {\n  if (!handler || typeof handler.type !== 'string' || typeof handler.callback !== 'function') {\n    return;\n  }\n  handlers[handler.type] = handlers[handler.type] || [];\n  (handlers[handler.type] as InstrumentHandlerCallback[]).push(handler.callback);\n  instrument(handler.type);\n}\n\n/** JSDoc */\nfunction triggerHandlers(type: InstrumentHandlerType, data: any): void {\n  if (!type || !handlers[type]) {\n    return;\n  }\n\n  for (const handler of handlers[type] || []) {\n    try {\n      handler(data);\n    } catch (e) {\n      logger.error(\n        `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(\n          handler,\n        )}\\nError: ${e}`,\n      );\n    }\n  }\n}\n\n/** JSDoc */\nfunction instrumentConsole(): void {\n  if (!('console' in global)) {\n    return;\n  }\n\n  ['debug', 'info', 'warn', 'error', 'log', 'assert'].forEach(function(level: string): void {\n    if (!(level in global.console)) {\n      return;\n    }\n\n    fill(global.console, level, function(originalConsoleLevel: () => any): Function {\n      return function(...args: any[]): void {\n        triggerHandlers('console', { args, level });\n\n        // this fails for some browsers. :(\n        if (originalConsoleLevel) {\n          Function.prototype.apply.call(originalConsoleLevel, global.console, args);\n        }\n      };\n    });\n  });\n}\n\n/** JSDoc */\nfunction instrumentFetch(): void {\n  if (!supportsNativeFetch()) {\n    return;\n  }\n\n  fill(global, 'fetch', function(originalFetch: () => void): () => void {\n    return function(...args: any[]): void {\n      const handlerData = {\n        args,\n        fetchData: {\n          method: getFetchMethod(args),\n          url: getFetchUrl(args),\n        },\n        startTimestamp: Date.now(),\n      };\n\n      triggerHandlers('fetch', {\n        ...handlerData,\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(global, args).then(\n        (response: Response) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            response,\n          });\n          return response;\n        },\n        (error: Error) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            error,\n          });\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\ntype XHRSendInput = null | Blob | BufferSource | FormData | URLSearchParams | string;\n\n/** JSDoc */\ninterface SentryWrappedXMLHttpRequest extends XMLHttpRequest {\n  [key: string]: any;\n  __sentry_xhr__?: {\n    method?: string;\n    url?: string;\n    status_code?: number;\n    body?: XHRSendInput;\n  };\n}\n\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/** Extract `method` from fetch call arguments */\nfunction getFetchMethod(fetchArgs: any[] = []): string {\n  if ('Request' in global && isInstanceOf(fetchArgs[0], Request) && fetchArgs[0].method) {\n    return String(fetchArgs[0].method).toUpperCase();\n  }\n  if (fetchArgs[1] && fetchArgs[1].method) {\n    return String(fetchArgs[1].method).toUpperCase();\n  }\n  return 'GET';\n}\n\n/** Extract `url` from fetch call arguments */\nfunction getFetchUrl(fetchArgs: any[] = []): string {\n  if (typeof fetchArgs[0] === 'string') {\n    return fetchArgs[0];\n  }\n  if ('Request' in global && isInstanceOf(fetchArgs[0], Request)) {\n    return fetchArgs[0].url;\n  }\n  return String(fetchArgs[0]);\n}\n/* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n/** JSDoc */\nfunction instrumentXHR(): void {\n  if (!('XMLHttpRequest' in global)) {\n    return;\n  }\n\n  // Poor man's implementation of ES6 `Map`, tracking and keeping in sync key and value separately.\n  const requestKeys: XMLHttpRequest[] = [];\n  const requestValues: Array<any>[] = [];\n  const xhrproto = XMLHttpRequest.prototype;\n\n  fill(xhrproto, 'open', function(originalOpen: () => void): () => void {\n    return function(this: SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const xhr = this;\n      const url = args[1];\n      xhr.__sentry_xhr__ = {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        method: isString(args[0]) ? args[0].toUpperCase() : args[0],\n        url: args[1],\n      };\n\n      // if Sentry key appears in URL, don't capture it as a request\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (isString(url) && xhr.__sentry_xhr__.method === 'POST' && url.match(/sentry_key/)) {\n        xhr.__sentry_own_request__ = true;\n      }\n\n      const onreadystatechangeHandler = function(): void {\n        if (xhr.readyState === 4) {\n          try {\n            // touching statusCode in some platforms throws\n            // an exception\n            if (xhr.__sentry_xhr__) {\n              xhr.__sentry_xhr__.status_code = xhr.status;\n            }\n          } catch (e) {\n            /* do nothing */\n          }\n\n          try {\n            const requestPos = requestKeys.indexOf(xhr);\n            if (requestPos !== -1) {\n              // Make sure to pop both key and value to keep it in sync.\n              requestKeys.splice(requestPos);\n              const args = requestValues.splice(requestPos)[0];\n              if (xhr.__sentry_xhr__ && args[0] !== undefined) {\n                xhr.__sentry_xhr__.body = args[0] as XHRSendInput;\n              }\n            }\n          } catch (e) {\n            /* do nothing */\n          }\n\n          triggerHandlers('xhr', {\n            args,\n            endTimestamp: Date.now(),\n            startTimestamp: Date.now(),\n            xhr,\n          });\n        }\n      };\n\n      if ('onreadystatechange' in xhr && typeof xhr.onreadystatechange === 'function') {\n        fill(xhr, 'onreadystatechange', function(original: WrappedFunction): Function {\n          return function(...readyStateArgs: any[]): void {\n            onreadystatechangeHandler();\n            return original.apply(xhr, readyStateArgs);\n          };\n        });\n      } else {\n        xhr.addEventListener('readystatechange', onreadystatechangeHandler);\n      }\n\n      return originalOpen.apply(xhr, args);\n    };\n  });\n\n  fill(xhrproto, 'send', function(originalSend: () => void): () => void {\n    return function(this: SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      requestKeys.push(this);\n      requestValues.push(args);\n\n      triggerHandlers('xhr', {\n        args,\n        startTimestamp: Date.now(),\n        xhr: this,\n      });\n\n      return originalSend.apply(this, args);\n    };\n  });\n}\n\nlet lastHref: string;\n\n/** JSDoc */\nfunction instrumentHistory(): void {\n  if (!supportsHistory()) {\n    return;\n  }\n\n  const oldOnPopState = global.onpopstate;\n  global.onpopstate = function(this: WindowEventHandlers, ...args: any[]): any {\n    const to = global.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n    triggerHandlers('history', {\n      from,\n      to,\n    });\n    if (oldOnPopState) {\n      return oldOnPopState.apply(this, args);\n    }\n  };\n\n  /** @hidden */\n  function historyReplacementFunction(originalHistoryFunction: () => void): () => void {\n    return function(this: History, ...args: any[]): void {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        // coerce to string (this is what pushState does)\n        const from = lastHref;\n        const to = String(url);\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n        triggerHandlers('history', {\n          from,\n          to,\n        });\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(global.history, 'pushState', historyReplacementFunction);\n  fill(global.history, 'replaceState', historyReplacementFunction);\n}\n\n/** JSDoc */\nfunction instrumentDOM(): void {\n  if (!('document' in global)) {\n    return;\n  }\n\n  // Capture breadcrumbs from any click that is unhandled / bubbled up all the way\n  // to the document. Do this before we instrument addEventListener.\n  global.document.addEventListener('click', domEventHandler('click', triggerHandlers.bind(null, 'dom')), false);\n  global.document.addEventListener('keypress', keypressEventHandler(triggerHandlers.bind(null, 'dom')), false);\n\n  // After hooking into document bubbled up click and keypresses events, we also hook into user handled click & keypresses.\n  ['EventTarget', 'Node'].forEach((target: string) => {\n    /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n    const proto = (global as any)[target] && (global as any)[target].prototype;\n\n    // eslint-disable-next-line no-prototype-builtins\n    if (!proto || !proto.hasOwnProperty || !proto.hasOwnProperty('addEventListener')) {\n      return;\n    }\n    /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n    fill(proto, 'addEventListener', function(\n      original: () => void,\n    ): (\n      eventName: string,\n      fn: EventListenerOrEventListenerObject,\n      options?: boolean | AddEventListenerOptions,\n    ) => void {\n      return function(\n        this: any,\n        eventName: string,\n        fn: EventListenerOrEventListenerObject,\n        options?: boolean | AddEventListenerOptions,\n      ): (eventName: string, fn: EventListenerOrEventListenerObject, capture?: boolean, secure?: boolean) => void {\n        if (fn && (fn as EventListenerObject).handleEvent) {\n          if (eventName === 'click') {\n            fill(fn, 'handleEvent', function(innerOriginal: () => void): (caughtEvent: Event) => void {\n              return function(this: any, event: Event): (event: Event) => void {\n                domEventHandler('click', triggerHandlers.bind(null, 'dom'))(event);\n                return innerOriginal.call(this, event);\n              };\n            });\n          }\n          if (eventName === 'keypress') {\n            fill(fn, 'handleEvent', function(innerOriginal: () => void): (caughtEvent: Event) => void {\n              return function(this: any, event: Event): (event: Event) => void {\n                keypressEventHandler(triggerHandlers.bind(null, 'dom'))(event);\n                return innerOriginal.call(this, event);\n              };\n            });\n          }\n        } else {\n          if (eventName === 'click') {\n            domEventHandler('click', triggerHandlers.bind(null, 'dom'), true)(this);\n          }\n          if (eventName === 'keypress') {\n            keypressEventHandler(triggerHandlers.bind(null, 'dom'))(this);\n          }\n        }\n\n        return original.call(this, eventName, fn, options);\n      };\n    });\n\n    fill(proto, 'removeEventListener', function(\n      original: () => void,\n    ): (\n      this: any,\n      eventName: string,\n      fn: EventListenerOrEventListenerObject,\n      options?: boolean | EventListenerOptions,\n    ) => () => void {\n      return function(\n        this: any,\n        eventName: string,\n        fn: EventListenerOrEventListenerObject,\n        options?: boolean | EventListenerOptions,\n      ): () => void {\n        try {\n          original.call(this, eventName, ((fn as unknown) as WrappedFunction).__sentry_wrapped__, options);\n        } catch (e) {\n          // ignore, accessing __sentry_wrapped__ will throw in some Selenium environments\n        }\n        return original.call(this, eventName, fn, options);\n      };\n    });\n  });\n}\n\nconst debounceDuration: number = 1000;\nlet debounceTimer: number = 0;\nlet keypressTimeout: number | undefined;\nlet lastCapturedEvent: Event | undefined;\n\n/**\n * Wraps addEventListener to capture UI breadcrumbs\n * @param name the event name (e.g. \"click\")\n * @param handler function that will be triggered\n * @param debounce decides whether it should wait till another event loop\n * @returns wrapped breadcrumb events handler\n * @hidden\n */\nfunction domEventHandler(name: string, handler: Function, debounce: boolean = false): (event: Event) => void {\n  return (event: Event): void => {\n    // reset keypress timeout; e.g. triggering a 'click' after\n    // a 'keypress' will reset the keypress debounce so that a new\n    // set of keypresses can be recorded\n    keypressTimeout = undefined;\n    // It's possible this handler might trigger multiple times for the same\n    // event (e.g. event propagation through node ancestors). Ignore if we've\n    // already captured the event.\n    if (!event || lastCapturedEvent === event) {\n      return;\n    }\n\n    lastCapturedEvent = event;\n\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n    }\n\n    if (debounce) {\n      debounceTimer = setTimeout(() => {\n        handler({ event, name });\n      });\n    } else {\n      handler({ event, name });\n    }\n  };\n}\n\n/**\n * Wraps addEventListener to capture keypress UI events\n * @param handler function that will be triggered\n * @returns wrapped keypress events handler\n * @hidden\n */\nfunction keypressEventHandler(handler: Function): (event: Event) => void {\n  // TODO: if somehow user switches keypress target before\n  //       debounce timeout is triggered, we will only capture\n  //       a single breadcrumb from the FIRST target (acceptable?)\n  return (event: Event): void => {\n    let target;\n\n    try {\n      target = event.target;\n    } catch (e) {\n      // just accessing event properties can throw an exception in some rare circumstances\n      // see: https://github.com/getsentry/raven-js/issues/838\n      return;\n    }\n\n    const tagName = target && (target as HTMLElement).tagName;\n\n    // only consider keypress events on actual input elements\n    // this will disregard keypresses targeting body (e.g. tabbing\n    // through elements, hotkeys, etc)\n    if (!tagName || (tagName !== 'INPUT' && tagName !== 'TEXTAREA' && !(target as HTMLElement).isContentEditable)) {\n      return;\n    }\n\n    // record first keypress in a series, but ignore subsequent\n    // keypresses until debounce clears\n    if (!keypressTimeout) {\n      domEventHandler('input', handler)(event);\n    }\n    clearTimeout(keypressTimeout);\n\n    keypressTimeout = (setTimeout(() => {\n      keypressTimeout = undefined;\n    }, debounceDuration) as any) as number;\n  };\n}\n\nlet _oldOnErrorHandler: OnErrorEventHandler = null;\n/** JSDoc */\nfunction instrumentError(): void {\n  _oldOnErrorHandler = global.onerror;\n\n  global.onerror = function(msg: any, url: any, line: any, column: any, error: any): boolean {\n    triggerHandlers('error', {\n      column,\n      error,\n      line,\n      msg,\n      url,\n    });\n\n    if (_oldOnErrorHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnErrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  };\n}\n\nlet _oldOnUnhandledRejectionHandler: ((e: any) => void) | null = null;\n/** JSDoc */\nfunction instrumentUnhandledRejection(): void {\n  _oldOnUnhandledRejectionHandler = global.onunhandledrejection;\n\n  global.onunhandledrejection = function(e: any): boolean {\n    triggerHandlers('unhandledrejection', e);\n\n    if (_oldOnUnhandledRejectionHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n}\n"]}