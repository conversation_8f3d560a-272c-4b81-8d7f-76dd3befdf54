{"version": 3, "file": "time.js", "sourceRoot": "", "sources": ["../src/time.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,QAAQ,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AASnD;;;;;;GAMG;AACH,IAAM,mBAAmB,GAAoB;IAC3C,UAAU,EAAE,cAAM,OAAA,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,EAAjB,CAAiB;CACpC,CAAC;AAiBF;;;;;GAKG;AACH,SAAS,qBAAqB;IACpB,IAAA,2CAAW,CAA+B;IAClD,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;QACpC,OAAO,SAAS,CAAC;KAClB;IAED,8EAA8E;IAC9E,EAAE;IACF,gHAAgH;IAChH,0DAA0D;IAC1D,EAAE;IACF,4GAA4G;IAC5G,aAAa;IACb,EAAE;IACF,iHAAiH;IACjH,wDAAwD;IACxD,EAAE;IACF,qHAAqH;IACrH,uGAAuG;IACvG,qHAAqH;IACrH,+DAA+D;IAC/D,EAAE;IACF,kEAAkE;IAClE,EAAE;IACF,iHAAiH;IACjH,0GAA0G;IAC1G,wCAAwC;IACxC,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IAElD,OAAO;QACL,GAAG,EAAE,cAAM,OAAA,WAAW,CAAC,GAAG,EAAE,EAAjB,CAAiB;QAC5B,UAAU,YAAA;KACX,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,kBAAkB;IACzB,IAAI;QACF,IAAM,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAiC,CAAC;QACvF,OAAO,SAAS,CAAC,WAAW,CAAC;KAC9B;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED;;GAEG;AACH,IAAM,mBAAmB,GAA4B,SAAS,EAAE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAElH,IAAM,eAAe,GACnB,mBAAmB,KAAK,SAAS;IAC/B,CAAC,CAAC,mBAAmB;IACrB,CAAC,CAAC;QACE,UAAU,EAAE,cAAM,OAAA,CAAC,mBAAmB,CAAC,UAAU,GAAG,mBAAmB,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,EAAnE,CAAmE;KACtF,CAAC;AAER;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAE/F;;;;;;;;;;GAUG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAEnF,4DAA4D;AAC5D,MAAM,CAAC,IAAM,eAAe,GAAG,kBAAkB,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,mBAAmB,KAAK,SAAS,CAAC;AAErE;;;GAGG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,CAAC;IACnC,IAAA,2CAAW,CAA+B;IAClD,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,WAAW,CAAC,UAAU,EAAE;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC;KAC/B;IACD,oHAAoH;IACpH,mGAAmG;IACnG,qHAAqH;IACrH,qHAAqH;IACrH,YAAY;IACZ,mDAAmD;IACnD,OAAO,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;AAClF,CAAC,CAAC,EAAE,CAAC", "sourcesContent": ["import { getGlobalObject } from './misc';\nimport { dynamicRequire, isNodeEnv } from './node';\n\n/**\n * An object that can return the current timestamp in seconds since the UNIX epoch.\n */\ninterface TimestampSource {\n  nowSeconds(): number;\n}\n\n/**\n * A TimestampSource implementation for environments that do not support the Performance Web API natively.\n *\n * Note that this TimestampSource does not use a monotonic clock. A call to `nowSeconds` may return a timestamp earlier\n * than a previously returned value. We do not try to emulate a monotonic behavior in order to facilitate debugging. It\n * is more obvious to explain \"why does my span have negative duration\" than \"why my spans have zero duration\".\n */\nconst dateTimestampSource: TimestampSource = {\n  nowSeconds: () => Date.now() / 1000,\n};\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction getBrowserPerformance(): Performance | undefined {\n  const { performance } = getGlobalObject<Window>();\n  if (!performance || !performance.now) {\n    return undefined;\n  }\n\n  // Replace performance.timeOrigin with our own timeOrigin based on Date.now().\n  //\n  // This is a partial workaround for browsers reporting performance.timeOrigin such that performance.timeOrigin +\n  // performance.now() gives a date arbitrarily in the past.\n  //\n  // Additionally, computing timeOrigin in this way fills the gap for browsers where performance.timeOrigin is\n  // undefined.\n  //\n  // The assumption that performance.timeOrigin + performance.now() ~= Date.now() is flawed, but we depend on it to\n  // interact with data coming out of performance entries.\n  //\n  // Note that despite recommendations against it in the spec, browsers implement the Performance API with a clock that\n  // might stop when the computer is asleep (and perhaps under other circumstances). Such behavior causes\n  // performance.timeOrigin + performance.now() to have an arbitrary skew over Date.now(). In laptop computers, we have\n  // observed skews that can be as long as days, weeks or months.\n  //\n  // See https://github.com/getsentry/sentry-javascript/issues/2590.\n  //\n  // BUG: despite our best intentions, this workaround has its limitations. It mostly addresses timings of pageload\n  // transactions, but ignores the skew built up over time that can aversely affect timestamps of navigation\n  // transactions of long-lived web pages.\n  const timeOrigin = Date.now() - performance.now();\n\n  return {\n    now: () => performance.now(),\n    timeOrigin,\n  };\n}\n\n/**\n * Returns the native Performance API implementation from Node.js. Returns undefined in old Node.js versions that don't\n * implement the API.\n */\nfunction getNodePerformance(): Performance | undefined {\n  try {\n    const perfHooks = dynamicRequire(module, 'perf_hooks') as { performance: Performance };\n    return perfHooks.performance;\n  } catch (_) {\n    return undefined;\n  }\n}\n\n/**\n * The Performance API implementation for the current platform, if available.\n */\nconst platformPerformance: Performance | undefined = isNodeEnv() ? getNodePerformance() : getBrowserPerformance();\n\nconst timestampSource: TimestampSource =\n  platformPerformance === undefined\n    ? dateTimestampSource\n    : {\n        nowSeconds: () => (platformPerformance.timeOrigin + platformPerformance.now()) / 1000,\n      };\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport const dateTimestampInSeconds = dateTimestampSource.nowSeconds.bind(dateTimestampSource);\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * See `usingPerformanceAPI` to test whether the Performance API is used.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds = timestampSource.nowSeconds.bind(timestampSource);\n\n// Re-exported with an old name for backwards-compatibility.\nexport const timestampWithMs = timestampInSeconds;\n\n/**\n * A boolean that is true when timestampInSeconds uses the Performance API to produce monotonic timestamps.\n */\nexport const usingPerformanceAPI = platformPerformance !== undefined;\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport const browserPerformanceTimeOrigin = ((): number | undefined => {\n  const { performance } = getGlobalObject<Window>();\n  if (!performance) {\n    return undefined;\n  }\n  if (performance.timeOrigin) {\n    return performance.timeOrigin;\n  }\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  return (performance.timing && performance.timing.navigationStart) || Date.now();\n})();\n"]}