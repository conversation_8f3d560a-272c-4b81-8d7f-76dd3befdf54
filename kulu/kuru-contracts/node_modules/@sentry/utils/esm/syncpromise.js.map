{"version": 3, "file": "syncpromise.js", "sourceRoot": "", "sources": ["../src/syncpromise.ts"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,+CAA+C;AAC/C,sEAAsE;AACtE,uDAAuD;AACvD,OAAO,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AAElC,kCAAkC;AAClC,IAAK,MAOJ;AAPD,WAAK,MAAM;IACT,cAAc;IACd,6BAAmB,CAAA;IACnB,oBAAoB;IACpB,+BAAqB,CAAA;IACrB,uBAAuB;IACvB,+BAAqB,CAAA;AACvB,CAAC,EAPI,MAAM,KAAN,MAAM,QAOV;AAED;;;GAGG;AACH;IASE,qBACE,QAAwG;QAD1G,iBAQC;QAhBO,WAAM,GAAW,MAAM,CAAC,OAAO,CAAC;QAChC,cAAS,GAIZ,EAAE,CAAC;QA+IR,YAAY;QACK,aAAQ,GAAG,UAAC,KAAiC;YAC5D,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,YAAY;QACK,YAAO,GAAG,UAAC,MAAY;YACtC,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF,YAAY;QACK,eAAU,GAAG,UAAC,KAAa,EAAE,KAAgC;YAC5E,IAAI,KAAI,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE;gBAClC,OAAO;aACR;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;gBACpB,KAAwB,CAAC,IAAI,CAAC,KAAI,CAAC,QAAQ,EAAE,KAAI,CAAC,OAAO,CAAC,CAAC;gBAC5D,OAAO;aACR;YAED,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YAEpB,KAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC;QAEF,cAAc;QACd,YAAY;QACK,mBAAc,GAAG,UAAC,OAOlC;YACC,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAChD,KAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC;QAEF,YAAY;QACK,qBAAgB,GAAG;YAClC,IAAI,KAAI,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE;gBAClC,OAAO;aACR;YAED,IAAM,cAAc,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC9C,KAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YAEpB,cAAc,CAAC,OAAO,CAAC,UAAA,OAAO;gBAC5B,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,OAAO;iBACR;gBAED,IAAI,KAAI,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE;oBACnC,IAAI,OAAO,CAAC,WAAW,EAAE;wBACvB,mEAAmE;wBACnE,OAAO,CAAC,WAAW,CAAE,KAAI,CAAC,MAAyB,CAAC,CAAC;qBACtD;iBACF;gBAED,IAAI,KAAI,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE;oBACnC,IAAI,OAAO,CAAC,UAAU,EAAE;wBACtB,OAAO,CAAC,UAAU,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;qBACjC;iBACF;gBAED,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QA/MA,IAAI;YACF,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACvC;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACjB;IACH,CAAC;IAED,YAAY;IACE,mBAAO,GAArB,UAAyB,KAAyB;QAChD,OAAO,IAAI,WAAW,CAAC,UAAA,OAAO;YAC5B,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACE,kBAAM,GAApB,UAAgC,MAAY;QAC1C,OAAO,IAAI,WAAW,CAAC,UAAC,CAAC,EAAE,MAAM;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACE,eAAG,GAAjB,UAA2B,UAAqC;QAC9D,OAAO,IAAI,WAAW,CAAM,UAAC,OAAO,EAAE,MAAM;YAC1C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC9B,MAAM,CAAC,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC,CAAC;gBACjE,OAAO;aACR;YAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAO,CAAC,EAAE,CAAC,CAAC;gBACZ,OAAO;aACR;YAED,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;YAChC,IAAM,kBAAkB,GAAQ,EAAE,CAAC;YAEnC,UAAU,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;gBAC7B,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;qBACtB,IAAI,CAAC,UAAA,KAAK;oBACT,kBAAkB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;oBAClC,OAAO,IAAI,CAAC,CAAC;oBAEb,IAAI,OAAO,KAAK,CAAC,EAAE;wBACjB,OAAO;qBACR;oBACD,OAAO,CAAC,kBAAkB,CAAC,CAAC;gBAC9B,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACL,0BAAI,GAAX,UACE,WAAqE,EACrE,UAAuE;QAFzE,iBAqCC;QAjCC,OAAO,IAAI,WAAW,CAAC,UAAC,OAAO,EAAE,MAAM;YACrC,KAAI,CAAC,cAAc,CAAC;gBAClB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,UAAA,MAAM;oBACjB,IAAI,CAAC,WAAW,EAAE;wBAChB,kBAAkB;wBAClB,cAAc;wBACd,OAAO,CAAC,MAAa,CAAC,CAAC;wBACvB,OAAO;qBACR;oBACD,IAAI;wBACF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;wBAC7B,OAAO;qBACR;oBAAC,OAAO,CAAC,EAAE;wBACV,MAAM,CAAC,CAAC,CAAC,CAAC;wBACV,OAAO;qBACR;gBACH,CAAC;gBACD,UAAU,EAAE,UAAA,MAAM;oBAChB,IAAI,CAAC,UAAU,EAAE;wBACf,MAAM,CAAC,MAAM,CAAC,CAAC;wBACf,OAAO;qBACR;oBACD,IAAI;wBACF,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBAC5B,OAAO;qBACR;oBAAC,OAAO,CAAC,EAAE;wBACV,MAAM,CAAC,CAAC,CAAC,CAAC;wBACV,OAAO;qBACR;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACL,2BAAK,GAAZ,UACE,UAAqE;QAErE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,EAAH,CAAG,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED,YAAY;IACL,6BAAO,GAAd,UAAwB,SAA+B;QAAvD,iBA6BC;QA5BC,OAAO,IAAI,WAAW,CAAU,UAAC,OAAO,EAAE,MAAM;YAC9C,IAAI,GAAkB,CAAC;YACvB,IAAI,UAAmB,CAAC;YAExB,OAAO,KAAI,CAAC,IAAI,CACd,UAAA,KAAK;gBACH,UAAU,GAAG,KAAK,CAAC;gBACnB,GAAG,GAAG,KAAK,CAAC;gBACZ,IAAI,SAAS,EAAE;oBACb,SAAS,EAAE,CAAC;iBACb;YACH,CAAC,EACD,UAAA,MAAM;gBACJ,UAAU,GAAG,IAAI,CAAC;gBAClB,GAAG,GAAG,MAAM,CAAC;gBACb,IAAI,SAAS,EAAE;oBACb,SAAS,EAAE,CAAC;iBACb;YACH,CAAC,CACF,CAAC,IAAI,CAAC;gBACL,IAAI,UAAU,EAAE;oBACd,MAAM,CAAC,GAAG,CAAC,CAAC;oBACZ,OAAO;iBACR;gBAED,OAAO,CAAE,GAAsB,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACL,8BAAQ,GAAf;QACE,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAyEH,kBAAC;AAAD,CAAC,AA5ND,IA4NC;AAED,OAAO,EAAE,WAAW,EAAE,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/typedef */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nenum States {\n  /** Pending */\n  PENDING = 'PENDING',\n  /** Resolved / OK */\n  RESOLVED = 'RESOLVED',\n  /** Rejected / Error */\n  REJECTED = 'REJECTED',\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise<T> implements PromiseLike<T> {\n  private _state: States = States.PENDING;\n  private _handlers: Array<{\n    done: boolean;\n    onfulfilled?: ((value: T) => T | PromiseLike<T>) | null;\n    onrejected?: ((reason: any) => any) | null;\n  }> = [];\n  private _value: any;\n\n  public constructor(\n    executor: (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void,\n  ) {\n    try {\n      executor(this._resolve, this._reject);\n    } catch (e) {\n      this._reject(e);\n    }\n  }\n\n  /** JSDoc */\n  public static resolve<T>(value: T | PromiseLike<T>): PromiseLike<T> {\n    return new SyncPromise(resolve => {\n      resolve(value);\n    });\n  }\n\n  /** JSDoc */\n  public static reject<T = never>(reason?: any): PromiseLike<T> {\n    return new SyncPromise((_, reject) => {\n      reject(reason);\n    });\n  }\n\n  /** JSDoc */\n  public static all<U = any>(collection: Array<U | PromiseLike<U>>): PromiseLike<U[]> {\n    return new SyncPromise<U[]>((resolve, reject) => {\n      if (!Array.isArray(collection)) {\n        reject(new TypeError(`Promise.all requires an array as input.`));\n        return;\n      }\n\n      if (collection.length === 0) {\n        resolve([]);\n        return;\n      }\n\n      let counter = collection.length;\n      const resolvedCollection: U[] = [];\n\n      collection.forEach((item, index) => {\n        SyncPromise.resolve(item)\n          .then(value => {\n            resolvedCollection[index] = value;\n            counter -= 1;\n\n            if (counter !== 0) {\n              return;\n            }\n            resolve(resolvedCollection);\n          })\n          .then(null, reject);\n      });\n    });\n  }\n\n  /** JSDoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._attachHandler({\n        done: false,\n        onfulfilled: result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n            return;\n          }\n          try {\n            resolve(onfulfilled(result));\n            return;\n          } catch (e) {\n            reject(e);\n            return;\n          }\n        },\n        onrejected: reason => {\n          if (!onrejected) {\n            reject(reason);\n            return;\n          }\n          try {\n            resolve(onrejected(reason));\n            return;\n          } catch (e) {\n            reject(e);\n            return;\n          }\n        },\n      });\n    });\n  }\n\n  /** JSDoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** JSDoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve((val as unknown) as any);\n      });\n    });\n  }\n\n  /** JSDoc */\n  public toString(): string {\n    return '[object SyncPromise]';\n  }\n\n  /** JSDoc */\n  private readonly _resolve = (value?: T | PromiseLike<T> | null) => {\n    this._setResult(States.RESOLVED, value);\n  };\n\n  /** JSDoc */\n  private readonly _reject = (reason?: any) => {\n    this._setResult(States.REJECTED, reason);\n  };\n\n  /** JSDoc */\n  private readonly _setResult = (state: States, value?: T | PromiseLike<T> | any) => {\n    if (this._state !== States.PENDING) {\n      return;\n    }\n\n    if (isThenable(value)) {\n      (value as PromiseLike<T>).then(this._resolve, this._reject);\n      return;\n    }\n\n    this._state = state;\n    this._value = value;\n\n    this._executeHandlers();\n  };\n\n  // TODO: FIXME\n  /** JSDoc */\n  private readonly _attachHandler = (handler: {\n    /** JSDoc */\n    done: boolean;\n    /** JSDoc */\n    onfulfilled?(value: T): any;\n    /** JSDoc */\n    onrejected?(reason: any): any;\n  }) => {\n    this._handlers = this._handlers.concat(handler);\n    this._executeHandlers();\n  };\n\n  /** JSDoc */\n  private readonly _executeHandlers = () => {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler.done) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        if (handler.onfulfilled) {\n          // eslint-disable-next-line @typescript-eslint/no-floating-promises\n          handler.onfulfilled((this._value as unknown) as any);\n        }\n      }\n\n      if (this._state === States.REJECTED) {\n        if (handler.onrejected) {\n          handler.onrejected(this._value);\n        }\n      }\n\n      handler.done = true;\n    });\n  };\n}\n\nexport { SyncPromise };\n"]}