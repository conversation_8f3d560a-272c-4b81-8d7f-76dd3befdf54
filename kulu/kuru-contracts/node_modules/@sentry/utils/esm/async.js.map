{"version": 3, "file": "async.js", "sourceRoot": "", "sources": ["../src/async.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,8DAA8D;AAC9D,MAAM,UAAU,MAAM,CAAC,OAAyB;IAC9C,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAA,CAAC;QAClB,uCAAuC;QACvC,sCAAsC;QACtC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * Consumes the promise and logs the error when it rejects.\n * @param promise A promise to forget.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function forget(promise: PromiseLike<any>): void {\n  promise.then(null, e => {\n    // TODO: Use a better logging mechanism\n    // eslint-disable-next-line no-console\n    console.error(e);\n  });\n}\n"]}