{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../src/browser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAEhC;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAa;IAK5C,kBAAkB;IAClB,8DAA8D;IAC9D,oFAAoF;IACpF,kDAAkD;IAClD,IAAI;QACF,IAAI,WAAW,GAAG,IAAkB,CAAC;QACrC,IAAM,mBAAmB,GAAG,CAAC,CAAC;QAC9B,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAM,SAAS,GAAG,KAAK,CAAC;QACxB,IAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;QACnC,IAAI,OAAO,SAAA,CAAC;QAEZ,uCAAuC;QACvC,OAAO,WAAW,IAAI,MAAM,EAAE,GAAG,mBAAmB,EAAE;YACpD,OAAO,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC5C,cAAc;YACd,kCAAkC;YAClC,0EAA0E;YAC1E,yDAAyD;YACzD,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,cAAc,CAAC,EAAE;gBACzG,MAAM;aACP;YAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAElB,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC;YACtB,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC;SACtC;QAED,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACtC;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,WAAW,CAAC;KACpB;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,EAAW;IACvC,IAAM,IAAI,GAAG,EAKZ,CAAC;IAEF,IAAM,GAAG,GAAG,EAAE,CAAC;IACf,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,CAAC;IACZ,IAAI,GAAG,CAAC;IACR,IAAI,IAAI,CAAC;IACT,IAAI,CAAC,CAAC;IAEN,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QAC1B,OAAO,EAAE,CAAC;KACX;IAED,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC,EAAE,EAAE;QACX,GAAG,CAAC,IAAI,CAAC,MAAI,IAAI,CAAC,EAAI,CAAC,CAAC;KACzB;IAED,wCAAwC;IACxC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IAC3B,IAAI,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,GAAG,CAAC,IAAI,CAAC,MAAI,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC;SAC5B;KACF;IACD,IAAM,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,IAAI,CAAC,MAAI,GAAG,WAAK,IAAI,QAAI,CAAC,CAAC;SAChC;KACF;IACD,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC", "sourcesContent": ["import { isString } from './is';\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(elem: unknown): string {\n  type SimpleNode = {\n    parentNode: SimpleNode;\n  } | null;\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const MAX_OUTPUT_LEN = 80;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n\n    // eslint-disable-next-line no-plusplus\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n  let className;\n  let classes;\n  let key;\n  let attr;\n  let i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n  if (elem.id) {\n    out.push(`#${elem.id}`);\n  }\n\n  // eslint-disable-next-line prefer-const\n  className = elem.className;\n  if (className && isString(className)) {\n    classes = className.split(/\\s+/);\n    for (i = 0; i < classes.length; i++) {\n      out.push(`.${classes[i]}`);\n    }\n  }\n  const allowedAttrs = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < allowedAttrs.length; i++) {\n    key = allowedAttrs[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push(`[${key}=\"${attr}\"]`);\n    }\n  }\n  return out.join('');\n}\n"]}