{"version": 3, "file": "is.js", "sourceRoot": "", "sources": ["../src/is.ts"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,sEAAsE;;AAGtE;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,GAAQ;IAC9B,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC3C,KAAK,gBAAgB;YACnB,OAAO,IAAI,CAAC;QACd,KAAK,oBAAoB;YACvB,OAAO,IAAI,CAAC;QACd,KAAK,uBAAuB;YAC1B,OAAO,IAAI,CAAC;QACd;YACE,OAAO,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KACnC;AACH,CAAC;AAXD,0BAWC;AAED;;;;;;GAMG;AACH,SAAgB,YAAY,CAAC,GAAQ;IACnC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,qBAAqB,CAAC;AACvE,CAAC;AAFD,oCAEC;AAED;;;;;;GAMG;AACH,SAAgB,UAAU,CAAC,GAAQ;IACjC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,mBAAmB,CAAC;AACrE,CAAC;AAFD,gCAEC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,GAAQ;IACrC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,uBAAuB,CAAC;AACzE,CAAC;AAFD,wCAEC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,GAAQ;IAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AACnE,CAAC;AAFD,4BAEC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,GAAQ;IAClC,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC;AAChF,CAAC;AAFD,kCAEC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,GAAQ;IACpC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AACnE,CAAC;AAFD,sCAEC;AAED;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,GAAQ;IAC9B,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC;AAFD,0BAEC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,GAAQ;IAChC,OAAO,OAAO,OAAO,KAAK,WAAW,IAAI,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAFD,8BAEC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,GAAQ;IAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AACnE,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,UAAU,CAAC,GAAQ;IACjC,sEAAsE;IACtE,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;AACpE,CAAC;AAHD,gCAGC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,GAAQ;IACvC,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,aAAa,IAAI,GAAG,IAAI,gBAAgB,IAAI,GAAG,IAAI,iBAAiB,IAAI,GAAG,CAAC;AAC3G,CAAC;AAFD,4CAEC;AACD;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,GAAQ,EAAE,IAAS;IAC9C,IAAI;QACF,OAAO,GAAG,YAAY,IAAI,CAAC;KAC5B;IAAC,OAAO,EAAE,EAAE;QACX,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAND,oCAMC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n\nimport { Primitive } from '@sentry/types';\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: any): boolean {\n  switch (Object.prototype.toString.call(wat)) {\n    case '[object Error]':\n      return true;\n    case '[object Exception]':\n      return true;\n    case '[object DOMException]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object ErrorEvent]';\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object DOMError]';\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object DOMException]';\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object String]';\n}\n\n/**\n * Checks whether given value's is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: any): wat is Primitive {\n  return wat === null || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object Object]';\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: any): boolean {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: any): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object RegExp]';\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): boolean {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat && wat.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: any): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n"]}