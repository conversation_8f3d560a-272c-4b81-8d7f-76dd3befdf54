{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": ";AAAA,uDAAuD;AACvD,+BAAyD;AAEzD,+DAA+D;AAC/D,IAAM,MAAM,GAAG,sBAAe,EAA0B,CAAC;AAEzD,iCAAiC;AACjC,IAAM,MAAM,GAAG,gBAAgB,CAAC;AAEhC,YAAY;AACZ;IAIE,YAAY;IACZ;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,YAAY;IACL,wBAAO,GAAd;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,YAAY;IACL,uBAAM,GAAb;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,YAAY;IACL,oBAAG,GAAV;QAAW,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,qBAAc,CAAC;YACb,MAAM,CAAC,OAAO,CAAC,GAAG,CAAI,MAAM,eAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACL,qBAAI,GAAX;QAAY,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,qBAAc,CAAC;YACb,MAAM,CAAC,OAAO,CAAC,IAAI,CAAI,MAAM,gBAAW,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACL,sBAAK,GAAZ;QAAa,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,qBAAc,CAAC;YACb,MAAM,CAAC,OAAO,CAAC,KAAK,CAAI,MAAM,iBAAY,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC;IACH,aAAC;AAAD,CAAC,AAhDD,IAgDC;AAED,0GAA0G;AAC1G,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC;AAC5C,IAAM,MAAM,GAAI,MAAM,CAAC,UAAU,CAAC,MAAiB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC,CAAC;AAExF,wBAAM", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { consoleSandbox, getGlobalObject } from './misc';\n\n// TODO: Implement different loggers for different environments\nconst global = getGlobalObject<Window | NodeJS.Global>();\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\n/** JSDoc */\nclass Logger {\n  /** JSDoc */\n  private _enabled: boolean;\n\n  /** JSDoc */\n  public constructor() {\n    this._enabled = false;\n  }\n\n  /** JSDoc */\n  public disable(): void {\n    this._enabled = false;\n  }\n\n  /** JSDoc */\n  public enable(): void {\n    this._enabled = true;\n  }\n\n  /** JSDoc */\n  public log(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.log(`${PREFIX}[Log]: ${args.join(' ')}`);\n    });\n  }\n\n  /** JSDoc */\n  public warn(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.warn(`${PREFIX}[Warn]: ${args.join(' ')}`);\n    });\n  }\n\n  /** JSDoc */\n  public error(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.error(`${PREFIX}[Error]: ${args.join(' ')}`);\n    });\n  }\n}\n\n// Ensure we only have a single logger instance, even if multiple versions of @sentry/utils are being used\nglobal.__SENTRY__ = global.__SENTRY__ || {};\nconst logger = (global.__SENTRY__.logger as Logger) || (global.__SENTRY__.logger = new Logger());\n\nexport { logger };\n"]}