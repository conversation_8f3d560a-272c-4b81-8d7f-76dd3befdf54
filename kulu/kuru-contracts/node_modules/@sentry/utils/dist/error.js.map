{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../src/error.ts"], "names": [], "mappings": ";;AAAA,uCAA4C;AAE5C,6DAA6D;AAC7D;IAAiC,uCAAK;IAIpC,qBAA0B,OAAe;;QAAzC,YACE,kBAAM,OAAO,CAAC,SAIf;QALyB,aAAO,GAAP,OAAO,CAAQ;QAGvC,KAAI,CAAC,IAAI,GAAG,WAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC;QAClD,yBAAc,CAAC,KAAI,EAAE,WAAW,SAAS,CAAC,CAAC;;IAC7C,CAAC;IACH,kBAAC;AAAD,CAAC,AAVD,CAAiC,KAAK,GAUrC;AAVY,kCAAW", "sourcesContent": ["import { setPrototypeOf } from './polyfill';\n\n/** An error emitted by Sentry SDKs and related utilities. */\nexport class SentryError extends Error {\n  /** Display name of this error instance. */\n  public name: string;\n\n  public constructor(public message: string) {\n    super(message);\n\n    this.name = new.target.prototype.constructor.name;\n    setPrototypeOf(this, new.target.prototype);\n  }\n}\n"]}