{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAAA,mCAAwD;AAexD;;;;GAIG;AACH,8DAA8D;AAC9D,SAAS,SAAS,CAAI,MAAc;IAAE,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAClD,IAAM,GAAG,GAAG,mBAAa,EAAE,CAAC;IAC5B,IAAI,GAAG,IAAI,GAAG,CAAC,MAAmB,CAAC,EAAE;QACnC,8DAA8D;QAC9D,OAAQ,GAAG,CAAC,MAAmB,CAAC,OAAxB,GAAG,mBAAiC,IAAI,GAAE;KACnD;IACD,MAAM,IAAI,KAAK,CAAC,uBAAqB,MAAM,yDAAsD,CAAC,CAAC;AACrG,CAAC;AAED;;;;;GAKG;AACH,iHAAiH;AACjH,SAAgB,gBAAgB,CAAC,SAAc,EAAE,cAA+B;IAC9E,IAAI,kBAAyB,CAAC;IAC9B,IAAI;QACF,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAAC,OAAO,SAAS,EAAE;QAClB,kBAAkB,GAAG,SAAkB,CAAC;KACzC;IACD,OAAO,SAAS,CAAC,kBAAkB,EAAE,SAAS,EAAE;QAC9C,cAAc,gBAAA;QACd,iBAAiB,EAAE,SAAS;QAC5B,kBAAkB,oBAAA;KACnB,CAAC,CAAC;AACL,CAAC;AAZD,4CAYC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,OAAe,EAAE,cAA0C;IACxF,IAAI,kBAAyB,CAAC;IAC9B,IAAI;QACF,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;KAC1B;IAAC,OAAO,SAAS,EAAE;QAClB,kBAAkB,GAAG,SAAkB,CAAC;KACzC;IAED,sFAAsF;IACtF,wDAAwD;IACxD,IAAM,KAAK,GAAG,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9E,IAAM,OAAO,GAAG,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAEpF,OAAO,SAAS,CAAC,gBAAgB,EAAE,OAAO,EAAE,KAAK,qBAC/C,iBAAiB,EAAE,OAAO,EAC1B,kBAAkB,oBAAA,IACf,OAAO,EACV,CAAC;AACL,CAAC;AAlBD,wCAkBC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,KAAY;IACvC,OAAO,SAAS,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,QAAgC;IAC7D,SAAS,CAAO,gBAAgB,EAAE,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAFD,wCAEC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,UAAsB;IAClD,SAAS,CAAO,eAAe,EAAE,UAAU,CAAC,CAAC;AAC/C,CAAC;AAFD,sCAEC;AAED;;;;GAIG;AACH,8DAA8D;AAC9D,SAAgB,UAAU,CAAC,IAAY,EAAE,OAAsC;IAC7E,SAAS,CAAO,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAFD,gCAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,MAAc;IACtC,SAAS,CAAO,WAAW,EAAE,MAAM,CAAC,CAAC;AACvC,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,OAAO,CAAC,IAAkC;IACxD,SAAS,CAAO,SAAS,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAFD,0BAEC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,GAAW,EAAE,KAAY;IAChD,SAAS,CAAO,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAFD,4BAEC;AAED;;;;;;;GAOG;AACH,SAAgB,MAAM,CAAC,GAAW,EAAE,KAAgB;IAClD,SAAS,CAAO,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC;AAFD,wBAEC;AAED;;;;GAIG;AACH,SAAgB,OAAO,CAAC,IAAiB;IACvC,SAAS,CAAO,SAAS,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAFD,0BAEC;AAED;;;;;;;;;;;;GAYG;AACH,SAAgB,SAAS,CAAC,QAAgC;IACxD,SAAS,CAAO,WAAW,EAAE,QAAQ,CAAC,CAAC;AACzC,CAAC;AAFD,8BAEC;AAED;;;;;;;;;GASG;AACH,8DAA8D;AAC9D,SAAgB,aAAa,CAAC,MAAc;IAAE,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAC1D,SAAS,iCAAO,eAAe,EAAE,MAAM,GAAK,IAAI,GAAE;AACpD,CAAC;AAFD,sCAEC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,gBAAgB,CAC9B,OAA2B,EAC3B,qBAA6C;IAE7C,OAAO,SAAS,CAAC,kBAAkB,uBAAO,OAAO,GAAI,qBAAqB,CAAC,CAAC;AAC9E,CAAC;AALD,4CAKC", "sourcesContent": ["import { getCur<PERSON>H<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@sentry/hub';\nimport {\n  Breadcrumb,\n  CaptureContext,\n  CustomSamplingContext,\n  Event,\n  Extra,\n  Extras,\n  Primitive,\n  Severity,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\n\n/**\n * This calls a function on the current hub.\n * @param method function to call on hub.\n * @param args to pass to function.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction callOnHub<T>(method: string, ...args: any[]): T {\n  const hub = getCurrentHub();\n  if (hub && hub[method as keyof Hub]) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (hub[method as keyof Hub] as any)(...args);\n  }\n  throw new Error(`No hub defined or ${method} was not found on the hub, please open a bug report.`);\n}\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception An exception-like object.\n * @returns The generated eventId.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nexport function captureException(exception: any, captureContext?: CaptureContext): string {\n  let syntheticException: Error;\n  try {\n    throw new Error('Sentry syntheticException');\n  } catch (exception) {\n    syntheticException = exception as Error;\n  }\n  return callOnHub('captureException', exception, {\n    captureContext,\n    originalException: exception,\n    syntheticException,\n  });\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param level Define the level of the message.\n * @returns The generated eventId.\n */\nexport function captureMessage(message: string, captureContext?: CaptureContext | Severity): string {\n  let syntheticException: Error;\n  try {\n    throw new Error(message);\n  } catch (exception) {\n    syntheticException = exception as Error;\n  }\n\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n\n  return callOnHub('captureMessage', message, level, {\n    originalException: message,\n    syntheticException,\n    ...context,\n  });\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @returns The generated eventId.\n */\nexport function captureEvent(event: Event): string {\n  return callOnHub('captureEvent', event);\n}\n\n/**\n * Callback to set context information onto the scope.\n * @param callback Callback function that receives Scope.\n */\nexport function configureScope(callback: (scope: Scope) => void): void {\n  callOnHub<void>('configureScope', callback);\n}\n\n/**\n * Records a new breadcrumb which will be attached to future events.\n *\n * Breadcrumbs will be added to subsequent events to provide more context on\n * user's actions prior to an error or crash.\n *\n * @param breadcrumb The breadcrumb to record.\n */\nexport function addBreadcrumb(breadcrumb: Breadcrumb): void {\n  callOnHub<void>('addBreadcrumb', breadcrumb);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function setContext(name: string, context: { [key: string]: any } | null): void {\n  callOnHub<void>('setContext', name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nexport function setExtras(extras: Extras): void {\n  callOnHub<void>('setExtras', extras);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nexport function setTags(tags: { [key: string]: Primitive }): void {\n  callOnHub<void>('setTags', tags);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nexport function setExtra(key: string, extra: Extra): void {\n  callOnHub<void>('setExtra', key, extra);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nexport function setTag(key: string, value: Primitive): void {\n  callOnHub<void>('setTag', key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nexport function setUser(user: User | null): void {\n  callOnHub<void>('setUser', user);\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n *\n * This is essentially a convenience function for:\n *\n *     pushScope();\n *     callback();\n *     popScope();\n *\n * @param callback that will be enclosed into push/popScope.\n */\nexport function withScope(callback: (scope: Scope) => void): void {\n  callOnHub<void>('withScope', callback);\n}\n\n/**\n * Calls a function on the latest client. Use this with caution, it's meant as\n * in \"internal\" helper so we don't need to expose every possible function in\n * the shim. It is not guaranteed that the client actually implements the\n * function.\n *\n * @param method The method to call on the client/client.\n * @param args Arguments to pass to the client/fontend.\n * @hidden\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function _callOnClient(method: string, ...args: any[]): void {\n  callOnHub<void>('_invokeClient', method, ...args);\n}\n\n/**\n * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.\n *\n * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a\n * new child span within the transaction or any span, call the respective `.startChild()` method.\n *\n * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.\n *\n * The transaction must be finished with a call to its `.finish()` method, at which point the transaction with all its\n * finished child spans will be sent to Sentry.\n *\n * @param context Properties of the new `Transaction`.\n * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent\n * default values). See {@link Options.tracesSampler}.\n *\n * @returns The transaction which was just started\n */\nexport function startTransaction(\n  context: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  return callOnHub('startTransaction', { ...context }, customSamplingContext);\n}\n"]}