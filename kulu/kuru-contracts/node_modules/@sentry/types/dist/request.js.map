{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../src/request.ts"], "names": [], "mappings": "", "sourcesContent": ["/** Possible SentryRequest types that can be used to make a distinction between Sentry features */\nexport type SentryRequestType = 'event' | 'transaction' | 'session';\n\n/** A generic client request. */\nexport interface SentryRequest {\n  body: string;\n  type: SentryRequestType;\n  url: string;\n}\n\n/** Request data included in an event as sent to Sentry */\nexport interface Request {\n  url?: string;\n  method?: string;\n  data?: any;\n  query_string?: string;\n  cookies?: { [key: string]: string };\n  env?: { [key: string]: string };\n  headers?: { [key: string]: string };\n}\n"]}