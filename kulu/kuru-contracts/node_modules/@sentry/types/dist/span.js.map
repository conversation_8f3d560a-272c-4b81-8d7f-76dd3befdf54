{"version": 3, "file": "span.js", "sourceRoot": "", "sources": ["../src/span.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Primitive } from './misc';\nimport { Transaction } from './transaction';\n\n/** Interface holding all properties that can be set on a Span on creation. */\nexport interface SpanContext {\n  /**\n   * Description of the Span.\n   */\n  description?: string;\n\n  /**\n   * Operation of the Span.\n   */\n  op?: string;\n\n  /**\n   * Completion status of the Span.\n   * See: {@sentry/tracing SpanStatus} for possible values\n   */\n  status?: string;\n\n  /**\n   * Parent Span ID\n   */\n  parentSpanId?: string;\n\n  /**\n   * Was this span chosen to be sent as part of the sample?\n   */\n  sampled?: boolean;\n\n  /**\n   * Span ID\n   */\n  spanId?: string;\n\n  /**\n   * Trace ID\n   */\n  traceId?: string;\n\n  /**\n   * Tags of the Span.\n   */\n  tags?: { [key: string]: Primitive };\n\n  /**\n   * Data of the Span.\n   */\n  data?: { [key: string]: any };\n\n  /**\n   * Timestamp in seconds (epoch time) indicating when the span started.\n   */\n  startTimestamp?: number;\n\n  /**\n   * Timestamp in seconds (epoch time) indicating when the span ended.\n   */\n  endTimestamp?: number;\n}\n\n/** Span holding trace_id, span_id */\nexport interface Span extends SpanContext {\n  /**\n   * @inheritDoc\n   */\n  spanId: string;\n\n  /**\n   * @inheritDoc\n   */\n  traceId: string;\n\n  /**\n   * @inheritDoc\n   */\n  startTimestamp: number;\n\n  /**\n   * @inheritDoc\n   */\n  tags: { [key: string]: Primitive };\n\n  /**\n   * @inheritDoc\n   */\n  data: { [key: string]: any };\n\n  /**\n   * The transaction containing this span\n   */\n  transaction?: Transaction;\n\n  /**\n   * Sets the finish timestamp on the current span.\n   * @param endTimestamp Takes an endTimestamp if the end should not be the time when you call this function.\n   */\n  finish(endTimestamp?: number): void;\n\n  /**\n   * Sets the tag attribute on the current span.\n   *\n   * Can also be used to unset a tag, by passing `undefined`.\n   *\n   * @param key Tag key\n   * @param value Tag value\n   */\n  setTag(key: string, value: Primitive): this;\n\n  /**\n   * Sets the data attribute on the current span\n   * @param key Data key\n   * @param value Data value\n   */\n  setData(key: string, value: any): this;\n\n  /**\n   * Sets the status attribute on the current span\n   * See: {@sentry/tracing SpanStatus} for possible values\n   * @param status http code used to set the status\n   */\n  setStatus(status: string): this;\n\n  /**\n   * Sets the status attribute on the current span based on the http code\n   * @param httpStatus http code used to set the status\n   */\n  setHttpStatus(httpStatus: number): this;\n\n  /**\n   * Use {@link startChild}\n   * @deprecated\n   */\n  child(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span;\n\n  /**\n   * Creates a new `Span` while setting the current `Span.id` as `parentSpanId`.\n   * Also the `sampled` decision will be inherited.\n   */\n  startChild(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span;\n\n  /**\n   * Determines whether span was successful (HTTP200)\n   */\n  isSuccess(): boolean;\n\n  /** Return a traceparent compatible header string */\n  toTraceparent(): string;\n\n  /** Convert the object to JSON for w. spans array info only */\n  getTraceContext(): {\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    trace_id: string;\n  };\n  /** Convert the object to JSON */\n  toJSON(): {\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    start_timestamp: number;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    timestamp?: number;\n    trace_id: string;\n  };\n}\n"]}