{"version": 3, "file": "event.js", "sourceRoot": "", "sources": ["../src/event.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Breadcrumb } from './breadcrumb';\nimport { Contexts } from './context';\nimport { Exception } from './exception';\nimport { Extras } from './extra';\nimport { Primitive } from './misc';\nimport { Request } from './request';\nimport { CaptureContext } from './scope';\nimport { SdkInfo } from './sdkinfo';\nimport { Severity } from './severity';\nimport { Span } from './span';\nimport { Stacktrace } from './stacktrace';\nimport { Measurements } from './transaction';\nimport { User } from './user';\n\n/** JSDoc */\nexport interface Event {\n  event_id?: string;\n  message?: string;\n  timestamp?: number;\n  start_timestamp?: number;\n  level?: Severity;\n  platform?: string;\n  logger?: string;\n  server_name?: string;\n  release?: string;\n  dist?: string;\n  environment?: string;\n  sdk?: SdkInfo;\n  request?: Request;\n  transaction?: string;\n  modules?: { [key: string]: string };\n  fingerprint?: string[];\n  exception?: {\n    values?: Exception[];\n  };\n  stacktrace?: Stacktrace;\n  breadcrumbs?: Breadcrumb[];\n  contexts?: Contexts;\n  tags?: { [key: string]: Primitive };\n  extra?: Extras;\n  user?: User;\n  type?: EventType;\n  spans?: Span[];\n  measurements?: Measurements;\n}\n\n/** JSDoc */\nexport type EventType = 'transaction';\n\n/** JSDoc */\nexport interface EventHint {\n  event_id?: string;\n  captureContext?: CaptureContext;\n  syntheticException?: Error | null;\n  originalException?: Error | string | null;\n  data?: any;\n}\n"]}