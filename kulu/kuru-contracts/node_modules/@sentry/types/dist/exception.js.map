{"version": 3, "file": "exception.js", "sourceRoot": "", "sources": ["../src/exception.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Mechanism } from './mechanism';\nimport { Stacktrace } from './stacktrace';\n\n/** JSDoc */\nexport interface Exception {\n  type?: string;\n  value?: string;\n  mechanism?: Mechanism;\n  module?: string;\n  thread_id?: number;\n  stacktrace?: Stacktrace;\n}\n"]}