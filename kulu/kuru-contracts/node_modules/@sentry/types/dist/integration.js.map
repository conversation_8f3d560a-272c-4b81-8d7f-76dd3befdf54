{"version": 3, "file": "integration.js", "sourceRoot": "", "sources": ["../src/integration.ts"], "names": [], "mappings": "", "sourcesContent": ["import { EventProcessor } from './eventprocessor';\nimport { Hub } from './hub';\n\n/** Integration Class Interface */\nexport interface IntegrationClass<T> {\n  /**\n   * Property that holds the integration name\n   */\n  id: string;\n\n  new (...args: any[]): T;\n}\n\n/** Integration interface */\nexport interface Integration {\n  /**\n   * Returns {@link IntegrationClass.id}\n   */\n  name: string;\n\n  /**\n   * Sets the integration up only once.\n   * This takes no options on purpose, options should be passed in the constructor\n   */\n  setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void;\n}\n"]}