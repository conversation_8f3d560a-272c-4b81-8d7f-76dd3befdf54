{"version": 3, "file": "eventprocessor.js", "sourceRoot": "", "sources": ["../src/eventprocessor.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Event, EventHint } from './event';\n\n/**\n * Event processors are used to change the event before it will be send.\n * We strongly advise to make this function sync.\n * Returning a PromiseLike<Event | null> will work just fine, but better be sure that you know what you are doing.\n * Event processing will be deferred until your Promise is resolved.\n */\nexport type EventProcessor = (event: Event, hint?: EventHint) => PromiseLike<Event | null> | Event | null;\n"]}