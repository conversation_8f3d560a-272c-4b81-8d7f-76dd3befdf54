{"version": 3, "file": "loglevel.js", "sourceRoot": "", "sources": ["../src/loglevel.ts"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,MAAM,CAAN,IAAY,QASX;AATD,WAAY,QAAQ;IAClB,iCAAiC;IACjC,uCAAQ,CAAA;IACR,+CAA+C;IAC/C,yCAAS,CAAA;IACT,+DAA+D;IAC/D,yCAAS,CAAA;IACT,sCAAsC;IACtC,6CAAW,CAAA;AACb,CAAC,EATW,QAAQ,KAAR,QAAQ,QASnB", "sourcesContent": ["/** Console logging verbosity for the SDK. */\nexport enum LogLevel {\n  /** No logs will be generated. */\n  None = 0,\n  /** Only SDK internal errors will be logged. */\n  Error = 1,\n  /** Information useful for debugging the SDK will be logged. */\n  Debug = 2,\n  /** All SDK actions will be logged. */\n  Verbose = 3,\n}\n"]}