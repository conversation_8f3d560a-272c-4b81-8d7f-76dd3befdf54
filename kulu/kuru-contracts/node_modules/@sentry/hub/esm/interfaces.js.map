{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../src/interfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Client } from '@sentry/types';\n\nimport { Hub } from './hub';\nimport { Scope } from './scope';\n\n/**\n * A layer in the process stack.\n * @hidden\n */\nexport interface Layer {\n  client?: Client;\n  scope?: Scope;\n}\n\n/**\n * An object that contains a hub and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: {\n    hub?: Hub;\n    /**\n     * Extra Hub properties injected by various SDKs\n     */\n    extensions?: {\n      /** Hack to prevent bundlers from breaking our usage of the domain package in the cross-platform Hub package */\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      domain?: { [key: string]: any };\n    } & {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n}\n\nexport interface DomainAsCarrier extends Carrier {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  members: { [key: string]: any }[];\n}\n"]}