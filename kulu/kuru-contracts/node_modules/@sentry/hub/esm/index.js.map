{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,uBAAuB,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACzD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EACL,eAAe,EACf,aAAa,EACb,iBAAiB,EACjB,cAAc,EACd,GAAG,EACH,QAAQ,EACR,eAAe,GAChB,MAAM,OAAO,CAAC", "sourcesContent": ["export { Carrier, DomainAsCarrier, Layer } from './interfaces';\nexport { addGlobalEventProcessor, Scope } from './scope';\nexport { Session } from './session';\nexport {\n  getActiveDomain,\n  getCurrentHub,\n  getHubFromCarrier,\n  getMainCarrier,\n  Hub,\n  makeMain,\n  setHubOnCarrier,\n} from './hub';\n"]}