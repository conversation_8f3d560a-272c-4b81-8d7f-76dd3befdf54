{"version": 3, "file": "integration.js", "sourceRoot": "", "sources": ["../src/integration.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,uBAAuB,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAErE,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAEvC,MAAM,CAAC,IAAM,qBAAqB,GAAa,EAAE,CAAC;AAOlD,kCAAkC;AAClC,MAAM,UAAU,sBAAsB,CAAC,OAAgB;IACrD,IAAM,mBAAmB,GAAG,CAAC,OAAO,CAAC,mBAAmB,aAAQ,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC;IACpG,IAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC;IAC9C,IAAI,YAAY,GAAkB,EAAE,CAAC;IACrC,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QACnC,IAAM,uBAAqB,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QAChE,IAAM,yBAAuB,GAAa,EAAE,CAAC;QAE7C,mGAAmG;QACnG,mBAAmB,CAAC,OAAO,CAAC,UAAA,kBAAkB;YAC5C,IACE,uBAAqB,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7D,yBAAuB,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC/D;gBACA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACtC,yBAAuB,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACvD;QACH,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,gBAAgB,CAAC,OAAO,CAAC,UAAA,eAAe;YACtC,IAAI,yBAAuB,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBAChE,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnC,yBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACpD;QACH,CAAC,CAAC,CAAC;KACJ;SAAM,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;QACjD,YAAY,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QACrD,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;KAC5E;SAAM;QACL,YAAY,YAAO,mBAAmB,CAAC,CAAC;KACzC;IAED,sEAAsE;IACtE,IAAM,iBAAiB,GAAG,YAAY,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;IACxD,IAAM,eAAe,GAAG,OAAO,CAAC;IAChC,IAAI,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD,YAAY,CAAC,IAAI,OAAjB,YAAY,WAAS,YAAY,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,GAAE;KAC1F;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,8BAA8B;AAC9B,MAAM,UAAU,gBAAgB,CAAC,WAAwB;IACvD,IAAI,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1D,OAAO;KACR;IACD,WAAW,CAAC,SAAS,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAC9D,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,CAAC,GAAG,CAAC,4BAA0B,WAAW,CAAC,IAAM,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAoB,OAAU;IAC7D,IAAM,YAAY,GAAqB,EAAE,CAAC;IAC1C,sBAAsB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAA,WAAW;QACjD,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;QAC7C,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IACH,OAAO,YAAY,CAAC;AACtB,CAAC", "sourcesContent": ["import { addGlobalEventProcessor, getCurrentHub } from '@sentry/hub';\nimport { Integration, Options } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport interface IntegrationIndex {\n  [key: string]: Integration;\n}\n\n/** Gets integration to install */\nexport function getIntegrationsToSetup(options: Options): Integration[] {\n  const defaultIntegrations = (options.defaultIntegrations && [...options.defaultIntegrations]) || [];\n  const userIntegrations = options.integrations;\n  let integrations: Integration[] = [];\n  if (Array.isArray(userIntegrations)) {\n    const userIntegrationsNames = userIntegrations.map(i => i.name);\n    const pickedIntegrationsNames: string[] = [];\n\n    // Leave only unique default integrations, that were not overridden with provided user integrations\n    defaultIntegrations.forEach(defaultIntegration => {\n      if (\n        userIntegrationsNames.indexOf(defaultIntegration.name) === -1 &&\n        pickedIntegrationsNames.indexOf(defaultIntegration.name) === -1\n      ) {\n        integrations.push(defaultIntegration);\n        pickedIntegrationsNames.push(defaultIntegration.name);\n      }\n    });\n\n    // Don't add same user integration twice\n    userIntegrations.forEach(userIntegration => {\n      if (pickedIntegrationsNames.indexOf(userIntegration.name) === -1) {\n        integrations.push(userIntegration);\n        pickedIntegrationsNames.push(userIntegration.name);\n      }\n    });\n  } else if (typeof userIntegrations === 'function') {\n    integrations = userIntegrations(defaultIntegrations);\n    integrations = Array.isArray(integrations) ? integrations : [integrations];\n  } else {\n    integrations = [...defaultIntegrations];\n  }\n\n  // Make sure that if present, `Debug` integration will always run last\n  const integrationsNames = integrations.map(i => i.name);\n  const alwaysLastToRun = 'Debug';\n  if (integrationsNames.indexOf(alwaysLastToRun) !== -1) {\n    integrations.push(...integrations.splice(integrationsNames.indexOf(alwaysLastToRun), 1));\n  }\n\n  return integrations;\n}\n\n/** Setup given integration */\nexport function setupIntegration(integration: Integration): void {\n  if (installedIntegrations.indexOf(integration.name) !== -1) {\n    return;\n  }\n  integration.setupOnce(addGlobalEventProcessor, getCurrentHub);\n  installedIntegrations.push(integration.name);\n  logger.log(`Integration installed: ${integration.name}`);\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations<O extends Options>(options: O): IntegrationIndex {\n  const integrations: IntegrationIndex = {};\n  getIntegrationsToSetup(options).forEach(integration => {\n    integrations[integration.name] = integration;\n    setupIntegration(integration);\n  });\n  return integrations;\n}\n"]}