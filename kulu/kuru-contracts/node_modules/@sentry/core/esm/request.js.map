{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../src/request.ts"], "names": [], "mappings": ";AAIA,6CAA6C;AAC7C,MAAM,UAAU,sBAAsB,CAAC,OAAgB,EAAE,GAAQ;IAC/D,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KAClC,CAAC,CAAC;IACH,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,EAAE,SAAS;KAChB,CAAC,CAAC;IAEH,OAAO;QACL,IAAI,EAAK,eAAe,UAAK,WAAW,UAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAG;QACtE,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,GAAG,CAAC,qCAAqC,EAAE;KACjD,CAAC;AACJ,CAAC;AAED,6CAA6C;AAC7C,MAAM,UAAU,oBAAoB,CAAC,KAAY,EAAE,GAAQ;IACzD,yCAAyC;IACzC,IAAM,qBAA6G,EAA3G,2CAAuC,EAAE,mCAA+B,EAAE,0EAAiC,CAAC;IACpH,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;IAEvB,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;IAEjD,IAAM,GAAG,GAAkB;QACzB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAC3B,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,OAAO;QAC3B,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,qCAAqC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,kCAAkC,EAAE;KAC1G,CAAC;IAEF,4CAA4C;IAE5C,0EAA0E;IAC1E,8EAA8E;IAC9E,8EAA8E;IAC9E,gCAAgC;IAChC,IAAI,WAAW,EAAE;QACf,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SAClC,CAAC,CAAC;QACH,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,IAAI,EAAE,KAAK,CAAC,IAAI;YAEhB,qGAAqG;YACrG,6DAA6D;YAC7D,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;SAezD,CAAC,CAAC;QACH,4EAA4E;QAC5E,6BAA6B;QAC7B,EAAE;QACF,wEAAwE;QACxE,IAAM,QAAQ,GAAM,eAAe,UAAK,WAAW,UAAK,GAAG,CAAC,IAAM,CAAC;QACnE,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;KACrB;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["import { Event, SentryRequest, Session } from '@sentry/types';\n\nimport { API } from './api';\n\n/** Creates a SentryRequest from an event. */\nexport function sessionToSentryRequest(session: Session, api: API): SentryRequest {\n  const envelopeHeaders = JSON.stringify({\n    sent_at: new Date().toISOString(),\n  });\n  const itemHeaders = JSON.stringify({\n    type: 'session',\n  });\n\n  return {\n    body: `${envelopeHeaders}\\n${itemHeaders}\\n${JSON.stringify(session)}`,\n    type: 'session',\n    url: api.getEnvelopeEndpointWithUrlEncodedAuth(),\n  };\n}\n\n/** Creates a SentryRequest from an event. */\nexport function eventToSentryRequest(event: Event, api: API): SentryRequest {\n  // since JS has no Object.prototype.pop()\n  const { __sentry_samplingMethod: samplingMethod, __sentry_sampleRate: sampleRate, ...otherTags } = event.tags || {};\n  event.tags = otherTags;\n\n  const useEnvelope = event.type === 'transaction';\n\n  const req: SentryRequest = {\n    body: JSON.stringify(event),\n    type: event.type || 'event',\n    url: useEnvelope ? api.getEnvelopeEndpointWithUrlEncodedAuth() : api.getStoreEndpointWithUrlEncodedAuth(),\n  };\n\n  // https://develop.sentry.dev/sdk/envelopes/\n\n  // Since we don't need to manipulate envelopes nor store them, there is no\n  // exported concept of an Envelope with operations including serialization and\n  // deserialization. Instead, we only implement a minimal subset of the spec to\n  // serialize events inline here.\n  if (useEnvelope) {\n    const envelopeHeaders = JSON.stringify({\n      event_id: event.event_id,\n      sent_at: new Date().toISOString(),\n    });\n    const itemHeaders = JSON.stringify({\n      type: event.type,\n\n      // TODO: Right now, sampleRate may or may not be defined (it won't be in the cases of inheritance and\n      // explicitly-set sampling decisions). Are we good with that?\n      sample_rates: [{ id: samplingMethod, rate: sampleRate }],\n\n      // The content-type is assumed to be 'application/json' and not part of\n      // the current spec for transaction items, so we don't bloat the request\n      // body with it.\n      //\n      // content_type: 'application/json',\n      //\n      // The length is optional. It must be the number of bytes in req.Body\n      // encoded as UTF-8. Since the server can figure this out and would\n      // otherwise refuse events that report the length incorrectly, we decided\n      // not to send the length to avoid problems related to reporting the wrong\n      // size and to reduce request body size.\n      //\n      // length: new TextEncoder().encode(req.body).length,\n    });\n    // The trailing newline is optional. We intentionally don't send it to avoid\n    // sending unnecessary bytes.\n    //\n    // const envelope = `${envelopeHeaders}\\n${itemHeaders}\\n${req.body}\\n`;\n    const envelope = `${envelopeHeaders}\\n${itemHeaders}\\n${req.body}`;\n    req.body = envelope;\n  }\n\n  return req;\n}\n"]}