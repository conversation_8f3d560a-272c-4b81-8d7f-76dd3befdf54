import { Event, EventHint, Options, Session, Severity, Transport } from '@sentry/types';
/**
 * Internal platform-dependent Sentry SDK Backend.
 *
 * While {@link Client} contains business logic specific to an SDK, the
 * Backend offers platform specific implementations for low-level operations.
 * These are persisting and loading information, sending events, and hooking
 * into the environment.
 *
 * Backends receive a handle to the Client in their constructor. When a
 * Backend automatically generates events, it must pass them to
 * the Client for validation and processing first.
 *
 * Usually, the Client will be of corresponding type, e.g. NodeBackend
 * receives NodeClient. However, higher-level SDKs can choose to instantiate
 * multiple Backends and delegate tasks between them. In this case, an event
 * generated by one backend might very well be sent by another one.
 *
 * The client also provides access to options via {@link Client.getOptions}.
 * @hidden
 */
export interface Backend {
    /** Creates a {@link Event} from an exception. */
    eventFromException(exception: any, hint?: EventHint): PromiseLike<Event>;
    /** Creates a {@link Event} from a plain message. */
    eventFromMessage(message: string, level?: Severity, hint?: EventHint): PromiseLike<Event>;
    /** Submits the event to Sentry */
    sendEvent(event: Event): void;
    /** Submits the session to Sentry */
    sendSession(session: Session): void;
    /**
     * Returns the transport that is used by the backend.
     * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.
     *
     * @returns The transport.
     */
    getTransport(): Transport;
}
/**
 * A class object that can instantiate Backend objects.
 * @hidden
 */
export declare type BackendClass<B extends Backend, O extends Options> = new (options: O) => B;
/**
 * This is the base implemention of a Backend.
 * @hidden
 */
export declare abstract class BaseBackend<O extends Options> implements Backend {
    /** Options passed to the SDK. */
    protected readonly _options: O;
    /** Cached transport used internally. */
    protected _transport: Transport;
    /** Creates a new backend instance. */
    constructor(options: O);
    /**
     * @inheritDoc
     */
    eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event>;
    /**
     * @inheritDoc
     */
    eventFromMessage(_message: string, _level?: Severity, _hint?: EventHint): PromiseLike<Event>;
    /**
     * @inheritDoc
     */
    sendEvent(event: Event): void;
    /**
     * @inheritDoc
     */
    sendSession(session: Session): void;
    /**
     * @inheritDoc
     */
    getTransport(): Transport;
    /**
     * Sets up the transport so it can be used later to send requests.
     */
    protected _setupTransport(): Transport;
}
//# sourceMappingURL=basebackend.d.ts.map