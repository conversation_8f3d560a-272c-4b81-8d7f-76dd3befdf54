{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,GACV,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,uBAAuB,EAAE,aAAa,EAAE,iBAAiB,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAC9G,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAgB,WAAW,EAAE,MAAM,eAAe,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,MAAM,WAAW,CAAC;AACzE,OAAO,EAAE,WAAW,EAAe,MAAM,OAAO,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,OAAO,KAAK,YAAY,MAAM,gBAAgB,CAAC;AAE/C,OAAO,EAAE,YAAY,EAAE,CAAC", "sourcesContent": ["export {\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  startTransaction,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  withScope,\n} from '@sentry/minimal';\nexport { addGlobalEventProcessor, getCurrentHub, getHubFromCarrier, Hub, makeMain, Scope } from '@sentry/hub';\nexport { API } from './api';\nexport { BaseClient } from './baseclient';\nexport { BackendClass, BaseBackend } from './basebackend';\nexport { eventToSentryRequest, sessionToSentryRequest } from './request';\nexport { initAndBind, ClientClass } from './sdk';\nexport { NoopTransport } from './transports/noop';\n\nimport * as Integrations from './integrations';\n\nexport { Integrations };\n"]}