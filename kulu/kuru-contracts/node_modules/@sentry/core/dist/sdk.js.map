{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../src/sdk.ts"], "names": [], "mappings": ";AAAA,mCAA4C;AAE5C,uCAAuC;AAKvC;;;;;;GAMG;AACH,SAAgB,WAAW,CAAsC,WAA8B,EAAE,OAAU;IACzG,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,EAAE;QAC1B,cAAM,CAAC,MAAM,EAAE,CAAC;KACjB;IACD,IAAM,GAAG,GAAG,mBAAa,EAAE,CAAC;IAC5B,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACxC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACzB,CAAC;AAPD,kCAOC", "sourcesContent": ["import { getCurrentHub } from '@sentry/hub';\nimport { Client, Options } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\n/** A class object that can instantiate Client objects. */\nexport type ClientClass<F extends Client, O extends Options> = new (options: O) => F;\n\n/**\n * Internal function to create a new SDK client instance. The client is\n * installed and then bound to the current scope.\n *\n * @param clientClass The client class to instantiate.\n * @param options Options to pass to the client.\n */\nexport function initAndBind<F extends Client, O extends Options>(clientClass: ClientClass<F, O>, options: O): void {\n  if (options.debug === true) {\n    logger.enable();\n  }\n  const hub = getCurrentHub();\n  const client = new clientClass(options);\n  hub.bindClient(client);\n}\n"]}