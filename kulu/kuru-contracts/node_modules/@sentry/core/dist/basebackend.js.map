{"version": 3, "file": "basebackend.js", "sourceRoot": "", "sources": ["../src/basebackend.ts"], "names": [], "mappings": ";AACA,uCAAoD;AAEpD,0CAAkD;AAmDlD;;;GAGG;AACH;IAOE,sCAAsC;IACtC,qBAAmB,OAAU;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACtB,cAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,iHAAiH;IAC1G,wCAAkB,GAAzB,UAA0B,UAAe,EAAE,KAAiB;QAC1D,MAAM,IAAI,mBAAW,CAAC,sDAAsD,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,sCAAgB,GAAvB,UAAwB,QAAgB,EAAE,MAAiB,EAAE,KAAiB;QAC5E,MAAM,IAAI,mBAAW,CAAC,oDAAoD,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,+BAAS,GAAhB,UAAiB,KAAY;QAC3B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,UAAA,MAAM;YAChD,cAAM,CAAC,KAAK,CAAC,gCAA8B,MAAQ,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,iCAAW,GAAlB,UAAmB,OAAgB;QACjC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAChC,cAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YACvF,OAAO;SACR;QAED,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,UAAA,MAAM;YACpD,cAAM,CAAC,KAAK,CAAC,kCAAgC,MAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,kCAAY,GAAnB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACO,qCAAe,GAAzB;QACE,OAAO,IAAI,oBAAa,EAAE,CAAC;IAC7B,CAAC;IACH,kBAAC;AAAD,CAAC,AAnED,IAmEC;AAnEqB,kCAAW", "sourcesContent": ["import { Event, EventHint, Options, Session, Severity, Transport } from '@sentry/types';\nimport { logger, SentryError } from '@sentry/utils';\n\nimport { NoopTransport } from './transports/noop';\n\n/**\n * Internal platform-dependent Sentry SDK Backend.\n *\n * While {@link Client} contains business logic specific to an SDK, the\n * Backend offers platform specific implementations for low-level operations.\n * These are persisting and loading information, sending events, and hooking\n * into the environment.\n *\n * Backends receive a handle to the Client in their constructor. When a\n * Backend automatically generates events, it must pass them to\n * the Client for validation and processing first.\n *\n * Usually, the Client will be of corresponding type, e.g. NodeBackend\n * receives NodeClient. However, higher-level SDKs can choose to instantiate\n * multiple Backends and delegate tasks between them. In this case, an event\n * generated by one backend might very well be sent by another one.\n *\n * The client also provides access to options via {@link Client.getOptions}.\n * @hidden\n */\nexport interface Backend {\n  /** Creates a {@link Event} from an exception. */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  eventFromException(exception: any, hint?: EventHint): PromiseLike<Event>;\n\n  /** Creates a {@link Event} from a plain message. */\n  eventFromMessage(message: string, level?: Severity, hint?: EventHint): PromiseLike<Event>;\n\n  /** Submits the event to Sentry */\n  sendEvent(event: Event): void;\n\n  /** Submits the session to Sentry */\n  sendSession(session: Session): void;\n\n  /**\n   * Returns the transport that is used by the backend.\n   * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.\n   *\n   * @returns The transport.\n   */\n  getTransport(): Transport;\n}\n\n/**\n * A class object that can instantiate Backend objects.\n * @hidden\n */\nexport type BackendClass<B extends Backend, O extends Options> = new (options: O) => B;\n\n/**\n * This is the base implemention of a Backend.\n * @hidden\n */\nexport abstract class BaseBackend<O extends Options> implements Backend {\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** Cached transport used internally. */\n  protected _transport: Transport;\n\n  /** Creates a new backend instance. */\n  public constructor(options: O) {\n    this._options = options;\n    if (!this._options.dsn) {\n      logger.warn('No DSN provided, backend will not do anything.');\n    }\n    this._transport = this._setupTransport();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event> {\n    throw new SentryError('Backend has to implement `eventFromException` method');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(_message: string, _level?: Severity, _hint?: EventHint): PromiseLike<Event> {\n    throw new SentryError('Backend has to implement `eventFromMessage` method');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): void {\n    this._transport.sendEvent(event).then(null, reason => {\n      logger.error(`Error while sending event: ${reason}`);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session): void {\n    if (!this._transport.sendSession) {\n      logger.warn(\"Dropping session because custom transport doesn't implement sendSession\");\n      return;\n    }\n\n    this._transport.sendSession(session).then(null, reason => {\n      logger.error(`Error while sending session: ${reason}`);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransport(): Transport {\n    return this._transport;\n  }\n\n  /**\n   * Sets up the transport so it can be used later to send requests.\n   */\n  protected _setupTransport(): Transport {\n    return new NoopTransport();\n  }\n}\n"]}