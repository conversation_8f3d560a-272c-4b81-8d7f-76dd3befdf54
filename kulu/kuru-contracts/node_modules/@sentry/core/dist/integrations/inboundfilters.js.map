{"version": 3, "file": "inboundfilters.js", "sourceRoot": "", "sources": ["../../src/integrations/inboundfilters.ts"], "names": [], "mappings": ";;AAAA,mCAAqE;AAErE,uCAA+E;AAE/E,6EAA6E;AAC7E,mFAAmF;AACnF,IAAM,qBAAqB,GAAG,CAAC,mBAAmB,EAAE,+CAA+C,CAAC,CAAC;AAerG,+CAA+C;AAC/C;IAWE,wBAAoC,QAA6C;QAA7C,yBAAA,EAAA,aAA6C;QAA7C,aAAQ,GAAR,QAAQ,CAAqC;QALjF;;WAEG;QACI,SAAI,GAAW,cAAc,CAAC,EAAE,CAAC;IAE4C,CAAC;IAErF;;OAEG;IACI,kCAAS,GAAhB;QACE,6BAAuB,CAAC,UAAC,KAAY;YACnC,IAAM,GAAG,GAAG,mBAAa,EAAE,CAAC;YAC5B,IAAI,CAAC,GAAG,EAAE;gBACR,OAAO,KAAK,CAAC;aACd;YACD,IAAM,IAAI,GAAG,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAChD,IAAI,IAAI,EAAE;gBACR,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC/B,IAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxD,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAClD,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;oBACzC,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;IACJ,yCAAgB,GAAxB,UAAyB,KAAY,EAAE,OAAuC;QAC5E,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACvC,cAAM,CAAC,IAAI,CAAC,+DAA6D,2BAAmB,CAAC,KAAK,CAAG,CAAC,CAAC;YACvG,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACxC,cAAM,CAAC,IAAI,CACT,0EAA0E,2BAAmB,CAAC,KAAK,CAAG,CACvG,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACrC,cAAM,CAAC,IAAI,CACT,sEAAsE,2BAAmB,CACvF,KAAK,CACN,gBAAW,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAG,CAC7C,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACvC,cAAM,CAAC,IAAI,CACT,2EAA2E,2BAAmB,CAC5F,KAAK,CACN,gBAAW,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAG,CAC7C,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY;IACJ,uCAAc,GAAtB,UAAuB,KAAY,EAAE,OAAuC;QAC1E,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QAED,IAAI;YACF,OAAO,CACL,CAAC,KAAK;gBACJ,KAAK,CAAC,SAAS;gBACf,KAAK,CAAC,SAAS,CAAC,MAAM;gBACtB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBACzB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC;gBACnD,KAAK,CACN,CAAC;SACH;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,YAAY;IACJ,wCAAe,GAAvB,UAAwB,KAAY,EAAE,OAAuC;QAC3E,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE;YACzD,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,OAAO;YACvD,4CAA4C;YAC5C,OAAC,OAAO,CAAC,YAAuC,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,yBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAnC,CAAmC,CAAC;QAArG,CAAqG,CACtG,CAAC;IACJ,CAAC;IAED,YAAY;IACJ,qCAAY,GAApB,UAAqB,KAAY,EAAE,OAAuC;QACxE,0BAA0B;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;YACjD,OAAO,KAAK,CAAC;SACd;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,yBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,EAA/B,CAA+B,CAAC,CAAC;IAC1F,CAAC;IAED,YAAY;IACJ,sCAAa,GAArB,UAAsB,KAAY,EAAE,OAAuC;QACzE,0BAA0B;QAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;YACnD,OAAO,IAAI,CAAC;SACb;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,yBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,EAA/B,CAA+B,CAAC,CAAC;IAC1F,CAAC;IAED,YAAY;IACJ,sCAAa,GAArB,UAAsB,aAAkD;QAAlD,8BAAA,EAAA,kBAAkD;QACtE,OAAO;YACL,SAAS,mBAEJ,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC,EACnC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC,EAE/B,CAAC,aAAa,CAAC,aAAa,IAAI,EAAE,CAAC,EACnC,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE,CAAC,CACnC;YACD,QAAQ,mBAEH,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC,EACnC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,EAE9B,CAAC,aAAa,CAAC,aAAa,IAAI,EAAE,CAAC,EACnC,CAAC,aAAa,CAAC,QAAQ,IAAI,EAAE,CAAC,CAClC;YACD,YAAY,mBACP,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,EAAE,CAAC,EAClC,CAAC,aAAa,CAAC,YAAY,IAAI,EAAE,CAAC,EAClC,qBAAqB,CACzB;YACD,cAAc,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI;SAC1G,CAAC;IACJ,CAAC;IAED,YAAY;IACJ,kDAAyB,GAAjC,UAAkC,KAAY;QAC5C,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACxB;QACD,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,IAAI;gBACI,IAAA,gEAAuF,EAArF,YAAS,EAAT,8BAAS,EAAE,aAAU,EAAV,+BAA0E,CAAC;gBAC9F,OAAO,CAAC,KAAG,KAAO,EAAK,IAAI,UAAK,KAAO,CAAC,CAAC;aAC1C;YAAC,OAAO,EAAE,EAAE;gBACX,cAAM,CAAC,KAAK,CAAC,sCAAoC,2BAAmB,CAAC,KAAK,CAAG,CAAC,CAAC;gBAC/E,OAAO,EAAE,CAAC;aACX;SACF;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,YAAY;IACJ,2CAAkB,GAA1B,UAA2B,KAAY;QACrC,IAAI;YACF,IAAI,KAAK,CAAC,UAAU,EAAE;gBACpB,IAAM,QAAM,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvC,OAAO,CAAC,QAAM,IAAI,QAAM,CAAC,QAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;aAC/D;YACD,IAAI,KAAK,CAAC,SAAS,EAAE;gBACnB,IAAM,QAAM,GACV,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;gBAChH,OAAO,CAAC,QAAM,IAAI,QAAM,CAAC,QAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;aAC/D;YACD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,EAAE,EAAE;YACX,cAAM,CAAC,KAAK,CAAC,kCAAgC,2BAAmB,CAAC,KAAK,CAAG,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAnLD;;OAEG;IACW,iBAAE,GAAW,gBAAgB,CAAC;IAiL9C,qBAAC;CAAA,AArLD,IAqLC;AArLY,wCAAc", "sourcesContent": ["import { addGlobalEventProcessor, getCurrentHub } from '@sentry/hub';\nimport { Event, Integration } from '@sentry/types';\nimport { getEventDescription, isMatchingPattern, logger } from '@sentry/utils';\n\n// \"Script error.\" is hard coded into browsers for errors that it can't read.\n// this is the result of a script being pulled in from an external domain and CORS.\nconst DEFAULT_IGNORE_ERRORS = [/^Script error\\.?$/, /^Javascript error: Script error\\.? on line 0$/];\n\n/** JSDoc */\ninterface InboundFiltersOptions {\n  allowUrls: Array<string | RegExp>;\n  denyUrls: Array<string | RegExp>;\n  ignoreErrors: Array<string | RegExp>;\n  ignoreInternal: boolean;\n\n  /** @deprecated use {@link InboundFiltersOptions.allowUrls} instead. */\n  whitelistUrls: Array<string | RegExp>;\n  /** @deprecated use {@link InboundFiltersOptions.denyUrls} instead. */\n  blacklistUrls: Array<string | RegExp>;\n}\n\n/** Inbound filters configurable by the user */\nexport class InboundFilters implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'InboundFilters';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = InboundFilters.id;\n\n  public constructor(private readonly _options: Partial<InboundFiltersOptions> = {}) {}\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    addGlobalEventProcessor((event: Event) => {\n      const hub = getCurrentHub();\n      if (!hub) {\n        return event;\n      }\n      const self = hub.getIntegration(InboundFilters);\n      if (self) {\n        const client = hub.getClient();\n        const clientOptions = client ? client.getOptions() : {};\n        const options = self._mergeOptions(clientOptions);\n        if (self._shouldDropEvent(event, options)) {\n          return null;\n        }\n      }\n      return event;\n    });\n  }\n\n  /** JSDoc */\n  private _shouldDropEvent(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (this._isSentryError(event, options)) {\n      logger.warn(`Event dropped due to being internal Sentry Error.\\nEvent: ${getEventDescription(event)}`);\n      return true;\n    }\n    if (this._isIgnoredError(event, options)) {\n      logger.warn(\n        `Event dropped due to being matched by \\`ignoreErrors\\` option.\\nEvent: ${getEventDescription(event)}`,\n      );\n      return true;\n    }\n    if (this._isDeniedUrl(event, options)) {\n      logger.warn(\n        `Event dropped due to being matched by \\`denyUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${this._getEventFilterUrl(event)}`,\n      );\n      return true;\n    }\n    if (!this._isAllowedUrl(event, options)) {\n      logger.warn(\n        `Event dropped due to not being matched by \\`allowUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${this._getEventFilterUrl(event)}`,\n      );\n      return true;\n    }\n    return false;\n  }\n\n  /** JSDoc */\n  private _isSentryError(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (!options.ignoreInternal) {\n      return false;\n    }\n\n    try {\n      return (\n        (event &&\n          event.exception &&\n          event.exception.values &&\n          event.exception.values[0] &&\n          event.exception.values[0].type === 'SentryError') ||\n        false\n      );\n    } catch (_oO) {\n      return false;\n    }\n  }\n\n  /** JSDoc */\n  private _isIgnoredError(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (!options.ignoreErrors || !options.ignoreErrors.length) {\n      return false;\n    }\n\n    return this._getPossibleEventMessages(event).some(message =>\n      // Not sure why TypeScript complains here...\n      (options.ignoreErrors as Array<RegExp | string>).some(pattern => isMatchingPattern(message, pattern)),\n    );\n  }\n\n  /** JSDoc */\n  private _isDeniedUrl(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    // TODO: Use Glob instead?\n    if (!options.denyUrls || !options.denyUrls.length) {\n      return false;\n    }\n    const url = this._getEventFilterUrl(event);\n    return !url ? false : options.denyUrls.some(pattern => isMatchingPattern(url, pattern));\n  }\n\n  /** JSDoc */\n  private _isAllowedUrl(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    // TODO: Use Glob instead?\n    if (!options.allowUrls || !options.allowUrls.length) {\n      return true;\n    }\n    const url = this._getEventFilterUrl(event);\n    return !url ? true : options.allowUrls.some(pattern => isMatchingPattern(url, pattern));\n  }\n\n  /** JSDoc */\n  private _mergeOptions(clientOptions: Partial<InboundFiltersOptions> = {}): Partial<InboundFiltersOptions> {\n    return {\n      allowUrls: [\n        // eslint-disable-next-line deprecation/deprecation\n        ...(this._options.whitelistUrls || []),\n        ...(this._options.allowUrls || []),\n        // eslint-disable-next-line deprecation/deprecation\n        ...(clientOptions.whitelistUrls || []),\n        ...(clientOptions.allowUrls || []),\n      ],\n      denyUrls: [\n        // eslint-disable-next-line deprecation/deprecation\n        ...(this._options.blacklistUrls || []),\n        ...(this._options.denyUrls || []),\n        // eslint-disable-next-line deprecation/deprecation\n        ...(clientOptions.blacklistUrls || []),\n        ...(clientOptions.denyUrls || []),\n      ],\n      ignoreErrors: [\n        ...(this._options.ignoreErrors || []),\n        ...(clientOptions.ignoreErrors || []),\n        ...DEFAULT_IGNORE_ERRORS,\n      ],\n      ignoreInternal: typeof this._options.ignoreInternal !== 'undefined' ? this._options.ignoreInternal : true,\n    };\n  }\n\n  /** JSDoc */\n  private _getPossibleEventMessages(event: Event): string[] {\n    if (event.message) {\n      return [event.message];\n    }\n    if (event.exception) {\n      try {\n        const { type = '', value = '' } = (event.exception.values && event.exception.values[0]) || {};\n        return [`${value}`, `${type}: ${value}`];\n      } catch (oO) {\n        logger.error(`Cannot extract message for event ${getEventDescription(event)}`);\n        return [];\n      }\n    }\n    return [];\n  }\n\n  /** JSDoc */\n  private _getEventFilterUrl(event: Event): string | null {\n    try {\n      if (event.stacktrace) {\n        const frames = event.stacktrace.frames;\n        return (frames && frames[frames.length - 1].filename) || null;\n      }\n      if (event.exception) {\n        const frames =\n          event.exception.values && event.exception.values[0].stacktrace && event.exception.values[0].stacktrace.frames;\n        return (frames && frames[frames.length - 1].filename) || null;\n      }\n      return null;\n    } catch (oO) {\n      logger.error(`Cannot extract url for event ${getEventDescription(event)}`);\n      return null;\n    }\n  }\n}\n"]}