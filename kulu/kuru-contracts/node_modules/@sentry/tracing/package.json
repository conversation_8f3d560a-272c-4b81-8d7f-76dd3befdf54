{"name": "@sentry/tracing", "version": "5.30.0", "description": "Extensions for Sentry AM", "repository": "git://github.com/getsentry/sentry-javascript.git", "homepage": "https://github.com/getsentry/sentry-javascript/tree/master/packages/tracing", "author": "Sentry", "license": "MIT", "engines": {"node": ">=6"}, "main": "dist/index.js", "module": "esm/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "dependencies": {"@sentry/hub": "5.30.0", "@sentry/minimal": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "devDependencies": {"@sentry-internal/eslint-config-sdk": "5.30.0", "@sentry/browser": "5.30.0", "@types/express": "^4.17.1", "@types/jsdom": "^16.2.3", "eslint": "7.6.0", "jest": "^24.7.1", "jsdom": "^16.2.2", "npm-run-all": "^4.1.2", "prettier": "1.19.0", "rimraf": "^2.6.3", "rollup": "^1.10.1", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-license": "^0.8.1", "rollup-plugin-node-resolve": "^4.2.3", "rollup-plugin-terser": "^4.0.4", "rollup-plugin-typescript2": "^0.21.0", "typescript": "3.7.5"}, "scripts": {"build": "run-p build:es5 build:esm build:bundle", "build:bundle": "rollup --config", "build:bundle:watch": "rollup --config --watch", "build:es5": "tsc -p tsconfig.build.json", "build:esm": "tsc -p tsconfig.esm.json", "build:watch": "run-p build:watch:es5 build:watch:esm", "build:watch:es5": "tsc -p tsconfig.build.json -w --preserveWatchOutput", "build:watch:esm": "tsc -p tsconfig.esm.json -w --preserveWatchOutput", "clean": "rimraf dist coverage build esm", "link:yarn": "yarn link", "lint": "run-s lint:prettier lint:eslint", "lint:prettier": "prettier --check \"{src,test}/**/*.ts\"", "lint:eslint": "eslint . --cache --cache-location '../../eslintcache/' --format stylish", "fix": "run-s fix:eslint fix:prettier", "fix:prettier": "prettier --write \"{src,test}/**/*.ts\"", "fix:eslint": "eslint . --format stylish --fix", "test": "jest", "test:watch": "jest --watch", "pack": "npm pack"}, "volta": {"extends": "../../package.json"}, "jest": {"collectCoverage": true, "transform": {"^.+\\.ts$": "ts-jest"}, "moduleFileExtensions": ["js", "ts"], "testEnvironment": "node", "testMatch": ["**/*.test.ts"], "globals": {"ts-jest": {"tsConfig": "./tsconfig.json", "diagnostics": false}}}, "sideEffects": ["./src/index.ts"]}