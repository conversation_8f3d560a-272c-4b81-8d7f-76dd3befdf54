{"version": 3, "file": "mysql.js", "sourceRoot": "", "sources": ["../../src/integrations/mysql.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAQ7D,iDAAiD;AACjD;IAAA;QAME;;WAEG;QACI,SAAI,GAAW,KAAK,CAAC,EAAE,CAAC;IA+CjC,CAAC;IA7CC;;OAEG;IACI,yBAAS,GAAhB,UAAiB,CAAqC,EAAE,aAAwB;QAC9E,IAAI,UAA2B,CAAC;QAEhC,IAAI;YACF,sGAAsG;YACtG,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;SAChE;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACzE,OAAO;SACR;QAED,2DAA2D;QAC3D,iCAAiC;QACjC,0CAA0C;QAC1C,kDAAkD;QAClD,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,UAAS,IAAgB;YAC3D,OAAO,UAAwB,OAAgB,EAAE,MAAe,EAAE,QAAiB;;gBACjF,IAAM,KAAK,GAAG,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACzC,IAAM,UAAU,SAAG,KAAK,0CAAE,OAAO,EAAE,CAAC;gBACpC,IAAM,IAAI,SAAG,UAAU,0CAAE,UAAU,CAAC;oBAClC,WAAW,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAE,OAA2B,CAAC,GAAG;oBACrF,EAAE,EAAE,IAAI;iBACT,CAAC,CAAC;gBAEH,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBAClC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAS,GAAU,EAAE,MAAe,EAAE,MAAe;;wBAC3F,MAAA,IAAI,0CAAE,MAAM,GAAG;wBACf,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;oBAChC,CAAC,CAAC,CAAC;iBACJ;gBAED,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,UAAS,GAAU,EAAE,MAAe,EAAE,MAAe;;wBACnF,MAAA,IAAI,0CAAE,MAAM,GAAG;wBACf,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC;iBACJ;gBAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACpD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAtDD;;OAEG;IACW,QAAE,GAAW,OAAO,CAAC;IAoDrC,YAAC;CAAA,AAxDD,IAwDC;SAxDY,KAAK", "sourcesContent": ["import { Hub } from '@sentry/hub';\nimport { EventProcessor, Integration } from '@sentry/types';\nimport { dynamicRequire, fill, logger } from '@sentry/utils';\n\ninterface MysqlConnection {\n  prototype: {\n    query: () => void;\n  };\n}\n\n/** Tracing integration for node-mysql package */\nexport class Mysql implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Mysql';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Mysql.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    let connection: MysqlConnection;\n\n    try {\n      // Unfortunatelly mysql is using some custom loading system and `Connection` is not exported directly.\n      connection = dynamicRequire(module, 'mysql/lib/Connection.js');\n    } catch (e) {\n      logger.error('Mysql Integration was unable to require `mysql` package.');\n      return;\n    }\n\n    // The original function will have one of these signatures:\n    //    function (callback) => void\n    //    function (options, callback) => void\n    //    function (options, values, callback) => void\n    fill(connection.prototype, 'query', function(orig: () => void) {\n      return function(this: unknown, options: unknown, values: unknown, callback: unknown) {\n        const scope = getCurrentHub().getScope();\n        const parentSpan = scope?.getSpan();\n        const span = parentSpan?.startChild({\n          description: typeof options === 'string' ? options : (options as { sql: string }).sql,\n          op: `db`,\n        });\n\n        if (typeof callback === 'function') {\n          return orig.call(this, options, values, function(err: Error, result: unknown, fields: unknown) {\n            span?.finish();\n            callback(err, result, fields);\n          });\n        }\n\n        if (typeof values === 'function') {\n          return orig.call(this, options, function(err: Error, result: unknown, fields: unknown) {\n            span?.finish();\n            values(err, result, fields);\n          });\n        }\n\n        return orig.call(this, options, values, callback);\n      };\n    });\n  }\n}\n"]}