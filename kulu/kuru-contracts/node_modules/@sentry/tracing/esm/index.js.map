{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAC3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AACtD,OAAO,KAAK,mBAAmB,MAAM,gBAAgB,CAAC;AAEtD,IAAM,YAAY,yBAAQ,mBAAmB,KAAE,cAAc,gBAAA,GAAE,CAAC;AAEhE,OAAO,EAAE,YAAY,EAAE,CAAC;AACxB,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EACL,8BAA8B,EAE9B,oCAAoC,GACrC,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;AAEvD,mEAAmE;AACnE,mBAAmB,EAAE,CAAC;AAEtB,OAAO,EAAE,mBAAmB,EAAE,CAAC;AAE/B,OAAO,EACL,sBAAsB,EACtB,oBAAoB,EACpB,iBAAiB,EACjB,wBAAwB,EACxB,kBAAkB,GACnB,MAAM,SAAS,CAAC", "sourcesContent": ["import { BrowserTracing } from './browser';\nimport { addExtensionMethods } from './hubextensions';\nimport * as TracingIntegrations from './integrations';\n\nconst Integrations = { ...TracingIntegrations, BrowserTracing };\n\nexport { Integrations };\nexport { Span } from './span';\nexport { Transaction } from './transaction';\nexport {\n  registerRequestInstrumentation,\n  RequestInstrumentationOptions,\n  defaultRequestInstrumentationOptions,\n} from './browser';\nexport { SpanStatus } from './spanstatus';\nexport { IdleTransaction } from './idletransaction';\nexport { startIdleTransaction } from './hubextensions';\n\n// We are patching the global object with our hub extension methods\naddExtensionMethods();\n\nexport { addExtensionMethods };\n\nexport {\n  extractTraceparentData,\n  getActiveTransaction,\n  hasTracingEnabled,\n  stripUrlQueryAndFragment,\n  TRACEPARENT_REGEXP,\n} from './utils';\n"]}