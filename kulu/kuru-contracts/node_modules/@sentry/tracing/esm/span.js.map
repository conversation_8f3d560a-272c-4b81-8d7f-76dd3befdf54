{"version": 3, "file": "span.js", "sourceRoot": "", "sources": ["../src/span.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAE1E,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;;;;GAKG;AACH;IAKE,sBAAmB,MAAqB;QAArB,uBAAA,EAAA,aAAqB;QAJjC,UAAK,GAAW,EAAE,CAAC;QAKxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACI,0BAAG,GAAV,UAAW,IAAU;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;YACpC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;SAC/B;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;IACH,CAAC;IACH,mBAAC;AAAD,CAAC,AAtBD,IAsBC;;AAED;;GAEG;AACH;IAmEE;;;;;;OAMG;IACH,cAAmB,WAAyB;QAzE5C;;WAEG;QACI,YAAO,GAAW,KAAK,EAAE,CAAC;QAEjC;;WAEG;QACI,WAAM,GAAW,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAiB9C;;WAEG;QACI,mBAAc,GAAW,eAAe,EAAE,CAAC;QAiBlD;;WAEG;QACI,SAAI,GAAiC,EAAE,CAAC;QAE/C;;WAEG;QACH,8DAA8D;QACvD,SAAI,GAA2B,EAAE,CAAC;QAoBvC,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,WAAW,CAAC,OAAO,EAAE;YACvB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;SACpC;QACD,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;SAClC;QACD,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;SAC9C;QACD,2CAA2C;QAC3C,IAAI,SAAS,IAAI,WAAW,EAAE;YAC5B,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;SACpC;QACD,IAAI,WAAW,CAAC,EAAE,EAAE;YAClB,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;SAC1B;QACD,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;SAC5C;QACD,IAAI,WAAW,CAAC,IAAI,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;SAC9B;QACD,IAAI,WAAW,CAAC,IAAI,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;SAC9B;QACD,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;SAClC;QACD,IAAI,WAAW,CAAC,cAAc,EAAE;YAC9B,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;SAClD;QACD,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;SAC9C;IACH,CAAC;IAED;;;OAGG;IACI,oBAAK,GAAZ,UACE,WAA8G;QAE9G,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,yBAAU,GAAjB,UACE,WAA8G;QAE9G,IAAM,SAAS,GAAG,IAAI,IAAI,uBACrB,WAAW,KACd,YAAY,EAAE,IAAI,CAAC,MAAM,EACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,IACrB,CAAC;QAEH,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,SAAS,CAAC,YAAY,EAAE;YAC1B,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAEzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,qBAAM,GAAb,UAAc,GAAW,EAAE,KAAgB;;QACzC,IAAI,CAAC,IAAI,yBAAQ,IAAI,CAAC,IAAI,gBAAG,GAAG,IAAG,KAAK,MAAE,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iHAAiH;IAC1G,sBAAO,GAAd,UAAe,GAAW,EAAE,KAAU;;QACpC,IAAI,CAAC,IAAI,yBAAQ,IAAI,CAAC,IAAI,gBAAG,GAAG,IAAG,KAAK,MAAE,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,wBAAS,GAAhB,UAAiB,KAAiB;QAChC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,4BAAa,GAApB,UAAqB,UAAkB;QACrC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACpD,IAAM,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,UAAU,KAAK,UAAU,CAAC,YAAY,EAAE;YAC1C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,wBAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,qBAAM,GAAb,UAAc,YAAqB;QACjC,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,4BAAa,GAApB;QACE,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC9B,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;SAC5C;QACD,OAAU,IAAI,CAAC,OAAO,SAAI,IAAI,CAAC,MAAM,GAAG,aAAe,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,8BAAe,GAAtB;QAWE,OAAO,iBAAiB,CAAC;YACvB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/D,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,cAAc,EAAE,IAAI,CAAC,YAAY;YACjC,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/D,QAAQ,EAAE,IAAI,CAAC,OAAO;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,qBAAM,GAAb;QAaE,OAAO,iBAAiB,CAAC;YACvB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/D,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,cAAc,EAAE,IAAI,CAAC,YAAY;YACjC,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,eAAe,EAAE,IAAI,CAAC,cAAc;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/D,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,QAAQ,EAAE,IAAI,CAAC,OAAO;SACvB,CAAC,CAAC;IACL,CAAC;IACH,WAAC;AAAD,CAAC,AAxQD,IAwQC", "sourcesContent": ["/* eslint-disable max-lines */\nimport { Primitive, Span as SpanInterface, SpanContext, Transaction } from '@sentry/types';\nimport { dropUndefinedKeys, timestampWithMs, uuid4 } from '@sentry/utils';\n\nimport { SpanStatus } from './spanstatus';\n\n/**\n * Keeps track of finished spans for a given transaction\n * @internal\n * @hideconstructor\n * @hidden\n */\nexport class SpanRecorder {\n  public spans: Span[] = [];\n\n  private readonly _maxlen: number;\n\n  public constructor(maxlen: number = 1000) {\n    this._maxlen = maxlen;\n  }\n\n  /**\n   * This is just so that we don't run out of memory while recording a lot\n   * of spans. At some point we just stop and flush out the start of the\n   * trace tree (i.e.the first n spans with the smallest\n   * start_timestamp).\n   */\n  public add(span: Span): void {\n    if (this.spans.length > this._maxlen) {\n      span.spanRecorder = undefined;\n    } else {\n      this.spans.push(span);\n    }\n  }\n}\n\n/**\n * Span contains all data about a span\n */\nexport class Span implements SpanInterface {\n  /**\n   * @inheritDoc\n   */\n  public traceId: string = uuid4();\n\n  /**\n   * @inheritDoc\n   */\n  public spanId: string = uuid4().substring(16);\n\n  /**\n   * @inheritDoc\n   */\n  public parentSpanId?: string;\n\n  /**\n   * Internal keeper of the status\n   */\n  public status?: SpanStatus | string;\n\n  /**\n   * @inheritDoc\n   */\n  public sampled?: boolean;\n\n  /**\n   * Timestamp in seconds when the span was created.\n   */\n  public startTimestamp: number = timestampWithMs();\n\n  /**\n   * Timestamp in seconds when the span ended.\n   */\n  public endTimestamp?: number;\n\n  /**\n   * @inheritDoc\n   */\n  public op?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public description?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public tags: { [key: string]: Primitive } = {};\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public data: { [key: string]: any } = {};\n\n  /**\n   * List of spans that were finalized\n   */\n  public spanRecorder?: SpanRecorder;\n\n  /**\n   * @inheritDoc\n   */\n  public transaction?: Transaction;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startTransaction()`\n   * or call `startChild()` on an existing span.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext?: SpanContext) {\n    if (!spanContext) {\n      return this;\n    }\n    if (spanContext.traceId) {\n      this.traceId = spanContext.traceId;\n    }\n    if (spanContext.spanId) {\n      this.spanId = spanContext.spanId;\n    }\n    if (spanContext.parentSpanId) {\n      this.parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this.sampled = spanContext.sampled;\n    }\n    if (spanContext.op) {\n      this.op = spanContext.op;\n    }\n    if (spanContext.description) {\n      this.description = spanContext.description;\n    }\n    if (spanContext.data) {\n      this.data = spanContext.data;\n    }\n    if (spanContext.tags) {\n      this.tags = spanContext.tags;\n    }\n    if (spanContext.status) {\n      this.status = spanContext.status;\n    }\n    if (spanContext.startTimestamp) {\n      this.startTimestamp = spanContext.startTimestamp;\n    }\n    if (spanContext.endTimestamp) {\n      this.endTimestamp = spanContext.endTimestamp;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated\n   */\n  public child(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span {\n    return this.startChild(spanContext);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startChild(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span {\n    const childSpan = new Span({\n      ...spanContext,\n      parentSpanId: this.spanId,\n      sampled: this.sampled,\n      traceId: this.traceId,\n    });\n\n    childSpan.spanRecorder = this.spanRecorder;\n    if (childSpan.spanRecorder) {\n      childSpan.spanRecorder.add(childSpan);\n    }\n\n    childSpan.transaction = this.transaction;\n\n    return childSpan;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this.tags = { ...this.tags, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public setData(key: string, value: any): this {\n    this.data = { ...this.data, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatus): this {\n    this.status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setHttpStatus(httpStatus: number): this {\n    this.setTag('http.status_code', String(httpStatus));\n    const spanStatus = SpanStatus.fromHttpCode(httpStatus);\n    if (spanStatus !== SpanStatus.UnknownError) {\n      this.setStatus(spanStatus);\n    }\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isSuccess(): boolean {\n    return this.status === SpanStatus.Ok;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): void {\n    this.endTimestamp = typeof endTimestamp === 'number' ? endTimestamp : timestampWithMs();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toTraceparent(): string {\n    let sampledString = '';\n    if (this.sampled !== undefined) {\n      sampledString = this.sampled ? '-1' : '-0';\n    }\n    return `${this.traceId}-${this.spanId}${sampledString}`;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTraceContext(): {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    trace_id: string;\n  } {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      trace_id: this.traceId,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toJSON(): {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    start_timestamp: number;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    timestamp?: number;\n    trace_id: string;\n  } {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      start_timestamp: this.startTimestamp,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      timestamp: this.endTimestamp,\n      trace_id: this.traceId,\n    });\n  }\n}\n"]}