{"version": 3, "file": "metrics.js", "sourceRoot": "", "sources": ["../../src/browser/metrics.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,4BAA4B,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAItF,OAAO,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AAGjE,IAAM,MAAM,GAAG,eAAe,EAAU,CAAC;AAEzC,8BAA8B;AAC9B;IAKE;QAJQ,kBAAa,GAAiB,EAAE,CAAC;QAEjC,uBAAkB,GAAW,CAAC,CAAC;QAGrC,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE;YAChC,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE;gBAC3B,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAChD;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAED,qDAAqD;IAC9C,sDAAqB,GAA5B,UAA6B,WAAwB;QAArD,iBAwIC;QAvIC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,4BAA4B,EAAE;YACrG,8CAA8C;YAC9C,OAAO;SACR;QAED,MAAM,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QAEvE,IAAM,UAAU,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACzD,IAAI,cAAkC,CAAC;QAEvC,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,4DAA4D;YAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,kEAAkE;gBAClE,qFAAqF;gBACrF,iCAAiC;gBACjC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM,EAAE;oBAChD,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;oBACzC,MAAM;iBACP;aACF;SACF;QAED,IAAI,yBAA6C,CAAC;QAClD,IAAI,wBAA4C,CAAC;QAEjD,MAAM,CAAC,WAAW;aACf,UAAU,EAAE;aACZ,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC;aAC9B,OAAO,CAAC,UAAC,KAA0B;YAClC,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YACrD,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAkB,CAAC,CAAC;YAEnD,IAAI,WAAW,CAAC,EAAE,KAAK,YAAY,IAAI,UAAU,GAAG,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE;gBAC1F,OAAO;aACR;YAED,QAAQ,KAAK,CAAC,SAAS,EAAE;gBACvB,KAAK,YAAY;oBACf,kBAAkB,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;oBACnD,MAAM;gBACR,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO,CAAC;gBACb,KAAK,SAAS,CAAC,CAAC;oBACd,IAAM,cAAc,GAAG,eAAe,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC5F,IAAI,wBAAwB,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,EAAE;wBAClF,wBAAwB,GAAG,cAAc,CAAC;qBAC3C;oBAED,qBAAqB;oBAErB,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;oBACrC,gEAAgE;oBAChE,IAAM,YAAY,GAAG,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;oBAE7D,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,YAAY,EAAE;wBAChD,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;wBACvC,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;wBACtD,KAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;qBAC3D;oBAED,IAAI,KAAK,CAAC,IAAI,KAAK,wBAAwB,IAAI,YAAY,EAAE;wBAC3D,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;wBACxC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;wBACvD,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;qBAC5D;oBAED,MAAM;iBACP;gBACD,KAAK,UAAU,CAAC,CAAC;oBACf,IAAM,YAAY,GAAI,KAAK,CAAC,IAAe,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAChF,IAAM,YAAY,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;oBACzG,2FAA2F;oBAC3F,IAAI,yBAAyB,KAAK,SAAS,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;wBAChG,yBAAyB,GAAG,YAAY,CAAC;qBAC1C;oBACD,MAAM;iBACP;gBACD,QAAQ;gBACR,4BAA4B;aAC7B;QACH,CAAC,CAAC,CAAC;QAEL,IAAI,yBAAyB,KAAK,SAAS,IAAI,wBAAwB,KAAK,SAAS,EAAE;YACrF,WAAW,CAAC,WAAW,EAAE;gBACvB,WAAW,EAAE,YAAY;gBACzB,YAAY,EAAE,wBAAwB;gBACtC,EAAE,EAAE,QAAQ;gBACZ,cAAc,EAAE,yBAAyB;aAC1C,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3E,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAElC,4DAA4D;QAC5D,IAAI,WAAW,CAAC,EAAE,KAAK,UAAU,EAAE;YACjC,qFAAqF;YAErF,IAAM,YAAU,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAEzD,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACvC,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,YAAU,IAAI,WAAW,CAAC,cAAc,EAAE;oBACzE,OAAO;iBACR;gBAED,8EAA8E;gBAC9E,4GAA4G;gBAC5G,+DAA+D;gBAE/D,IAAM,QAAQ,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;gBAChD,IAAM,oBAAoB,GAAG,YAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC5D,4CAA4C;gBAC5C,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAAoB,GAAG,WAAW,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC;gBAE7F,IAAM,KAAK,GAAG,eAAe,GAAG,QAAQ,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,+BAA6B,IAAI,cAAS,QAAQ,YAAO,eAAe,UAAK,KAAK,MAAG,CAAC,CAAC;gBAElG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,eAAe,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;gBAC/D,sBAAsB;gBAEtB,WAAW,CAAC,WAAW,EAAE;oBACvB,WAAW,EAAE,mBAAmB;oBAChC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;oBAC7F,EAAE,EAAE,YAAY;oBAChB,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK;iBACrD,CAAC,CAAC;aACJ;YAED,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjD;IACH,CAAC;IAED,uEAAuE;IAC/D,0CAAS,GAAjB;QAAA,iBAWC;QAVC,MAAM,CAAC,UAAA,MAAM;YACX,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAEnC,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YAED,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACxC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gDAAe,GAAvB,UAAwB,WAAwB;QAC9C,IAAM,SAAS,GAAG,MAAM,CAAC,SAAqF,CAAC;QAE/G,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QAED,6BAA6B;QAE7B,IAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IAAI,UAAU,EAAE;YACd,IAAI,UAAU,CAAC,aAAa,EAAE;gBAC5B,WAAW,CAAC,MAAM,CAAC,yBAAyB,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;aACzE;YAED,IAAI,UAAU,CAAC,IAAI,EAAE;gBACnB,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;aACvD;YAED,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACtC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,GAAa,EAAE,CAAC;aAC5E;YAED,IAAI,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC3C,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,QAAkB,EAAE,CAAC;aACtF;SACF;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;YAC9C,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;SACpE;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE;YACrD,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC;SAClF;IACH,CAAC;IAED,wEAAwE;IAChE,0CAAS,GAAjB;QAAA,iBAcC;QAbC,MAAM,CAAC,UAAA,MAAM;YACX,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAEnC,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YAED,IAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACnD,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YACrD,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACxC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;YACpD,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,GAAG,SAAS,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iEAAiE;IACzD,0CAAS,GAAjB;QAAA,iBAcC;QAbC,MAAM,CAAC,UAAA,MAAM;YACX,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAEnC,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YAED,IAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACnD,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC;YACrD,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACxC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;YACpD,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,GAAG,SAAS,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kEAAkE;IAC1D,2CAAU,GAAlB;QAAA,iBAeC;QAdC,OAAO,CAAC,UAAA,MAAM;;YACZ,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAEnC,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YAED,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;YAErD,yFAAyF;YACzF,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,GAAI,MAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,uCAAI,KAAK,EAAiC,CAAC,YAAY,CAAC;YAC9G,KAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IACH,6BAAC;AAAD,CAAC,AAtQD,IAsQC;;AAED,oCAAoC;AACpC,SAAS,kBAAkB,CAAC,WAAwB,EAAE,KAA0B,EAAE,UAAkB;IAClG,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IAC9E,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC3E,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,uBAAuB,EAAE,UAAU,CAAC,CAAC;IACxF,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAC5E,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IAC1E,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IACjG,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;IAC7F,8BAA8B,CAAC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAC/E,UAAU,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC;AAED,mCAAmC;AACnC,SAAS,eAAe,CACtB,WAAwB,EACxB,KAA0B,EAC1B,SAAiB,EACjB,QAAgB,EAChB,UAAkB;IAElB,IAAM,qBAAqB,GAAG,UAAU,GAAG,SAAS,CAAC;IACrD,IAAM,mBAAmB,GAAG,qBAAqB,GAAG,QAAQ,CAAC;IAE7D,WAAW,CAAC,WAAW,EAAE;QACvB,WAAW,EAAE,KAAK,CAAC,IAAc;QACjC,YAAY,EAAE,mBAAmB;QACjC,EAAE,EAAE,KAAK,CAAC,SAAmB;QAC7B,cAAc,EAAE,qBAAqB;KACtC,CAAC,CAAC;IAEH,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AASD,oCAAoC;AACpC,MAAM,UAAU,gBAAgB,CAC9B,WAAwB,EACxB,KAAoB,EACpB,YAAoB,EACpB,SAAiB,EACjB,QAAgB,EAChB,UAAkB;IAElB,oEAAoE;IACpE,wBAAwB;IACxB,IAAI,KAAK,CAAC,aAAa,KAAK,gBAAgB,IAAI,KAAK,CAAC,aAAa,KAAK,OAAO,EAAE;QAC/E,OAAO,SAAS,CAAC;KAClB;IAED,IAAM,IAAI,GAAwB,EAAE,CAAC;IACrC,IAAI,cAAc,IAAI,KAAK,EAAE;QAC3B,IAAI,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC;KAC5C;IACD,IAAI,iBAAiB,IAAI,KAAK,EAAE;QAC9B,IAAI,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC,eAAe,CAAC;KACnD;IACD,IAAI,iBAAiB,IAAI,KAAK,EAAE;QAC9B,IAAI,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC,eAAe,CAAC;KACnD;IAED,IAAM,cAAc,GAAG,UAAU,GAAG,SAAS,CAAC;IAC9C,IAAM,YAAY,GAAG,cAAc,GAAG,QAAQ,CAAC;IAE/C,WAAW,CAAC,WAAW,EAAE;QACvB,WAAW,EAAE,YAAY;QACzB,YAAY,cAAA;QACZ,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,cAAY,KAAK,CAAC,aAAe,CAAC,CAAC,CAAC,UAAU;QACxE,cAAc,gBAAA;QACd,IAAI,MAAA;KACL,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,kDAAkD;AAClD,SAAS,8BAA8B,CACrC,WAAwB,EACxB,KAA0B,EAC1B,KAAa,EACb,UAAkB,EAClB,QAAiB;IAEjB,IAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAE,KAAK,CAAC,QAAQ,CAAwB,CAAC,CAAC,CAAE,KAAK,CAAI,KAAK,QAAK,CAAwB,CAAC;IAC9G,IAAM,KAAK,GAAG,KAAK,CAAI,KAAK,UAAO,CAAuB,CAAC;IAC3D,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;QAClB,OAAO;KACR;IACD,WAAW,CAAC,WAAW,EAAE;QACvB,EAAE,EAAE,SAAS;QACb,WAAW,EAAE,KAAK;QAClB,cAAc,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3C,YAAY,EAAE,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;KACxC,CAAC,CAAC;AACL,CAAC;AAED,gDAAgD;AAChD,SAAS,UAAU,CAAC,WAAwB,EAAE,KAA0B,EAAE,UAAkB;IAC1F,WAAW,CAAC,WAAW,EAAE;QACvB,EAAE,EAAE,SAAS;QACb,WAAW,EAAE,SAAS;QACtB,cAAc,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,YAAsB,CAAC;QAClE,YAAY,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,WAAqB,CAAC;KAChE,CAAC,CAAC;IAEH,WAAW,CAAC,WAAW,EAAE;QACvB,EAAE,EAAE,SAAS;QACb,WAAW,EAAE,UAAU;QACvB,cAAc,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,aAAuB,CAAC;QACnE,YAAY,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,WAAqB,CAAC;KAChE,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,WAAwB,EAAE,EAAuC;IAArC,IAAA,kCAAc,EAAE,oCAAM;IAC5E,IAAI,cAAc,IAAI,WAAW,CAAC,cAAc,GAAG,cAAc,EAAE;QACjE,WAAW,CAAC,cAAc,GAAG,cAAc,CAAC;KAC7C;IAED,OAAO,WAAW,CAAC,UAAU,YAC3B,cAAc,gBAAA,IACX,GAAG,EACN,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,KAAU;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Measurements, SpanContext } from '@sentry/types';\nimport { browserPerformanceTimeOrigin, getGlobalObject, logger } from '@sentry/utils';\n\nimport { Span } from '../span';\nimport { Transaction } from '../transaction';\nimport { msToSec } from '../utils';\nimport { getCLS } from './web-vitals/getCLS';\nimport { getFID } from './web-vitals/getFID';\nimport { getLCP } from './web-vitals/getLCP';\nimport { getTTFB } from './web-vitals/getTTFB';\nimport { getFirstHidden } from './web-vitals/lib/getFirstHidden';\nimport { NavigatorDeviceMemory, NavigatorNetworkInformation } from './web-vitals/types';\n\nconst global = getGlobalObject<Window>();\n\n/** Class tracking metrics  */\nexport class MetricsInstrumentation {\n  private _measurements: Measurements = {};\n\n  private _performanceCursor: number = 0;\n\n  public constructor() {\n    if (global && global.performance) {\n      if (global.performance.mark) {\n        global.performance.mark('sentry-tracing-init');\n      }\n\n      this._trackCLS();\n      this._trackLCP();\n      this._trackFID();\n      this._trackTTFB();\n    }\n  }\n\n  /** Add performance related spans to a transaction */\n  public addPerformanceEntries(transaction: Transaction): void {\n    if (!global || !global.performance || !global.performance.getEntries || !browserPerformanceTimeOrigin) {\n      // Gatekeeper if performance API not available\n      return;\n    }\n\n    logger.log('[Tracing] Adding & adjusting spans using Performance API');\n\n    const timeOrigin = msToSec(browserPerformanceTimeOrigin);\n    let entryScriptSrc: string | undefined;\n\n    if (global.document) {\n      // eslint-disable-next-line @typescript-eslint/prefer-for-of\n      for (let i = 0; i < document.scripts.length; i++) {\n        // We go through all scripts on the page and look for 'data-entry'\n        // We remember the name and measure the time between this script finished loading and\n        // our mark 'sentry-tracing-init'\n        if (document.scripts[i].dataset.entry === 'true') {\n          entryScriptSrc = document.scripts[i].src;\n          break;\n        }\n      }\n    }\n\n    let entryScriptStartTimestamp: number | undefined;\n    let tracingInitMarkStartTime: number | undefined;\n\n    global.performance\n      .getEntries()\n      .slice(this._performanceCursor)\n      .forEach((entry: Record<string, any>) => {\n        const startTime = msToSec(entry.startTime as number);\n        const duration = msToSec(entry.duration as number);\n\n        if (transaction.op === 'navigation' && timeOrigin + startTime < transaction.startTimestamp) {\n          return;\n        }\n\n        switch (entry.entryType) {\n          case 'navigation':\n            addNavigationSpans(transaction, entry, timeOrigin);\n            break;\n          case 'mark':\n          case 'paint':\n          case 'measure': {\n            const startTimestamp = addMeasureSpans(transaction, entry, startTime, duration, timeOrigin);\n            if (tracingInitMarkStartTime === undefined && entry.name === 'sentry-tracing-init') {\n              tracingInitMarkStartTime = startTimestamp;\n            }\n\n            // capture web vitals\n\n            const firstHidden = getFirstHidden();\n            // Only report if the page wasn't hidden prior to the web vital.\n            const shouldRecord = entry.startTime < firstHidden.timeStamp;\n\n            if (entry.name === 'first-paint' && shouldRecord) {\n              logger.log('[Measurements] Adding FP');\n              this._measurements['fp'] = { value: entry.startTime };\n              this._measurements['mark.fp'] = { value: startTimestamp };\n            }\n\n            if (entry.name === 'first-contentful-paint' && shouldRecord) {\n              logger.log('[Measurements] Adding FCP');\n              this._measurements['fcp'] = { value: entry.startTime };\n              this._measurements['mark.fcp'] = { value: startTimestamp };\n            }\n\n            break;\n          }\n          case 'resource': {\n            const resourceName = (entry.name as string).replace(window.location.origin, '');\n            const endTimestamp = addResourceSpans(transaction, entry, resourceName, startTime, duration, timeOrigin);\n            // We remember the entry script end time to calculate the difference to the first init mark\n            if (entryScriptStartTimestamp === undefined && (entryScriptSrc || '').indexOf(resourceName) > -1) {\n              entryScriptStartTimestamp = endTimestamp;\n            }\n            break;\n          }\n          default:\n          // Ignore other entry types.\n        }\n      });\n\n    if (entryScriptStartTimestamp !== undefined && tracingInitMarkStartTime !== undefined) {\n      _startChild(transaction, {\n        description: 'evaluation',\n        endTimestamp: tracingInitMarkStartTime,\n        op: 'script',\n        startTimestamp: entryScriptStartTimestamp,\n      });\n    }\n\n    this._performanceCursor = Math.max(performance.getEntries().length - 1, 0);\n\n    this._trackNavigator(transaction);\n\n    // Measurements are only available for pageload transactions\n    if (transaction.op === 'pageload') {\n      // normalize applicable web vital values to be relative to transaction.startTimestamp\n\n      const timeOrigin = msToSec(browserPerformanceTimeOrigin);\n\n      ['fcp', 'fp', 'lcp', 'ttfb'].forEach(name => {\n        if (!this._measurements[name] || timeOrigin >= transaction.startTimestamp) {\n          return;\n        }\n\n        // The web vitals, fcp, fp, lcp, and ttfb, all measure relative to timeOrigin.\n        // Unfortunately, timeOrigin is not captured within the transaction span data, so these web vitals will need\n        // to be adjusted to be relative to transaction.startTimestamp.\n\n        const oldValue = this._measurements[name].value;\n        const measurementTimestamp = timeOrigin + msToSec(oldValue);\n        // normalizedValue should be in milliseconds\n        const normalizedValue = Math.abs((measurementTimestamp - transaction.startTimestamp) * 1000);\n\n        const delta = normalizedValue - oldValue;\n        logger.log(`[Measurements] Normalized ${name} from ${oldValue} to ${normalizedValue} (${delta})`);\n\n        this._measurements[name].value = normalizedValue;\n      });\n\n      if (this._measurements['mark.fid'] && this._measurements['fid']) {\n        // create span for FID\n\n        _startChild(transaction, {\n          description: 'first input delay',\n          endTimestamp: this._measurements['mark.fid'].value + msToSec(this._measurements['fid'].value),\n          op: 'web.vitals',\n          startTimestamp: this._measurements['mark.fid'].value,\n        });\n      }\n\n      transaction.setMeasurements(this._measurements);\n    }\n  }\n\n  /** Starts tracking the Cumulative Layout Shift on the current page. */\n  private _trackCLS(): void {\n    getCLS(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      logger.log('[Measurements] Adding CLS');\n      this._measurements['cls'] = { value: metric.value };\n    });\n  }\n\n  /**\n   * Capture the information of the user agent.\n   */\n  private _trackNavigator(transaction: Transaction): void {\n    const navigator = global.navigator as null | (Navigator & NavigatorNetworkInformation & NavigatorDeviceMemory);\n\n    if (!navigator) {\n      return;\n    }\n\n    // track network connectivity\n\n    const connection = navigator.connection;\n    if (connection) {\n      if (connection.effectiveType) {\n        transaction.setTag('effectiveConnectionType', connection.effectiveType);\n      }\n\n      if (connection.type) {\n        transaction.setTag('connectionType', connection.type);\n      }\n\n      if (isMeasurementValue(connection.rtt)) {\n        this._measurements['connection.rtt'] = { value: connection.rtt as number };\n      }\n\n      if (isMeasurementValue(connection.downlink)) {\n        this._measurements['connection.downlink'] = { value: connection.downlink as number };\n      }\n    }\n\n    if (isMeasurementValue(navigator.deviceMemory)) {\n      transaction.setTag('deviceMemory', String(navigator.deviceMemory));\n    }\n\n    if (isMeasurementValue(navigator.hardwareConcurrency)) {\n      transaction.setTag('hardwareConcurrency', String(navigator.hardwareConcurrency));\n    }\n  }\n\n  /** Starts tracking the Largest Contentful Paint on the current page. */\n  private _trackLCP(): void {\n    getLCP(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      const timeOrigin = msToSec(performance.timeOrigin);\n      const startTime = msToSec(entry.startTime as number);\n      logger.log('[Measurements] Adding LCP');\n      this._measurements['lcp'] = { value: metric.value };\n      this._measurements['mark.lcp'] = { value: timeOrigin + startTime };\n    });\n  }\n\n  /** Starts tracking the First Input Delay on the current page. */\n  private _trackFID(): void {\n    getFID(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      const timeOrigin = msToSec(performance.timeOrigin);\n      const startTime = msToSec(entry.startTime as number);\n      logger.log('[Measurements] Adding FID');\n      this._measurements['fid'] = { value: metric.value };\n      this._measurements['mark.fid'] = { value: timeOrigin + startTime };\n    });\n  }\n\n  /** Starts tracking the Time to First Byte on the current page. */\n  private _trackTTFB(): void {\n    getTTFB(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      logger.log('[Measurements] Adding TTFB');\n      this._measurements['ttfb'] = { value: metric.value };\n\n      // Capture the time spent making the request and receiving the first byte of the response\n      const requestTime = metric.value - ((metric.entries[0] ?? entry) as PerformanceNavigationTiming).requestStart;\n      this._measurements['ttfb.requestTime'] = { value: requestTime };\n    });\n  }\n}\n\n/** Instrument navigation entries */\nfunction addNavigationSpans(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  addPerformanceNavigationTiming(transaction, entry, 'unloadEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'redirect', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'domContentLoadedEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'loadEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'connect', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'secureConnection', timeOrigin, 'connectEnd');\n  addPerformanceNavigationTiming(transaction, entry, 'fetch', timeOrigin, 'domainLookupStart');\n  addPerformanceNavigationTiming(transaction, entry, 'domainLookup', timeOrigin);\n  addRequest(transaction, entry, timeOrigin);\n}\n\n/** Create measure related spans */\nfunction addMeasureSpans(\n  transaction: Transaction,\n  entry: Record<string, any>,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): number {\n  const measureStartTimestamp = timeOrigin + startTime;\n  const measureEndTimestamp = measureStartTimestamp + duration;\n\n  _startChild(transaction, {\n    description: entry.name as string,\n    endTimestamp: measureEndTimestamp,\n    op: entry.entryType as string,\n    startTimestamp: measureStartTimestamp,\n  });\n\n  return measureStartTimestamp;\n}\n\nexport interface ResourceEntry extends Record<string, unknown> {\n  initiatorType?: string;\n  transferSize?: number;\n  encodedBodySize?: number;\n  decodedBodySize?: number;\n}\n\n/** Create resource-related spans */\nexport function addResourceSpans(\n  transaction: Transaction,\n  entry: ResourceEntry,\n  resourceName: string,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): number | undefined {\n  // we already instrument based on fetch and xhr, so we don't need to\n  // duplicate spans here.\n  if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {\n    return undefined;\n  }\n\n  const data: Record<string, any> = {};\n  if ('transferSize' in entry) {\n    data['Transfer Size'] = entry.transferSize;\n  }\n  if ('encodedBodySize' in entry) {\n    data['Encoded Body Size'] = entry.encodedBodySize;\n  }\n  if ('decodedBodySize' in entry) {\n    data['Decoded Body Size'] = entry.decodedBodySize;\n  }\n\n  const startTimestamp = timeOrigin + startTime;\n  const endTimestamp = startTimestamp + duration;\n\n  _startChild(transaction, {\n    description: resourceName,\n    endTimestamp,\n    op: entry.initiatorType ? `resource.${entry.initiatorType}` : 'resource',\n    startTimestamp,\n    data,\n  });\n\n  return endTimestamp;\n}\n\n/** Create performance navigation related spans */\nfunction addPerformanceNavigationTiming(\n  transaction: Transaction,\n  entry: Record<string, any>,\n  event: string,\n  timeOrigin: number,\n  eventEnd?: string,\n): void {\n  const end = eventEnd ? (entry[eventEnd] as number | undefined) : (entry[`${event}End`] as number | undefined);\n  const start = entry[`${event}Start`] as number | undefined;\n  if (!start || !end) {\n    return;\n  }\n  _startChild(transaction, {\n    op: 'browser',\n    description: event,\n    startTimestamp: timeOrigin + msToSec(start),\n    endTimestamp: timeOrigin + msToSec(end),\n  });\n}\n\n/** Create request and response related spans */\nfunction addRequest(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  _startChild(transaction, {\n    op: 'browser',\n    description: 'request',\n    startTimestamp: timeOrigin + msToSec(entry.requestStart as number),\n    endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n  });\n\n  _startChild(transaction, {\n    op: 'browser',\n    description: 'response',\n    startTimestamp: timeOrigin + msToSec(entry.responseStart as number),\n    endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n  });\n}\n\n/**\n * Helper function to start child on transactions. This function will make sure that the transaction will\n * use the start timestamp of the created child span if it is earlier than the transactions actual\n * start timestamp.\n */\nexport function _startChild(transaction: Transaction, { startTimestamp, ...ctx }: SpanContext): Span {\n  if (startTimestamp && transaction.startTimestamp > startTimestamp) {\n    transaction.startTimestamp = startTimestamp;\n  }\n\n  return transaction.startChild({\n    startTimestamp,\n    ...ctx,\n  });\n}\n\n/**\n * Checks if a given value is a valid measurement value.\n */\nfunction isMeasurementValue(value: any): boolean {\n  return typeof value === 'number' && isFinite(value);\n}\n"]}