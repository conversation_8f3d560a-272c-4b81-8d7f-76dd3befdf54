{"version": 3, "file": "getLCP.js", "sourceRoot": "", "sources": ["../../../src/browser/web-vitals/getLCP.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAA2B,MAAM,eAAe,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,MAAM,CAAC,IAAM,MAAM,GAAG,UAAC,QAAuB,EAAE,gBAAwB;IAAxB,iCAAA,EAAA,wBAAwB;IACtE,IAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IAErC,IAAI,MAAuC,CAAC;IAE5C,IAAM,YAAY,GAAG,UAAC,KAAuB;QAC3C,8EAA8E;QAC9E,2CAA2C;QAC3C,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;QAE9B,2DAA2D;QAC3D,mEAAmE;QACnE,IAAI,KAAK,GAAG,WAAW,CAAC,SAAS,EAAE;YACjC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5B;aAAM;YACL,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QAED,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;IAEF,IAAM,EAAE,GAAG,OAAO,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC;IAE7D,IAAI,EAAE,EAAE;QACN,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAE9D,IAAM,OAAO,GAAG;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACnB,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,YAAuC,CAAC,CAAC;gBAC9D,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;gBACtB,MAAM,EAAE,CAAC;aACV;QACH,CAAC,CAAC;QAEF,KAAK,SAAS,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACzB;AACH,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getFirstHidden } from './lib/getFirstHidden';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { whenInput } from './lib/whenInput';\nimport { ReportHandler } from './types';\n\nexport const getLCP = (onReport: ReportHandler, reportAllChanges = false): void => {\n  const metric = initMetric('LCP');\n  const firstHidden = getFirstHidden();\n\n  let report: ReturnType<typeof bindReporter>;\n\n  const entryHandler = (entry: PerformanceEntry): void => {\n    // The startTime attribute returns the value of the renderTime if it is not 0,\n    // and the value of the loadTime otherwise.\n    const value = entry.startTime;\n\n    // If the page was hidden prior to paint time of the entry,\n    // ignore it and mark the metric as final, otherwise add the entry.\n    if (value < firstHidden.timeStamp) {\n      metric.value = value;\n      metric.entries.push(entry);\n    } else {\n      metric.isFinal = true;\n    }\n\n    report();\n  };\n\n  const po = observe('largest-contentful-paint', entryHandler);\n\n  if (po) {\n    report = bindReporter(onReport, metric, po, reportAllChanges);\n\n    const onFinal = (): void => {\n      if (!metric.isFinal) {\n        po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n        metric.isFinal = true;\n        report();\n      }\n    };\n\n    void whenInput().then(onFinal);\n    onHidden(onFinal, true);\n  }\n};\n"]}