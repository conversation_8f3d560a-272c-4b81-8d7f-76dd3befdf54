/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { getGlobalObject } from '@sentry/utils';
import { initMetric } from './lib/initMetric';
var global = getGlobalObject();
var afterLoad = function (callback) {
    if (document.readyState === 'complete') {
        // Queue a task so the callback runs after `loadEventEnd`.
        setTimeout(callback, 0);
    }
    else {
        // Use `pageshow` so the callback runs after `loadEventEnd`.
        addEventListener('pageshow', callback);
    }
};
var getNavigationEntryFromPerformanceTiming = function () {
    // Really annoying that TypeScript errors when using `PerformanceTiming`.
    // eslint-disable-next-line deprecation/deprecation
    var timing = global.performance.timing;
    var navigationEntry = {
        entryType: 'navigation',
        startTime: 0,
    };
    for (var key in timing) {
        if (key !== 'navigationStart' && key !== 'toJSON') {
            navigationEntry[key] = Math.max(timing[key] - timing.navigationStart, 0);
        }
    }
    return navigationEntry;
};
export var getTTFB = function (onReport) {
    var metric = initMetric('TTFB');
    afterLoad(function () {
        try {
            // Use the NavigationTiming L2 entry if available.
            var navigationEntry = global.performance.getEntriesByType('navigation')[0] || getNavigationEntryFromPerformanceTiming();
            metric.value = metric.delta = navigationEntry.responseStart;
            metric.entries = [navigationEntry];
            onReport(metric);
        }
        catch (error) {
            // Do nothing.
        }
    });
};
//# sourceMappingURL=getTTFB.js.map