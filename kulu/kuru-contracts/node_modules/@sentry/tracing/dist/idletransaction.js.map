{"version": 3, "file": "idletransaction.js", "sourceRoot": "", "sources": ["../src/idletransaction.ts"], "names": [], "mappings": ";;AAEA,uCAAwD;AAExD,+BAA4C;AAC5C,2CAA0C;AAC1C,6CAA4C;AAE/B,QAAA,oBAAoB,GAAG,IAAI,CAAC;AAEzC;;GAEG;AACH;IAAiD,uDAAY;IAC3D,qCACmB,aAAmC,EACnC,YAAkC,EAC5C,iBAA8B,EACrC,MAAe;QADR,kCAAA,EAAA,sBAA8B;QAHvC,YAME,kBAAM,MAAM,CAAC,SACd;QANkB,mBAAa,GAAb,aAAa,CAAsB;QACnC,kBAAY,GAAZ,YAAY,CAAsB;QAC5C,uBAAiB,GAAjB,iBAAiB,CAAa;;IAIvC,CAAC;IAED;;OAEG;IACI,yCAAG,GAAV,UAAW,IAAU;QAArB,iBAiBC;QAhBC,4DAA4D;QAC5D,sDAAsD;QACtD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,iBAAiB,EAAE;YAC1C,2EAA2E;YAC3E,IAAI,CAAC,MAAM,GAAG,UAAC,YAAqB;gBAClC,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,uBAAe,EAAE,CAAC;gBACxF,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,CAAC,CAAC;YAEF,iFAAiF;YACjF,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;gBACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACjC;SACF;QAED,iBAAM,GAAG,YAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IACH,kCAAC;AAAD,CAAC,AA/BD,CAAiD,mBAAY,GA+B5D;AA/BY,kEAA2B;AAmCxC;;;;GAIG;AACH;IAAqC,2CAAW;IAuB9C,yBACE,kBAAsC,EACrB,QAAc;IAC/B,oFAAoF;IACnE,YAA2C;IAC5D,kFAAkF;IACjE,QAAyB;QAFzB,6BAAA,EAAA,eAAuB,4BAAoB;QAE3C,yBAAA,EAAA,gBAAyB;QAN5C,YAQE,kBAAM,kBAAkB,EAAE,QAAQ,CAAC,SAWpC;QAjBkB,cAAQ,GAAR,QAAQ,CAAM;QAEd,kBAAY,GAAZ,YAAY,CAA+B;QAE3C,cAAQ,GAAR,QAAQ,CAAiB;QA5B5C,0CAA0C;QACnC,gBAAU,GAA4B,EAAE,CAAC;QAEhD,sDAAsD;QAC9C,qBAAe,GAAW,CAAC,CAAC;QAKpC,yFAAyF;QACjF,uBAAiB,GAAW,CAAC,CAAC;QAEtC,2DAA2D;QACnD,eAAS,GAAY,KAAK,CAAC;QAElB,4BAAsB,GAA2B,EAAE,CAAC;QAiBnE,IAAI,QAAQ,IAAI,QAAQ,EAAE;YACxB,2DAA2D;YAC3D,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAEjC,6EAA6E;YAC7E,sCAAsC;YACtC,cAAM,CAAC,GAAG,CAAC,iDAA+C,KAAI,CAAC,MAAQ,CAAC,CAAC;YACzE,QAAQ,CAAC,cAAc,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,OAAO,CAAC,KAAI,CAAC,EAAnB,CAAmB,CAAC,CAAC;SACvD;;IACH,CAAC;IAED,oBAAoB;IACb,gCAAM,GAAb,UAAc,YAAwC;;QAAtD,iBA6CC;QA7Ca,6BAAA,EAAA,eAAuB,uBAAe,EAAE;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAM,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;;gBAExG,KAAuB,IAAA,KAAA,iBAAA,IAAI,CAAC,sBAAsB,CAAA,gBAAA,4BAAE;oBAA/C,IAAM,QAAQ,WAAA;oBACjB,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;iBAC9B;;;;;;;;;YAED,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,UAAC,IAAU;gBAClE,mEAAmE;gBACnE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAI,CAAC,MAAM,EAAE;oBAC/B,OAAO,IAAI,CAAC;iBACb;gBAED,0GAA0G;gBAC1G,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACtB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;oBACjC,IAAI,CAAC,SAAS,CAAC,uBAAU,CAAC,SAAS,CAAC,CAAC;oBACrC,cAAM,CAAC,GAAG,CAAC,yDAAyD,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC3G;gBAED,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC;gBACpD,IAAI,CAAC,QAAQ,EAAE;oBACb,cAAM,CAAC,GAAG,CACR,4EAA4E,EAC5E,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CACnC,CAAC;iBACH;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,wEAAwE;YACxE,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACvC;YAED,cAAM,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;SAClD;aAAM;YACL,cAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;SACnD;QAED,OAAO,iBAAM,MAAM,YAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,sDAA4B,GAAnC,UAAoC,QAA8B;QAChE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,0CAAgB,GAAvB,UAAwB,MAAe;QAAvC,iBA4BC;QA3BC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;gBAC7B,IAAI,CAAC,KAAI,CAAC,SAAS,EAAE;oBACnB,KAAI,CAAC,MAAM,EAAE,CAAC;iBACf;YACH,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtB,IAAM,YAAY,GAAG,UAAC,EAAU;gBAC9B,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,OAAO;iBACR;gBACD,KAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YACzB,CAAC,CAAC;YACF,IAAM,WAAW,GAAG,UAAC,EAAU;gBAC7B,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,OAAO;iBACR;gBACD,KAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACxB,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,GAAG,IAAI,2BAA2B,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEpG,2DAA2D;YAC3D,cAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACjC,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;QACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACK,uCAAa,GAArB,UAAsB,MAAc;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;SAC/B;QACD,cAAM,CAAC,GAAG,CAAC,6BAA2B,MAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC/B,cAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACK,sCAAY,GAApB,UAAqB,MAAc;QAAnC,iBAoBC;QAnBC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC3B,cAAM,CAAC,GAAG,CAAC,2BAAyB,MAAQ,CAAC,CAAC;YAC9C,gEAAgE;YAChE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/B,cAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;SACnF;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;YAClC,mFAAmF;YACnF,2DAA2D;YAC3D,IAAM,KAAG,GAAG,uBAAe,EAAE,GAAG,OAAO,GAAG,IAAI,CAAC;YAE/C,UAAU,CAAC;gBACT,IAAI,CAAC,KAAI,CAAC,SAAS,EAAE;oBACnB,KAAI,CAAC,MAAM,CAAC,KAAG,CAAC,CAAC;iBAClB;YACH,CAAC,EAAE,OAAO,CAAC,CAAC;SACb;IACH,CAAC;IAED;;;OAGG;IACK,+BAAK,GAAb;QACE,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnC,0EAA0E;QAC1E,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAC,IAAY,EAAE,OAAe,IAAK,OAAA,IAAI,GAAG,OAAO,EAAd,CAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1G,IAAI,eAAe,KAAK,IAAI,CAAC,oBAAoB,EAAE;YACjD,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC;QAE5C,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,EAAE;YAC/B,cAAM,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAC;YACpF,IAAI,CAAC,SAAS,CAAC,uBAAU,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;aAAM;YACL,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;IACH,CAAC;IAED;;OAEG;IACK,wCAAc,GAAtB;QAAA,iBAKC;QAJC,cAAM,CAAC,GAAG,CAAC,2CAAyC,IAAI,CAAC,iBAAmB,CAAC,CAAC;QAC9E,IAAI,CAAC,eAAe,GAAI,UAAU,CAAC;YACjC,KAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC,EAAE,IAAI,CAAuB,CAAC;IACjC,CAAC;IACH,sBAAC;AAAD,CAAC,AAzND,CAAqC,yBAAW,GAyN/C;AAzNY,0CAAe;AA2N5B;;GAEG;AACH,SAAS,sBAAsB,CAAC,GAAS;IACvC,IAAI,GAAG,EAAE;QACP,IAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAI,KAAK,EAAE;YACT,IAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAC3C,IAAI,WAAW,EAAE;gBACf,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC1B;SACF;KACF;AACH,CAAC", "sourcesContent": ["import { Hub } from '@sentry/hub';\nimport { TransactionContext } from '@sentry/types';\nimport { logger, timestampWithMs } from '@sentry/utils';\n\nimport { Span, SpanRecorder } from './span';\nimport { SpanStatus } from './spanstatus';\nimport { Transaction } from './transaction';\n\nexport const DEFAULT_IDLE_TIMEOUT = 1000;\n\n/**\n * @inheritDoc\n */\nexport class IdleTransactionSpanRecorder extends SpanRecorder {\n  public constructor(\n    private readonly _pushActivity: (id: string) => void,\n    private readonly _popActivity: (id: string) => void,\n    public transactionSpanId: string = '',\n    maxlen?: number,\n  ) {\n    super(maxlen);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public add(span: Span): void {\n    // We should make sure we do not push and pop activities for\n    // the transaction that this span recorder belongs to.\n    if (span.spanId !== this.transactionSpanId) {\n      // We patch span.finish() to pop an activity after setting an endTimestamp.\n      span.finish = (endTimestamp?: number) => {\n        span.endTimestamp = typeof endTimestamp === 'number' ? endTimestamp : timestampWithMs();\n        this._popActivity(span.spanId);\n      };\n\n      // We should only push new activities if the span does not have an end timestamp.\n      if (span.endTimestamp === undefined) {\n        this._pushActivity(span.spanId);\n      }\n    }\n\n    super.add(span);\n  }\n}\n\nexport type BeforeFinishCallback = (transactionSpan: IdleTransaction, endTimestamp: number) => void;\n\n/**\n * An IdleTransaction is a transaction that automatically finishes. It does this by tracking child spans as activities.\n * You can have multiple IdleTransactions active, but if the `onScope` option is specified, the idle transaction will\n * put itself on the scope on creation.\n */\nexport class IdleTransaction extends Transaction {\n  // Activities store a list of active spans\n  public activities: Record<string, boolean> = {};\n\n  // Stores reference to the timeout that calls _beat().\n  private _heartbeatTimer: number = 0;\n\n  // Track state of activities in previous heartbeat\n  private _prevHeartbeatString: string | undefined;\n\n  // Amount of times heartbeat has counted. Will cause transaction to finish after 3 beats.\n  private _heartbeatCounter: number = 0;\n\n  // We should not use heartbeat if we finished a transaction\n  private _finished: boolean = false;\n\n  private readonly _beforeFinishCallbacks: BeforeFinishCallback[] = [];\n\n  // If a transaction is created and no activities are added, we want to make sure that\n  // it times out properly. This is cleared and not used when activities are added.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _initTimeout: any;\n\n  public constructor(\n    transactionContext: TransactionContext,\n    private readonly _idleHub?: Hub,\n    // The time to wait in ms until the idle transaction will be finished. Default: 1000\n    private readonly _idleTimeout: number = DEFAULT_IDLE_TIMEOUT,\n    // If an idle transaction should be put itself on and off the scope automatically.\n    private readonly _onScope: boolean = false,\n  ) {\n    super(transactionContext, _idleHub);\n\n    if (_idleHub && _onScope) {\n      // There should only be one active transaction on the scope\n      clearActiveTransaction(_idleHub);\n\n      // We set the transaction here on the scope so error events pick up the trace\n      // context and attach it to the error.\n      logger.log(`Setting idle transaction on scope. Span ID: ${this.spanId}`);\n      _idleHub.configureScope(scope => scope.setSpan(this));\n    }\n  }\n\n  /** {@inheritDoc} */\n  public finish(endTimestamp: number = timestampWithMs()): string | undefined {\n    this._finished = true;\n    this.activities = {};\n\n    if (this.spanRecorder) {\n      logger.log('[Tracing] finishing IdleTransaction', new Date(endTimestamp * 1000).toISOString(), this.op);\n\n      for (const callback of this._beforeFinishCallbacks) {\n        callback(this, endTimestamp);\n      }\n\n      this.spanRecorder.spans = this.spanRecorder.spans.filter((span: Span) => {\n        // If we are dealing with the transaction itself, we just return it\n        if (span.spanId === this.spanId) {\n          return true;\n        }\n\n        // We cancel all pending spans with status \"cancelled\" to indicate the idle transaction was finished early\n        if (!span.endTimestamp) {\n          span.endTimestamp = endTimestamp;\n          span.setStatus(SpanStatus.Cancelled);\n          logger.log('[Tracing] cancelling span since transaction ended early', JSON.stringify(span, undefined, 2));\n        }\n\n        const keepSpan = span.startTimestamp < endTimestamp;\n        if (!keepSpan) {\n          logger.log(\n            '[Tracing] discarding Span since it happened after Transaction was finished',\n            JSON.stringify(span, undefined, 2),\n          );\n        }\n        return keepSpan;\n      });\n\n      // this._onScope is true if the transaction was previously on the scope.\n      if (this._onScope) {\n        clearActiveTransaction(this._idleHub);\n      }\n\n      logger.log('[Tracing] flushing IdleTransaction');\n    } else {\n      logger.log('[Tracing] No active IdleTransaction');\n    }\n\n    return super.finish(endTimestamp);\n  }\n\n  /**\n   * Register a callback function that gets excecuted before the transaction finishes.\n   * Useful for cleanup or if you want to add any additional spans based on current context.\n   *\n   * This is exposed because users have no other way of running something before an idle transaction\n   * finishes.\n   */\n  public registerBeforeFinishCallback(callback: BeforeFinishCallback): void {\n    this._beforeFinishCallbacks.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public initSpanRecorder(maxlen?: number): void {\n    if (!this.spanRecorder) {\n      this._initTimeout = setTimeout(() => {\n        if (!this._finished) {\n          this.finish();\n        }\n      }, this._idleTimeout);\n\n      const pushActivity = (id: string): void => {\n        if (this._finished) {\n          return;\n        }\n        this._pushActivity(id);\n      };\n      const popActivity = (id: string): void => {\n        if (this._finished) {\n          return;\n        }\n        this._popActivity(id);\n      };\n\n      this.spanRecorder = new IdleTransactionSpanRecorder(pushActivity, popActivity, this.spanId, maxlen);\n\n      // Start heartbeat so that transactions do not run forever.\n      logger.log('Starting heartbeat');\n      this._pingHeartbeat();\n    }\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * Start tracking a specific activity.\n   * @param spanId The span id that represents the activity\n   */\n  private _pushActivity(spanId: string): void {\n    if (this._initTimeout) {\n      clearTimeout(this._initTimeout);\n      this._initTimeout = undefined;\n    }\n    logger.log(`[Tracing] pushActivity: ${spanId}`);\n    this.activities[spanId] = true;\n    logger.log('[Tracing] new activities count', Object.keys(this.activities).length);\n  }\n\n  /**\n   * Remove an activity from usage\n   * @param spanId The span id that represents the activity\n   */\n  private _popActivity(spanId: string): void {\n    if (this.activities[spanId]) {\n      logger.log(`[Tracing] popActivity ${spanId}`);\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this.activities[spanId];\n      logger.log('[Tracing] new activities count', Object.keys(this.activities).length);\n    }\n\n    if (Object.keys(this.activities).length === 0) {\n      const timeout = this._idleTimeout;\n      // We need to add the timeout here to have the real endtimestamp of the transaction\n      // Remember timestampWithMs is in seconds, timeout is in ms\n      const end = timestampWithMs() + timeout / 1000;\n\n      setTimeout(() => {\n        if (!this._finished) {\n          this.finish(end);\n        }\n      }, timeout);\n    }\n  }\n\n  /**\n   * Checks when entries of this.activities are not changing for 3 beats.\n   * If this occurs we finish the transaction.\n   */\n  private _beat(): void {\n    clearTimeout(this._heartbeatTimer);\n    // We should not be running heartbeat if the idle transaction is finished.\n    if (this._finished) {\n      return;\n    }\n\n    const keys = Object.keys(this.activities);\n    const heartbeatString = keys.length ? keys.reduce((prev: string, current: string) => prev + current) : '';\n\n    if (heartbeatString === this._prevHeartbeatString) {\n      this._heartbeatCounter += 1;\n    } else {\n      this._heartbeatCounter = 1;\n    }\n\n    this._prevHeartbeatString = heartbeatString;\n\n    if (this._heartbeatCounter >= 3) {\n      logger.log(`[Tracing] Transaction finished because of no change for 3 heart beats`);\n      this.setStatus(SpanStatus.DeadlineExceeded);\n      this.setTag('heartbeat', 'failed');\n      this.finish();\n    } else {\n      this._pingHeartbeat();\n    }\n  }\n\n  /**\n   * Pings the heartbeat\n   */\n  private _pingHeartbeat(): void {\n    logger.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`);\n    this._heartbeatTimer = (setTimeout(() => {\n      this._beat();\n    }, 5000) as unknown) as number;\n  }\n}\n\n/**\n * Reset active transaction on scope\n */\nfunction clearActiveTransaction(hub?: Hub): void {\n  if (hub) {\n    const scope = hub.getScope();\n    if (scope) {\n      const transaction = scope.getTransaction();\n      if (transaction) {\n        scope.setSpan(undefined);\n      }\n    }\n  }\n}\n"]}