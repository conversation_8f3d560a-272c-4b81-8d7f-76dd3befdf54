{"version": 3, "file": "index.bundle.js", "sourceRoot": "", "sources": ["../src/index.bundle.ts"], "names": [], "mappings": ";;AAAA,uCAauB;AANrB,2BAAA,QAAQ,CAAA;AAGR,yBAAA,MAAM,CAAA;AAKR,2CAoByB;AAnBvB,4CAAA,uBAAuB,CAAA;AACvB,kCAAA,aAAa,CAAA;AACb,qCAAA,gBAAgB,CAAA;AAChB,iCAAA,YAAY,CAAA;AACZ,mCAAA,cAAc,CAAA;AACd,mCAAA,cAAc,CAAA;AACd,sCAAA,iBAAiB,CAAA;AACjB,kCAAA,aAAa,CAAA;AACb,wBAAA,GAAG,CAAA;AACH,0BAAA,KAAK,CAAA;AACL,+BAAA,UAAU,CAAA;AACV,6BAAA,QAAQ,CAAA;AACR,8BAAA,SAAS,CAAA;AACT,2BAAA,MAAM,CAAA;AACN,4BAAA,OAAO,CAAA;AACP,4BAAA,OAAO,CAAA;AACP,qCAAA,gBAAgB,CAAA;AAChB,+BAAA,UAAU,CAAA;AACV,8BAAA,SAAS,CAAA;AAIX,2CAAqE;AAA5D,kCAAA,aAAa,CAAA;AACtB,2CAUyB;AATvB,wCAAA,mBAAmB,CAAA;AACnB,8BAAA,SAAS,CAAA;AACT,yBAAA,IAAI,CAAA;AACJ,gCAAA,WAAW,CAAA;AACX,2BAAA,MAAM,CAAA;AACN,qCAAA,gBAAgB,CAAA;AAChB,0BAAA,KAAK,CAAA;AACL,0BAAA,KAAK,CAAA;AACL,yBAAA,IAAI,CAAA;AAEN,2CAAwD;AAA/C,6BAAA,QAAQ,CAAA;AAAE,gCAAA,WAAW,CAAA;AAE9B,2CAAsE;AACtE,uCAAgD;AAEhD,qCAA2C;AAC3C,iDAAsD;AAuB7C,8BAvBA,mCAAmB,CAuBA;AArB5B,+BAA8B;AAArB,sBAAA,IAAI,CAAA;AAEb,IAAI,kBAAkB,GAAG,EAAE,CAAC;AAE5B,gGAAgG;AAChG,IAAM,OAAO,GAAG,uBAAe,EAAU,CAAC;AAC1C,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE;IACjD,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;CAClD;AAED,IAAM,YAAY,0DACb,kBAAkB,GAClB,sBAAmB,KACtB,cAAc,0BAAA,GACf,CAAC;AAEuB,oCAAY;AAErC,mEAAmE;AACnE,mCAAmB,EAAE,CAAC", "sourcesContent": ["export {\n  Breadcrumb,\n  Request,\n  SdkInfo,\n  Event,\n  Exception,\n  Response,\n  Severity,\n  StackFrame,\n  Stacktrace,\n  Status,\n  Thread,\n  User,\n} from '@sentry/types';\n\nexport {\n  addGlobalEventProcessor,\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  getHubFromCarrier,\n  getCurrentHub,\n  Hub,\n  Scope,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  startTransaction,\n  Transports,\n  withScope,\n} from '@sentry/browser';\n\nexport { BrowserOptions } from '@sentry/browser';\nexport { BrowserClient, ReportDialogOptions } from '@sentry/browser';\nexport {\n  defaultIntegrations,\n  forceLoad,\n  init,\n  lastEventId,\n  onLoad,\n  showReportDialog,\n  flush,\n  close,\n  wrap,\n} from '@sentry/browser';\nexport { SDK_NAME, SDK_VERSION } from '@sentry/browser';\n\nimport { Integrations as BrowserIntegrations } from '@sentry/browser';\nimport { getGlobalObject } from '@sentry/utils';\n\nimport { BrowserTracing } from './browser';\nimport { addExtensionMethods } from './hubextensions';\n\nexport { Span } from './span';\n\nlet windowIntegrations = {};\n\n// This block is needed to add compatibility with the integrations packages when used with a CDN\nconst _window = getGlobalObject<Window>();\nif (_window.Sentry && _window.Sentry.Integrations) {\n  windowIntegrations = _window.Sentry.Integrations;\n}\n\nconst INTEGRATIONS = {\n  ...windowIntegrations,\n  ...BrowserIntegrations,\n  BrowserTracing,\n};\n\nexport { INTEGRATIONS as Integrations };\n\n// We are patching the global object with our hub extension methods\naddExtensionMethods();\n\nexport { addExtensionMethods };\n"]}