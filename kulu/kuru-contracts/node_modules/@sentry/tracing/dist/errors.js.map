{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": ";AAAA,uCAAkE;AAElE,2CAA0C;AAC1C,iCAA+C;AAE/C;;GAEG;AACH,SAAgB,4BAA4B;IAC1C,iCAAyB,CAAC;QACxB,QAAQ,EAAE,aAAa;QACvB,IAAI,EAAE,OAAO;KACd,CAAC,CAAC;IACH,iCAAyB,CAAC;QACxB,QAAQ,EAAE,aAAa;QACvB,IAAI,EAAE,oBAAoB;KAC3B,CAAC,CAAC;AACL,CAAC;AATD,oEASC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,IAAM,iBAAiB,GAAG,4BAAoB,EAAE,CAAC;IACjD,IAAI,iBAAiB,EAAE;QACrB,cAAM,CAAC,GAAG,CAAC,4BAA0B,uBAAU,CAAC,aAAa,6BAA0B,CAAC,CAAC;QACzF,iBAAiB,CAAC,SAAS,CAAC,uBAAU,CAAC,aAAa,CAAC,CAAC;KACvD;AACH,CAAC", "sourcesContent": ["import { addInstrumentation<PERSON>andler, logger } from '@sentry/utils';\n\nimport { SpanStatus } from './spanstatus';\nimport { getActiveTransaction } from './utils';\n\n/**\n * Configures global error listeners\n */\nexport function registerErrorInstrumentation(): void {\n  addInstrumentationHandler({\n    callback: errorCallback,\n    type: 'error',\n  });\n  addInstrumentationHandler({\n    callback: errorCallback,\n    type: 'unhandledrejection',\n  });\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active transaction as failed\n */\nfunction errorCallback(): void {\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    logger.log(`[Tracing] Transaction: ${SpanStatus.InternalError} -> Global error occured`);\n    activeTransaction.setStatus(SpanStatus.InternalError);\n  }\n}\n"]}