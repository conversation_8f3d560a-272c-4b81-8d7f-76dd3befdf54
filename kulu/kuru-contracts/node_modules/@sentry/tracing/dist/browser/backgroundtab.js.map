{"version": 3, "file": "backgroundtab.js", "sourceRoot": "", "sources": ["../../src/browser/backgroundtab.ts"], "names": [], "mappings": ";AAAA,uCAAwD;AAGxD,4CAA2C;AAC3C,kCAAgD;AAEhD,IAAM,MAAM,GAAG,uBAAe,EAAU,CAAC;AAEzC;;;GAGG;AACH,SAAgB,8BAA8B;IAC5C,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;QAC7B,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE;YACnD,IAAM,iBAAiB,GAAG,4BAAoB,EAAqB,CAAC;YACpE,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,iBAAiB,EAAE;gBAC/C,cAAM,CAAC,GAAG,CACR,4BAA0B,uBAAU,CAAC,SAAS,mDAA8C,iBAAiB,CAAC,EAAI,CACnH,CAAC;gBACF,sFAAsF;gBACtF,4DAA4D;gBAC5D,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;oBAC7B,iBAAiB,CAAC,SAAS,CAAC,uBAAU,CAAC,SAAS,CAAC,CAAC;iBACnD;gBACD,iBAAiB,CAAC,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;gBAChE,iBAAiB,CAAC,MAAM,EAAE,CAAC;aAC5B;QACH,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,cAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;KACnG;AACH,CAAC;AApBD,wEAoBC", "sourcesContent": ["import { getGlobalObject, logger } from '@sentry/utils';\n\nimport { IdleTransaction } from '../idletransaction';\nimport { SpanStatus } from '../spanstatus';\nimport { getActiveTransaction } from '../utils';\n\nconst global = getGlobalObject<Window>();\n\n/**\n * Add a listener that cancels and finishes a transaction when the global\n * document is hidden.\n */\nexport function registerBackgroundTabDetection(): void {\n  if (global && global.document) {\n    global.document.addEventListener('visibilitychange', () => {\n      const activeTransaction = getActiveTransaction() as IdleTransaction;\n      if (global.document.hidden && activeTransaction) {\n        logger.log(\n          `[Tracing] Transaction: ${SpanStatus.Cancelled} -> since tab moved to the background, op: ${activeTransaction.op}`,\n        );\n        // We should not set status if it is already set, this prevent important statuses like\n        // error or data loss from being overwritten on transaction.\n        if (!activeTransaction.status) {\n          activeTransaction.setStatus(SpanStatus.Cancelled);\n        }\n        activeTransaction.setTag('visibilitychange', 'document.hidden');\n        activeTransaction.finish();\n      }\n    });\n  } else {\n    logger.warn('[Tracing] Could not set up background tab detection due to lack of global document');\n  }\n}\n"]}