{"version": 3, "file": "whenInput.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/whenInput.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,IAAI,YAA4B,CAAC;AAEpB,QAAA,SAAS,GAAG;IACvB,IAAI,CAAC,YAAY,EAAE;QACjB,YAAY,GAAG,IAAI,OAAO,CAAC,UAAA,CAAC;YAC1B,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI;gBAClD,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE;oBACxB,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nlet inputPromise: Promise<Event>;\n\nexport const whenInput = (): Promise<Event> => {\n  if (!inputPromise) {\n    inputPromise = new Promise(r => {\n      return ['scroll', 'keydown', 'pointerdown'].map(type => {\n        addEventListener(type, r, {\n          once: true,\n          passive: true,\n          capture: true,\n        });\n      });\n    });\n  }\n  return inputPromise;\n};\n"]}