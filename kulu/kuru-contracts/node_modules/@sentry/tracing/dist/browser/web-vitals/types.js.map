{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/browser/web-vitals/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Metric {\n  // The name of the metric (in acronym form).\n  name: 'CLS' | 'FCP' | 'FID' | 'LCP' | 'TTFB';\n\n  // The current value of the metric.\n  value: number;\n\n  // The delta between the current value and the last-reported value.\n  // On the first report, `delta` and `value` will always be the same.\n  delta: number;\n\n  // A unique ID representing this particular metric that's specific to the\n  // current page. This ID can be used by an analytics tool to dedupe\n  // multiple values sent for the same metric, or to group multiple deltas\n  // together and calculate a total.\n  id: string;\n\n  // `false` if the value of the metric may change in the future,\n  // for the current page.\n  isFinal: boolean;\n\n  // Any performance entries used in the metric value calculation.\n  // Note, entries will be added to the array as the value changes.\n  entries: PerformanceEntry[];\n}\n\nexport interface ReportHandler {\n  (metric: Metric): void;\n}\n\n// http://wicg.github.io/netinfo/#navigatornetworkinformation-interface\nexport interface NavigatorNetworkInformation {\n  readonly connection?: NetworkInformation;\n}\n\n// http://wicg.github.io/netinfo/#connection-types\ntype ConnectionType = 'bluetooth' | 'cellular' | 'ethernet' | 'mixed' | 'none' | 'other' | 'unknown' | 'wifi' | 'wimax';\n\n// http://wicg.github.io/netinfo/#effectiveconnectiontype-enum\ntype EffectiveConnectionType = '2g' | '3g' | '4g' | 'slow-2g';\n\n// http://wicg.github.io/netinfo/#dom-megabit\ntype Megabit = number;\n// http://wicg.github.io/netinfo/#dom-millisecond\ntype Millisecond = number;\n\n// http://wicg.github.io/netinfo/#networkinformation-interface\ninterface NetworkInformation extends EventTarget {\n  // http://wicg.github.io/netinfo/#type-attribute\n  readonly type?: ConnectionType;\n  // http://wicg.github.io/netinfo/#effectivetype-attribute\n  readonly effectiveType?: EffectiveConnectionType;\n  // http://wicg.github.io/netinfo/#downlinkmax-attribute\n  readonly downlinkMax?: Megabit;\n  // http://wicg.github.io/netinfo/#downlink-attribute\n  readonly downlink?: Megabit;\n  // http://wicg.github.io/netinfo/#rtt-attribute\n  readonly rtt?: Millisecond;\n  // http://wicg.github.io/netinfo/#savedata-attribute\n  readonly saveData?: boolean;\n  // http://wicg.github.io/netinfo/#handling-changes-to-the-underlying-connection\n  onchange?: EventListener;\n}\n\n// https://w3c.github.io/device-memory/#sec-device-memory-js-api\nexport interface NavigatorDeviceMemory {\n  readonly deviceMemory?: number;\n}\n\nexport type NavigationTimingPolyfillEntry = Omit<\n  PerformanceNavigationTiming,\n  | 'initiatorType'\n  | 'nextHopProtocol'\n  | 'redirectCount'\n  | 'transferSize'\n  | 'encodedBodySize'\n  | 'decodedBodySize'\n  | 'toJSON'\n>;\n"]}