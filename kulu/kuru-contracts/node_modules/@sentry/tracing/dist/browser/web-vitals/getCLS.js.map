{"version": 3, "file": "getCLS.js", "sourceRoot": "", "sources": ["../../../src/browser/web-vitals/getCLS.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,mDAAkD;AAClD,+CAA8C;AAC9C,yCAAiE;AACjE,2CAA0C;AAS7B,QAAA,MAAM,GAAG,UAAC,QAAuB,EAAE,gBAAwB;IAAxB,iCAAA,EAAA,wBAAwB;IACtE,IAAM,MAAM,GAAG,uBAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAEpC,IAAI,MAAuC,CAAC;IAE5C,IAAM,YAAY,GAAG,UAAC,KAAkB;QACtC,sDAAsD;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YACxB,MAAM,CAAC,KAAgB,IAAI,KAAK,CAAC,KAAK,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,EAAE,CAAC;SACV;IACH,CAAC,CAAC;IAEF,IAAM,EAAE,GAAG,iBAAO,CAAC,cAAc,EAAE,YAAuC,CAAC,CAAC;IAC5E,IAAI,EAAE,EAAE;QACN,MAAM,GAAG,2BAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAE9D,mBAAQ,CAAC,UAAC,EAAe;gBAAb,4BAAW;YACrB,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,YAAuC,CAAC,CAAC;YAE9D,IAAI,WAAW,EAAE;gBACf,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;aACvB;YACD,MAAM,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;KACJ;AACH,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { ReportHandler } from './types';\n\n// https://wicg.github.io/layout-instability/#sec-layout-shift\ninterface LayoutShift extends PerformanceEntry {\n  value: number;\n  hadRecentInput: boolean;\n}\n\nexport const getCLS = (onReport: ReportHandler, reportAllChanges = false): void => {\n  const metric = initMetric('CLS', 0);\n\n  let report: ReturnType<typeof bindReporter>;\n\n  const entryHandler = (entry: LayoutShift): void => {\n    // Only count layout shifts without recent user input.\n    if (!entry.hadRecentInput) {\n      (metric.value as number) += entry.value;\n      metric.entries.push(entry);\n      report();\n    }\n  };\n\n  const po = observe('layout-shift', entryHandler as PerformanceEntryHandler);\n  if (po) {\n    report = bindReporter(onReport, metric, po, reportAllChanges);\n\n    onHidden(({ isUnloading }) => {\n      po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n\n      if (isUnloading) {\n        metric.isFinal = true;\n      }\n      report();\n    });\n  }\n};\n"]}