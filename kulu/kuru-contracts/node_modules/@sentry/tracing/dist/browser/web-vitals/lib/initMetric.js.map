{"version": 3, "file": "initMetric.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/initMetric.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAGH,uDAAsD;AAEzC,QAAA,UAAU,GAAG,UAAC,IAAoB,EAAE,KAAU;IAAV,sBAAA,EAAA,SAAS,CAAC;IACzD,OAAO;QACL,IAAI,MAAA;QACJ,KAAK,OAAA;QACL,KAAK,EAAE,CAAC;QACR,OAAO,EAAE,EAAE;QACX,EAAE,EAAE,mCAAgB,EAAE;QACtB,OAAO,EAAE,KAAK;KACf,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Metric } from '../types';\nimport { generateUniqueID } from './generateUniqueID';\n\nexport const initMetric = (name: Metric['name'], value = -1): Metric => {\n  return {\n    name,\n    value,\n    delta: 0,\n    entries: [],\n    id: generateUniqueID(),\n    isFinal: false,\n  };\n};\n"]}