{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAAA,qCAA2C;AAC3C,iDAAsD;AAoB7C,8BApBA,mCAAmB,CAoBA;AAnB5B,oDAAsD;AAEtD,IAAM,YAAY,yCAAQ,mBAAmB,KAAE,cAAc,0BAAA,GAAE,CAAC;AAEvD,oCAAY;AACrB,+BAA8B;AAArB,sBAAA,IAAI,CAAA;AACb,6CAA4C;AAAnC,oCAAA,WAAW,CAAA;AACpB,qCAImB;AAHjB,mDAAA,8BAA8B,CAAA;AAE9B,yDAAA,oCAAoC,CAAA;AAEtC,2CAA0C;AAAjC,kCAAA,UAAU,CAAA;AACnB,qDAAoD;AAA3C,4CAAA,eAAe,CAAA;AACxB,iDAAuD;AAA9C,+CAAA,oBAAoB,CAAA;AAE7B,mEAAmE;AACnE,mCAAmB,EAAE,CAAC;AAItB,iCAMiB;AALf,yCAAA,sBAAsB,CAAA;AACtB,uCAAA,oBAAoB,CAAA;AACpB,oCAAA,iBAAiB,CAAA;AACjB,2CAAA,wBAAwB,CAAA;AACxB,qCAAA,kBAAkB,CAAA", "sourcesContent": ["import { BrowserTracing } from './browser';\nimport { addExtensionMethods } from './hubextensions';\nimport * as TracingIntegrations from './integrations';\n\nconst Integrations = { ...TracingIntegrations, BrowserTracing };\n\nexport { Integrations };\nexport { Span } from './span';\nexport { Transaction } from './transaction';\nexport {\n  registerRequestInstrumentation,\n  RequestInstrumentationOptions,\n  defaultRequestInstrumentationOptions,\n} from './browser';\nexport { SpanStatus } from './spanstatus';\nexport { IdleTransaction } from './idletransaction';\nexport { startIdleTransaction } from './hubextensions';\n\n// We are patching the global object with our hub extension methods\naddExtensionMethods();\n\nexport { addExtensionMethods };\n\nexport {\n  extractTraceparentData,\n  getActiveTransaction,\n  hasTracingEnabled,\n  stripUrlQueryAndFragment,\n  TRACEPARENT_REGEXP,\n} from './utils';\n"]}