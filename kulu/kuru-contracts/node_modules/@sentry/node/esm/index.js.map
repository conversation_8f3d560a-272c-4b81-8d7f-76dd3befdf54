{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EASL,QAAQ,EAGR,MAAM,GAGP,MAAM,eAAe,CAAC;AAEvB,OAAO,EACL,uBAAuB,EACvB,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,GAAG,EACH,QAAQ,EACR,KAAK,EACL,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,GACV,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,WAAW,EAAe,MAAM,WAAW,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC7E,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AAElD,OAAO,EAAE,YAAY,IAAI,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAC;AAEjC,OAAO,KAAK,QAAQ,MAAM,YAAY,CAAC;AACvC,OAAO,KAAK,gBAAgB,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,UAAU,MAAM,cAAc,CAAC;AAE3C,IAAM,YAAY,yBACb,gBAAgB,GAChB,gBAAgB,CACpB,CAAC;AAEF,OAAO,EAAE,YAAY,IAAI,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAE9D,mHAAmH;AACnH,uGAAuG;AACvG,IAAM,OAAO,GAAG,cAAc,EAAE,CAAC;AACjC,IAAI,OAAO,CAAC,UAAU,EAAE;IACtB,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE,CAAC;IACpE,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC;CACvF", "sourcesContent": ["export {\n  Breadcrumb,\n  BreadcrumbHint,\n  Request,\n  SdkInfo,\n  Event,\n  EventHint,\n  Exception,\n  Response,\n  Severity,\n  StackFrame,\n  Stacktrace,\n  Status,\n  Thread,\n  User,\n} from '@sentry/types';\n\nexport {\n  addGlobalEventProcessor,\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  getHubFromCarrier,\n  getCurrentHub,\n  Hub,\n  makeMain,\n  Scope,\n  startTransaction,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  withScope,\n} from '@sentry/core';\n\nexport { NodeBackend, NodeOptions } from './backend';\nexport { NodeClient } from './client';\nexport { defaultIntegrations, init, lastEventId, flush, close } from './sdk';\nexport { SDK_NAME, SDK_VERSION } from './version';\n\nimport { Integrations as CoreIntegrations } from '@sentry/core';\nimport { getMainCarrier } from '@sentry/hub';\nimport * as domain from 'domain';\n\nimport * as Handlers from './handlers';\nimport * as NodeIntegrations from './integrations';\nimport * as Transports from './transports';\n\nconst INTEGRATIONS = {\n  ...CoreIntegrations,\n  ...NodeIntegrations,\n};\n\nexport { INTEGRATIONS as Integrations, Transports, Handlers };\n\n// We need to patch domain on the global __SENTRY__ object to make it work for node in cross-platform packages like\n// @sentry/hub. If we don't do this, browser bundlers will have troubles resolving `require('domain')`.\nconst carrier = getMainCarrier();\nif (carrier.__SENTRY__) {\n  carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n  carrier.__SENTRY__.extensions.domain = carrier.__SENTRY__.extensions.domain || domain;\n}\n"]}