{"version": 3, "file": "parsers.js", "sourceRoot": "", "sources": ["../src/parsers.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAClF,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAC9B,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAGjC,OAAO,KAAK,UAAU,MAAM,cAAc,CAAC;AAE3C,IAAM,wBAAwB,GAAW,CAAC,CAAC;AAC3C,IAAM,kBAAkB,GAAG,IAAI,MAAM,CAAwB,GAAG,CAAC,CAAC;AAElE;;;GAGG;AACH,MAAM,UAAU,qBAAqB;IACnC,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAC7B,CAAC;AAED,YAAY;AACZ,SAAS,WAAW,CAAC,KAA4B;IAC/C,IAAI;QACF,OAAO,KAAK,CAAC,YAAY,IAAO,KAAK,CAAC,QAAQ,UAAI,KAAK,CAAC,UAAU,IAAI,aAAa,CAAE,CAAC;KACvF;IAAC,OAAO,CAAC,EAAE;QACV,0DAA0D;QAC1D,+BAA+B;QAC/B,+DAA+D;QAC/D,OAAO,aAAa,CAAC;KACtB;AACH,CAAC;AAED,IAAM,UAAU,GAAW,CAAG,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,OAAG,CAAC;AAE1B,YAAY;AACZ,SAAS,SAAS,CAAC,QAAgB,EAAE,IAAa;IAChD,IAAI,CAAC,IAAI,EAAE;QACT,6CAA6C;QAC7C,IAAI,GAAG,UAAU,CAAC;KACnB;IAED,6BAA6B;IAC7B,IAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACvC,6CAA6C;IAC7C,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC/C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACV,6BAA6B;QAC7B,OAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,SAAI,IAAM,CAAC;KACjE;IACD,8CAA8C;IAC9C,6DAA6D;IAC7D,CAAC,GAAG,CAAG,QAAQ,MAAG,CAAA,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACxC,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAClE,IAAI,UAAU,EAAE;YACd,UAAU,IAAI,GAAG,CAAC;SACnB;QACD,UAAU,IAAI,IAAI,CAAC;QACnB,OAAO,UAAU,CAAC;KACnB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,SAAmB;IAC1C,oDAAoD;IACpD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAChC;IAED,OAAO,IAAI,WAAW,CAEnB,UAAA,OAAO;QACR,IAAM,WAAW,GAEb,EAAE,CAAC;QAEP,IAAI,KAAK,GAAG,CAAC,CAAC;gCAEL,CAAC;YACR,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/C,sBAAsB;YACtB,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,oEAAoE;gBACpE,0CAA0C;gBAC1C,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;iBAC/B;gBACD,uCAAuC;gBACvC,KAAK,EAAE,CAAC;gBACR,uFAAuF;gBACvF,6CAA6C;gBAC7C,IAAI,KAAK,KAAK,SAAS,CAAC,MAAM,EAAE;oBAC9B,OAAO,CAAC,WAAW,CAAC,CAAC;iBACtB;;aAEF;YAED,QAAQ,CAAC,QAAQ,EAAE,UAAC,GAAiB,EAAE,IAAY;gBACjD,IAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7C,WAAW,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;gBAEhC,iGAAiG;gBACjG,gDAAgD;gBAChD,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC1C,uCAAuC;gBACvC,KAAK,EAAE,CAAC;gBACR,IAAI,KAAK,KAAK,SAAS,CAAC,MAAM,EAAE;oBAC9B,OAAO,CAAC,WAAW,CAAC,CAAC;iBACtB;YACH,CAAC,CAAC,CAAC;;QAlCL,4DAA4D;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAhC,CAAC;SAkCT;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAY;IAChD,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,EAAE,CAAC;KACX;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAA8B,EAAE,OAAqB;IAC9E,IAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,IAAM,cAAc,GAClB,OAAO,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,CAAC;IAE5G,IAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,UAAA,KAAK;QAC1C,IAAM,WAAW,GAAe;YAC9B,KAAK,EAAE,KAAK,CAAC,YAAY;YACzB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE;YAC9B,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;YAC5B,MAAM,EAAE,KAAK,CAAC,UAAU;SACzB,CAAC;QAEF,IAAM,UAAU,GACd,KAAK,CAAC,MAAM;YACZ,CAAC,WAAW,CAAC,QAAQ;gBACnB,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;gBACrC,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;gBACrC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/C,qFAAqF;QACrF,yEAAyE;QACzE,yDAAyD;QACzD,WAAW,CAAC,MAAM;YAChB,CAAC,UAAU,IAAI,WAAW,CAAC,QAAQ,KAAK,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;QAE5G,8CAA8C;QAC9C,IAAI,WAAW,CAAC,QAAQ,EAAE;YACxB,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,CAAC,UAAU,IAAI,cAAc,GAAG,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzF,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACxC;SACF;QAED,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,iEAAiE;IACjE,IAAI,cAAc,IAAI,CAAC,EAAE;QACvB,OAAO,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACpC;IAED,IAAI;QACF,OAAO,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;KAC/D;IAAC,OAAO,CAAC,EAAE;QACV,sFAAsF;QACtF,kFAAkF;QAClF,OAAO,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACpC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CACxB,WAAqB,EACrB,MAAoB,EACpB,cAAsB;IAEtB,OAAO,IAAI,WAAW,CAAe,UAAA,OAAO;QAC1C,OAAA,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAA,WAAW;YAC3C,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK;gBAC7B,IAAI,KAAK,CAAC,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;oBACjD,IAAI;wBACF,IAAM,KAAK,GAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAElE,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;qBACjD;oBAAC,OAAO,CAAC,EAAE;wBACV,mCAAmC;wBACnC,0EAA0E;qBAC3E;iBACF;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC;IAhBF,CAgBE,CACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAY,EAAE,OAAqB;IACvE,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;IAClD,IAAM,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC3C,OAAO,IAAI,WAAW,CAAY,UAAA,OAAO;QACvC,OAAA,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;YACpC,IAAM,MAAM,GAAG;gBACb,UAAU,EAAE;oBACV,MAAM,EAAE,qBAAqB,CAAC,MAAM,CAAC;iBACtC;gBACD,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;YACF,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC;IATF,CASE,CACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAoB,EAAE,OAAqB;IACpE,OAAO,IAAI,WAAW,CAAQ,UAAA,OAAO;QACnC,OAAA,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,SAAoB;YAC9D,OAAO,CAAC;gBACN,SAAS,EAAE;oBACT,MAAM,EAAE,CAAC,SAAS,CAAC;iBACpB;aACF,CAAC,CAAC;QACL,CAAC,CAAC;IANF,CAME,CACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAmB;IACvD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QAC3B,OAAO,EAAE,CAAC;KACX;IAED,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAM,kBAAkB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC;IAExD,IAAI,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,kBAAkB,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;QAChH,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAClC;IAED,4EAA4E;IAC5E,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC;AAC9B,CAAC", "sourcesContent": ["import { Event, Exception, ExtendedError, StackFrame } from '@sentry/types';\nimport { addContextToFrame, basename, dirname, SyncPromise } from '@sentry/utils';\nimport { readFile } from 'fs';\nimport { LRUMap } from 'lru_map';\n\nimport { NodeOptions } from './backend';\nimport * as stacktrace from './stacktrace';\n\nconst DEFAULT_LINES_OF_CONTEXT: number = 7;\nconst FILE_CONTENT_CACHE = new LRUMap<string, string | null>(100);\n\n/**\n * Resets the file cache. Exists for testing purposes.\n * @hidden\n */\nexport function resetFileContentCache(): void {\n  FILE_CONTENT_CACHE.clear();\n}\n\n/** JSDoc */\nfunction getFunction(frame: stacktrace.StackFrame): string {\n  try {\n    return frame.functionName || `${frame.typeName}.${frame.methodName || '<anonymous>'}`;\n  } catch (e) {\n    // This seems to happen sometimes when using 'use strict',\n    // stemming from `getTypeName`.\n    // [TypeError: Cannot read property 'constructor' of undefined]\n    return '<anonymous>';\n  }\n}\n\nconst mainModule: string = `${(require.main && require.main.filename && dirname(require.main.filename)) ||\n  global.process.cwd()}/`;\n\n/** JSDoc */\nfunction getModule(filename: string, base?: string): string {\n  if (!base) {\n    // eslint-disable-next-line no-param-reassign\n    base = mainModule;\n  }\n\n  // It's specifically a module\n  const file = basename(filename, '.js');\n  // eslint-disable-next-line no-param-reassign\n  filename = dirname(filename);\n  let n = filename.lastIndexOf('/node_modules/');\n  if (n > -1) {\n    // /node_modules/ is 14 chars\n    return `${filename.substr(n + 14).replace(/\\//g, '.')}:${file}`;\n  }\n  // Let's see if it's a part of the main module\n  // To be a part of main module, it has to share the same base\n  n = `${filename}/`.lastIndexOf(base, 0);\n  if (n === 0) {\n    let moduleName = filename.substr(base.length).replace(/\\//g, '.');\n    if (moduleName) {\n      moduleName += ':';\n    }\n    moduleName += file;\n    return moduleName;\n  }\n  return file;\n}\n\n/**\n * This function reads file contents and caches them in a global LRU cache.\n * Returns a Promise filepath => content array for all files that we were able to read.\n *\n * @param filenames Array of filepaths to read content from.\n */\nfunction readSourceFiles(filenames: string[]): PromiseLike<{ [key: string]: string | null }> {\n  // we're relying on filenames being de-duped already\n  if (filenames.length === 0) {\n    return SyncPromise.resolve({});\n  }\n\n  return new SyncPromise<{\n    [key: string]: string | null;\n  }>(resolve => {\n    const sourceFiles: {\n      [key: string]: string | null;\n    } = {};\n\n    let count = 0;\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < filenames.length; i++) {\n      const filename = filenames[i];\n\n      const cache = FILE_CONTENT_CACHE.get(filename);\n      // We have a cache hit\n      if (cache !== undefined) {\n        // If it's not null (which means we found a file and have a content)\n        // we set the content and return it later.\n        if (cache !== null) {\n          sourceFiles[filename] = cache;\n        }\n        // eslint-disable-next-line no-plusplus\n        count++;\n        // In any case we want to skip here then since we have a content already or we couldn't\n        // read the file and don't want to try again.\n        if (count === filenames.length) {\n          resolve(sourceFiles);\n        }\n        continue;\n      }\n\n      readFile(filename, (err: Error | null, data: Buffer) => {\n        const content = err ? null : data.toString();\n        sourceFiles[filename] = content;\n\n        // We always want to set the cache, even to null which means there was an error reading the file.\n        // We do not want to try to read the file again.\n        FILE_CONTENT_CACHE.set(filename, content);\n        // eslint-disable-next-line no-plusplus\n        count++;\n        if (count === filenames.length) {\n          resolve(sourceFiles);\n        }\n      });\n    }\n  });\n}\n\n/**\n * @hidden\n */\nexport function extractStackFromError(error: Error): stacktrace.StackFrame[] {\n  const stack = stacktrace.parse(error);\n  if (!stack) {\n    return [];\n  }\n  return stack;\n}\n\n/**\n * @hidden\n */\nexport function parseStack(stack: stacktrace.StackFrame[], options?: NodeOptions): PromiseLike<StackFrame[]> {\n  const filesToRead: string[] = [];\n\n  const linesOfContext =\n    options && options.frameContextLines !== undefined ? options.frameContextLines : DEFAULT_LINES_OF_CONTEXT;\n\n  const frames: StackFrame[] = stack.map(frame => {\n    const parsedFrame: StackFrame = {\n      colno: frame.columnNumber,\n      filename: frame.fileName || '',\n      function: getFunction(frame),\n      lineno: frame.lineNumber,\n    };\n\n    const isInternal =\n      frame.native ||\n      (parsedFrame.filename &&\n        !parsedFrame.filename.startsWith('/') &&\n        !parsedFrame.filename.startsWith('.') &&\n        parsedFrame.filename.indexOf(':\\\\') !== 1);\n\n    // in_app is all that's not an internal Node function or a module within node_modules\n    // note that isNative appears to return true even for node core libraries\n    // see https://github.com/getsentry/raven-node/issues/176\n    parsedFrame.in_app =\n      !isInternal && parsedFrame.filename !== undefined && parsedFrame.filename.indexOf('node_modules/') === -1;\n\n    // Extract a module name based on the filename\n    if (parsedFrame.filename) {\n      parsedFrame.module = getModule(parsedFrame.filename);\n\n      if (!isInternal && linesOfContext > 0 && filesToRead.indexOf(parsedFrame.filename) === -1) {\n        filesToRead.push(parsedFrame.filename);\n      }\n    }\n\n    return parsedFrame;\n  });\n\n  // We do an early return if we do not want to fetch context liens\n  if (linesOfContext <= 0) {\n    return SyncPromise.resolve(frames);\n  }\n\n  try {\n    return addPrePostContext(filesToRead, frames, linesOfContext);\n  } catch (_) {\n    // This happens in electron for example where we are not able to read files from asar.\n    // So it's fine, we recover be just returning all frames without pre/post context.\n    return SyncPromise.resolve(frames);\n  }\n}\n\n/**\n * This function tries to read the source files + adding pre and post context (source code)\n * to a frame.\n * @param filesToRead string[] of filepaths\n * @param frames StackFrame[] containg all frames\n */\nfunction addPrePostContext(\n  filesToRead: string[],\n  frames: StackFrame[],\n  linesOfContext: number,\n): PromiseLike<StackFrame[]> {\n  return new SyncPromise<StackFrame[]>(resolve =>\n    readSourceFiles(filesToRead).then(sourceFiles => {\n      const result = frames.map(frame => {\n        if (frame.filename && sourceFiles[frame.filename]) {\n          try {\n            const lines = (sourceFiles[frame.filename] as string).split('\\n');\n\n            addContextToFrame(lines, frame, linesOfContext);\n          } catch (e) {\n            // anomaly, being defensive in case\n            // unlikely to ever happen in practice but can definitely happen in theory\n          }\n        }\n        return frame;\n      });\n\n      resolve(result);\n    }),\n  );\n}\n\n/**\n * @hidden\n */\nexport function getExceptionFromError(error: Error, options?: NodeOptions): PromiseLike<Exception> {\n  const name = error.name || error.constructor.name;\n  const stack = extractStackFromError(error);\n  return new SyncPromise<Exception>(resolve =>\n    parseStack(stack, options).then(frames => {\n      const result = {\n        stacktrace: {\n          frames: prepareFramesForEvent(frames),\n        },\n        type: name,\n        value: error.message,\n      };\n      resolve(result);\n    }),\n  );\n}\n\n/**\n * @hidden\n */\nexport function parseError(error: ExtendedError, options?: NodeOptions): PromiseLike<Event> {\n  return new SyncPromise<Event>(resolve =>\n    getExceptionFromError(error, options).then((exception: Exception) => {\n      resolve({\n        exception: {\n          values: [exception],\n        },\n      });\n    }),\n  );\n}\n\n/**\n * @hidden\n */\nexport function prepareFramesForEvent(stack: StackFrame[]): StackFrame[] {\n  if (!stack || !stack.length) {\n    return [];\n  }\n\n  let localStack = stack;\n  const firstFrameFunction = localStack[0].function || '';\n\n  if (firstFrameFunction.indexOf('captureMessage') !== -1 || firstFrameFunction.indexOf('captureException') !== -1) {\n    localStack = localStack.slice(1);\n  }\n\n  // The frame where the crash happened, should be the last entry in the array\n  return localStack.reverse();\n}\n"]}