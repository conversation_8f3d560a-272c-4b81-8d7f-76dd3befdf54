{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../src/transports/http.ts"], "names": [], "mappings": ";;AACA,uCAA4C;AAC5C,2BAA6B;AAE7B,+BAAuC;AAEvC,iCAAiC;AACjC;IAAmC,yCAAa;IAC9C,+CAA+C;IAC/C,uBAA0B,OAAyB;QAAnD,YACE,kBAAM,OAAO,CAAC,SAMf;QAPyB,aAAO,GAAP,OAAO,CAAkB;QAEjD,IAAM,KAAK,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QAC1D,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,KAAI,CAAC,MAAM,GAAG,KAAK;YACjB,CAAC,CAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAgB;YAC3D,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;IAC1E,CAAC;IAED;;OAEG;IACI,iCAAS,GAAhB,UAAiB,KAAY;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,mBAAW,CAAC,sCAAsC,CAAC,CAAC;SAC/D;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IACH,oBAAC;AAAD,CAAC,AApBD,CAAmC,oBAAa,GAoB/C;AApBY,sCAAa", "sourcesContent": ["import { Event, Response, TransportOptions } from '@sentry/types';\nimport { SentryError } from '@sentry/utils';\nimport * as http from 'http';\n\nimport { BaseTransport } from './base';\n\n/** Node http module transport */\nexport class HTTPTransport extends BaseTransport {\n  /** Create a new instance and set this.agent */\n  public constructor(public options: TransportOptions) {\n    super(options);\n    const proxy = options.httpProxy || process.env.http_proxy;\n    this.module = http;\n    this.client = proxy\n      ? (new (require('https-proxy-agent'))(proxy) as http.Agent)\n      : new http.Agent({ keepAlive: false, maxSockets: 30, timeout: 2000 });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): Promise<Response> {\n    if (!this.module) {\n      throw new SentryError('No module available in HTTPTransport');\n    }\n    return this._sendWithModule(this.module, event);\n  }\n}\n"]}