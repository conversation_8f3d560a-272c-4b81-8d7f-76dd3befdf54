{"version": 3, "file": "onunhandledrejection.js", "sourceRoot": "", "sources": ["../../src/integrations/onunhandledrejection.ts"], "names": [], "mappings": ";AAAA,qCAAoD;AAEpD,uCAA+C;AAE/C,wCAAgD;AAIhD,uCAAuC;AACvC;IAWE;;OAEG;IACH,8BACmB,QAMG;QANH,yBAAA,EAAA,aAMX,IAAI,EAAE,MAAM,EAAE;QANH,aAAQ,GAAR,QAAQ,CAML;QAftB;;WAEG;QACI,SAAI,GAAW,oBAAoB,CAAC,EAAE,CAAC;IAa3C,CAAC;IAEJ;;OAEG;IACI,wCAAS,GAAhB;QACE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;;;OAIG;IACH,iHAAiH;IAC1G,mDAAoB,GAA3B,UAA4B,MAAW,EAAE,OAAY;QACnD,IAAM,GAAG,GAAG,oBAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE;YAC7C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC9B,OAAO;SACR;QAED,+DAA+D;QAC/D,IAAM,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAEvE,GAAG,CAAC,SAAS,CAAC,UAAC,KAAY;YACzB,KAAK,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;YAElD,2DAA2D;YAC3D,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC7B;YACD,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC7B;YACD,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAChC;YAED,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,+DAA+D;QAE/D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,8DAA8D;IACtD,+CAAgB,GAAxB,UAAyB,MAAW;QAClC,0HAA0H;QAC1H,IAAM,gBAAgB,GACpB,kCAAkC;YAClC,8DAA8D;YAC9D,gEAAgE;YAChE,wCAAwC,CAAC;QAE3C,+BAA+B;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM,EAAE;YACjC,sBAAc,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC/B,sEAAsE;gBACtE,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC1C,sBAAc,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YACH,4BAAiB,CAAC,MAAM,CAAC,CAAC;SAC3B;QACD,8BAA8B;IAChC,CAAC;IA9FD;;OAEG;IACW,uBAAE,GAAW,sBAAsB,CAAC;IA4FpD,2BAAC;CAAA,AAhGD,IAgGC;AAhGY,oDAAoB", "sourcesContent": ["import { getCurrentHub, Scope } from '@sentry/core';\nimport { Integration } from '@sentry/types';\nimport { consoleSandbox } from '@sentry/utils';\n\nimport { logAndExitProcess } from '../handlers';\n\ntype UnhandledRejectionMode = 'none' | 'warn' | 'strict';\n\n/** Global Promise Rejection handler */\nexport class OnUnhandledRejection implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'OnUnhandledRejection';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = OnUnhandledRejection.id;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(\n    private readonly _options: {\n      /**\n       * Option deciding what to do after capturing unhandledRejection,\n       * that mimicks behavior of node's --unhandled-rejection flag.\n       */\n      mode: UnhandledRejectionMode;\n    } = { mode: 'warn' },\n  ) {}\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    global.process.on('unhandledRejection', this.sendUnhandledPromise.bind(this));\n  }\n\n  /**\n   * Send an exception with reason\n   * @param reason string\n   * @param promise promise\n   */\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/no-explicit-any\n  public sendUnhandledPromise(reason: any, promise: any): void {\n    const hub = getCurrentHub();\n\n    if (!hub.getIntegration(OnUnhandledRejection)) {\n      this._handleRejection(reason);\n      return;\n    }\n\n    /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n    const context = (promise.domain && promise.domain.sentryContext) || {};\n\n    hub.withScope((scope: Scope) => {\n      scope.setExtra('unhandledPromiseRejection', true);\n\n      // Preserve backwards compatibility with raven-node for now\n      if (context.user) {\n        scope.setUser(context.user);\n      }\n      if (context.tags) {\n        scope.setTags(context.tags);\n      }\n      if (context.extra) {\n        scope.setExtras(context.extra);\n      }\n\n      hub.captureException(reason, { originalException: promise });\n    });\n    /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n\n    this._handleRejection(reason);\n  }\n\n  /**\n   * Handler for `mode` option\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _handleRejection(reason: any): void {\n    // https://github.com/nodejs/node/blob/7cf6f9e964aa00772965391c23acda6d71972a9a/lib/internal/process/promises.js#L234-L240\n    const rejectionWarning =\n      'This error originated either by ' +\n      'throwing inside of an async function without a catch block, ' +\n      'or by rejecting a promise which was not handled with .catch().' +\n      ' The promise rejected with the reason:';\n\n    /* eslint-disable no-console */\n    if (this._options.mode === 'warn') {\n      consoleSandbox(() => {\n        console.warn(rejectionWarning);\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        console.error(reason && reason.stack ? reason.stack : reason);\n      });\n    } else if (this._options.mode === 'strict') {\n      consoleSandbox(() => {\n        console.warn(rejectionWarning);\n      });\n      logAndExitProcess(reason);\n    }\n    /* eslint-enable no-console */\n  }\n}\n"]}