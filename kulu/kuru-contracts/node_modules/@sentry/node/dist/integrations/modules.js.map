{"version": 3, "file": "modules.js", "sourceRoot": "", "sources": ["../../src/integrations/modules.ts"], "names": [], "mappings": ";;AACA,yBAA8C;AAC9C,6BAAqC;AAErC,IAAI,WAAsC,CAAC;AAE3C,sCAAsC;AACtC,SAAS,QAAQ;IACf,IAAI;QACF,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAgC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACnF;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED,qDAAqD;AACrD,SAAS,cAAc;IAGrB,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IAC7D,IAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;IACzB,IAAM,KAAK,GAEP,EAAE,CAAC;IACP,IAAM,IAAI,GAEN,EAAE,CAAC;IAEP,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;QAChB,IAAI,GAAG,GAAG,IAAI,CAAC;QAEf,qEAAqE;QACrE,IAAM,KAAK,GAAG;YACZ,IAAM,IAAI,GAAG,GAAG,CAAC;YACjB,GAAG,GAAG,cAAO,CAAC,IAAI,CAAC,CAAC;YAEpB,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,OAAO,SAAS,CAAC;aAClB;YACD,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAC9B,OAAO,KAAK,EAAE,CAAC;aAChB;YAED,IAAM,OAAO,GAAG,WAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YAElB,IAAI,CAAC,eAAU,CAAC,OAAO,CAAC,EAAE;gBACxB,OAAO,KAAK,EAAE,CAAC;aAChB;YAED,IAAI;gBACF,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAGpD,CAAC;gBACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;aACjC;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW;aACZ;QACH,CAAC,CAAC;QAEF,KAAK,EAAE,CAAC;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED,+CAA+C;AAC/C;IAAA;QAME;;WAEG;QACI,SAAI,GAAW,OAAO,CAAC,EAAE,CAAC;IAwBnC,CAAC;IAtBC;;OAEG;IACI,2BAAS,GAAhB,UAAiB,uBAA2D,EAAE,aAAwB;QAAtG,iBAUC;QATC,uBAAuB,CAAC,UAAA,KAAK;YAC3B,IAAI,CAAC,aAAa,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC5C,OAAO,KAAK,CAAC;aACd;YACD,6CACK,KAAK,KACR,OAAO,EAAE,KAAI,CAAC,WAAW,EAAE,IAC3B;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kGAAkG;IAC1F,6BAAW,GAAnB;QACE,IAAI,CAAC,WAAW,EAAE;YAChB,WAAW,GAAG,cAAc,EAAE,CAAC;SAChC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IA/BD;;OAEG;IACW,UAAE,GAAW,SAAS,CAAC;IA6BvC,cAAC;CAAA,AAjCD,IAiCC;AAjCY,0BAAO", "sourcesContent": ["import { EventProcessor, Hub, Integration } from '@sentry/types';\nimport { existsSync, readFileSync } from 'fs';\nimport { dirname, join } from 'path';\n\nlet moduleCache: { [key: string]: string };\n\n/** Extract information about paths */\nfunction getPaths(): string[] {\n  try {\n    return require.cache ? Object.keys(require.cache as Record<string, unknown>) : [];\n  } catch (e) {\n    return [];\n  }\n}\n\n/** Extract information about package.json modules */\nfunction collectModules(): {\n  [name: string]: string;\n} {\n  const mainPaths = (require.main && require.main.paths) || [];\n  const paths = getPaths();\n  const infos: {\n    [name: string]: string;\n  } = {};\n  const seen: {\n    [path: string]: boolean;\n  } = {};\n\n  paths.forEach(path => {\n    let dir = path;\n\n    /** Traverse directories upward in the search of package.json file */\n    const updir = (): void | (() => void) => {\n      const orig = dir;\n      dir = dirname(orig);\n\n      if (!dir || orig === dir || seen[orig]) {\n        return undefined;\n      }\n      if (mainPaths.indexOf(dir) < 0) {\n        return updir();\n      }\n\n      const pkgfile = join(orig, 'package.json');\n      seen[orig] = true;\n\n      if (!existsSync(pkgfile)) {\n        return updir();\n      }\n\n      try {\n        const info = JSON.parse(readFileSync(pkgfile, 'utf8')) as {\n          name: string;\n          version: string;\n        };\n        infos[info.name] = info.version;\n      } catch (_oO) {\n        // no-empty\n      }\n    };\n\n    updir();\n  });\n\n  return infos;\n}\n\n/** Add node modules / packages to the event */\nexport class Modules implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Modules';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Modules.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    addGlobalEventProcessor(event => {\n      if (!getCurrentHub().getIntegration(Modules)) {\n        return event;\n      }\n      return {\n        ...event,\n        modules: this._getModules(),\n      };\n    });\n  }\n\n  /** Fetches the list of modules and the versions loaded by the entry file for your node.js app. */\n  private _getModules(): { [key: string]: string } {\n    if (!moduleCache) {\n      moduleCache = collectModules();\n    }\n    return moduleCache;\n  }\n}\n"]}