{"version": 3, "file": "backend.js", "sourceRoot": "", "sources": ["../src/backend.ts"], "names": [], "mappings": ";;AAAA,qCAA0D;AAC1D,uCAA4G;AAC5G,uCASuB;AAEvB,qCAAiG;AACjG,2CAA6D;AA6B7D;;;GAGG;AACH;IAAiC,uCAAwB;IAAzD;;IAyGA,CAAC;IAxGC;;OAEG;IACH,iHAAiH;IAC1G,wCAAkB,GAAzB,UAA0B,SAAc,EAAE,IAAgB;QAA1D,iBA0CC;QAzCC,8DAA8D;QAC9D,IAAI,EAAE,GAAQ,SAAS,CAAC;QACxB,IAAM,SAAS,GAAc;YAC3B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,SAAS;SAChB,CAAC;QAEF,IAAI,CAAC,eAAO,CAAC,SAAS,CAAC,EAAE;YACvB,IAAI,qBAAa,CAAC,SAAS,CAAC,EAAE;gBAC5B,6DAA6D;gBAC7D,yEAAyE;gBACzE,IAAM,OAAO,GAAG,6CAA2C,sCAA8B,CAAC,SAAS,CAAG,CAAC;gBAEvG,oBAAa,EAAE,CAAC,cAAc,CAAC,UAAA,KAAK;oBAClC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,uBAAe,CAAC,SAAoC,CAAC,CAAC,CAAC;gBAC1F,CAAC,CAAC,CAAC;gBAEH,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC5D,EAAY,CAAC,OAAO,GAAG,OAAO,CAAC;aACjC;iBAAM;gBACL,+DAA+D;gBAC/D,yEAAyE;gBACzE,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,KAAK,CAAC,SAAmB,CAAC,CAAC;gBACxE,EAAY,CAAC,OAAO,GAAG,SAAS,CAAC;aACnC;YACD,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;SAC5B;QAED,OAAO,IAAI,mBAAW,CAAQ,UAAC,OAAO,EAAE,MAAM;YAC5C,OAAA,oBAAU,CAAC,EAAW,EAAE,KAAI,CAAC,QAAQ,CAAC;iBACnC,IAAI,CAAC,UAAA,KAAK;gBACT,6BAAqB,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;gBACnD,6BAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBAExC,OAAO,uCACF,KAAK,KACR,QAAQ,EAAE,IAAI,IAAI,IAAI,CAAC,QAAQ,IAC/B,CAAC;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QAVrB,CAUqB,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,sCAAgB,GAAvB,UAAwB,OAAe,EAAE,KAA+B,EAAE,IAAgB;QAA1F,iBAwBC;QAxBwC,sBAAA,EAAA,QAAkB,gBAAQ,CAAC,IAAI;QACtE,IAAM,KAAK,GAAU;YACnB,QAAQ,EAAE,IAAI,IAAI,IAAI,CAAC,QAAQ;YAC/B,KAAK,OAAA;YACL,OAAO,SAAA;SACR,CAAC;QAEF,OAAO,IAAI,mBAAW,CAAQ,UAAA,OAAO;YACnC,IAAI,KAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACrE,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,+BAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5F,oBAAU,CAAC,KAAK,EAAE,KAAI,CAAC,QAAQ,CAAC;qBAC7B,IAAI,CAAC,UAAA,MAAM;oBACV,KAAK,CAAC,UAAU,GAAG;wBACjB,MAAM,EAAE,+BAAqB,CAAC,MAAM,CAAC;qBACtC,CAAC;oBACF,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,EAAE;oBACV,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC;aACN;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,CAAC;aAChB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,qCAAe,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACtB,6DAA6D;YAC7D,OAAO,iBAAM,eAAe,WAAE,CAAC;SAChC;QAED,IAAM,GAAG,GAAG,IAAI,WAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAM,gBAAgB,4FACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,GACnE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,GACtE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,KAChE,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,GACvB,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAC3B,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;SACtD;QACD,IAAI,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE;YAC3B,OAAO,IAAI,0BAAa,CAAC,gBAAgB,CAAC,CAAC;SAC5C;QACD,OAAO,IAAI,2BAAc,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IACH,kBAAC;AAAD,CAAC,AAzGD,CAAiC,kBAAW,GAyG3C;AAzGY,kCAAW", "sourcesContent": ["import { BaseBackend, getCurrentHub } from '@sentry/core';\nimport { Event, EventHint, Mechanism, Options, Severity, Transport, TransportOptions } from '@sentry/types';\nimport {\n  addExceptionMechanism,\n  addExceptionTypeValue,\n  Dsn,\n  extractException<PERSON>eysForMessage,\n  isError,\n  isPlainObject,\n  normalizeToSize,\n  SyncPromise,\n} from '@sentry/utils';\n\nimport { extractStackFromError, parseError, parseStack, prepareFramesForEvent } from './parsers';\nimport { HTTPSTransport, HTTPTransport } from './transports';\n\n/**\n * Configuration options for the Sentry Node SDK.\n * @see NodeClient for more information.\n */\nexport interface NodeOptions extends Options {\n  /** Sets an optional server name (device name) */\n  serverName?: string;\n\n  /** Maximum time in milliseconds to wait to drain the request queue, before the process is allowed to exit. */\n  shutdownTimeout?: number;\n\n  /** Set a HTTP proxy that should be used for outbound requests. */\n  httpProxy?: string;\n\n  /** Set a HTTPS proxy that should be used for outbound requests. */\n  httpsProxy?: string;\n\n  /** HTTPS proxy certificates path */\n  caCerts?: string;\n\n  /** Sets the number of context lines for each frame when loading a file. */\n  frameContextLines?: number;\n\n  /** Callback that is executed when a fatal global error occurs. */\n  onFatalError?(error: Error): void;\n}\n\n/**\n * The Sentry Node SDK Backend.\n * @hidden\n */\nexport class NodeBackend extends BaseBackend<NodeOptions> {\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public eventFromException(exception: any, hint?: EventHint): PromiseLike<Event> {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let ex: any = exception;\n    const mechanism: Mechanism = {\n      handled: true,\n      type: 'generic',\n    };\n\n    if (!isError(exception)) {\n      if (isPlainObject(exception)) {\n        // This will allow us to group events based on top-level keys\n        // which is much better than creating new group when any key/value change\n        const message = `Non-Error exception captured with keys: ${extractExceptionKeysForMessage(exception)}`;\n\n        getCurrentHub().configureScope(scope => {\n          scope.setExtra('__serialized__', normalizeToSize(exception as Record<string, unknown>));\n        });\n\n        ex = (hint && hint.syntheticException) || new Error(message);\n        (ex as Error).message = message;\n      } else {\n        // This handles when someone does: `throw \"something awesome\";`\n        // We use synthesized Error here so we can extract a (rough) stack trace.\n        ex = (hint && hint.syntheticException) || new Error(exception as string);\n        (ex as Error).message = exception;\n      }\n      mechanism.synthetic = true;\n    }\n\n    return new SyncPromise<Event>((resolve, reject) =>\n      parseError(ex as Error, this._options)\n        .then(event => {\n          addExceptionTypeValue(event, undefined, undefined);\n          addExceptionMechanism(event, mechanism);\n\n          resolve({\n            ...event,\n            event_id: hint && hint.event_id,\n          });\n        })\n        .then(null, reject),\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(message: string, level: Severity = Severity.Info, hint?: EventHint): PromiseLike<Event> {\n    const event: Event = {\n      event_id: hint && hint.event_id,\n      level,\n      message,\n    };\n\n    return new SyncPromise<Event>(resolve => {\n      if (this._options.attachStacktrace && hint && hint.syntheticException) {\n        const stack = hint.syntheticException ? extractStackFromError(hint.syntheticException) : [];\n        parseStack(stack, this._options)\n          .then(frames => {\n            event.stacktrace = {\n              frames: prepareFramesForEvent(frames),\n            };\n            resolve(event);\n          })\n          .then(null, () => {\n            resolve(event);\n          });\n      } else {\n        resolve(event);\n      }\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _setupTransport(): Transport {\n    if (!this._options.dsn) {\n      // We return the noop transport here in case there is no Dsn.\n      return super._setupTransport();\n    }\n\n    const dsn = new Dsn(this._options.dsn);\n\n    const transportOptions: TransportOptions = {\n      ...this._options.transportOptions,\n      ...(this._options.httpProxy && { httpProxy: this._options.httpProxy }),\n      ...(this._options.httpsProxy && { httpsProxy: this._options.httpsProxy }),\n      ...(this._options.caCerts && { caCerts: this._options.caCerts }),\n      dsn: this._options.dsn,\n    };\n\n    if (this._options.transport) {\n      return new this._options.transport(transportOptions);\n    }\n    if (dsn.protocol === 'http') {\n      return new HTTPTransport(transportOptions);\n    }\n    return new HTTPSTransport(transportOptions);\n  }\n}\n"]}