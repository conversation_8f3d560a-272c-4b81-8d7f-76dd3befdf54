{"name": "check-error", "description": "Error comparison and information related utility for node and the browser", "keywords": ["check-error", "error", "chai util"], "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://alogicalparadox.com)", "contributors": ["<PERSON> (https://github.com/davelosert)", "<PERSON> (https://github.com/keithamus)", "<PERSON><PERSON><PERSON> (https://github.com/bajtos)", "<PERSON> (https://github.com/lucasfcosta)"], "files": ["index.js", "check-error.js"], "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/check-error.git"}, "scripts": {"build": "browserify --bare $npm_package_main --standalone checkError -o check-error.js", "lint": "eslint --ignore-path .gitignore .", "prepublish": "npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest": "npm run lint", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "test:browser": "karma start --singleRun=true", "test:node": "istanbul cover _mocha", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "eslintConfig": {"extends": ["strict/es5"], "env": {"es6": true}, "globals": {"HTMLElement": false}, "rules": {"complexity": 0, "max-statements": 0}}, "dependencies": {}, "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^1.0.0", "coveralls": "2.11.9", "eslint": "^2.4.0", "eslint-config-strict": "^8.5.0", "eslint-plugin-filenames": "^0.2.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "karma": "^0.13.22", "karma-browserify": "^5.0.2", "karma-coverage": "^0.5.5", "karma-mocha": "^0.2.2", "karma-phantomjs-launcher": "^1.0.0", "karma-sauce-launcher": "^0.3.1", "lcov-result-merger": "^1.0.2", "mocha": "^2.4.5", "phantomjs-prebuilt": "^2.1.5", "semantic-release": "^4.3.5", "simple-assert": "^1.0.0", "travis-after-all": "^1.4.4", "validate-commit-msg": "^2.3.1"}, "engines": {"node": "*"}, "version": "1.0.2"}