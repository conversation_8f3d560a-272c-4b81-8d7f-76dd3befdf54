# 1.7.0 -- remove console log

1. Remove bad console log

# 1.6.0 -- empty hex to zero

1. Empty hex 0x -> 0

# 1.5.0 -- hex number fix

1. Fixed hex number convetion, now if hex prefixed, always assumes base 16

# 1.4.0 -- fix hex number

1. Fix hex number shim
2. More test cases added

# 1.3.0 -- better everythign

1. Better error messages
2. Added umd builds
3. Far more comprehensive testing coverage
4. Far more support for hex string coverage
5. Far more support for number string coverage
6. Less lines of code, more bang for your buck
7. More config

# 1.2.0 -- decimal number fix

1. now throws under decimal number

# 1.1.0 -- es5 support

1. es5 support

# 0.0.1 -- number-to-bn

1. Basic testing
2. Basic docs
3. License
4. basic exports
