{"name": "array.prototype.reduce", "version": "1.0.5", "description": "An ES5 spec-compliant `Array.prototype.reduce` shim/polyfill/replacement that works as far down as ES3.", "main": "index.js", "directories": {"test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "testling": "npx testling --html > test.html"}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/Array.prototype.reduce.git"}, "keywords": ["Array.prototype.reduce", "reduce", "array", "ES5", "shim", "polyfill", "es-shim API"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/es-shims/Array.prototype.reduce/issues"}, "homepage": "https://github.com/es-shims/Array.prototype.reduce#readme", "engines": {"node": ">= 0.4"}, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-array-method-boxes-properly": "^1.0.0", "is-string": "^1.0.7"}, "devDependencies": {"@es-shims/api": "^2.2.3", "@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}