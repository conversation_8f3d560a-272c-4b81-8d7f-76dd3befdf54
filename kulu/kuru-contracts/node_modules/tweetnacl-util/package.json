{"name": "tweetnacl-util", "version": "0.15.1", "description": "String encoding utilitlies extracted from TweetNaCl.js", "main": "nacl-util.js", "types": "nacl-util.d.ts", "scripts": {"build": "uglifyjs nacl-util.js -c -m -o nacl-util.min.js", "test": "tape test/*.js; yarn run build-browser-test", "build-browser-test": "browserify test/test.js > test/browser/bundle.js"}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-util-js.git"}, "keywords": ["base64", "utf8", "string", "encoding"], "author": "TweetNaCl-js contributors", "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-util-js/issues"}, "homepage": "https://github.com/dchest/tweetnacl-util-js", "devDependencies": {"browserify": "^16.5.0", "tape": "^4.13.0", "uglify-js": "^3.7.6"}, "browser": {"buffer": false}}