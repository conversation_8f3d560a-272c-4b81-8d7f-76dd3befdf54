{"name": "object.getownpropertydescriptors", "version": "2.1.6", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object.getownpropertydescriptors.git"}, "keywords": ["Object.getOwnPropertyDescriptors", "descriptor", "property descriptor", "ES8", "ES2017", "shim", "polyfill", "getOwnPropertyDescriptor", "es-shim API"], "dependencies": {"array.prototype.reduce": "^1.0.5", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.21.2", "safe-array-concat": "^1.0.0"}, "devDependencies": {"@es-shims/api": "^2.3.1", "@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/5.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/12.0..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.8"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "2.1.6"}, "publishConfig": {"ignore": [".github/workflows"]}}