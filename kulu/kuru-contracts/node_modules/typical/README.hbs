[![view on npm](http://img.shields.io/npm/v/typical.svg)](https://www.npmjs.org/package/typical)
[![npm module downloads](http://img.shields.io/npm/dt/typical.svg)](https://www.npmjs.org/package/typical)
[![Build Status](https://travis-ci.org/75lb/typical.svg?branch=master)](https://travis-ci.org/75lb/typical)
[![Coverage Status](https://coveralls.io/repos/github/75lb/typical/badge.svg?branch=master)](https://coveralls.io/github/75lb/typical?branch=master)
[![Dependency Status](https://badgen.net/david/dep/75lb/typical)](https://david-dm.org/75lb/typical)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

{{>main}}

## Load anywhere

This library is compatible with Node.js, the Web and any style of module loader. It can be loaded anywhere, natively without transpilation.

Node.js:

```js
const typical = require('typical')
```

Within Node.js with ECMAScript Module support enabled:

```js
import typical from 'typical'
```

Within a modern browser ECMAScript Module:

```js
import typical from './node_modules/typical/index.mjs'
```

Old browser (adds `window.typical`):

```html
<script nomodule src="./node_modules/typical/dist/index.js"></script>
```

* * *

&copy; 2014-19 Lloyd Brookes \<<EMAIL>\>. Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
