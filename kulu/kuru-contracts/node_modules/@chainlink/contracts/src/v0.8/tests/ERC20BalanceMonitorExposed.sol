// SPDX-License-Identifier: MIT

pragma solidity 0.8.6;

import "../upkeeps/ERC20BalanceMonitor.sol";

contract ERC20BalanceMonitorExposed is ERC20BalanceMonitor {
  constructor(
    address erc20TokenAddress,
    address keeperReg<PERSON>ry<PERSON>ddress,
    uint256 minWaitPeriod
  ) ERC20BalanceMonitor(erc20TokenAddress, keeperRegistryAddress, minWaitPeriod) {}

  function setLastTopUpXXXTestOnly(address target, uint56 lastTopUpTimestamp) external {
    s_targets[target].lastTopUpTimestamp = lastTopUpTimestamp;
  }
}
