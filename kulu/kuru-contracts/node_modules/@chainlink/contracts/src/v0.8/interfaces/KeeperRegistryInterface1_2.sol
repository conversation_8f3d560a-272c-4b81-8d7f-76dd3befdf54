// SPDX-License-Identifier: MIT
/**
 * @notice This is a deprecated interface. Please use AutomationRegistryInterface1_2 directly.
 */
pragma solidity ^0.8.0;
import {Config, State} from "./AutomationRegistryInterface1_2.sol";
import {AutomationRegistryBaseInterface as KeeperRegistryBaseInterface} from "./AutomationRegistryInterface1_2.sol";
import {AutomationRegistryInterface as KeeperRegistryInterface} from "./AutomationRegistryInterface1_2.sol";
import {AutomationRegistryExecutableInterface as KeeperRegistryExecutableInterface} from "./AutomationRegistryInterface1_2.sol";
