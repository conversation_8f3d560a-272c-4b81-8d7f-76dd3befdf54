[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "L1OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "L1OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "acceptL1Ownership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "l1Owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "transferL1Ownership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]