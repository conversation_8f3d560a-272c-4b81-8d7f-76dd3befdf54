[{"inputs": [], "name": "activateAuthorizedReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "senders", "type": "address[]"}], "name": "addAuthorizedSenders", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "authorizedReceiverActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deactivateAuthorizedReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAuthorizedSenders", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "isAuthorizedSender", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "senders", "type": "address[]"}], "name": "removeAuthorizedSenders", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]