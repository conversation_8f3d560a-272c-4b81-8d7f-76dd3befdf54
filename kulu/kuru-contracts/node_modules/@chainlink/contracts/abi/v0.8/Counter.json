[{"inputs": [], "name": "AlwaysRevert", "type": "error"}, {"inputs": [], "name": "alwaysRevert", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "alwaysRevertWithString", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "count", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "increment", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "reset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]