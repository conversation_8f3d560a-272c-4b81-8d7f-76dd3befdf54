[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "node", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "label", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "owner", "type": "address"}], "name": "New<PERSON>wn<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "node", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "resolver", "type": "address"}], "name": "NewResolver", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "node", "type": "bytes32"}, {"indexed": false, "internalType": "uint64", "name": "ttl", "type": "uint64"}], "name": "NewTTL", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "node", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "owner", "type": "address"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}], "name": "resolver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}, {"internalType": "address", "name": "resolver", "type": "address"}], "name": "setResolver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}, {"internalType": "bytes32", "name": "label", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "setSubnodeOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}, {"internalType": "uint64", "name": "ttl", "type": "uint64"}], "name": "setTTL", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "node", "type": "bytes32"}], "name": "ttl", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}]