[{"inputs": [{"internalType": "address", "name": "r<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "user", "type": "address"}], "name": "AddedAccess", "type": "event"}, {"anonymous": false, "inputs": [], "name": "CheckAccessDisabled", "type": "event"}, {"anonymous": false, "inputs": [], "name": "CheckAccessEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}], "name": "FlagLowered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}], "name": "FlagRaised", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previous", "type": "address"}, {"indexed": true, "internalType": "address", "name": "current", "type": "address"}], "name": "RaisingAccessControllerUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "user", "type": "address"}], "name": "RemovedAccess", "type": "event"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "addAccess", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disableAccess<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "enableAccessCheck", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}], "name": "getFlag", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "subjects", "type": "address[]"}], "name": "getFlags", "outputs": [{"internalType": "bool[]", "name": "", "type": "bool[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "bytes", "name": "_calldata", "type": "bytes"}], "name": "hasAccess", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "subjects", "type": "address[]"}], "name": "lowerFlags", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}], "name": "raiseFlag", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "subjects", "type": "address[]"}], "name": "raiseFlags", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "raisingAccessController", "outputs": [{"internalType": "contract AccessControllerInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "removeAccess", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "r<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "setRaisingAccessController", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]