[{"inputs": [{"internalType": "address", "name": "aggregator", "type": "address"}, {"internalType": "contract AggregatorValidatorInterface", "name": "validator", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "aggregator", "type": "address"}], "name": "AggregatorProposed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previous", "type": "address"}, {"indexed": true, "internalType": "address", "name": "current", "type": "address"}], "name": "AggregatorUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "proposed", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "previousRoundId", "type": "uint256"}, {"indexed": false, "internalType": "int256", "name": "previousAnswer", "type": "int256"}, {"indexed": false, "internalType": "uint256", "name": "currentRoundId", "type": "uint256"}, {"indexed": false, "internalType": "int256", "name": "currentAnswer", "type": "int256"}], "name": "ProposedAggregatorValidateCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract AggregatorValidatorInterface", "name": "validator", "type": "address"}], "name": "ValidatorProposed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract AggregatorValidatorInterface", "name": "previous", "type": "address"}, {"indexed": true, "internalType": "contract AggregatorValidatorInterface", "name": "current", "type": "address"}], "name": "ValidatorUpgraded", "type": "event"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAggregators", "outputs": [{"internalType": "address", "name": "current", "type": "address"}, {"internalType": "bool", "name": "hasProposal", "type": "bool"}, {"internalType": "address", "name": "proposed", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getValidators", "outputs": [{"internalType": "contract AggregatorValidatorInterface", "name": "current", "type": "address"}, {"internalType": "bool", "name": "hasProposal", "type": "bool"}, {"internalType": "contract AggregatorValidatorInterface", "name": "proposed", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "proposed", "type": "address"}], "name": "proposeNewAggregator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract AggregatorValidatorInterface", "name": "proposed", "type": "address"}], "name": "proposeNewValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "upgradeAggregator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "upgradeValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "previousRoundId", "type": "uint256"}, {"internalType": "int256", "name": "previousAnswer", "type": "int256"}, {"internalType": "uint256", "name": "currentRoundId", "type": "uint256"}, {"internalType": "int256", "name": "currentAnswer", "type": "int256"}], "name": "validate", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}]