[{"inputs": [{"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "int256", "name": "reason2", "type": "int256"}], "name": "Unauthorized", "type": "error"}, {"inputs": [], "name": "revertSilently", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "revertWithCustomError", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string", "name": "message", "type": "string"}], "name": "revertWithMessage", "outputs": [], "stateMutability": "pure", "type": "function"}]