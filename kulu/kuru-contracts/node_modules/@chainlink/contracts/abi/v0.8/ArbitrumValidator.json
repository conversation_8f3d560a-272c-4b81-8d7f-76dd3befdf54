[{"inputs": [{"internalType": "address", "name": "crossDomainMessengerAddr", "type": "address"}, {"internalType": "address", "name": "l2ArbitrumSequencerUptimeFeedAddr", "type": "address"}, {"internalType": "address", "name": "configACAddr", "type": "address"}, {"internalType": "uint256", "name": "maxGas", "type": "uint256"}, {"internalType": "uint256", "name": "gasPriceBid", "type": "uint256"}, {"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "gasPriceL1FeedAddr", "type": "address"}, {"internalType": "enum ArbitrumValidator.PaymentStrategy", "name": "paymentStrategy", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "user", "type": "address"}], "name": "AddedAccess", "type": "event"}, {"anonymous": false, "inputs": [], "name": "CheckAccessDisabled", "type": "event"}, {"anonymous": false, "inputs": [], "name": "CheckAccessEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previous", "type": "address"}, {"indexed": true, "internalType": "address", "name": "current", "type": "address"}], "name": "ConfigACSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "maxGas", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "gasPriceBid", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "gasPriceL1FeedAddr", "type": "address"}], "name": "GasConfigSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "refundAddr", "type": "address"}], "name": "L2WithdrawalRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum ArbitrumValidator.PaymentStrategy", "name": "paymentStrategy", "type": "uint8"}], "name": "PaymentStrategySet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "user", "type": "address"}], "name": "RemovedAccess", "type": "event"}, {"inputs": [], "name": "CROSS_DOMAIN_MESSENGER", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "L2_ALIAS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "L2_SEQ_STATUS_RECORDER", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "addAccess", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "configAC", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disableAccess<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "enableAccessCheck", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "gasConfig", "outputs": [{"components": [{"internalType": "uint256", "name": "maxGas", "type": "uint256"}, {"internalType": "uint256", "name": "gasPriceBid", "type": "uint256"}, {"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "gasPriceL1FeedAddr", "type": "address"}], "internalType": "struct ArbitrumValidator.GasConfig", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "hasAccess", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paymentStrategy", "outputs": [{"internalType": "enum ArbitrumValidator.PaymentStrategy", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "removeAccess", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "accessController", "type": "address"}], "name": "setConfigAC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxGas", "type": "uint256"}, {"internalType": "uint256", "name": "gasPriceBid", "type": "uint256"}, {"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "gasPriceL1FeedAddr", "type": "address"}], "name": "setGasConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum ArbitrumValidator.PaymentStrategy", "name": "paymentStrategy", "type": "uint8"}], "name": "setPaymentStrategy", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "int256", "name": "previousAnswer", "type": "int256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "int256", "name": "currentAnswer", "type": "int256"}], "name": "validate", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "refundAddr", "type": "address"}], "name": "withdrawFundsFromL2", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "recipient", "type": "address"}], "name": "withdrawFundsTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]