[{"inputs": [{"internalType": "uint256[]", "name": "upkeepIDs", "type": "uint256[]"}, {"internalType": "address", "name": "destination", "type": "address"}], "name": "migrateUpkeeps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "encodedUpkeeps", "type": "bytes"}], "name": "receiveUpkeeps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "upkeepTranscoderVersion", "outputs": [{"internalType": "enum UpkeepFormat", "name": "version", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function"}]