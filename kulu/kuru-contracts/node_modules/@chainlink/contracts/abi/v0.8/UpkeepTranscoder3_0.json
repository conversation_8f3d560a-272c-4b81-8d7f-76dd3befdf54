[{"inputs": [], "name": "InvalidTranscoding", "type": "error"}, {"inputs": [{"internalType": "enum UpkeepFormat", "name": "fromVersion", "type": "uint8"}, {"internalType": "enum UpkeepFormat", "name": "toVersion", "type": "uint8"}, {"internalType": "bytes", "name": "encodedUpkeeps", "type": "bytes"}], "name": "transcodeUpkeeps", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}]