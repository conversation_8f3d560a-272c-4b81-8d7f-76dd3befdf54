[{"inputs": [{"internalType": "address", "name": "_vrfCoordinator", "type": "address"}, {"internalType": "address", "name": "_link", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "OnlyCallableFromLink", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "randomness", "type": "uint256"}], "name": "rawFulfillRandomness", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "s_randomnessOutput", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_requestId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}]