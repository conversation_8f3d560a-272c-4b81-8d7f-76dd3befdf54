[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "", "type": "uint256"}], "name": "Log1", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "", "type": "uint256"}], "name": "Log2", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "", "type": "string"}], "name": "Log3", "type": "event"}, {"inputs": [{"internalType": "uint256[]", "name": "v", "type": "uint256[]"}], "name": "EmitLog1", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "v", "type": "uint256[]"}], "name": "EmitLog2", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "v", "type": "string[]"}], "name": "EmitLog3", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]