[{"inputs": [{"internalType": "address", "name": "vrfCoordinator", "type": "address"}, {"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "bytes32", "name": "keyHash", "type": "bytes32"}, {"internalType": "uint16", "name": "requestConfirmations", "type": "uint16"}, {"internalType": "uint256", "name": "upkeepInterval", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "have", "type": "address"}, {"internalType": "address", "name": "want", "type": "address"}], "name": "OnlyCoordinatorCanFulfill", "type": "error"}, {"inputs": [], "name": "COORDINATOR", "outputs": [{"internalType": "contract VRFCoordinatorV2Interface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "KEY_HASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REQUEST_CONFIRMATIONS", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "SUBSCRIPTION_ID", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPKEEP_INTERVAL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "upkeepNeeded", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint256[]", "name": "randomWords", "type": "uint256[]"}], "name": "rawFulfillRandomWords", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "s_lastTimeStamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "s_requests", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "bool", "name": "fulfilled", "type": "bool"}, {"internalType": "uint32", "name": "callbackGasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "randomness", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_vrfRequestCounter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_vrfResponseCounter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]