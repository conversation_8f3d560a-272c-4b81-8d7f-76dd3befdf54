[{"inputs": [{"internalType": "address", "name": "vrfCoordinator", "type": "address"}, {"internalType": "address", "name": "link", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "bytes32", "name": "keyHash", "type": "bytes32"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "doRequestRandomness", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "randomnessOutput", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "randomness", "type": "uint256"}], "name": "rawFulfillRandomness", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requestId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}]