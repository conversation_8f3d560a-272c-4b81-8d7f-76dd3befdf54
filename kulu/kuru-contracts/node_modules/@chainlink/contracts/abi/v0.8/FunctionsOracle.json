[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AlreadySet", "type": "error"}, {"inputs": [], "name": "EmptyBillingRegistry", "type": "error"}, {"inputs": [], "name": "EmptyPublicKey", "type": "error"}, {"inputs": [], "name": "EmptyRequestData", "type": "error"}, {"inputs": [], "name": "EmptySendersList", "type": "error"}, {"inputs": [], "name": "InconsistentReportData", "type": "error"}, {"inputs": [], "name": "InvalidRequestID", "type": "error"}, {"inputs": [], "name": "NotAllowedToSetSenders", "type": "error"}, {"inputs": [], "name": "ReportInvalid", "type": "error"}, {"inputs": [], "name": "UnauthorizedPublicKeyChange", "type": "error"}, {"inputs": [], "name": "UnauthorizedSender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "AuthorizedSendersActive", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "senders", "type": "address[]"}, {"indexed": false, "internalType": "address", "name": "changedBy", "type": "address"}], "name": "AuthorizedSenders<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "AuthorizedSendersDeactive", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "previousConfigBlockNumber", "type": "uint32"}, {"indexed": false, "internalType": "bytes32", "name": "configDigest", "type": "bytes32"}, {"indexed": false, "internalType": "uint64", "name": "configCount", "type": "uint64"}, {"indexed": false, "internalType": "address[]", "name": "signers", "type": "address[]"}, {"indexed": false, "internalType": "address[]", "name": "transmitters", "type": "address[]"}, {"indexed": false, "internalType": "uint8", "name": "f", "type": "uint8"}, {"indexed": false, "internalType": "bytes", "name": "onchainConfig", "type": "bytes"}, {"indexed": false, "internalType": "uint64", "name": "offchainConfigVersion", "type": "uint64"}, {"indexed": false, "internalType": "bytes", "name": "offchainConfig", "type": "bytes"}], "name": "ConfigSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "requestingContract", "type": "address"}, {"indexed": false, "internalType": "address", "name": "requestInitiator", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"indexed": false, "internalType": "address", "name": "subscriptionOwner", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "OracleRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "OracleResponse", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "configDigest", "type": "bytes32"}, {"indexed": false, "internalType": "uint32", "name": "epoch", "type": "uint32"}], "name": "Transmitted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "UserCallbackError", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes", "name": "lowLevelData", "type": "bytes"}], "name": "UserCallbackRawError", "type": "event"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "activateAuthorizedReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "senders", "type": "address[]"}], "name": "addAuthorizedSenders", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "authorizedReceiverActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deactivateAuthorizedReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "node", "type": "address"}], "name": "deleteNodePublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "name": "estimateCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllNodePublicKeys", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAuthorizedSenders", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDONPublicKey", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}, {"components": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "address", "name": "client", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "internalType": "struct FunctionsBillingRegistryInterface.RequestBilling", "name": "", "type": "tuple"}], "name": "getRequiredFee", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "isAuthorizedSender", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfigDetails", "outputs": [{"internalType": "uint32", "name": "configCount", "type": "uint32"}, {"internalType": "uint32", "name": "blockNumber", "type": "uint32"}, {"internalType": "bytes32", "name": "configDigest", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfigDigestAndEpoch", "outputs": [{"internalType": "bool", "name": "scanLogs", "type": "bool"}, {"internalType": "bytes32", "name": "configDigest", "type": "bytes32"}, {"internalType": "uint32", "name": "epoch", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "senders", "type": "address[]"}], "name": "removeAuthorizedSenders", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}], "name": "sendRequest", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_signers", "type": "address[]"}, {"internalType": "address[]", "name": "_transmitters", "type": "address[]"}, {"internalType": "uint8", "name": "_f", "type": "uint8"}, {"internalType": "bytes", "name": "_onchainConfig", "type": "bytes"}, {"internalType": "uint64", "name": "_offchainConfigVersion", "type": "uint64"}, {"internalType": "bytes", "name": "_offchainConfig", "type": "bytes"}], "name": "setConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "don<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes"}], "name": "setDONPublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "node", "type": "address"}, {"internalType": "bytes", "name": "public<PERSON>ey", "type": "bytes"}], "name": "setNodePublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "setRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32[3]", "name": "reportContext", "type": "bytes32[3]"}, {"internalType": "bytes", "name": "report", "type": "bytes"}, {"internalType": "bytes32[]", "name": "rs", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "ss", "type": "bytes32[]"}, {"internalType": "bytes32", "name": "rawVs", "type": "bytes32"}], "name": "transmit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "transmitters", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}]