[{"inputs": [{"internalType": "address", "name": "flagsContract", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "flags", "outputs": [{"internalType": "contract Flags", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}], "name": "getFlag", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "subjects", "type": "address[]"}], "name": "getFlags", "outputs": [{"internalType": "bool[]", "name": "", "type": "bool[]"}], "stateMutability": "view", "type": "function"}]