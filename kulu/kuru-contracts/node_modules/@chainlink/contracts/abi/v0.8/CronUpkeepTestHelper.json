[{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "delegate", "type": "address"}, {"internalType": "uint256", "name": "maxJobs", "type": "uint256"}, {"internalType": "bytes", "name": "first<PERSON>ob", "type": "bytes"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "CallFailed", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "CronJobIDNotFound", "type": "error"}, {"inputs": [], "name": "ExceedsMaxJobs", "type": "error"}, {"inputs": [{"internalType": "string", "name": "field", "type": "string"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "InvalidField", "type": "error"}, {"inputs": [], "name": "Invalid<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "string", "name": "reason", "type": "string"}], "name": "InvalidSpec", "type": "error"}, {"inputs": [], "name": "ListTooLarge", "type": "error"}, {"inputs": [], "name": "OnlySimulatedBackend", "type": "error"}, {"inputs": [], "name": "TickDoesntMatchSpec", "type": "error"}, {"inputs": [], "name": "TickInFuture", "type": "error"}, {"inputs": [], "name": "TickTooOld", "type": "error"}, {"inputs": [], "name": "UnknownFieldType", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "handler", "type": "bytes"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "CronJobDeleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "CronJobExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "handler", "type": "bytes"}], "name": "CronJobUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "handler", "type": "bytes"}, {"internalType": "bytes", "name": "encodedCronSpec", "type": "bytes"}], "name": "createCronJobFromEncodedSpec", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "handler", "type": "bytes"}, {"internalType": "string", "name": "cronString", "type": "string"}], "name": "createCronJobFromString", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "deleteCronJob", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActiveCronJobIDs", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getCronJob", "outputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "handler", "type": "bytes"}, {"internalType": "string", "name": "cronString", "type": "string"}, {"internalType": "uint256", "name": "nextTick", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "performData", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "s_maxJobs", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "checkData", "type": "bytes"}], "name": "txCheckUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "newTarget", "type": "address"}, {"internalType": "bytes", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "bytes", "name": "newEncodedCronSpec", "type": "bytes"}], "name": "updateCronJob", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]