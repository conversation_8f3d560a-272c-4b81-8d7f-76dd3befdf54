[{"inputs": [{"internalType": "address", "name": "erc20TokenAddress", "type": "address"}, {"internalType": "address", "name": "keeper<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "minWaitPeriodSeconds", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "duplicate", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "InvalidWatchList", "type": "error"}, {"inputs": [], "name": "Only<PERSON>eeperReg<PERSON><PERSON>", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "ERC20TokenAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amountWithdrawn", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "payee", "type": "address"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "KeeperRegistryAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldMinWaitPeriod", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newMinWaitPeriod", "type": "uint256"}], "name": "MinWaitPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "topUpAddress", "type": "address"}], "name": "TopUpSucceeded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "oldWatchlist", "type": "address[]"}, {"indexed": false, "internalType": "address[]", "name": "newWatchlist", "type": "address[]"}], "name": "WatchlistUpdated", "type": "event"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "upkeepNeeded", "type": "bool"}, {"internalType": "bytes", "name": "performData", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "targetAddress", "type": "address"}], "name": "getAccountInfo", "outputs": [{"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint96", "name": "minBalance", "type": "uint96"}, {"internalType": "uint96", "name": "topUpLevel", "type": "uint96"}, {"internalType": "uint56", "name": "lastTopUpTimestamp", "type": "uint56"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getERC20TokenAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMinWaitPeriodSeconds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnderfundedAddresses", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getWatchList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "performData", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "erc20TokenAddress", "type": "address"}], "name": "setERC20TokenAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "keeper<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "setMinWaitPeriodSeconds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}, {"internalType": "uint96[]", "name": "minBalances", "type": "uint96[]"}, {"internalType": "uint96[]", "name": "topUpLevels", "type": "uint96[]"}], "name": "setWatchList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "needsFunding", "type": "address[]"}], "name": "topUp", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "payee", "type": "address"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]