[{"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EmptySource", "type": "error"}, {"inputs": [], "name": "RequestIsAlreadyPending", "type": "error"}, {"inputs": [], "name": "RequestIsNotPending", "type": "error"}, {"inputs": [], "name": "SenderIsNotRegistry", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes", "name": "response", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "err", "type": "bytes"}], "name": "FulfillRequestInvoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "RequestFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "RequestSent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "sourceCode", "type": "string"}, {"indexed": false, "internalType": "uint64", "name": "subscriptionId", "type": "uint64"}], "name": "SendRequestInvoked", "type": "event"}, {"inputs": [{"components": [{"internalType": "enum Functions.Location", "name": "codeLocation", "type": "uint8"}, {"internalType": "enum Functions.Location", "name": "secretsLocation", "type": "uint8"}, {"internalType": "enum Functions.CodeLanguage", "name": "language", "type": "uint8"}, {"internalType": "string", "name": "source", "type": "string"}, {"internalType": "bytes", "name": "secrets", "type": "bytes"}, {"internalType": "string[]", "name": "args", "type": "string[]"}], "internalType": "struct Functions.Request", "name": "req", "type": "tuple"}, {"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "name": "estimateCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "sourceCode", "type": "string"}, {"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "uint256", "name": "gasCost", "type": "uint256"}], "name": "estimateJuelCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDONPublicKey", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "response", "type": "bytes"}, {"internalType": "bytes", "name": "err", "type": "bytes"}], "name": "handleOracleFulfillment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "sourceCode", "type": "string"}, {"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}], "name": "sendSimpleRequestWithJavaScript", "outputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "on", "type": "bool"}], "name": "setDoInvalidOperation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "on", "type": "bool"}], "name": "setRevertFulfillRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]