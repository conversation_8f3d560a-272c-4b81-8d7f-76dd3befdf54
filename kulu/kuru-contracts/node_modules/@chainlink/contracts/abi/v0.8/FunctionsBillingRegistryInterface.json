[{"inputs": [{"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}, {"internalType": "uint96", "name": "<PERSON><PERSON><PERSON>", "type": "uint96"}, {"internalType": "uint96", "name": "registryFee", "type": "uint96"}], "name": "estimateCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "response", "type": "bytes"}, {"internalType": "bytes", "name": "err", "type": "bytes"}, {"internalType": "address", "name": "transmitter", "type": "address"}, {"internalType": "address[31]", "name": "signers", "type": "address[31]"}, {"internalType": "uint8", "name": "signerCount", "type": "uint8"}, {"internalType": "uint256", "name": "reportValidationGas", "type": "uint256"}, {"internalType": "uint256", "name": "initialGas", "type": "uint256"}], "name": "fulfillAndBill", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getRequestConfig", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}, {"components": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "address", "name": "client", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "internalType": "struct FunctionsBillingRegistryInterface.RequestBilling", "name": "billing", "type": "tuple"}], "name": "getRequiredFee", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}], "name": "getSubscriptionOwner", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}, {"components": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "address", "name": "client", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "internalType": "struct FunctionsBillingRegistryInterface.RequestBilling", "name": "billing", "type": "tuple"}], "name": "startBilling", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}]