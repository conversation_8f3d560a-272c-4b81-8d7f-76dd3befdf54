[{"inputs": [{"internalType": "address", "name": "coordinator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "ErrorReturned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "lowLevelData", "type": "bytes"}], "name": "RawErrorReturned", "type": "event"}, {"inputs": [], "name": "COORDINATOR", "outputs": [{"internalType": "contract VRFCoordinatorV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256[2]", "name": "pk", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "gamma", "type": "uint256[2]"}, {"internalType": "uint256", "name": "c", "type": "uint256"}, {"internalType": "uint256", "name": "s", "type": "uint256"}, {"internalType": "uint256", "name": "seed", "type": "uint256"}, {"internalType": "address", "name": "uWitness", "type": "address"}, {"internalType": "uint256[2]", "name": "cGammaWitness", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "sHashWitness", "type": "uint256[2]"}, {"internalType": "uint256", "name": "zInv", "type": "uint256"}], "internalType": "struct VRFTypes.Proof[]", "name": "proofs", "type": "tuple[]"}, {"components": [{"internalType": "uint64", "name": "blockNum", "type": "uint64"}, {"internalType": "uint64", "name": "subId", "type": "uint64"}, {"internalType": "uint32", "name": "callbackGasLimit", "type": "uint32"}, {"internalType": "uint32", "name": "numWords", "type": "uint32"}, {"internalType": "address", "name": "sender", "type": "address"}], "internalType": "struct VRFTypes.RequestCommitment[]", "name": "rcs", "type": "tuple[]"}], "name": "fulfillRandomWords", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]