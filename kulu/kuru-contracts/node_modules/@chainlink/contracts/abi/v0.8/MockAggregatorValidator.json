[{"inputs": [{"internalType": "uint8", "name": "id_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "id", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "previousRoundId", "type": "uint256"}, {"indexed": false, "internalType": "int256", "name": "previousAnswer", "type": "int256"}, {"indexed": false, "internalType": "uint256", "name": "currentRoundId", "type": "uint256"}, {"indexed": false, "internalType": "int256", "name": "currentAnswer", "type": "int256"}], "name": "ValidateCalled", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "previousRoundId", "type": "uint256"}, {"internalType": "int256", "name": "previousAnswer", "type": "int256"}, {"internalType": "uint256", "name": "currentRoundId", "type": "uint256"}, {"internalType": "int256", "name": "currentAnswer", "type": "int256"}], "name": "validate", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}]