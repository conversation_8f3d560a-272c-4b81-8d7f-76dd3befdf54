[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes", "name": "payload", "type": "bytes"}], "name": "RequestData", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_key", "type": "string"}, {"internalType": "string", "name": "_value", "type": "string"}], "name": "add", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_key", "type": "string"}, {"internalType": "bytes", "name": "_value", "type": "bytes"}], "name": "addBytes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_key", "type": "string"}, {"internalType": "int256", "name": "_value", "type": "int256"}], "name": "addInt", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_key", "type": "string"}, {"internalType": "string[]", "name": "_values", "type": "string[]"}], "name": "addStringArray", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_key", "type": "string"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "addUint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "closeEvent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]