[{"inputs": [{"internalType": "uint32", "name": "_callbackGasLimit", "type": "uint32"}], "name": "calculateRequestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_callbackGasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "_requestGasPriceWei", "type": "uint256"}], "name": "estimateRequestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastRequestId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]