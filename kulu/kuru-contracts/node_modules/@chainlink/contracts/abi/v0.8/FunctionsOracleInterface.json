[{"inputs": [{"internalType": "address", "name": "node", "type": "address"}], "name": "deleteNodePublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "name": "estimateCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllNodePublicKeys", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDONPublicKey", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}, {"components": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "address", "name": "client", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "uint256", "name": "gasPrice", "type": "uint256"}], "internalType": "struct FunctionsBillingRegistryInterface.RequestBilling", "name": "billing", "type": "tuple"}], "name": "getRequiredFee", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "subscriptionId", "type": "uint64"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}], "name": "sendRequest", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "don<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes"}], "name": "setDONPublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "node", "type": "address"}, {"internalType": "bytes", "name": "public<PERSON>ey", "type": "bytes"}], "name": "setNodePublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "setRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]