[{"inputs": [], "name": "getDONPublicKey", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "response", "type": "bytes"}, {"internalType": "bytes", "name": "err", "type": "bytes"}], "name": "handleOracleFulfillment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]