[{"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "expiration", "type": "uint256"}, {"internalType": "bytes32", "name": "data", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "node", "type": "address"}], "name": "isAuthorizedSender", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]