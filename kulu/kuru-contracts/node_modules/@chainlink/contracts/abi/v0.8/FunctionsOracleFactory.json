[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "don", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "OracleCreated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "OracleAddress", "type": "address"}], "name": "created", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deployNewOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}]