[{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "msgHash", "type": "bytes32"}], "name": "FailedRelayedMessage", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "msgHash", "type": "bytes32"}], "name": "RelayedMessage", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes", "name": "message", "type": "bytes"}], "name": "SentMessage", "type": "event"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "_setMockMessageSender", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_target", "type": "address"}, {"internalType": "bytes", "name": "_message", "type": "bytes"}, {"internalType": "uint32", "name": "_gasLimit", "type": "uint32"}], "name": "sendMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "xDomainMessageSender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]