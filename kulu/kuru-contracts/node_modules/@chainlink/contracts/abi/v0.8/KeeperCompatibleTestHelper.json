[{"inputs": [], "name": "OnlySimulatedBackend", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "verifyCannotExecute", "outputs": [], "stateMutability": "view", "type": "function"}]