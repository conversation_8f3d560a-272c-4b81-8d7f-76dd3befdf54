[{"inputs": [{"internalType": "address", "name": "vrfCoordinator", "type": "address"}, {"internalType": "address", "name": "link", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "have", "type": "address"}, {"internalType": "address", "name": "want", "type": "address"}], "name": "OnlyCoordinatorCanFulfill", "type": "error"}, {"inputs": [{"internalType": "uint96", "name": "amount", "type": "uint96"}], "name": "createSubscriptionAndFund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint256[]", "name": "randomWords", "type": "uint256[]"}], "name": "rawFulfillRandomWords", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "keyHash", "type": "bytes32"}, {"internalType": "uint64", "name": "subId", "type": "uint64"}, {"internalType": "uint16", "name": "minReqConfs", "type": "uint16"}, {"internalType": "uint32", "name": "callbackGasLimit", "type": "uint32"}, {"internalType": "uint32", "name": "numWords", "type": "uint32"}], "name": "requestRandomness", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "s_gasAvailable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "s_randomWords", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_requestId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_subId", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint96", "name": "amount", "type": "uint96"}], "name": "topUpSubscription", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "consumers", "type": "address[]"}], "name": "updateSubscription", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]