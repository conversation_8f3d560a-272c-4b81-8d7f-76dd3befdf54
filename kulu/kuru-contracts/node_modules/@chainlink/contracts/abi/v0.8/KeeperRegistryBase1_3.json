[{"inputs": [], "name": "ArrayHasNoEntries", "type": "error"}, {"inputs": [], "name": "CannotCancel", "type": "error"}, {"inputs": [], "name": "DuplicateEntry", "type": "error"}, {"inputs": [], "name": "EmptyAddress", "type": "error"}, {"inputs": [], "name": "GasLimitCanOnlyIncrease", "type": "error"}, {"inputs": [], "name": "GasLimitOutsideRange", "type": "error"}, {"inputs": [], "name": "IndexOutOfRange", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidDataLength", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "InvalidRecipient", "type": "error"}, {"inputs": [], "name": "KeepersMustTakeTurns", "type": "error"}, {"inputs": [], "name": "MigrationNotPermitted", "type": "error"}, {"inputs": [], "name": "NotAContract", "type": "error"}, {"inputs": [], "name": "OnlyActiveKeepers", "type": "error"}, {"inputs": [], "name": "OnlyCallableByAdmin", "type": "error"}, {"inputs": [], "name": "OnlyCallableByLINKToken", "type": "error"}, {"inputs": [], "name": "OnlyCallableByOwnerOrAdmin", "type": "error"}, {"inputs": [], "name": "OnlyCallableByOwnerOrRegistrar", "type": "error"}, {"inputs": [], "name": "OnlyCallableByPayee", "type": "error"}, {"inputs": [], "name": "OnlyCallableByProposedAdmin", "type": "error"}, {"inputs": [], "name": "OnlyCallableByProposedPayee", "type": "error"}, {"inputs": [], "name": "OnlyPausedUpkeep", "type": "error"}, {"inputs": [], "name": "OnlySimulatedBackend", "type": "error"}, {"inputs": [], "name": "OnlyUnpausedUpkeep", "type": "error"}, {"inputs": [], "name": "ParameterLengthError", "type": "error"}, {"inputs": [], "name": "PaymentGreaterThanAllLINK", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "reason", "type": "bytes"}], "name": "TargetCheckReverted", "type": "error"}, {"inputs": [], "name": "TranscoderNotSet", "type": "error"}, {"inputs": [], "name": "UpkeepCancelled", "type": "error"}, {"inputs": [], "name": "UpkeepNotCanceled", "type": "error"}, {"inputs": [], "name": "UpkeepNotNeeded", "type": "error"}, {"inputs": [], "name": "ValueNotChanged", "type": "error"}, {"anonymous": false, "inputs": [{"components": [{"internalType": "uint32", "name": "paymentPremiumPPB", "type": "uint32"}, {"internalType": "uint32", "name": "flatFeeMicroLink", "type": "uint32"}, {"internalType": "uint24", "name": "blockCountPerTurn", "type": "uint24"}, {"internalType": "uint32", "name": "checkGasLimit", "type": "uint32"}, {"internalType": "uint24", "name": "stalenessSeconds", "type": "uint24"}, {"internalType": "uint16", "name": "gasCeilingMultiplier", "type": "uint16"}, {"internalType": "uint96", "name": "minUpkeepSpend", "type": "uint96"}, {"internalType": "uint32", "name": "maxPerformGas", "type": "uint32"}, {"internalType": "uint256", "name": "fallbackGasPrice", "type": "uint256"}, {"internalType": "uint256", "name": "fallbackLinkPrice", "type": "uint256"}, {"internalType": "address", "name": "transcoder", "type": "address"}, {"internalType": "address", "name": "registrar", "type": "address"}], "indexed": false, "internalType": "struct Config", "name": "config", "type": "tuple"}], "name": "ConfigSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint96", "name": "amount", "type": "uint96"}], "name": "FundsAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "to", "type": "address"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "keepers", "type": "address[]"}, {"indexed": false, "internalType": "address[]", "name": "payees", "type": "address[]"}], "name": "KeepersUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint96", "name": "amount", "type": "uint96"}], "name": "OwnerFundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "keeper", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "PayeeshipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "keeper", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "PayeeshipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "keeper", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "address", "name": "payee", "type": "address"}], "name": "PaymentWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "UpkeepAdminTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "UpkeepAdminTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "uint64", "name": "atBlockHeight", "type": "uint64"}], "name": "UpkeepCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "newCheckData", "type": "bytes"}], "name": "UpkeepCheckDataUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint96", "name": "gasLimit", "type": "uint96"}], "name": "UpkeepGasLimitSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "remainingBalance", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "destination", "type": "address"}], "name": "UpkeepMigrated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "UpkeepPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "bool", "name": "success", "type": "bool"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint96", "name": "payment", "type": "uint96"}, {"indexed": false, "internalType": "bytes", "name": "performData", "type": "bytes"}], "name": "UpkeepPerformed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "startingBalance", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "importedFrom", "type": "address"}], "name": "UpkeepReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint32", "name": "executeGas", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "admin", "type": "address"}], "name": "UpkeepRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "UpkeepUnpaused", "type": "event"}, {"inputs": [], "name": "ARB_NITRO_ORACLE", "outputs": [{"internalType": "contract ArbGasInfo", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FAST_GAS_FEED", "outputs": [{"internalType": "contract AggregatorV3Interface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LINK", "outputs": [{"internalType": "contract LinkTokenInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LINK_ETH_FEED", "outputs": [{"internalType": "contract AggregatorV3Interface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "OPTIMISM_ORACLE", "outputs": [{"internalType": "contract OVM_GasPriceOracle", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAYMENT_MODEL", "outputs": [{"internalType": "enum KeeperRegistryBase1_3.PaymentModel", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REGISTRY_GAS_OVERHEAD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]