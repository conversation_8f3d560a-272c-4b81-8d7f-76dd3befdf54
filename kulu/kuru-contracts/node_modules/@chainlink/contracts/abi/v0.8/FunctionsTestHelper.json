[{"inputs": [], "name": "EmptyArgs", "type": "error"}, {"inputs": [], "name": "EmptySecrets", "type": "error"}, {"inputs": [], "name": "EmptySource", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "RequestData", "type": "event"}, {"inputs": [], "name": "addEmptyArgs", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "secrets", "type": "bytes"}], "name": "addSecrets", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "arg1", "type": "string"}, {"internalType": "string", "name": "arg2", "type": "string"}], "name": "addTwoArgs", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "closeEvent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "sourceCode", "type": "string"}], "name": "initializeRequestForInlineJavaScript", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]