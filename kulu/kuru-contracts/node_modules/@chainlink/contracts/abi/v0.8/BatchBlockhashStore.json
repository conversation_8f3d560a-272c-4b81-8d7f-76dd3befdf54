[{"inputs": [{"internalType": "address", "name": "blockhashStoreAddr", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "BHS", "outputs": [{"internalType": "contract BlockhashStore", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "blockNumbers", "type": "uint256[]"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "blockNumbers", "type": "uint256[]"}], "name": "store", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "blockNumbers", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "headers", "type": "bytes[]"}], "name": "storeVerifyHeader", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]