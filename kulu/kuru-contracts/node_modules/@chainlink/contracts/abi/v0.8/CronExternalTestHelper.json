[{"inputs": [{"internalType": "string", "name": "cronString", "type": "string"}], "name": "calculateLastTick", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "cronString", "type": "string"}], "name": "calculateNextTick", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "cronString", "type": "string"}], "name": "encodeCronString", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "encodedSpec", "type": "bytes"}], "name": "encodedSpecToString", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}]