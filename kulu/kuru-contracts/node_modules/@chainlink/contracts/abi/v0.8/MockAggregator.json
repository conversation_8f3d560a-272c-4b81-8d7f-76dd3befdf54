[{"inputs": [], "name": "latestAnswer", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "s_answer", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "answer", "type": "int256"}], "name": "setLatestAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]