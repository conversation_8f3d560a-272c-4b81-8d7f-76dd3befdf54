[{"inputs": [{"internalType": "address", "name": "_link", "type": "address"}, {"internalType": "address", "name": "_oracle", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "LinkAmount", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "id", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "bytes4", "name": "callbackfunctionSelector", "type": "bytes4"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "Request", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "fulfillRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}], "name": "publicAddExternalRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes4", "name": "_callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "_expiration", "type": "uint256"}], "name": "publicCancelRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "publicChainlinkToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "publicFulfillChainlinkRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "publicLINK", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_id", "type": "bytes32"}, {"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "bytes", "name": "_fulfillmentSignature", "type": "bytes"}], "name": "publicNewRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "publicOracleAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_id", "type": "bytes32"}, {"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "bytes", "name": "_fulfillmentSignature", "type": "bytes"}, {"internalType": "uint256", "name": "_wei", "type": "uint256"}], "name": "publicRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_id", "type": "bytes32"}, {"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "bytes", "name": "_fulfillmentSignature", "type": "bytes"}, {"internalType": "uint256", "name": "_wei", "type": "uint256"}], "name": "publicRequestRunTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]