[{"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}, {"internalType": "uint256", "name": "_roundID", "type": "uint256"}], "name": "readGetAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}, {"internalType": "uint80", "name": "_roundID", "type": "uint80"}], "name": "readGetRoundData", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}, {"internalType": "uint256", "name": "_roundID", "type": "uint256"}], "name": "readGetTimestamp", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}], "name": "readLatestAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}], "name": "readLatestRound", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}], "name": "readLatestRoundData", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}], "name": "readLatestTimestamp", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}, {"internalType": "address", "name": "_oracle", "type": "address"}], "name": "readOracleRoundState", "outputs": [{"internalType": "uint80", "name": "roundId", "type": "uint80"}, {"internalType": "int256", "name": "answer", "type": "int256"}, {"internalType": "uint256", "name": "startedAt", "type": "uint256"}, {"internalType": "uint256", "name": "updatedAt", "type": "uint256"}, {"internalType": "uint80", "name": "answeredInRound", "type": "uint80"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_aggregator", "type": "address"}], "name": "requestNewRound", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requestedRoundId", "outputs": [{"internalType": "uint80", "name": "", "type": "uint80"}], "stateMutability": "view", "type": "function"}]