[{"inputs": [{"internalType": "address", "name": "_link", "type": "address"}, {"internalType": "address", "name": "_oracle", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "assertFail", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "cancelRequestOnFulfill", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "doesNothing", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "remove", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_id", "type": "bytes32"}, {"internalType": "bytes", "name": "_callbackFunc", "type": "bytes"}], "name": "requestData", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "stealEthCall", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "stealEthSend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "stealEthTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]