[{"inputs": [{"internalType": "address", "name": "_flags", "type": "address"}, {"internalType": "uint24", "name": "_flaggingT<PERSON><PERSON>old", "type": "uint24"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint24", "name": "previous", "type": "uint24"}, {"indexed": true, "internalType": "uint24", "name": "current", "type": "uint24"}], "name": "FlaggingThresholdUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previous", "type": "address"}, {"indexed": true, "internalType": "address", "name": "current", "type": "address"}], "name": "FlagsAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "THRESHOLD_MULTIPLIER", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "flagging<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "flags", "outputs": [{"internalType": "contract FlagsInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "int256", "name": "_previousAnswer", "type": "int256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "int256", "name": "_answer", "type": "int256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "_flaggingT<PERSON><PERSON>old", "type": "uint24"}], "name": "setFlaggingThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_flags", "type": "address"}], "name": "setFlagsAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_previousRoundId", "type": "uint256"}, {"internalType": "int256", "name": "_previousAnswer", "type": "int256"}, {"internalType": "uint256", "name": "_roundId", "type": "uint256"}, {"internalType": "int256", "name": "_answer", "type": "int256"}], "name": "validate", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}]