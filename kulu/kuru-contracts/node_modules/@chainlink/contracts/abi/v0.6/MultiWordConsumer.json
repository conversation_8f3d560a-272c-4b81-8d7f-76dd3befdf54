[{"inputs": [{"internalType": "address", "name": "_link", "type": "address"}, {"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_specId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes", "name": "price", "type": "bytes"}], "name": "RequestFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "first", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "second", "type": "bytes32"}], "name": "RequestMultipleFulfilled", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}], "name": "addExternalRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes4", "name": "_callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "_expiration", "type": "uint256"}], "name": "cancelRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentPrice", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "first", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "_price", "type": "bytes"}], "name": "fulfillBytes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "_first", "type": "bytes32"}, {"internalType": "bytes32", "name": "_second", "type": "bytes32"}], "name": "fulfillMultipleParameters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestEthereumPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "address", "name": "_callback", "type": "address"}], "name": "requestEthereumPriceByCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestMultipleParameters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "second", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawLink", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]