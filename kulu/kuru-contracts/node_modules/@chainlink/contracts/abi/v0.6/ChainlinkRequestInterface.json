[{"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "requestPrice", "type": "uint256"}, {"internalType": "bytes32", "name": "serviceAgreementID", "type": "bytes32"}, {"internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "dataVersion", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]