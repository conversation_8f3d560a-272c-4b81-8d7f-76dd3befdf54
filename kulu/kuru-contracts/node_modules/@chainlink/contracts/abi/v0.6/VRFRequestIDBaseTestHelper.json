[{"inputs": [{"internalType": "bytes32", "name": "_keyHash", "type": "bytes32"}, {"internalType": "uint256", "name": "_vRFInputSeed", "type": "uint256"}], "name": "makeRequestId_", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_keyHash", "type": "bytes32"}, {"internalType": "uint256", "name": "_userSeed", "type": "uint256"}, {"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "uint256", "name": "_nonce", "type": "uint256"}], "name": "makeVRFInputSeed_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}]