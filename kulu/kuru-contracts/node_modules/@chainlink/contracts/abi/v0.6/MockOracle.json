[{"inputs": [{"internalType": "address", "name": "_link", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "CancelOracleRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "specId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "requester", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "payment", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "callbackAddr", "type": "address"}, {"indexed": false, "internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"indexed": false, "internalType": "uint256", "name": "cancelExpiration", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "dataVersion", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "OracleRequest", "type": "event"}, {"inputs": [], "name": "EXPIRY_TIME", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes4", "name": "", "type": "bytes4"}, {"internalType": "uint256", "name": "_expiration", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "_data", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getChainlinkToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sender", "type": "address"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes32", "name": "_specId", "type": "bytes32"}, {"internalType": "address", "name": "_callback<PERSON><PERSON>ress", "type": "address"}, {"internalType": "bytes4", "name": "_callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "_nonce", "type": "uint256"}, {"internalType": "uint256", "name": "_dataVersion", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]