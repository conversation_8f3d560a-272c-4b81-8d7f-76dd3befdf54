[{"inputs": [{"internalType": "uint256", "name": "n", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "n", "type": "uint256"}, {"internalType": "bytes32", "name": "h", "type": "bytes32"}], "name": "godmodeSetHash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "n", "type": "uint256"}], "name": "store", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "storeEarliest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "n", "type": "uint256"}, {"internalType": "bytes", "name": "header", "type": "bytes"}], "name": "storeVerifyHeader", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]