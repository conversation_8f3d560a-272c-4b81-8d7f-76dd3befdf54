[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "price", "type": "bytes32"}], "name": "RequestFulfilled", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}], "name": "addExternalRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes4", "name": "_callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "_expiration", "type": "uint256"}], "name": "cancelRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentPrice", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "_price", "type": "bytes32"}], "name": "fulfill", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestEthereumPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "address", "name": "_callback", "type": "address"}], "name": "requestEthereumPriceByCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawLink", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]