[{"inputs": [{"internalType": "int256", "name": "a", "type": "int256"}, {"internalType": "int256", "name": "b", "type": "int256"}], "name": "add", "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}, {"internalType": "bool", "name": "ok", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "a", "type": "int256"}, {"internalType": "int256", "name": "b", "type": "int256"}], "name": "div", "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}, {"internalType": "bool", "name": "ok", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "a", "type": "int256"}, {"internalType": "int256", "name": "b", "type": "int256"}], "name": "mul", "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}, {"internalType": "bool", "name": "ok", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "a", "type": "int256"}, {"internalType": "int256", "name": "b", "type": "int256"}], "name": "sub", "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}, {"internalType": "bool", "name": "ok", "type": "bool"}], "stateMutability": "pure", "type": "function"}]