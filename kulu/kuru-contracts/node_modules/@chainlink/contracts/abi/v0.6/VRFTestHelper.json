[{"inputs": [], "name": "PROOF_LENGTH", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[2]", "name": "p1", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "p2", "type": "uint256[2]"}, {"internalType": "uint256", "name": "invZ", "type": "uint256"}], "name": "affineECAdd_", "outputs": [{"internalType": "uint256[2]", "name": "", "type": "uint256[2]"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "base", "type": "uint256"}, {"internalType": "uint256", "name": "exponent", "type": "uint256"}], "name": "bigModExp_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[2]", "name": "x", "type": "uint256[2]"}, {"internalType": "uint256", "name": "scalar", "type": "uint256"}, {"internalType": "uint256[2]", "name": "q", "type": "uint256[2]"}], "name": "ecmulVerify_", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "b", "type": "bytes"}], "name": "fieldHash_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[2]", "name": "pk", "type": "uint256[2]"}, {"internalType": "uint256", "name": "x", "type": "uint256"}], "name": "hashToCurve_", "outputs": [{"internalType": "uint256[2]", "name": "", "type": "uint256[2]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "c", "type": "uint256"}, {"internalType": "uint256[2]", "name": "p1", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "cp1Witness", "type": "uint256[2]"}, {"internalType": "uint256", "name": "s", "type": "uint256"}, {"internalType": "uint256[2]", "name": "p2", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "sp2Witness", "type": "uint256[2]"}, {"internalType": "uint256", "name": "zInv", "type": "uint256"}], "name": "linearCombination_", "outputs": [{"internalType": "uint256[2]", "name": "", "type": "uint256[2]"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "px", "type": "uint256"}, {"internalType": "uint256", "name": "py", "type": "uint256"}, {"internalType": "uint256", "name": "qx", "type": "uint256"}, {"internalType": "uint256", "name": "qy", "type": "uint256"}], "name": "projectiveECAdd_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "randomValueFromVRFProof_", "outputs": [{"internalType": "uint256", "name": "output", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[2]", "name": "hash", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "pk", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "gamma", "type": "uint256[2]"}, {"internalType": "address", "name": "uWitness", "type": "address"}, {"internalType": "uint256[2]", "name": "v", "type": "uint256[2]"}], "name": "scalarFromCurvePoints_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "x", "type": "uint256"}], "name": "squareRoot_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "c", "type": "uint256"}, {"internalType": "uint256[2]", "name": "p", "type": "uint256[2]"}, {"internalType": "uint256", "name": "s", "type": "uint256"}, {"internalType": "address", "name": "lc<PERSON><PERSON><PERSON>", "type": "address"}], "name": "verifyLinearCombinationWithGenerator_", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[2]", "name": "pk", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "gamma", "type": "uint256[2]"}, {"internalType": "uint256", "name": "c", "type": "uint256"}, {"internalType": "uint256", "name": "s", "type": "uint256"}, {"internalType": "uint256", "name": "seed", "type": "uint256"}, {"internalType": "address", "name": "uWitness", "type": "address"}, {"internalType": "uint256[2]", "name": "cGammaWitness", "type": "uint256[2]"}, {"internalType": "uint256[2]", "name": "sHashWitness", "type": "uint256[2]"}, {"internalType": "uint256", "name": "zInv", "type": "uint256"}], "name": "verifyVRFProof_", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "x", "type": "uint256"}], "name": "ySquared_", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}]