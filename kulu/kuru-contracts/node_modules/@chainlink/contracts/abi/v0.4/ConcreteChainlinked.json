[{"constant": false, "inputs": [{"name": "_amount", "type": "uint256"}], "name": "publicLINK", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_payment", "type": "uint256"}, {"name": "_callbackFunctionId", "type": "bytes4"}, {"name": "_expiration", "type": "uint256"}], "name": "publicCancelRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_oracle", "type": "address"}, {"name": "_requestId", "type": "bytes32"}], "name": "publicAddExternalRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "fulfillRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_id", "type": "bytes32"}, {"name": "_address", "type": "address"}, {"name": "_fulfillmentSignature", "type": "bytes"}, {"name": "_wei", "type": "uint256"}], "name": "publicRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "publicChainlinkToken", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_oracle", "type": "address"}, {"name": "_id", "type": "bytes32"}, {"name": "_address", "type": "address"}, {"name": "_fulfillmentSignature", "type": "bytes"}, {"name": "_wei", "type": "uint256"}], "name": "publicRequestRunTo", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "publicFulfillChainlinkRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_id", "type": "bytes32"}, {"name": "_address", "type": "address"}, {"name": "_fulfillmentSignature", "type": "bytes"}], "name": "publicNewRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "publicOracleAddress", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "_link", "type": "address"}, {"name": "_oracle", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "id", "type": "bytes32"}, {"indexed": false, "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "name": "callbackfunctionSelector", "type": "bytes4"}, {"indexed": false, "name": "data", "type": "bytes"}], "name": "Request", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "amount", "type": "uint256"}], "name": "LinkAmount", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}]