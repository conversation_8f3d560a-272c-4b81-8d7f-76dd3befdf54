[{"constant": false, "inputs": [{"name": "", "type": "address"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes32"}, {"name": "", "type": "address"}, {"name": "", "type": "bytes4"}, {"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "", "type": "bytes32"}, {"name": "", "type": "uint256"}, {"name": "", "type": "address"}, {"name": "", "type": "bytes4"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "withdrawable", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "", "type": "bytes32"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes4"}, {"name": "", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "", "type": "address"}, {"name": "", "type": "bool"}], "name": "setFulfillmentPermission", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "address"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "address"}], "name": "getAuthorizationStatus", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "", "type": "address"}, {"name": "", "type": "uint256"}], "name": "withdraw", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]