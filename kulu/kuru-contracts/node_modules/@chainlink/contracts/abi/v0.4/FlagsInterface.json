[{"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "getFlag", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "name": "getFlags", "outputs": [{"internalType": "bool[]", "name": "", "type": "bool[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "name": "lowerFlags", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "raiseFlag", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "name": "raiseFlags", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "setRaisingAccessController", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]