[{"constant": false, "inputs": [{"name": "requestId", "type": "bytes32"}, {"name": "payment", "type": "uint256"}, {"name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"name": "callbackFunctionId", "type": "bytes4"}, {"name": "expiration", "type": "uint256"}, {"name": "data", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "withdrawable", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "address"}, {"name": "allowed", "type": "bool"}], "name": "setFulfillmentPermission", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "address"}], "name": "getAuthorizationStatus", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]