[{"constant": false, "inputs": [{"name": "sender", "type": "address"}, {"name": "payment", "type": "uint256"}, {"name": "id", "type": "bytes32"}, {"name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"name": "callbackFunctionId", "type": "bytes4"}, {"name": "nonce", "type": "uint256"}, {"name": "version", "type": "uint256"}, {"name": "data", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "requestId", "type": "bytes32"}, {"name": "payment", "type": "uint256"}, {"name": "callbackFunctionId", "type": "bytes4"}, {"name": "expiration", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]