[{"inputs": [{"internalType": "address", "name": "LINKAddress", "type": "address"}, {"internalType": "uint256", "name": "minimumLINKJuels", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}, {"indexed": false, "internalType": "uint32", "name": "windowSizeInBlocks", "type": "uint32"}, {"indexed": false, "internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "keeper<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "minLINKJuels", "type": "uint256"}], "name": "Config<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "displayName", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "upkeepId", "type": "uint256"}], "name": "RegistrationApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "hash", "type": "bytes32"}], "name": "RegistrationRejected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "encryptedEmail", "type": "bytes"}, {"indexed": true, "internalType": "address", "name": "upkeepContract", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "admin<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "checkData", "type": "bytes"}, {"indexed": false, "internalType": "uint96", "name": "amount", "type": "uint96"}, {"indexed": true, "internalType": "uint8", "name": "source", "type": "uint8"}], "name": "RegistrationRequested", "type": "event"}, {"inputs": [], "name": "LINK", "outputs": [{"internalType": "contract LinkTokenInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "upkeepContract", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "address", "name": "admin<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes", "name": "checkData", "type": "bytes"}, {"internalType": "bytes32", "name": "hash", "type": "bytes32"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}], "name": "cancel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}], "name": "getPendingRequest", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRegistrationConfig", "outputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "uint32", "name": "windowSizeInBlocks", "type": "uint32"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "address", "name": "keeper<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "minLINKJuels", "type": "uint256"}, {"internalType": "uint64", "name": "windowStart", "type": "uint64"}, {"internalType": "uint16", "name": "approvedInCurrentWindow", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bytes", "name": "encryptedEmail", "type": "bytes"}, {"internalType": "address", "name": "upkeepContract", "type": "address"}, {"internalType": "uint32", "name": "gasLimit", "type": "uint32"}, {"internalType": "address", "name": "admin<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes", "name": "checkData", "type": "bytes"}, {"internalType": "uint96", "name": "amount", "type": "uint96"}, {"internalType": "uint8", "name": "source", "type": "uint8"}], "name": "register", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "uint32", "name": "windowSizeInBlocks", "type": "uint32"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "address", "name": "keeper<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "minLINKJuels", "type": "uint256"}], "name": "setRegistrationConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "typeAndVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}]