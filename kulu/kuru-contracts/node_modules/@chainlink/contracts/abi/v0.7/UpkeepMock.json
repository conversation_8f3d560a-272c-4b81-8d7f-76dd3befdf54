[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes", "name": "upkeepData", "type": "bytes"}], "name": "UpkeepPerformedWith", "type": "event"}, {"inputs": [], "name": "can<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "canPerform", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkGasToBurn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "callable", "type": "bool"}, {"internalType": "bytes", "name": "<PERSON><PERSON>", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "performData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "performGasToBurn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "value", "type": "bool"}], "name": "setCanCheck", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "value", "type": "bool"}], "name": "setCanPerform", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "setCheckGasToBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "setPerformData", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "setPerformGasToBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "value", "type": "bool"}], "name": "setShouldRevertCheck", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shouldRevertCheck", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}]