[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "senders", "type": "address[]"}, {"indexed": false, "internalType": "address", "name": "changedBy", "type": "address"}], "name": "AuthorizedSenders<PERSON><PERSON>ed", "type": "event"}, {"inputs": [], "name": "getAuthorizedSenders", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "isAuthorizedSender", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "senders", "type": "address[]"}], "name": "setAuthorizedSenders", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]