[{"inputs": [{"internalType": "uint256", "name": "_testRange", "type": "uint256"}, {"internalType": "uint256", "name": "_interval", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBlock", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "lastBlock", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "previousBlock", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "counter", "type": "uint256"}], "name": "PerformingUpkeep", "type": "event"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "counter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eligible", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "interval", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "performData", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "previousPerformBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_testRange", "type": "uint256"}, {"internalType": "uint256", "name": "_interval", "type": "uint256"}], "name": "setSpread", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "testRange", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]