[{"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "newPrice", "type": "uint256"}, {"internalType": "uint256", "name": "newDecimals", "type": "uint256"}], "name": "setPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]