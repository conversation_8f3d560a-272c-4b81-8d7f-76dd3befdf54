[{"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "expiration", "type": "uint256"}, {"internalType": "bytes32", "name": "data", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "expiration", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "fulfillOracleRequest2", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "payment", "type": "uint256"}, {"internalType": "bytes32", "name": "specId", "type": "bytes32"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "dataVersion", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "operatorRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "requestPrice", "type": "uint256"}, {"internalType": "bytes32", "name": "serviceAgreementID", "type": "bytes32"}, {"internalType": "address", "name": "callback<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes4", "name": "callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "dataVersion", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "ownerTransferAndCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]