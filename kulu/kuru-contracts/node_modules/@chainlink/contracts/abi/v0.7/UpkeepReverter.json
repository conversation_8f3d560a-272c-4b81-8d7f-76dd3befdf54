[{"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "checkUpkeep", "outputs": [{"internalType": "bool", "name": "callable", "type": "bool"}, {"internalType": "bytes", "name": "<PERSON><PERSON>", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "performUpkeep", "outputs": [], "stateMutability": "pure", "type": "function"}]