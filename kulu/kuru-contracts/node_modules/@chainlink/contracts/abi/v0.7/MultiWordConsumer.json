[{"inputs": [{"internalType": "address", "name": "_link", "type": "address"}, {"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_specId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes", "name": "price", "type": "bytes"}], "name": "RequestFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "usd", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "eur", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "jpy", "type": "bytes32"}], "name": "RequestMultipleFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "usd", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "eur", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "jpy", "type": "uint256"}], "name": "RequestMultipleFulfilledWithCustomURLs", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}], "name": "addExternalRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle", "type": "address"}, {"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}, {"internalType": "bytes4", "name": "_callbackFunctionId", "type": "bytes4"}, {"internalType": "uint256", "name": "_expiration", "type": "uint256"}], "name": "cancelRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentPrice", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eur", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eurInt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes", "name": "_price", "type": "bytes"}], "name": "fulfillBytes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "bytes32", "name": "_usd", "type": "bytes32"}, {"internalType": "bytes32", "name": "_eur", "type": "bytes32"}, {"internalType": "bytes32", "name": "_jpy", "type": "bytes32"}], "name": "fulfillMultipleParameters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_requestId", "type": "bytes32"}, {"internalType": "uint256", "name": "_usd", "type": "uint256"}, {"internalType": "uint256", "name": "_eur", "type": "uint256"}, {"internalType": "uint256", "name": "_jpy", "type": "uint256"}], "name": "fulfillMultipleParametersWithCustomURLs", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "jpy", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "jpyInt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "publicGetNextRequestCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestEthereumPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currency", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestMultipleParameters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_urlUSD", "type": "string"}, {"internalType": "string", "name": "_pathUSD", "type": "string"}, {"internalType": "string", "name": "_urlEUR", "type": "string"}, {"internalType": "string", "name": "_pathEUR", "type": "string"}, {"internalType": "string", "name": "_urlJPY", "type": "string"}, {"internalType": "string", "name": "_pathJPY", "type": "string"}, {"internalType": "uint256", "name": "_payment", "type": "uint256"}], "name": "requestMultipleParametersWithCustomURLs", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_specId", "type": "bytes32"}], "name": "setSpecID", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usd", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "usdInt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawLink", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]