[{"constant": true, "inputs": [], "name": "getChainlinkToken", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_sender", "type": "address"}, {"name": "_amount", "type": "uint256"}, {"name": "_data", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]