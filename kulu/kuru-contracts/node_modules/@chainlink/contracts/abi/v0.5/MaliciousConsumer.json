[{"constant": true, "inputs": [{"name": "", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "doesNothing", "outputs": [], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "stealEthCall", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "remove", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "stealEthSend", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "cancelRequestOnFulfill", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "stealEthTransfer", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_id", "type": "bytes32"}, {"name": "_callbackFunc", "type": "bytes"}], "name": "requestData", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "bytes32"}, {"name": "", "type": "bytes32"}], "name": "assertFail", "outputs": [], "payable": false, "stateMutability": "pure", "type": "function"}, {"inputs": [{"name": "_link", "type": "address"}, {"name": "_oracle", "type": "address"}], "payable": true, "stateMutability": "payable", "type": "constructor"}, {"payable": true, "stateMutability": "payable", "type": "fallback"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "bytes32"}], "name": "ChainlinkCancelled", "type": "event"}]