[{"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_aggregatorArgs", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_serviceAgreementData", "type": "bytes"}, {"name": "_oracleSignaturesData", "type": "bytes"}], "name": "initiateServiceAgreement", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}]