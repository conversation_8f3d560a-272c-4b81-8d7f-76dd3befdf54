[{"constant": true, "inputs": [], "name": "HALF_Q", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "Q", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "signingPubKeyX", "type": "uint256"}, {"name": "pubKeyYParity", "type": "uint8"}, {"name": "signature", "type": "uint256"}, {"name": "msgHash", "type": "uint256"}, {"name": "nonceTimesGeneratorAddress", "type": "address"}], "name": "verifySignature", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "pure", "type": "function"}]