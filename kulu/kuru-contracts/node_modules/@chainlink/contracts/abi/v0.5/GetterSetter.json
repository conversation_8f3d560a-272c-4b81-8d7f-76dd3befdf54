[{"constant": true, "inputs": [], "name": "requestId", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getBytes", "outputs": [{"name": "", "type": "bytes"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getBytes32", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_value", "type": "uint256"}], "name": "requestedUint256", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_value", "type": "bytes"}], "name": "requestedBytes", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getUint256", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_value", "type": "bytes32"}], "name": "setBytes32", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_value", "type": "uint256"}], "name": "setUint256", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_value", "type": "bytes"}], "name": "setBytes", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_value", "type": "bytes32"}], "name": "requestedBytes32", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "value", "type": "bytes32"}], "name": "SetBytes32", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "value", "type": "uint256"}], "name": "SetUint256", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": false, "name": "value", "type": "bytes"}], "name": "SetBytes", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "b32", "type": "bytes32"}, {"indexed": false, "name": "u256", "type": "uint256"}, {"indexed": false, "name": "b322", "type": "bytes32"}], "name": "Output", "type": "event"}]