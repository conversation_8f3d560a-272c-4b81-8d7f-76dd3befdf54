[{"constant": true, "inputs": [{"name": "", "type": "address"}], "name": "withdrawableTokens", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_requestId", "type": "bytes32"}, {"name": "_data", "type": "bytes32"}], "name": "fulfillOracleRequest", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_sender", "type": "address"}, {"name": "_amount", "type": "uint256"}, {"name": "_sAId", "type": "bytes32"}, {"name": "_callback<PERSON><PERSON>ress", "type": "address"}, {"name": "_callbackFunctionId", "type": "bytes4"}, {"name": "_nonce", "type": "uint256"}, {"name": "_dataVersion", "type": "uint256"}, {"name": "_data", "type": "bytes"}], "name": "oracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "EXPIRY_TIME", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "_agreementData", "type": "bytes"}], "name": "getId", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": false, "inputs": [{"name": "", "type": "bytes32"}, {"name": "", "type": "uint256"}, {"name": "", "type": "bytes4"}, {"name": "", "type": "uint256"}], "name": "cancelOracleRequest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "_account", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "bytes32"}], "name": "serviceAgreements", "outputs": [{"name": "payment", "type": "uint256"}, {"name": "expiration", "type": "uint256"}, {"name": "endAt", "type": "uint256"}, {"name": "requestDigest", "type": "bytes32"}, {"name": "aggregator", "type": "address"}, {"name": "aggInitiateJobSelector", "type": "bytes4"}, {"name": "aggFulfillSelector", "type": "bytes4"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_sender", "type": "address"}, {"name": "_amount", "type": "uint256"}], "name": "depositFunds", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_sender", "type": "address"}, {"name": "_amount", "type": "uint256"}, {"name": "_data", "type": "bytes"}], "name": "onTokenTransfer", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_serviceAgreementData", "type": "bytes"}, {"name": "_oracleSignaturesData", "type": "bytes"}], "name": "initiateServiceAgreement", "outputs": [{"name": "serviceAgreementID", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "_recipient", "type": "address"}, {"name": "_amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_link", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "sAId", "type": "bytes32"}, {"indexed": false, "name": "requester", "type": "address"}, {"indexed": false, "name": "requestId", "type": "bytes32"}, {"indexed": false, "name": "payment", "type": "uint256"}, {"indexed": false, "name": "callbackAddr", "type": "address"}, {"indexed": false, "name": "callbackFunctionId", "type": "bytes4"}, {"indexed": false, "name": "cancelExpiration", "type": "uint256"}, {"indexed": false, "name": "dataVersion", "type": "uint256"}, {"indexed": false, "name": "data", "type": "bytes"}], "name": "OracleRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "said", "type": "bytes32"}, {"indexed": true, "name": "requestDigest", "type": "bytes32"}], "name": "NewServiceAgreement", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "internalId", "type": "bytes32"}], "name": "CancelOracleRequest", "type": "event"}]