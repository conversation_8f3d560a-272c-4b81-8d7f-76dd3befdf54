# Generator

## What is generator

**[generator-napi-module](https://www.npmjs.com/package/generator-napi-module)** is a module to quickly generate a skeleton module using
**N-API**, the new API for Native addons. This module automatically sets up your
**gyp file** to use **node-addon-api**, the C++ wrappers for N-API and generates
a wrapper JS module. Optionally, it can even configure the generated project to
use **TypeScript** instead.

## **generator-napi-module** reference

  - [Installation and usage](https://www.npmjs.com/package/generator-napi-module#installation)
