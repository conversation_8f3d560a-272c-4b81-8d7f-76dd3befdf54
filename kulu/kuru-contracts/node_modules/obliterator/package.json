{"name": "obliterator", "version": "2.0.4", "description": "Higher order iterator library for JavaScript/TypeScript.", "main": "index.js", "types": "index.d.ts", "scripts": {"lint": "eslint *.js", "prepublishOnly": "npm run lint && npm test", "prettier": "prettier --write '*.js' '*.ts'", "test": "mocha test.js && npm run test:types", "test:types": "tsc --lib es2015,dom --noEmit --noImplicitAny --noImplicitReturns ./test-types.ts"}, "repository": {"type": "git", "url": "git+https://github.com/yomguithereal/obliterator.git"}, "keywords": ["iterator"], "author": {"name": "<PERSON>", "url": "http://github.com/Yomguithereal"}, "license": "MIT", "bugs": {"url": "https://github.com/yomguithereal/obliterator/issues"}, "homepage": "https://github.com/yomguithereal/obliterator#readme", "devDependencies": {"@yomguithereal/eslint-config": "^4.4.0", "@yomguithereal/prettier-config": "^1.2.0", "eslint": "^8.13.0", "eslint-config-prettier": "^8.5.0", "mocha": "^9.2.2", "prettier": "^2.6.2", "typescript": "^4.6.3"}, "eslintConfig": {"extends": ["@yomguithereal/eslint-config", "eslint-config-prettier"]}, "prettier": "@yomguithereal/prettier-config"}