{">=6.0.0 <6.3.0": ["--debug", "--debug-brk", "--icu-data-dir", "--no-deprecation", "--no-warnings", "--port", "--prof-process", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=6.3.0 <6.9.0": ["--debug", "--debug-brk", "--icu-data-dir", "--inspect", "--no-deprecation", "--no-warnings", "--port", "--prof-process", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=6.9.0 <6.11.0": ["--debug", "--debug-brk", "--icu-data-dir", "--inspect", "--no-deprecation", "--no-warnings", "--openssl-config", "--port", "--prof-process", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--v8-pool-size", "--zero-fill-buffers", "-r"], "~6.11.0": ["--debug", "--debug-brk", "--icu-data-dir", "--inspect", "--no-deprecation", "--no-warnings", "--openssl-config", "--port", "--prof-process", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=6.12.0 <8.0.0": ["--debug", "--debug-brk", "--icu-data-dir", "--inspect", "--inspect-brk", "--inspect-port", "--no-deprecation", "--no-warnings", "--openssl-config", "--port", "--prof-process", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=8.0.0 <8.2.0": ["--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=8.2.0 <8.4.0": ["--abort-on-uncaught-exception", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], "~8.4.0": ["--abort-on-uncaught-exception", "--expose-http2", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=8.5.0 <8.8.0": ["--abort-on-uncaught-exception", "--experimental-modules", "--expose-http2", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=8.8.0 <8.12.0": ["--abort-on-uncaught-exception", "--experimental-modules", "--expose-http2", "--force-async-hooks-checks", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=8.12.0 <10.0.0": ["--abort-on-uncaught-exception", "--experimental-modules", "--expose-http2", "--force-async-hooks-checks", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--max-old-space-size", "--napi-modules", "--no-deprecation", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-event-file-pattern", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=10.0.0 <10.3.0": ["--enable-fips", "--experimental-modules", "--experimental-repl-await", "--experimental-vm-modules", "--force-fips", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--napi-modules", "--no-deprecation", "--no-force-async-hooks-checks", "--no-warnings", "--openssl-config", "--pending-deprecation", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-event-file-pattern", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=10.3.0 <10.5.0": ["--enable-fips", "--experimental-modules", "--experimental-repl-await", "--experimental-vm-modules", "--force-fips", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--napi-modules", "--no-deprecation", "--no-force-async-hooks-checks", "--no-warnings", "--openssl-config", "--pending-deprecation", "--prof", "--prof-process", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-event-file-pattern", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=10.5.0 <10.7.0": ["--enable-fips", "--experimental-modules", "--experimental-repl-await", "--experimental-vm-modules", "--experimental-worker", "--force-fips", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--napi-modules", "--no-deprecation", "--no-force-async-hooks-checks", "--no-warnings", "--openssl-config", "--pending-deprecation", "--prof", "--prof-process", "--redirect-warnings", "--require", "--throw-deprecation", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-event-file-pattern", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"], ">=10.7.0 <10.10.0": ["--enable-fips", "--experimental-modules", "--experimental-repl-await", "--experimental-vm-modules", "--experimental-worker", "--force-fips", "--icu-data-dir", "--inspect-brk", "--inspect-port", "--inspect", "--loader", "--napi-modules", "--no-deprecation", "--no-force-async-hooks-checks", "--no-warnings", "--openssl-config", "--pending-deprecation", "--prof", "--prof-process", "--redirect-warnings", "--require", "--throw-deprecation", "--title", "--tls-cipher-list", "--trace-deprecation", "--trace-event-categories", "--trace-event-file-pattern", "--trace-events-enabled", "--trace-sync-io", "--trace-warnings", "--track-heap-objects", "--use-bundled-ca", "--use-openssl-ca", "--v8-pool-size", "--zero-fill-buffers", "-r"]}