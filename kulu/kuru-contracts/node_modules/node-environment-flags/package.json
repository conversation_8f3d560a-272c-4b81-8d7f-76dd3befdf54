{"name": "node-environment-flags", "version": "1.0.6", "description": "", "main": "index.js", "scripts": {"test": "mocha", "format": "prettier-eslint --write \"*.js\" \"*.json\" \"test/**/*.js\"", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (https://boneskull.com/)", "license": "Apache-2.0", "devDependencies": {"eslint": "^5.9.0", "eslint-config-prettier": "^3.3.0", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.1.3", "lint-staged": "^8.0.4", "mocha": "^5.2.0", "nyc": "^14.1.1", "prettier-eslint-cli": "^4.7.1", "semantic-release": "^15.13.16", "travis-deploy-once": "^5.0.9", "unexpected": "^10.39.1"}, "dependencies": {"object.getownpropertydescriptors": "^2.0.3", "semver": "^5.7.0"}, "files": ["implementation.js", "flags.json", "index.js", "polyfill.js", "shim.js"], "lint-staged": {"*.{js,json}": ["prettier-eslint --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "prettier": {"singleQuote": true, "bracketSpacing": false}, "repository": {"type": "git", "url": "https://github.com/boneskull/node-environment-flags.git"}}