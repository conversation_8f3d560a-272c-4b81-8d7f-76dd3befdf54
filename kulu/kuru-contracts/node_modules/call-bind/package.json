{"name": "call-bind", "version": "1.0.2", "description": "Robustly `.call.bind()` a function", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./callBound": [{"default": "./callBound.js"}, "./callBound.js"], "./package.json": "./package.json"}, "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/*'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind.git"}, "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/call-bind/issues"}, "homepage": "https://github.com/ljharb/call-bind#readme", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.17.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.1.1"}, "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}