language: node_js
os:
 - linux
node_js:
  - "8.4"
  - "7.10"
  - "6.11"
  - "5.12"
  - "4.8"
  - "iojs-v3.3"
  - "iojs-v2.5"
  - "iojs-v1.8"
  - "0.12"
  - "0.10"
  - "0.8"
before_install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ]; then npm install -g npm@1.3 ; elif [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then case "$(npm --version)" in 1.*) npm install -g npm@1.4.28 ;; 2.*) npm install -g npm@2 ;; esac ; fi'
  - 'if [ "${TRAVIS_NODE_VERSION}" != "0.6" ] && [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then if [ "${TRAVIS_NODE_VERSION%${TRAVIS_NODE_VERSION#[0-9]}}" = "0" ] || [ "${TRAVIS_NODE_VERSION:0:4}" = "iojs" ]; then npm install -g npm@4.5 ; else npm install -g npm; fi; fi'
install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ]; then nvm install 0.8 && npm install -g npm@1.3 && npm install -g npm@1.4.28 && npm install -g npm@2 && npm install && nvm use "${TRAVIS_NODE_VERSION}"; else npm install; fi;'
script:
  - 'if [ -n "${PRETEST-}" ]; then npm run pretest ; fi'
  - 'if [ -n "${POSTTEST-}" ]; then npm run posttest ; fi'
  - 'if [ -n "${COVERAGE-}" ]; then npm run coverage ; fi'
  - 'if [ -n "${TEST-}" ]; then npm run tests-only ; fi'
sudo: false
env:
  - TEST=true
matrix:
  fast_finish: true
  include:
    - node_js: "node"
      env: PRETEST=true
    - node_js: "4"
      env: COVERAGE=true
    - node_js: "8.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.11"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.11"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.4"
      env: TEST=true ALLOW_FAILURE=true
  allow_failures:
    - os: osx
    - env: TEST=true ALLOW_FAILURE=true
