{"name": "js-sha3", "version": "0.8.0", "description": "A simple SHA-3 / Keccak / Shake hash function for JavaScript supports UTF-8 encoding.", "main": "src/sha3.js", "devDependencies": {"expect.js": "~0.3.1", "mocha": "~2.3.4", "nyc": "^11.3.0", "uglify-js": "^3.1.9", "webworker-threads": "^0.7.13"}, "scripts": {"test": "nyc mocha tests/node-test.js", "report": "nyc --reporter=html --reporter=text mocha tests/node-test.js", "coveralls": "nyc report --reporter=text-lcov | coveralls", "build": "uglifyjs src/sha3.js -c -m --comments --output build/sha3.min.js"}, "repository": {"type": "git", "url": "https://github.com/emn178/js-sha3.git"}, "keywords": ["sha3", "keccak", "shake", "cshake", "kmac", "hash", "encryption", "cryptography", "HMAC"], "license": "MIT", "author": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/emn178/js-sha3", "bugs": {"url": "https://github.com/emn178/js-sha3/issues"}, "nyc": {"exclude": ["tests"]}}