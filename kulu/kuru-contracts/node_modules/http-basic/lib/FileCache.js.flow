// @flow
// Generated using flowgen2

import type {ICache} from './ICache';
import type {CachedResponse} from './CachedResponse';

declare class FileCache {
  constructor(location: string): void;
  getResponse(
    url: string,
    callback: (err: null | Error, response: null | CachedResponse) => void,
  ): void;
  setResponse(url: string, response: CachedResponse): void;
  updateResponseHeaders(
    url: string,
    response: {[key: 'headers' | 'requestTimestamp']: any},
  ): void;
  invalidateResponse(
    url: string,
    callback: (err: ErrnoError | null) => void,
  ): void;
}
export default FileCache;
