
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/helpers/command-line.helper.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/helpers</a> command-line.helper.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.71% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>171/175</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">94.73% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>54/57</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>11/11</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">97.71% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>171/175</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">33x</span>
<span class="cline-any cline-yes">33x</span>
<span class="cline-any cline-yes">241x</span>
<span class="cline-any cline-yes">241x</span>
<span class="cline-any cline-yes">241x</span>
<span class="cline-any cline-yes">241x</span>
<span class="cline-any cline-yes">33x</span>
<span class="cline-any cline-yes">33x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">246x</span>
<span class="cline-any cline-yes">246x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">22x</span>
<span class="cline-any cline-yes">22x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">21x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">37x</span>
<span class="cline-any cline-yes">37x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-yes">37x</span>
<span class="cline-any cline-yes">37x</span>
<span class="cline-any cline-yes">37x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">77x</span>
<span class="cline-any cline-yes">73x</span>
<span class="cline-any cline-yes">73x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">77x</span>
<span class="cline-any cline-yes">77x</span>
<span class="cline-any cline-yes">77x</span>
<span class="cline-any cline-yes">7x</span>
<span class="cline-any cline-yes">77x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">81x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">24x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">56x</span>
<span class="cline-any cline-yes">56x</span>
<span class="cline-any cline-yes">106x</span>
<span class="cline-any cline-yes">106x</span>
<span class="cline-any cline-yes">106x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">510x</span>
<span class="cline-any cline-yes">510x</span>
<span class="cline-any cline-yes">510x</span>
<span class="cline-any cline-yes">102x</span>
<span class="cline-any cline-yes">102x</span>
<span class="cline-any cline-yes">510x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-yes">162x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { PropertyOptions, ArgumentConfig, ArgumentOptions, CommandLineOption } from '../contracts';
import { isBoolean } from './options.helper';
&nbsp;
export function createCommandLineConfig&lt;T&gt;(config: ArgumentOptions&lt;T&gt;): CommandLineOption[] {
    return Object.keys(config).map((key) =&gt; {
        const argConfig: any = config[key as keyof T];
        const definition: PropertyOptions&lt;any&gt; = typeof argConfig === 'object' ? argConfig <span class="branch-0 cbranch-no" title="branch not covered" >: { type: argConfig };</span>
&nbsp;
        return { name: key, ...definition };
    });
}
&nbsp;
export function normaliseConfig&lt;T&gt;(config: ArgumentConfig&lt;T&gt;): ArgumentOptions&lt;T&gt; {
    Object.keys(config).forEach((key) =&gt; {
        const argConfig: any = config[key as keyof T];
        config[key as keyof T] = typeof argConfig === 'object' ? argConfig : { type: argConfig };
    });
&nbsp;
    return config as ArgumentOptions&lt;T&gt;;
}
&nbsp;
export function mergeConfig&lt;T&gt;(
    parsedConfig: Partial&lt;T&gt;,
    parsedConfigWithoutDefaults: Partial&lt;T&gt;,
    fileContent: Record&lt;string, unknown&gt;,
    options: ArgumentOptions&lt;T&gt;,
    jsonPath: keyof T | undefined,
): Partial&lt;T&gt; {
    const configPath: string | undefined = jsonPath ? (parsedConfig[jsonPath] as any) : undefined;
    const configFromFile = resolveConfigFromFile(fileContent, configPath);
    if (configFromFile == null) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        throw new Error(`Could not resolve config object from specified file and path`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    return { ...parsedConfig, ...applyTypeConversion(configFromFile, options), ...parsedConfigWithoutDefaults };
}
&nbsp;
function resolveConfigFromFile&lt;T&gt;(configfromFile: any, configPath?: string): Partial&lt;Record&lt;keyof T, any&gt;&gt; {
    if (configPath == null || configPath == '') {
        return configfromFile as Partial&lt;Record&lt;keyof T, any&gt;&gt;;
    }
    const paths = configPath.split('.');
    const key = paths.shift();
&nbsp;
    if (key == null) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return configfromFile;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    const config = configfromFile[key];
    return resolveConfigFromFile(config, paths.join('.'));
}
&nbsp;
function applyTypeConversion&lt;T&gt;(
    configfromFile: Partial&lt;Record&lt;keyof T, any&gt;&gt;,
    options: ArgumentOptions&lt;T&gt;,
): Partial&lt;T&gt; {
    const transformedParams: Partial&lt;T&gt; = {};
&nbsp;
    Object.keys(configfromFile).forEach((prop) =&gt; {
        const key = prop as keyof T;
        const argumentOptions = options[key];
        if (argumentOptions == null) {
            return;
        }
        const fileValue = configfromFile[key];
        if (argumentOptions.multiple || argumentOptions.lazyMultiple) {
            const fileArrayValue = Array.isArray(fileValue) ? fileValue : [fileValue];
&nbsp;
            transformedParams[key] = fileArrayValue.map((arrayValue) =&gt;
                convertType(arrayValue, argumentOptions),
            ) as any;
        } else {
            transformedParams[key] = convertType(fileValue, argumentOptions) as any;
        }
    });
&nbsp;
    return transformedParams;
}
&nbsp;
function convertType&lt;T&gt;(value: any, propOptions: PropertyOptions&lt;T&gt;): any {
    if (propOptions.type.name === 'Boolean') {
        switch (value) {
            case 'true':
                return propOptions.type(true);
            case 'false':
                return propOptions.type(false);
        }
    }
&nbsp;
    return propOptions.type(value);
}
&nbsp;
type ArgsAndLastOption = { args: string[]; lastOption?: PropertyOptions&lt;any&gt; };
type PartialAndLastOption&lt;T&gt; = {
    partial: Partial&lt;T&gt;;
    lastOption?: PropertyOptions&lt;any&gt;;
    lastName?: Extract&lt;keyof T, string&gt;;
};
const argNameRegExp = /^-{1,2}(\w+)(=(\w+))?$/;
const booleanValue = ['1', '0', 'true', 'false'];
&nbsp;
/**
 * commandLineArgs throws an error if we pass aa value for a boolean arg as follows:
 * myCommand -a=true --booleanArg=false --otherArg true
 * this function removes these booleans so as to avoid errors from commandLineArgs
 * @param args
 * @param config
 */
export function removeBooleanValues&lt;T&gt;(args: string[], config: ArgumentOptions&lt;T&gt;): string[] {
    function removeBooleanArgs(argsAndLastValue: ArgsAndLastOption, arg: string): ArgsAndLastOption {
        const { argOptions, argValue } = getParamConfig(arg, config);
&nbsp;
        const lastOption = argsAndLastValue.lastOption;
&nbsp;
        if (lastOption != null &amp;&amp; isBoolean(lastOption) &amp;&amp; booleanValue.some((boolValue) =&gt; boolValue === arg)) {
            const args = argsAndLastValue.args.concat();
            args.pop();
            return { args };
        } else if (argOptions != null &amp;&amp; isBoolean(argOptions) &amp;&amp; argValue != null) {
            return { args: argsAndLastValue.args };
        } else {
            return { args: [...argsAndLastValue.args, arg], lastOption: argOptions };
        }
    }
&nbsp;
    return args.reduce&lt;ArgsAndLastOption&gt;(removeBooleanArgs, { args: [] }).args;
}
&nbsp;
/**
 * Gets the values of any boolean arguments that were specified on the command line with a value
 * These arguments were removed by removeBooleanValues
 * @param args
 * @param config
 */
export function getBooleanValues&lt;T&gt;(args: string[], config: ArgumentOptions&lt;T&gt;): Partial&lt;T&gt; {
    function getBooleanValues(argsAndLastOption: PartialAndLastOption&lt;T&gt;, arg: string): PartialAndLastOption&lt;T&gt; {
        const { argOptions, argName, argValue } = getParamConfig(arg, config);
&nbsp;
        const lastOption = argsAndLastOption.lastOption;
&nbsp;
        if (argOptions != null &amp;&amp; isBoolean(argOptions) &amp;&amp; argValue != null &amp;&amp; argName != null) {
            argsAndLastOption.partial[argName] = convertType(argValue, argOptions) as any;
        } else if (
            argsAndLastOption.lastName != null &amp;&amp;
            lastOption != null &amp;&amp;
            isBoolean(lastOption) &amp;&amp;
            booleanValue.some((boolValue) =&gt; boolValue === arg)
        ) {
            argsAndLastOption.partial[argsAndLastOption.lastName] = convertType(arg, lastOption) as any;
        }
        return { partial: argsAndLastOption.partial, lastName: argName, lastOption: argOptions };
    }
&nbsp;
    return args.reduce&lt;PartialAndLastOption&lt;T&gt;&gt;(getBooleanValues, { partial: {} }).partial;
}
&nbsp;
function getParamConfig&lt;T&gt;(
    arg: string,
    config: ArgumentOptions&lt;T&gt;,
): { argName?: Extract&lt;keyof T, string&gt;; argOptions?: PropertyOptions&lt;any&gt;; argValue?: string } {
    const regExpResult = argNameRegExp.exec(arg);
    if (regExpResult == null) {
        return {};
    }
&nbsp;
    const nameOrAlias = regExpResult[1];
&nbsp;
    for (const argName in config) {
        const argConfig = config[argName];
&nbsp;
        if (argName === nameOrAlias || argConfig.alias === nameOrAlias) {
            return { argOptions: argConfig as PropertyOptions&lt;any&gt;, argName, argValue: regExpResult[3] };
        }
    }
    return {};
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at Fri Jun 02 2023 06:26:26 GMT+0000 (Coordinated Universal Time)
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    