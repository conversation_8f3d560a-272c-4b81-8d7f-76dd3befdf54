
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/helpers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/helpers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.93% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>816/878</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">90.07% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>254/282</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.3% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>48/52</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.93% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>816/878</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="add-content.helper.ts"><a href="add-content.helper.ts.html">add-content.helper.ts</a></td>
	<td data-value="77.41" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 77%"></div><div class="cover-empty" style="width: 23%"></div></div>
	</td>
	<td data-value="77.41" class="pct medium">77.41%</td>
	<td data-value="62" class="abs medium">48/62</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="77.41" class="pct medium">77.41%</td>
	<td data-value="62" class="abs medium">48/62</td>
	</tr>

<tr>
	<td class="file high" data-value="command-line.helper.ts"><a href="command-line.helper.ts.html">command-line.helper.ts</a></td>
	<td data-value="97.71" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.71" class="pct high">97.71%</td>
	<td data-value="175" class="abs high">171/175</td>
	<td data-value="94.73" class="pct high">94.73%</td>
	<td data-value="57" class="abs high">54/57</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="97.71" class="pct high">97.71%</td>
	<td data-value="175" class="abs high">171/175</td>
	</tr>

<tr>
	<td class="file high" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="insert-code.helper.ts"><a href="insert-code.helper.ts.html">insert-code.helper.ts</a></td>
	<td data-value="97.64" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.64" class="pct high">97.64%</td>
	<td data-value="170" class="abs high">166/170</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="48" class="abs high">40/48</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="97.64" class="pct high">97.64%</td>
	<td data-value="170" class="abs high">166/170</td>
	</tr>

<tr>
	<td class="file high" data-value="line-ending.helper.ts"><a href="line-ending.helper.ts.html">line-ending.helper.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="16" class="abs high">16/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	</tr>

<tr>
	<td class="file medium" data-value="markdown.helper.ts"><a href="markdown.helper.ts.html">markdown.helper.ts</a></td>
	<td data-value="79.67" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 79%"></div><div class="cover-empty" style="width: 21%"></div></div>
	</td>
	<td data-value="79.67" class="pct medium">79.67%</td>
	<td data-value="182" class="abs medium">145/182</td>
	<td data-value="91.78" class="pct high">91.78%</td>
	<td data-value="73" class="abs high">67/73</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="79.67" class="pct medium">79.67%</td>
	<td data-value="182" class="abs medium">145/182</td>
	</tr>

<tr>
	<td class="file high" data-value="options.helper.ts"><a href="options.helper.ts.html">options.helper.ts</a></td>
	<td data-value="97.65" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.65" class="pct high">97.65%</td>
	<td data-value="128" class="abs high">125/128</td>
	<td data-value="83.72" class="pct high">83.72%</td>
	<td data-value="43" class="abs high">36/43</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="97.65" class="pct high">97.65%</td>
	<td data-value="128" class="abs high">125/128</td>
	</tr>

<tr>
	<td class="file high" data-value="string.helper.ts"><a href="string.helper.ts.html">string.helper.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="17" class="abs high">17/17</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	</tr>

<tr>
	<td class="file high" data-value="visitor.ts"><a href="visitor.ts.html">visitor.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="27" class="abs high">27/27</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="27" class="abs high">27/27</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at Fri Jun 02 2023 06:26:26 GMT+0000 (Coordinated Universal Time)
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    