<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1685687186682" clover="3.2.0">
  <project timestamp="1685687186682" name="All files">
    <metrics statements="1408" coveredstatements="1337" conditionals="340" coveredconditionals="300" methods="60" coveredmethods="55" elements="1808" coveredelements="1692" complexity="0" loc="1408" ncloc="1408" packages="3" files="12" classes="12"/>
    <package name="src">
      <metrics statements="343" coveredstatements="334" conditionals="58" coveredconditionals="46" methods="8" coveredmethods="7"/>
      <file name="parse.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/parse.ts">
        <metrics statements="199" coveredstatements="190" conditionals="58" coveredconditionals="46" methods="8" coveredmethods="7"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="24" type="stmt"/>
        <line num="36" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="37" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="38" count="24" type="stmt"/>
        <line num="39" count="24" type="stmt"/>
        <line num="40" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="41" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="42" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="24" type="stmt"/>
        <line num="44" count="24" type="stmt"/>
        <line num="45" count="24" type="stmt"/>
        <line num="46" count="24" type="stmt"/>
        <line num="47" count="24" type="stmt"/>
        <line num="48" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="24" type="stmt"/>
        <line num="56" count="24" type="stmt"/>
        <line num="57" count="24" type="stmt"/>
        <line num="58" count="24" type="stmt"/>
        <line num="59" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="60" count="12" type="stmt"/>
        <line num="61" count="12" type="stmt"/>
        <line num="62" count="12" type="stmt"/>
        <line num="63" count="12" type="stmt"/>
        <line num="64" count="12" type="stmt"/>
        <line num="65" count="12" type="cond" truecount="1" falsecount="0"/>
        <line num="66" count="12" type="stmt"/>
        <line num="67" count="12" type="stmt"/>
        <line num="68" count="12" type="stmt"/>
        <line num="69" count="12" type="stmt"/>
        <line num="70" count="12" type="stmt"/>
        <line num="71" count="12" type="stmt"/>
        <line num="72" count="12" type="stmt"/>
        <line num="73" count="12" type="stmt"/>
        <line num="74" count="12" type="stmt"/>
        <line num="75" count="12" type="stmt"/>
        <line num="76" count="12" type="stmt"/>
        <line num="77" count="24" type="stmt"/>
        <line num="78" count="24" type="stmt"/>
        <line num="79" count="24" type="stmt"/>
        <line num="80" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="81" count="2" type="stmt"/>
        <line num="82" count="2" type="stmt"/>
        <line num="83" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="87" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="5" type="stmt"/>
        <line num="95" count="5" type="stmt"/>
        <line num="96" count="5" type="stmt"/>
        <line num="97" count="5" type="stmt"/>
        <line num="98" count="5" type="cond" truecount="3" falsecount="0"/>
        <line num="99" count="5" type="stmt"/>
        <line num="100" count="5" type="stmt"/>
        <line num="101" count="5" type="stmt"/>
        <line num="102" count="23" type="cond" truecount="1" falsecount="0"/>
        <line num="103" count="23" type="stmt"/>
        <line num="104" count="23" type="stmt"/>
        <line num="105" count="23" type="stmt"/>
        <line num="106" count="23" type="stmt"/>
        <line num="107" count="23" type="stmt"/>
        <line num="108" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="109" count="4" type="stmt"/>
        <line num="110" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="111" count="19" type="cond" truecount="0" falsecount="1"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="19" type="stmt"/>
        <line num="115" count="19" type="stmt"/>
        <line num="116" count="19" type="stmt"/>
        <line num="117" count="24" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="120" count="5" type="stmt"/>
        <line num="121" count="5" type="stmt"/>
        <line num="122" count="5" type="stmt"/>
        <line num="123" count="5" type="stmt"/>
        <line num="124" count="5" type="stmt"/>
        <line num="125" count="5" type="stmt"/>
        <line num="126" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="129" count="2" type="stmt"/>
        <line num="130" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="131" count="3" type="stmt"/>
        <line num="132" count="5" type="stmt"/>
        <line num="133" count="5" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="136" count="2" type="stmt"/>
        <line num="137" count="2" type="stmt"/>
        <line num="138" count="2" type="stmt"/>
        <line num="139" count="2" type="stmt"/>
        <line num="140" count="2" type="stmt"/>
        <line num="141" count="2" type="stmt"/>
        <line num="142" count="2" type="stmt"/>
        <line num="143" count="2" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="145" count="2" type="stmt"/>
        <line num="146" count="2" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="2" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="149" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="150" count="81" type="stmt"/>
        <line num="151" count="81" type="cond" truecount="1" falsecount="0"/>
        <line num="152" count="39" type="stmt"/>
        <line num="153" count="81" type="cond" truecount="1" falsecount="0"/>
        <line num="154" count="42" type="stmt"/>
        <line num="155" count="81" type="stmt"/>
        <line num="156" count="2" type="stmt"/>
        <line num="157" count="2" type="stmt"/>
        <line num="158" count="2" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="160" count="2" type="stmt"/>
        <line num="161" count="2" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="164" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="165" count="10" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="168" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="169" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="170" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="171" count="10" type="stmt"/>
        <line num="172" count="10" type="stmt"/>
        <line num="173" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="174" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="175" count="10" type="stmt"/>
        <line num="176" count="5" type="stmt"/>
        <line num="177" count="5" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="180" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="181" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="182" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="183" count="2" type="stmt"/>
        <line num="184" count="2" type="stmt"/>
        <line num="185" count="2" type="stmt"/>
        <line num="186" count="5" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="189" count="24" type="stmt"/>
        <line num="190" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="191" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="192" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="193" count="9" type="stmt"/>
        <line num="194" count="9" type="stmt"/>
        <line num="195" count="9" type="stmt"/>
        <line num="196" count="21" type="stmt"/>
        <line num="197" count="21" type="stmt"/>
        <line num="198" count="24" type="stmt"/>
        <line num="199" count="24" type="stmt"/>
      </file>
      <file name="write-markdown.constants.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/write-markdown.constants.ts">
        <metrics statements="144" coveredstatements="144" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.example">
      <metrics statements="187" coveredstatements="187" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="configs.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/example/configs.ts">
        <metrics statements="187" coveredstatements="187" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.helpers">
      <metrics statements="878" coveredstatements="816" conditionals="282" coveredconditionals="254" methods="52" coveredmethods="48"/>
      <file name="add-content.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/add-content.helper.ts">
        <metrics statements="62" coveredstatements="48" conditionals="21" coveredconditionals="17" methods="2" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="14" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="15" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="11" type="stmt"/>
        <line num="17" count="11" type="stmt"/>
        <line num="18" count="11" type="stmt"/>
        <line num="19" count="11" type="stmt"/>
        <line num="20" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="21" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="11" type="stmt"/>
        <line num="23" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="24" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="25" count="11" type="stmt"/>
        <line num="26" count="11" type="cond" truecount="3" falsecount="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="11" type="stmt"/>
        <line num="33" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="11" type="stmt"/>
        <line num="35" count="11" type="stmt"/>
        <line num="36" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="37" count="11" type="stmt"/>
        <line num="38" count="11" type="stmt"/>
        <line num="39" count="11" type="stmt"/>
        <line num="40" count="11" type="stmt"/>
        <line num="41" count="11" type="stmt"/>
        <line num="42" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="43" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="11" type="stmt"/>
        <line num="46" count="11" type="stmt"/>
        <line num="47" count="11" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
      <file name="command-line.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/command-line.helper.ts">
        <metrics statements="175" coveredstatements="171" conditionals="57" coveredconditionals="54" methods="11" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="5" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="6" count="241" type="stmt"/>
        <line num="7" count="241" type="cond" truecount="0" falsecount="1"/>
        <line num="8" count="241" type="stmt"/>
        <line num="9" count="241" type="stmt"/>
        <line num="10" count="33" type="stmt"/>
        <line num="11" count="33" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="14" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="15" count="246" type="stmt"/>
        <line num="16" count="246" type="cond" truecount="2" falsecount="0"/>
        <line num="17" count="34" type="stmt"/>
        <line num="18" count="34" type="stmt"/>
        <line num="19" count="34" type="stmt"/>
        <line num="20" count="34" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="21" type="stmt"/>
        <line num="24" count="21" type="stmt"/>
        <line num="25" count="21" type="stmt"/>
        <line num="26" count="21" type="stmt"/>
        <line num="27" count="21" type="stmt"/>
        <line num="28" count="21" type="stmt"/>
        <line num="29" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="30" count="21" type="stmt"/>
        <line num="31" count="21" type="cond" truecount="0" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="21" type="stmt"/>
        <line num="35" count="21" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="38" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="21" type="stmt"/>
        <line num="40" count="21" type="stmt"/>
        <line num="41" count="24" type="stmt"/>
        <line num="42" count="24" type="stmt"/>
        <line num="43" count="24" type="stmt"/>
        <line num="44" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="24" type="stmt"/>
        <line num="48" count="24" type="stmt"/>
        <line num="49" count="24" type="stmt"/>
        <line num="50" count="24" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="53" count="21" type="stmt"/>
        <line num="54" count="21" type="stmt"/>
        <line num="55" count="21" type="stmt"/>
        <line num="56" count="21" type="stmt"/>
        <line num="57" count="21" type="stmt"/>
        <line num="58" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="59" count="28" type="stmt"/>
        <line num="60" count="28" type="stmt"/>
        <line num="61" count="28" type="cond" truecount="1" falsecount="0"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="28" type="stmt"/>
        <line num="65" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="67" count="5" type="stmt"/>
        <line num="68" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="69" count="5" type="stmt"/>
        <line num="70" count="5" type="stmt"/>
        <line num="71" count="28" type="cond" truecount="1" falsecount="0"/>
        <line num="72" count="22" type="stmt"/>
        <line num="73" count="22" type="stmt"/>
        <line num="74" count="21" type="stmt"/>
        <line num="75" count="21" type="stmt"/>
        <line num="76" count="21" type="stmt"/>
        <line num="77" count="21" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="37" type="cond" truecount="1" falsecount="0"/>
        <line num="80" count="37" type="cond" truecount="1" falsecount="0"/>
        <line num="81" count="15" type="stmt"/>
        <line num="82" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="83" count="5" type="stmt"/>
        <line num="84" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="85" count="5" type="stmt"/>
        <line num="86" count="15" type="stmt"/>
        <line num="87" count="15" type="stmt"/>
        <line num="88" count="37" type="stmt"/>
        <line num="89" count="37" type="stmt"/>
        <line num="90" count="37" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="109" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="110" count="81" type="stmt"/>
        <line num="111" count="81" type="stmt"/>
        <line num="112" count="81" type="stmt"/>
        <line num="113" count="81" type="stmt"/>
        <line num="114" count="81" type="cond" truecount="4" falsecount="0"/>
        <line num="115" count="4" type="stmt"/>
        <line num="116" count="4" type="stmt"/>
        <line num="117" count="4" type="stmt"/>
        <line num="118" count="81" type="cond" truecount="4" falsecount="0"/>
        <line num="119" count="4" type="stmt"/>
        <line num="120" count="77" type="cond" truecount="1" falsecount="0"/>
        <line num="121" count="73" type="stmt"/>
        <line num="122" count="73" type="stmt"/>
        <line num="123" count="81" type="stmt"/>
        <line num="124" count="24" type="stmt"/>
        <line num="125" count="24" type="stmt"/>
        <line num="126" count="24" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="136" count="81" type="stmt"/>
        <line num="137" count="81" type="stmt"/>
        <line num="138" count="81" type="stmt"/>
        <line num="139" count="81" type="stmt"/>
        <line num="140" count="81" type="cond" truecount="4" falsecount="0"/>
        <line num="141" count="4" type="stmt"/>
        <line num="142" count="81" type="cond" truecount="1" falsecount="0"/>
        <line num="143" count="77" type="cond" truecount="1" falsecount="0"/>
        <line num="144" count="77" type="cond" truecount="1" falsecount="0"/>
        <line num="145" count="77" type="cond" truecount="1" falsecount="0"/>
        <line num="146" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="147" count="77" type="cond" truecount="1" falsecount="0"/>
        <line num="148" count="4" type="stmt"/>
        <line num="149" count="4" type="stmt"/>
        <line num="150" count="81" type="stmt"/>
        <line num="151" count="81" type="stmt"/>
        <line num="152" count="24" type="stmt"/>
        <line num="153" count="24" type="stmt"/>
        <line num="154" count="24" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="162" type="cond" truecount="1" falsecount="0"/>
        <line num="157" count="162" type="stmt"/>
        <line num="158" count="162" type="stmt"/>
        <line num="159" count="162" type="stmt"/>
        <line num="160" count="162" type="stmt"/>
        <line num="161" count="162" type="cond" truecount="1" falsecount="0"/>
        <line num="162" count="56" type="stmt"/>
        <line num="163" count="56" type="stmt"/>
        <line num="164" count="106" type="cond" truecount="1" falsecount="0"/>
        <line num="165" count="106" type="stmt"/>
        <line num="166" count="106" type="stmt"/>
        <line num="167" count="162" type="cond" truecount="1" falsecount="0"/>
        <line num="168" count="510" type="stmt"/>
        <line num="169" count="510" type="stmt"/>
        <line num="170" count="510" type="cond" truecount="2" falsecount="0"/>
        <line num="171" count="102" type="stmt"/>
        <line num="172" count="102" type="stmt"/>
        <line num="173" count="510" type="stmt"/>
        <line num="174" count="162" type="stmt"/>
        <line num="175" count="162" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/index.ts">
        <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
      </file>
      <file name="insert-code.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/insert-code.helper.ts">
        <metrics statements="170" coveredstatements="166" conditionals="48" coveredconditionals="40" methods="5" coveredmethods="5"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="22" count="15" type="stmt"/>
        <line num="23" count="15" type="stmt"/>
        <line num="24" count="15" type="stmt"/>
        <line num="25" count="15" type="stmt"/>
        <line num="26" count="15" type="stmt"/>
        <line num="27" count="15" type="stmt"/>
        <line num="28" count="15" type="stmt"/>
        <line num="29" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="30" count="2" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="32" count="15" type="stmt"/>
        <line num="33" count="15" type="stmt"/>
        <line num="34" count="15" type="stmt"/>
        <line num="35" count="15" type="stmt"/>
        <line num="36" count="15" type="stmt"/>
        <line num="37" count="15" type="stmt"/>
        <line num="38" count="15" type="stmt"/>
        <line num="39" count="15" type="stmt"/>
        <line num="40" count="15" type="stmt"/>
        <line num="41" count="15" type="stmt"/>
        <line num="42" count="15" type="stmt"/>
        <line num="43" count="15" type="stmt"/>
        <line num="44" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="45" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="15" type="stmt"/>
        <line num="48" count="15" type="stmt"/>
        <line num="49" count="15" type="stmt"/>
        <line num="50" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="51" count="2" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="53" count="15" type="stmt"/>
        <line num="54" count="15" type="stmt"/>
        <line num="55" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="56" count="15" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="59" count="27" type="stmt"/>
        <line num="60" count="27" type="stmt"/>
        <line num="61" count="27" type="stmt"/>
        <line num="62" count="27" type="stmt"/>
        <line num="63" count="27" type="stmt"/>
        <line num="64" count="27" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="27" type="cond" truecount="0" falsecount="1"/>
        <line num="66" count="27" type="stmt"/>
        <line num="67" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="26" type="cond" truecount="1" falsecount="0"/>
        <line num="71" count="26" type="stmt"/>
        <line num="72" count="26" type="stmt"/>
        <line num="73" count="26" type="cond" truecount="1" falsecount="0"/>
        <line num="74" count="27" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="27" type="stmt"/>
        <line num="76" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="77" count="12" type="stmt"/>
        <line num="78" count="12" type="stmt"/>
        <line num="79" count="27" type="stmt"/>
        <line num="80" count="27" type="stmt"/>
        <line num="81" count="27" type="stmt"/>
        <line num="82" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="83" count="27" type="cond" truecount="0" falsecount="1"/>
        <line num="84" count="27" type="stmt"/>
        <line num="85" count="27" type="stmt"/>
        <line num="86" count="27" type="stmt"/>
        <line num="87" count="27" type="stmt"/>
        <line num="88" count="27" type="cond" truecount="2" falsecount="0"/>
        <line num="89" count="27" type="stmt"/>
        <line num="90" count="27" type="stmt"/>
        <line num="91" count="27" type="stmt"/>
        <line num="92" count="27" type="stmt"/>
        <line num="93" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="94" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="95" count="27" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="14" type="cond" truecount="1" falsecount="0"/>
        <line num="102" count="14" type="stmt"/>
        <line num="103" count="14" type="stmt"/>
        <line num="104" count="14" type="stmt"/>
        <line num="105" count="14" type="stmt"/>
        <line num="106" count="14" type="stmt"/>
        <line num="107" count="14" type="stmt"/>
        <line num="108" count="14" type="cond" truecount="1" falsecount="0"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="114" count="13" type="stmt"/>
        <line num="115" count="13" type="stmt"/>
        <line num="116" count="13" type="stmt"/>
        <line num="117" count="14" type="cond" truecount="1" falsecount="1"/>
        <line num="118" count="14" type="stmt"/>
        <line num="119" count="14" type="stmt"/>
        <line num="120" count="14" type="stmt"/>
        <line num="121" count="14" type="stmt"/>
        <line num="122" count="14" type="stmt"/>
        <line num="123" count="14" type="stmt"/>
        <line num="124" count="14" type="stmt"/>
        <line num="125" count="14" type="stmt"/>
        <line num="126" count="14" type="stmt"/>
        <line num="127" count="14" type="stmt"/>
        <line num="128" count="14" type="cond" truecount="3" falsecount="1"/>
        <line num="129" count="14" type="stmt"/>
        <line num="130" count="14" type="stmt"/>
        <line num="131" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="14" type="cond" truecount="0" falsecount="1"/>
        <line num="133" count="14" type="stmt"/>
        <line num="134" count="14" type="cond" truecount="1" falsecount="1"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="14" type="stmt"/>
        <line num="140" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="141" count="14" type="stmt"/>
        <line num="142" count="14" type="cond" truecount="1" falsecount="0"/>
        <line num="143" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="144" count="2" type="stmt"/>
        <line num="145" count="14" type="stmt"/>
        <line num="146" count="14" type="stmt"/>
        <line num="147" count="14" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="150" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="151" count="27" type="cond" truecount="2" falsecount="0"/>
        <line num="152" count="13" type="stmt"/>
        <line num="153" count="13" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="40" type="cond" truecount="1" falsecount="0"/>
        <line num="158" count="40" type="stmt"/>
        <line num="159" count="40" type="stmt"/>
        <line num="160" count="40" type="stmt"/>
        <line num="161" count="40" type="stmt"/>
        <line num="162" count="40" type="cond" truecount="1" falsecount="0"/>
        <line num="163" count="105" type="stmt"/>
        <line num="164" count="105" type="cond" truecount="1" falsecount="0"/>
        <line num="165" count="27" type="stmt"/>
        <line num="166" count="27" type="stmt"/>
        <line num="167" count="105" type="stmt"/>
        <line num="168" count="40" type="stmt"/>
        <line num="169" count="40" type="stmt"/>
        <line num="170" count="40" type="stmt"/>
      </file>
      <file name="line-ending.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/line-ending.helper.ts">
        <metrics statements="63" coveredstatements="63" conditionals="16" coveredconditionals="16" methods="5" coveredmethods="5"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="15" count="5" type="stmt"/>
        <line num="16" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="27" count="5" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="31" type="cond" truecount="1" falsecount="0"/>
        <line num="34" count="31" type="stmt"/>
        <line num="35" count="31" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="2" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="38" count="31" type="stmt"/>
        <line num="39" count="31" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="28" type="stmt"/>
        <line num="41" count="28" type="stmt"/>
        <line num="42" count="31" type="stmt"/>
        <line num="43" count="31" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="55" type="cond" truecount="1" falsecount="0"/>
        <line num="52" count="55" type="stmt"/>
        <line num="53" count="55" type="stmt"/>
        <line num="54" count="55" type="stmt"/>
        <line num="55" count="55" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="60" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="61" count="18" type="stmt"/>
        <line num="62" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="63" count="18" type="stmt"/>
      </file>
      <file name="markdown.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/markdown.helper.ts">
        <metrics statements="182" coveredstatements="145" conditionals="73" coveredconditionals="67" methods="14" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="20" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="21" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="22" count="8" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="8" type="stmt"/>
        <line num="26" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="27" count="8" type="stmt"/>
        <line num="28" count="8" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="32" count="15" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="13" type="stmt"/>
        <line num="36" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="37" count="13" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="39" count="13" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="42" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="43" count="7" type="stmt"/>
        <line num="44" count="7" type="stmt"/>
        <line num="45" count="13" type="stmt"/>
        <line num="46" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="47" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="53" count="13" type="stmt"/>
        <line num="54" count="13" type="stmt"/>
        <line num="55" count="13" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="65" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="66" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="70" count="8" type="stmt"/>
        <line num="71" count="8" type="stmt"/>
        <line num="72" count="8" type="stmt"/>
        <line num="73" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="8" type="stmt"/>
        <line num="77" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="78" count="8" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="81" count="8" type="stmt"/>
        <line num="82" count="8" type="stmt"/>
        <line num="83" count="8" type="stmt"/>
        <line num="84" count="8" type="stmt"/>
        <line num="85" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="86" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="87" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="88" count="8" type="stmt"/>
        <line num="89" count="8" type="stmt"/>
        <line num="90" count="8" type="stmt"/>
        <line num="91" count="8" type="stmt"/>
        <line num="92" count="8" type="stmt"/>
        <line num="93" count="8" type="cond" truecount="4" falsecount="0"/>
        <line num="94" count="8" type="cond" truecount="4" falsecount="0"/>
        <line num="95" count="8" type="stmt"/>
        <line num="96" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="97" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="98" count="8" type="stmt"/>
        <line num="99" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="100" count="8" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="52" type="cond" truecount="1" falsecount="0"/>
        <line num="103" count="52" type="stmt"/>
        <line num="104" count="52" type="cond" truecount="1" falsecount="0"/>
        <line num="105" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="106" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="107" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="108" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="109" count="52" type="stmt"/>
        <line num="110" count="52" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="113" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="114" count="2" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="116" count="21" type="stmt"/>
        <line num="117" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="118" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="119" count="21" type="stmt"/>
        <line num="120" count="21" type="stmt"/>
        <line num="121" count="21" type="stmt"/>
        <line num="122" count="21" type="stmt"/>
        <line num="123" count="21" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="48" type="cond" truecount="1" falsecount="2"/>
        <line num="126" count="48" type="cond" truecount="4" falsecount="0"/>
        <line num="127" count="48" type="stmt"/>
        <line num="128" count="48" type="cond" truecount="3" falsecount="0"/>
        <line num="129" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="130" count="48" type="stmt"/>
        <line num="131" count="48" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="134" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="13" type="stmt"/>
        <line num="136" count="13" type="stmt"/>
        <line num="137" count="48" type="stmt"/>
        <line num="138" count="48" type="stmt"/>
        <line num="139" count="48" type="stmt"/>
        <line num="140" count="48" type="cond" truecount="0" falsecount="1"/>
        <line num="141" count="48" type="cond" truecount="3" falsecount="0"/>
        <line num="142" count="48" type="stmt"/>
        <line num="143" count="48" type="stmt"/>
        <line num="144" count="48" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
      </file>
      <file name="options.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/options.helper.ts">
        <metrics statements="128" coveredstatements="125" conditionals="43" coveredconditionals="36" methods="9" coveredmethods="9"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="12" count="9" type="stmt"/>
        <line num="13" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="14" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="8" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="10" type="stmt"/>
        <line num="32" count="10" type="stmt"/>
        <line num="33" count="10" type="stmt"/>
        <line num="34" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="9" type="stmt"/>
        <line num="36" count="9" type="stmt"/>
        <line num="37" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="38" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="39" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="10" type="cond" truecount="2" falsecount="1"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="44" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="8" type="stmt"/>
        <line num="49" count="8" type="stmt"/>
        <line num="50" count="8" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="53" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="57" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="2" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="58" type="cond" truecount="1" falsecount="0"/>
        <line num="67" count="58" type="stmt"/>
        <line num="68" count="58" type="stmt"/>
        <line num="69" count="58" type="stmt"/>
        <line num="70" count="58" type="stmt"/>
        <line num="71" count="58" type="stmt"/>
        <line num="72" count="58" type="stmt"/>
        <line num="73" count="58" type="stmt"/>
        <line num="74" count="58" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="58" type="cond" truecount="1" falsecount="0"/>
        <line num="77" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="78" count="55" type="stmt"/>
        <line num="79" count="55" type="stmt"/>
        <line num="80" count="58" type="stmt"/>
        <line num="81" count="58" type="cond" truecount="0" falsecount="1"/>
        <line num="82" count="58" type="stmt"/>
        <line num="83" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="48" type="stmt"/>
        <line num="87" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="48" type="stmt"/>
        <line num="91" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="48" type="stmt"/>
        <line num="95" count="48" type="stmt"/>
        <line num="96" count="48" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="58" type="cond" truecount="1" falsecount="0"/>
        <line num="99" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="100" count="50" type="stmt"/>
        <line num="101" count="50" type="stmt"/>
        <line num="102" count="58" type="stmt"/>
        <line num="103" count="58" type="stmt"/>
        <line num="104" count="58" type="stmt"/>
        <line num="105" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="106" count="2" type="stmt"/>
        <line num="107" count="2" type="stmt"/>
        <line num="108" count="48" type="stmt"/>
        <line num="109" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="110" count="4" type="stmt"/>
        <line num="111" count="4" type="stmt"/>
        <line num="112" count="48" type="stmt"/>
        <line num="113" count="48" type="stmt"/>
        <line num="114" count="48" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="117" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="118" count="8" type="cond" truecount="3" falsecount="0"/>
        <line num="119" count="8" type="stmt"/>
        <line num="120" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="121" count="8" type="stmt"/>
        <line num="122" count="8" type="stmt"/>
        <line num="123" count="8" type="stmt"/>
        <line num="124" count="8" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="196" type="cond" truecount="1" falsecount="0"/>
        <line num="127" count="196" type="stmt"/>
        <line num="128" count="196" type="stmt"/>
      </file>
      <file name="string.helper.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/string.helper.ts">
        <metrics statements="63" coveredstatements="63" conditionals="17" coveredconditionals="17" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="62" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="62" type="stmt"/>
        <line num="14" count="62" type="stmt"/>
        <line num="15" count="62" type="stmt"/>
        <line num="16" count="62" type="stmt"/>
        <line num="17" count="62" type="stmt"/>
        <line num="18" count="62" type="stmt"/>
        <line num="19" count="62" type="stmt"/>
        <line num="20" count="62" type="stmt"/>
        <line num="21" count="62" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="41" type="cond" truecount="1" falsecount="0"/>
        <line num="24" count="41" type="stmt"/>
        <line num="25" count="41" type="cond" truecount="1" falsecount="0"/>
        <line num="26" count="2" type="stmt"/>
        <line num="27" count="41" type="cond" truecount="2" falsecount="0"/>
        <line num="28" count="9" type="stmt"/>
        <line num="29" count="9" type="stmt"/>
        <line num="30" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="31" count="2" type="stmt"/>
        <line num="32" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="33" count="7" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="35" count="39" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="30" type="cond" truecount="1" falsecount="0"/>
        <line num="37" count="9" type="stmt"/>
        <line num="38" count="9" type="stmt"/>
        <line num="39" count="30" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="8" type="stmt"/>
        <line num="41" count="8" type="stmt"/>
        <line num="42" count="30" type="stmt"/>
        <line num="43" count="32" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="32" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="44" type="cond" truecount="1" falsecount="0"/>
        <line num="52" count="44" type="stmt"/>
        <line num="53" count="44" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="56" count="6" type="stmt"/>
        <line num="57" count="6" type="stmt"/>
        <line num="58" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="59" count="4" type="stmt"/>
        <line num="60" count="4" type="stmt"/>
        <line num="61" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="62" count="2" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
      </file>
      <file name="visitor.ts" path="/home/<USER>/work/ts-command-line-args/ts-command-line-args/src/helpers/visitor.ts">
        <metrics statements="27" coveredstatements="27" conditionals="7" coveredconditionals="7" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="10" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="11" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="12" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="20" type="cond" truecount="1" falsecount="0"/>
        <line num="14" count="20" type="stmt"/>
        <line num="15" count="24" type="stmt"/>
        <line num="16" count="24" type="stmt"/>
        <line num="17" count="24" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="81" type="cond" truecount="1" falsecount="0"/>
        <line num="20" count="81" type="stmt"/>
        <line num="21" count="81" type="stmt"/>
        <line num="22" count="81" type="stmt"/>
        <line num="23" count="81" type="stmt"/>
        <line num="24" count="81" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="22" type="stmt"/>
        <line num="26" count="22" type="stmt"/>
        <line num="27" count="81" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
