{"name": "ts-command-line-args", "version": "2.5.1", "description": "A Typescript wrapper around command-line-args with additional support for markdown usage guide generation", "bin": {"write-markdown": "dist/write-markdown.js"}, "keywords": ["argv", "parse", "argument", "args", "option", "options", "parser", "parsing", "cli", "command", "line", "typescript"], "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "rimraf dist coverage", "build": "tsc -p tsconfig.build.json", "build:example": "tsc -p src/example", "watch-build:example": "tsc -p src/example --watch", "watch-build": "tsc --watch -p tsconfig.build.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest --ci --coverage && tsc --noemit", "watch-test": "jest --watch", "prebuild-release": "npm run clean", "build-release": "concurrently --kill-others-on-fail npm:test npm:lint npm:build npm:build:example npm:verify-markdown", "prewrite-markdown": "npm run build", "preverify-markdown": "npm run build", "write-markdown": "node dist/write-markdown -f package.json -p markdownConfig", "verify-markdown": "node dist/write-markdown -f package.json -p markdownConfig -v", "prepublishOnly": "npm run build --if-present && npm run test --if-present && npm run lint --if-present"}, "repository": {"type": "git", "url": "git+https://github.com/Roaders/ts-command-line-args.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Roaders/ts-command-line-args/issues"}, "homepage": "https://github.com/Roaders/ts-command-line-args#readme", "dependencies": {"chalk": "^4.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "string-format": "^2.0.0"}, "devDependencies": {"@morgan-stanley/ts-mocking-bird": "^0.7.0", "@types/command-line-args": "^5.0.0", "@types/command-line-usage": "^5.0.1", "@types/jest": "^27.5.1", "@types/node": "^16.18.23", "@types/string-format": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "ansi-regex": "^5.0.1", "concurrently": "^6.3.0", "eslint": "^7.7.0", "eslint-config-prettier": "^6.11.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "hosted-git-info": "^2.8.9", "jest": "^27.5.1", "lodash": "^4.17.21", "prettier": "^2.1.1", "rimraf": "^3.0.2", "ts-jest": "^27.1.5", "typescript": "4.3"}, "markdownConfig": {"markdownPath": "README.MD", "jsFile": "dist/write-markdown.constants.js", "verifyMessage": "'{fileName}' is out of date. Please regenerate by running 'npm run write-markdown'", "removeDoubleBlankLines": true, "insertCodeBelow": "[//]: # (ts-command-line-args_readme-generation_insertCodeBelow", "insertCodeAbove": "[//]: # (ts-command-line-args_readme-generation_insertCodeAbove)"}}