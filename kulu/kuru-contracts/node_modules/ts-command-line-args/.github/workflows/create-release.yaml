name: Create Release
on:
  push:
    branches:
      - main

jobs:
  build:
    name: Create Release
    runs-on: ubuntu-latest
    steps:

      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: '16.x'

      - name: Install node modules and verify build
        run: npm ci && npm run build-release

      - name: Release
        uses: justincy/github-action-npm-release@2.0.2
        id: release

      - name: Print release output
        if: ${{ steps.release.outputs.released == 'true' }}
        run: echo Release ID ${{ steps.release.outputs.release_id }}

      - name: Publish
        if: steps.release.outputs.released == 'true'
        uses: JS-DevTools/npm-publish@v1
        with:
          token: ${{ secrets.NPM_TOKEN }}
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v2