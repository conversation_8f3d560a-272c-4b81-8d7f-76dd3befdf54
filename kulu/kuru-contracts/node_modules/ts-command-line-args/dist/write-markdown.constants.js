"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.usageGuideInfo = exports.parseOptions = exports.argumentConfig = exports.footerReplaceBelowMarker = exports.configImportNameDefault = exports.copyCodeAboveDefault = exports.copyCodeBelowDefault = exports.insertCodeAboveDefault = exports.insertCodeBelowDefault = exports.replaceAboveDefault = exports.replaceBelowDefault = void 0;
exports.replaceBelowDefault = "[//]: ####ts-command-line-args_write-markdown_replaceBelow";
exports.replaceAboveDefault = "[//]: ####ts-command-line-args_write-markdown_replaceAbove";
exports.insertCodeBelowDefault = "[//]: # (ts-command-line-args_write-markdown_insertCodeBelow";
exports.insertCodeAboveDefault = "[//]: # (ts-command-line-args_write-markdown_insertCodeAbove)";
exports.copyCodeBelowDefault = "// ts-command-line-args_write-markdown_copyCodeBelow";
exports.copyCodeAboveDefault = "// ts-command-line-args_write-markdown_copyCodeAbove";
exports.configImportNameDefault = "usageGuideInfo";
exports.footerReplaceBelowMarker = "[//]: ####ts-command-line-args_generated-by-footer";
exports.argumentConfig = {
    markdownPath: {
        type: String,
        alias: 'm',
        defaultOption: true,
        description: 'The file to write to. Without replacement markers the whole file content will be replaced. Path can be absolute or relative.',
    },
    replaceBelow: {
        type: String,
        defaultValue: exports.replaceBelowDefault,
        description: "A marker in the file to replace text below.",
        optional: true,
    },
    replaceAbove: {
        type: String,
        defaultValue: exports.replaceAboveDefault,
        description: "A marker in the file to replace text above.",
        optional: true,
    },
    insertCodeBelow: {
        type: String,
        defaultValue: exports.insertCodeBelowDefault,
        description: "A marker in the file to insert code below. File path to insert must be added at the end of the line and optionally codeComment flag: 'insertToken file=\"path/toFile.md\" codeComment=\"ts\"'",
        optional: true,
    },
    insertCodeAbove: {
        type: String,
        defaultValue: exports.insertCodeAboveDefault,
        description: "A marker in the file to insert code above.",
        optional: true,
    },
    copyCodeBelow: {
        type: String,
        defaultValue: exports.copyCodeBelowDefault,
        description: "A marker in the file being inserted to say only copy code below this line",
        optional: true,
    },
    copyCodeAbove: {
        type: String,
        defaultValue: exports.copyCodeAboveDefault,
        description: "A marker in the file being inserted to say only copy code above this line",
        optional: true,
    },
    jsFile: {
        type: String,
        optional: true,
        alias: 'j',
        description: "jsFile to 'require' that has an export with the 'UsageGuideConfig' export. Multiple files can be specified.",
        multiple: true,
    },
    configImportName: {
        type: String,
        alias: 'c',
        defaultValue: [exports.configImportNameDefault],
        description: "Export name of the 'UsageGuideConfig' object. Defaults to '" + exports.configImportNameDefault + "'. Multiple exports can be specified.",
        multiple: true,
    },
    verify: {
        type: Boolean,
        alias: 'v',
        description: "Verify the markdown file. Does not update the file but returns a non zero exit code if the markdown file is not correct. Useful for a pre-publish script.",
    },
    configFile: {
        type: String,
        alias: 'f',
        optional: true,
        description: "Optional config file to load config from. package.json can be used if jsonPath specified as well",
    },
    jsonPath: {
        type: String,
        alias: 'p',
        optional: true,
        description: "Used in conjunction with 'configFile'. The path within the config file to load the config from. For example: 'configs.writeMarkdown'",
    },
    verifyMessage: {
        type: String,
        optional: true,
        description: "Optional message that is printed when markdown verification fails. Use '\\{fileName\\}' to refer to the file being processed.",
    },
    removeDoubleBlankLines: {
        type: Boolean,
        description: 'When replacing content removes any more than a single blank line',
    },
    skipFooter: {
        type: Boolean,
        description: "Does not add the 'Markdown Generated by...' footer to the end of the markdown",
    },
    help: { type: Boolean, alias: 'h', description: "Show this usage guide." },
};
exports.parseOptions = {
    helpArg: 'help',
    loadFromFileArg: 'configFile',
    loadFromFileJsonPathArg: 'jsonPath',
    baseCommand: "write-markdown",
    optionsHeaderLevel: 3,
    defaultSectionHeaderLevel: 3,
    optionsHeaderText: "write-markdown cli options",
    headerContentSections: [
        {
            header: 'Markdown Generation',
            headerLevel: 2,
            content: "A markdown version of the usage guide can be generated and inserted into an existing markdown document.\nMarkers in the document describe where the content should be inserted, existing content betweeen the markers is overwritten.",
        },
        {
            content: "{highlight write-markdown -m README.MD -j usageGuideConstants.js}",
        },
    ],
    footerContentSections: [
        {
            header: 'Default Replacement Markers',
            content: "replaceBelow defaults to:\n{code '" + exports.replaceBelowDefault + "'}\nreplaceAbove defaults to:\n{code '" + exports.replaceAboveDefault + "'}\ninsertCodeBelow defaults to:\n{code '" + exports.insertCodeBelowDefault + "'}\ninsertCodeAbove defaults to:\n{code '" + exports.insertCodeAboveDefault + "'}\ncopyCodeBelow defaults to:\n{code '" + exports.copyCodeBelowDefault + "'}\ncopyCodeAbove defaults to:\n{code '" + exports.copyCodeAboveDefault + "'}",
        },
    ],
};
exports.usageGuideInfo = {
    arguments: exports.argumentConfig,
    parseOptions: exports.parseOptions,
};
