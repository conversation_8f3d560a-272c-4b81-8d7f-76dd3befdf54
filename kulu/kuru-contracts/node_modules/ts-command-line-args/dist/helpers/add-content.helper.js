"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from) {
    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)
        to[j] = from[i];
    return to;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addCommandLineArgsFooter = exports.addContent = void 0;
var write_markdown_constants_1 = require("../write-markdown.constants");
var line_ending_helper_1 = require("./line-ending.helper");
/**
 * Adds or replaces content between 2 markers within a text string
 * @param inputString
 * @param content
 * @param options
 * @returns
 */
function addContent(inputString, content, options) {
    var replaceBelow = options === null || options === void 0 ? void 0 : options.replaceBelow;
    var replaceAbove = options === null || options === void 0 ? void 0 : options.replaceAbove;
    content = Array.isArray(content) ? content : [content];
    var lineBreak = line_ending_helper_1.findEscapeSequence(inputString);
    var lines = line_ending_helper_1.splitContent(inputString);
    var replaceBelowLine = replaceBelow != null ? lines.filter(function (line) { return line.indexOf(replaceBelow) === 0; })[0] : undefined;
    var replaceBelowIndex = replaceBelowLine != null ? lines.indexOf(replaceBelowLine) : -1;
    var replaceAboveLine = replaceAbove != null ? lines.filter(function (line) { return line.indexOf(replaceAbove) === 0; })[0] : undefined;
    var replaceAboveIndex = replaceAboveLine != null ? lines.indexOf(replaceAboveLine) : -1;
    if (replaceAboveIndex > -1 && replaceBelowIndex > -1 && replaceAboveIndex < replaceBelowIndex) {
        throw new Error("The replaceAbove marker '" + options.replaceAbove + "' was found before the replaceBelow marker '" + options.replaceBelow + "'. The replaceBelow marked must be before the replaceAbove.");
    }
    var linesBefore = lines.slice(0, replaceBelowIndex + 1);
    var linesAfter = replaceAboveIndex >= 0 ? lines.slice(replaceAboveIndex) : [];
    var contentLines = content.reduce(function (lines, currentContent) { return __spreadArray(__spreadArray([], lines), line_ending_helper_1.splitContent(currentContent)); }, new Array());
    var allLines = __spreadArray(__spreadArray(__spreadArray([], linesBefore), contentLines), linesAfter);
    if (options.removeDoubleBlankLines) {
        allLines = allLines.filter(function (line, index, lines) { return line_ending_helper_1.filterDoubleBlankLines(line, index, lines); });
    }
    return allLines.join(lineBreak);
}
exports.addContent = addContent;
function addCommandLineArgsFooter(fileContent) {
    if (fileContent.indexOf(write_markdown_constants_1.footerReplaceBelowMarker) < 0) {
        fileContent = fileContent + "  \n\n" + write_markdown_constants_1.footerReplaceBelowMarker;
    }
    var footerContent = "Markdown Generated by [ts-command-line-args](https://www.npmjs.com/package/ts-command-line-args)";
    return addContent(fileContent, footerContent, {
        replaceBelow: write_markdown_constants_1.footerReplaceBelowMarker,
        removeDoubleBlankLines: false,
    });
}
exports.addCommandLineArgsFooter = addCommandLineArgsFooter;
