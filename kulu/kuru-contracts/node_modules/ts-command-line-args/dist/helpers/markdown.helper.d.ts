import { UsageGuideConfig, IWriteMarkDown, ArgumentConfig, CommandLineOption, ParseOptions, Content, HeaderLevel, OptionContent, SectionHeader } from '../contracts';
export declare function createUsageGuide<T = any>(config: UsageGuideConfig<T>): string;
export declare function createSection<T>(section: Content, config: UsageGuideConfig<T>): string;
export declare function createSectionContent(section: Content): string;
export declare function createSectionTable(rows: any[]): string;
export declare function createOptionsSections<T>(cliArguments: ArgumentConfig<T>, options: ParseOptions<any>): string[];
export declare function createOptionsSection(optionList: CommandLineOption<any>[], content: OptionContent, options: ParseOptions<any>): string;
export declare function createHeading(section: SectionHeader, defaultLevel: HeaderLevel): string;
export declare function createOptionRow(option: CommandLineOption, includeAlias?: boolean, includeDescription?: boolean): string;
export declare function getType(option: CommandLineOption): string;
export declare function generateUsageGuides(args: IWriteMarkDown): string[] | undefined;
export declare function loadArgConfig(jsFile: string, importName: string): UsageGuideConfig | undefined;
