{"name": "@typechain/ethers-v5", "description": "🔌 TypeChain target for ethers-v5", "keywords": ["ethers", "et<PERSON><PERSON>s", "ethereum", "TypeChain", "TypeScript"], "version": "10.2.1", "license": "MIT", "repository": "https://github.com/ethereum-ts/Typechain", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**", "static/**", "README.md", "LICENSE"], "peerDependencies": {"@ethersproject/abi": "^5.0.0", "@ethersproject/providers": "^5.0.0", "ethers": "^5.1.3", "typechain": "^8.1.1", "typescript": ">=4.3.0"}, "devDependencies": {"@ethersproject/abi": "^5.0.0", "@ethersproject/bytes": "^5.0.0", "@ethersproject/providers": "^5.0.0", "@types/lodash": "^4.14.139", "ethers": "^5.1.3", "test-utils": "1.0.0", "typechain": "^8.1.1", "typescript": ">=4.3.0", "@types/proxyquire": "^1.3.28", "proxyquire": "^2.1.3"}, "dependencies": {"lodash": "^4.17.15", "ts-essentials": "^7.0.1"}, "scripts": {"start": "ts-node -T ./src/index.ts", "build": "tsc --build ./tsconfig.build.json --verbose", "format": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --check \"./**/*.ts\" README.md", "format:fix": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --write \"./**/*.ts\" README.md", "lint": "eslint --ext .ts src test", "lint:fix": "pnpm lint --fix", "typecheck": "tsc --noEmit --incremental false --composite false", "clean": "rm -rf dist && rm -f tsconfig.build.tsbuildinfo", "test": "mocha --config ../../.mocharc.js", "test:fix": "pnpm lint:fix && pnpm format:fix && pnpm test && pnpm typecheck"}}