{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/codegen/types.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAGhC,sCAAuE;AAOvE,SAAgB,kBAAkB,CAAC,KAA0B,EAAE,OAA4B;IACzF,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,EAAE,CAAA;KACV;IACD,OAAO,CACL,KAAK;SACF,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,MAAM,KAAK,EAAE,KAAK,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;SAClG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CACrB,CAAA;AACH,CAAC;AATD,gDASC;AAED,SAAgB,mBAAmB,CAAC,OAA4B,EAAE,OAAkC;IAClG,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACvD,OAAO,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;KACpD;SAAM;QACL,OAAO,yBAAyB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;KACnD;AACH,CAAC;AAND,kDAMC;AAED,gEAAgE;AAChE,SAAgB,iBAAiB,CAAC,OAA4B,EAAE,OAAgB;IAC9E,QAAQ,OAAO,CAAC,IAAI,EAAE;QACpB,KAAK,SAAS;YACZ,OAAO,oBAAoB,CAAC,cAAc,CAAC,CAAA;QAC7C,KAAK,UAAU;YACb,OAAO,oBAAoB,CAAC,cAAc,CAAC,CAAA;QAC7C,KAAK,SAAS;YACZ,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QACvC,KAAK,OAAO,CAAC;QACb,KAAK,eAAe;YAClB,OAAO,oBAAoB,CAAC,WAAW,CAAC,CAAA;QAC1C,KAAK,OAAO;YACV,OAAO,wBAAwB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAC7F,KAAK,SAAS;YACZ,OAAO,oBAAoB,CAAC,SAAS,CAAC,CAAA;QACxC,KAAK,QAAQ;YACX,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QACvC,KAAK,OAAO;YACV,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE;gBAC5C,OAAO,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,6BAAoB,CAAA;aAC5D;YACD,OAAO,yBAAyB,CAAC,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;QAC3G,KAAK,SAAS;YACZ,OAAO,KAAK,CAAA;KACf;AACH,CAAC;AAzBD,8CAyBC;AAED,SAAgB,kBAAkB,CAAC,OAA4B,EAAE,OAAsB;IACrF,QAAQ,OAAO,CAAC,IAAI,EAAE;QACpB,KAAK,SAAS,CAAC;QACf,KAAK,UAAU;YACb,OAAO,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAA;QACpD,KAAK,SAAS;YACZ,OAAO,QAAQ,CAAA;QACjB,KAAK,MAAM;YACT,OAAO,MAAM,CAAA;QACf,KAAK,OAAO,CAAC;QACb,KAAK,eAAe;YAClB,OAAO,QAAQ,CAAA;QACjB,KAAK,OAAO;YACV,OAAO,wBAAwB,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9F,KAAK,SAAS;YACZ,OAAO,SAAS,CAAA;QAClB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAA;QACjB,KAAK,OAAO;YACV,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE;gBAC5C,OAAO,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,8BAAqB,CAAA;aAC7D;YACD,OAAO,yBAAyB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,GAAG,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAA;QACxF,KAAK,SAAS;YACZ,OAAO,KAAK,CAAA;KACf;AACH,CAAC;AA1BD,gDA0BC;AAED,SAAgB,yBAAyB,CAAC,KAAgB,EAAE,SAAuC;IACjG,OAAO,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;AACtH,CAAC;AAFD,8DAEC;AAED;;;IAGI;AACJ,SAAgB,yBAAyB,CAAC,UAAgC,EAAE,OAA4B;IACtG,MAAM,wBAAwB,GAAG,IAAA,gBAAO,EAAC;QACvC,gCAAgC,CAAC,UAAU,EAAE,OAAO,CAAC;QACrD,kCAAkC,CAAC,UAAU,EAAE,OAAO,CAAC;KACxD,CAAC,CAAA;IACF,OAAO,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC7C,CAAC;AAND,8DAMC;AAED,SAAgB,gCAAgC,CAC9C,UAAgC,EAChC,OAA4B;IAE5B,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;AACrF,CAAC;AALD,4EAKC;AAED,SAAgB,kCAAkC,CAChD,UAAgC,EAChC,OAA4B;IAE5B,IAAI,iBAAiB,CAAA;IACrB,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACxD,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5B,iBAAiB;YACf,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;KACxG;IAED,OAAO,iBAAiB,CAAA;AAC1B,CAAC;AAZD,gFAYC;AAED,SAAS,wBAAwB,CAAC,IAAY,EAAE,MAAe;IAC7D,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,EAAE;QACtC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;KAClD;SAAM;QACL,OAAO,GAAG,IAAI,IAAI,CAAA;KACnB;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW;IACvC,OAAO,kBAAkB,GAAG,GAAG,CAAA;AACjC,CAAC"}