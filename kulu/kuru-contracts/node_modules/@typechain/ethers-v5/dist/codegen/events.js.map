{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../../src/codegen/events.ts"], "names": [], "mappings": ";;;AAAA,yCAKkB;AAElB,mCAAiH;AAEjH,SAAgB,oBAAoB,CAAC,MAA0B;IAC7D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;QACvB,MAAM,gBAAgB,GAAG,GAAG,uBAAuB,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAA;QAE9F,OAAO;SACF,sBAAsB,CAAC,KAAK,CAAC,KAAK,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,gBAAgB;QAC1F,KAAK,CAAC,IAAI,IAAI,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,gBAAgB;KACxE,CAAA;KACF;SAAM;QACL,OAAO,MAAM;aACV,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,gBAAgB,GAAG,GAAG,uBAAuB,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAA;YAE7F,OAAO,IAAI,sBAAsB,CAAC,KAAK,CAAC,KAAK,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAA;QACzG,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;KACd;AACH,CAAC;AAlBD,oDAkBC;AAED,SAAgB,wBAAwB,CAAC,MAA0B;IACjE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;KACjD;SAAM;QACL,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KACtE;AACH,CAAC;AAND,4DAMC;AAED,SAAgB,uBAAuB,CAAC,KAAuB,EAAE,eAAwB;IACvF,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,WAAC,OAAA,CAAC,EAAE,IAAI,EAAE,MAAA,KAAK,CAAC,IAAI,mCAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA,EAAA,CAAC,CAAA;IACnH,MAAM,WAAW,GAAG,IAAA,wCAAgC,EAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAA;IACtF,MAAM,YAAY,GAAG,IAAA,0CAAkC,EAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,CAAA;IAEjG,MAAM,UAAU,GAAG,uBAAuB,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,CAAC,CAAA;IAEtE,OAAO;uBACc,UAAU,UAAU,YAAY;kBACrC,UAAU,iBAAiB,WAAW,KAAK,UAAU;;kBAErD,UAAU,6BAA6B,UAAU;GAChE,CAAA;AACH,CAAC;AAbD,0DAaC;AAED,SAAgB,iCAAiC,CAAC,KAAuB;IACvE,OAAO,IAAI,sBAAsB,CAAC,KAAK,CAAC,mBAAmB,CAAA;AAC7D,CAAC;AAFD,8EAEC;AAED,SAAgB,sBAAsB,CAAC,KAAuB;IAC5D,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;AAChG,CAAC;AAFD,wDAEC;AAED,SAAgB,mBAAmB,CAAC,SAAgC;IAClE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,EAAE,CAAA;KACV;IACD,OAAO,CACL,SAAS;SACN,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;QAClB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,sCAA0B,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,MAAM,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAA;IAC5G,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CACrB,CAAA;AACH,CAAC;AAXD,kDAWC;AAED,SAAgB,oBAAoB,CAAC,QAA6B;IAChE,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAA,yBAAiB,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAA;AACzG,CAAC;AAFD,oDAEC;AAED,SAAgB,gBAAgB,CAAC,KAAuB,EAAE,YAAqB;IAC7E,OAAO,qCACL,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IACvD,oBAAoB,CAAA;AACtB,CAAC;AAJD,4CAIC;AAED,SAAS,uBAAuB,CAAC,KAAuB,EAAE,EAAE,eAAe,KAAoC,EAAE;IAC/G,IAAI,eAAe,EAAE;QACnB,OAAO,IAAA,4CAAgC,EAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;KAC1D;SAAM;QACL,OAAO,KAAK,CAAC,IAAI,GAAG,OAAO,CAAA;KAC5B;AACH,CAAC;AAEY,QAAA,sBAAsB,GAAG;;;;;;;;;;;;;;;CAerC,CAAA;AAEY,QAAA,aAAa,GAAG,CAAC,kBAAkB,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA"}