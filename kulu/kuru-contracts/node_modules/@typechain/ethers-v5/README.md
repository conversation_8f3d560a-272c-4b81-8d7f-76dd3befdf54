# Typechain target Ethers-v5

<p align="center">
  <img src="https://github.com/Neufund/TypeChain/blob/d82f3cc644a11e22ca8e42505c16f035e2f2555d/docs/images/typechain-logo.png?raw=true" width="300" alt="TypeChain">
  <h3 align="center">TypeChain target Ethers-v5</h3>
  <p align="center">🔌 TypeScript bindings for Ethers 5.x.x smartcontracts</p>

  <p align="center">
    <a href="https://github.com/ethereum-ts/TypeChain/actions"><img alt="Build Status" src="https://github.com/ethereum-ts/TypeChain/workflows/CI/badge.svg"></a>
    <img alt="Downloads" src="https://img.shields.io/npm/dm/typechain.svg">
    <a href="https://github.com/prettier/prettier"><img alt="Prettier" src="https://img.shields.io/badge/code_style-prettier-ff69b4.svg"></a>
    <a href="/package.json"><img alt="Software License" src="https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square"></a>
  </p>

  <p align="center">
    <a href="https://blog.neufund.org/introducing-typechain-typescript-bindings-for-ethereum-smart-contracts-839fc2becf22">Medium post</a> | <a href="https://www.youtube.com/watch?v=9x6AkShGkwU">DappCon Video</a>
  </p>
</p>

This package requires TypeScript >= 4.0. If you need support for earlier TS versions check out: 1.0 version of this
package.

## [TypeChain readme](https://github.com/ethereum-ts/TypeChain)

## Contract typings

The main files generated by this target are `<contract-name>.ts`. They declare typesafe interfaces for your contracts
on top of ethers `Contract` instances:

- typed contract's methods, available both at `contract.someMethod(...)` and `contract.functions.someMethod(...)`
- typed events in `contract.interface.events.AnEvent` and filters in `contract.filters.AnEvent`
- typed method gas estimates in `contract.estimateGas.someMethod`
- overrides for the event listener methods (`on`, `once`, etc) that return the same contract type.

Note: these are just _type declarations_ to help you call the blockchain properly, so they're not available at runtime,
and all of the contracts are still instances of the same `Contract` class.

## Contract factories

This target also generates a concrete factory class for each contract, to help you deploy or connect to contract
instances. The factory classes are an extension of ethers' `ContractFactory`. They serve two main purposes:

- wrap passing contract ABI and bytecode to the `ContractFactory` class, so you don't have to load and parse the JSON
  manually
- provide a correctly typed interface to `ContractFactory` (since it returns plain `Contract` instances).

Abstract contracts or solidity interfaces are handled a bit different, because they have no bytecode. For those, a
simplified factory is generated that doesn't extends `ContractFactory`, and only includes the static `connect` method,
so you can easily connect to a deployed instance without having to pass the ABI manually.

## Basic example

Suppose you have an `Erc20Token.sol` solidity interface and a `DummyToken.sol` contract implementing it.

```typescript
import { BigNumber } from 'ethers';
import { Wallet } from 'ethers';

import { DummyTokenFactory } from 'typechain-out-dir/DummyTokenFactory';
import { DummyToken } from 'typechain-out-dir/DummyToken';
import { Erc20TokenFactory } from 'typechain-out-dir/Erc20TokenFactory';

const provider = getYourProvider(...);

// use the concrete contract factory if you need to operate on the bytecode (ie. deploy)
async function deployTestToken(ownerPK: string): Promise<DummyToken> {
    const owner = new Wallet(ownerPK, provider);
    return new DummyTokenFactory(owner).deploy();
}

// to call existing contracts, a factory for both the concrete contract and for the interface
// can be used since the ABI is the same
async function getTokenBalance(walletAddress: string, tokenAddress: string): Promise<BigNumber> {
    const token = Erc20TokenFactory.connect(tokenAddress, provider);
    return token.balanceOf(walletAddress);
}
```
