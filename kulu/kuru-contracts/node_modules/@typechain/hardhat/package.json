{"name": "@typechain/hardhat", "description": "Zero-config TypeChain support for Hardhat", "keywords": ["TypeScript", "hardhat", "plugin", "typechain", "ethereum", "bindings", "smartcontract", "blockchain"], "version": "6.1.6", "license": "MIT", "repository": "https://github.com/ethereum-ts/Typechain", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**/*"], "dependencies": {"fs-extra": "^9.1.0"}, "devDependencies": {"@ethersproject/abi": "^5.4.7", "@ethersproject/providers": "^5.4.7", "@nomiclabs/hardhat-ethers": "^2.0.2", "@typechain/ethers-v5": "^10.2.1", "@types/fs-extra": "^9.0.7", "@types/rimraf": "^3.0.0", "ethers": "^5.4.7", "hardhat": "^2.9.9", "rimraf": "^3.0.2", "typechain": "^8.1.1", "typescript": "^4"}, "peerDependencies": {"@ethersproject/abi": "^5.4.7", "@ethersproject/providers": "^5.4.7", "@typechain/ethers-v5": "^10.2.1", "ethers": "^5.4.7", "hardhat": "^2.9.9", "typechain": "^8.1.1"}, "scripts": {"format": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --check \"./**/*.ts\" README.md", "format:fix": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --write \"./**/*.ts\"  README.md", "lint": "eslint --ext .ts src test", "lint:fix": "pnpm lint --fix", "typecheck": "tsc --noEmit --incremental false --composite false", "clean": "rm -rf dist && rm -f tsconfig.build.tsbuildinfo", "test": "mocha --config ../../.mocharc.js", "test:fix": "pnpm lint:fix && pnpm format:fix && pnpm test && pnpm typecheck"}}