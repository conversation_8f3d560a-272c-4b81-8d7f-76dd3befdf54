{"author": "<PERSON> <<EMAIL>>", "browser": "./dist/ethers.min.js", "dependencies": {"aes-js": "3.0.0", "bn.js": "^4.11.9", "elliptic": "6.5.4", "hash.js": "1.1.3", "js-sha3": "0.5.7", "scrypt-js": "2.0.4", "setimmediate": "1.0.4", "uuid": "2.0.1", "xmlhttprequest": "1.8.0"}, "description": "Ethereum wallet library.", "devDependencies": {"@types/node": "10.17.28", "browserify": "^16.2.3", "browserify-zlib": "^0.2.0", "dts-bundle": "^0.7.3", "eslint": "^5.16.0", "eslint-plugin-promise": "^3.8.0", "ethereumjs-tx": "^1.3.5", "ethereumjs-util": "^5.2.0", "gulp": "^4.0.0", "gulp-cli": "^2.0.1", "gulp-sourcemaps": "^2.6.4", "gulp-typescript": "^5.0.0-alpha.1", "gulp-uglify": "^3.0.0", "mocha": "^5.2.0", "mocha-phantomjs-core": "2.1.2", "solc": "^0.7.0", "tsify": "^4.0.0", "tslint": "^5.10.0", "typescript": "^2.9.1", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0", "web3-providers-http": "1.0.0-beta.35"}, "ethereum": "donations.ethers.eth", "gitHead": "bd5e39edf8f42d226c13f906f44ac40f9a173405", "keywords": ["ethereum", "library", "wallet"], "license": "MIT", "main": "./index.js", "name": "ethers", "publishConfig": {"access": "public", "tag": "legacy"}, "repository": {"type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"auto-build": "npm run build -- -w", "build": "npm run dist-version && tsc -p ./tsconfig.json", "dist": "npm run dist-version && npm run build && gulp default minified shims && npm run dist-types", "dist-bip39": "gulp bip39-es bip39-fr bip39-it bip39-ja bip39-ko bip39-zh", "dist-test": "gulp default-test minified-test shims", "dist-types": "dts-bundle --name ethers --main ./index.d.ts --out ./dist/ethers.types.txt", "dist-version": "node -e \"let v = require('./package.json').version; require('fs').writeFileSync('./src.ts/_version.ts', 'export const version = \\\"' + v +'\\\";\\n')\"", "eslint": "eslint index.js contracts/*.js providers/*.js utils/*.js wallet/*.js wordlists/*.js", "test": "if [ \"$RUN_PHANTOMJS\" = \"1\" ]; then npm run-script test-phantomjs; else npm run-script test-node; fi", "test-node": "npm run dist-test && mocha --no-colors --reporter tests/reporter tests/test-*.js", "test-phantomjs": "npm run dist-test && gulp tests && phantomjs --web-security=false ./node_modules/mocha-phantomjs-core/mocha-phantomjs-core.js ./tests/test.html ./tests/reporter.js", "version": "npm dist"}, "tarballHash": "0xa8d5cf6c8e254c1d8caad8d67adaf8f3c0d64200ed3f1a5900cc6bcf2ae41106", "types": "./index.d.ts", "version": "4.0.49"}