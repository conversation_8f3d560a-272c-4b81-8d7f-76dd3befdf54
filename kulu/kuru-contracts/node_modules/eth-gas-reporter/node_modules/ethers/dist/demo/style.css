body {
    background-color: #eee;
    font-family: sans-serif;
    font-size: 18px;
    margin: 0;
}

.centerer {
    margin-left: 50%;
    min-height: 100%;
}

.centered:before {
    box-shadow: -10px 0 15px -10px #999 inset;
    content: " ";
    height: 100%;
    left: -10px;
    position: absolute;
    top: 0;
    width: 10px;
}

.centered:after {
    box-shadow: 10px 0 15px -10px #999 inset;
    content: " ";
    height: 100%;
    right: -10px;
    position: absolute;
    top: 0;
    width: 10px;
}

.centered {
    background-color: #fff;
    border-left: 1px solid #888;
    border-right: 1px solid #888;
    dbox-shadow: -1px 0 15px 0 #999;
    margin-left: -370px;
    min-height: 100%;
    padding: 20px;
    position: relative;
    width: 700px;
}

.hidden {
    display: none;
}

p {
    text-align: justify;
    margin-bottom: 30px;
}
th {
    text-align: left;
    padding: 0 15px 15px 0;
}
td {
    padding: 0 15px 15px 0;
}

input[type=text] {
    border: 1px solid #555;
    font-size: 16px;
    padding: 10px;
    width: 501px;
}

input[type=password] {
    border: 1px solid #555;
    font-size: 16px;
    padding: 10px;
    width: 501px;
}

input[type=file] {
    dbackground: #fff;
    border: 1px solid #555;
    cursor: pointer;
    font-size: 16px;
    opacity: 0;
    padding: 10px;
    position: relative;
    dvisibility: hidden;
    width: 501px;
}

input[type=text].readonly {
    border: 1px solid #ccc;
    color: #888;
}

.file {
    border: 1px solid green;
    color: #444;
    font-size: 12px;
    line-height: 16px;
    margin-top: 2px;
    padding: 13px 10px 12px;
    position: absolute;
    text-align: center;
    width: 478px;
}

.file.highlight {
    box-shadow: 0px 0px 5px #888;
}

.clearfix {
    clear: both;
}

.option {
    border: 1px solid #999;
    box-sizing: border-box;
    cursor: pointer;
    float: left;
    font-size: 16px;
    opacity: 0.3;
    padding: 10px;
    margin-right: 20px;
    text-align: center;
    transition: opacity 0.1s linear;
    width: 110px;
}

.option span {
    font-size: 0.8em;
    opacity: 0.5;
}

.option.selected {
    opacity: 1;
}

.option:hover {
    box-shadow: 0px 0px 5px #888;
    opacity: 1;
}

table {
    margin-bottom: 40px;
}

div.activity {
    font-family: monospace;
    margin-bottom: 40px;
}

div.activity a {
    display: block;
    text-decoration: none;
}

.username {
    color: #888;
    font-size: 14px;
}

.submit {
    border: 1px solid #555;
    box-shadow: 0px 0px 5px #888;
    cursor: pointer;
    font-size: 16px;
    padding: 10px;
    text-align: center;
    transition: opacity 0.1s linear;
    width: 480px;
}

.submit:hover {
    border: 1px solid #999;
    box-shadow: 0px 0px 5px #aaa;
}

.submit:active {
    box-shadow: none;
}

.submit.disable {
    box-shadow: none;
    opacity: 0.5;
}

.submit.disable:hover {
    border: 1px solid #555;
    box-shadow: none;
}

.submit.disable:active {
    box-shadow: none;
}

.left {
    float: left;
}

.right {
    float: right;
}
