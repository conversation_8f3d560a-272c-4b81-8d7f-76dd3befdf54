// Generated by dts-bundle v0.7.3

declare module 'ethers' {
    import * as ethers from 'ethers/ethers';
    export { ethers };
    export * from 'ethers/ethers';
}

declare module 'ethers/ethers' {
    import { Contract, ContractFactory, VoidSigner } from 'ethers/contract';
    import { Signer } from 'ethers/abstract-signer';
    import { Wallet } from 'ethers/wallet';
    import * as constants from 'ethers/constants';
    import * as errors from 'ethers/errors';
    import * as providers from 'ethers/providers';
    import * as utils from 'ethers/utils';
    import * as wordlists from 'ethers/wordlists';
    import { platform } from 'ethers/utils/shims';
    import { version } from 'ethers/_version';
    import { ContractFunction, ContractTransaction, Event, EventFilter } from 'ethers/contract';
    function getDefaultProvider(network?: utils.Network | string): providers.BaseProvider;
    export { Signer, Wallet, VoidSigner, getDefaultProvider, providers, Contract, ContractFactory, constants, errors, utils, wordlists, platform, version, ContractFunction, ContractTransaction, Event, EventFilter };
}

declare module 'ethers/contract' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Indexed, Interface } from 'ethers/utils/interface';
    import { UnsignedTransaction } from 'ethers/utils/transaction';
    import { BlockTag, Provider } from 'ethers/providers/abstract-provider';
    import { Signer } from 'ethers/abstract-signer';
    import { Arrayish } from 'ethers/utils/bytes';
    import { EventFragment, FunctionFragment, ParamType } from 'ethers/utils/abi-coder';
    import { Block, Listener, Log, TransactionReceipt, TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    export type ContractFunction = (...params: Array<any>) => Promise<any>;
    export type EventFilter = {
        address?: string;
        topics?: Array<string>;
    };
    export interface Event extends Log {
        args?: Array<any>;
        decode?: (data: string, topics?: Array<string>) => any;
        event?: string;
        eventSignature?: string;
        removeListener: () => void;
        getBlock: () => Promise<Block>;
        getTransaction: () => Promise<TransactionResponse>;
        getTransactionReceipt: () => Promise<TransactionReceipt>;
    }
    export interface ContractReceipt extends TransactionReceipt {
        events?: Array<Event>;
    }
    export interface ContractTransaction extends TransactionResponse {
        wait(confirmations?: number): Promise<ContractReceipt>;
    }
    export class VoidSigner extends Signer {
        readonly address: string;
        constructor(address: string, provider: Provider);
        getAddress(): Promise<string>;
        _fail(message: string, operation: string): Promise<any>;
        signMessage(message: Arrayish | string): Promise<string>;
        sendTransaction(transaction: TransactionRequest): Promise<TransactionResponse>;
        connect(provider: Provider): VoidSigner;
    }
    interface Bucket<T> {
        [name: string]: T;
    }
    export class Contract {
        readonly address: string;
        readonly interface: Interface;
        readonly signer: Signer;
        readonly provider: Provider;
        readonly estimate: Bucket<(...params: Array<any>) => Promise<BigNumber>>;
        readonly functions: Bucket<ContractFunction>;
        readonly filters: Bucket<(...params: Array<any>) => EventFilter>;
        readonly [name: string]: ContractFunction | any;
        readonly addressPromise: Promise<string>;
        readonly deployTransaction: TransactionResponse;
        constructor(addressOrName: string, contractInterface: Array<string | FunctionFragment | EventFragment | ParamType> | string | Interface, signerOrProvider: Signer | Provider);
        deployed(): Promise<Contract>;
        _deployed(blockTag?: BlockTag): Promise<Contract>;
        fallback(overrides?: TransactionRequest): Promise<TransactionResponse>;
        connect(signerOrProvider: Signer | Provider | string): Contract;
        attach(addressOrName: string): Contract;
        static isIndexed(value: any): value is Indexed;
        on(event: EventFilter | string, listener: Listener): Contract;
        once(event: EventFilter | string, listener: Listener): Contract;
        addListener(eventName: EventFilter | string, listener: Listener): Contract;
        emit(eventName: EventFilter | string, ...args: Array<any>): boolean;
        listenerCount(eventName?: EventFilter | string): number;
        listeners(eventName: EventFilter | string): Array<Listener>;
        removeAllListeners(eventName: EventFilter | string): Contract;
        removeListener(eventName: any, listener: Listener): Contract;
    }
    export class ContractFactory {
        readonly interface: Interface;
        readonly bytecode: string;
        readonly signer: Signer;
        constructor(contractInterface: Array<string | FunctionFragment | EventFragment | ParamType> | string | Interface, bytecode: Arrayish | string | {
            object: string;
        }, signer?: Signer);
        getDeployTransaction(...args: Array<any>): UnsignedTransaction;
        deploy(...args: Array<any>): Promise<Contract>;
        attach(address: string): Contract;
        connect(signer: Signer): ContractFactory;
        static fromSolidity(compilerOutput: any, signer?: Signer): ContractFactory;
    }
    export {};
}

declare module 'ethers/abstract-signer' {
    import { Provider } from 'ethers/providers/abstract-provider';
    import { Arrayish } from 'ethers/utils/bytes';
    import { TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    export abstract class Signer {
        readonly provider?: Provider;
        abstract getAddress(): Promise<string>;
        abstract signMessage(message: Arrayish | string): Promise<string>;
        abstract sendTransaction(transaction: TransactionRequest): Promise<TransactionResponse>;
        constructor();
        static isSigner(value: any): value is Signer;
    }
}

declare module 'ethers/wallet' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { HDNode } from 'ethers/utils/hdnode';
    import { SigningKey } from 'ethers/utils/signing-key';
    import { Wordlist } from 'ethers/utils/wordlist';
    import { Signer as AbstractSigner } from 'ethers/abstract-signer';
    import { Provider } from 'ethers/providers/abstract-provider';
    import { ProgressCallback } from 'ethers/utils/secret-storage';
    import { Arrayish } from 'ethers/utils/bytes';
    import { BlockTag, TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    export class Wallet extends AbstractSigner {
            readonly provider: Provider;
            constructor(privateKey: SigningKey | HDNode | Arrayish, provider?: Provider);
            readonly address: string;
            readonly mnemonic: string;
            readonly path: string;
            readonly privateKey: string;
            /**
                *  Create a new instance of this Wallet connected to provider.
                */
            connect(provider: Provider): Wallet;
            getAddress(): Promise<string>;
            sign(transaction: TransactionRequest): Promise<string>;
            signMessage(message: Arrayish | string): Promise<string>;
            getBalance(blockTag?: BlockTag): Promise<BigNumber>;
            getTransactionCount(blockTag?: BlockTag): Promise<number>;
            sendTransaction(transaction: TransactionRequest): Promise<TransactionResponse>;
            encrypt(password: Arrayish | string, options?: any, progressCallback?: ProgressCallback): Promise<string>;
            /**
                *  Static methods to create Wallet instances.
                */
            static createRandom(options?: any): Wallet;
            static fromEncryptedJson(json: string, password: Arrayish, progressCallback?: ProgressCallback): Promise<Wallet>;
            static fromMnemonic(mnemonic: string, path?: string, wordlist?: Wordlist): Wallet;
    }
}

declare module 'ethers/constants' {
    import { BigNumber } from 'ethers/utils/bignumber';
    const AddressZero = "******************************************";
    const HashZero = "******************************************000000000000000000000000";
    const EtherSymbol = "\u039E";
    const NegativeOne: BigNumber;
    const Zero: BigNumber;
    const One: BigNumber;
    const Two: BigNumber;
    const WeiPerEther: BigNumber;
    const MaxUint256: BigNumber;
    export { AddressZero, HashZero, EtherSymbol, NegativeOne, Zero, One, Two, WeiPerEther, MaxUint256 };
}

declare module 'ethers/errors' {
    export const UNKNOWN_ERROR = "UNKNOWN_ERROR";
    export const NOT_IMPLEMENTED = "NOT_IMPLEMENTED";
    export const MISSING_NEW = "MISSING_NEW";
    export const CALL_EXCEPTION = "CALL_EXCEPTION";
    export const INVALID_ARGUMENT = "INVALID_ARGUMENT";
    export const MISSING_ARGUMENT = "MISSING_ARGUMENT";
    export const UNEXPECTED_ARGUMENT = "UNEXPECTED_ARGUMENT";
    export const NUMERIC_FAULT = "NUMERIC_FAULT";
    export const INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS";
    export const NONCE_EXPIRED = "NONCE_EXPIRED";
    export const REPLACEMENT_UNDERPRICED = "REPLACEMENT_UNDERPRICED";
    export const UNSUPPORTED_OPERATION = "UNSUPPORTED_OPERATION";
    export function throwError(message: string, code: string, params: any): never;
    export function checkNew(self: any, kind: any): void;
    export function checkArgumentCount(count: number, expectedCount: number, suffix?: string): void;
    export function setCensorship(censorship: boolean, permanent?: boolean): void;
    export function checkNormalize(): void;
    export function setLogLevel(logLevel: string): void;
    export function warn(...args: Array<any>): void;
    export function info(...args: Array<any>): void;
}

declare module 'ethers/providers' {
    import { Provider } from 'ethers/providers/abstract-provider';
    import { BaseProvider } from 'ethers/providers/base-provider';
    import { EtherscanProvider } from 'ethers/providers/etherscan-provider';
    import { FallbackProvider } from 'ethers/providers/fallback-provider';
    import { IpcProvider } from 'ethers/providers/ipc-provider';
    import { InfuraProvider } from 'ethers/providers/infura-provider';
    import { JsonRpcProvider, JsonRpcSigner } from 'ethers/providers/json-rpc-provider';
    import { Web3Provider } from 'ethers/providers/web3-provider';
    import { Block, BlockTag, EventType, Filter, Log, Listener, TransactionReceipt, TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    import { AsyncSendable } from 'ethers/providers/web3-provider';
    export { Provider, BaseProvider, FallbackProvider, EtherscanProvider, InfuraProvider, JsonRpcProvider, Web3Provider, IpcProvider, JsonRpcSigner, Block, BlockTag, EventType, Filter, Log, Listener, TransactionReceipt, TransactionRequest, TransactionResponse, AsyncSendable };
}

declare module 'ethers/utils' {
    import { AbiCoder, defaultAbiCoder, formatSignature, formatParamType, parseSignature, parseParamType } from 'ethers/utils/abi-coder';
    import { getAddress, getContractAddress, getCreate2Address, getIcapAddress } from 'ethers/utils/address';
    import * as base64 from 'ethers/utils/base64';
    import { BigNumber, bigNumberify } from 'ethers/utils/bignumber';
    import { arrayify, concat, hexDataSlice, hexDataLength, hexlify, hexStripZeros, hexZeroPad, isHexString, joinSignature, padZeros, splitSignature, stripZeros } from 'ethers/utils/bytes';
    import { hashMessage, id, namehash } from 'ethers/utils/hash';
    import * as HDNode from 'ethers/utils/hdnode';
    import { Interface } from 'ethers/utils/interface';
    import { getJsonWalletAddress } from 'ethers/utils/json-wallet';
    import { keccak256 } from 'ethers/utils/keccak256';
    import { sha256 } from 'ethers/utils/sha2';
    import { keccak256 as solidityKeccak256, pack as solidityPack, sha256 as soliditySha256 } from 'ethers/utils/solidity';
    import { randomBytes } from 'ethers/utils/random-bytes';
    import { getNetwork } from 'ethers/utils/networks';
    import { checkProperties, deepCopy, defineReadOnly, resolveProperties, shallowCopy } from 'ethers/utils/properties';
    import * as RLP from 'ethers/utils/rlp';
    import { computeAddress, computePublicKey, recoverAddress, recoverPublicKey, verifyMessage } from 'ethers/utils/secp256k1';
    import { SigningKey } from 'ethers/utils/signing-key';
    import { populateTransaction } from 'ethers/utils/transaction';
    import { parse as parseTransaction, serialize as serializeTransaction } from 'ethers/utils/transaction';
    import { formatBytes32String, parseBytes32String, toUtf8Bytes, toUtf8String } from 'ethers/utils/utf8';
    import { commify, formatEther, parseEther, formatUnits, parseUnits } from 'ethers/utils/units';
    import { fetchJson, poll } from 'ethers/utils/web';
    import { SupportedAlgorithms } from 'ethers/utils/hmac';
    import { UnicodeNormalizationForm } from 'ethers/utils/utf8';
    import { CoerceFunc, EventFragment, FunctionFragment, ParamType } from 'ethers/utils/abi-coder';
    import { BigNumberish } from 'ethers/utils/bignumber';
    import { Arrayish, Hexable, Signature } from 'ethers/utils/bytes';
    import { Indexed, DeployDescription, EventDescription, FunctionDescription, LogDescription, TransactionDescription } from 'ethers/utils/interface';
    import { Network, Networkish } from 'ethers/utils/networks';
    import { Transaction, UnsignedTransaction } from 'ethers/utils/transaction';
    import { ConnectionInfo, OnceBlockable, PollOptions } from 'ethers/utils/web';
    import { EncryptOptions, ProgressCallback } from 'ethers/utils/secret-storage';
    import { Wordlist } from 'ethers/utils/wordlist';
    export { AbiCoder, defaultAbiCoder, formatSignature, formatParamType, parseSignature, parseParamType, RLP, fetchJson, getNetwork, checkProperties, deepCopy, defineReadOnly, resolveProperties, shallowCopy, arrayify, concat, padZeros, stripZeros, HDNode, SigningKey, Interface, base64, BigNumber, bigNumberify, hexlify, isHexString, hexStripZeros, hexZeroPad, hexDataLength, hexDataSlice, toUtf8Bytes, toUtf8String, formatBytes32String, parseBytes32String, hashMessage, namehash, id, getAddress, getIcapAddress, getContractAddress, getCreate2Address, formatEther, parseEther, formatUnits, parseUnits, commify, keccak256, sha256, randomBytes, solidityPack, solidityKeccak256, soliditySha256, splitSignature, joinSignature, parseTransaction, populateTransaction, serializeTransaction, getJsonWalletAddress, computeAddress, computePublicKey, recoverAddress, recoverPublicKey, verifyMessage, poll, SupportedAlgorithms, UnicodeNormalizationForm, CoerceFunc, EventFragment, FunctionFragment, ParamType, BigNumberish, Arrayish, Hexable, Signature, Indexed, DeployDescription, EventDescription, FunctionDescription, LogDescription, TransactionDescription, Network, Networkish, Transaction, UnsignedTransaction, ConnectionInfo, OnceBlockable, PollOptions, EncryptOptions, ProgressCallback, Wordlist, };
}

declare module 'ethers/wordlists' {
    import { Wordlist } from 'ethers/utils/wordlist';
    const en: Wordlist;
    const es: Wordlist;
    const fr: Wordlist;
    const it: Wordlist;
    const ja: Wordlist;
    const ko: Wordlist;
    const zh: Wordlist;
    const zh_cn: Wordlist;
    const zh_tw: Wordlist;
    export { en, es, fr, it, ja, ko, zh, zh_cn, zh_tw };
}

declare module 'ethers/utils/shims' {
    export const platform = "node";
}

declare module 'ethers/_version' {
    export const version = "4.0.49";
}

declare module 'ethers/utils/bignumber' {
    import { Hexable } from 'ethers/utils/bytes';
    import { Arrayish } from 'ethers/utils/bytes';
    export type BigNumberish = BigNumber | string | number | Arrayish;
    export class BigNumber implements Hexable {
        constructor(value: BigNumberish);
        fromTwos(value: number): BigNumber;
        toTwos(value: number): BigNumber;
        abs(): BigNumber;
        add(other: BigNumberish): BigNumber;
        sub(other: BigNumberish): BigNumber;
        div(other: BigNumberish): BigNumber;
        mul(other: BigNumberish): BigNumber;
        mod(other: BigNumberish): BigNumber;
        pow(other: BigNumberish): BigNumber;
        maskn(value: number): BigNumber;
        eq(other: BigNumberish): boolean;
        lt(other: BigNumberish): boolean;
        lte(other: BigNumberish): boolean;
        gt(other: BigNumberish): boolean;
        gte(other: BigNumberish): boolean;
        isZero(): boolean;
        toNumber(): number;
        toString(): string;
        toHexString(): string;
        static isBigNumber(value: any): value is BigNumber;
    }
    export function bigNumberify(value: BigNumberish): BigNumber;
}

declare module 'ethers/utils/interface' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { BigNumberish } from 'ethers/utils/bignumber';
    import { EventFragment, FunctionFragment, ParamType } from 'ethers/utils/abi-coder';
    export interface Indexed {
        readonly hash: string;
    }
    export interface DeployDescription {
        readonly inputs: Array<ParamType>;
        readonly payable: boolean;
        encode(bytecode: string, params: Array<any>): string;
    }
    export interface FunctionDescription {
        readonly type: "call" | "transaction";
        readonly name: string;
        readonly signature: string;
        readonly sighash: string;
        readonly inputs: Array<ParamType>;
        readonly outputs: Array<ParamType>;
        readonly payable: boolean;
        readonly gas: BigNumber;
        encode(params: Array<any>): string;
        decode(data: string): any;
    }
    export interface EventDescription {
        readonly name: string;
        readonly signature: string;
        readonly inputs: Array<ParamType>;
        readonly anonymous: boolean;
        readonly topic: string;
        encodeTopics(params: Array<any>): Array<string>;
        decode(data: string, topics?: Array<string>): any;
    }
    export interface LogDescription {
        readonly decode: (data: string, topics: Array<string>) => any;
        readonly name: string;
        readonly signature: string;
        readonly topic: string;
        readonly values: any;
    }
    export interface TransactionDescription {
        readonly name: string;
        readonly args: Array<any>;
        readonly signature: string;
        readonly sighash: string;
        readonly decode: (data: string) => any;
        readonly value: BigNumber;
    }
    class Description {
        constructor(info: any);
    }
    class _DeployDescription extends Description implements DeployDescription {
        readonly inputs: Array<ParamType>;
        readonly payable: boolean;
        encode(bytecode: string, params: Array<any>): string;
    }
    class _FunctionDescription extends Description implements FunctionDescription {
        readonly type: "call" | "transaction";
        readonly name: string;
        readonly signature: string;
        readonly sighash: string;
        readonly inputs: Array<ParamType>;
        readonly outputs: Array<ParamType>;
        readonly payable: boolean;
        readonly gas: BigNumber;
        encode(params: Array<any>): string;
        decode(data: string): any;
    }
    class _EventDescription extends Description implements EventDescription {
        readonly name: string;
        readonly signature: string;
        readonly inputs: Array<ParamType>;
        readonly anonymous: boolean;
        readonly topic: string;
        encodeTopics(params: Array<any>): Array<string>;
        decode(data: string, topics?: Array<string>): any;
    }
    class _TransactionDescription extends Description implements TransactionDescription {
        readonly name: string;
        readonly args: Array<any>;
        readonly signature: string;
        readonly sighash: string;
        readonly decode: (data: string) => any;
        readonly value: BigNumber;
    }
    class _LogDescription extends Description implements LogDescription {
        readonly name: string;
        readonly signature: string;
        readonly topic: string;
        readonly decode: (data: string, topics: Array<string>) => any;
        readonly values: any;
    }
    export class Interface {
        readonly abi: Array<EventFragment | FunctionFragment>;
        readonly functions: {
            [name: string]: _FunctionDescription;
        };
        readonly events: {
            [name: string]: _EventDescription;
        };
        readonly deployFunction: _DeployDescription;
        constructor(abi: Array<string | FunctionFragment | EventFragment | ParamType> | string);
        parseTransaction(tx: {
            data: string;
            value?: BigNumberish;
        }): _TransactionDescription;
        parseLog(log: {
            topics: Array<string>;
            data: string;
        }): _LogDescription;
        static isInterface(value: any): value is Interface;
        static isIndexed(value: any): value is Indexed;
    }
    export {};
}

declare module 'ethers/utils/transaction' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Arrayish, Signature } from 'ethers/utils/bytes';
    import { BigNumberish } from 'ethers/utils/bignumber';
    import { Provider } from 'ethers/providers/abstract-provider';
    export type UnsignedTransaction = {
        to?: string;
        nonce?: number;
        gasLimit?: BigNumberish;
        gasPrice?: BigNumberish;
        data?: Arrayish;
        value?: BigNumberish;
        chainId?: number;
    };
    export interface Transaction {
        hash?: string;
        to?: string;
        from?: string;
        nonce: number;
        gasLimit: BigNumber;
        gasPrice: BigNumber;
        data: string;
        value: BigNumber;
        chainId: number;
        r?: string;
        s?: string;
        v?: number;
    }
    export function serialize(transaction: UnsignedTransaction, signature?: Arrayish | Signature): string;
    export function parse(rawTransaction: Arrayish): Transaction;
    export function populateTransaction(transaction: any, provider: Provider, from: string | Promise<string>): Promise<Transaction>;
}

declare module 'ethers/providers/abstract-provider' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Arrayish } from 'ethers/utils/bytes';
    import { BigNumberish } from 'ethers/utils/bignumber';
    import { Network } from 'ethers/utils/networks';
    import { OnceBlockable } from 'ethers/utils/web';
    import { Transaction } from 'ethers/utils/transaction';
    export interface Block {
        hash: string;
        parentHash: string;
        number: number;
        timestamp: number;
        nonce: string;
        difficulty: number;
        gasLimit: BigNumber;
        gasUsed: BigNumber;
        miner: string;
        extraData: string;
        transactions: Array<string>;
    }
    export type BlockTag = string | number;
    export type Filter = {
        fromBlock?: BlockTag;
        toBlock?: BlockTag;
        address?: string;
        topics?: Array<string | Array<string>>;
    };
    export type FilterByBlock = {
        blockHash?: string;
        address?: string;
        topics?: Array<string | Array<string>>;
    };
    export interface Log {
        blockNumber?: number;
        blockHash?: string;
        transactionIndex?: number;
        removed?: boolean;
        transactionLogIndex?: number;
        address: string;
        data: string;
        topics: Array<string>;
        transactionHash?: string;
        logIndex?: number;
    }
    export interface TransactionReceipt {
        to?: string;
        from?: string;
        contractAddress?: string;
        transactionIndex?: number;
        root?: string;
        gasUsed?: BigNumber;
        logsBloom?: string;
        blockHash?: string;
        transactionHash?: string;
        logs?: Array<Log>;
        blockNumber?: number;
        confirmations?: number;
        cumulativeGasUsed?: BigNumber;
        byzantium: boolean;
        status?: number;
    }
    export type TransactionRequest = {
        to?: string | Promise<string>;
        from?: string | Promise<string>;
        nonce?: BigNumberish | Promise<BigNumberish>;
        gasLimit?: BigNumberish | Promise<BigNumberish>;
        gasPrice?: BigNumberish | Promise<BigNumberish>;
        data?: Arrayish | Promise<Arrayish>;
        value?: BigNumberish | Promise<BigNumberish>;
        chainId?: number | Promise<number>;
    };
    export interface TransactionResponse extends Transaction {
        blockNumber?: number;
        blockHash?: string;
        timestamp?: number;
        confirmations: number;
        from: string;
        raw?: string;
        wait: (confirmations?: number) => Promise<TransactionReceipt>;
    }
    export type EventType = string | Array<string> | Filter;
    export type Listener = (...args: Array<any>) => void;
    export abstract class Provider implements OnceBlockable {
        abstract getNetwork(): Promise<Network>;
        abstract getBlockNumber(): Promise<number>;
        abstract getGasPrice(): Promise<BigNumber>;
        abstract getBalance(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<BigNumber>;
        abstract getTransactionCount(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<number>;
        abstract getCode(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        abstract getStorageAt(addressOrName: string | Promise<string>, position: BigNumberish | Promise<BigNumberish>, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        abstract sendTransaction(signedTransaction: string | Promise<string>): Promise<TransactionResponse>;
        abstract call(transaction: TransactionRequest, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        abstract estimateGas(transaction: TransactionRequest): Promise<BigNumber>;
        abstract getBlock(blockHashOrBlockTag: BlockTag | string | Promise<BlockTag | string>, includeTransactions?: boolean): Promise<Block>;
        abstract getTransaction(transactionHash: string): Promise<TransactionResponse>;
        abstract getTransactionReceipt(transactionHash: string): Promise<TransactionReceipt>;
        abstract getLogs(filter: Filter | FilterByBlock): Promise<Array<Log>>;
        abstract resolveName(name: string | Promise<string>): Promise<string>;
        abstract lookupAddress(address: string | Promise<string>): Promise<string>;
        abstract on(eventName: EventType, listener: Listener): Provider;
        abstract once(eventName: EventType, listener: Listener): Provider;
        abstract listenerCount(eventName?: EventType): number;
        abstract listeners(eventName: EventType): Array<Listener>;
        abstract removeAllListeners(eventName: EventType): Provider;
        abstract removeListener(eventName: EventType, listener: Listener): Provider;
        abstract waitForTransaction(transactionHash: string, confirmations?: number): Promise<TransactionReceipt>;
        constructor();
        static isProvider(value: any): value is Provider;
    }
}

declare module 'ethers/utils/bytes' {
    /**
      *  Conversion Utilities
      *
      */
    export type Arrayish = string | ArrayLike<number>;
    export interface Hexable {
        toHexString(): string;
    }
    export interface Signature {
        r: string;
        s: string;
        recoveryParam?: number;
        v?: number;
    }
    export function isHexable(value: any): value is Hexable;
    export function isArrayish(value: any): value is Arrayish;
    export function arrayify(value: Arrayish | Hexable): Uint8Array;
    export function concat(objects: Array<Arrayish>): Uint8Array;
    export function stripZeros(value: Arrayish): Uint8Array;
    export function padZeros(value: Arrayish, length: number): Uint8Array;
    export function isHexString(value: any, length?: number): boolean;
    export function hexlify(value: Arrayish | Hexable | number): string;
    export function hexDataLength(data: string): number;
    export function hexDataSlice(data: string, offset: number, endOffset?: number): string;
    export function hexStripZeros(value: string): string;
    export function hexZeroPad(value: string, length: number): string;
    export function splitSignature(signature: Arrayish | Signature): Signature;
    export function joinSignature(signature: Signature): string;
}

declare module 'ethers/utils/abi-coder' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Arrayish } from 'ethers/utils/bytes';
    export type CoerceFunc = (type: string, value: any) => any;
    export type ParamType = {
        name?: string;
        type: string;
        indexed?: boolean;
        components?: Array<any>;
    };
    export type EventFragment = {
        type: string;
        name: string;
        anonymous: boolean;
        inputs: Array<ParamType>;
    };
    export type FunctionFragment = {
        type: string;
        name: string;
        constant: boolean;
        inputs: Array<ParamType>;
        outputs: Array<ParamType>;
        payable: boolean;
        stateMutability: string;
        gas?: BigNumber;
    };
    export const defaultCoerceFunc: CoerceFunc;
    export function parseParamType(type: string): ParamType;
    export function formatParamType(paramType: ParamType): string;
    export function formatSignature(fragment: EventFragment | FunctionFragment): string;
    export function parseSignature(fragment: string): EventFragment | FunctionFragment;
    export class AbiCoder {
        readonly coerceFunc: CoerceFunc;
        constructor(coerceFunc?: CoerceFunc);
        encode(types: Array<string | ParamType>, values: Array<any>): string;
        decode(types: Array<string | ParamType>, data: Arrayish): any;
    }
    export const defaultAbiCoder: AbiCoder;
}

declare module 'ethers/utils/hdnode' {
    import { Arrayish } from 'ethers/utils/bytes';
    import { Wordlist } from 'ethers/utils/wordlist';
    export const defaultPath = "m/44'/60'/0'/0/0";
    export class HDNode {
        readonly privateKey: string;
        readonly publicKey: string;
        readonly fingerprint: string;
        readonly parentFingerprint: string;
        readonly address: string;
        readonly mnemonic: string;
        readonly path: string;
        readonly chainCode: string;
        readonly index: number;
        readonly depth: number;
        /**
          *  This constructor should not be called directly.
          *
          *  Please use:
          *   - fromMnemonic
          *   - fromSeed
          */
        constructor(constructorGuard: any, privateKey: string, publicKey: string, parentFingerprint: string, chainCode: string, index: number, depth: number, mnemonic: string, path: string);
        readonly extendedKey: string;
        neuter(): HDNode;
        derivePath(path: string): HDNode;
        static isHDNode(value: any): value is HDNode;
    }
    export function fromExtendedKey(extendedKey: string): HDNode;
    export function fromMnemonic(mnemonic: string, wordlist?: Wordlist, password?: string): HDNode;
    export function fromSeed(seed: Arrayish): HDNode;
    export function mnemonicToSeed(mnemonic: string, password?: string): string;
    export function mnemonicToEntropy(mnemonic: string, wordlist?: Wordlist): string;
    export function entropyToMnemonic(entropy: Arrayish, wordlist?: Wordlist): string;
    export function isValidMnemonic(mnemonic: string, wordlist?: Wordlist): boolean;
}

declare module 'ethers/utils/signing-key' {
    /**
      *  SigningKey
      *
      *
      */
    import { HDNode } from 'ethers/utils/hdnode';
    import { Arrayish, Signature } from 'ethers/utils/bytes';
    export class SigningKey {
        readonly privateKey: string;
        readonly publicKey: string;
        readonly address: string;
        readonly mnemonic: string;
        readonly path: string;
        constructor(privateKey: Arrayish | HDNode);
        signDigest(digest: Arrayish): Signature;
        computeSharedSecret(key: Arrayish | string): string;
        static isSigningKey(value: any): value is SigningKey;
    }
}

declare module 'ethers/utils/wordlist' {
    export function check(wordlist: Wordlist): string;
    export abstract class Wordlist {
        readonly locale: string;
        constructor(locale: string);
        abstract getWord(index: number): string;
        abstract getWordIndex(word: string): number;
        split(mnemonic: string): Array<string>;
        join(words: Array<string>): string;
    }
    export function register(lang: Wordlist, name?: string): void;
}

declare module 'ethers/utils/secret-storage' {
    import { SigningKey } from 'ethers/utils/signing-key';
    import { Arrayish } from 'ethers/utils/bytes';
    export type ProgressCallback = (percent: number) => void;
    export type EncryptOptions = {
        iv?: Arrayish;
        entropy?: Arrayish;
        mnemonic?: string;
        path?: string;
        wordlist?: any;
        client?: string;
        salt?: Arrayish;
        uuid?: string;
        scrypt?: {
            N?: number;
            r?: number;
            p?: number;
        };
    };
    export function decryptCrowdsale(json: string, password: Arrayish | string): SigningKey;
    export function decrypt(json: string, password: Arrayish, progressCallback?: ProgressCallback): Promise<SigningKey>;
    export function encrypt(privateKey: Arrayish | SigningKey, password: Arrayish | string, options?: EncryptOptions, progressCallback?: ProgressCallback): Promise<string>;
}

declare module 'ethers/providers/base-provider' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Provider } from 'ethers/providers/abstract-provider';
    import { Block, BlockTag, EventType, Filter, FilterByBlock, Listener, Log, TransactionReceipt, TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    import { BigNumberish } from 'ethers/utils/bignumber';
    import { Transaction } from 'ethers/utils/transaction';
    import { Network, Networkish } from 'ethers/utils/networks';
    export class BaseProvider extends Provider {
        protected _emitted: {
            [eventName: string]: number | 'pending';
        };
        /**
          *  ready
          *
          *  A Promise<Network> that resolves only once the provider is ready.
          *
          *  Sub-classes that call the super with a network without a chainId
          *  MUST set this. Standard named networks have a known chainId.
          *
          */
        protected ready: Promise<Network>;
        constructor(network: Networkish | Promise<Network>);
        resetEventsBlock(blockNumber: number): void;
        readonly network: Network;
        getNetwork(): Promise<Network>;
        readonly blockNumber: number;
        polling: boolean;
        pollingInterval: number;
        _getFastBlockNumber(): Promise<number>;
        _setFastBlockNumber(blockNumber: number): void;
        waitForTransaction(transactionHash: string, confirmations?: number): Promise<TransactionReceipt>;
        getBlockNumber(): Promise<number>;
        getGasPrice(): Promise<BigNumber>;
        getBalance(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<BigNumber>;
        getTransactionCount(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<number>;
        getCode(addressOrName: string | Promise<string>, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        getStorageAt(addressOrName: string | Promise<string>, position: BigNumberish | Promise<BigNumberish>, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        sendTransaction(signedTransaction: string | Promise<string>): Promise<TransactionResponse>;
        _wrapTransaction(tx: Transaction, hash?: string): TransactionResponse;
        call(transaction: TransactionRequest, blockTag?: BlockTag | Promise<BlockTag>): Promise<string>;
        estimateGas(transaction: TransactionRequest): Promise<BigNumber>;
        getBlock(blockHashOrBlockTag: BlockTag | string | Promise<BlockTag | string>, includeTransactions?: boolean): Promise<Block>;
        getTransaction(transactionHash: string): Promise<TransactionResponse>;
        getTransactionReceipt(transactionHash: string): Promise<TransactionReceipt>;
        getLogs(filter: Filter | FilterByBlock): Promise<Array<Log>>;
        getEtherPrice(): Promise<number>;
        _getAddress(addressOrName: string): Promise<string>;
        resolveName(name: string | Promise<string>): Promise<string>;
        lookupAddress(address: string | Promise<string>): Promise<string>;
        static checkTransactionResponse(transaction: any): TransactionResponse;
        doPoll(): void;
        perform(method: string, params: any): Promise<any>;
        protected _startPending(): void;
        protected _stopPending(): void;
        on(eventName: EventType, listener: Listener): Provider;
        once(eventName: EventType, listener: Listener): Provider;
        addEventListener(eventName: EventType, listener: Listener): Provider;
        emit(eventName: EventType, ...args: Array<any>): boolean;
        listenerCount(eventName?: EventType): number;
        listeners(eventName: EventType): Array<Listener>;
        removeAllListeners(eventName?: EventType): Provider;
        removeListener(eventName: EventType, listener: Listener): Provider;
    }
}

declare module 'ethers/providers/etherscan-provider' {
    import { BaseProvider } from 'ethers/providers/base-provider';
    import { BlockTag, TransactionResponse } from 'ethers/providers/abstract-provider';
    import { Networkish } from 'ethers/utils/networks';
    export class EtherscanProvider extends BaseProvider {
        readonly baseUrl: string;
        readonly apiKey: string;
        constructor(network?: Networkish, apiKey?: string);
        perform(method: string, params: any): Promise<any>;
        getHistory(addressOrName: string | Promise<string>, startBlock?: BlockTag, endBlock?: BlockTag): Promise<Array<TransactionResponse>>;
    }
}

declare module 'ethers/providers/fallback-provider' {
    import { BaseProvider } from 'ethers/providers/base-provider';
    export class FallbackProvider extends BaseProvider {
        constructor(providers: Array<BaseProvider>);
        readonly providers: Array<BaseProvider>;
        perform(method: string, params: {
            [name: string]: any;
        }): any;
    }
}

declare module 'ethers/providers/ipc-provider' {
    import { JsonRpcProvider } from 'ethers/providers/json-rpc-provider';
    import { Networkish } from 'ethers/utils/networks';
    export class IpcProvider extends JsonRpcProvider {
        readonly path: string;
        constructor(path: string, network?: Networkish);
        send(method: string, params: any): Promise<any>;
    }
}

declare module 'ethers/providers/infura-provider' {
    import { JsonRpcProvider, JsonRpcSigner } from 'ethers/providers/json-rpc-provider';
    import { Networkish } from 'ethers/utils/networks';
    export class InfuraProvider extends JsonRpcProvider {
        readonly apiAccessToken: string;
        readonly projectId: string;
        constructor(network?: Networkish, projectId?: string);
        protected _startPending(): void;
        getSigner(address?: string): JsonRpcSigner;
        listAccounts(): Promise<Array<string>>;
    }
}

declare module 'ethers/providers/json-rpc-provider' {
    import { BaseProvider } from 'ethers/providers/base-provider';
    import { Signer } from 'ethers/abstract-signer';
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Arrayish } from 'ethers/utils/bytes';
    import { Networkish } from 'ethers/utils/networks';
    import { ConnectionInfo } from 'ethers/utils/web';
    import { BlockTag, TransactionRequest, TransactionResponse } from 'ethers/providers/abstract-provider';
    export class JsonRpcSigner extends Signer {
        readonly provider: JsonRpcProvider;
        constructor(constructorGuard: any, provider: JsonRpcProvider, addressOrIndex?: string | number);
        getAddress(): Promise<string>;
        getBalance(blockTag?: BlockTag): Promise<BigNumber>;
        getTransactionCount(blockTag?: BlockTag): Promise<number>;
        sendUncheckedTransaction(transaction: TransactionRequest): Promise<string>;
        sendTransaction(transaction: TransactionRequest): Promise<TransactionResponse>;
        signMessage(message: Arrayish | string): Promise<string>;
        unlock(password: string): Promise<boolean>;
    }
    export class JsonRpcProvider extends BaseProvider {
        readonly connection: ConnectionInfo;
        constructor(url?: ConnectionInfo | string, network?: Networkish);
        getSigner(addressOrIndex?: string | number): JsonRpcSigner;
        listAccounts(): Promise<Array<string>>;
        send(method: string, params: any): Promise<any>;
        perform(method: string, params: any): Promise<any>;
        protected _startPending(): void;
        protected _stopPending(): void;
        static hexlifyTransaction(transaction: TransactionRequest, allowExtra?: {
            [key: string]: boolean;
        }): {
            [key: string]: string;
        };
    }
}

declare module 'ethers/providers/web3-provider' {
    import { JsonRpcProvider } from 'ethers/providers/json-rpc-provider';
    import { Networkish } from 'ethers/utils/networks';
    export type AsyncSendable = {
        isMetaMask?: boolean;
        host?: string;
        path?: string;
        sendAsync?: (request: any, callback: (error: any, response: any) => void) => void;
        send?: (request: any, callback: (error: any, response: any) => void) => void;
    };
    export class Web3Provider extends JsonRpcProvider {
        readonly _web3Provider: AsyncSendable;
        constructor(web3Provider: AsyncSendable, network?: Networkish);
        send(method: string, params: any): Promise<any>;
    }
}

declare module 'ethers/utils/address' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { Arrayish } from 'ethers/utils/bytes';
    export function getAddress(address: string): string;
    export function getIcapAddress(address: string): string;
    export function getContractAddress(transaction: {
        from: string;
        nonce: Arrayish | BigNumber | number;
    }): string;
    export type Create2Options = {
        from: string;
        salt: Arrayish;
        initCode?: Arrayish;
        initCodeHash?: Arrayish;
    };
    export function getCreate2Address(options: Create2Options): string;
}

declare module 'ethers/utils/base64' {
    import { Arrayish } from 'ethers/utils/bytes';
    export function decode(textData: string): Uint8Array;
    export function encode(data: Arrayish): string;
}

declare module 'ethers/utils/hash' {
    import { Arrayish } from 'ethers/utils/bytes';
    export function namehash(name: string): string;
    export function id(text: string): string;
    export function hashMessage(message: Arrayish | string): string;
}

declare module 'ethers/utils/json-wallet' {
    export function isCrowdsaleWallet(json: string): boolean;
    export function isSecretStorageWallet(json: string): boolean;
    export function getJsonWalletAddress(json: string): string;
}

declare module 'ethers/utils/keccak256' {
    import { Arrayish } from 'ethers/utils/bytes';
    export function keccak256(data: Arrayish): string;
}

declare module 'ethers/utils/sha2' {
    import { Arrayish } from 'ethers/utils/bytes';
    export function ripemd160(data: Arrayish): string;
    export function sha256(data: Arrayish): string;
    export function sha512(data: Arrayish): string;
}

declare module 'ethers/utils/solidity' {
    export function pack(types: Array<string>, values: Array<any>): string;
    export function keccak256(types: Array<string>, values: Array<any>): string;
    export function sha256(types: Array<string>, values: Array<any>): string;
}

declare module 'ethers/utils/random-bytes' {
    export function randomBytes(length: number): Uint8Array;
}

declare module 'ethers/utils/networks' {
    export type Network = {
        name: string;
        chainId: number;
        ensAddress?: string;
        _defaultProvider?: (providers: any) => any;
    };
    export type Networkish = Network | string | number;
    /**
      *  getNetwork
      *
      *  Converts a named common networks or chain ID (network ID) to a Network
      *  and verifies a network is a valid Network..
      */
    export function getNetwork(network: Networkish): Network;
}

declare module 'ethers/utils/properties' {
    export function defineReadOnly(object: any, name: string, value: any): void;
    export function setType(object: any, type: string): void;
    export function isType(object: any, type: string): boolean;
    export function resolveProperties(object: any): Promise<any>;
    export function checkProperties(object: any, properties: {
        [name: string]: boolean;
    }): void;
    export function shallowCopy(object: any): any;
    export function deepCopy(object: any, frozen?: boolean): any;
    export function inheritable(parent: any): (child: any) => void;
}

declare module 'ethers/utils/rlp' {
    import { Arrayish } from 'ethers/utils/bytes';
    export function encode(object: any): string;
    export function decode(data: Arrayish): any;
}

declare module 'ethers/utils/secp256k1' {
    import { Arrayish, Signature } from 'ethers/utils/bytes';
    export class KeyPair {
        readonly privateKey: string;
        readonly publicKey: string;
        readonly compressedPublicKey: string;
        readonly publicKeyBytes: Uint8Array;
        constructor(privateKey: Arrayish | string);
        sign(digest: Arrayish | string): Signature;
        computeSharedSecret(otherKey: Arrayish | string): string;
        _addPoint(other: Arrayish | string): string;
    }
    export function computePublicKey(key: Arrayish | string, compressed?: boolean): string;
    export function computeAddress(key: Arrayish | string): string;
    export function recoverPublicKey(digest: Arrayish | string, signature: Signature | string): string;
    export function recoverAddress(digest: Arrayish | string, signature: Signature | string): string;
    export function verifyMessage(message: Arrayish | string, signature: Signature | string): string;
}

declare module 'ethers/utils/utf8' {
    import { Arrayish } from 'ethers/utils/bytes';
    export enum UnicodeNormalizationForm {
        current = "",
        NFC = "NFC",
        NFD = "NFD",
        NFKC = "NFKC",
        NFKD = "NFKD"
    }
    export function toUtf8Bytes(str: string, form?: UnicodeNormalizationForm): Uint8Array;
    export function toUtf8String(bytes: Arrayish, ignoreErrors?: boolean): string;
    export function formatBytes32String(text: string): string;
    export function parseBytes32String(bytes: Arrayish): string;
}

declare module 'ethers/utils/units' {
    import { BigNumber } from 'ethers/utils/bignumber';
    import { BigNumberish } from 'ethers/utils/bignumber';
    export function commify(value: string | number): string;
    export function formatUnits(value: BigNumberish, unitType?: string | number): string;
    export function parseUnits(value: string, unitType?: string | number): BigNumber;
    export function formatEther(wei: BigNumberish): string;
    export function parseEther(ether: string): BigNumber;
}

declare module 'ethers/utils/web' {
    export type ConnectionInfo = {
        url: string;
        user?: string;
        password?: string;
        allowInsecure?: boolean;
        timeout?: number;
        headers?: {
            [key: string]: string | number;
        };
    };
    export interface OnceBlockable {
        once(eventName: "block", handler: () => void): void;
    }
    export type PollOptions = {
        timeout?: number;
        floor?: number;
        ceiling?: number;
        interval?: number;
        onceBlock?: OnceBlockable;
        fastRetry?: number;
    };
    export function fetchJson(connection: string | ConnectionInfo, json: string, processFunc: (value: any) => any): Promise<any>;
    export function poll(func: () => Promise<any>, options?: PollOptions): Promise<any>;
}

declare module 'ethers/utils/hmac' {
    import { Arrayish } from 'ethers/utils/bytes';
    export enum SupportedAlgorithms {
        sha256 = "sha256",
        sha512 = "sha512"
    }
    export function computeHmac(algorithm: SupportedAlgorithms, key: Arrayish, data: Arrayish): Uint8Array;
}

