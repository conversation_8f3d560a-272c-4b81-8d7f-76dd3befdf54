import { Contract, ContractFactory, VoidSigner } from './contract';
import { Signer } from './abstract-signer';
import { Wallet } from './wallet';
import * as constants from './constants';
import * as errors from './errors';
import * as providers from './providers';
import * as utils from './utils';
import * as wordlists from './wordlists';
import { platform } from './utils/shims';
import { version } from './_version';
import { ContractFunction, ContractTransaction, Event, EventFilter } from './contract';
declare function getDefaultProvider(network?: utils.Network | string): providers.BaseProvider;
export { Signer, Wallet, VoidSigner, getDefaultProvider, providers, Contract, ContractFactory, constants, errors, utils, wordlists, platform, version, ContractFunction, ContractTransaction, Event, EventFilter };
