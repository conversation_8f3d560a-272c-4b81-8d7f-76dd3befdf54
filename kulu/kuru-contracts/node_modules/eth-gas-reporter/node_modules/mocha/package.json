{"name": "mocha", "version": "7.2.0", "description": "simple, flexible, fun test framework", "keywords": ["mocha", "test", "bdd", "tdd", "tap", "testing", "chai", "assertion", "ava", "jest", "tape", "jasmine", "karma"], "funding": {"type": "opencollective", "url": "https://opencollective.com/mochajs"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mochajs/mocha.git"}, "bugs": {"url": "https://github.com/mochajs/mocha/issues/"}, "homepage": "https://mochajs.org/", "logo": "https://cldup.com/S9uQ-cOLYz.svg", "notifyLogo": "https://ibin.co/4QuRuGjXvl36.png", "bin": {"mocha": "./bin/mocha", "_mocha": "./bin/_mocha"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": ">= 8.10.0"}, "scripts": {"prepublishOnly": "nps test clean build", "start": "nps", "test": "nps test", "version": "nps version"}, "dependencies": {"ansi-colors": "3.2.3", "browser-stdout": "1.3.1", "chokidar": "3.3.0", "debug": "3.2.6", "diff": "3.5.0", "escape-string-regexp": "1.0.5", "find-up": "3.0.0", "glob": "7.1.3", "growl": "1.10.5", "he": "1.2.0", "js-yaml": "3.13.1", "log-symbols": "3.0.0", "minimatch": "3.0.4", "mkdirp": "0.5.5", "ms": "2.1.1", "node-environment-flags": "1.0.6", "object.assign": "4.1.0", "strip-json-comments": "2.0.1", "supports-color": "6.0.0", "which": "1.3.1", "wide-align": "1.1.3", "yargs": "13.3.2", "yargs-parser": "13.1.2", "yargs-unparser": "1.6.0"}, "devDependencies": {"@11ty/eleventy": "^0.10.0", "@11ty/eleventy-plugin-inclusive-language": "^1.0.0", "@mocha/docdash": "^2.1.3", "assetgraph-builder": "^8.0.0", "autoprefixer": "^9.7.4", "babel-eslint": "^10.1.0", "browserify": "^16.5.0", "browserify-package-json": "^1.0.1", "chai": "^4.2.0", "coffee-script": "^1.12.7", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "cross-spawn": "^6.0.5", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-config-semistandard": "^15.0.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "fs-extra": "^8.0.1", "husky": "^4.2.3", "hyperlink": "^4.4.3", "image-size": "^0.8.3", "jsdoc": "^3.6.3", "karma": "^4.1.0", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^2.0.2", "lint-staged": "^9.5.0", "markdown-it": "^10.0.0", "markdown-it-anchor": "^5.2.5", "markdown-it-attrs": "^3.0.2", "markdown-it-emoji": "^1.4.0", "markdown-it-prism": "^2.0.5", "markdown-toc": "^1.2.0", "markdownlint-cli": "^0.22.0", "needle": "^2.4.1", "nps": "^5.9.12", "nyc": "^15.0.0", "prettier": "^1.19.1", "remark": "^11.0.2", "remark-github": "^8.0.0", "remark-inline-links": "^3.1.3", "rewiremock": "^3.14.1", "rimraf": "^3.0.2", "sinon": "^9.0.1", "strip-ansi": "^6.0.0", "svgo": "^1.3.2", "through2": "^3.0.1", "to-vfile": "^6.1.0", "unexpected": "^11.13.0", "unexpected-eventemitter": "^2.2.0", "unexpected-sinon": "^10.11.2", "uslug": "^1.0.4", "watchify": "^3.11.1"}, "files": ["bin/*mocha", "assets/growl/*.png", "lib/**/*.{js,html,json}", "index.js", "mocha.css", "mocha.js", "browser-entry.js"], "browserify": {"transform": ["./scripts/package-json-cullify"]}, "browser": {"./index.js": "./browser-entry.js", "./lib/growl.js": "./lib/browser/growl.js", "tty": "./lib/browser/tty.js", "./lib/cli/*.js": false, "chokidar": false, "fs": false, "glob": false, "path": false, "supports-color": false}, "prettier": {"singleQuote": true, "bracketSpacing": false, "endOfLine": "auto"}, "gitter": "https://gitter.im/mochajs/mocha", "husky": {"hooks": {"pre-commit": "lint-staged"}}}