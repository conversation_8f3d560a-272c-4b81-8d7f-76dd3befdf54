# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## 1.6.0 (2019-07-30)


### Bug Fixes

* **security:** update deps addressing recent audit vulnerabilities ([#40](https://github.com/yargs/yargs-unparser/issues/40)) ([2e74f1b](https://github.com/yargs/yargs-unparser/commit/2e74f1b))
* address bug with camelCased flattened keys ([#32](https://github.com/yargs/yargs-unparser/issues/32)) ([981533a](https://github.com/yargs/yargs-unparser/commit/981533a))
* **deps:** updated the lodash version to fix vulnerability ([#39](https://github.com/yargs/yargs-unparser/issues/39)) ([7375966](https://github.com/yargs/yargs-unparser/commit/7375966))
* **package:** update yargs to version 10.0.3 ([f1eb4cb](https://github.com/yargs/yargs-unparser/commit/f1eb4cb))
* **package:** update yargs to version 11.0.0 ([6aa7c91](https://github.com/yargs/yargs-unparser/commit/6aa7c91))


### Features

* add interoperation with minimist ([ba477f5](https://github.com/yargs/yargs-unparser/commit/ba477f5))

<a name="1.5.0"></a>
# 1.5.0 (2018-11-30)


### Bug Fixes

* **package:** update yargs to version 10.0.3 ([f1eb4cb](https://github.com/yargs/yargs-unparser/commit/f1eb4cb))
* **package:** update yargs to version 11.0.0 ([6aa7c91](https://github.com/yargs/yargs-unparser/commit/6aa7c91))


### Features

* add interoperation with minimist ([ba477f5](https://github.com/yargs/yargs-unparser/commit/ba477f5))



<a name="1.4.0"></a>
# [1.4.0](https://github.com/moxystudio/yargs-unparser/compare/v1.3.0...v1.4.0) (2017-12-30)


### Features

* add interoperation with minimist ([ba477f5](https://github.com/moxystudio/yargs-unparser/commit/ba477f5))
