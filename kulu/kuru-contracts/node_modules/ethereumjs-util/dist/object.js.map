{"version": 3, "file": "object.js", "sourceRoot": "", "sources": ["../src/object.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA2B;AAC3B,yCAA2C;AAC3C,2CAAiC;AACjC,mCAAyD;AAEzD;;;;;;;;;;GAUG;AACI,MAAM,gBAAgB,GAAG,UAAU,IAAS,EAAE,MAAW,EAAE,IAAU;IAC1E,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;IACb,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;IAEjB,sBAAsB;IACtB,IAAI,CAAC,MAAM,GAAG,UAAU,QAAiB,KAAK;QAC5C,IAAI,KAAK,EAAE;YAET,MAAM,GAAG,GAAS,EAAE,CAAA;YACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAa,EAAE,EAAE;gBACrC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAA;YACjD,CAAC,CAAC,CAAA;YACF,OAAO,GAAG,CAAA;SACX;QACD,OAAO,IAAA,gBAAQ,EAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3B,CAAC,CAAA;IAED,IAAI,CAAC,SAAS,GAAG,SAAS,SAAS;QACjC,OAAO,eAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC,CAAA;IAED,MAAM,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,CAAS,EAAE,EAAE;QACvC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC7B,SAAS,MAAM;YACb,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACpB,CAAC;QACD,SAAS,MAAM,CAAC,CAAM;YACpB,CAAC,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAA;YAEf,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAClD,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;aAC1B;YAED,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,EAAE;gBACnC,CAAC,GAAG,IAAA,mBAAW,EAAC,CAAC,CAAC,CAAA;gBAClB,IAAA,gBAAM,EACJ,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EACxB,aAAa,KAAK,CAAC,IAAI,uBAAuB,KAAK,CAAC,MAAM,QAAQ,CACnE,CAAA;aACF;iBAAM,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;gBAC/D,IAAA,gBAAM,EACJ,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EACzB,aAAa,KAAK,CAAC,IAAI,6BAA6B,KAAK,CAAC,MAAM,EAAE,CACnE,CAAA;aACF;YAED,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC;QAED,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;YACtC,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,GAAG,EAAE,MAAM;YACX,GAAG,EAAE,MAAM;SACZ,CAAC,CAAA;QAEF,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAA;SACjC;QAED,eAAe;QACf,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE;gBACvC,UAAU,EAAE,KAAK;gBACjB,YAAY,EAAE,IAAI;gBAClB,GAAG,EAAE,MAAM;gBACX,GAAG,EAAE,MAAM;aACZ,CAAC,CAAA;SACH;IACH,CAAC,CAAC,CAAA;IAEF,mCAAmC;IACnC,IAAI,IAAI,EAAE;QACR,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,yBAAc,EAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAA;SAChD;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,GAAG,eAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACxB;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;aAClD;YAED,sCAAsC;YACtC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAA;YACrC,CAAC,CAAC,CAAA;SACH;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACnC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;gBAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBACxE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC7E,CAAC,CAAC,CAAA;SACH;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;AACH,CAAC,CAAA;AApGY,QAAA,gBAAgB,oBAoG5B"}