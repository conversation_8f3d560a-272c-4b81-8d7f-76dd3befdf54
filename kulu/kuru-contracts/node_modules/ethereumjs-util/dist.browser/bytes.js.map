{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../src/bytes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAgC;AAChC,uCAAkF;AAQlF,qCAA4E;AAE5E;;;;GAIG;AACI,IAAM,QAAQ,GAAG,UAAU,CAAS;IACzC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,4CAAqC,CAAC,CAAE,CAAC,CAAA;KAC1D;IACD,OAAO,YAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAE,CAAA;AAC9B,CAAC,CAAA;AALY,QAAA,QAAQ,YAKpB;AAED;;;;GAIG;AACI,IAAM,WAAW,GAAG,UAAU,CAAS;IAC5C,IAAM,GAAG,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAA;IACvB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAS,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACpD,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;;GAGG;AACI,IAAM,KAAK,GAAG,UAAU,KAAa;IAC1C,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC1C,CAAC,CAAA;AAFY,QAAA,KAAK,SAEjB;AAED;;;;;;;GAOG;AACH,IAAM,SAAS,GAAG,UAAU,GAAW,EAAE,MAAc,EAAE,KAAc;IACrE,IAAM,GAAG,GAAG,IAAA,aAAK,EAAC,MAAM,CAAC,CAAA;IACzB,IAAI,KAAK,EAAE;QACT,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;KAC5B;SAAM;QACL,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAA;YAClC,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAA;KAC1B;AACH,CAAC,CAAA;AAED;;;;;;GAMG;AACI,IAAM,aAAa,GAAG,UAAU,GAAW,EAAE,MAAc;IAChE,IAAA,wBAAc,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;AACtC,CAAC,CAAA;AAHY,QAAA,aAAa,iBAGzB;AAED;;;;;;GAMG;AACI,IAAM,cAAc,GAAG,UAAU,GAAW,EAAE,MAAc;IACjE,IAAA,wBAAc,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA;AAHY,QAAA,cAAc,kBAG1B;AAED;;;;GAIG;AACH,IAAM,UAAU,GAAG,UAAU,CAAM;IACjC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAChB,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;QAC/C,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACd,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;KACb;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAED;;;;GAIG;AACI,IAAM,WAAW,GAAG,UAAU,CAAS;IAC5C,IAAA,wBAAc,EAAC,CAAC,CAAC,CAAA;IACjB,OAAO,UAAU,CAAC,CAAC,CAAW,CAAA;AAChC,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;;;GAIG;AACI,IAAM,UAAU,GAAG,UAAU,CAAW;IAC7C,IAAA,uBAAa,EAAC,CAAC,CAAC,CAAA;IAChB,OAAO,UAAU,CAAC,CAAC,CAAa,CAAA;AAClC,CAAC,CAAA;AAHY,QAAA,UAAU,cAGtB;AAED;;;;GAIG;AACI,IAAM,cAAc,GAAG,UAAU,CAAS;IAC/C,IAAA,2BAAiB,EAAC,CAAC,CAAC,CAAA;IACpB,CAAC,GAAG,IAAA,yBAAc,EAAC,CAAC,CAAC,CAAA;IACrB,OAAO,UAAU,CAAC,CAAC,CAAW,CAAA;AAChC,CAAC,CAAA;AAJY,QAAA,cAAc,kBAI1B;AAcD;;;;;GAKG;AACI,IAAM,QAAQ,GAAG,UAAU,CAAqB;IACrD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;QACjC,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;KAC7B;IAED,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KACtB;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,UAAU,EAAE;QAC/C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAe,CAAC,CAAA;KACpC;IAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,IAAI,CAAC,IAAA,sBAAW,EAAC,CAAC,CAAC,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,qHAA8G,CAAC,CAAE,CAClH,CAAA;SACF;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAS,EAAC,IAAA,yBAAc,EAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;KACxD;IAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAA,mBAAW,EAAC,CAAC,CAAC,CAAA;KACtB;IAED,IAAI,cAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACd,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,uDAAgD,CAAC,CAAE,CAAC,CAAA;SACrE;QACD,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;KAC7B;IAED,IAAI,CAAC,CAAC,OAAO,EAAE;QACb,4BAA4B;QAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;KAChC;IAED,IAAI,CAAC,CAAC,QAAQ,EAAE;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;KACjC;IAED,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;AACjC,CAAC,CAAA;AA3CY,QAAA,QAAQ,YA2CpB;AAED;;;;GAIG;AACI,IAAM,WAAW,GAAG,UAAU,GAAW;IAC9C,OAAO,IAAI,cAAE,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;AACzC,CAAC,CAAA;AAFY,QAAA,WAAW,eAEvB;AAED;;;GAGG;AACI,IAAM,WAAW,GAAG,UAAU,GAAW;IAC9C,GAAG,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;AACnC,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;;GAGG;AACI,IAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,OAAO,IAAI,cAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAClC,CAAC,CAAA;AAFY,QAAA,UAAU,cAEtB;AAED;;;GAGG;AACI,IAAM,UAAU,GAAG,UAAU,GAAO;IACzC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;AAC/C,CAAC,CAAA;AAFY,QAAA,UAAU,cAEtB;AAED;;GAEG;AACI,IAAM,YAAY,GAAG,UAAU,GAAW;IAC/C,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAG,CAAA;KACX;IAED,OAAO,IAAA,wBAAa,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;AAC9C,CAAC,CAAA;AANY,QAAA,YAAY,gBAMxB;AAED;;;;;;;;;;;;;;;;GAgBG;AACI,IAAM,MAAM,GAAG,UAAU,GAAW;IACzC,IAAM,WAAW,GAAG,gBAAgB,CAAA;IACpC,GAAG,GAAG,IAAA,yBAAc,EAAC,GAAG,CAAC,CAAA;IACzB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAA;KAC3E;IACD,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;IAElE,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AACnC,CAAC,CAAA;AATY,QAAA,MAAM,UASlB;AAED;;;;GAIG;AACI,IAAM,QAAQ,GAAG,UAAU,EAAO;IACvC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACvB,OAAO,YAAK,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAA;KACjC;SAAM,IAAI,EAAE,YAAY,KAAK,EAAE;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAC5B;QACD,OAAO,KAAK,CAAA;KACb;AACH,CAAC,CAAA;AAVY,QAAA,QAAQ,YAUpB;AAED;;;;;;;;;;;;GAYG;AACI,IAAM,uBAAuB,GAAG,UAAU,MAA6C;;;QAC5F,KAAqB,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,gBAAA,4BAAE;YAAlC,IAAA,KAAA,mBAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;YACd,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjD,MAAM,IAAI,KAAK,CAAC,UAAG,CAAC,oDAA0C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAC,CAAA;aACnF;SACF;;;;;;;;;AACH,CAAC,CAAA;AANY,QAAA,uBAAuB,2BAMnC;AAQD,SAAgB,WAAW,CAAC,GAAkC;IAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KACxB;IACD,OAAO,GAAG,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,CAAC,EAAd,CAAc,CAAC,CAAA;AACvC,CAAC;AALD,kCAKC;AAQD,SAAgB,WAAW,CAAC,GAA+B;IACzD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,EAAE,CAAC,CAAA;KAClC;IACD,OAAO,GAAG,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,CAAC,EAAd,CAAc,CAAC,CAAA;AACvC,CAAC;AALD,kCAKC"}