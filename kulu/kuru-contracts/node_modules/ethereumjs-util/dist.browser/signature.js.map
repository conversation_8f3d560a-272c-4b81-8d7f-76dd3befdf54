{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../src/signature.ts"], "names": [], "mappings": ";;;AAAA,6DAA2F;AAC3F,yCAAgC;AAChC,iCAA2E;AAC3E,+BAA+B;AAC/B,qCAA0C;AAC1C,iCAAoD;AAmBpD,SAAgB,MAAM,CAAC,OAAe,EAAE,UAAkB,EAAE,OAAY;IAChE,IAAA,KAAiC,IAAA,qBAAS,EAAC,OAAO,EAAE,UAAU,CAAC,EAA7D,SAAS,eAAA,EAAS,QAAQ,WAAmC,CAAA;IAErE,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC7C,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;IAE9C,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,2GAA2G;QAC3G,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;SACF;QACD,IAAM,GAAC,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAA;QACjE,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,KAAA,EAAE,CAAA;KACnB;IAED,IAAM,SAAS,GAAG,IAAA,cAAM,EAAC,OAAiB,EAAE,kBAAU,CAAC,EAAE,CAAC,CAAA;IAC1D,IAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IACvE,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAA;AACpB,CAAC;AApBD,wBAoBC;AAED,SAAS,oBAAoB,CAAC,CAAS,EAAE,OAAgB;IACvD,IAAM,GAAG,GAAG,IAAA,cAAM,EAAC,CAAC,EAAE,kBAAU,CAAC,EAAE,CAAC,CAAA;IAEpC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,OAAO,IAAA,cAAM,EAAC,CAAC,EAAE,kBAAU,CAAC,EAAE,CAAC,CAAA;IAE7D,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACpB;IACD,IAAM,SAAS,GAAG,IAAA,cAAM,EAAC,OAAO,EAAE,kBAAU,CAAC,EAAE,CAAC,CAAA;IAChD,OAAO,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC5C,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAqB;IAC/C,IAAM,GAAG,GAAG,IAAI,cAAE,CAAC,QAAQ,CAAC,CAAA;IAC5B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AACjC,CAAC;AAED;;;;GAIG;AACI,IAAM,SAAS,GAAG,UACvB,OAAe,EACf,CAAS,EACT,CAAS,EACT,CAAS,EACT,OAAgB;IAEhB,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACjF,IAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IACD,IAAM,YAAY,GAAG,IAAA,wBAAY,EAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAA;IAC1E,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,4BAAgB,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AACpE,CAAC,CAAA;AAdY,QAAA,SAAS,aAcrB;AAED;;;;GAIG;AACI,IAAM,QAAQ,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,OAAgB;IACjF,IAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,6EAA6E;IAC7E,OAAO,IAAA,mBAAW,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9F,CAAC,CAAA;AARY,QAAA,QAAQ,YAQpB;AAED;;;;GAIG;AACI,IAAM,YAAY,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,OAAgB;IACrF,IAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,IAAM,EAAE,GAAG,IAAA,cAAM,EAAC,CAAC,EAAE,kBAAU,CAAC,MAAM,CAAC,CAAA;IACvC,IAAI,EAAE,GAAG,CAAC,CAAA;IACV,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACtD,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACd;IAED,OAAO,IAAA,mBAAW,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;AAClF,CAAC,CAAA;AAdY,QAAA,YAAY,gBAcxB;AAED;;;;;GAKG;AACI,IAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,IAAM,GAAG,GAAW,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAA;IAEjC,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IACb,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACrB,CAAC,GAAG,IAAA,mBAAW,EAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;KAC/B;SAAM,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,6EAA6E;QAC7E,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACrB,CAAC,GAAG,IAAA,mBAAW,EAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACb;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,gDAAgD;IAChD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,CAAC,IAAI,EAAE,CAAA;KACR;IAED,OAAO;QACL,CAAC,GAAA;QACD,CAAC,GAAA;QACD,CAAC,GAAA;KACF,CAAA;AACH,CAAC,CAAA;AA9BY,QAAA,UAAU,cA8BtB;AAED;;;;GAIG;AACI,IAAM,gBAAgB,GAAG,UAC9B,CAAS,EACT,CAAS,EACT,CAAS,EACT,gBAAgC,EAChC,OAAgB;IADhB,iCAAA,EAAA,uBAAgC;IAGhC,IAAM,iBAAiB,GAAG,IAAI,cAAE,CAC9B,kEAAkE,EAClE,EAAE,CACH,CAAA;IACD,IAAM,WAAW,GAAG,IAAI,cAAE,CAAC,kEAAkE,EAAE,EAAE,CAAC,CAAA;IAElG,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;QACtC,OAAO,KAAK,CAAA;KACb;IAED,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;IAED,IAAM,GAAG,GAAG,IAAI,cAAE,CAAC,CAAC,CAAC,CAAA;IACrB,IAAM,GAAG,GAAG,IAAI,cAAE,CAAC,CAAC,CAAC,CAAA;IAErB,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAO,KAAK,CAAA;KACb;IAED,IAAI,gBAAgB,IAAI,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAjCY,QAAA,gBAAgB,oBAiC5B;AAED;;;;;GAKG;AACI,IAAM,mBAAmB,GAAG,UAAU,OAAe;IAC1D,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAA;IACvB,IAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,0CAAmC,OAAO,CAAC,MAAM,CAAE,EAAE,OAAO,CAAC,CAAA;IACxF,OAAO,IAAA,aAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;AACjD,CAAC,CAAA;AAJY,QAAA,mBAAmB,uBAI/B"}