#!/usr/bin/env node

if (require.main !== module) {
  throw new Error('Executable-only module should not be required');
}

// we must import global ShellJS methods after the require.main check to prevent the global
// namespace from being polluted if the error is caught
require('../global');

function exitWithErrorMessage(msg) {
  console.log(msg);
  console.log();
  process.exit(1);
}

if (process.argv.length < 3) {
  exitWithErrorMessage('ShellJS: missing argument (script name)');
}

var args,
  scriptName = process.argv[2];
env['NODE_PATH'] = __dirname + '/../..';

if (!scriptName.match(/\.js/) && !scriptName.match(/\.coffee/)) {
  if (test('-f', scriptName + '.js'))
    scriptName += '.js';
  if (test('-f', scriptName + '.coffee'))
    scriptName += '.coffee';
}

if (!test('-f', scriptName)) {
  exitWithErrorMessage('ShellJS: script not found ('+scriptName+')');
}

args = process.argv.slice(3);

for (var i = 0, l = args.length; i < l; i++) {
  if (args[i][0] !== "-"){
    args[i] = '"' + args[i] + '"'; // fixes arguments with multiple words
  }
}

var path = require('path');
var extensions = require('interpret').extensions;
var rechoir = require('rechoir');
rechoir.prepare(extensions, scriptName);
require(require.resolve(path.resolve(process.cwd(), scriptName)));
