{"name": "@types/mocha", "version": "10.0.1", "description": "TypeScript definitions for mocha", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mocha", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/kazimanzurrashid", "githubUsername": "kazimanzurrashid"}, {"name": "otiai10", "url": "https://github.com/otiai10", "githubUsername": "otiai10"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/enlight", "githubUsername": "enlight"}, {"name": "<PERSON>", "url": "https://github.com/cspotcode", "githubUsername": "cspotcode"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/1999", "githubUsername": "1999"}, {"name": "<PERSON>", "url": "https://github.com/strangedev", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nicojs", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mocha"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "9e25ced549081319185ba4b0a2b7a176f05736ea212170ca227caf4012a8fd8b", "typeScriptVersion": "4.1"}