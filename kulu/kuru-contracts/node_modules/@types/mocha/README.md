# Installation
> `npm install --save @types/mocha`

# Summary
This package contains type definitions for mocha (https://mochajs.org).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mocha.

### Additional Details
 * Last updated: Mon, 28 Nov 2022 19:32:55 GMT
 * Dependencies: none
 * Global values: `Mocha`, `after`, `afterEach`, `before`, `beforeEach`, `context`, `describe`, `it`, `mocha`, `run`, `setup`, `specify`, `suite`, `suiteSetup`, `suiteTeardown`, `teardown`, `test`, `xcontext`, `xdescribe`, `xit`, `xspecify`

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/kazimanzurrashid), [otiai10](https://github.com/otiai10), [<PERSON><PERSON><PERSON>](https://github.com/enlight), [<PERSON>](https://github.com/cspotcode), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/1999), [<PERSON>](https://github.com/strangedev), and [nicojs](https://github.com/nicojs).
