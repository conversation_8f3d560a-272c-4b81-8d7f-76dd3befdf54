{"name": "@types/chai-as-promised", "version": "7.1.5", "description": "TypeScript definitions for chai-as-promised", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chai-as-promised", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kuniwak", "githubUsername": "Kuniwak"}, {"name": "<PERSON>", "url": "https://github.com/leonard-thieu", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/lazerwalker", "githubUsername": "lazerwalker"}, {"name": "<PERSON>", "url": "https://github.com/mattbishop", "githubUsername": "mat<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/worr", "githubUsername": "worr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chai-as-promised"}, "scripts": {}, "dependencies": {"@types/chai": "*"}, "typesPublisherContentHash": "ff7a49b77674464e382178a2e1521aa36a6e0d58704be34f30c7994b31a272dd", "typeScriptVersion": "3.8"}