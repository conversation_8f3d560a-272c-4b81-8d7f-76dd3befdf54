{"authors": "<PERSON> <https://github.com/soywiz>, <PERSON> <https://github.com/leonyu>", "definitionFilename": "index.d.ts", "libraryDependencies": ["node"], "moduleDependencies": ["stream"], "libraryMajorVersion": 0, "libraryMinorVersion": 0, "libraryName": "form-data", "typingsPackageName": "form-data", "projectName": "https://github.com/felixge/node-form-data", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "ProperModule", "globals": ["FormData"], "declaredModules": ["form-data"], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "9cc91a75fca75af54344c0e61a0c29e9f9939ea01d67642720916db50a7245ef"}