{"name": "@types/form-data", "version": "0.0.33", "description": "TypeScript definitions for form-data", "license": "MIT", "author": "<PERSON> <https://github.com/soywiz>, <PERSON> <https://github.com/leonyu>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "9cc91a75fca75af54344c0e61a0c29e9f9939ea01d67642720916db50a7245ef"}