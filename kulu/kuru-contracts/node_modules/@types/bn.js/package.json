{"name": "@types/bn.js", "version": "5.1.1", "description": "TypeScript definitions for bn.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bn.js", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/LogvinovLeon", "githubUsername": "LogvinovLeon"}, {"name": "<PERSON>", "url": "https://github.com/HenryNguyen5", "githubUsername": "HenryNguyen5"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Gilthoniel", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bn.js"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8233e6f16bdc2e54b1778247f86d1fe3239c32be839e462d4f976b34ba95ec35", "typeScriptVersion": "4.0"}