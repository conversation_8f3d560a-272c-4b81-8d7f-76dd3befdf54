{"name": "@types/prettier", "version": "2.7.3", "description": "TypeScript definitions for prettier", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prettier", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/ikatyang", "githubUsername": "ikatyang"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr", "githubUsername": "ifiokjr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ffflorian", "githubUsername": "f<PERSON>lorian"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sosuke<PERSON>zuki", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Shinigami92", "githubUsername": "Shinigami92"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/thorn0", "githubUsername": "thorn0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shian15810", "githubUsername": "shian15810"}, {"name": "<PERSON>", "url": "https://github.com/marcgibbons", "githubUsername": "ma<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prettier"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "2f54649f157f965233986e2ddf054ef280991891ec4ce7533f45a6556b96a8f3", "typeScriptVersion": "4.3"}