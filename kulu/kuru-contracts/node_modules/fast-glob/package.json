{"name": "fast-glob", "version": "3.2.12", "description": "It's a very fast and efficient glob library for Node.js", "license": "MIT", "repository": "mrmlnc/fast-glob", "author": {"name": "<PERSON>", "url": "https://mrmlnc.com"}, "engines": {"node": ">=8.6.0"}, "main": "out/index.js", "typings": "out/index.d.ts", "files": ["out", "!out/{benchmark,tests}", "!out/**/*.map", "!out/**/*.spec.*"], "keywords": ["glob", "patterns", "fast", "implementation"], "devDependencies": {"@nodelib/fs.macchiato": "^1.0.1", "@types/compute-stdev": "^1.0.0", "@types/easy-table": "^0.0.32", "@types/glob": "^7.1.1", "@types/glob-parent": "^5.1.0", "@types/is-ci": "^2.0.0", "@types/merge2": "^1.1.4", "@types/micromatch": "^4.0.0", "@types/minimist": "^1.2.0", "@types/mocha": "^5.2.7", "@types/node": "^12.7.8", "@types/rimraf": "^2.0.2", "@types/sinon": "^7.5.0", "compute-stdev": "^1.0.0", "easy-table": "^1.1.1", "eslint": "^6.5.1", "eslint-config-mrmlnc": "^1.1.0", "execa": "^2.0.4", "fast-glob": "^3.0.4", "fdir": "^5.1.0", "glob": "^7.1.4", "is-ci": "^2.0.0", "log-update": "^4.0.0", "minimist": "^1.2.0", "mocha": "^6.2.1", "rimraf": "^3.0.0", "sinon": "^7.5.0", "tiny-glob": "^0.2.6", "typescript": "^3.6.3"}, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> out", "lint": "eslint \"src/**/*.ts\" --cache", "compile": "tsc", "test": "mocha \"out/**/*.spec.js\" -s 0", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "smoke:sync": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(sync\\)\"", "smoke:async": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(async\\)\"", "smoke:stream": "mocha \"out/**/*.smoke.js\" -s 0 --grep \"\\(stream\\)\"", "build": "npm run clean && npm run compile && npm run lint && npm test", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "bench": "npm run bench-async && npm run bench-stream && npm run bench-sync", "bench-async": "npm run bench-async-flatten && npm run bench-async-deep && npm run bench-async-partial-flatten && npm run bench-async-partial-deep", "bench-stream": "npm run bench-stream-flatten && npm run bench-stream-deep && npm run bench-stream-partial-flatten && npm run bench-stream-partial-deep", "bench-sync": "npm run bench-sync-flatten && npm run bench-sync-deep && npm run bench-sync-partial-flatten && npm run bench-sync-partial-deep", "bench-async-flatten": "node ./out/benchmark --mode async --pattern \"*\"", "bench-async-deep": "node ./out/benchmark --mode async --pattern \"**\"", "bench-async-partial-flatten": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/{first,second}/*\"", "bench-async-partial-deep": "node ./out/benchmark --mode async --pattern \"{fixtures,out}/**\"", "bench-stream-flatten": "node ./out/benchmark --mode stream --pattern \"*\"", "bench-stream-deep": "node ./out/benchmark --mode stream --pattern \"**\"", "bench-stream-partial-flatten": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/{first,second}/*\"", "bench-stream-partial-deep": "node ./out/benchmark --mode stream --pattern \"{fixtures,out}/**\"", "bench-sync-flatten": "node ./out/benchmark --mode sync --pattern \"*\"", "bench-sync-deep": "node ./out/benchmark --mode sync --pattern \"**\"", "bench-sync-partial-flatten": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/{first,second}/*\"", "bench-sync-partial-deep": "node ./out/benchmark --mode sync --pattern \"{fixtures,out}/**\""}}