# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.7](https://github.com/inspect-js/is-callable/compare/v1.2.6...v1.2.7) - 2022-09-23

### Commits

- [Fix] recognize `document.all` in IE 6-10 [`06c1db2`](https://github.com/inspect-js/is-callable/commit/06c1db2b9b2e0f28428e1293eb572f8f93871ec7)
- [Tests] improve logic for FF 20-35 [`0f7d9b9`](https://github.com/inspect-js/is-callable/commit/0f7d9b9c7fe149ca87e71f0a125ade251a6a578c)
- [Fix] handle `document.all` in FF 27 (and +, probably) [`696c661`](https://github.com/inspect-js/is-callable/commit/696c661b8c0810c2d05ab172f1607f4e77ddf81e)
- [Tests] fix proxy tests in FF 42-63 [`985df0d`](https://github.com/inspect-js/is-callable/commit/985df0dd36f8cfe6f1993657b7c0f4cfc19dae30)
- [readme] update tested browsers [`389e919`](https://github.com/inspect-js/is-callable/commit/389e919493b1cb2010126b0411e5291bf76169bd)
- [Fix] detect `document.all` in Opera 12.16 [`b9f1022`](https://github.com/inspect-js/is-callable/commit/b9f1022b3d7e466b7f09080bd64c253caf644325)
- [Fix] HTML elements: properly report as callable in Opera 12.16 [`17391fe`](https://github.com/inspect-js/is-callable/commit/17391fe02b895777c4337be28dca3b364b743b34)
- [Tests] fix inverted logic in FF3 test [`056ebd4`](https://github.com/inspect-js/is-callable/commit/056ebd48790f46ca18ff5b12f51b44c08ccc3595)

## [v1.2.6](https://github.com/inspect-js/is-callable/compare/v1.2.5...v1.2.6) - 2022-09-14

### Commits

- [Fix] work for `document.all` in Firefox 3 and IE 6-8 [`015132a`](https://github.com/inspect-js/is-callable/commit/015132aaef886ec777b5b3593ef4ce461dd0c7d4)
- [Test] skip function toString check for nullish values [`8698116`](https://github.com/inspect-js/is-callable/commit/8698116f95eb59df8b48ec8e4585fc1cdd8cae9f)
- [readme] add "supported engines" section [`0442207`](https://github.com/inspect-js/is-callable/commit/0442207a89a1554d41ba36daf21862ef7ccbd500)
- [Tests] skip one of the fixture objects in FF 3.6 [`a501141`](https://github.com/inspect-js/is-callable/commit/a5011410bc6edb276c6ec8b47ce5c5d83c4bee15)
- [Tests] allow `class` constructor tests to fail in FF v45 - v54, which has undetectable classes [`b12e4a4`](https://github.com/inspect-js/is-callable/commit/b12e4a4d8c438678bd7710f9f896680150766b51)
- [Fix] Safari 4: regexes should not be considered callable [`4b732ff`](https://github.com/inspect-js/is-callable/commit/4b732ffa34346db3f0193ea4e46b7d4e637e6c82)
- [Fix] properly recognize `document.all` in Safari 4 [`3193735`](https://github.com/inspect-js/is-callable/commit/319373525dc4603346661641840cd9a3e0613136)

## [v1.2.5](https://github.com/inspect-js/is-callable/compare/v1.2.4...v1.2.5) - 2022-09-11

### Commits

- [actions] reuse common workflows [`5bb4b32`](https://github.com/inspect-js/is-callable/commit/5bb4b32dc93987328ab4f396601f751c4a7abd62)
- [meta] better `eccheck` command [`b9bd597`](https://github.com/inspect-js/is-callable/commit/b9bd597322b6e3a24c74c09881ca73e1d9f9f485)
- [meta] use `npmignore` to autogenerate an npmignore file [`3192d38`](https://github.com/inspect-js/is-callable/commit/3192d38527c7fc461d05d5aa93d47628e658bc45)
- [Fix] for HTML constructors, always use `tryFunctionObject` even in pre-toStringTag browsers [`3076ea2`](https://github.com/inspect-js/is-callable/commit/3076ea21d1f6ecc1cb711dcf1da08f257892c72b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `available-typed-arrays`, `object-inspect`, `safe-publish-latest`, `tape` [`8986746`](https://github.com/inspect-js/is-callable/commit/89867464c42adc5cd375ee074a4574b0295442cb)
- [meta] add `auto-changelog` [`7dda9d0`](https://github.com/inspect-js/is-callable/commit/7dda9d04e670a69ae566c8fa596da4ff4371e615)
- [Fix] properly report `document.all` [`da90b2b`](https://github.com/inspect-js/is-callable/commit/da90b2b68dc4f33702c2e01ad07b4f89bcb60984)
- [actions] update codecov uploader [`c8f847c`](https://github.com/inspect-js/is-callable/commit/c8f847c90e04e54ff73c7cfae86e96e94990e324)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`899ae00`](https://github.com/inspect-js/is-callable/commit/899ae00b6abd10d81fc8bc7f02b345fd885d5f56)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es-value-fixtures`, `object-inspect`, `tape` [`344e913`](https://github.com/inspect-js/is-callable/commit/344e913b149609bf741aa7345fa32dc0b90d8893)
- [meta] remove greenkeeper config [`737dce5`](https://github.com/inspect-js/is-callable/commit/737dce5590b1abb16183a63cb9d7d26920b3b394)
- [meta] npmignore coverage output [`680a883`](https://github.com/inspect-js/is-callable/commit/680a8839071bf36a419fe66e1ced7a3303c27b28)

<!-- auto-changelog-above -->
1.2.4 / 2021-08-05
=================
  * [Fix] use `has-tostringtag` approach to behave correctly in the presence of symbol shams
  * [readme] fix repo URLs
  * [readme] add actions and codecov badges
  * [readme] remove defunct badges
  * [meta] ignore eclint checking coverage output
  * [meta] use `prepublishOnly` script for npm 7+
  * [actions] use `node/install` instead of `node/run`; use `codecov` action
  * [actions] remove unused workflow file
  * [Tests] run `nyc` on all tests; use `tape` runner
  * [Tests] use `available-typed-arrays`, `for-each`, `has-symbols`, `object-inspect`
  * [Dev Deps] update `available-typed-arrays`, `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape`

1.2.3 / 2021-01-31
=================
  * [Fix] `document.all` is callable (do not use `document.all`!)
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape`
  * [Tests] migrate tests to Github Actions
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event

1.2.2 / 2020-09-21
=================
  * [Fix] include actual fix from 579179e
  * [Dev Deps] update `eslint`

1.2.1 / 2020-09-09
=================
  * [Fix] phantomjs‘ Reflect.apply does not throw properly on a bad array-like
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`
  * [meta] fix eclint error

1.2.0 / 2020-06-02
=================
  * [New] use `Reflect.apply`‑based callability detection
  * [readme] add install instructions (#55)
  * [meta] only run `aud` on prod deps
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `make-arrow-function`, `make-generator-function`; add `aud`, `safe-publish-latest`, `make-async-function`
  * [Tests] add tests for function proxies (#53, #25)

1.1.5 / 2019-12-18
=================
  * [meta] remove unused Makefile and associated utilities
  * [meta] add `funding` field; add FUNDING.yml
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `semver`, `tape`, `covert`, `rimraf`
  * [Tests] use shared travis configs
  * [Tests] use `eccheck` over `editorconfig-tools`
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] remove `jscs`
  * [actions] add automatic rebasing / merge commit blocking

1.1.4 / 2018-07-02
=================
  * [Fix] improve `class` and arrow function detection (#30, #31)
  * [Tests] on all latest node minors; improve matrix
  * [Dev Deps] update all dev deps

1.1.3 / 2016-02-27
=================
  * [Fix] ensure “class “ doesn’t screw up “class” detection
  * [Tests] up to `node` `v5.7`, `v4.3`
  * [Dev Deps] update to `eslint` v2, `@ljharb/eslint-config`, `jscs`

1.1.2 / 2016-01-15
=================
  * [Fix] Make sure comments don’t screw up “class” detection (#4)
  * [Tests] up to `node` `v5.3`
  * [Tests] Add `parallelshell`, run both `--es-staging` and stock tests at once
  * [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`
  * [Refactor] convert `isNonES6ClassFn` into `isES6ClassFn`

1.1.1 / 2015-11-30
=================
  * [Fix] do not throw when a non-function has a function in its [[Prototype]] (#2)
  * [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config`, `jscs`, `nsp`, `semver`
  * [Tests] up to `node` `v5.1`
  * [Tests] no longer allow node 0.8 to fail.
  * [Tests] fix npm upgrades in older nodes

1.1.0 / 2015-10-02
=================
  * [Fix] Some browsers report TypedArray constructors as `typeof object`
  * [New] return false for "class" constructors, when possible.
  * [Tests] up to `io.js` `v3.3`, `node` `v4.1`
  * [Dev Deps] update `eslint`, `editorconfig-tools`, `nsp`, `tape`, `semver`, `jscs`, `covert`, `make-arrow-function`
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG

1.0.4 / 2015-01-30
=================
  * If @@toStringTag is not present, use the old-school Object#toString test.

1.0.3 / 2015-01-29
=================
  * Add tests to ensure arrow functions are callable.
  * Refactor to aid optimization of non-try/catch code.

1.0.2 / 2015-01-29
=================
  * Fix broken package.json

1.0.1 / 2015-01-29
=================
  * Add early exit for typeof not "function"

1.0.0 / 2015-01-29
=================
  * Initial release.
