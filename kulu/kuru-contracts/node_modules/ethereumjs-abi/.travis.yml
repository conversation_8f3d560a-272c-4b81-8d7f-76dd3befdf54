language: node_js
node_js:
  - "8"
  - "9"
  - "10"
  - "11"
env:
  - CXX=g++-4.8
addons:
  apt:
    sources:
    - ubuntu-toolchain-r-test
    packages:
    - g++-4.8
env:
  global:
    - DISPLAY=:99.0
  matrix:
    - CXX=g++-4.8 TEST_SUITE=test
matrix:
  fast_finish: true
  include:
    - os: linux
      node_js: "10"
      env: CXX=g++-4.8 TEST_SUITE=coveralls
    - os: linux
      node_js: "10"
      env: CXX=g++-4.8 TEST_SUITE=lint
script: npm run $TEST_SUITE
