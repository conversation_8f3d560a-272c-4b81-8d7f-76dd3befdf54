{"name": "@types/bn.js", "version": "4.11.6", "description": "TypeScript definitions for bn.js", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/LogvinovLeon", "githubUsername": "LogvinovLeon"}, {"name": "<PERSON>", "url": "https://github.com/HenryNguyen5", "githubUsername": "HenryNguyen5"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Gilthoniel", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bn.js"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "ca69ec916d802a517fb065f342c3de9fdb96529c8876e50e1a2021da7c6a7cf6", "typeScriptVersion": "2.8"}