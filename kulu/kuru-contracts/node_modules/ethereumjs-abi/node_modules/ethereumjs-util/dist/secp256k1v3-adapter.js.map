{"version": 3, "file": "secp256k1v3-adapter.js", "sourceRoot": "", "sources": ["../src/secp256k1v3-adapter.ts"], "names": [], "mappings": ";;;AAAA,IAAM,SAAS,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAA;AAC5D,IAAM,WAAW,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAA;AACtD,IAAM,GAAG,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAA;AAwB5C;;;;;GAKG;AACU,QAAA,gBAAgB,GAAG,UAAS,UAAkB;IACzD,+DAA+D;IAC/D,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,OAAO,KAAK,CAAA;KACb;IAED,OAAO,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;AAChE,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,UAAS,UAAkB,EAAE,UAAoB;IAC/E,+DAA+D;IAC/D,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,MAAM,IAAI,UAAU,CAAC,+BAA+B,CAAC,CAAA;KACtD;IAED,IAAM,SAAS,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;IAEtE,OAAO,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;AAChE,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,gBAAgB,GAAG,UAAS,UAAkB;IACzD,8DAA8D;IAC9D,qCAAqC;IACrC,UAAU,GAAG,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IAC7C,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,IAAI,wBAAgB,CAAC,UAAU,CAAC,EAAE;QACnF,OAAO,UAAU,CAAA;KAClB;IAED,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;AACpD,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,gBAAgB,GAAG,UAAS,UAAkB;IACzD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;AAC7E,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,oBAAoB,GAAG,UAAS,UAAkB;IAC7D,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;KACjD;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;AACnF,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,UAAS,UAAkB,EAAE,KAAa;IAC1E,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;AACtF,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,UAAS,UAAkB,EAAE,KAAa;IAC1E,OAAO,MAAM,CAAC,IAAI,CAChB,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAClF,CAAA;AACH,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,UAAS,UAAkB,EAAE,UAAoB;IAC9E,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,UAAS,SAAiB,EAAE,UAAoB;IAC9E,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,eAAe,GAAG,UAAS,SAAiB;IACvD,oEAAoE;IACpE,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE;QACtD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;AAC9D,CAAC,CAAA;AAED;;;;;;;GAOG;AACU,QAAA,iBAAiB,GAAG,UAC/B,SAAiB,EACjB,KAAa,EACb,UAAoB;IAEpB,OAAO,MAAM,CAAC,IAAI,CAChB,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,CAC5F,CAAA;AACH,CAAC,CAAA;AAED;;;;;;;GAOG;AACU,QAAA,iBAAiB,GAAG,UAC/B,SAAiB,EACjB,KAAa,EACb,UAAoB;IAEpB,OAAO,MAAM,CAAC,IAAI,CAChB,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,CAC5F,CAAA;AACH,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,UAAS,UAAoB,EAAE,UAAoB;IACjF,IAAM,IAAI,GAAiB,EAAE,CAAA;IAC7B,UAAU,CAAC,OAAO,CAAC,UAAC,SAAiB;QACnC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAA;AAClE,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,kBAAkB,GAAG,UAAS,SAAiB;IAC1D,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;AAC9E,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,eAAe,GAAG,UAAS,SAAiB;IACvD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;AAC3E,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,eAAe,GAAG,UAAS,SAAiB;IACvD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;AAC3E,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,kBAAkB,GAAG,UAAS,SAAiB;IAC1D,gEAAgE;IAChE,qCAAqC;IACrC,0CAA0C;IAC1C,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,MAAM,IAAI,UAAU,CAAC,6BAA6B,CAAC,CAAA;KACpD;IAED,IAAM,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAA;IAChD,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;KAChD;IAED,OAAO,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;AAC5C,CAAC,CAAA;AAED;;;;;;;GAOG;AACU,QAAA,IAAI,GAAG,UAClB,OAAe,EACf,UAAkB,EAClB,OAAqB;IAErB,IAAI,OAAO,KAAK,IAAI,EAAE;QACpB,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;KACnD;IAED,IAAI,WAAW,GAA8B,SAAS,CAAA;IAEtD,IAAI,OAAO,EAAE;QACX,WAAW,GAAG,EAAE,CAAA;QAEhB,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;YACzB,8BAA8B;YAC9B,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAA;SACvD;QAED,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;gBAC7B,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAA;aACvD;YAED,WAAW,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SAChD;QAED,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAA;SAC5D;QAED,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,4CAA4C;YAC5C,WAAW,CAAC,OAAO,GAAG,UACpB,OAAmB,EACnB,UAAsB,EACtB,IAAuB,EACvB,IAAuB,EACvB,OAAe;gBAEf,IAAM,UAAU,GAAkB,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;gBACzE,IAAM,UAAU,GAAkB,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;gBAEzE,IAAI,MAAM,GAAW,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAEpC,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,MAAM,GAAG,OAAO,CAAC,OAAO,CACtB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EACpB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EACvB,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAA;iBACF;gBAED,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA;YAC/B,CAAC,CAAA;SACF;KACF;IAED,IAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAC7B,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EACxB,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAC3B,WAAW,CACZ,CAAA;IAED,OAAO;QACL,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;QACrC,QAAQ,EAAE,GAAG,CAAC,KAAK;KACpB,CAAA;AACH,CAAC,CAAA;AAED;;;;;;;GAOG;AACU,QAAA,MAAM,GAAG,UAAS,OAAe,EAAE,SAAiB,EAAE,SAAiB;IAClF,OAAO,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;AAC/F,CAAC,CAAA;AAED;;;;;;;;GAQG;AACU,QAAA,OAAO,GAAG,UACrB,OAAe,EACf,SAAiB,EACjB,KAAa,EACb,UAAoB;IAEpB,OAAO,MAAM,CAAC,IAAI,CAChB,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,CAChG,CAAA;AACH,CAAC,CAAA;AAED;;;;;;GAMG;AACU,QAAA,IAAI,GAAG,UAAS,SAAiB,EAAE,UAAkB;IAChE,sDAAsD;IACtD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AACjG,CAAC,CAAA;AAEY,QAAA,UAAU,GAAG,UACxB,SAAiB,EACjB,UAAkB,EAClB,UAAoB;IAEpB,wDAAwD;IACxD,qCAAqC;IACrC,gCAAgC;IAChC,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE;QACtD,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAA;KACrD;IAED,iCAAiC;IACjC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,MAAM,IAAI,UAAU,CAAC,+BAA+B,CAAC,CAAA;KACtD;IAED,OAAO,MAAM,CAAC,IAAI,CAChB,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAC5F,CAAA;AACH,CAAC,CAAA"}