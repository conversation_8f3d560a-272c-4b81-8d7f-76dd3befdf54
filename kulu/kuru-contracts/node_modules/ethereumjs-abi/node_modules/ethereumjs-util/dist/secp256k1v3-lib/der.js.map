{"version": 3, "file": "der.js", "sourceRoot": "", "sources": ["../../src/secp256k1v3-lib/der.ts"], "names": [], "mappings": ";AAAA,0CAA0C;AAC1C,qEAAqE;;AAIrE,IAAM,gCAAgC,GAAG,MAAM,CAAC,IAAI,CAAC;IACnD,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,cAAc;IACd,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAA<PERSON>;IACJ,IAAI;IACJ,IAA<PERSON>;I<PERSON><PERSON>,IAA<PERSON>;<PERSON><PERSON><PERSON>,IAA<PERSON>;IACJ,IAA<PERSON>;IACJ,IAA<PERSON>;IACJ,IAA<PERSON>;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,aAAa;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC,CAAA;AAEF,IAAM,kCAAkC,GAAG,MAAM,CAAC,IAAI,CAAC;IACrD,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,cAAc;IACd,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,aAAa;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAC,CAAA;AAEF,OAAO,CAAC,gBAAgB,GAAG,UACzB,UAAkB,EAClB,SAAiB,EACjB,UAA0B;IAA1B,2BAAA,EAAA,iBAA0B;IAE1B,IAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CACxB,UAAU,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,kCAAkC,CACnF,CAAA;IACD,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3C,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IAC9C,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,OAAO,CAAC,gBAAgB,GAAG,UAAS,UAAkB;IACpD,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAA;IAEhC,kBAAkB;IAClB,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IACjE,KAAK,IAAI,CAAC,CAAA;IAEV,8BAA8B;IAC9B,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAAE,OAAO,IAAI,CAAA;IAElE,IAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;IACrC,KAAK,IAAI,CAAC,CAAA;IACV,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC;QAAE,OAAO,IAAI,CAAA;IACrC,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI;QAAE,OAAO,IAAI,CAAA;IAEtC,kBAAkB;IAClB,IAAM,GAAG,GAAG,UAAU,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7F,KAAK,IAAI,IAAI,CAAA;IACb,IAAI,MAAM,GAAG,KAAK,GAAG,GAAG;QAAE,OAAO,IAAI,CAAA;IAErC,0CAA0C;IAC1C,IACE,MAAM,GAAG,KAAK,GAAG,CAAC;QAClB,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI;QAC1B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI;QAC9B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,EAC9B;QACA,OAAO,IAAI,CAAA;KACZ;IACD,KAAK,IAAI,CAAC,CAAA;IAEV,mDAAmD;IACnD,IACE,MAAM,GAAG,KAAK,GAAG,CAAC;QAClB,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI;QAC1B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;QAC5B,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,EAC1C;QACA,OAAO,IAAI,CAAA;KACZ;IAED,OAAO,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;AACvE,CAAC,CAAA;AAED,OAAO,CAAC,kBAAkB,GAAG,UAAS,SAAiB;IACrD,IAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC7B,IAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAE7B,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAA;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,oBAAoB;IACpB,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;QAC/B,OAAO,IAAI,CAAA;KACZ;IAED,uBAAuB;IACvB,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,CAAA;IAChC,IAAI,OAAO,GAAG,IAAI,EAAE;QAClB,KAAK,IAAI,OAAO,GAAG,IAAI,CAAA;QACvB,IAAI,KAAK,GAAG,MAAM,EAAE;YAClB,OAAO,IAAI,CAAA;SACZ;KACF;IAED,0BAA0B;IAC1B,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;QAC/B,OAAO,IAAI,CAAA;KACZ;IAED,eAAe;IACf,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,CAAA;IAC7B,IAAI,IAAI,GAAG,IAAI,EAAE;QACf,OAAO,GAAG,IAAI,GAAG,IAAI,CAAA;QACrB,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM,EAAE;YAC5B,OAAO,IAAI,CAAA;SACZ;QACD,OAAO,OAAO,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;YAAC,CAAC;QAC3E,KAAK,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;YAAE,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAA;KAC5F;IACD,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,EAAE;QACzB,OAAO,IAAI,CAAA;KACZ;IACD,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,KAAK,IAAI,IAAI,CAAA;IAEb,0BAA0B;IAC1B,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;QAC/B,OAAO,IAAI,CAAA;KACZ;IAED,eAAe;IACf,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,CAAA;IAC7B,IAAI,IAAI,GAAG,IAAI,EAAE;QACf,OAAO,GAAG,IAAI,GAAG,IAAI,CAAA;QACrB,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM,EAAE;YAC5B,OAAO,IAAI,CAAA;SACZ;QACD,OAAO,OAAO,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;YAAC,CAAC;QAC3E,KAAK,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;YAAE,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAA;KAC5F;IACD,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,EAAE;QACzB,OAAO,IAAI,CAAA;KACZ;IACD,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,KAAK,IAAI,IAAI,CAAA;IAEb,4BAA4B;IAC5B,OAAO,IAAI,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;QAAC,CAAC;IACvE,eAAe;IACf,IAAI,IAAI,GAAG,EAAE,EAAE;QACb,OAAO,IAAI,CAAA;KACZ;IACD,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAA;IACrD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;IAElC,4BAA4B;IAC5B,OAAO,IAAI,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;QAAC,CAAC;IACvE,eAAe;IACf,IAAI,IAAI,GAAG,EAAE,EAAE;QACb,OAAO,IAAI,CAAA;KACZ;IACD,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAA;IACrD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;IAElC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;AACvB,CAAC,CAAA"}