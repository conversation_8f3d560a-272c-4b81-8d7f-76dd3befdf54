{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../src/signature.ts"], "names": [], "mappings": ";;;AAAA,IAAM,SAAS,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAA;AAClD,0BAA4B;AAC5B,iCAAyE;AACzE,+BAA+B;AAQ/B;;GAEG;AACU,QAAA,MAAM,GAAG,UACpB,OAAe,EACf,UAAkB,EAClB,OAAgB;IAEhB,IAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IAC/C,IAAM,QAAQ,GAAW,GAAG,CAAC,QAAQ,CAAA;IAErC,IAAM,GAAG,GAAG;QACV,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QAC7B,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;QAC9B,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE;KAC3D,CAAA;IAED,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,SAAS,GAAG,UACvB,OAAe,EACf,CAAS,EACT,CAAS,EACT,CAAS,EACT,OAAgB;IAEhB,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,iBAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,iBAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACzE,IAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IACD,IAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAA;IACpE,OAAO,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AACjE,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,QAAQ,GAAG,UAAS,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,OAAgB;IAChF,IAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,6EAA6E;IAC7E,OAAO,mBAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,qBAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,qBAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,gBAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9F,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,UAAU,GAAG,UAAS,GAAW;IAC5C,IAAM,GAAG,GAAW,gBAAQ,CAAC,GAAG,CAAC,CAAA;IAEjC,6EAA6E;IAC7E,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAA;IACf,gDAAgD;IAChD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,CAAC,IAAI,EAAE,CAAA;KACR;IAED,OAAO;QACL,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QACnB,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;KACrB,CAAA;AACH,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,gBAAgB,GAAG,UAC9B,CAAS,EACT,CAAS,EACT,CAAS,EACT,gBAAgC,EAChC,OAAgB;IADhB,iCAAA,EAAA,uBAAgC;IAGhC,IAAM,iBAAiB,GAAG,IAAI,EAAE,CAC9B,kEAAkE,EAClE,EAAE,CACH,CAAA;IACD,IAAM,WAAW,GAAG,IAAI,EAAE,CAAC,kEAAkE,EAAE,EAAE,CAAC,CAAA;IAElG,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;QACtC,OAAO,KAAK,CAAA;KACb;IAED,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;IAED,IAAM,GAAG,GAAO,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;IACzB,IAAM,GAAG,GAAO,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;IAEzB,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAO,KAAK,CAAA;KACb;IAED,IAAI,gBAAgB,IAAI,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED;;;;;GAKG;AACU,QAAA,mBAAmB,GAAG,UAAS,OAAe;IACzD,IAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CACxB,qCAAmC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAI,EAC9D,OAAO,CACR,CAAA;IACD,OAAO,aAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;AACjD,CAAC,CAAA;AAED,SAAS,oBAAoB,CAAC,CAAS,EAAE,OAAgB;IACvD,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;AAClD,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,OAAO,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAA;AACzC,CAAC"}