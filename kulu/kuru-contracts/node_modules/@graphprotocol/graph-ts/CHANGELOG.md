# @graphprotocol/graph-ts

## 0.31.0

### Minor Changes

- [#1340](https://github.com/graphprotocol/graph-tooling/pull/1340)
  [`2375877`](https://github.com/graphprotocol/graph-tooling/commit/23758774b33b5b7c6934f57a3e137870205ca6f0)
  Thanks [@incrypto32](https://github.com/incrypto32)! - export `loadRelated` host function

- [#1296](https://github.com/graphprotocol/graph-tooling/pull/1296)
  [`dab4ca1`](https://github.com/graphprotocol/graph-tooling/commit/dab4ca1f5df7dcd0928bbaa20304f41d23b20ced)
  Thanks [@dotansimha](https://github.com/dotansimha)! - Added support for handling GraphQL `Int8`
  scalar as `i64` (AssemblyScript)

## 0.30.0

### Minor Changes

- [#1299](https://github.com/graphprotocol/graph-tooling/pull/1299)
  [`3f8b514`](https://github.com/graphprotocol/graph-tooling/commit/3f8b51440db281e69879be7d91d79cd43e45fe86)
  Thanks [@saihaj](https://github.com/saihaj)! - introduce new Etherum utility to get a CREATE2
  Address

- [#1306](https://github.com/graphprotocol/graph-tooling/pull/1306)
  [`f5e4b58`](https://github.com/graphprotocol/graph-tooling/commit/f5e4b58989edc5f3bb8211f1b912449e77832de8)
  Thanks [@saihaj](https://github.com/saihaj)! - expose Host's `get_in_block` function

## 0.29.3

### Patch Changes

- [#1057](https://github.com/graphprotocol/graph-tooling/pull/1057)
  [`b7a2ec3`](https://github.com/graphprotocol/graph-tooling/commit/b7a2ec3e9e2206142236f892e2314118d410ac93)
  Thanks [@saihaj](https://github.com/saihaj)! - fix publihsed contents

## 0.29.2

### Patch Changes

- [#1044](https://github.com/graphprotocol/graph-tooling/pull/1044)
  [`8367f90`](https://github.com/graphprotocol/graph-tooling/commit/8367f90167172181870c1a7fe5b3e84d2c5aeb2c)
  Thanks [@saihaj](https://github.com/saihaj)! - publish readme with packages
