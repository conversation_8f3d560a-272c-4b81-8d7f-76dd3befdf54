# The Graph TypeScript Library (graph-ts)

[![npm (scoped)](https://img.shields.io/npm/v/@graphprotocol/graph-ts.svg)](https://www.npmjs.com/package/@graphprotocol/graph-ts)
[![Build Status](https://travis-ci.org/graphprotocol/graph-ts.svg?branch=master)](https://travis-ci.org/graphprotocol/graph-ts)

TypeScript/AssemblyScript library for writing subgraph mappings to be deployed to
[The Graph](https://github.com/graphprotocol/graph-node).

## Usage

For a detailed guide on how to create a subgraph, please see the
[Graph CLI docs](https://github.com/graphprotocol/graph-cli).

One step of creating the subgraph is writing mappings that will process blockchain events and will
write entities into the store. These mappings are written in TypeScript/AssemblyScript.

The `graph-ts` library provides APIs to access the Graph Node store, blockchain data, smart
contracts, data on IPFS, cryptographic functions and more. To use it, all you have to do is add a
dependency on it:

```sh
npm install --dev @graphprotocol/graph-ts # NPM
yarn add --dev @graphprotocol/graph-ts    # Yarn
```

After that, you can import the `store` API and other features from this library in your mappings. A
few examples:

```typescript
import { store, crypto } from '@graphprotocol/graph-ts'

// This is just an example event type generated by `graph-cli`
// from an Ethereum smart contract ABI
import { NameRegistered } from './types/abis/SomeContract'

// This is an example of an entity type generated from a
// subgraph's GraphQL schema
import { Domain } from './types/schema'

function handleNameRegistered(event: NameRegistered) {
  // Example use of a crypto function
  let id = crypto.keccak256(name).toHexString()

  // Example use of the generated `Entry` class
  let domain = new Domain()
  domain.name = name
  domain.owner = event.params.owner
  domain.timeRegistered = event.block.timestamp

  // Example use of the store API
  store.set('Name', id, entity)
}
```

## Helper Functions for AssemblyScript

Refer to the `helper-functions.ts` file in this repository for a few common functions that help
build on top of the AssemblyScript library, such as byte array concatenation, among others.

## API

Documentation on the API can be found
[here](https://thegraph.com/docs/en/developer/assemblyscript-api/).

For examples of `graph-ts` in use take a look at one of the following subgraphs:

- https://github.com/graphprotocol/ens-subgraph
- https://github.com/graphprotocol/decentraland-subgraph
- https://github.com/graphprotocol/adchain-subgraph
- https://github.com/graphprotocol/0x-subgraph
- https://github.com/graphprotocol/aragon-subgraph
- https://github.com/graphprotocol/dharma-subgraph

## License

Copyright &copy; 2018 Graph Protocol, Inc. and contributors.

The Graph TypeScript library is dual-licensed under the [MIT license](LICENSE-MIT) and the
[Apache License, Version 2.0](LICENSE-APACHE).

Unless required by applicable law or agreed to in writing, software distributed under the License is
distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
implied. See the License for the specific language governing permissions and limitations under the
License.
