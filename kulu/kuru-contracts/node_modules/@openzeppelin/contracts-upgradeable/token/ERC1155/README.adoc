= ERC 1155

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/token/erc1155

This set of interfaces and contracts are all related to the https://eips.ethereum.org/EIPS/eip-1155[ERC1155 Multi Token Standard].

The EIP consists of three interfaces which fulfill different roles, found here as {IERC1155}, {IERC1155MetadataURI} and {IERC1155Receiver}.

{ERC1155} implements the mandatory {IERC1155} interface, as well as the optional extension {IERC1155MetadataURI}, by relying on the substitution mechanism to use the same URI for all token types, dramatically reducing gas costs.

Additionally there are multiple custom extensions, including:

* designation of addresses that can pause token transfers for all users ({ERC1155Pausable}).
* destruction of own tokens ({ERC1155Burnable}).

NOTE: This core set of contracts is designed to be unopinionated, allowing developers to access the internal functions in ERC1155 (such as <<ERC1155-_mint-address-uint256-uint256-bytes-,`_mint`>>) and expose them as external functions in the way they prefer. On the other hand, xref:ROOT:erc1155.adoc#Presets[ERC1155 Presets] (such as {ERC1155PresetMinterPauser}) are designed using opinionated patterns to provide developers with ready to use, deployable contracts.

== Core

{{IERC1155}}

{{IERC1155MetadataURI}}

{{ERC1155}}

{{IERC1155Receiver}}

{{ERC1155Receiver}}

== Extensions

{{ERC1155Pausable}}

{{ERC1155Burnable}}

{{ERC1155Supply}}

{{ERC1155URIStorage}}

== Presets

These contracts are preconfigured combinations of the above features. They can be used through inheritance or as models to copy and paste their source code.

{{ERC1155PresetMinterPauser}}

== Utilities

{{ERC1155Holder}}
