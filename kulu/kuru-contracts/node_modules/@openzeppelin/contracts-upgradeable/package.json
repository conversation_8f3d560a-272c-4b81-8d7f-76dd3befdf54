{"name": "@openzeppelin/contracts-upgradeable", "description": "Secure Smart Contract library for Solidity", "version": "4.9.2", "files": ["**/*.sol", "/build/contracts/*.json", "!/mocks/**/*"], "scripts": {"prepare": "bash ../scripts/prepare-contracts-package.sh", "prepare-docs": "cd ..; npm run prepare-docs"}, "repository": {"type": "git", "url": "https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable.git"}, "keywords": ["solidity", "ethereum", "smart", "contracts", "security", "zeppelin"], "author": "OpenZeppelin Community <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/OpenZeppelin/openzeppelin-contracts/issues"}, "homepage": "https://openzeppelin.com/contracts/"}