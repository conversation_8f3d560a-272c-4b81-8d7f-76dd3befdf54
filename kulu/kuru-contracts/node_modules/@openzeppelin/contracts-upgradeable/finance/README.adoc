= Finance

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/finance

This directory includes primitives for financial systems:

- {PaymentSplitter} allows to split Ether and ERC20 payments among a group of accounts. The sender does not need to be
  aware that the assets will be split in this way, since it is handled transparently by the contract. The split can be
  in equal parts or in any other arbitrary proportion.

- {VestingWallet} handles the vesting of Ether and ERC20 tokens for a given beneficiary. Custody of multiple tokens can
  be given to this contract, which will release the token to the beneficiary following a given, customizable, vesting
  schedule.

== Contracts

{{PaymentSplitter}}

{{VestingWallet}}
