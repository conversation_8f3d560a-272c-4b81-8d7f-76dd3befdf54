T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
T__18=19
T__19=20
T__20=21
T__21=22
T__22=23
T__23=24
T__24=25
T__25=26
T__26=27
T__27=28
T__28=29
T__29=30
T__30=31
T__31=32
T__32=33
T__33=34
T__34=35
T__35=36
T__36=37
T__37=38
T__38=39
T__39=40
T__40=41
T__41=42
T__42=43
T__43=44
T__44=45
T__45=46
T__46=47
T__47=48
T__48=49
T__49=50
T__50=51
T__51=52
T__52=53
T__53=54
T__54=55
T__55=56
T__56=57
T__57=58
T__58=59
T__59=60
T__60=61
T__61=62
T__62=63
T__63=64
T__64=65
T__65=66
T__66=67
T__67=68
T__68=69
T__69=70
T__70=71
T__71=72
T__72=73
T__73=74
T__74=75
T__75=76
T__76=77
T__77=78
T__78=79
T__79=80
T__80=81
T__81=82
T__82=83
T__83=84
T__84=85
T__85=86
T__86=87
T__87=88
T__88=89
T__89=90
T__90=91
T__91=92
T__92=93
T__93=94
T__94=95
T__95=96
T__96=97
Int=98
Uint=99
Byte=100
Fixed=101
Ufixed=102
BooleanLiteral=103
DecimalNumber=104
HexNumber=105
NumberUnit=106
HexLiteralFragment=107
ReservedKeyword=108
AnonymousKeyword=109
BreakKeyword=110
ConstantKeyword=111
ImmutableKeyword=112
ContinueKeyword=113
LeaveKeyword=114
ExternalKeyword=115
IndexedKeyword=116
InternalKeyword=117
PayableKeyword=118
PrivateKeyword=119
PublicKeyword=120
VirtualKeyword=121
PureKeyword=122
TypeKeyword=123
ViewKeyword=124
GlobalKeyword=125
ConstructorKeyword=126
FallbackKeyword=127
ReceiveKeyword=128
Identifier=129
StringLiteralFragment=130
VersionLiteral=131
WS=132
COMMENT=133
LINE_COMMENT=134
'pragma'=1
';'=2
'*'=3
'||'=4
'^'=5
'~'=6
'>='=7
'>'=8
'<'=9
'<='=10
'='=11
'as'=12
'import'=13
'from'=14
'{'=15
','=16
'}'=17
'abstract'=18
'contract'=19
'interface'=20
'library'=21
'is'=22
'('=23
')'=24
'error'=25
'using'=26
'for'=27
'struct'=28
'modifier'=29
'function'=30
'returns'=31
'event'=32
'enum'=33
'['=34
']'=35
'address'=36
'.'=37
'mapping'=38
'=>'=39
'memory'=40
'storage'=41
'calldata'=42
'if'=43
'else'=44
'try'=45
'catch'=46
'while'=47
'unchecked'=48
'assembly'=49
'do'=50
'return'=51
'throw'=52
'emit'=53
'revert'=54
'var'=55
'bool'=56
'string'=57
'byte'=58
'++'=59
'--'=60
'new'=61
':'=62
'+'=63
'-'=64
'after'=65
'delete'=66
'!'=67
'**'=68
'/'=69
'%'=70
'<<'=71
'>>'=72
'&'=73
'|'=74
'=='=75
'!='=76
'&&'=77
'?'=78
'|='=79
'^='=80
'&='=81
'<<='=82
'>>='=83
'+='=84
'-='=85
'*='=86
'/='=87
'%='=88
'let'=89
':='=90
'=:'=91
'switch'=92
'case'=93
'default'=94
'->'=95
'callback'=96
'override'=97
'anonymous'=109
'break'=110
'constant'=111
'immutable'=112
'continue'=113
'leave'=114
'external'=115
'indexed'=116
'internal'=117
'payable'=118
'private'=119
'public'=120
'virtual'=121
'pure'=122
'type'=123
'view'=124
'global'=125
'constructor'=126
'fallback'=127
'receive'=128
