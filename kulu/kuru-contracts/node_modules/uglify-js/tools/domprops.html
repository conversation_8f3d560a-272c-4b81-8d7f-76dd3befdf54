<!doctype html>
<html>
<body>
    <script>
        !function(G) {
            var domprops = [];
            var objs = [ G ];
            var tagNames = [
                "a",
                "abbr",
                "acronym",
                "address",
                "applet",
                "area",
                "article",
                "aside",
                "audio",
                "b",
                "base",
                "basefont",
                "bdi",
                "bdo",
                "bgsound",
                "big",
                "blink",
                "blockquote",
                "body",
                "br",
                "button",
                "canvas",
                "caption",
                "center",
                "checked",
                "cite",
                "code",
                "col",
                "colgroup",
                "command",
                "comment",
                "compact",
                "content",
                "data",
                "datalist",
                "dd",
                "declare",
                "defer",
                "del",
                "details",
                "dfn",
                "dialog",
                "dir",
                "disabled",
                "div",
                "dl",
                "dt",
                "element",
                "em",
                "embed",
                "fieldset",
                "figcaption",
                "figure",
                "font",
                "footer",
                "form",
                "frame",
                "frameset",
                "h1",
                "h2",
                "h3",
                "h4",
                "h5",
                "h6",
                "head",
                "header",
                "hgroup",
                "hr",
                "html",
                "i",
                "iframe",
                "image",
                "img",
                "input",
                "ins",
                "isindex",
                "ismap",
                "kbd",
                "keygen",
                "label",
                "legend",
                "li",
                "link",
                "listing",
                "main",
                "map",
                "mark",
                "marquee",
                "math",
                "menu",
                "menuitem",
                "meta",
                "meter",
                "multicol",
                "multiple",
                "nav",
                "nextid",
                "nobr",
                "noembed",
                "noframes",
                "nohref",
                "noresize",
                "noscript",
                "noshade",
                "nowrap",
                "object",
                "ol",
                "optgroup",
                "option",
                "output",
                "p",
                "param",
                "picture",
                "plaintext",
                "pre",
                "progress",
                "q",
                "rb",
                "readonly",
                "rp",
                "rt",
                "rtc",
                "ruby",
                "s",
                "samp",
                "script",
                "section",
                "select",
                "selected",
                "shadow",
                "slot",
                "small",
                "source",
                "spacer",
                "span",
                "strike",
                "strong",
                "style",
                "sub",
                "summary",
                "sup",
                "svg",
                "table",
                "tbody",
                "td",
                "template",
                "textarea",
                "tfoot",
                "th",
                "thead",
                "time",
                "title",
                "tr",
                "track",
                "tt",
                "u",
                "ul",
                "var",
                "video",
                "wbr",
                "xmp",
                "XXX",
            ];
            for (var n = 0; n < tagNames.length; n++) {
                add(document.createElement(tagNames[n]));
            }
            var nsNames = {
                "http://www.w3.org/1998/Math/MathML": [
                    "annotation",
                    "annotation-xml",
                    "maction",
                    "maligngroup",
                    "malignmark",
                    "math",
                    "menclose",
                    "merror",
                    "mfenced",
                    "mfrac",
                    "mglyph",
                    "mi",
                    "mlabeledtr",
                    "mlongdiv",
                    "mmultiscripts",
                    "mn",
                    "mo",
                    "mover",
                    "mpadded",
                    "mphantom",
                    "mprescripts",
                    "mroot",
                    "mrow",
                    "ms",
                    "mscarries",
                    "mscarry",
                    "msgroup",
                    "msline",
                    "mspace",
                    "msqrt",
                    "msrow",
                    "mstack",
                    "mstyle",
                    "msub",
                    "msubsup",
                    "msup",
                    "mtable",
                    "mtd",
                    "mtext",
                    "mtr",
                    "munder",
                    "munderover",
                    "none",
                    "semantics",
                ],
                "http://www.w3.org/2000/svg": [
                    "a",
                    "altGlyph",
                    "altGlyphDef",
                    "altGlyphItem",
                    "animate",
                    "animateColor",
                    "animateMotion",
                    "animateTransform",
                    "circle",
                    "clipPath",
                    "color-profile",
                    "cursor",
                    "defs",
                    "desc",
                    "discard",
                    "ellipse",
                    "feBlend",
                    "feColorMatrix",
                    "feComponentTransfer",
                    "feComposite",
                    "feConvolveMatrix",
                    "feDiffuseLighting",
                    "feDisplacementMap",
                    "feDistantLight",
                    "feDropShadow",
                    "feFlood",
                    "feFuncA",
                    "feFuncB",
                    "feFuncG",
                    "feFuncR",
                    "feGaussianBlur",
                    "feImage",
                    "feMerge",
                    "feMergeNode",
                    "feMorphology",
                    "feOffset",
                    "fePointLight",
                    "feSpecularLighting",
                    "feSpotLight",
                    "feTile",
                    "feTurbulence",
                    "filter",
                    "font",
                    "font-face",
                    "font-face-format",
                    "font-face-name",
                    "font-face-src",
                    "font-face-uri",
                    "foreignObject",
                    "g",
                    "glyph",
                    "glyphRef",
                    "hatch",
                    "hatchpath",
                    "hkern",
                    "image",
                    "line",
                    "linearGradient",
                    "marker",
                    "mask",
                    "mesh",
                    "meshgradient",
                    "meshpatch",
                    "meshrow",
                    "metadata",
                    "missing-glyph",
                    "mpath",
                    "path",
                    "pattern",
                    "polygon",
                    "polyline",
                    "radialGradient",
                    "rect",
                    "script",
                    "set",
                    "solidcolor",
                    "stop",
                    "style",
                    "svg",
                    "switch",
                    "symbol",
                    "text",
                    "textPath",
                    "title",
                    "tref",
                    "tspan",
                    "unknown",
                    "use",
                    "view",
                    "vkern",
                ],
            };
            if (document.createElementNS) for (var ns in nsNames) {
                for (var n = 0; n < nsNames[ns].length; n++) {
                    add(document.createElementNS(ns, nsNames[ns][n]));
                }
            }
            var skips = [
                G.alert,
                G.back,
                G.blur,
                G.captureEvents,
                G.clearImmediate,
                G.clearInterval,
                G.clearTimeout,
                G.close,
                G.confirm,
                G.console,
                G.dump,
                G.fetch,
                G.find,
                G.focus,
                G.forward,
                G.getAttention,
                G.history,
                G.home,
                G.location,
                G.moveBy,
                G.moveTo,
                G.navigator,
                G.open,
                G.openDialog,
                G.print,
                G.process,
                G.prompt,
                G.resizeBy,
                G.resizeTo,
                G.setImmediate,
                G.setInterval,
                G.setTimeout,
                G.showModalDialog,
                G.sizeToContent,
                G.stop,
            ];
            var types = [];
            var interfaces = [
                "beforeunloadevent",
                "compositionevent",
                "customevent",
                "devicemotionevent",
                "deviceorientationevent",
                "dragevent",
                "event",
                "events",
                "focusevent",
                "hashchangeevent",
                "htmlevents",
                "keyboardevent",
                "messageevent",
                "mouseevent",
                "mouseevents",
                "storageevent",
                "svgevents",
                "textevent",
                "touchevent",
                "uievent",
                "uievents",
            ];
            var i = 0, full = false;
            var addEvent = document.createEvent ? function(type) {
                if (~indexOf(types, type)) return;
                types.push(type);
                for (var j = 0; j < interfaces.length; j++) try {
                    var event = document.createEvent(interfaces[j]);
                    event.initEvent(type, true, true);
                    add(event);
                } catch (e) {}
            } : function() {};
            var scanProperties = Object.getOwnPropertyNames ? function(o, fn) {
                var names = Object.getOwnPropertyNames(o);
                names.forEach(fn);
                for (var k in o) if (!~indexOf(names, k)) fn(k);
            } : function(o, fn) {
                for (var k in o) fn(k);
            };
            setTimeout(function next() {
                for (var j = 10; --j >= 0 && i < objs.length; i++) {
                    var o = objs[i];
                    var skip = ~indexOf(skips, o);
                    try {
                        scanProperties(o, function(k) {
                            if (!~indexOf(domprops, k)) domprops.push(k);
                            if (/^on/.test(k)) addEvent(k.slice(2));
                            if (!full) try {
                                add(o[k]);
                            } catch (e) {}
                        });
                    } catch (e) {}
                    if (skip || full) continue;
                    try {
                        add(o.__proto__);
                    } catch (e) {}
                    try {
                        add(o.prototype);
                    } catch (e) {}
                    try {
                        add(new o());
                    } catch (e) {}
                    try {
                        add(o());
                    } catch (e) {}
                }
                if (!full && objs.length > 20000) {
                    alert(objs.length);
                    full = true;
                }
                if (i < objs.length) {
                    setTimeout(next, 0);
                } else {
                    document.write('<pre>[\n    "' + domprops.sort().join('",\n    "').replace(/&/g, "&amp;").replace(/</g, "&lt;") + '"\n]</pre>');
                }
            }, 0);

            function add(o) {
                if (o) switch (typeof o) {
                case "function":
                case "object":
                    if (!~indexOf(objs, o)) objs.push(o);
                }
            }

            function indexOf(list, value) {
                var j = list.length;
                while (--j >= 0) {
                    if (list[j] === value) break;
                }
                return j;
            }
        }(function() {
            return this;
        }());
    </script>
</body>
</html>
