!function(t,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define("ethUnit",[],r):"object"==typeof exports?exports.ethUnit=r():t.ethUnit=r()}(this,function(){return function(t){function r(e){if(i[e])return i[e].exports;var n=i[e]={i:e,l:!1,exports:{}};return t[e].call(n.exports,n,n.exports,r),n.l=!0,n.exports}var i={};return r.m=t,r.c=i,r.i=function(t){return t},r.d=function(t,r,i){Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:i})},r.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},r.p="",r(r.s=3)}([function(t,r,i){(function(t){!function(t,r){"use strict";function e(t,r){if(!t)throw Error(r||"Assertion failed")}function n(t,r){t.super_=r;var i=function(){};i.prototype=r.prototype,t.prototype=new i,t.prototype.constructor=t}function o(t,r,i){return o.isBN(t)?t:(this.negative=0,this.words=null,this.length=0,this.red=null,void(null!==t&&("le"!==r&&"be"!==r||(i=r,r=10),this._init(t||0,r||10,i||"be"))))}function h(t,r,i){for(var e=0,n=Math.min(t.length,i),o=r;n>o;o++){var h=t.charCodeAt(o)-48;e<<=4,e|=49>h||h>54?17>h||h>22?15&h:h-17+10:h-49+10}return e}function s(t,r,i,e){for(var n=0,o=Math.min(t.length,i),h=r;o>h;h++){var s=t.charCodeAt(h)-48;n*=e,n+=49>s?17>s?s:s-17+10:s-49+10}return n}function u(t){for(var r=Array(t.bitLength()),i=0;r.length>i;i++){var e=i/26|0,n=i%26;r[i]=(t.words[e]&1<<n)>>>n}return r}function a(t,r,i){i.negative=r.negative^t.negative;var e=t.length+r.length|0;i.length=e,e=e-1|0;var n=0|t.words[0],o=0|r.words[0],h=n*o,s=67108863&h,u=h/67108864|0;i.words[0]=s;for(var a=1;e>a;a++){for(var f=u>>>26,l=67108863&u,m=Math.min(a,r.length-1),p=Math.max(0,a-t.length+1);m>=p;p++){var d=a-p|0;n=0|t.words[d],o=0|r.words[p],h=n*o+l,f+=h/67108864|0,l=67108863&h}i.words[a]=0|l,u=0|f}return 0!==u?i.words[a]=0|u:i.length--,i.strip()}function f(t,r,i){i.negative=r.negative^t.negative,i.length=t.length+r.length;for(var e=0,n=0,o=0;i.length-1>o;o++){var h=n;n=0;for(var s=67108863&e,u=Math.min(o,r.length-1),a=Math.max(0,o-t.length+1);u>=a;a++){var f=o-a,l=0|t.words[f],m=0|r.words[a],p=l*m,d=67108863&p;h=h+(p/67108864|0)|0,d=d+s|0,s=67108863&d,h=h+(d>>>26)|0,n+=h>>>26,h&=67108863}i.words[o]=s,e=h,h=n}return 0!==e?i.words[o]=e:i.length--,i.strip()}function l(t,r,i){var e=new m;return e.mulp(t,r,i)}function m(t,r){this.x=t,this.y=r}function p(t,r){this.name=t,this.p=new o(r,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function d(){p.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function c(){p.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function g(){p.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function v(){p.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function y(t){if("string"==typeof t){var r=o._prime(t);this.m=r.p,this.prime=r}else e(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function w(t){y.call(this,t),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}"object"==typeof t?t.exports=o:r.BN=o,o.BN=o,o.wordSize=26;var M;try{M=i(1).Buffer}catch(b){}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,r){return t.cmp(r)>0?t:r},o.min=function(t,r){return t.cmp(r)<0?t:r},o.prototype._init=function(t,r,i){if("number"==typeof t)return this._initNumber(t,r,i);if("object"==typeof t)return this._initArray(t,r,i);"hex"===r&&(r=16),e(r===(0|r)&&r>=2&&36>=r),t=(""+t).replace(/\s+/g,"");var n=0;"-"===t[0]&&n++,16===r?this._parseHex(t,n):this._parseBase(t,r,n),"-"===t[0]&&(this.negative=1),this.strip(),"le"===i&&this._initArray(this.toArray(),r,i)},o.prototype._initNumber=function(t,r,i){0>t&&(this.negative=1,t=-t),67108864>t?(this.words=[67108863&t],this.length=1):4503599627370496>t?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(e(9007199254740992>t),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===i&&this._initArray(this.toArray(),r,i)},o.prototype._initArray=function(t,r,i){if(e("number"==typeof t.length),0>=t.length)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=Array(this.length);for(var n=0;this.length>n;n++)this.words[n]=0;var o,h,s=0;if("be"===i)for(n=t.length-1,o=0;n>=0;n-=3)h=t[n]|t[n-1]<<8|t[n-2]<<16,this.words[o]|=h<<s&67108863,this.words[o+1]=h>>>26-s&67108863,s+=24,26>s||(s-=26,o++);else if("le"===i)for(n=0,o=0;t.length>n;n+=3)h=t[n]|t[n+1]<<8|t[n+2]<<16,this.words[o]|=h<<s&67108863,this.words[o+1]=h>>>26-s&67108863,s+=24,26>s||(s-=26,o++);return this.strip()},o.prototype._parseHex=function(t,r){this.length=Math.ceil((t.length-r)/6),this.words=Array(this.length);for(var i=0;this.length>i;i++)this.words[i]=0;var e,n,o=0;for(i=t.length-6,e=0;i>=r;i-=6)n=h(t,i,i+6),this.words[e]|=n<<o&67108863,this.words[e+1]|=n>>>26-o&4194303,o+=24,26>o||(o-=26,e++);i+6!==r&&(n=h(t,r,i+6),this.words[e]|=n<<o&67108863,this.words[e+1]|=n>>>26-o&4194303),this.strip()},o.prototype._parseBase=function(t,r,i){this.words=[0],this.length=1;for(var e=0,n=1;67108863>=n;n*=r)e++;e--,n=n/r|0;for(var o=t.length-i,h=o%e,u=Math.min(o,o-h)+i,a=0,f=i;u>f;f+=e)a=s(t,f,f+e,r),this.imuln(n),67108864>this.words[0]+a?this.words[0]+=a:this._iaddn(a);if(0!==h){var l=1;for(a=s(t,f,t.length,r),f=0;h>f;f++)l*=r;this.imuln(l),67108864>this.words[0]+a?this.words[0]+=a:this._iaddn(a)}},o.prototype.copy=function(t){t.words=Array(this.length);for(var r=0;this.length>r;r++)t.words[r]=this.words[r];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;t>this.length;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var _=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],A=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],E=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];o.prototype.toString=function(t,r){t=t||10,r=0|r||1;var i;if(16===t||"hex"===t){i="";for(var n=0,o=0,h=0;this.length>h;h++){var s=this.words[h],u=(16777215&(s<<n|o)).toString(16);o=s>>>24-n&16777215,i=0!==o||h!==this.length-1?_[6-u.length]+u+i:u+i,n+=2,26>n||(n-=26,h--)}for(0!==o&&(i=o.toString(16)+i);i.length%r!==0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}if(t===(0|t)&&t>=2&&36>=t){var a=A[t],f=E[t];i="";var l=this.clone();for(l.negative=0;!l.isZero();){var m=l.modn(f).toString(t);l=l.idivn(f),i=l.isZero()?m+i:_[a-m.length]+m+i}for(this.isZero()&&(i="0"+i);i.length%r!==0;)i="0"+i;return 0!==this.negative&&(i="-"+i),i}e(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&e(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,r){return e(void 0!==M),this.toArrayLike(M,t,r)},o.prototype.toArray=function(t,r){return this.toArrayLike(Array,t,r)},o.prototype.toArrayLike=function(t,r,i){var n=this.byteLength(),o=i||Math.max(1,n);e(o>=n,"byte array longer than desired length"),e(o>0,"Requested array length <= 0"),this.strip();var h,s,u="le"===r,a=new t(o),f=this.clone();if(u){for(s=0;!f.isZero();s++)h=f.andln(255),f.iushrn(8),a[s]=h;for(;o>s;s++)a[s]=0}else{for(s=0;o-n>s;s++)a[s]=0;for(s=0;!f.isZero();s++)h=f.andln(255),f.iushrn(8),a[o-s-1]=h}return a},o.prototype._countBits=Math.clz32?function(t){return 32-Math.clz32(t)}:function(t){var r=t,i=0;return 4096>r||(i+=13,r>>>=13),64>r||(i+=7,r>>>=7),8>r||(i+=4,r>>>=4),2>r||(i+=2,r>>>=2),i+r},o.prototype._zeroBits=function(t){if(0===t)return 26;var r=t,i=0;return 0===(8191&r)&&(i+=13,r>>>=13),0===(127&r)&&(i+=7,r>>>=7),0===(15&r)&&(i+=4,r>>>=4),0===(3&r)&&(i+=2,r>>>=2),0===(1&r)&&i++,i},o.prototype.bitLength=function(){var t=this.words[this.length-1],r=this._countBits(t);return 26*(this.length-1)+r},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,r=0;this.length>r;r++){var i=this._zeroBits(this.words[r]);if(t+=i,26!==i)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;t.length>this.length;)this.words[this.length++]=0;for(var r=0;t.length>r;r++)this.words[r]=this.words[r]|t.words[r];return this.strip()},o.prototype.ior=function(t){return e(0===(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var r;r=this.length>t.length?t:this;for(var i=0;r.length>i;i++)this.words[i]=this.words[i]&t.words[i];return this.length=r.length,this.strip()},o.prototype.iand=function(t){return e(0===(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var r,i;this.length>t.length?(r=this,i=t):(r=t,i=this);for(var e=0;i.length>e;e++)this.words[e]=r.words[e]^i.words[e];if(this!==r)for(;r.length>e;e++)this.words[e]=r.words[e];return this.length=r.length,this.strip()},o.prototype.ixor=function(t){return e(0===(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){e("number"==typeof t&&t>=0);var r=0|Math.ceil(t/26),i=t%26;this._expand(r),i>0&&r--;for(var n=0;r>n;n++)this.words[n]=67108863&~this.words[n];return i>0&&(this.words[n]=~this.words[n]&67108863>>26-i),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,r){e("number"==typeof t&&t>=0);var i=t/26|0,n=t%26;return this._expand(i+1),this.words[i]=r?this.words[i]|1<<n:this.words[i]&~(1<<n),this.strip()},o.prototype.iadd=function(t){var r;if(0!==this.negative&&0===t.negative)return this.negative=0,r=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,r=this.isub(t),t.negative=1,r._normSign();var i,e;this.length>t.length?(i=this,e=t):(i=t,e=this);for(var n=0,o=0;e.length>o;o++)r=(0|i.words[o])+(0|e.words[o])+n,this.words[o]=67108863&r,n=r>>>26;for(;0!==n&&i.length>o;o++)r=(0|i.words[o])+n,this.words[o]=67108863&r,n=r>>>26;if(this.length=i.length,0!==n)this.words[this.length]=n,this.length++;else if(i!==this)for(;i.length>o;o++)this.words[o]=i.words[o];return this},o.prototype.add=function(t){var r;return 0!==t.negative&&0===this.negative?(t.negative=0,r=this.sub(t),t.negative^=1,r):0===t.negative&&0!==this.negative?(this.negative=0,r=t.sub(this),this.negative=1,r):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var r=this.iadd(t);return t.negative=1,r._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;var e,n;i>0?(e=this,n=t):(e=t,n=this);for(var o=0,h=0;n.length>h;h++)r=(0|e.words[h])-(0|n.words[h])+o,o=r>>26,this.words[h]=67108863&r;for(;0!==o&&e.length>h;h++)r=(0|e.words[h])+o,o=r>>26,this.words[h]=67108863&r;if(0===o&&e.length>h&&e!==this)for(;e.length>h;h++)this.words[h]=e.words[h];return this.length=Math.max(this.length,h),e!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var R=function(t,r,i){var e,n,o,h=t.words,s=r.words,u=i.words,a=0,f=0|h[0],l=8191&f,m=f>>>13,p=0|h[1],d=8191&p,c=p>>>13,g=0|h[2],v=8191&g,y=g>>>13,w=0|h[3],M=8191&w,b=w>>>13,_=0|h[4],A=8191&_,E=_>>>13,R=0|h[5],B=8191&R,S=R>>>13,P=0|h[6],T=8191&P,x=P>>>13,U=0|h[7],k=8191&U,I=U>>>13,L=0|h[8],O=8191&L,Y=L>>>13,N=0|h[9],C=8191&N,D=N>>>13,j=0|s[0],Z=8191&j,q=j>>>13,z=0|s[1],F=8191&z,$=z>>>13,K=0|s[2],J=8191&K,V=K>>>13,H=0|s[3],W=8191&H,X=H>>>13,G=0|s[4],Q=8191&G,tt=G>>>13,rt=0|s[5],it=8191&rt,et=rt>>>13,nt=0|s[6],ot=8191&nt,ht=nt>>>13,st=0|s[7],ut=8191&st,at=st>>>13,ft=0|s[8],lt=8191&ft,mt=ft>>>13,pt=0|s[9],dt=8191&pt,ct=pt>>>13;i.negative=t.negative^r.negative,i.length=19,e=Math.imul(l,Z),n=Math.imul(l,q),n=n+Math.imul(m,Z)|0,o=Math.imul(m,q);var gt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(gt>>>26)|0,gt&=67108863,e=Math.imul(d,Z),n=Math.imul(d,q),n=n+Math.imul(c,Z)|0,o=Math.imul(c,q),e=e+Math.imul(l,F)|0,n=n+Math.imul(l,$)|0,n=n+Math.imul(m,F)|0,o=o+Math.imul(m,$)|0;var vt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(vt>>>26)|0,vt&=67108863,e=Math.imul(v,Z),n=Math.imul(v,q),n=n+Math.imul(y,Z)|0,o=Math.imul(y,q),e=e+Math.imul(d,F)|0,n=n+Math.imul(d,$)|0,n=n+Math.imul(c,F)|0,o=o+Math.imul(c,$)|0,e=e+Math.imul(l,J)|0,n=n+Math.imul(l,V)|0,n=n+Math.imul(m,J)|0,o=o+Math.imul(m,V)|0;var yt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(yt>>>26)|0,yt&=67108863,e=Math.imul(M,Z),n=Math.imul(M,q),n=n+Math.imul(b,Z)|0,o=Math.imul(b,q),e=e+Math.imul(v,F)|0,n=n+Math.imul(v,$)|0,n=n+Math.imul(y,F)|0,o=o+Math.imul(y,$)|0,e=e+Math.imul(d,J)|0,n=n+Math.imul(d,V)|0,n=n+Math.imul(c,J)|0,o=o+Math.imul(c,V)|0,e=e+Math.imul(l,W)|0,n=n+Math.imul(l,X)|0,n=n+Math.imul(m,W)|0,o=o+Math.imul(m,X)|0;var wt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(wt>>>26)|0,wt&=67108863,e=Math.imul(A,Z),n=Math.imul(A,q),n=n+Math.imul(E,Z)|0,o=Math.imul(E,q),e=e+Math.imul(M,F)|0,n=n+Math.imul(M,$)|0,n=n+Math.imul(b,F)|0,o=o+Math.imul(b,$)|0,e=e+Math.imul(v,J)|0,n=n+Math.imul(v,V)|0,n=n+Math.imul(y,J)|0,o=o+Math.imul(y,V)|0,e=e+Math.imul(d,W)|0,n=n+Math.imul(d,X)|0,n=n+Math.imul(c,W)|0,o=o+Math.imul(c,X)|0,e=e+Math.imul(l,Q)|0,n=n+Math.imul(l,tt)|0,n=n+Math.imul(m,Q)|0,o=o+Math.imul(m,tt)|0;var Mt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,e=Math.imul(B,Z),n=Math.imul(B,q),n=n+Math.imul(S,Z)|0,o=Math.imul(S,q),e=e+Math.imul(A,F)|0,n=n+Math.imul(A,$)|0,n=n+Math.imul(E,F)|0,o=o+Math.imul(E,$)|0,e=e+Math.imul(M,J)|0,n=n+Math.imul(M,V)|0,n=n+Math.imul(b,J)|0,o=o+Math.imul(b,V)|0,e=e+Math.imul(v,W)|0,n=n+Math.imul(v,X)|0,n=n+Math.imul(y,W)|0,o=o+Math.imul(y,X)|0,e=e+Math.imul(d,Q)|0,n=n+Math.imul(d,tt)|0,n=n+Math.imul(c,Q)|0,o=o+Math.imul(c,tt)|0,e=e+Math.imul(l,it)|0,n=n+Math.imul(l,et)|0,n=n+Math.imul(m,it)|0,o=o+Math.imul(m,et)|0;var bt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(bt>>>26)|0,bt&=67108863,e=Math.imul(T,Z),n=Math.imul(T,q),n=n+Math.imul(x,Z)|0,o=Math.imul(x,q),e=e+Math.imul(B,F)|0,n=n+Math.imul(B,$)|0,n=n+Math.imul(S,F)|0,o=o+Math.imul(S,$)|0,e=e+Math.imul(A,J)|0,n=n+Math.imul(A,V)|0,n=n+Math.imul(E,J)|0,o=o+Math.imul(E,V)|0,e=e+Math.imul(M,W)|0,n=n+Math.imul(M,X)|0,n=n+Math.imul(b,W)|0,o=o+Math.imul(b,X)|0,e=e+Math.imul(v,Q)|0,n=n+Math.imul(v,tt)|0,n=n+Math.imul(y,Q)|0,o=o+Math.imul(y,tt)|0,e=e+Math.imul(d,it)|0,n=n+Math.imul(d,et)|0,n=n+Math.imul(c,it)|0,o=o+Math.imul(c,et)|0,e=e+Math.imul(l,ot)|0,n=n+Math.imul(l,ht)|0,n=n+Math.imul(m,ot)|0,o=o+Math.imul(m,ht)|0;var _t=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(_t>>>26)|0,_t&=67108863,e=Math.imul(k,Z),n=Math.imul(k,q),n=n+Math.imul(I,Z)|0,o=Math.imul(I,q),e=e+Math.imul(T,F)|0,n=n+Math.imul(T,$)|0,n=n+Math.imul(x,F)|0,o=o+Math.imul(x,$)|0,e=e+Math.imul(B,J)|0,n=n+Math.imul(B,V)|0,n=n+Math.imul(S,J)|0,o=o+Math.imul(S,V)|0,e=e+Math.imul(A,W)|0,n=n+Math.imul(A,X)|0,n=n+Math.imul(E,W)|0,o=o+Math.imul(E,X)|0,e=e+Math.imul(M,Q)|0,n=n+Math.imul(M,tt)|0,n=n+Math.imul(b,Q)|0,o=o+Math.imul(b,tt)|0,e=e+Math.imul(v,it)|0,n=n+Math.imul(v,et)|0,n=n+Math.imul(y,it)|0,o=o+Math.imul(y,et)|0,e=e+Math.imul(d,ot)|0,n=n+Math.imul(d,ht)|0,n=n+Math.imul(c,ot)|0,o=o+Math.imul(c,ht)|0,e=e+Math.imul(l,ut)|0,n=n+Math.imul(l,at)|0,n=n+Math.imul(m,ut)|0,o=o+Math.imul(m,at)|0;var At=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(At>>>26)|0,At&=67108863,e=Math.imul(O,Z),n=Math.imul(O,q),n=n+Math.imul(Y,Z)|0,o=Math.imul(Y,q),e=e+Math.imul(k,F)|0,n=n+Math.imul(k,$)|0,n=n+Math.imul(I,F)|0,o=o+Math.imul(I,$)|0,e=e+Math.imul(T,J)|0,n=n+Math.imul(T,V)|0,n=n+Math.imul(x,J)|0,o=o+Math.imul(x,V)|0,e=e+Math.imul(B,W)|0,n=n+Math.imul(B,X)|0,n=n+Math.imul(S,W)|0,o=o+Math.imul(S,X)|0,e=e+Math.imul(A,Q)|0,n=n+Math.imul(A,tt)|0,n=n+Math.imul(E,Q)|0,o=o+Math.imul(E,tt)|0,e=e+Math.imul(M,it)|0,n=n+Math.imul(M,et)|0,n=n+Math.imul(b,it)|0,o=o+Math.imul(b,et)|0,e=e+Math.imul(v,ot)|0,n=n+Math.imul(v,ht)|0,n=n+Math.imul(y,ot)|0,o=o+Math.imul(y,ht)|0,e=e+Math.imul(d,ut)|0,n=n+Math.imul(d,at)|0,n=n+Math.imul(c,ut)|0,o=o+Math.imul(c,at)|0,e=e+Math.imul(l,lt)|0,n=n+Math.imul(l,mt)|0,n=n+Math.imul(m,lt)|0,o=o+Math.imul(m,mt)|0;var Et=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Et>>>26)|0,Et&=67108863,e=Math.imul(C,Z),n=Math.imul(C,q),n=n+Math.imul(D,Z)|0,o=Math.imul(D,q),e=e+Math.imul(O,F)|0,n=n+Math.imul(O,$)|0,n=n+Math.imul(Y,F)|0,o=o+Math.imul(Y,$)|0,e=e+Math.imul(k,J)|0,n=n+Math.imul(k,V)|0,n=n+Math.imul(I,J)|0,o=o+Math.imul(I,V)|0,e=e+Math.imul(T,W)|0,n=n+Math.imul(T,X)|0,n=n+Math.imul(x,W)|0,o=o+Math.imul(x,X)|0,e=e+Math.imul(B,Q)|0,n=n+Math.imul(B,tt)|0,n=n+Math.imul(S,Q)|0,o=o+Math.imul(S,tt)|0,e=e+Math.imul(A,it)|0,n=n+Math.imul(A,et)|0,n=n+Math.imul(E,it)|0,o=o+Math.imul(E,et)|0,e=e+Math.imul(M,ot)|0,n=n+Math.imul(M,ht)|0,n=n+Math.imul(b,ot)|0,o=o+Math.imul(b,ht)|0,e=e+Math.imul(v,ut)|0,n=n+Math.imul(v,at)|0,n=n+Math.imul(y,ut)|0,o=o+Math.imul(y,at)|0,e=e+Math.imul(d,lt)|0,n=n+Math.imul(d,mt)|0,n=n+Math.imul(c,lt)|0,o=o+Math.imul(c,mt)|0,e=e+Math.imul(l,dt)|0,n=n+Math.imul(l,ct)|0,n=n+Math.imul(m,dt)|0,o=o+Math.imul(m,ct)|0;var Rt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,e=Math.imul(C,F),n=Math.imul(C,$),n=n+Math.imul(D,F)|0,o=Math.imul(D,$),e=e+Math.imul(O,J)|0,n=n+Math.imul(O,V)|0,n=n+Math.imul(Y,J)|0,o=o+Math.imul(Y,V)|0,e=e+Math.imul(k,W)|0,n=n+Math.imul(k,X)|0,n=n+Math.imul(I,W)|0,o=o+Math.imul(I,X)|0,e=e+Math.imul(T,Q)|0,n=n+Math.imul(T,tt)|0,n=n+Math.imul(x,Q)|0,o=o+Math.imul(x,tt)|0,e=e+Math.imul(B,it)|0,n=n+Math.imul(B,et)|0,n=n+Math.imul(S,it)|0,o=o+Math.imul(S,et)|0,e=e+Math.imul(A,ot)|0,n=n+Math.imul(A,ht)|0,n=n+Math.imul(E,ot)|0,o=o+Math.imul(E,ht)|0,e=e+Math.imul(M,ut)|0,n=n+Math.imul(M,at)|0,n=n+Math.imul(b,ut)|0,o=o+Math.imul(b,at)|0,e=e+Math.imul(v,lt)|0,n=n+Math.imul(v,mt)|0,n=n+Math.imul(y,lt)|0,o=o+Math.imul(y,mt)|0,e=e+Math.imul(d,dt)|0,n=n+Math.imul(d,ct)|0,n=n+Math.imul(c,dt)|0,o=o+Math.imul(c,ct)|0;var Bt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,e=Math.imul(C,J),n=Math.imul(C,V),n=n+Math.imul(D,J)|0,o=Math.imul(D,V),e=e+Math.imul(O,W)|0,n=n+Math.imul(O,X)|0,n=n+Math.imul(Y,W)|0,o=o+Math.imul(Y,X)|0,e=e+Math.imul(k,Q)|0,n=n+Math.imul(k,tt)|0,n=n+Math.imul(I,Q)|0,o=o+Math.imul(I,tt)|0,e=e+Math.imul(T,it)|0,n=n+Math.imul(T,et)|0,n=n+Math.imul(x,it)|0,o=o+Math.imul(x,et)|0,e=e+Math.imul(B,ot)|0,n=n+Math.imul(B,ht)|0,n=n+Math.imul(S,ot)|0,o=o+Math.imul(S,ht)|0,e=e+Math.imul(A,ut)|0,n=n+Math.imul(A,at)|0,n=n+Math.imul(E,ut)|0,o=o+Math.imul(E,at)|0,e=e+Math.imul(M,lt)|0,n=n+Math.imul(M,mt)|0,n=n+Math.imul(b,lt)|0,o=o+Math.imul(b,mt)|0,e=e+Math.imul(v,dt)|0,n=n+Math.imul(v,ct)|0,n=n+Math.imul(y,dt)|0,o=o+Math.imul(y,ct)|0;var St=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(St>>>26)|0,St&=67108863,e=Math.imul(C,W),n=Math.imul(C,X),n=n+Math.imul(D,W)|0,o=Math.imul(D,X),e=e+Math.imul(O,Q)|0,n=n+Math.imul(O,tt)|0,n=n+Math.imul(Y,Q)|0,o=o+Math.imul(Y,tt)|0,e=e+Math.imul(k,it)|0,n=n+Math.imul(k,et)|0,n=n+Math.imul(I,it)|0,o=o+Math.imul(I,et)|0,e=e+Math.imul(T,ot)|0,n=n+Math.imul(T,ht)|0,n=n+Math.imul(x,ot)|0,o=o+Math.imul(x,ht)|0,e=e+Math.imul(B,ut)|0,n=n+Math.imul(B,at)|0,n=n+Math.imul(S,ut)|0,o=o+Math.imul(S,at)|0,e=e+Math.imul(A,lt)|0,n=n+Math.imul(A,mt)|0,n=n+Math.imul(E,lt)|0,o=o+Math.imul(E,mt)|0,e=e+Math.imul(M,dt)|0,n=n+Math.imul(M,ct)|0,n=n+Math.imul(b,dt)|0,o=o+Math.imul(b,ct)|0;var Pt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,e=Math.imul(C,Q),n=Math.imul(C,tt),n=n+Math.imul(D,Q)|0,o=Math.imul(D,tt),e=e+Math.imul(O,it)|0,n=n+Math.imul(O,et)|0,n=n+Math.imul(Y,it)|0,o=o+Math.imul(Y,et)|0,e=e+Math.imul(k,ot)|0,n=n+Math.imul(k,ht)|0,n=n+Math.imul(I,ot)|0,o=o+Math.imul(I,ht)|0,e=e+Math.imul(T,ut)|0,n=n+Math.imul(T,at)|0,n=n+Math.imul(x,ut)|0,o=o+Math.imul(x,at)|0,e=e+Math.imul(B,lt)|0,n=n+Math.imul(B,mt)|0,n=n+Math.imul(S,lt)|0,o=o+Math.imul(S,mt)|0,e=e+Math.imul(A,dt)|0,n=n+Math.imul(A,ct)|0,n=n+Math.imul(E,dt)|0,o=o+Math.imul(E,ct)|0;var Tt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,e=Math.imul(C,it),n=Math.imul(C,et),n=n+Math.imul(D,it)|0,o=Math.imul(D,et),e=e+Math.imul(O,ot)|0,n=n+Math.imul(O,ht)|0,n=n+Math.imul(Y,ot)|0,o=o+Math.imul(Y,ht)|0,e=e+Math.imul(k,ut)|0,n=n+Math.imul(k,at)|0,n=n+Math.imul(I,ut)|0,o=o+Math.imul(I,at)|0,e=e+Math.imul(T,lt)|0,n=n+Math.imul(T,mt)|0,n=n+Math.imul(x,lt)|0,o=o+Math.imul(x,mt)|0,e=e+Math.imul(B,dt)|0,n=n+Math.imul(B,ct)|0,n=n+Math.imul(S,dt)|0,o=o+Math.imul(S,ct)|0;var xt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(xt>>>26)|0,xt&=67108863,e=Math.imul(C,ot),n=Math.imul(C,ht),n=n+Math.imul(D,ot)|0,o=Math.imul(D,ht),e=e+Math.imul(O,ut)|0,n=n+Math.imul(O,at)|0,n=n+Math.imul(Y,ut)|0,o=o+Math.imul(Y,at)|0,e=e+Math.imul(k,lt)|0,n=n+Math.imul(k,mt)|0,n=n+Math.imul(I,lt)|0,o=o+Math.imul(I,mt)|0,e=e+Math.imul(T,dt)|0,n=n+Math.imul(T,ct)|0,n=n+Math.imul(x,dt)|0,o=o+Math.imul(x,ct)|0;var Ut=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(Ut>>>26)|0,Ut&=67108863,e=Math.imul(C,ut),n=Math.imul(C,at),n=n+Math.imul(D,ut)|0,o=Math.imul(D,at),e=e+Math.imul(O,lt)|0,n=n+Math.imul(O,mt)|0,n=n+Math.imul(Y,lt)|0,o=o+Math.imul(Y,mt)|0,e=e+Math.imul(k,dt)|0,n=n+Math.imul(k,ct)|0,n=n+Math.imul(I,dt)|0,o=o+Math.imul(I,ct)|0;var kt=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(kt>>>26)|0,kt&=67108863,e=Math.imul(C,lt),n=Math.imul(C,mt),n=n+Math.imul(D,lt)|0,o=Math.imul(D,mt),e=e+Math.imul(O,dt)|0,n=n+Math.imul(O,ct)|0,n=n+Math.imul(Y,dt)|0,o=o+Math.imul(Y,ct)|0;var It=(a+e|0)+((8191&n)<<13)|0;a=(o+(n>>>13)|0)+(It>>>26)|0,It&=67108863,e=Math.imul(C,dt),n=Math.imul(C,ct),n=n+Math.imul(D,dt)|0,o=Math.imul(D,ct);var Lt=(a+e|0)+((8191&n)<<13)|0;return a=(o+(n>>>13)|0)+(Lt>>>26)|0,Lt&=67108863,u[0]=gt,u[1]=vt,u[2]=yt,u[3]=wt,u[4]=Mt,u[5]=bt,u[6]=_t,u[7]=At,u[8]=Et,u[9]=Rt,u[10]=Bt,u[11]=St,u[12]=Pt,u[13]=Tt,u[14]=xt,u[15]=Ut,u[16]=kt,u[17]=It,u[18]=Lt,0!==a&&(u[19]=a,i.length++),i};Math.imul||(R=a),o.prototype.mulTo=function(t,r){var i,e=this.length+t.length;return i=10===this.length&&10===t.length?R(this,t,r):63>e?a(this,t,r):1024>e?f(this,t,r):l(this,t,r)},m.prototype.makeRBT=function(t){for(var r=Array(t),i=o.prototype._countBits(t)-1,e=0;t>e;e++)r[e]=this.revBin(e,i,t);return r},m.prototype.revBin=function(t,r,i){if(0===t||t===i-1)return t;for(var e=0,n=0;r>n;n++)e|=(1&t)<<r-n-1,t>>=1;return e},m.prototype.permute=function(t,r,i,e,n,o){for(var h=0;o>h;h++)e[h]=r[t[h]],n[h]=i[t[h]]},m.prototype.transform=function(t,r,i,e,n,o){this.permute(o,t,r,i,e,n);for(var h=1;n>h;h<<=1)for(var s=h<<1,u=Math.cos(2*Math.PI/s),a=Math.sin(2*Math.PI/s),f=0;n>f;f+=s)for(var l=u,m=a,p=0;h>p;p++){var d=i[f+p],c=e[f+p],g=i[f+p+h],v=e[f+p+h],y=l*g-m*v;v=l*v+m*g,g=y,i[f+p]=d+g,e[f+p]=c+v,i[f+p+h]=d-g,e[f+p+h]=c-v,p!==s&&(y=u*l-a*m,m=u*m+a*l,l=y)}},m.prototype.guessLen13b=function(t,r){var i=1|Math.max(r,t),e=1&i,n=0;for(i=i/2|0;i;i>>>=1)n++;return 1<<n+1+e},m.prototype.conjugate=function(t,r,i){if(i>1)for(var e=0;i/2>e;e++){var n=t[e];t[e]=t[i-e-1],t[i-e-1]=n,n=r[e],r[e]=-r[i-e-1],r[i-e-1]=-n}},m.prototype.normalize13b=function(t,r){for(var i=0,e=0;r/2>e;e++){var n=8192*Math.round(t[2*e+1]/r)+Math.round(t[2*e]/r)+i;t[e]=67108863&n,i=67108864>n?0:n/67108864|0}return t},m.prototype.convert13b=function(t,r,i,n){for(var o=0,h=0;r>h;h++)o+=0|t[h],i[2*h]=8191&o,o>>>=13,i[2*h+1]=8191&o,o>>>=13;for(h=2*r;n>h;++h)i[h]=0;e(0===o),e(0===(o&-8192))},m.prototype.stub=function(t){for(var r=Array(t),i=0;t>i;i++)r[i]=0;return r},m.prototype.mulp=function(t,r,i){var e=2*this.guessLen13b(t.length,r.length),n=this.makeRBT(e),o=this.stub(e),h=Array(e),s=Array(e),u=Array(e),a=Array(e),f=Array(e),l=Array(e),m=i.words;m.length=e,this.convert13b(t.words,t.length,h,e),this.convert13b(r.words,r.length,a,e),this.transform(h,o,s,u,e,n),this.transform(a,o,f,l,e,n);for(var p=0;e>p;p++){var d=s[p]*f[p]-u[p]*l[p];u[p]=s[p]*l[p]+u[p]*f[p],s[p]=d}return this.conjugate(s,u,e),this.transform(s,u,m,o,e,n),this.conjugate(m,o,e),this.normalize13b(m,e),i.negative=t.negative^r.negative,i.length=t.length+r.length,i.strip()},o.prototype.mul=function(t){var r=new o(null);return r.words=Array(this.length+t.length),this.mulTo(t,r)},o.prototype.mulf=function(t){var r=new o(null);return r.words=Array(this.length+t.length),l(this,t,r)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){e("number"==typeof t),e(67108864>t);for(var r=0,i=0;this.length>i;i++){var n=(0|this.words[i])*t,o=(67108863&n)+(67108863&r);r>>=26,r+=n/67108864|0,r+=o>>>26,this.words[i]=67108863&o}return 0!==r&&(this.words[i]=r,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var r=u(t);if(0===r.length)return new o(1);for(var i=this,e=0;r.length>e&&0===r[e];e++,i=i.sqr());if(++e<r.length)for(var n=i.sqr();r.length>e;e++,n=n.sqr())0!==r[e]&&(i=i.mul(n));return i},o.prototype.iushln=function(t){e("number"==typeof t&&t>=0);var r,i=t%26,n=(t-i)/26,o=67108863>>>26-i<<26-i;if(0!==i){var h=0;for(r=0;this.length>r;r++){var s=this.words[r]&o,u=(0|this.words[r])-s<<i;this.words[r]=u|h,h=s>>>26-i}h&&(this.words[r]=h,this.length++)}if(0!==n){for(r=this.length-1;r>=0;r--)this.words[r+n]=this.words[r];for(r=0;n>r;r++)this.words[r]=0;this.length+=n}return this.strip()},o.prototype.ishln=function(t){return e(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,r,i){e("number"==typeof t&&t>=0);var n;n=r?(r-r%26)/26:0;var o=t%26,h=Math.min((t-o)/26,this.length),s=67108863^67108863>>>o<<o,u=i;if(n-=h,n=Math.max(0,n),u){for(var a=0;h>a;a++)u.words[a]=this.words[a];u.length=h}if(0===h);else if(this.length>h)for(this.length-=h,a=0;this.length>a;a++)this.words[a]=this.words[a+h];else this.words[0]=0,this.length=1;var f=0;for(a=this.length-1;!(0>a||0===f&&n>a);a--){var l=0|this.words[a];this.words[a]=f<<26-o|l>>>o,f=l&s}return u&&0!==f&&(u.words[u.length++]=f),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,r,i){return e(0===this.negative),this.iushrn(t,r,i)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){e("number"==typeof t&&t>=0);var r=t%26,i=(t-r)/26,n=1<<r;if(i>=this.length)return!1;var o=this.words[i];return!!(o&n)},o.prototype.imaskn=function(t){e("number"==typeof t&&t>=0);var r=t%26,i=(t-r)/26;if(e(0===this.negative,"imaskn works only with positive numbers"),i>=this.length)return this;if(0!==r&&i++,this.length=Math.min(i,this.length),0!==r){var n=67108863^67108863>>>r<<r;this.words[this.length-1]&=n}return this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return e("number"==typeof t),e(67108864>t),0>t?this.isubn(-t):0!==this.negative?1===this.length&&t>(0|this.words[0])?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var r=0;this.length>r&&this.words[r]>=67108864;r++)this.words[r]-=67108864,r===this.length-1?this.words[r+1]=1:this.words[r+1]++;return this.length=Math.max(this.length,r+1),this},o.prototype.isubn=function(t){if(e("number"==typeof t),e(67108864>t),0>t)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&0>this.words[0])this.words[0]=-this.words[0],this.negative=1;else for(var r=0;this.length>r&&0>this.words[r];r++)this.words[r]+=67108864,this.words[r+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,r,i){var n,o=t.length+i;this._expand(o);var h,s=0;for(n=0;t.length>n;n++){h=(0|this.words[n+i])+s;var u=(0|t.words[n])*r;h-=67108863&u,s=(h>>26)-(u/67108864|0),this.words[n+i]=67108863&h}for(;this.length-i>n;n++)h=(0|this.words[n+i])+s,s=h>>26,this.words[n+i]=67108863&h;if(0===s)return this.strip();for(e(s===-1),s=0,n=0;this.length>n;n++)h=-(0|this.words[n])+s,s=h>>26,this.words[n]=67108863&h;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,r){var i=this.length-t.length,e=this.clone(),n=t,h=0|n.words[n.length-1],s=this._countBits(h);i=26-s,0!==i&&(n=n.ushln(i),e.iushln(i),h=0|n.words[n.length-1]);var u,a=e.length-n.length;if("mod"!==r){u=new o(null),u.length=a+1,u.words=Array(u.length);for(var f=0;u.length>f;f++)u.words[f]=0}var l=e.clone()._ishlnsubmul(n,1,a);0===l.negative&&(e=l,u&&(u.words[a]=1));for(var m=a-1;m>=0;m--){var p=67108864*(0|e.words[n.length+m])+(0|e.words[n.length+m-1]);for(p=Math.min(p/h|0,67108863),e._ishlnsubmul(n,p,m);0!==e.negative;)p--,e.negative=0,e._ishlnsubmul(n,1,m),e.isZero()||(e.negative^=1);u&&(u.words[m]=p)}return u&&u.strip(),e.strip(),"div"!==r&&0!==i&&e.iushrn(i),{div:u||null,mod:e}},o.prototype.divmod=function(t,r,i){if(e(!t.isZero()),this.isZero())return{div:new o(0),mod:new o(0)};var n,h,s;return 0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,r),"mod"!==r&&(n=s.div.neg()),"div"!==r&&(h=s.mod.neg(),i&&0!==h.negative&&h.iadd(t)),{div:n,mod:h}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),r),"mod"!==r&&(n=s.div.neg()),{div:n,mod:s.mod}):0!==(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),r),"div"!==r&&(h=s.mod.neg(),i&&0!==h.negative&&h.isub(t)),{div:s.div,mod:h}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===r?{div:this.divn(t.words[0]),mod:null
}:"mod"===r?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,r)},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var r=this.divmod(t);if(r.mod.isZero())return r.div;var i=0!==r.div.negative?r.mod.isub(t):r.mod,e=t.ushrn(1),n=t.andln(1),o=i.cmp(e);return 0>o||1===n&&0===o?r.div:0!==r.div.negative?r.div.isubn(1):r.div.iaddn(1)},o.prototype.modn=function(t){e(67108863>=t);for(var r=(1<<26)%t,i=0,n=this.length-1;n>=0;n--)i=(r*i+(0|this.words[n]))%t;return i},o.prototype.idivn=function(t){e(67108863>=t);for(var r=0,i=this.length-1;i>=0;i--){var n=(0|this.words[i])+67108864*r;this.words[i]=n/t|0,r=n%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){e(0===t.negative),e(!t.isZero());var r=this,i=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var n=new o(1),h=new o(0),s=new o(0),u=new o(1),a=0;r.isEven()&&i.isEven();)r.iushrn(1),i.iushrn(1),++a;for(var f=i.clone(),l=r.clone();!r.isZero();){for(var m=0,p=1;0===(r.words[0]&p)&&26>m;++m,p<<=1);if(m>0)for(r.iushrn(m);m-- >0;)(n.isOdd()||h.isOdd())&&(n.iadd(f),h.isub(l)),n.iushrn(1),h.iushrn(1);for(var d=0,c=1;0===(i.words[0]&c)&&26>d;++d,c<<=1);if(d>0)for(i.iushrn(d);d-- >0;)(s.isOdd()||u.isOdd())&&(s.iadd(f),u.isub(l)),s.iushrn(1),u.iushrn(1);r.cmp(i)<0?(i.isub(r),s.isub(n),u.isub(h)):(r.isub(i),n.isub(s),h.isub(u))}return{a:s,b:u,gcd:i.iushln(a)}},o.prototype._invmp=function(t){e(0===t.negative),e(!t.isZero());var r=this,i=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var n=new o(1),h=new o(0),s=i.clone();r.cmpn(1)>0&&i.cmpn(1)>0;){for(var u=0,a=1;0===(r.words[0]&a)&&26>u;++u,a<<=1);if(u>0)for(r.iushrn(u);u-- >0;)n.isOdd()&&n.iadd(s),n.iushrn(1);for(var f=0,l=1;0===(i.words[0]&l)&&26>f;++f,l<<=1);if(f>0)for(i.iushrn(f);f-- >0;)h.isOdd()&&h.iadd(s),h.iushrn(1);r.cmp(i)<0?(i.isub(r),h.isub(n)):(r.isub(i),n.isub(h))}var m;return m=0===r.cmpn(1)?n:h,m.cmpn(0)<0&&m.iadd(t),m},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var r=this.clone(),i=t.clone();r.negative=0,i.negative=0;for(var e=0;r.isEven()&&i.isEven();e++)r.iushrn(1),i.iushrn(1);for(;;){for(;r.isEven();)r.iushrn(1);for(;i.isEven();)i.iushrn(1);var n=r.cmp(i);if(0>n){var o=r;r=i,i=o}else if(0===n||0===i.cmpn(1))break;r.isub(i)}return i.iushln(e)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0===(1&this.words[0])},o.prototype.isOdd=function(){return 1===(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){e("number"==typeof t);var r=t%26,i=(t-r)/26,n=1<<r;if(i>=this.length)return this._expand(i+1),this.words[i]|=n,this;for(var o=n,h=i;0!==o&&this.length>h;h++){var s=0|this.words[h];s+=o,o=s>>>26,s&=67108863,this.words[h]=s}return 0!==o&&(this.words[h]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var r=0>t;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;this.strip();var i;if(this.length>1)i=1;else{r&&(t=-t),e(67108863>=t,"Number is too big");var n=0|this.words[0];i=n===t?0:t>n?-1:1}return 0!==this.negative?0|-i:i},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var r=this.ucmp(t);return 0!==this.negative?0|-r:r},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(t.length>this.length)return-1;for(var r=0,i=this.length-1;i>=0;i--){var e=0|this.words[i],n=0|t.words[i];if(e!==n){n>e?r=-1:e>n&&(r=1);break}}return r},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return this.cmpn(t)===-1},o.prototype.lt=function(t){return this.cmp(t)===-1},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new y(t)},o.prototype.toRed=function(t){return e(!this.red,"Already a number in reduction context"),e(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return e(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return e(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return e(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return e(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return e(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return e(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return e(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return e(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return e(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return e(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return e(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return e(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return e(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return e(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return e(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var B={k256:null,p224:null,p192:null,p25519:null};p.prototype._tmp=function(){var t=new o(null);return t.words=Array(Math.ceil(this.n/13)),t},p.prototype.ireduce=function(t){var r,i=t;do this.split(i,this.tmp),i=this.imulK(i),i=i.iadd(this.tmp),r=i.bitLength();while(r>this.n);var e=this.n>r?-1:i.ucmp(this.p);return 0===e?(i.words[0]=0,i.length=1):e>0?i.isub(this.p):i.strip(),i},p.prototype.split=function(t,r){t.iushrn(this.n,0,r)},p.prototype.imulK=function(t){return t.imul(this.k)},n(d,p),d.prototype.split=function(t,r){for(var i=4194303,e=Math.min(t.length,9),n=0;e>n;n++)r.words[n]=t.words[n];if(r.length=e,9>=t.length)return t.words[0]=0,void(t.length=1);var o=t.words[9];for(r.words[r.length++]=o&i,n=10;t.length>n;n++){var h=0|t.words[n];t.words[n-10]=(h&i)<<4|o>>>22,o=h}o>>>=22,t.words[n-10]=o,t.length-=0===o&&t.length>10?10:9},d.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var r=0,i=0;t.length>i;i++){var e=0|t.words[i];r+=977*e,t.words[i]=67108863&r,r=64*e+(r/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},n(c,p),n(g,p),n(v,p),v.prototype.imulK=function(t){for(var r=0,i=0;t.length>i;i++){var e=19*(0|t.words[i])+r,n=67108863&e;e>>>=26,t.words[i]=n,r=e}return 0!==r&&(t.words[t.length++]=r),t},o._prime=function S(t){if(B[t])return B[t];var S;if("k256"===t)S=new d;else if("p224"===t)S=new c;else if("p192"===t)S=new g;else{if("p25519"!==t)throw Error("Unknown prime "+t);S=new v}return B[t]=S,S},y.prototype._verify1=function(t){e(0===t.negative,"red works only with positives"),e(t.red,"red works only with red numbers")},y.prototype._verify2=function(t,r){e(0===(t.negative|r.negative),"red works only with positives"),e(t.red&&t.red===r.red,"red works only with red numbers")},y.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},y.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},y.prototype.add=function(t,r){this._verify2(t,r);var i=t.add(r);return i.cmp(this.m)<0||i.isub(this.m),i._forceRed(this)},y.prototype.iadd=function(t,r){this._verify2(t,r);var i=t.iadd(r);return i.cmp(this.m)<0||i.isub(this.m),i},y.prototype.sub=function(t,r){this._verify2(t,r);var i=t.sub(r);return i.cmpn(0)<0&&i.iadd(this.m),i._forceRed(this)},y.prototype.isub=function(t,r){this._verify2(t,r);var i=t.isub(r);return i.cmpn(0)<0&&i.iadd(this.m),i},y.prototype.shl=function(t,r){return this._verify1(t),this.imod(t.ushln(r))},y.prototype.imul=function(t,r){return this._verify2(t,r),this.imod(t.imul(r))},y.prototype.mul=function(t,r){return this._verify2(t,r),this.imod(t.mul(r))},y.prototype.isqr=function(t){return this.imul(t,t.clone())},y.prototype.sqr=function(t){return this.mul(t,t)},y.prototype.sqrt=function(t){if(t.isZero())return t.clone();var r=this.m.andln(3);if(e(r%2===1),3===r){var i=this.m.add(new o(1)).iushrn(2);return this.pow(t,i)}for(var n=this.m.subn(1),h=0;!n.isZero()&&0===n.andln(1);)h++,n.iushrn(1);e(!n.isZero());var s=new o(1).toRed(this),u=s.redNeg(),a=this.m.subn(1).iushrn(1),f=this.m.bitLength();for(f=new o(2*f*f).toRed(this);0!==this.pow(f,a).cmp(u);)f.redIAdd(u);for(var l=this.pow(f,n),m=this.pow(t,n.addn(1).iushrn(1)),p=this.pow(t,n),d=h;0!==p.cmp(s);){for(var c=p,g=0;0!==c.cmp(s);g++)c=c.redSqr();e(d>g);var v=this.pow(l,new o(1).iushln(d-g-1));m=m.redMul(v),l=v.redSqr(),p=p.redMul(l),d=g}return m},y.prototype.invm=function(t){var r=t._invmp(this.m);return 0!==r.negative?(r.negative=0,this.imod(r).redNeg()):this.imod(r)},y.prototype.pow=function(t,r){if(r.isZero())return new o(1);if(0===r.cmpn(1))return t.clone();var i=4,e=Array(1<<i);e[0]=new o(1).toRed(this),e[1]=t;for(var n=2;e.length>n;n++)e[n]=this.mul(e[n-1],t);var h=e[0],s=0,u=0,a=r.bitLength()%26;for(0===a&&(a=26),n=r.length-1;n>=0;n--){for(var f=r.words[n],l=a-1;l>=0;l--){var m=f>>l&1;h!==e[0]&&(h=this.sqr(h)),0!==m||0!==s?(s<<=1,s|=m,u++,(u===i||0===n&&0===l)&&(h=this.mul(h,e[s]),u=0,s=0)):u=0}a=26}return h},y.prototype.convertTo=function(t){var r=t.umod(this.m);return r===t?r.clone():r},y.prototype.convertFrom=function(t){var r=t.clone();return r.red=null,r},o.mont=function(t){return new w(t)},n(w,y),w.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},w.prototype.convertFrom=function(t){var r=this.imod(t.mul(this.rinv));return r.red=null,r},w.prototype.imul=function(t,r){if(t.isZero()||r.isZero())return t.words[0]=0,t.length=1,t;var i=t.imul(r),e=i.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=i.isub(e).iushrn(this.shift),o=n;return n.cmp(this.m)<0?n.cmpn(0)<0&&(o=n.iadd(this.m)):o=n.isub(this.m),o._forceRed(this)},w.prototype.mul=function(t,r){if(t.isZero()||r.isZero())return new o(0)._forceRed(this);var i=t.mul(r),e=i.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=i.isub(e).iushrn(this.shift),h=n;return n.cmp(this.m)<0?n.cmpn(0)<0&&(h=n.iadd(this.m)):h=n.isub(this.m),h._forceRed(this)},w.prototype.invm=function(t){var r=this.imod(t._invmp(this.m).mul(this.r2));return r._forceRed(this)}}(void 0===t||t,this)}).call(r,i(10)(t))},function(t,r,i){"use strict";(function(t,e){function n(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(r){return!1}}function o(){return t.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function h(r,i){if(o()<i)throw new RangeError("Invalid typed array length");return t.TYPED_ARRAY_SUPPORT?(r=new Uint8Array(i),r.__proto__=t.prototype):(null===r&&(r=new t(i)),r.length=i),r}function t(r,i,e){if(!(t.TYPED_ARRAY_SUPPORT||this instanceof t))return new t(r,i,e);if("number"==typeof r){if("string"==typeof i)throw Error("If encoding is specified then the first argument must be a string");return f(this,r)}return s(this,r,i,e)}function s(t,r,i,e){if("number"==typeof r)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&r instanceof ArrayBuffer?p(t,r,i,e):"string"==typeof r?l(t,r,i):d(t,r)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(0>t)throw new RangeError('"size" argument must not be negative')}function a(t,r,i,e){return u(r),r>0&&void 0!==i?"string"==typeof e?h(t,r).fill(i,e):h(t,r).fill(i):h(t,r)}function f(r,i){if(u(i),r=h(r,0>i?0:0|c(i)),!t.TYPED_ARRAY_SUPPORT)for(var e=0;i>e;++e)r[e]=0;return r}function l(r,i,e){if("string"==typeof e&&""!==e||(e="utf8"),!t.isEncoding(e))throw new TypeError('"encoding" must be a valid string encoding');var n=0|v(i,e);r=h(r,n);var o=r.write(i,e);return o!==n&&(r=r.slice(0,o)),r}function m(t,r){var i=0>r.length?0:0|c(r.length);t=h(t,i);for(var e=0;i>e;e+=1)t[e]=255&r[e];return t}function p(r,i,e,n){if(0>e||e>i.byteLength)throw new RangeError("'offset' is out of bounds");if(e+(n||0)>i.byteLength)throw new RangeError("'length' is out of bounds");return i=void 0===e&&void 0===n?new Uint8Array(i):void 0===n?new Uint8Array(i,e):new Uint8Array(i,e,n),t.TYPED_ARRAY_SUPPORT?(r=i,r.__proto__=t.prototype):r=m(r,i),r}function d(r,i){if(t.isBuffer(i)){var e=0|c(i.length);return r=h(r,e),0===r.length?r:(i.copy(r,0,0,e),r)}if(i){if("undefined"!=typeof ArrayBuffer&&i.buffer instanceof ArrayBuffer||"length"in i)return"number"!=typeof i.length||W(i.length)?h(r,0):m(r,i);if("Buffer"===i.type&&Q(i.data))return m(r,i.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function c(t){if(t>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|t}function g(r){return+r!=r&&(r=0),t.alloc(+r)}function v(r,i){if(t.isBuffer(r))return r.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(r)||r instanceof ArrayBuffer))return r.byteLength;"string"!=typeof r&&(r=""+r);var e=r.length;if(0===e)return 0;for(var n=!1;;)switch(i){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":case void 0:return $(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return V(r).length;default:if(n)return $(r).length;i=(""+i).toLowerCase(),n=!0}}function y(t,r,i){var e=!1;if((void 0===r||0>r)&&(r=0),r>this.length)return"";if((void 0===i||i>this.length)&&(i=this.length),0>=i)return"";if(i>>>=0,r>>>=0,r>=i)return"";for(t||(t="utf8");;)switch(t){case"hex":return I(this,r,i);case"utf8":case"utf-8":return T(this,r,i);case"ascii":return U(this,r,i);case"latin1":case"binary":return k(this,r,i);case"base64":return P(this,r,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return L(this,r,i);default:if(e)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),e=!0}}function w(t,r,i){var e=t[r];t[r]=t[i],t[i]=e}function M(r,i,e,n,o){if(0===r.length)return-1;if("string"==typeof e?(n=e,e=0):e>2147483647?e=2147483647:-2147483648>e&&(e=-2147483648),e=+e,isNaN(e)&&(e=o?0:r.length-1),0>e&&(e=r.length+e),r.length>e){if(0>e){if(!o)return-1;e=0}}else{if(o)return-1;e=r.length-1}if("string"==typeof i&&(i=t.from(i,n)),t.isBuffer(i))return 0===i.length?-1:b(r,i,e,n,o);if("number"==typeof i)return i=255&i,t.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(r,i,e):Uint8Array.prototype.lastIndexOf.call(r,i,e):b(r,[i],e,n,o);throw new TypeError("val must be string, number or Buffer")}function b(t,r,i,e,n){function o(t,r){return 1===h?t[r]:t.readUInt16BE(r*h)}var h=1,s=t.length,u=r.length;if(void 0!==e&&(e=(e+"").toLowerCase(),"ucs2"===e||"ucs-2"===e||"utf16le"===e||"utf-16le"===e)){if(2>t.length||2>r.length)return-1;h=2,s/=2,u/=2,i/=2}var a;if(n){var f=-1;for(a=i;s>a;a++)if(o(t,a)===o(r,f===-1?0:a-f)){if(f===-1&&(f=a),a-f+1===u)return f*h}else f!==-1&&(a-=a-f),f=-1}else for(i+u>s&&(i=s-u),a=i;a>=0;a--){for(var l=!0,m=0;u>m;m++)if(o(t,a+m)!==o(r,m)){l=!1;break}if(l)return a}return-1}function _(t,r,i,e){i=+i||0;var n=t.length-i;e?(e=+e,e>n&&(e=n)):e=n;var o=r.length;if(o%2!==0)throw new TypeError("Invalid hex string");e>o/2&&(e=o/2);for(var h=0;e>h;++h){var s=parseInt(r.substr(2*h,2),16);if(isNaN(s))return h;t[i+h]=s}return h}function A(t,r,i,e){return H($(r,t.length-i),t,i,e)}function E(t,r,i,e){return H(K(r),t,i,e)}function R(t,r,i,e){return E(t,r,i,e)}function B(t,r,i,e){return H(V(r),t,i,e)}function S(t,r,i,e){return H(J(r,t.length-i),t,i,e)}function P(t,r,i){return X.fromByteArray(0===r&&i===t.length?t:t.slice(r,i))}function T(t,r,i){i=Math.min(t.length,i);for(var e=[],n=r;i>n;){var o=t[n],h=null,s=o>239?4:o>223?3:o>191?2:1;if(i>=n+s){var u,a,f,l;switch(s){case 1:128>o&&(h=o);break;case 2:u=t[n+1],128===(192&u)&&(l=(31&o)<<6|63&u,l>127&&(h=l));break;case 3:u=t[n+1],a=t[n+2],128===(192&u)&&128===(192&a)&&(l=(15&o)<<12|(63&u)<<6|63&a,l>2047&&(55296>l||l>57343)&&(h=l));break;case 4:u=t[n+1],a=t[n+2],f=t[n+3],128===(192&u)&&128===(192&a)&&128===(192&f)&&(l=(15&o)<<18|(63&u)<<12|(63&a)<<6|63&f,l>65535&&1114112>l&&(h=l))}}null===h?(h=65533,s=1):h>65535&&(h-=65536,e.push(h>>>10&1023|55296),h=56320|1023&h),e.push(h),n+=s}return x(e)}function x(t){var r=t.length;if(tt>=r)return String.fromCharCode.apply(String,t);for(var i="",e=0;r>e;)i+=String.fromCharCode.apply(String,t.slice(e,e+=tt));return i}function U(t,r,i){var e="";i=Math.min(t.length,i);for(var n=r;i>n;++n)e+=String.fromCharCode(127&t[n]);return e}function k(t,r,i){var e="";i=Math.min(t.length,i);for(var n=r;i>n;++n)e+=String.fromCharCode(t[n]);return e}function I(t,r,i){var e=t.length;r&&r>=0||(r=0),(!i||0>i||i>e)&&(i=e);for(var n="",o=r;i>o;++o)n+=F(t[o]);return n}function L(t,r,i){for(var e=t.slice(r,i),n="",o=0;e.length>o;o+=2)n+=String.fromCharCode(e[o]+256*e[o+1]);return n}function O(t,r,i){if(t%1!==0||0>t)throw new RangeError("offset is not uint");if(t+r>i)throw new RangeError("Trying to access beyond buffer length")}function Y(r,i,e,n,o,h){if(!t.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(i>o||h>i)throw new RangeError('"value" argument is out of bounds');if(e+n>r.length)throw new RangeError("Index out of range")}function N(t,r,i,e){0>r&&(r=65535+r+1);for(var n=0,o=Math.min(t.length-i,2);o>n;++n)t[i+n]=(r&255<<8*(e?n:1-n))>>>8*(e?n:1-n)}function C(t,r,i,e){0>r&&(r=4294967295+r+1);for(var n=0,o=Math.min(t.length-i,4);o>n;++n)t[i+n]=r>>>8*(e?n:3-n)&255}function D(t,r,i,e,n,o){if(i+e>t.length)throw new RangeError("Index out of range");if(0>i)throw new RangeError("Index out of range")}function j(t,r,i,e,n){return n||D(t,r,i,4,3.4028234663852886e38,-3.4028234663852886e38),G.write(t,r,i,e,23,4),i+4}function Z(t,r,i,e,n){return n||D(t,r,i,8,1.7976931348623157e308,-1.7976931348623157e308),G.write(t,r,i,e,52,8),i+8}function q(t){if(t=z(t).replace(rt,""),2>t.length)return"";for(;t.length%4!==0;)t+="=";return t}function z(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function F(t){return 16>t?"0"+t.toString(16):t.toString(16)}function $(t,r){r=r||1/0;for(var i,e=t.length,n=null,o=[],h=0;e>h;++h){if(i=t.charCodeAt(h),i>55295&&57344>i){if(!n){if(i>56319){(r-=3)>-1&&o.push(239,191,189);continue}if(h+1===e){(r-=3)>-1&&o.push(239,191,189);continue}n=i;continue}if(56320>i){(r-=3)>-1&&o.push(239,191,189),n=i;continue}i=(n-55296<<10|i-56320)+65536}else n&&(r-=3)>-1&&o.push(239,191,189);if(n=null,128>i){if((r-=1)<0)break;o.push(i)}else if(2048>i){if((r-=2)<0)break;o.push(i>>6|192,63&i|128)}else if(65536>i){if((r-=3)<0)break;o.push(i>>12|224,i>>6&63|128,63&i|128)}else{if(i>=1114112)throw Error("Invalid code point");if((r-=4)<0)break;o.push(i>>18|240,i>>12&63|128,i>>6&63|128,63&i|128)}}return o}function K(t){for(var r=[],i=0;t.length>i;++i)r.push(255&t.charCodeAt(i));return r}function J(t,r){for(var i,e,n,o=[],h=0;t.length>h&&(r-=2)>=0;++h)i=t.charCodeAt(h),e=i>>8,n=i%256,o.push(n),o.push(e);return o}function V(t){return X.toByteArray(q(t))}function H(t,r,i,e){for(var n=0;e>n&&(n+i<r.length&&n<t.length);++n)r[n+i]=t[n];return n}function W(t){return t!==t}var X=i(4),G=i(5),Q=i(7);r.Buffer=t,r.SlowBuffer=g,r.INSPECT_MAX_BYTES=50,t.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:n(),r.kMaxLength=o(),t.poolSize=8192,t._augment=function(r){return r.__proto__=t.prototype,r},t.from=function(t,r,i){return s(null,t,r,i)},t.TYPED_ARRAY_SUPPORT&&(t.prototype.__proto__=Uint8Array.prototype,t.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&t[Symbol.species]===t&&Object.defineProperty(t,Symbol.species,{value:null,configurable:!0})),t.alloc=function(t,r,i){return a(null,t,r,i)},t.allocUnsafe=function(t){return f(null,t)},t.allocUnsafeSlow=function(t){return f(null,t)},t.isBuffer=function(t){return!(null==t||!t._isBuffer)},t.compare=function(r,i){if(!t.isBuffer(r)||!t.isBuffer(i))throw new TypeError("Arguments must be Buffers");if(r===i)return 0;for(var e=r.length,n=i.length,o=0,h=Math.min(e,n);h>o;++o)if(r[o]!==i[o]){e=r[o],n=i[o];break}return n>e?-1:e>n?1:0},t.isEncoding=function(t){switch((t+"").toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},t.concat=function(r,i){if(!Q(r))throw new TypeError('"list" argument must be an Array of Buffers');if(0===r.length)return t.alloc(0);var e;if(void 0===i)for(i=0,e=0;r.length>e;++e)i+=r[e].length;var n=t.allocUnsafe(i),o=0;for(e=0;r.length>e;++e){var h=r[e];if(!t.isBuffer(h))throw new TypeError('"list" argument must be an Array of Buffers');h.copy(n,o),o+=h.length}return n},t.byteLength=v,t.prototype._isBuffer=!0,t.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;t>r;r+=2)w(this,r,r+1);return this},t.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;t>r;r+=4)w(this,r,r+3),w(this,r+1,r+2);return this},t.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;t>r;r+=8)w(this,r,r+7),w(this,r+1,r+6),w(this,r+2,r+5),w(this,r+3,r+4);return this},t.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?T(this,0,t):y.apply(this,arguments)},t.prototype.equals=function(r){if(!t.isBuffer(r))throw new TypeError("Argument must be a Buffer");return this===r||0===t.compare(this,r)},t.prototype.inspect=function(){var t="",i=r.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,i).match(/.{2}/g).join(" "),this.length>i&&(t+=" ... ")),"<Buffer "+t+">"},t.prototype.compare=function(r,i,e,n,o){if(!t.isBuffer(r))throw new TypeError("Argument must be a Buffer");if(void 0===i&&(i=0),void 0===e&&(e=r?r.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),0>i||e>r.length||0>n||o>this.length)throw new RangeError("out of range index");if(n>=o&&i>=e)return 0;if(n>=o)return-1;if(i>=e)return 1;if(i>>>=0,e>>>=0,n>>>=0,o>>>=0,this===r)return 0;for(var h=o-n,s=e-i,u=Math.min(h,s),a=this.slice(n,o),f=r.slice(i,e),l=0;u>l;++l)if(a[l]!==f[l]){h=a[l],s=f[l];break}return s>h?-1:h>s?1:0},t.prototype.includes=function(t,r,i){return this.indexOf(t,r,i)!==-1},t.prototype.indexOf=function(t,r,i){return M(this,t,r,i,!0)},t.prototype.lastIndexOf=function(t,r,i){return M(this,t,r,i,!1)},t.prototype.write=function(t,r,i,e){if(void 0===r)e="utf8",i=this.length,r=0;else if(void 0===i&&"string"==typeof r)e=r,i=this.length,r=0;else{if(!isFinite(r))throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r=0|r,isFinite(i)?(i=0|i,void 0===e&&(e="utf8")):(e=i,i=void 0)}var n=this.length-r;if((void 0===i||i>n)&&(i=n),t.length>0&&(0>i||0>r)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");e||(e="utf8");for(var o=!1;;)switch(e){case"hex":return _(this,t,r,i);case"utf8":case"utf-8":return A(this,t,r,i);case"ascii":return E(this,t,r,i);case"latin1":case"binary":return R(this,t,r,i);case"base64":return B(this,t,r,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,r,i);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(""+e).toLowerCase(),o=!0}},t.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var tt=4096;t.prototype.slice=function(r,i){var e=this.length;r=~~r,i=void 0===i?e:~~i,0>r?(r+=e,0>r&&(r=0)):r>e&&(r=e),0>i?(i+=e,0>i&&(i=0)):i>e&&(i=e),r>i&&(i=r);var n;if(t.TYPED_ARRAY_SUPPORT)n=this.subarray(r,i),n.__proto__=t.prototype;else{var o=i-r;n=new t(o,(void 0));for(var h=0;o>h;++h)n[h]=this[h+r]}return n},t.prototype.readUIntLE=function(t,r,i){t=0|t,r=0|r,i||O(t,r,this.length);for(var e=this[t],n=1,o=0;++o<r&&(n*=256);)e+=this[t+o]*n;return e},t.prototype.readUIntBE=function(t,r,i){t=0|t,r=0|r,i||O(t,r,this.length);for(var e=this[t+--r],n=1;r>0&&(n*=256);)e+=this[t+--r]*n;return e},t.prototype.readUInt8=function(t,r){return r||O(t,1,this.length),this[t]},t.prototype.readUInt16LE=function(t,r){return r||O(t,2,this.length),this[t]|this[t+1]<<8},t.prototype.readUInt16BE=function(t,r){return r||O(t,2,this.length),this[t]<<8|this[t+1]},t.prototype.readUInt32LE=function(t,r){return r||O(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},t.prototype.readUInt32BE=function(t,r){return r||O(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},t.prototype.readIntLE=function(t,r,i){t=0|t,r=0|r,i||O(t,r,this.length);for(var e=this[t],n=1,o=0;++o<r&&(n*=256);)e+=this[t+o]*n;return n*=128,n>e||(e-=Math.pow(2,8*r)),e},t.prototype.readIntBE=function(t,r,i){t=0|t,r=0|r,i||O(t,r,this.length);for(var e=r,n=1,o=this[t+--e];e>0&&(n*=256);)o+=this[t+--e]*n;return n*=128,n>o||(o-=Math.pow(2,8*r)),o},t.prototype.readInt8=function(t,r){return r||O(t,1,this.length),128&this[t]?(255-this[t]+1)*-1:this[t]},t.prototype.readInt16LE=function(t,r){r||O(t,2,this.length);var i=this[t]|this[t+1]<<8;return 32768&i?4294901760|i:i},t.prototype.readInt16BE=function(t,r){r||O(t,2,this.length);var i=this[t+1]|this[t]<<8;return 32768&i?4294901760|i:i},t.prototype.readInt32LE=function(t,r){return r||O(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},t.prototype.readInt32BE=function(t,r){return r||O(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},t.prototype.readFloatLE=function(t,r){return r||O(t,4,this.length),G.read(this,t,!0,23,4)},t.prototype.readFloatBE=function(t,r){return r||O(t,4,this.length),G.read(this,t,!1,23,4)},t.prototype.readDoubleLE=function(t,r){return r||O(t,8,this.length),G.read(this,t,!0,52,8)},t.prototype.readDoubleBE=function(t,r){return r||O(t,8,this.length),G.read(this,t,!1,52,8)},t.prototype.writeUIntLE=function(t,r,i,e){if(t=+t,r=0|r,i=0|i,!e){var n=Math.pow(2,8*i)-1;Y(this,t,r,i,n,0)}var o=1,h=0;for(this[r]=255&t;++h<i&&(o*=256);)this[r+h]=t/o&255;return r+i},t.prototype.writeUIntBE=function(t,r,i,e){if(t=+t,r=0|r,i=0|i,!e){var n=Math.pow(2,8*i)-1;Y(this,t,r,i,n,0)}var o=i-1,h=1;for(this[r+o]=255&t;--o>=0&&(h*=256);)this[r+o]=t/h&255;return r+i},t.prototype.writeUInt8=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,1,255,0),t.TYPED_ARRAY_SUPPORT||(r=Math.floor(r)),this[i]=255&r,i+1},t.prototype.writeUInt16LE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,2,65535,0),t.TYPED_ARRAY_SUPPORT?(this[i]=255&r,this[i+1]=r>>>8):N(this,r,i,!0),i+2},t.prototype.writeUInt16BE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,2,65535,0),t.TYPED_ARRAY_SUPPORT?(this[i]=r>>>8,this[i+1]=255&r):N(this,r,i,!1),i+2},t.prototype.writeUInt32LE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,4,4294967295,0),t.TYPED_ARRAY_SUPPORT?(this[i+3]=r>>>24,this[i+2]=r>>>16,this[i+1]=r>>>8,this[i]=255&r):C(this,r,i,!0),i+4},t.prototype.writeUInt32BE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,4,4294967295,0),t.TYPED_ARRAY_SUPPORT?(this[i]=r>>>24,this[i+1]=r>>>16,this[i+2]=r>>>8,this[i+3]=255&r):C(this,r,i,!1),i+4},t.prototype.writeIntLE=function(t,r,i,e){if(t=+t,r=0|r,!e){var n=Math.pow(2,8*i-1);Y(this,t,r,i,n-1,-n)}var o=0,h=1,s=0;for(this[r]=255&t;++o<i&&(h*=256);)0>t&&0===s&&0!==this[r+o-1]&&(s=1),this[r+o]=(t/h>>0)-s&255;return r+i},t.prototype.writeIntBE=function(t,r,i,e){if(t=+t,r=0|r,!e){var n=Math.pow(2,8*i-1);Y(this,t,r,i,n-1,-n)}var o=i-1,h=1,s=0;for(this[r+o]=255&t;--o>=0&&(h*=256);)0>t&&0===s&&0!==this[r+o+1]&&(s=1),this[r+o]=(t/h>>0)-s&255;return r+i},t.prototype.writeInt8=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,1,127,-128),t.TYPED_ARRAY_SUPPORT||(r=Math.floor(r)),0>r&&(r=255+r+1),this[i]=255&r,i+1},t.prototype.writeInt16LE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,2,32767,-32768),t.TYPED_ARRAY_SUPPORT?(this[i]=255&r,this[i+1]=r>>>8):N(this,r,i,!0),i+2},t.prototype.writeInt16BE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,2,32767,-32768),t.TYPED_ARRAY_SUPPORT?(this[i]=r>>>8,this[i+1]=255&r):N(this,r,i,!1),i+2},t.prototype.writeInt32LE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,4,2147483647,-2147483648),t.TYPED_ARRAY_SUPPORT?(this[i]=255&r,this[i+1]=r>>>8,this[i+2]=r>>>16,this[i+3]=r>>>24):C(this,r,i,!0),i+4},t.prototype.writeInt32BE=function(r,i,e){return r=+r,i=0|i,e||Y(this,r,i,4,2147483647,-2147483648),0>r&&(r=4294967295+r+1),t.TYPED_ARRAY_SUPPORT?(this[i]=r>>>24,this[i+1]=r>>>16,this[i+2]=r>>>8,this[i+3]=255&r):C(this,r,i,!1),i+4},t.prototype.writeFloatLE=function(t,r,i){return j(this,t,r,!0,i)},t.prototype.writeFloatBE=function(t,r,i){return j(this,t,r,!1,i)},t.prototype.writeDoubleLE=function(t,r,i){return Z(this,t,r,!0,i)},t.prototype.writeDoubleBE=function(t,r,i){return Z(this,t,r,!1,i)},t.prototype.copy=function(r,i,e,n){if(e||(e=0),n||0===n||(n=this.length),r.length>i||(i=r.length),i||(i=0),n>0&&e>n&&(n=e),n===e)return 0;if(0===r.length||0===this.length)return 0;if(0>i)throw new RangeError("targetStart out of bounds");if(0>e||e>=this.length)throw new RangeError("sourceStart out of bounds");if(0>n)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),n-e>r.length-i&&(n=r.length-i+e);var o,h=n-e;if(this===r&&i>e&&n>i)for(o=h-1;o>=0;--o)r[o+i]=this[o+e];else if(1e3>h||!t.TYPED_ARRAY_SUPPORT)for(o=0;h>o;++o)r[o+i]=this[o+e];else Uint8Array.prototype.set.call(r,this.subarray(e,e+h),i);return h},t.prototype.fill=function(r,i,e,n){if("string"==typeof r){if("string"==typeof i?(n=i,i=0,e=this.length):"string"==typeof e&&(n=e,e=this.length),1===r.length){var o=r.charCodeAt(0);256>o&&(r=o)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!t.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof r&&(r=255&r);if(0>i||i>this.length||e>this.length)throw new RangeError("Out of range index");if(i>=e)return this;i>>>=0,e=void 0===e?this.length:e>>>0,r||(r=0);var h;if("number"==typeof r)for(h=i;e>h;++h)this[h]=r;else{var s=t.isBuffer(r)?r:$(""+new t(r,n)),u=s.length;for(h=0;e-i>h;++h)this[h+i]=s[h%u]}return this};var rt=/[^+\/0-9A-Za-z-_]/g}).call(r,i(1).Buffer,i(9))},function(t,r,i){var e=i(0),n=i(8);console.log(new e("87234987239872349872489724897248972348972389472498728723897234",16).toString(10)),t.exports=function(t){if("string"==typeof t||"number"==typeof t){var r=new e(1),i=(t+"").toLowerCase().trim(),o="0x"===i.substr(0,2)||"-0x"===i.substr(0,3),h=n(i);if("-"===h.substr(0,1)&&(h=n(h.slice(1)),r=new e((-1),10)),h=""===h?"0":h,!h.match(/^-?[0-9]+$/)&&h.match(/^[0-9A-Fa-f]+$/)||h.match(/^[a-fA-F]+$/)||o===!0&&h.match(/^[0-9A-Fa-f]+$/))return new e(h,16).mul(r);if((h.match(/^-?[0-9]+$/)||""===h)&&o===!1)return new e(h,10).mul(r);
}else if("object"==typeof t&&t.toString&&!t.pop&&!t.push&&t.toString(10).match(/^-?[0-9]+$/)&&(t.mul||t.dividedToIntegerBy))return new e(t.toString(10),10);throw Error("[number-to-bn] while converting number "+JSON.stringify(t)+" to BN.js instance, error: invalid number value. Value must be an integer, hex string, BN or BigNumber instance. Note, decimals are not supported.")}},function(t,r,i){"use strict";function e(t){var r=t?t.toLowerCase():"ether",i=l[r];if("string"!=typeof i)throw Error("[ethjs-unit] the unit provided "+t+" doesn't exists, please use the one of the following units "+JSON.stringify(l,null,2));return new s(i,10)}function n(t){if("string"==typeof t){if(!t.match(/^-?[0-9.]+$/))throw Error("while converting number to string, invalid number value '"+t+"', should be a number matching (^-?[0-9.]+).");return t}if("number"==typeof t)return t+"";if("object"==typeof t&&t.toString&&(t.toTwos||t.dividedToIntegerBy))return t.toPrecision?t.toPrecision()+"":t.toString(10);throw Error("while converting number to string, invalid number value '"+t+"' type "+typeof t+".")}function o(t,r,i){var n=u(t),o=n.lt(a),h=e(r),s=l[r].length-1||1,m=i||{};o&&(n=n.mul(f));for(var p=n.mod(h).toString(10);s>p.length;)p="0"+p;m.pad||(p=p.match(/^([0-9]*[1-9]|0)(0*)/)[1]);var d=n.div(h).toString(10);m.commify&&(d=d.replace(/\B(?=(\d{3})+(?!\d))/g,","));var c=""+d+("0"==p?"":"."+p);return o&&(c="-"+c),c}function h(t,r){var i=n(t),o=e(r),h=l[r].length-1||1,u="-"===i.substring(0,1);if(u&&(i=i.substring(1)),"."===i)throw Error("[ethjs-unit] while converting number "+t+" to wei, invalid value");var a=i.split(".");if(a.length>2)throw Error("[ethjs-unit] while converting number "+t+" to wei,  too many decimal points");var m=a[0],p=a[1];if(m||(m="0"),p||(p="0"),p.length>h)throw Error("[ethjs-unit] while converting number "+t+" to wei, too many decimal places");for(;h>p.length;)p+="0";m=new s(m),p=new s(p);var d=m.mul(o).add(p);return u&&(d=d.mul(f)),new s(d.toString(10),10)}var s=i(0),u=i(2),a=new s(0),f=new s((-1)),l={noether:"0",wei:"1",kwei:"1000",Kwei:"1000",babbage:"1000",femtoether:"1000",mwei:"1000000",Mwei:"1000000",lovelace:"1000000",picoether:"1000000",gwei:"1000000000",Gwei:"1000000000",shannon:"1000000000",nanoether:"1000000000",nano:"1000000000",szabo:"1000000000000",microether:"1000000000000",micro:"1000000000000",finney:"1000000000000000",milliether:"1000000000000000",milli:"1000000000000000",ether:"1000000000000000000",kether:"1000000000000000000000",grand:"1000000000000000000000",mether:"1000000000000000000000000",gether:"1000000000000000000000000000",tether:"1000000000000000000000000000000"};t.exports={unitMap:l,numberToString:n,getValueOfUnit:e,fromWei:o,toWei:h}},function(t,r){"use strict";function i(t){var r=t.length;if(r%4>0)throw Error("Invalid string. Length must be a multiple of 4");return"="===t[r-2]?2:"="===t[r-1]?1:0}function e(t){return 3*t.length/4-i(t)}function n(t){var r,e,n,o,h,s,u=t.length;h=i(t),s=new f(3*u/4-h),n=h>0?u-4:u;var l=0;for(r=0,e=0;n>r;r+=4,e+=3)o=a[t.charCodeAt(r)]<<18|a[t.charCodeAt(r+1)]<<12|a[t.charCodeAt(r+2)]<<6|a[t.charCodeAt(r+3)],s[l++]=o>>16&255,s[l++]=o>>8&255,s[l++]=255&o;return 2===h?(o=a[t.charCodeAt(r)]<<2|a[t.charCodeAt(r+1)]>>4,s[l++]=255&o):1===h&&(o=a[t.charCodeAt(r)]<<10|a[t.charCodeAt(r+1)]<<4|a[t.charCodeAt(r+2)]>>2,s[l++]=o>>8&255,s[l++]=255&o),s}function o(t){return u[t>>18&63]+u[t>>12&63]+u[t>>6&63]+u[63&t]}function h(t,r,i){for(var e,n=[],h=r;i>h;h+=3)e=(t[h]<<16)+(t[h+1]<<8)+t[h+2],n.push(o(e));return n.join("")}function s(t){for(var r,i=t.length,e=i%3,n="",o=[],s=16383,a=0,f=i-e;f>a;a+=s)o.push(h(t,a,a+s>f?f:a+s));return 1===e?(r=t[i-1],n+=u[r>>2],n+=u[r<<4&63],n+="=="):2===e&&(r=(t[i-2]<<8)+t[i-1],n+=u[r>>10],n+=u[r>>4&63],n+=u[r<<2&63],n+="="),o.push(n),o.join("")}r.byteLength=e,r.toByteArray=n,r.fromByteArray=s;for(var u=[],a=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m=0,p=l.length;p>m;++m)u[m]=l[m],a[l.charCodeAt(m)]=m;a["-".charCodeAt(0)]=62,a["_".charCodeAt(0)]=63},function(t,r){r.read=function(t,r,i,e,n){var o,h,s=8*n-e-1,u=(1<<s)-1,a=u>>1,f=-7,l=i?n-1:0,m=i?-1:1,p=t[r+l];for(l+=m,o=p&(1<<-f)-1,p>>=-f,f+=s;f>0;o=256*o+t[r+l],l+=m,f-=8);for(h=o&(1<<-f)-1,o>>=-f,f+=e;f>0;h=256*h+t[r+l],l+=m,f-=8);if(0===o)o=1-a;else{if(o===u)return h?NaN:(p?-1:1)*(1/0);h+=Math.pow(2,e),o-=a}return(p?-1:1)*h*Math.pow(2,o-e)},r.write=function(t,r,i,e,n,o){var h,s,u,a=8*o-n-1,f=(1<<a)-1,l=f>>1,m=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,p=e?0:o-1,d=e?1:-1,c=0>r||0===r&&0>1/r?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(s=isNaN(r)?1:0,h=f):(h=Math.floor(Math.log(r)/Math.LN2),r*(u=Math.pow(2,-h))<1&&(h--,u*=2),r+=1>h+l?m*Math.pow(2,1-l):m/u,2>r*u||(h++,u/=2),f>h+l?1>h+l?(s=r*Math.pow(2,l-1)*Math.pow(2,n),h=0):(s=(r*u-1)*Math.pow(2,n),h+=l):(s=0,h=f));n>=8;t[i+p]=255&s,p+=d,s/=256,n-=8);for(h=h<<n|s,a+=n;a>0;t[i+p]=255&h,p+=d,h/=256,a-=8);t[i+p-d]|=128*c}},function(t,r){t.exports=function(t){if("string"!=typeof t)throw Error("[is-hex-prefixed] value must be type 'string', is currently type "+typeof t+", while checking isHexPrefixed.");return"0x"===t.slice(0,2)}},function(t,r){var i={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==i.call(t)}},function(t,r,i){var e=i(6);t.exports=function(t){return"string"!=typeof t?t:e(t)?t.slice(2):t}},function(t,r){var i;i=function(){return this}();try{i=i||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(i=window)}t.exports=i},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,configurable:!1,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,configurable:!1,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])});