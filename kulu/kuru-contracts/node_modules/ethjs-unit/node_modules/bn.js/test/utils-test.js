/* global describe, it */

var assert = require('assert');
var BN = require('../').BN;

describe('BN.js/Utils', function () {
  describe('.toString()', function () {
    describe('binary padding', function () {
      it('should have a length of 256', function () {
        var a = new BN(0);

        assert.equal(a.toString(2, 256).length, 256);
      });
    });
    describe('hex padding', function () {
      it('should have length of 8 from leading 15', function () {
        var a = new BN('ffb9602', 16);

        assert.equal(a.toString('hex', 2).length, 8);
      });

      it('should have length of 8 from leading zero', function () {
        var a = new BN('fb9604', 16);

        assert.equal(a.toString('hex', 8).length, 8);
      });

      it('should have length of 8 from leading zeros', function () {
        var a = new BN(0);

        assert.equal(a.toString('hex', 8).length, 8);
      });

      it('should have length of 64 from leading 15', function () {
        var a = new BN(
          'ffb96ff654e61130ba8422f0debca77a0ea74ae5ea8bca9b54ab64aabf01003',
          16);

        assert.equal(a.toString('hex', 2).length, 64);
      });

      it('should have length of 64 from leading zero', function () {
        var a = new BN(
          'fb96ff654e61130ba8422f0debca77a0ea74ae5ea8bca9b54ab64aabf01003',
          16);

        assert.equal(a.toString('hex', 64).length, 64);
      });
    });
  });

  describe('.isNeg()', function () {
    it('should return true for negative numbers', function () {
      assert.equal(new BN(-1).isNeg(), true);
      assert.equal(new BN(1).isNeg(), false);
      assert.equal(new BN(0).isNeg(), false);
      assert.equal(new BN('-0', 10).isNeg(), false);
    });
  });

  describe('.isOdd()', function () {
    it('should return true for odd numbers', function () {
      assert.equal(new BN(0).isOdd(), false);
      assert.equal(new BN(1).isOdd(), true);
      assert.equal(new BN(2).isOdd(), false);
      assert.equal(new BN('-0', 10).isOdd(), false);
      assert.equal(new BN('-1', 10).isOdd(), true);
      assert.equal(new BN('-2', 10).isOdd(), false);
    });
  });

  describe('.isEven()', function () {
    it('should return true for even numbers', function () {
      assert.equal(new BN(0).isEven(), true);
      assert.equal(new BN(1).isEven(), false);
      assert.equal(new BN(2).isEven(), true);
      assert.equal(new BN('-0', 10).isEven(), true);
      assert.equal(new BN('-1', 10).isEven(), false);
      assert.equal(new BN('-2', 10).isEven(), true);
    });
  });

  describe('.isZero()', function () {
    it('should return true for zero', function () {
      assert.equal(new BN(0).isZero(), true);
      assert.equal(new BN(1).isZero(), false);
      assert.equal(new BN(0xffffffff).isZero(), false);
    });
  });

  describe('.bitLength()', function () {
    it('should return proper bitLength', function () {
      assert.equal(new BN(0).bitLength(), 0);
      assert.equal(new BN(0x1).bitLength(), 1);
      assert.equal(new BN(0x2).bitLength(), 2);
      assert.equal(new BN(0x3).bitLength(), 2);
      assert.equal(new BN(0x4).bitLength(), 3);
      assert.equal(new BN(0x8).bitLength(), 4);
      assert.equal(new BN(0x10).bitLength(), 5);
      assert.equal(new BN(0x100).bitLength(), 9);
      assert.equal(new BN(0x123456).bitLength(), 21);
      assert.equal(new BN('123456789', 16).bitLength(), 33);
      assert.equal(new BN('8023456789', 16).bitLength(), 40);
    });
  });

  describe('.byteLength()', function () {
    it('should return proper byteLength', function () {
      assert.equal(new BN(0).byteLength(), 0);
      assert.equal(new BN(0x1).byteLength(), 1);
      assert.equal(new BN(0x2).byteLength(), 1);
      assert.equal(new BN(0x3).byteLength(), 1);
      assert.equal(new BN(0x4).byteLength(), 1);
      assert.equal(new BN(0x8).byteLength(), 1);
      assert.equal(new BN(0x10).byteLength(), 1);
      assert.equal(new BN(0x100).byteLength(), 2);
      assert.equal(new BN(0x123456).byteLength(), 3);
      assert.equal(new BN('123456789', 16).byteLength(), 5);
      assert.equal(new BN('8023456789', 16).byteLength(), 5);
    });
  });

  describe('.toArray()', function () {
    it('should return [ 0 ] for `0`', function () {
      var n = new BN(0);
      assert.deepEqual(n.toArray('be'), [ 0 ]);
      assert.deepEqual(n.toArray('le'), [ 0 ]);
    });

    it('should zero pad to desired lengths', function () {
      var n = new BN(0x123456);
      assert.deepEqual(n.toArray('be', 5), [ 0x00, 0x00, 0x12, 0x34, 0x56 ]);
      assert.deepEqual(n.toArray('le', 5), [ 0x56, 0x34, 0x12, 0x00, 0x00 ]);
    });

    it('should throw when naturally larger than desired length', function () {
      var n = new BN(0x123456);
      assert.throws(function () {
        n.toArray('be', 2);
      }, /^Error: byte array longer than desired length$/);
    });
  });

  describe('.toBuffer', function () {
    it('should return proper Buffer', function () {
      var n = new BN(0x123456);
      assert.deepEqual(n.toBuffer('be', 5).toString('hex'), '0000123456');
      assert.deepEqual(n.toBuffer('le', 5).toString('hex'), '5634120000');
    });
  });

  describe('.toNumber()', function () {
    it('should return proper Number if below the limit', function () {
      assert.deepEqual(new BN(0x123456).toNumber(), 0x123456);
      assert.deepEqual(new BN(0x3ffffff).toNumber(), 0x3ffffff);
      assert.deepEqual(new BN(0x4000000).toNumber(), 0x4000000);
      assert.deepEqual(new BN(0x10000000000000).toNumber(), 0x10000000000000);
      assert.deepEqual(new BN(0x10040004004000).toNumber(), 0x10040004004000);
      assert.deepEqual(new BN(-0x123456).toNumber(), -0x123456);
      assert.deepEqual(new BN(-0x3ffffff).toNumber(), -0x3ffffff);
      assert.deepEqual(new BN(-0x4000000).toNumber(), -0x4000000);
      assert.deepEqual(new BN(-0x10000000000000).toNumber(), -0x10000000000000);
      assert.deepEqual(new BN(-0x10040004004000).toNumber(), -0x10040004004000);
    });

    it('should throw when number exceeds 53 bits', function () {
      var n = new BN(1).iushln(54);
      assert.throws(function () {
        n.toNumber();
      }, /^Error: Number can only safely store up to 53 bits$/);
    });
  });

  describe('.zeroBits()', function () {
    it('should return proper zeroBits', function () {
      assert.equal(new BN(0).zeroBits(), 0);
      assert.equal(new BN(0x1).zeroBits(), 0);
      assert.equal(new BN(0x2).zeroBits(), 1);
      assert.equal(new BN(0x3).zeroBits(), 0);
      assert.equal(new BN(0x4).zeroBits(), 2);
      assert.equal(new BN(0x8).zeroBits(), 3);
      assert.equal(new BN(0x10).zeroBits(), 4);
      assert.equal(new BN(0x100).zeroBits(), 8);
      assert.equal(new BN(0x1000000).zeroBits(), 24);
      assert.equal(new BN(0x123456).zeroBits(), 1);
    });
  });

  describe('.toJSON', function () {
    it('should return hex string', function () {
      assert.equal(new BN(0x123).toJSON(), '123');
    });
  });

  describe('.cmpn', function () {
    it('should return -1, 0, 1 correctly', function () {
      assert.equal(new BN(42).cmpn(42), 0);
      assert.equal(new BN(42).cmpn(43), -1);
      assert.equal(new BN(42).cmpn(41), 1);
      assert.equal(new BN(0x3fffffe).cmpn(0x3fffffe), 0);
      assert.equal(new BN(0x3fffffe).cmpn(0x3ffffff), -1);
      assert.equal(new BN(0x3fffffe).cmpn(0x3fffffd), 1);
      assert.throws(function () {
        new BN(0x3fffffe).cmpn(0x4000000);
      }, /^Error: Number is too big$/);
      assert.equal(new BN(42).cmpn(-42), 1);
      assert.equal(new BN(-42).cmpn(42), -1);
      assert.equal(new BN(-42).cmpn(-42), 0);
      assert.equal(1 / new BN(-42).cmpn(-42), Infinity);
    });
  });

  describe('.cmp', function () {
    it('should return -1, 0, 1 correctly', function () {
      assert.equal(new BN(42).cmp(new BN(42)), 0);
      assert.equal(new BN(42).cmp(new BN(43)), -1);
      assert.equal(new BN(42).cmp(new BN(41)), 1);
      assert.equal(new BN(0x3fffffe).cmp(new BN(0x3fffffe)), 0);
      assert.equal(new BN(0x3fffffe).cmp(new BN(0x3ffffff)), -1);
      assert.equal(new BN(0x3fffffe).cmp(new BN(0x3fffffd)), 1);
      assert.equal(new BN(0x3fffffe).cmp(new BN(0x4000000)), -1);
      assert.equal(new BN(42).cmp(new BN(-42)), 1);
      assert.equal(new BN(-42).cmp(new BN(42)), -1);
      assert.equal(new BN(-42).cmp(new BN(-42)), 0);
      assert.equal(1 / new BN(-42).cmp(new BN(-42)), Infinity);
    });
  });

  describe('comparison shorthands', function () {
    it('.gtn greater than', function () {
      assert.equal(new BN(3).gtn(2), true);
      assert.equal(new BN(3).gtn(3), false);
      assert.equal(new BN(3).gtn(4), false);
    });
    it('.gt greater than', function () {
      assert.equal(new BN(3).gt(new BN(2)), true);
      assert.equal(new BN(3).gt(new BN(3)), false);
      assert.equal(new BN(3).gt(new BN(4)), false);
    });
    it('.gten greater than or equal', function () {
      assert.equal(new BN(3).gten(3), true);
      assert.equal(new BN(3).gten(2), true);
      assert.equal(new BN(3).gten(4), false);
    });
    it('.gte greater than or equal', function () {
      assert.equal(new BN(3).gte(new BN(3)), true);
      assert.equal(new BN(3).gte(new BN(2)), true);
      assert.equal(new BN(3).gte(new BN(4)), false);
    });
    it('.ltn less than', function () {
      assert.equal(new BN(2).ltn(3), true);
      assert.equal(new BN(2).ltn(2), false);
      assert.equal(new BN(2).ltn(1), false);
    });
    it('.lt less than', function () {
      assert.equal(new BN(2).lt(new BN(3)), true);
      assert.equal(new BN(2).lt(new BN(2)), false);
      assert.equal(new BN(2).lt(new BN(1)), false);
    });
    it('.lten less than or equal', function () {
      assert.equal(new BN(3).lten(3), true);
      assert.equal(new BN(3).lten(2), false);
      assert.equal(new BN(3).lten(4), true);
    });
    it('.lte less than or equal', function () {
      assert.equal(new BN(3).lte(new BN(3)), true);
      assert.equal(new BN(3).lte(new BN(2)), false);
      assert.equal(new BN(3).lte(new BN(4)), true);
    });
    it('.eqn equal', function () {
      assert.equal(new BN(3).eqn(3), true);
      assert.equal(new BN(3).eqn(2), false);
      assert.equal(new BN(3).eqn(4), false);
    });
    it('.eq equal', function () {
      assert.equal(new BN(3).eq(new BN(3)), true);
      assert.equal(new BN(3).eq(new BN(2)), false);
      assert.equal(new BN(3).eq(new BN(4)), false);
    });
  });

  describe('.fromTwos', function () {
    it('should convert from two\'s complement to negative number', function () {
      assert.equal(new BN('00000000', 16).fromTwos(32).toNumber(), 0);
      assert.equal(new BN('00000001', 16).fromTwos(32).toNumber(), 1);
      assert.equal(new BN('7fffffff', 16).fromTwos(32).toNumber(), 2147483647);
      assert.equal(new BN('80000000', 16).fromTwos(32).toNumber(), -2147483648);
      assert.equal(new BN('f0000000', 16).fromTwos(32).toNumber(), -268435456);
      assert.equal(new BN('f1234567', 16).fromTwos(32).toNumber(), -249346713);
      assert.equal(new BN('ffffffff', 16).fromTwos(32).toNumber(), -1);
      assert.equal(new BN('fffffffe', 16).fromTwos(32).toNumber(), -2);
      assert.equal(new BN('fffffffffffffffffffffffffffffffe', 16)
        .fromTwos(128).toNumber(), -2);
      assert.equal(new BN('ffffffffffffffffffffffffffffffff' +
        'fffffffffffffffffffffffffffffffe', 16).fromTwos(256).toNumber(), -2);
      assert.equal(new BN('ffffffffffffffffffffffffffffffff' +
        'ffffffffffffffffffffffffffffffff', 16).fromTwos(256).toNumber(), -1);
      assert.equal(new BN('7fffffffffffffffffffffffffffffff' +
        'ffffffffffffffffffffffffffffffff', 16).fromTwos(256).toString(10),
        new BN('5789604461865809771178549250434395392663499' +
          '2332820282019728792003956564819967', 10).toString(10));
      assert.equal(new BN('80000000000000000000000000000000' +
        '00000000000000000000000000000000', 16).fromTwos(256).toString(10),
        new BN('-578960446186580977117854925043439539266349' +
          '92332820282019728792003956564819968', 10).toString(10));
    });
  });

  describe('.toTwos', function () {
    it('should convert from negative number to two\'s complement', function () {
      assert.equal(new BN(0).toTwos(32).toString(16), '0');
      assert.equal(new BN(1).toTwos(32).toString(16), '1');
      assert.equal(new BN(2147483647).toTwos(32).toString(16), '7fffffff');
      assert.equal(new BN('-2147483648', 10).toTwos(32).toString(16), '80000000');
      assert.equal(new BN('-268435456', 10).toTwos(32).toString(16), 'f0000000');
      assert.equal(new BN('-249346713', 10).toTwos(32).toString(16), 'f1234567');
      assert.equal(new BN('-1', 10).toTwos(32).toString(16), 'ffffffff');
      assert.equal(new BN('-2', 10).toTwos(32).toString(16), 'fffffffe');
      assert.equal(new BN('-2', 10).toTwos(128).toString(16),
        'fffffffffffffffffffffffffffffffe');
      assert.equal(new BN('-2', 10).toTwos(256).toString(16),
        'fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe');
      assert.equal(new BN('-1', 10).toTwos(256).toString(16),
        'ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');
      assert.equal(new BN('5789604461865809771178549250434395392663' +
        '4992332820282019728792003956564819967', 10).toTwos(256).toString(16),
        '7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');
      assert.equal(new BN('-578960446186580977117854925043439539266' +
        '34992332820282019728792003956564819968', 10).toTwos(256).toString(16),
        '8000000000000000000000000000000000000000000000000000000000000000');
    });
  });

  describe('.isBN', function () {
    it('should return true for BN', function () {
      assert.equal(BN.isBN(new BN()), true);
    });

    it('should return false for everything else', function () {
      assert.equal(BN.isBN(1), false);
      assert.equal(BN.isBN([]), false);
      assert.equal(BN.isBN({}), false);
    });
  });
});
