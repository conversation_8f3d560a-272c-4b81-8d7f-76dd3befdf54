{"name": "mocha", "version": "10.2.0", "type": "commonjs", "description": "simple, flexible, fun test framework", "keywords": ["mocha", "test", "bdd", "tdd", "tap", "testing", "chai", "assertion", "ava", "jest", "tape", "jasmine", "karma"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mochajs/mocha.git"}, "bugs": {"url": "https://github.com/mochajs/mocha/issues/"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mochajs"}, "gitter": "https://gitter.im/mochajs/mocha", "homepage": "https://mochajs.org/", "logo": "https://cldup.com/S9uQ-cOLYz.svg", "notifyLogo": "https://ibin.co/4QuRuGjXvl36.png", "bin": {"mocha": "./bin/mocha.js", "_mocha": "./bin/_mocha"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": ">= 14.0.0"}, "scripts": {"prepublishOnly": "nps test clean build", "start": "nps", "test": "nps test", "version": "nps version", "test:smoke": "node ./bin/mocha --no-config test/smoke/smoke.spec.js"}, "dependencies": {"ansi-colors": "4.1.1", "browser-stdout": "1.3.1", "chokidar": "3.5.3", "debug": "4.3.4", "diff": "5.0.0", "escape-string-regexp": "4.0.0", "find-up": "5.0.0", "glob": "7.2.0", "he": "1.2.0", "js-yaml": "4.1.0", "log-symbols": "4.1.0", "minimatch": "5.0.1", "ms": "2.1.3", "nanoid": "3.3.3", "serialize-javascript": "6.0.0", "strip-json-comments": "3.1.1", "supports-color": "8.1.1", "workerpool": "6.2.1", "yargs": "16.2.0", "yargs-parser": "20.2.4", "yargs-unparser": "2.0.0"}, "devDependencies": {"@11ty/eleventy": "^1.0.0", "@11ty/eleventy-plugin-inclusive-language": "^1.0.3", "@babel/eslint-parser": "^7.19.1", "@mocha/docdash": "^4.0.1", "@rollup/plugin-commonjs": "^21.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "assetgraph-builder": "^9.0.0", "autoprefixer": "^9.8.6", "canvas": "^2.9.0", "chai": "^4.3.4", "coffeescript": "^2.6.1", "coveralls": "^3.1.1", "cross-env": "^7.0.2", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-config-semistandard": "^17.0.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.1", "fail-on-errors-webpack-plugin": "^3.0.0", "fs-extra": "^10.0.0", "husky": "^4.2.5", "hyperlink": "^5.0.4", "jsdoc": "^3.6.7", "jsdoc-ts-utils": "^2.0.1", "karma": "^6.3.11", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^4.3.6", "lint-staged": "^10.2.11", "markdown-it": "^12.3.2", "markdown-it-anchor": "^8.4.1", "markdown-it-attrs": "^4.1.3", "markdown-it-emoji": "^2.0.0", "markdown-it-prism": "^2.2.2", "markdown-toc": "^1.2.0", "markdownlint-cli": "^0.30.0", "needle": "^2.5.0", "nps": "^5.10.0", "nyc": "^15.1.0", "pidtree": "^0.5.0", "prettier": "^2.4.1", "remark": "^14.0.2", "remark-github": "^11.2.2", "remark-inline-links": "^6.0.1", "rewiremock": "^3.14.3", "rimraf": "^3.0.2", "rollup": "^2.70.1", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-polyfill-node": "^0.8.0", "rollup-plugin-visualizer": "^5.6.0", "sinon": "^9.0.3", "strip-ansi": "^6.0.0", "svgo": "^1.3.2", "touch": "^3.1.0", "unexpected": "^11.14.0", "unexpected-eventemitter": "^2.2.0", "unexpected-map": "^2.0.0", "unexpected-set": "^3.0.0", "unexpected-sinon": "^10.11.2", "update-notifier": "^4.1.0", "uslug": "^1.0.4", "uuid": "^8.3.0", "webpack": "^5.67.0", "webpack-cli": "^4.9.1"}, "files": ["bin/*mocha*", "lib/**/*.{js,html,json}", "index.js", "mocha.css", "mocha.js", "mocha.js.map", "browser-entry.js"], "browser": {"./index.js": "./browser-entry.js", "fs": false, "path": false, "supports-color": false, "./lib/nodejs/buffered-worker-pool.js": false, "./lib/nodejs/esm-utils.js": false, "./lib/nodejs/file-unloader.js": false, "./lib/nodejs/parallel-buffered-runner.js": false, "./lib/nodejs/serializer.js": false, "./lib/nodejs/worker.js": false, "./lib/nodejs/reporters/parallel-buffered.js": false, "./lib/cli/index.js": false}, "prettier": {"arrowParens": "avoid", "bracketSpacing": false, "endOfLine": "auto", "singleQuote": true, "trailingComma": "none"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}