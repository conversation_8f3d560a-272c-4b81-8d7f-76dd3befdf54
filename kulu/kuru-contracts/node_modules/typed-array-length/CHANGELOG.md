# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.4](https://github.com/inspect-js/typed-array-length/compare/v1.0.3...v1.0.4) - 2022-05-23

### Commits

- [actions] reuse common workflows [`dfd4a37`](https://github.com/inspect-js/typed-array-length/commit/dfd4a37d851a28e3d74d892a69874e02f2e58c37)
- [meta] use `npmignore` to autogenerate an npmignore file [`a837e80`](https://github.com/inspect-js/typed-array-length/commit/a837e80d4029f26785ab9f3aa571ca782ac8e851)
- [Dev <PERSON>] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `is-callable`, `object-inspect`, `tape` [`7b05a87`](https://github.com/inspect-js/typed-array-length/commit/7b05a8772af399e52bb448618a246cd34d3e3273)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`c495f6e`](https://github.com/inspect-js/typed-array-length/commit/c495f6e050a4a7463a82c9195f31f44cf2760945)
- [meta] simplify "exports" [`e42a6b6`](https://github.com/inspect-js/typed-array-length/commit/e42a6b6b0dc243fce32df20a75a7962782ef2a83)
- [Fix] ensure `for-each` dependency is properly listed [`8ec761c`](https://github.com/inspect-js/typed-array-length/commit/8ec761ca56c13927281d626958a2f55211e14f45)
- [Deps] update `call-bind`, `is-typed-array` [`2cc173a`](https://github.com/inspect-js/typed-array-length/commit/2cc173a4216e167db896bea7b8e03edf8b2d3833)
- [meta] add `safe-publish-latest` [`e8e3afa`](https://github.com/inspect-js/typed-array-length/commit/e8e3afa431ce98bbdbb68c9f8e3c029cc5128c6c)
- [Deps] update `is-typed-array` [`cd8084d`](https://github.com/inspect-js/typed-array-length/commit/cd8084db59b734ac4519b6d47f96233b6f73b1a6)

## [v1.0.3](https://github.com/inspect-js/typed-array-length/compare/v1.0.2...v1.0.3) - 2020-12-05

### Commits

- [Tests] migrate tests to Github Actions [`a578b83`](https://github.com/inspect-js/typed-array-length/commit/a578b83e68055c1e7c7120bc4583e1d6926fc268)
- [meta] avoid publishing github workflows [`f064a4b`](https://github.com/inspect-js/typed-array-length/commit/f064a4bf9090202154249d969be0799c34804ad4)
- [Tests] run `nyc` on all tests [`69b841e`](https://github.com/inspect-js/typed-array-length/commit/69b841e43042358c71c3290342514b6d107f08d1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`4594e83`](https://github.com/inspect-js/typed-array-length/commit/4594e83250579cdbff870aa951e7af56ca169489)
- [actions] add "Allow Edits" workflow [`81e953b`](https://github.com/inspect-js/typed-array-length/commit/81e953ba6b3f59c5657e0d17fa1e7619b94891f5)
- [Deps] update `is-typed-array`; use `call-bind` instead of `es-abstract` [`e7da56b`](https://github.com/inspect-js/typed-array-length/commit/e7da56b3c03b7f0db9bb110444ec1ccf19d7e9f9)
- [readme] remove travis badge [`6d610d8`](https://github.com/inspect-js/typed-array-length/commit/6d610d83cb78ac5286c5ca273f4b3c7289f7686e)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`2d0ad64`](https://github.com/inspect-js/typed-array-length/commit/2d0ad644a11f754e61b49d327fdf891605abbe58)

## [v1.0.2](https://github.com/inspect-js/typed-array-length/compare/v1.0.1...v1.0.2) - 2020-04-22

### Commits

- [Dev Deps] update `make-arrow-function`, `make-generator-function` [`4facf69`](https://github.com/inspect-js/typed-array-length/commit/4facf697cafb36b9c1057dc4ca1a21d8550c564e)
- [Deps] update `is-typed-array`, `es-abstract` [`aaf3585`](https://github.com/inspect-js/typed-array-length/commit/aaf3585429896b9520dedd886c07aa4a96b50615)
- [Dev Deps] update `aud`, `auto-changelog` [`f10e298`](https://github.com/inspect-js/typed-array-length/commit/f10e298c7733b8de59231c1581c9b000c205edbd)
- [meta] allow `package.json` to be required/imported [`104f4c6`](https://github.com/inspect-js/typed-array-length/commit/104f4c6a6363e600d54aeb7abd90e37d99693aaf)
- [Tests] only audit prod deps [`c748ab5`](https://github.com/inspect-js/typed-array-length/commit/c748ab596de505483df14ca7eeda7f27aeb20383)
- [Deps] update `es-abstract` [`6cd213e`](https://github.com/inspect-js/typed-array-length/commit/6cd213ec654da3325abc8190f8c07c860474d944)
- [Dev Deps] update `tape` [`2b0b2ea`](https://github.com/inspect-js/typed-array-length/commit/2b0b2ea9be106e8a068597c3f499ef703cce1edb)
- [Dev Deps] update `@ljharb/eslint-config` [`cf462f3`](https://github.com/inspect-js/typed-array-length/commit/cf462f3352cf2fd592e624746371e3de800a265d)
- [Deps] update `is-typed-array` [`ff46995`](https://github.com/inspect-js/typed-array-length/commit/ff469955b5d92942ba066c77eac7467e0c4de1ec)

## [v1.0.1](https://github.com/inspect-js/typed-array-length/compare/v1.0.0...v1.0.1) - 2020-01-19

### Commits

- readme [`d3643fd`](https://github.com/inspect-js/typed-array-length/commit/d3643fd11919844b1f42041ef980a1f33215b515)
- [meta] fix "exports" field [`006e28b`](https://github.com/inspect-js/typed-array-length/commit/006e28b30b11f8948e607d13ef0e96c3d7d7f61f)

## v1.0.0 - 2020-01-18

### Commits

- Initial commit [`5f9e2ec`](https://github.com/inspect-js/typed-array-length/commit/5f9e2ec6650f80dc894e354e9e98181b09006346)
- Tests [`6b9cadb`](https://github.com/inspect-js/typed-array-length/commit/6b9cadb0c274933bc7ee5e3fc6a5a380163cbe76)
- Implementation [`6a3cb50`](https://github.com/inspect-js/typed-array-length/commit/6a3cb50429f40fc4ac9020bbf9539560c1b70213)
- npm init [`41d42cd`](https://github.com/inspect-js/typed-array-length/commit/41d42cddfd3d47df6c9d480cf77787eae1109432)
- [meta] add `auto-changelog` [`4fd159b`](https://github.com/inspect-js/typed-array-length/commit/4fd159bc6535e86c370a2186d60a68656f0d8917)
- [meta] add `funding` field; create FUNDING.yml [`6a9fca7`](https://github.com/inspect-js/typed-array-length/commit/6a9fca7e0fdf3ff3fd4b0f18596471ca3d050a39)
- [actions] add automatic rebasing / merge commit blocking [`8303296`](https://github.com/inspect-js/typed-array-length/commit/83032967b14afd37c382d4bf2c1fc5c95e3764bd)
- [Tests] add `npm run lint` [`47a9c21`](https://github.com/inspect-js/typed-array-length/commit/47a9c211f474dbe8528f6b28f50080eacd5bf7eb)
- [Tests] use shared travis-ci configs [`d0c8915`](https://github.com/inspect-js/typed-array-length/commit/d0c89153e1c50f1eadd0b42521bcdcf3366b8af5)
- Only apps should have lockfiles [`3eaef9c`](https://github.com/inspect-js/typed-array-length/commit/3eaef9cd192b1a25d1930739e7c0044e39ad3c0d)
