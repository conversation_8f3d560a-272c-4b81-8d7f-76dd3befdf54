{"name": "object.assign", "version": "4.1.0", "author": "<PERSON>", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint && es-shim-api", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim", "test:native": "node test/native.js", "test:shim": "node test/shimmed.js", "test:implementation": "node test/index.js", "coverage": "covert test/*.js", "coverage:quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "eslint": "eslint *.js test/*.js", "jscs": "jscs *.js test/*.js", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublish": "npm run --silent build", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "dependencies": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}, "devDependencies": {"@es-shims/api": "^2.1.1", "@ljharb/eslint-config": "^12.2.1", "browserify": "^14.5.0", "covert": "^1.1.0", "eslint": "^4.13.1", "for-each": "^0.3.2", "is": "^3.2.1", "jscs": "^3.0.7", "nsp": "^3.1.0", "tape": "^4.8.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}