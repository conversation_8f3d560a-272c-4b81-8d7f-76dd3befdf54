(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.meriyah=e()}})(function(){"use strict";var B=(a,g)=>()=>(g||a((g={exports:{}}).exports,g),g.exports);var k2=B((X3,Fu)=>{var A1=function(a){return a&&a.Math==Math&&a};Fu.exports=A1(typeof globalThis=="object"&&globalThis)||A1(typeof window=="object"&&window)||A1(typeof self=="object"&&self)||A1(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var D2=B((z3,Lu)=>{Lu.exports=function(a){try{return!!a()}catch{return!0}}});var S2=B((W3,Ou)=>{var uo=D2();Ou.exports=!uo(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var ue=B((K3,Tu)=>{var io=D2();Tu.exports=!io(function(){var a=function(){}.bind();return typeof a!="function"||a.hasOwnProperty("prototype")})});var E1=B((Y3,Iu)=>{var no=ue(),P1=Function.prototype.call;Iu.exports=no?P1.bind(P1):function(){return P1.apply(P1,arguments)}});var ju=B(Nu=>{"use strict";var Ru={}.propertyIsEnumerable,Vu=Object.getOwnPropertyDescriptor,to=Vu&&!Ru.call({1:2},1);Nu.f=to?function(g){var b=Vu(this,g);return!!b&&b.enumerable}:Ru});var ie=B((Q3,_u)=>{_u.exports=function(a,g){return{enumerable:!(a&1),configurable:!(a&2),writable:!(a&4),value:g}}});var F2=B((G3,Ju)=>{var Mu=ue(),Uu=Function.prototype,ne=Uu.call,oo=Mu&&Uu.bind.bind(ne,ne);Ju.exports=Mu?oo:function(a){return function(){return ne.apply(a,arguments)}}});var Xu=B((x3,Hu)=>{var $u=F2(),lo=$u({}.toString),fo=$u("".slice);Hu.exports=function(a){return fo(lo(a),8,-1)}});var Wu=B((p3,zu)=>{var co=F2(),so=D2(),ao=Xu(),te=Object,go=co("".split);zu.exports=so(function(){return!te("z").propertyIsEnumerable(0)})?function(a){return ao(a)=="String"?go(a,""):te(a)}:te});var oe=B((e6,Ku)=>{Ku.exports=function(a){return a==null}});var le=B((u6,Yu)=>{var ho=oe(),mo=TypeError;Yu.exports=function(a){if(ho(a))throw mo("Can't call method on "+a);return a}});var C1=B((i6,Zu)=>{var bo=Wu(),ko=le();Zu.exports=function(a){return bo(ko(a))}});var ce=B((n6,Qu)=>{var fe=typeof document=="object"&&document.all,ro=typeof fe>"u"&&fe!==void 0;Qu.exports={all:fe,IS_HTMLDDA:ro}});var A2=B((t6,xu)=>{var Gu=ce(),vo=Gu.all;xu.exports=Gu.IS_HTMLDDA?function(a){return typeof a=="function"||a===vo}:function(a){return typeof a=="function"}});var Z2=B((o6,ui)=>{var pu=A2(),ei=ce(),yo=ei.all;ui.exports=ei.IS_HTMLDDA?function(a){return typeof a=="object"?a!==null:pu(a)||a===yo}:function(a){return typeof a=="object"?a!==null:pu(a)}});var D1=B((l6,ii)=>{var se=k2(),Ao=A2(),Po=function(a){return Ao(a)?a:void 0};ii.exports=function(a,g){return arguments.length<2?Po(se[a]):se[a]&&se[a][g]}});var ti=B((f6,ni)=>{var Eo=F2();ni.exports=Eo({}.isPrototypeOf)});var li=B((c6,oi)=>{var Co=D1();oi.exports=Co("navigator","userAgent")||""});var hi=B((s6,gi)=>{var di=k2(),ae=li(),fi=di.process,ci=di.Deno,si=fi&&fi.versions||ci&&ci.version,ai=si&&si.v8,P2,w1;ai&&(P2=ai.split("."),w1=P2[0]>0&&P2[0]<4?1:+(P2[0]+P2[1]));!w1&&ae&&(P2=ae.match(/Edge\/(\d+)/),(!P2||P2[1]>=74)&&(P2=ae.match(/Chrome\/(\d+)/),P2&&(w1=+P2[1])));gi.exports=w1});var de=B((a6,bi)=>{var mi=hi(),Do=D2();bi.exports=!!Object.getOwnPropertySymbols&&!Do(function(){var a=Symbol();return!String(a)||!(Object(a)instanceof Symbol)||!Symbol.sham&&mi&&mi<41})});var ge=B((d6,ki)=>{var wo=de();ki.exports=wo&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var he=B((g6,ri)=>{var qo=D1(),Bo=A2(),So=ti(),Fo=ge(),Lo=Object;ri.exports=Fo?function(a){return typeof a=="symbol"}:function(a){var g=qo("Symbol");return Bo(g)&&So(g.prototype,Lo(a))}});var yi=B((h6,vi)=>{var Oo=String;vi.exports=function(a){try{return Oo(a)}catch{return"Object"}}});var Pi=B((m6,Ai)=>{var To=A2(),Io=yi(),Ro=TypeError;Ai.exports=function(a){if(To(a))return a;throw Ro(Io(a)+" is not a function")}});var Ci=B((b6,Ei)=>{var Vo=Pi(),No=oe();Ei.exports=function(a,g){var b=a[g];return No(b)?void 0:Vo(b)}});var wi=B((k6,Di)=>{var me=E1(),be=A2(),ke=Z2(),jo=TypeError;Di.exports=function(a,g){var b,f;if(g==="string"&&be(b=a.toString)&&!ke(f=me(b,a))||be(b=a.valueOf)&&!ke(f=me(b,a))||g!=="string"&&be(b=a.toString)&&!ke(f=me(b,a)))return f;throw jo("Can't convert object to primitive value")}});var Bi=B((r6,qi)=>{qi.exports=!1});var q1=B((v6,Fi)=>{var Si=k2(),_o=Object.defineProperty;Fi.exports=function(a,g){try{_o(Si,a,{value:g,configurable:!0,writable:!0})}catch{Si[a]=g}return g}});var B1=B((y6,Oi)=>{var Mo=k2(),Uo=q1(),Li="__core-js_shared__",Jo=Mo[Li]||Uo(Li,{});Oi.exports=Jo});var re=B((A6,Ii)=>{var $o=Bi(),Ti=B1();(Ii.exports=function(a,g){return Ti[a]||(Ti[a]=g!==void 0?g:{})})("versions",[]).push({version:"3.26.1",mode:$o?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var Vi=B((P6,Ri)=>{var Ho=le(),Xo=Object;Ri.exports=function(a){return Xo(Ho(a))}});var R2=B((E6,Ni)=>{var zo=F2(),Wo=Vi(),Ko=zo({}.hasOwnProperty);Ni.exports=Object.hasOwn||function(g,b){return Ko(Wo(g),b)}});var ve=B((C6,ji)=>{var Yo=F2(),Zo=0,Qo=Math.random(),Go=Yo(1 .toString);ji.exports=function(a){return"Symbol("+(a===void 0?"":a)+")_"+Go(++Zo+Qo,36)}});var Hi=B((D6,$i)=>{var xo=k2(),po=re(),_i=R2(),el=ve(),Mi=de(),Ji=ge(),Q2=po("wks"),$2=xo.Symbol,Ui=$2&&$2.for,ul=Ji?$2:$2&&$2.withoutSetter||el;$i.exports=function(a){if(!_i(Q2,a)||!(Mi||typeof Q2[a]=="string")){var g="Symbol."+a;Mi&&_i($2,a)?Q2[a]=$2[a]:Ji&&Ui?Q2[a]=Ui(g):Q2[a]=ul(g)}return Q2[a]}});var Ki=B((w6,Wi)=>{var il=E1(),Xi=Z2(),zi=he(),nl=Ci(),tl=wi(),ol=Hi(),ll=TypeError,fl=ol("toPrimitive");Wi.exports=function(a,g){if(!Xi(a)||zi(a))return a;var b=nl(a,fl),f;if(b){if(g===void 0&&(g="default"),f=il(b,a,g),!Xi(f)||zi(f))return f;throw ll("Can't convert object to primitive value")}return g===void 0&&(g="number"),tl(a,g)}});var ye=B((q6,Yi)=>{var cl=Ki(),sl=he();Yi.exports=function(a){var g=cl(a,"string");return sl(g)?g:g+""}});var Gi=B((B6,Qi)=>{var al=k2(),Zi=Z2(),Ae=al.document,dl=Zi(Ae)&&Zi(Ae.createElement);Qi.exports=function(a){return dl?Ae.createElement(a):{}}});var Pe=B((S6,xi)=>{var gl=S2(),hl=D2(),ml=Gi();xi.exports=!gl&&!hl(function(){return Object.defineProperty(ml("div"),"a",{get:function(){return 7}}).a!=7})});var Ee=B(en=>{var bl=S2(),kl=E1(),rl=ju(),vl=ie(),yl=C1(),Al=ye(),Pl=R2(),El=Pe(),pi=Object.getOwnPropertyDescriptor;en.f=bl?pi:function(g,b){if(g=yl(g),b=Al(b),El)try{return pi(g,b)}catch{}if(Pl(g,b))return vl(!kl(rl.f,g,b),g[b])}});var nn=B((L6,un)=>{var Cl=S2(),Dl=D2();un.exports=Cl&&Dl(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var S1=B((O6,tn)=>{var wl=Z2(),ql=String,Bl=TypeError;tn.exports=function(a){if(wl(a))return a;throw Bl(ql(a)+" is not an object")}});var u1=B(ln=>{var Sl=S2(),Fl=Pe(),Ll=nn(),F1=S1(),on=ye(),Ol=TypeError,Ce=Object.defineProperty,Tl=Object.getOwnPropertyDescriptor,De="enumerable",we="configurable",qe="writable";ln.f=Sl?Ll?function(g,b,f){if(F1(g),b=on(b),F1(f),typeof g=="function"&&b==="prototype"&&"value"in f&&qe in f&&!f[qe]){var A=Tl(g,b);A&&A[qe]&&(g[b]=f.value,f={configurable:we in f?f[we]:A[we],enumerable:De in f?f[De]:A[De],writable:!1})}return Ce(g,b,f)}:Ce:function(g,b,f){if(F1(g),b=on(b),F1(f),Fl)try{return Ce(g,b,f)}catch{}if("get"in f||"set"in f)throw Ol("Accessors not supported");return"value"in f&&(g[b]=f.value),g}});var Be=B((I6,fn)=>{var Il=S2(),Rl=u1(),Vl=ie();fn.exports=Il?function(a,g,b){return Rl.f(a,g,Vl(1,b))}:function(a,g,b){return a[g]=b,a}});var an=B((R6,sn)=>{var Se=S2(),Nl=R2(),cn=Function.prototype,jl=Se&&Object.getOwnPropertyDescriptor,Fe=Nl(cn,"name"),_l=Fe&&function(){}.name==="something",Ml=Fe&&(!Se||Se&&jl(cn,"name").configurable);sn.exports={EXISTS:Fe,PROPER:_l,CONFIGURABLE:Ml}});var gn=B((V6,dn)=>{var Ul=F2(),Jl=A2(),Le=B1(),$l=Ul(Function.toString);Jl(Le.inspectSource)||(Le.inspectSource=function(a){return $l(a)});dn.exports=Le.inspectSource});var bn=B((N6,mn)=>{var Hl=k2(),Xl=A2(),hn=Hl.WeakMap;mn.exports=Xl(hn)&&/native code/.test(String(hn))});var vn=B((j6,rn)=>{var zl=re(),Wl=ve(),kn=zl("keys");rn.exports=function(a){return kn[a]||(kn[a]=Wl(a))}});var Oe=B((_6,yn)=>{yn.exports={}});var Cn=B((M6,En)=>{var Kl=bn(),Pn=k2(),Yl=Z2(),Zl=Be(),Te=R2(),Ie=B1(),Ql=vn(),Gl=Oe(),An="Object already initialized",Re=Pn.TypeError,xl=Pn.WeakMap,L1,i1,O1,pl=function(a){return O1(a)?i1(a):L1(a,{})},e4=function(a){return function(g){var b;if(!Yl(g)||(b=i1(g)).type!==a)throw Re("Incompatible receiver, "+a+" required");return b}};Kl||Ie.state?(E2=Ie.state||(Ie.state=new xl),E2.get=E2.get,E2.has=E2.has,E2.set=E2.set,L1=function(a,g){if(E2.has(a))throw Re(An);return g.facade=a,E2.set(a,g),g},i1=function(a){return E2.get(a)||{}},O1=function(a){return E2.has(a)}):(H2=Ql("state"),Gl[H2]=!0,L1=function(a,g){if(Te(a,H2))throw Re(An);return g.facade=a,Zl(a,H2,g),g},i1=function(a){return Te(a,H2)?a[H2]:{}},O1=function(a){return Te(a,H2)});var E2,H2;En.exports={set:L1,get:i1,has:O1,enforce:pl,getterFor:e4}});var Ne=B((U6,wn)=>{var u4=D2(),i4=A2(),T1=R2(),Ve=S2(),n4=an().CONFIGURABLE,t4=gn(),Dn=Cn(),o4=Dn.enforce,l4=Dn.get,I1=Object.defineProperty,f4=Ve&&!u4(function(){return I1(function(){},"length",{value:8}).length!==8}),c4=String(String).split("String"),s4=wn.exports=function(a,g,b){String(g).slice(0,7)==="Symbol("&&(g="["+String(g).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),b&&b.getter&&(g="get "+g),b&&b.setter&&(g="set "+g),(!T1(a,"name")||n4&&a.name!==g)&&(Ve?I1(a,"name",{value:g,configurable:!0}):a.name=g),f4&&b&&T1(b,"arity")&&a.length!==b.arity&&I1(a,"length",{value:b.arity});try{b&&T1(b,"constructor")&&b.constructor?Ve&&I1(a,"prototype",{writable:!1}):a.prototype&&(a.prototype=void 0)}catch{}var f=o4(a);return T1(f,"source")||(f.source=c4.join(typeof g=="string"?g:"")),a};Function.prototype.toString=s4(function(){return i4(this)&&l4(this).source||t4(this)},"toString")});var Bn=B((J6,qn)=>{var a4=A2(),d4=u1(),g4=Ne(),h4=q1();qn.exports=function(a,g,b,f){f||(f={});var A=f.enumerable,L=f.name!==void 0?f.name:g;if(a4(b)&&g4(b,L,f),f.global)A?a[g]=b:h4(g,b);else{try{f.unsafe?a[g]&&(A=!0):delete a[g]}catch{}A?a[g]=b:d4.f(a,g,{value:b,enumerable:!1,configurable:!f.nonConfigurable,writable:!f.nonWritable})}return a}});var Fn=B(($6,Sn)=>{var m4=Math.ceil,b4=Math.floor;Sn.exports=Math.trunc||function(g){var b=+g;return(b>0?b4:m4)(b)}});var je=B((H6,Ln)=>{var k4=Fn();Ln.exports=function(a){var g=+a;return g!==g||g===0?0:k4(g)}});var Tn=B((X6,On)=>{var r4=je(),v4=Math.max,y4=Math.min;On.exports=function(a,g){var b=r4(a);return b<0?v4(b+g,0):y4(b,g)}});var Rn=B((z6,In)=>{var A4=je(),P4=Math.min;In.exports=function(a){return a>0?P4(A4(a),9007199254740991):0}});var Nn=B((W6,Vn)=>{var E4=Rn();Vn.exports=function(a){return E4(a.length)}});var Mn=B((K6,_n)=>{var C4=C1(),D4=Tn(),w4=Nn(),jn=function(a){return function(g,b,f){var A=C4(g),L=w4(A),S=D4(f,L),V;if(a&&b!=b){for(;L>S;)if(V=A[S++],V!=V)return!0}else for(;L>S;S++)if((a||S in A)&&A[S]===b)return a||S||0;return!a&&-1}};_n.exports={includes:jn(!0),indexOf:jn(!1)}});var $n=B((Y6,Jn)=>{var q4=F2(),_e=R2(),B4=C1(),S4=Mn().indexOf,F4=Oe(),Un=q4([].push);Jn.exports=function(a,g){var b=B4(a),f=0,A=[],L;for(L in b)!_e(F4,L)&&_e(b,L)&&Un(A,L);for(;g.length>f;)_e(b,L=g[f++])&&(~S4(A,L)||Un(A,L));return A}});var Xn=B((Z6,Hn)=>{Hn.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var Wn=B(zn=>{var L4=$n(),O4=Xn(),T4=O4.concat("length","prototype");zn.f=Object.getOwnPropertyNames||function(g){return L4(g,T4)}});var Yn=B(Kn=>{Kn.f=Object.getOwnPropertySymbols});var Qn=B((x6,Zn)=>{var I4=D1(),R4=F2(),V4=Wn(),N4=Yn(),j4=S1(),_4=R4([].concat);Zn.exports=I4("Reflect","ownKeys")||function(g){var b=V4.f(j4(g)),f=N4.f;return f?_4(b,f(g)):b}});var pn=B((p6,xn)=>{var Gn=R2(),M4=Qn(),U4=Ee(),J4=u1();xn.exports=function(a,g,b){for(var f=M4(g),A=J4.f,L=U4.f,S=0;S<f.length;S++){var V=f[S];!Gn(a,V)&&!(b&&Gn(b,V))&&A(a,V,L(g,V))}}});var u0=B((ef,e0)=>{var $4=D2(),H4=A2(),X4=/#|\.prototype\./,n1=function(a,g){var b=W4[z4(a)];return b==Y4?!0:b==K4?!1:H4(g)?$4(g):!!g},z4=n1.normalize=function(a){return String(a).replace(X4,".").toLowerCase()},W4=n1.data={},K4=n1.NATIVE="N",Y4=n1.POLYFILL="P";e0.exports=n1});var n0=B((uf,i0)=>{var Me=k2(),Z4=Ee().f,Q4=Be(),G4=Bn(),x4=q1(),p4=pn(),e3=u0();i0.exports=function(a,g){var b=a.target,f=a.global,A=a.stat,L,S,V,r,X,Y;if(f?S=Me:A?S=Me[b]||x4(b,{}):S=(Me[b]||{}).prototype,S)for(V in g){if(X=g[V],a.dontCallGetSet?(Y=Z4(S,V),r=Y&&Y.value):r=S[V],L=e3(f?V:b+(A?".":"#")+V,a.forced),!L&&r!==void 0){if(typeof X==typeof r)continue;p4(X,r)}(a.sham||r&&r.sham)&&Q4(X,"sham",!0),G4(S,V,X,a)}}});var t0=B(()=>{var u3=n0(),Ue=k2();u3({global:!0,forced:Ue.globalThis!==Ue},{globalThis:Ue})});var o0=B(()=>{t0()});var c0=B((ff,f0)=>{var l0=Ne(),i3=u1();f0.exports=function(a,g,b){return b.get&&l0(b.get,g,{getter:!0}),b.set&&l0(b.set,g,{setter:!0}),i3.f(a,g,b)}});var a0=B((cf,s0)=>{"use strict";var n3=S1();s0.exports=function(){var a=n3(this),g="";return a.hasIndices&&(g+="d"),a.global&&(g+="g"),a.ignoreCase&&(g+="i"),a.multiline&&(g+="m"),a.dotAll&&(g+="s"),a.unicode&&(g+="u"),a.unicodeSets&&(g+="v"),a.sticky&&(g+="y"),g}});var h0=B(()=>{var t3=k2(),o3=S2(),l3=c0(),f3=a0(),c3=D2(),d0=t3.RegExp,g0=d0.prototype,s3=o3&&c3(function(){var a=!0;try{d0(".","d")}catch{a=!1}var g={},b="",f=a?"dgimsy":"gimsy",A=function(r,X){Object.defineProperty(g,r,{get:function(){return b+=X,!0}})},L={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};a&&(L.hasIndices="d");for(var S in L)A(S,L[S]);var V=Object.getOwnPropertyDescriptor(g0,"flags").get.call(g);return V!==f||b!==f});s3&&l3(g0,"flags",{configurable:!0,get:f3})});var $3=B((df,O0)=>{o0();h0();var Xe=Object.defineProperty,a3=Object.getOwnPropertyDescriptor,ze=Object.getOwnPropertyNames,d3=Object.prototype.hasOwnProperty,b0=(a,g)=>function(){return a&&(g=(0,a[ze(a)[0]])(a=0)),g},t2=(a,g)=>function(){return g||(0,a[ze(a)[0]])((g={exports:{}}).exports,g),g.exports},g3=(a,g)=>{for(var b in g)Xe(a,b,{get:g[b],enumerable:!0})},h3=(a,g,b,f)=>{if(g&&typeof g=="object"||typeof g=="function")for(let A of ze(g))!d3.call(a,A)&&A!==b&&Xe(a,A,{get:()=>g[A],enumerable:!(f=a3(g,A))||f.enumerable});return a},m3=a=>h3(Xe({},"__esModule",{value:!0}),a),n2=b0({"<define:process>"(){}}),k0=t2({"src/common/parser-create-error.js"(a,g){"use strict";n2();function b(f,A){let L=new SyntaxError(f+" ("+A.start.line+":"+A.start.column+")");return L.loc=A,L}g.exports=b}}),b3=t2({"src/utils/try-combinations.js"(a,g){"use strict";n2();function b(){let f;for(var A=arguments.length,L=new Array(A),S=0;S<A;S++)L[S]=arguments[S];for(let[V,r]of L.entries())try{return{result:r()}}catch(X){V===0&&(f=X)}return{error:f}}g.exports=b}}),r0={};g3(r0,{EOL:()=>He,arch:()=>k3,cpus:()=>D0,default:()=>F0,endianness:()=>v0,freemem:()=>E0,getNetworkInterfaces:()=>S0,hostname:()=>y0,loadavg:()=>A0,networkInterfaces:()=>B0,platform:()=>r3,release:()=>q0,tmpDir:()=>Je,tmpdir:()=>$e,totalmem:()=>C0,type:()=>w0,uptime:()=>P0});function v0(){if(typeof R1>"u"){var a=new ArrayBuffer(2),g=new Uint8Array(a),b=new Uint16Array(a);if(g[0]=1,g[1]=2,b[0]===258)R1="BE";else if(b[0]===513)R1="LE";else throw new Error("unable to figure out endianess")}return R1}function y0(){return typeof globalThis.location<"u"?globalThis.location.hostname:""}function A0(){return[]}function P0(){return 0}function E0(){return Number.MAX_VALUE}function C0(){return Number.MAX_VALUE}function D0(){return[]}function w0(){return"Browser"}function q0(){return typeof globalThis.navigator<"u"?globalThis.navigator.appVersion:""}function B0(){}function S0(){}function k3(){return"javascript"}function r3(){return"browser"}function Je(){return"/tmp"}var R1,$e,He,F0,v3=b0({"node-modules-polyfills:os"(){n2(),$e=Je,He=`
`,F0={EOL:He,tmpdir:$e,tmpDir:Je,networkInterfaces:B0,getNetworkInterfaces:S0,release:q0,type:w0,cpus:D0,totalmem:C0,freemem:E0,uptime:P0,loadavg:A0,hostname:y0,endianness:v0}}}),y3=t2({"node-modules-polyfills-commonjs:os"(a,g){n2();var b=(v3(),m3(r0));if(b&&b.default){g.exports=b.default;for(let f in b)g.exports[f]=b[f]}else b&&(g.exports=b)}}),A3=t2({"node_modules/detect-newline/index.js"(a,g){"use strict";n2();var b=f=>{if(typeof f!="string")throw new TypeError("Expected a string");let A=f.match(/(?:\r?\n)/g)||[];if(A.length===0)return;let L=A.filter(V=>V===`\r
`).length,S=A.length-L;return L>S?`\r
`:`
`};g.exports=b,g.exports.graceful=f=>typeof f=="string"&&b(f)||`
`}}),P3=t2({"node_modules/jest-docblock/build/index.js"(a){"use strict";n2(),Object.defineProperty(a,"__esModule",{value:!0}),a.extract=T,a.parse=w2,a.parseWithComments=C,a.print=J,a.strip=z;function g(){let U=y3();return g=function(){return U},U}function b(){let U=f(A3());return b=function(){return U},U}function f(U){return U&&U.__esModule?U:{default:U}}var A=/\*\/$/,L=/^\/\*\*?/,S=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,V=/(^|\s+)\/\/([^\r\n]*)/g,r=/^(\r?\n)+/,X=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,Y=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,G=/(\r?\n|^) *\* ?/g,u2=[];function T(U){let e2=U.match(S);return e2?e2[0].trimLeft():""}function z(U){let e2=U.match(S);return e2&&e2[0]?U.substring(e2[0].length):U}function w2(U){return C(U).pragmas}function C(U){let e2=(0,b().default)(U)||g().EOL;U=U.replace(L,"").replace(A,"").replace(G,"$1");let g2="";for(;g2!==U;)g2=U,U=U.replace(X,`${e2}$1 $2${e2}`);U=U.replace(r,"").trimRight();let l2=Object.create(null),V2=U.replace(Y,"").replace(r,"").trimRight(),f2;for(;f2=Y.exec(U);){let N2=f2[2].replace(V,"");typeof l2[f2[1]]=="string"||Array.isArray(l2[f2[1]])?l2[f2[1]]=u2.concat(l2[f2[1]],N2):l2[f2[1]]=N2}return{comments:V2,pragmas:l2}}function J(U){let{comments:e2="",pragmas:g2={}}=U,l2=(0,b().default)(e2)||g().EOL,V2="/**",f2=" *",N2=" */",q2=Object.keys(g2),V1=q2.map(a2=>p(a2,g2[a2])).reduce((a2,t1)=>a2.concat(t1),[]).map(a2=>`${f2} ${a2}${l2}`).join("");if(!e2){if(q2.length===0)return"";if(q2.length===1&&!Array.isArray(g2[q2[0]])){let a2=g2[q2[0]];return`${V2} ${p(q2[0],a2)[0]}${N2}`}}let N1=e2.split(l2).map(a2=>`${f2} ${a2}`).join(l2)+l2;return V2+l2+(e2?N1:"")+(e2&&q2.length?f2+l2:"")+V1+N2}function p(U,e2){return u2.concat(e2).map(g2=>`@${U} ${g2}`.trim())}}}),E3=t2({"src/common/end-of-line.js"(a,g){"use strict";n2();function b(S){let V=S.indexOf("\r");return V>=0?S.charAt(V+1)===`
`?"crlf":"cr":"lf"}function f(S){switch(S){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function A(S,V){let r;switch(V){case`
`:r=/\n/g;break;case"\r":r=/\r/g;break;case`\r
`:r=/\r\n/g;break;default:throw new Error(`Unexpected "eol" ${JSON.stringify(V)}.`)}let X=S.match(r);return X?X.length:0}function L(S){return S.replace(/\r\n?/g,`
`)}g.exports={guessEndOfLine:b,convertEndOfLineToChars:f,countEndOfLineChars:A,normalizeEndOfLine:L}}}),C3=t2({"src/language-js/utils/get-shebang.js"(a,g){"use strict";n2();function b(f){if(!f.startsWith("#!"))return"";let A=f.indexOf(`
`);return A===-1?f:f.slice(0,A)}g.exports=b}}),D3=t2({"src/language-js/pragma.js"(a,g){"use strict";n2();var{parseWithComments:b,strip:f,extract:A,print:L}=P3(),{normalizeEndOfLine:S}=E3(),V=C3();function r(G){let u2=V(G);u2&&(G=G.slice(u2.length+1));let T=A(G),{pragmas:z,comments:w2}=b(T);return{shebang:u2,text:G,pragmas:z,comments:w2}}function X(G){let u2=Object.keys(r(G).pragmas);return u2.includes("prettier")||u2.includes("format")}function Y(G){let{shebang:u2,text:T,pragmas:z,comments:w2}=r(G),C=f(T),J=L({pragmas:Object.assign({format:""},z),comments:w2.trimStart()});return(u2?`${u2}
`:"")+S(J)+(C.startsWith(`
`)?`
`:`

`)+C}g.exports={hasPragma:X,insertPragma:Y}}}),w3=t2({"src/utils/is-non-empty-array.js"(a,g){"use strict";n2();function b(f){return Array.isArray(f)&&f.length>0}g.exports=b}}),L0=t2({"src/language-js/loc.js"(a,g){"use strict";n2();var b=w3();function f(r){var X,Y;let G=r.range?r.range[0]:r.start,u2=(X=(Y=r.declaration)===null||Y===void 0?void 0:Y.decorators)!==null&&X!==void 0?X:r.decorators;return b(u2)?Math.min(f(u2[0]),G):G}function A(r){return r.range?r.range[1]:r.end}function L(r,X){let Y=f(r);return Number.isInteger(Y)&&Y===f(X)}function S(r,X){let Y=A(r);return Number.isInteger(Y)&&Y===A(X)}function V(r,X){return L(r,X)&&S(r,X)}g.exports={locStart:f,locEnd:A,hasSameLocStart:L,hasSameLoc:V}}}),q3=t2({"src/language-js/parse/utils/create-parser.js"(a,g){"use strict";n2();var{hasPragma:b}=D3(),{locStart:f,locEnd:A}=L0();function L(S){return S=typeof S=="function"?{parse:S}:S,Object.assign({astFormat:"estree",hasPragma:b,locStart:f,locEnd:A},S)}g.exports=L}}),B3=t2({"src/language-js/utils/is-ts-keyword-type.js"(a,g){"use strict";n2();function b(f){let{type:A}=f;return A.startsWith("TS")&&A.endsWith("Keyword")}g.exports=b}}),S3=t2({"src/language-js/utils/is-block-comment.js"(a,g){"use strict";n2();var b=new Set(["Block","CommentBlock","MultiLine"]),f=A=>b.has(A==null?void 0:A.type);g.exports=f}}),F3=t2({"src/language-js/utils/is-type-cast-comment.js"(a,g){"use strict";n2();var b=S3();function f(A){return b(A)&&A.value[0]==="*"&&/@(?:type|satisfies)\b/.test(A.value)}g.exports=f}}),L3=t2({"src/utils/get-last.js"(a,g){"use strict";n2();var b=f=>f[f.length-1];g.exports=b}}),O3=t2({"src/language-js/parse/postprocess/visit-node.js"(a,g){"use strict";n2();function b(f,A){if(Array.isArray(f)){for(let L=0;L<f.length;L++)f[L]=b(f[L],A);return f}if(f&&typeof f=="object"&&typeof f.type=="string"){let L=Object.keys(f);for(let S=0;S<L.length;S++)f[L[S]]=b(f[L[S]],A);return A(f)||f}return f}g.exports=b}}),T3=t2({"src/language-js/parse/postprocess/throw-syntax-error.js"(a,g){"use strict";n2();var b=k0();function f(A,L){let{start:S,end:V}=A.loc;throw b(L,{start:{line:S.line,column:S.column+1},end:{line:V.line,column:V.column+1}})}g.exports=f}}),I3=t2({"src/language-js/parse/postprocess/index.js"(a,g){"use strict";n2();var{locStart:b,locEnd:f}=L0(),A=B3(),L=F3(),S=L3(),V=O3(),r=T3();function X(T,z){if(z.parser!=="typescript"&&z.parser!=="flow"&&z.parser!=="acorn"&&z.parser!=="espree"&&z.parser!=="meriyah"){let C=new Set;T=V(T,J=>{J.leadingComments&&J.leadingComments.some(L)&&C.add(b(J))}),T=V(T,J=>{if(J.type==="ParenthesizedExpression"){let{expression:p}=J;if(p.type==="TypeCastExpression")return p.range=J.range,p;let U=b(J);if(!C.has(U))return p.extra=Object.assign(Object.assign({},p.extra),{},{parenthesized:!0}),p}})}return T=V(T,C=>{switch(C.type){case"ChainExpression":return Y(C.expression);case"LogicalExpression":{if(G(C))return u2(C);break}case"VariableDeclaration":{let J=S(C.declarations);J&&J.init&&w2(C,J);break}case"TSParenthesizedType":return A(C.typeAnnotation)||C.typeAnnotation.type==="TSThisType"||(C.typeAnnotation.range=[b(C),f(C)]),C.typeAnnotation;case"TSTypeParameter":if(typeof C.name=="string"){let J=b(C);C.name={type:"Identifier",name:C.name,range:[J,J+C.name.length]}}break;case"ObjectExpression":if(z.parser==="typescript"){let J=C.properties.find(p=>p.type==="Property"&&p.value.type==="TSEmptyBodyFunctionExpression");J&&r(J.value,"Unexpected token.")}break;case"SequenceExpression":{let J=S(C.expressions);C.range=[b(C),Math.min(f(J),f(C))];break}case"TopicReference":z.__isUsingHackPipeline=!0;break;case"ExportAllDeclaration":{let{exported:J}=C;if(z.parser==="meriyah"&&J&&J.type==="Identifier"){let p=z.originalText.slice(b(J),f(J));(p.startsWith('"')||p.startsWith("'"))&&(C.exported=Object.assign(Object.assign({},C.exported),{},{type:"Literal",value:C.exported.name,raw:p}))}break}case"PropertyDefinition":if(z.parser==="meriyah"&&C.static&&!C.computed&&!C.key){let J="static",p=b(C);Object.assign(C,{static:!1,key:{type:"Identifier",name:J,range:[p,p+J.length]}})}break}}),T;function w2(C,J){z.originalText[f(J)]!==";"&&(C.range=[b(C),f(J)])}}function Y(T){switch(T.type){case"CallExpression":T.type="OptionalCallExpression",T.callee=Y(T.callee);break;case"MemberExpression":T.type="OptionalMemberExpression",T.object=Y(T.object);break;case"TSNonNullExpression":T.expression=Y(T.expression);break}return T}function G(T){return T.type==="LogicalExpression"&&T.right.type==="LogicalExpression"&&T.operator===T.right.operator}function u2(T){return G(T)?u2({type:"LogicalExpression",operator:T.operator,left:u2({type:"LogicalExpression",operator:T.operator,left:T.left,right:T.right.left,range:[b(T.left),f(T.right.left)]}),right:T.right.right,range:[b(T),f(T)]}):T}g.exports=X}}),R3=t2({"node_modules/meriyah/dist/meriyah.cjs"(a){"use strict";n2(),Object.defineProperty(a,"__esModule",{value:!0});var g={[0]:"Unexpected token",[28]:"Unexpected token: '%0'",[1]:"Octal escape sequences are not allowed in strict mode",[2]:"Octal escape sequences are not allowed in template strings",[3]:"Unexpected token `#`",[4]:"Illegal Unicode escape sequence",[5]:"Invalid code point %0",[6]:"Invalid hexadecimal escape sequence",[8]:"Octal literals are not allowed in strict mode",[7]:"Decimal integer literals with a leading zero are forbidden in strict mode",[9]:"Expected number in radix %0",[145]:"Invalid left-hand side assignment to a destructible right-hand side",[10]:"Non-number found after exponent indicator",[11]:"Invalid BigIntLiteral",[12]:"No identifiers allowed directly after numeric literal",[13]:"Escapes \\8 or \\9 are not syntactically valid escapes",[14]:"Unterminated string literal",[15]:"Unterminated template literal",[16]:"Multiline comment was not closed properly",[17]:"The identifier contained dynamic unicode escape that was not closed",[18]:"Illegal character '%0'",[19]:"Missing hexadecimal digits",[20]:"Invalid implicit octal",[21]:"Invalid line break in string literal",[22]:"Only unicode escapes are legal in identifier names",[23]:"Expected '%0'",[24]:"Invalid left-hand side in assignment",[25]:"Invalid left-hand side in async arrow",[26]:'Calls to super must be in the "constructor" method of a class expression or class declaration that has a superclass',[27]:"Member access on super must be in a method",[29]:"Await expression not allowed in formal parameter",[30]:"Yield expression not allowed in formal parameter",[92]:"Unexpected token: 'escaped keyword'",[31]:"Unary expressions as the left operand of an exponentiation expression must be disambiguated with parentheses",[119]:"Async functions can only be declared at the top level or inside a block",[32]:"Unterminated regular expression",[33]:"Unexpected regular expression flag",[34]:"Duplicate regular expression flag '%0'",[35]:"%0 functions must have exactly %1 argument%2",[36]:"Setter function argument must not be a rest parameter",[37]:"%0 declaration must have a name in this context",[38]:"Function name may not contain any reserved words or be eval or arguments in strict mode",[39]:"The rest operator is missing an argument",[40]:"A getter cannot be a generator",[41]:"A computed property name must be followed by a colon or paren",[130]:"Object literal keys that are strings or numbers must be a method or have a colon",[43]:"Found `* async x(){}` but this should be `async * x(){}`",[42]:"Getters and setters can not be generators",[44]:"'%0' can not be generator method",[45]:"No line break is allowed after '=>'",[46]:"The left-hand side of the arrow can only be destructed through assignment",[47]:"The binding declaration is not destructible",[48]:"Async arrow can not be followed by new expression",[49]:"Classes may not have a static property named 'prototype'",[50]:"Class constructor may not be a %0",[51]:"Duplicate constructor method in class",[52]:"Invalid increment/decrement operand",[53]:"Invalid use of `new` keyword on an increment/decrement expression",[54]:"`=>` is an invalid assignment target",[55]:"Rest element may not have a trailing comma",[56]:"Missing initializer in %0 declaration",[57]:"'for-%0' loop head declarations can not have an initializer",[58]:"Invalid left-hand side in for-%0 loop: Must have a single binding",[59]:"Invalid shorthand property initializer",[60]:"Property name __proto__ appears more than once in object literal",[61]:"Let is disallowed as a lexically bound name",[62]:"Invalid use of '%0' inside new expression",[63]:"Illegal 'use strict' directive in function with non-simple parameter list",[64]:'Identifier "let" disallowed as left-hand side expression in strict mode',[65]:"Illegal continue statement",[66]:"Illegal break statement",[67]:"Cannot have `let[...]` as a var name in strict mode",[68]:"Invalid destructuring assignment target",[69]:"Rest parameter may not have a default initializer",[70]:"The rest argument must the be last parameter",[71]:"Invalid rest argument",[73]:"In strict mode code, functions can only be declared at top level or inside a block",[74]:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement",[75]:"Without web compatibility enabled functions can not be declared at top level, inside a block, or as the body of an if statement",[76]:"Class declaration can't appear in single-statement context",[77]:"Invalid left-hand side in for-%0",[78]:"Invalid assignment in for-%0",[79]:"for await (... of ...) is only valid in async functions and async generators",[80]:"The first token after the template expression should be a continuation of the template",[82]:"`let` declaration not allowed here and `let` cannot be a regular var name in strict mode",[81]:"`let \n [` is a restricted production at the start of a statement",[83]:"Catch clause requires exactly one parameter, not more (and no trailing comma)",[84]:"Catch clause parameter does not support default values",[85]:"Missing catch or finally after try",[86]:"More than one default clause in switch statement",[87]:"Illegal newline after throw",[88]:"Strict mode code may not include a with statement",[89]:"Illegal return statement",[90]:"The left hand side of the for-header binding declaration is not destructible",[91]:"new.target only allowed within functions",[93]:"'#' not followed by identifier",[99]:"Invalid keyword",[98]:"Can not use 'let' as a class name",[97]:"'A lexical declaration can't define a 'let' binding",[96]:"Can not use `let` as variable name in strict mode",[94]:"'%0' may not be used as an identifier in this context",[95]:"Await is only valid in async functions",[100]:"The %0 keyword can only be used with the module goal",[101]:"Unicode codepoint must not be greater than 0x10FFFF",[102]:"%0 source must be string",[103]:"Only a identifier can be used to indicate alias",[104]:"Only '*' or '{...}' can be imported after default",[105]:"Trailing decorator may be followed by method",[106]:"Decorators can't be used with a constructor",[108]:"HTML comments are only allowed with web compatibility (Annex B)",[109]:"The identifier 'let' must not be in expression position in strict mode",[110]:"Cannot assign to `eval` and `arguments` in strict mode",[111]:"The left-hand side of a for-of loop may not start with 'let'",[112]:"Block body arrows can not be immediately invoked without a group",[113]:"Block body arrows can not be immediately accessed without a group",[114]:"Unexpected strict mode reserved word",[115]:"Unexpected eval or arguments in strict mode",[116]:"Decorators must not be followed by a semicolon",[117]:"Calling delete on expression not allowed in strict mode",[118]:"Pattern can not have a tail",[120]:"Can not have a `yield` expression on the left side of a ternary",[121]:"An arrow function can not have a postfix update operator",[122]:"Invalid object literal key character after generator star",[123]:"Private fields can not be deleted",[125]:"Classes may not have a field called constructor",[124]:"Classes may not have a private element named constructor",[126]:"A class field initializer may not contain arguments",[127]:"Generators can only be declared at the top level or inside a block",[128]:"Async methods are a restricted production and cannot have a newline following it",[129]:"Unexpected character after object literal property name",[131]:"Invalid key token",[132]:"Label '%0' has already been declared",[133]:"continue statement must be nested within an iteration statement",[134]:"Undefined label '%0'",[135]:"Trailing comma is disallowed inside import(...) arguments",[136]:"import() requires exactly one argument",[137]:"Cannot use new with import(...)",[138]:"... is not allowed in import()",[139]:"Expected '=>'",[140]:"Duplicate binding '%0'",[141]:"Cannot export a duplicate name '%0'",[144]:"Duplicate %0 for-binding",[142]:"Exported binding '%0' needs to refer to a top-level declared variable",[143]:"Unexpected private field",[147]:"Numeric separators are not allowed at the end of numeric literals",[146]:"Only one underscore is allowed as numeric separator",[148]:"JSX value should be either an expression or a quoted JSX text",[149]:"Expected corresponding JSX closing tag for %0",[150]:"Adjacent JSX elements must be wrapped in an enclosing tag",[151]:"JSX attributes must only be assigned a non-empty 'expression'",[152]:"'%0' has already been declared",[153]:"'%0' shadowed a catch clause binding",[154]:"Dot property must be an identifier",[155]:"Encountered invalid input after spread/rest argument",[156]:"Catch without try",[157]:"Finally without try",[158]:"Expected corresponding closing tag for JSX fragment",[159]:"Coalescing and logical operators used together in the same expression must be disambiguated with parentheses",[160]:"Invalid tagged template on optional chain",[161]:"Invalid optional chain from super property",[162]:"Invalid optional chain from new expression",[163]:'Cannot use "import.meta" outside a module',[164]:"Leading decorators must be attached to a class declaration"},b=class extends SyntaxError{constructor(e,u,i,n){for(var t=arguments.length,o=new Array(t>4?t-4:0),l=4;l<t;l++)o[l-4]=arguments[l];let c="["+u+":"+i+"]: "+g[n].replace(/%(\d+)/g,(s,m)=>o[m]);super(`${c}`),this.index=e,this.line=u,this.column=i,this.description=c,this.loc={line:u,column:i}}};function f(e,u){for(var i=arguments.length,n=new Array(i>2?i-2:0),t=2;t<i;t++)n[t-2]=arguments[t];throw new b(e.index,e.line,e.column,u,...n)}function A(e){throw new b(e.index,e.line,e.column,e.type,e.params)}function L(e,u,i,n){for(var t=arguments.length,o=new Array(t>4?t-4:0),l=4;l<t;l++)o[l-4]=arguments[l];throw new b(e,u,i,n,...o)}function S(e,u,i,n){throw new b(e,u,i,n)}var V=((e,u)=>{let i=new Uint32Array(104448),n=0,t=0;for(;n<3540;){let o=e[n++];if(o<0)t-=o;else{let l=e[n++];o&2&&(l=u[l]),o&1?i.fill(l,t,t+=e[n++]):i[t++]=l}}return i})([-1,2,24,2,25,2,5,-1,0,77595648,3,44,2,3,0,14,2,57,2,58,3,0,3,0,3168796671,0,4294956992,2,1,2,0,2,59,3,0,4,0,4294966523,3,0,4,2,16,2,60,2,0,0,4294836735,0,3221225471,0,4294901942,2,61,0,134152192,3,0,2,0,4294951935,3,0,2,0,2683305983,0,2684354047,2,17,2,0,0,4294961151,3,0,2,2,19,2,0,0,608174079,2,0,2,131,2,6,2,56,-1,2,37,0,4294443263,2,1,3,0,3,0,4294901711,2,39,0,4089839103,0,2961209759,0,1342439375,0,4294543342,0,3547201023,0,1577204103,0,4194240,0,4294688750,2,2,0,80831,0,4261478351,0,4294549486,2,2,0,2967484831,0,196559,0,3594373100,0,3288319768,0,8469959,2,194,2,3,0,3825204735,0,123747807,0,65487,0,4294828015,0,4092591615,0,1080049119,0,458703,2,3,2,0,0,2163244511,0,4227923919,0,4236247022,2,66,0,4284449919,0,851904,2,4,2,11,0,67076095,-1,2,67,0,1073741743,0,4093591391,-1,0,50331649,0,3265266687,2,32,0,4294844415,0,4278190047,2,18,2,129,-1,3,0,2,2,21,2,0,2,9,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,10,0,261632,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,2088959,2,27,2,8,0,909311,3,0,2,0,814743551,2,41,0,67057664,3,0,2,2,40,2,0,2,28,2,0,2,29,2,7,0,268374015,2,26,2,49,2,0,2,76,0,134153215,-1,2,6,2,0,2,7,0,2684354559,0,67044351,0,3221160064,0,1,-1,3,0,2,2,42,0,1046528,3,0,3,2,8,2,0,2,51,0,4294960127,2,9,2,38,2,10,0,4294377472,2,11,3,0,7,0,4227858431,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-1,2,124,0,1048577,2,82,2,13,-1,2,13,0,131042,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,1046559,2,0,2,14,2,0,0,2147516671,2,20,3,86,2,2,0,-16,2,87,0,524222462,2,4,2,0,0,4269801471,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,2,121,2,0,0,3220242431,3,0,3,2,19,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,2,0,0,4351,2,0,2,8,3,0,2,0,67043391,0,3909091327,2,0,2,22,2,8,2,18,3,0,2,0,67076097,2,7,2,0,2,20,0,67059711,0,4236247039,3,0,2,0,939524103,0,8191999,2,97,2,98,2,15,2,21,3,0,3,0,67057663,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,3774349439,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,2,23,0,1638399,2,172,2,105,3,0,3,2,18,2,24,2,25,2,5,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-3,2,150,-4,2,18,2,0,2,35,0,1,2,0,2,62,2,28,2,11,2,9,2,0,2,110,-1,3,0,4,2,9,2,21,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277137519,0,2269118463,-1,3,18,2,-1,2,32,2,36,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,46,-10,2,0,0,203775,-2,2,18,2,43,2,35,-2,2,17,2,117,2,20,3,0,2,2,36,0,2147549120,2,0,2,11,2,17,2,135,2,0,2,37,2,52,0,5242879,3,0,2,0,402644511,-1,2,120,0,1090519039,-2,2,122,2,38,2,0,0,67045375,2,39,0,4226678271,0,3766565279,0,2039759,-4,3,0,2,0,3288270847,0,3,3,0,2,0,67043519,-5,2,0,0,4282384383,0,1056964609,-1,3,0,2,0,67043345,-1,2,0,2,40,2,41,-1,2,10,2,42,-6,2,0,2,11,-3,3,0,2,0,2147484671,2,125,0,4190109695,2,50,-2,2,126,0,4244635647,0,27,2,0,2,7,2,43,2,0,2,63,-1,2,0,2,40,-8,2,54,2,44,0,67043329,2,127,2,45,0,8388351,-2,2,128,0,3028287487,2,46,2,130,0,33259519,2,41,-9,2,20,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,2,41,-2,2,17,2,49,2,0,2,20,2,50,2,132,2,23,-21,3,0,2,-4,3,0,2,0,4294936575,2,0,0,4294934783,-2,0,196635,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,0,1677656575,-166,0,4161266656,0,4071,0,15360,-4,0,28,-13,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,0,4294954999,2,0,-16,2,0,2,88,2,0,0,2105343,0,4160749584,0,65534,-42,0,4194303871,0,2011,-6,2,0,0,1073684479,0,17407,-11,2,0,2,31,-40,3,0,6,0,8323103,-1,3,0,2,2,42,-37,2,55,2,144,2,145,2,146,2,147,2,148,-105,2,24,-32,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-22381,3,0,7,2,23,-6130,3,5,2,-1,0,69207040,3,44,2,3,0,14,2,57,2,58,-3,0,3168731136,0,4294956864,2,1,2,0,2,59,3,0,4,0,4294966275,3,0,4,2,16,2,60,2,0,2,33,-1,2,17,2,61,-1,2,0,2,56,0,4294885376,3,0,2,0,3145727,0,2617294944,0,4294770688,2,23,2,62,3,0,2,0,131135,2,95,0,70256639,0,71303167,0,272,2,40,2,56,-1,2,37,2,30,-1,2,96,2,63,0,4278255616,0,4294836227,0,4294549473,0,600178175,0,2952806400,0,268632067,0,4294543328,0,57540095,0,1577058304,0,1835008,0,4294688736,2,65,2,64,0,33554435,2,123,2,65,2,151,0,131075,0,3594373096,0,67094296,2,64,-1,0,4294828e3,0,603979263,2,160,0,3,0,4294828001,0,602930687,2,183,0,393219,0,4294828016,0,671088639,0,2154840064,0,4227858435,0,4236247008,2,66,2,36,-1,2,4,0,917503,2,36,-1,2,67,0,537788335,0,4026531935,-1,0,1,-1,2,32,2,68,0,7936,-3,2,0,0,2147485695,0,1010761728,0,4292984930,0,16387,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,11,-1,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,253951,3,19,2,0,122879,2,0,2,8,0,276824064,-2,3,0,2,2,40,2,0,0,4294903295,2,0,2,29,2,7,-1,2,17,2,49,2,0,2,76,2,41,-1,2,20,2,0,2,27,-2,0,128,-2,2,77,2,8,0,4064,-1,2,119,0,4227907585,2,0,2,118,2,0,2,48,2,173,2,9,2,38,2,10,-1,0,74440192,3,0,6,-2,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-3,2,82,2,13,-3,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,817183,2,0,2,14,2,0,0,33023,2,20,3,86,2,-17,2,87,0,524157950,2,4,2,0,2,88,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,0,3072,2,0,0,2147516415,2,9,3,0,2,2,23,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,0,4294965179,0,7,2,0,2,8,2,91,2,8,-1,0,1761345536,2,95,0,4294901823,2,36,2,18,2,96,2,34,2,166,0,2080440287,2,0,2,33,2,143,0,3296722943,2,0,0,1046675455,0,939524101,0,1837055,2,97,2,98,2,15,2,21,3,0,3,0,7,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,2700607615,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,-3,2,105,3,0,3,2,18,-1,3,5,2,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-8,2,18,2,0,2,35,-1,2,0,2,62,2,28,2,29,2,9,2,0,2,110,-1,3,0,4,2,9,2,17,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277075969,2,29,-1,3,18,2,-1,2,32,2,117,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,48,-10,2,0,0,197631,-2,2,18,2,43,2,118,-2,2,17,2,117,2,20,2,119,2,51,-2,2,119,2,23,2,17,2,33,2,119,2,36,0,4294901904,0,4718591,2,119,2,34,0,335544350,-1,2,120,2,121,-2,2,122,2,38,2,7,-1,2,123,2,65,0,3758161920,0,3,-4,2,0,2,27,0,2147485568,0,3,2,0,2,23,0,176,-5,2,0,2,47,2,186,-1,2,0,2,23,2,197,-1,2,0,0,16779263,-2,2,11,-7,2,0,2,121,-3,3,0,2,2,124,2,125,0,2147549183,0,2,-2,2,126,2,35,0,10,0,4294965249,0,67633151,0,4026597376,2,0,0,536871935,-1,2,0,2,40,-8,2,54,2,47,0,1,2,127,2,23,-3,2,128,2,35,2,129,2,130,0,16778239,-10,2,34,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,-3,2,17,2,131,2,0,2,23,2,48,2,132,2,23,-21,3,0,2,-4,3,0,2,0,67583,-1,2,103,-2,0,11,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,2,135,-187,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,2,143,-73,2,0,0,1065361407,0,16384,-11,2,0,2,121,-40,3,0,6,2,117,-1,3,0,2,0,2063,-37,2,55,2,144,2,145,2,146,2,147,2,148,-138,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-28517,2,0,0,1,-1,2,124,2,0,0,8193,-21,2,193,0,10255,0,4,-11,2,64,2,171,-1,0,71680,-1,2,161,0,4292900864,0,805306431,-5,2,150,-1,2,157,-1,0,6144,-2,2,127,-1,2,154,-1,0,2147532800,2,151,2,165,2,0,2,164,0,524032,0,4,-4,2,190,0,205128192,0,1333757536,0,2147483696,0,423953,0,747766272,0,2717763192,0,4286578751,0,278545,2,152,0,4294886464,0,33292336,0,417809,2,152,0,1327482464,0,4278190128,0,700594195,0,1006647527,0,4286497336,0,4160749631,2,153,0,469762560,0,4171219488,0,8323120,2,153,0,202375680,0,3214918176,0,4294508592,2,153,-1,0,983584,0,48,0,58720273,0,3489923072,0,10517376,0,4293066815,0,1,0,2013265920,2,177,2,0,0,2089,0,3221225552,0,201375904,2,0,-2,0,256,0,122880,0,16777216,2,150,0,4160757760,2,0,-6,2,167,-11,0,3263218176,-1,0,49664,0,2160197632,0,8388802,-1,0,12713984,-1,2,154,2,159,2,178,-2,2,162,-20,0,3758096385,-2,2,155,0,4292878336,2,90,2,169,0,4294057984,-2,2,163,2,156,2,175,-2,2,155,-1,2,182,-1,2,170,2,124,0,4026593280,0,14,0,4292919296,-1,2,158,0,939588608,-1,0,805306368,-1,2,124,0,1610612736,2,156,2,157,2,4,2,0,-2,2,158,2,159,-3,0,267386880,-1,2,160,0,7168,-1,0,65024,2,154,2,161,2,179,-7,2,168,-8,2,162,-1,0,1426112704,2,163,-1,2,164,0,271581216,0,2149777408,2,23,2,161,2,124,0,851967,2,180,-1,2,23,2,181,-4,2,158,-20,2,195,2,165,-56,0,3145728,2,185,-4,2,166,2,124,-4,0,32505856,-1,2,167,-1,0,2147385088,2,90,1,2155905152,2,-3,2,103,2,0,2,168,-2,2,169,-6,2,170,0,4026597375,0,1,-1,0,1,-1,2,171,-3,2,117,2,64,-2,2,166,-2,2,176,2,124,-878,2,159,-36,2,172,-1,2,201,-10,2,188,-5,2,174,-6,0,4294965251,2,27,-1,2,173,-1,2,174,-2,0,4227874752,-3,0,2146435072,2,159,-2,0,1006649344,2,124,-1,2,90,0,201375744,-3,0,134217720,2,90,0,4286677377,0,32896,-1,2,158,-3,2,175,-349,2,176,0,1920,2,177,3,0,264,-11,2,157,-2,2,178,2,0,0,520617856,0,2692743168,0,36,-3,0,524284,-11,2,23,-1,2,187,-1,2,184,0,3221291007,2,178,-1,2,202,0,2158720,-3,2,159,0,1,-4,2,124,0,3808625411,0,3489628288,2,200,0,1207959680,0,3221274624,2,0,-3,2,179,0,120,0,7340032,-2,2,180,2,4,2,23,2,163,3,0,4,2,159,-1,2,181,2,177,-1,0,8176,2,182,2,179,2,183,-1,0,4290773232,2,0,-4,2,163,2,189,0,15728640,2,177,-1,2,161,-1,0,4294934512,3,0,4,-9,2,90,2,170,2,184,3,0,4,0,704,0,1849688064,2,185,-1,2,124,0,4294901887,2,0,0,130547712,0,1879048192,2,199,3,0,2,-1,2,186,2,187,-1,0,17829776,0,2025848832,0,4261477888,-2,2,0,-1,0,4286580608,-1,0,29360128,2,192,0,16252928,0,3791388672,2,38,3,0,2,-2,2,196,2,0,-1,2,103,-1,0,66584576,-1,2,191,3,0,9,2,124,-1,0,4294755328,3,0,2,-1,2,161,2,178,3,0,2,2,23,2,188,2,90,-2,0,245760,0,2147418112,-1,2,150,2,203,0,4227923456,-1,2,164,2,161,2,90,-3,0,4292870145,0,262144,2,124,3,0,2,0,1073758848,2,189,-1,0,4227921920,2,190,0,68289024,0,528402016,0,4292927536,3,0,4,-2,0,268435456,2,91,-2,2,191,3,0,5,-1,2,192,2,163,2,0,-2,0,4227923936,2,62,-1,2,155,2,95,2,0,2,154,2,158,3,0,6,-1,2,177,3,0,3,-2,0,2146959360,0,9440640,0,104857600,0,4227923840,3,0,2,0,768,2,193,2,77,-2,2,161,-2,2,119,-1,2,155,3,0,8,0,512,0,8388608,2,194,2,172,2,187,0,4286578944,3,0,2,0,1152,0,1266679808,2,191,0,576,0,4261707776,2,95,3,0,9,2,155,3,0,5,2,16,-1,0,2147221504,-28,2,178,3,0,3,-3,0,4292902912,-6,2,96,3,0,85,-33,0,4294934528,3,0,126,-18,2,195,3,0,269,-17,2,155,2,124,2,198,3,0,2,2,23,0,4290822144,-2,0,67174336,0,520093700,2,17,3,0,21,-2,2,179,3,0,3,-2,0,30720,-1,0,32512,3,0,2,0,4294770656,-191,2,174,-38,2,170,2,0,2,196,3,0,279,-8,2,124,2,0,0,4294508543,0,65295,-11,2,177,3,0,72,-3,0,3758159872,0,201391616,3,0,155,-7,2,170,-1,0,384,-1,0,133693440,-3,2,196,-2,2,26,3,0,4,2,169,-2,2,90,2,155,3,0,4,-2,2,164,-1,2,150,0,335552923,2,197,-1,0,538974272,0,2214592512,0,132e3,-10,0,192,-8,0,12288,-21,0,134213632,0,4294901761,3,0,42,0,100663424,0,4294965284,3,0,6,-1,0,3221282816,2,198,3,0,11,-1,2,199,3,0,40,-6,0,4286578784,2,0,-2,0,1006694400,3,0,24,2,35,-1,2,94,3,0,2,0,1,2,163,3,0,6,2,197,0,4110942569,0,1432950139,0,2701658217,0,4026532864,0,4026532881,2,0,2,45,3,0,8,-1,2,158,-2,2,169,0,98304,0,65537,2,170,-5,0,4294950912,2,0,2,118,0,65528,2,177,0,4294770176,2,26,3,0,4,-30,2,174,0,3758153728,-3,2,169,-2,2,155,2,188,2,158,-1,2,191,-1,2,161,0,4294754304,3,0,2,-3,0,33554432,-2,2,200,-3,2,169,0,4175478784,2,201,0,4286643712,0,4286644216,2,0,-4,2,202,-1,2,165,0,4227923967,3,0,32,-1334,2,163,2,0,-129,2,94,-6,2,163,-180,2,203,-233,2,4,3,0,96,-16,2,163,3,0,47,-154,2,165,3,0,22381,-7,2,17,3,0,6128],[4294967295,4294967291,4092460543,4294828031,4294967294,134217726,268435455,2147483647,1048575,1073741823,3892314111,134217727,1061158911,536805376,4294910143,4160749567,4294901759,4294901760,536870911,262143,8388607,4294902783,4294918143,65535,67043328,2281701374,4294967232,2097151,4294903807,4194303,255,67108863,4294967039,511,524287,131071,127,4292870143,4294902271,4294549487,33554431,1023,67047423,4294901888,4286578687,4294770687,67043583,32767,15,2047999,67043343,16777215,4294902e3,4294934527,4294966783,4294967279,2047,262083,20511,4290772991,41943039,493567,4294959104,603979775,65536,602799615,805044223,4294965206,8191,1031749119,4294917631,2134769663,4286578493,4282253311,4294942719,33540095,4294905855,4294967264,2868854591,1608515583,265232348,534519807,2147614720,1060109444,4093640016,17376,2139062143,224,4169138175,4294909951,4286578688,4294967292,4294965759,2044,4292870144,4294966272,4294967280,8289918,4294934399,4294901775,4294965375,1602223615,4294967259,4294443008,268369920,4292804608,486341884,4294963199,3087007615,1073692671,4128527,4279238655,4294902015,4294966591,2445279231,3670015,3238002687,31,63,4294967288,4294705151,4095,3221208447,4294549472,2147483648,4285526655,4294966527,4294705152,4294966143,64,4294966719,16383,3774873592,458752,536807423,67043839,3758096383,3959414372,3755993023,2080374783,4294835295,4294967103,4160749565,4087,184024726,2862017156,1593309078,268434431,268434414,4294901763,536870912,2952790016,202506752,139264,402653184,4261412864,4227922944,49152,61440,3758096384,117440512,65280,3233808384,3221225472,2097152,4294965248,32768,57152,67108864,4293918720,4290772992,25165824,57344,4227915776,4278190080,4227907584,65520,4026531840,4227858432,4160749568,3758129152,4294836224,63488,1073741824,4294967040,4194304,251658240,196608,4294963200,64512,417808,4227923712,12582912,50331648,65472,4294967168,4294966784,16,4294917120,2080374784,4096,65408,524288,65532]);function r(e){return e.column++,e.currentChar=e.source.charCodeAt(++e.index)}function X(e,u){if((u&64512)!==55296)return 0;let i=e.source.charCodeAt(e.index+1);return(i&64512)!==56320?0:(u=e.currentChar=65536+((u&1023)<<10)+(i&1023),V[(u>>>5)+0]>>>u&31&1||f(e,18,T(u)),e.index++,e.column++,1)}function Y(e,u){e.currentChar=e.source.charCodeAt(++e.index),e.flags|=1,u&4||(e.column=0,e.line++)}function G(e){e.flags|=1,e.currentChar=e.source.charCodeAt(++e.index),e.column=0,e.line++}function u2(e){return e===160||e===65279||e===133||e===5760||e>=8192&&e<=8203||e===8239||e===8287||e===12288||e===8201||e===65519}function T(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(e>>>10)+String.fromCharCode(e&1023)}function z(e){return e<65?e-48:e-65+10&15}function w2(e){switch(e){case 134283266:return"NumericLiteral";case 134283267:return"StringLiteral";case 86021:case 86022:return"BooleanLiteral";case 86023:return"NullLiteral";case 65540:return"RegularExpression";case 67174408:case 67174409:case 132:return"TemplateLiteral";default:return(e&143360)===143360?"Identifier":(e&4096)===4096?"Keyword":"Punctuator"}}var C=[0,0,0,0,0,0,0,0,0,0,1032,0,0,2056,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8192,0,3,0,0,8192,0,0,0,256,0,33024,0,0,242,242,114,114,114,114,114,114,594,594,0,0,16384,0,0,0,0,67,67,67,67,67,67,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,1,0,0,4099,0,71,71,71,71,71,71,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,16384,0,0,0,0],J=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0],p=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0];function U(e){return e<=127?J[e]:V[(e>>>5)+34816]>>>e&31&1}function e2(e){return e<=127?p[e]:V[(e>>>5)+0]>>>e&31&1||e===8204||e===8205}var g2=["SingleLine","MultiLine","HTMLOpen","HTMLClose","HashbangComment"];function l2(e){let u=e.source;e.currentChar===35&&u.charCodeAt(e.index+1)===33&&(r(e),r(e),f2(e,u,0,4,e.tokenPos,e.linePos,e.colPos))}function V2(e,u,i,n,t,o,l,c){return n&2048&&f(e,0),f2(e,u,i,t,o,l,c)}function f2(e,u,i,n,t,o,l){let{index:c}=e;for(e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column;e.index<e.end;){if(C[e.currentChar]&8){let s=e.currentChar===13;G(e),s&&e.index<e.end&&e.currentChar===10&&(e.currentChar=u.charCodeAt(++e.index));break}else if((e.currentChar^8232)<=1){G(e);break}r(e),e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column}if(e.onComment){let s={start:{line:o,column:l},end:{line:e.linePos,column:e.colPos}};e.onComment(g2[n&255],u.slice(c,e.tokenPos),t,e.tokenPos,s)}return i|1}function N2(e,u,i){let{index:n}=e;for(;e.index<e.end;)if(e.currentChar<43){let t=!1;for(;e.currentChar===42;)if(t||(i&=-5,t=!0),r(e)===47){if(r(e),e.onComment){let o={start:{line:e.linePos,column:e.colPos},end:{line:e.line,column:e.column}};e.onComment(g2[1],u.slice(n,e.index-2),n-2,e.index,o)}return e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column,i}if(t)continue;C[e.currentChar]&8?e.currentChar===13?(i|=5,G(e)):(Y(e,i),i=i&-5|1):r(e)}else(e.currentChar^8232)<=1?(i=i&-5|1,G(e)):(i&=-5,r(e));f(e,16)}function q2(e,u){let i=e.index,n=0;e:for(;;){let k=e.currentChar;if(r(e),n&1)n&=-2;else switch(k){case 47:if(n)break;break e;case 92:n|=1;break;case 91:n|=2;break;case 93:n&=1;break;case 13:case 10:case 8232:case 8233:f(e,32)}if(e.index>=e.source.length)return f(e,32)}let t=e.index-1,o=0,l=e.currentChar,{index:c}=e;for(;e2(l);){switch(l){case 103:o&2&&f(e,34,"g"),o|=2;break;case 105:o&1&&f(e,34,"i"),o|=1;break;case 109:o&4&&f(e,34,"m"),o|=4;break;case 117:o&16&&f(e,34,"g"),o|=16;break;case 121:o&8&&f(e,34,"y"),o|=8;break;case 115:o&32&&f(e,34,"s"),o|=32;break;default:f(e,33)}l=r(e)}let s=e.source.slice(c,e.index),m=e.source.slice(i,t);return e.tokenRegExp={pattern:m,flags:s},u&512&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),e.tokenValue=V1(e,m,s),65540}function V1(e,u,i){try{return new RegExp(u,i)}catch{f(e,32)}}function N1(e,u,i){let{index:n}=e,t="",o=r(e),l=e.index;for(;!(C[o]&8);){if(o===i)return t+=e.source.slice(l,e.index),r(e),u&512&&(e.tokenRaw=e.source.slice(n,e.index)),e.tokenValue=t,134283267;if((o&8)===8&&o===92){if(t+=e.source.slice(l,e.index),o=r(e),o<127||o===8232||o===8233){let c=a2(e,u,o);c>=0?t+=T(c):t1(e,c,0)}else t+=T(o);l=e.index+1}e.index>=e.end&&f(e,14),o=r(e)}f(e,14)}function a2(e,u,i){switch(i){case 98:return 8;case 102:return 12;case 114:return 13;case 110:return 10;case 116:return 9;case 118:return 11;case 13:if(e.index<e.end){let n=e.source.charCodeAt(e.index+1);n===10&&(e.index=e.index+1,e.currentChar=n)}case 10:case 8232:case 8233:return e.column=-1,e.line++,-1;case 48:case 49:case 50:case 51:{let n=i-48,t=e.index+1,o=e.column+1;if(t<e.end){let l=e.source.charCodeAt(t);if(C[l]&32){if(u&1024)return-2;if(e.currentChar=l,n=n<<3|l-48,t++,o++,t<e.end){let c=e.source.charCodeAt(t);C[c]&32&&(e.currentChar=c,n=n<<3|c-48,t++,o++)}e.flags|=64,e.index=t-1,e.column=o-1}else if((n!==0||C[l]&512)&&u&1024)return-2}return n}case 52:case 53:case 54:case 55:{if(u&1024)return-2;let n=i-48,t=e.index+1,o=e.column+1;if(t<e.end){let l=e.source.charCodeAt(t);C[l]&32&&(n=n<<3|l-48,e.currentChar=l,e.index=t,e.column=o)}return e.flags|=64,n}case 120:{let n=r(e);if(!(C[n]&64))return-4;let t=z(n),o=r(e);if(!(C[o]&64))return-4;let l=z(o);return t<<4|l}case 117:{let n=r(e);if(e.currentChar===123){let t=0;for(;C[r(e)]&64;)if(t=t<<4|z(e.currentChar),t>1114111)return-5;return e.currentChar<1||e.currentChar!==125?-4:t}else{if(!(C[n]&64))return-4;let t=e.source.charCodeAt(e.index+1);if(!(C[t]&64))return-4;let o=e.source.charCodeAt(e.index+2);if(!(C[o]&64))return-4;let l=e.source.charCodeAt(e.index+3);return C[l]&64?(e.index+=3,e.column+=3,e.currentChar=e.source.charCodeAt(e.index),z(n)<<12|z(t)<<8|z(o)<<4|z(l)):-4}}case 56:case 57:if(!(u&256))return-3;default:return i}}function t1(e,u,i){switch(u){case-1:return;case-2:f(e,i?2:1);case-3:f(e,13);case-4:f(e,6);case-5:f(e,101)}}function We(e,u){let{index:i}=e,n=67174409,t="",o=r(e);for(;o!==96;){if(o===36&&e.source.charCodeAt(e.index+1)===123){r(e),n=67174408;break}else if((o&8)===8&&o===92)if(o=r(e),o>126)t+=T(o);else{let l=a2(e,u|1024,o);if(l>=0)t+=T(l);else if(l!==-1&&u&65536){t=void 0,o=T0(e,o),o<0&&(n=67174408);break}else t1(e,l,1)}else e.index<e.end&&o===13&&e.source.charCodeAt(e.index)===10&&(t+=T(o),e.currentChar=e.source.charCodeAt(++e.index)),((o&83)<3&&o===10||(o^8232)<=1)&&(e.column=-1,e.line++),t+=T(o);e.index>=e.end&&f(e,15),o=r(e)}return r(e),e.tokenValue=t,e.tokenRaw=e.source.slice(i+1,e.index-(n===67174409?1:2)),n}function T0(e,u){for(;u!==96;){switch(u){case 36:{let i=e.index+1;if(i<e.end&&e.source.charCodeAt(i)===123)return e.index=i,e.column++,-u;break}case 10:case 8232:case 8233:e.column=-1,e.line++}e.index>=e.end&&f(e,15),u=r(e)}return u}function I0(e,u){return e.index>=e.end&&f(e,0),e.index--,e.column--,We(e,u)}function Ke(e,u,i){let n=e.currentChar,t=0,o=9,l=i&64?0:1,c=0,s=0;if(i&64)t="."+o1(e,n),n=e.currentChar,n===110&&f(e,11);else{if(n===48)if(n=r(e),(n|32)===120){for(i=136,n=r(e);C[n]&4160;){if(n===95){s||f(e,146),s=0,n=r(e);continue}s=1,t=t*16+z(n),c++,n=r(e)}(c<1||!s)&&f(e,c<1?19:147)}else if((n|32)===111){for(i=132,n=r(e);C[n]&4128;){if(n===95){s||f(e,146),s=0,n=r(e);continue}s=1,t=t*8+(n-48),c++,n=r(e)}(c<1||!s)&&f(e,c<1?0:147)}else if((n|32)===98){for(i=130,n=r(e);C[n]&4224;){if(n===95){s||f(e,146),s=0,n=r(e);continue}s=1,t=t*2+(n-48),c++,n=r(e)}(c<1||!s)&&f(e,c<1?0:147)}else if(C[n]&32)for(u&1024&&f(e,1),i=1;C[n]&16;){if(C[n]&512){i=32,l=0;break}t=t*8+(n-48),n=r(e)}else C[n]&512?(u&1024&&f(e,1),e.flags|=64,i=32):n===95&&f(e,0);if(i&48){if(l){for(;o>=0&&C[n]&4112;){if(n===95){n=r(e),(n===95||i&32)&&S(e.index,e.line,e.index+1,146),s=1;continue}s=0,t=10*t+(n-48),n=r(e),--o}if(s&&S(e.index,e.line,e.index+1,147),o>=0&&!U(n)&&n!==46)return e.tokenValue=t,u&512&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),134283266}t+=o1(e,n),n=e.currentChar,n===46&&(r(e)===95&&f(e,0),i=64,t+="."+o1(e,e.currentChar),n=e.currentChar)}}let m=e.index,k=0;if(n===110&&i&128)k=1,n=r(e);else if((n|32)===101){n=r(e),C[n]&256&&(n=r(e));let{index:h}=e;(C[n]&16)<1&&f(e,10),t+=e.source.substring(m,h)+o1(e,n),n=e.currentChar}return(e.index<e.end&&C[n]&16||U(n))&&f(e,12),k?(e.tokenRaw=e.source.slice(e.tokenPos,e.index),e.tokenValue=BigInt(t),134283389):(e.tokenValue=i&15?t:i&32?parseFloat(e.source.substring(e.tokenPos,e.index)):+t,u&512&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),134283266)}function o1(e,u){let i=0,n=e.index,t="";for(;C[u]&4112;){if(u===95){let{index:o}=e;u=r(e),u===95&&S(e.index,e.line,e.index+1,146),i=1,t+=e.source.substring(n,o),n=e.index;continue}i=0,u=r(e)}return i&&S(e.index,e.line,e.index+1,147),t+e.source.substring(n,e.index)}var Z=["end of source","identifier","number","string","regular expression","false","true","null","template continuation","template tail","=>","(","{",".","...","}",")",";",",","[","]",":","?","'",'"',"</","/>","++","--","=","<<=",">>=",">>>=","**=","+=","-=","*=","/=","%=","^=","|=","&=","||=","&&=","??=","typeof","delete","void","!","~","+","-","in","instanceof","*","%","/","**","&&","||","===","!==","==","!=","<=",">=","<",">","<<",">>",">>>","&","|","^","var","let","const","break","case","catch","class","continue","debugger","default","do","else","export","extends","finally","for","function","if","import","new","return","super","switch","this","throw","try","while","with","implements","interface","package","private","protected","public","static","yield","as","async","await","constructor","get","set","from","of","enum","eval","arguments","escaped keyword","escaped future reserved keyword","reserved if strict","#","BigIntLiteral","??","?.","WhiteSpace","Illegal","LineTerminator","PrivateField","Template","@","target","meta","LineFeed","Escaped","JSXText"],Ye=Object.create(null,{this:{value:86113},function:{value:86106},if:{value:20571},return:{value:20574},var:{value:86090},else:{value:20565},for:{value:20569},new:{value:86109},in:{value:8738868},typeof:{value:16863277},while:{value:20580},case:{value:20558},break:{value:20557},try:{value:20579},catch:{value:20559},delete:{value:16863278},throw:{value:86114},switch:{value:86112},continue:{value:20561},default:{value:20563},instanceof:{value:8476725},do:{value:20564},void:{value:16863279},finally:{value:20568},async:{value:209007},await:{value:209008},class:{value:86096},const:{value:86092},constructor:{value:12401},debugger:{value:20562},export:{value:20566},extends:{value:20567},false:{value:86021},from:{value:12404},get:{value:12402},implements:{value:36966},import:{value:86108},interface:{value:36967},let:{value:241739},null:{value:86023},of:{value:274549},package:{value:36968},private:{value:36969},protected:{value:36970},public:{value:36971},set:{value:12403},static:{value:36972},super:{value:86111},true:{value:86022},with:{value:20581},yield:{value:241773},enum:{value:86134},eval:{value:537079927},as:{value:77934},arguments:{value:537079928},target:{value:143494},meta:{value:143495}});function Ze(e,u,i){for(;p[r(e)];);return e.tokenValue=e.source.slice(e.tokenPos,e.index),e.currentChar!==92&&e.currentChar<126?Ye[e.tokenValue]||208897:j1(e,u,0,i)}function R0(e,u){let i=Qe(e);return e2(i)||f(e,4),e.tokenValue=T(i),j1(e,u,1,C[i]&4)}function j1(e,u,i,n){let t=e.index;for(;e.index<e.end;)if(e.currentChar===92){e.tokenValue+=e.source.slice(t,e.index),i=1;let l=Qe(e);e2(l)||f(e,4),n=n&&C[l]&4,e.tokenValue+=T(l),t=e.index}else if(e2(e.currentChar)||X(e,e.currentChar))r(e);else break;e.index<=e.end&&(e.tokenValue+=e.source.slice(t,e.index));let o=e.tokenValue.length;if(n&&o>=2&&o<=11){let l=Ye[e.tokenValue];return l===void 0?208897:i?u&1024?l===209008&&!(u&4196352)?l:l===36972||(l&36864)===36864?122:121:u&1073741824&&!(u&8192)&&(l&20480)===20480?l:l===241773?u&1073741824?143483:u&2097152?121:l:l===209007&&u&1073741824?143483:(l&36864)===36864||l===209008&&!(u&4194304)?l:121:l}return 208897}function V0(e){return U(r(e))||f(e,93),131}function Qe(e){return e.source.charCodeAt(e.index+1)!==117&&f(e,4),e.currentChar=e.source.charCodeAt(e.index+=2),N0(e)}function N0(e){let u=0,i=e.currentChar;if(i===123){let l=e.index-2;for(;C[r(e)]&64;)u=u<<4|z(e.currentChar),u>1114111&&S(l,e.line,e.index+1,101);return e.currentChar!==125&&S(l,e.line,e.index-1,6),r(e),u}C[i]&64||f(e,6);let n=e.source.charCodeAt(e.index+1);C[n]&64||f(e,6);let t=e.source.charCodeAt(e.index+2);C[t]&64||f(e,6);let o=e.source.charCodeAt(e.index+3);return C[o]&64||f(e,6),u=z(i)<<12|z(n)<<8|z(t)<<4|z(o),e.currentChar=e.source.charCodeAt(e.index+=4),u}var Ge=[129,129,129,129,129,129,129,129,129,128,136,128,128,130,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,128,16842800,134283267,131,208897,8457015,8455751,134283267,67174411,16,8457014,25233970,18,25233971,67108877,8457016,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,21,1074790417,8456258,1077936157,8456259,22,133,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,69271571,137,20,8455497,208897,132,4096,4096,4096,4096,4096,4096,4096,208897,4096,208897,208897,4096,208897,4096,208897,4096,208897,4096,4096,4096,208897,4096,4096,208897,4096,4096,2162700,8455240,1074790415,16842801,129];function E(e,u){if(e.flags=(e.flags|1)^1,e.startPos=e.index,e.startColumn=e.column,e.startLine=e.line,e.token=xe(e,u,0),e.onToken&&e.token!==1048576){let i={start:{line:e.linePos,column:e.colPos},end:{line:e.line,column:e.column}};e.onToken(w2(e.token),e.tokenPos,e.index,i)}}function xe(e,u,i){let n=e.index===0,t=e.source,o=e.index,l=e.line,c=e.column;for(;e.index<e.end;){e.tokenPos=e.index,e.colPos=e.column,e.linePos=e.line;let s=e.currentChar;if(s<=126){let m=Ge[s];switch(m){case 67174411:case 16:case 2162700:case 1074790415:case 69271571:case 20:case 21:case 1074790417:case 18:case 16842801:case 133:case 129:return r(e),m;case 208897:return Ze(e,u,0);case 4096:return Ze(e,u,1);case 134283266:return Ke(e,u,144);case 134283267:return N1(e,u,s);case 132:return We(e,u);case 137:return R0(e,u);case 131:return V0(e);case 128:r(e);break;case 130:i|=5,G(e);break;case 136:Y(e,i),i=i&-5|1;break;case 8456258:let k=r(e);if(e.index<e.end){if(k===60)return e.index<e.end&&r(e)===61?(r(e),4194334):8456516;if(k===61)return r(e),8456256;if(k===33){let d=e.index+1;if(d+1<e.end&&t.charCodeAt(d)===45&&t.charCodeAt(d+1)==45){e.column+=3,e.currentChar=t.charCodeAt(e.index+=3),i=V2(e,t,i,u,2,e.tokenPos,e.linePos,e.colPos),o=e.tokenPos,l=e.linePos,c=e.colPos;continue}return 8456258}if(k===47){if((u&16)<1)return 8456258;let d=e.index+1;if(d<e.end&&(k=t.charCodeAt(d),k===42||k===47))break;return r(e),25}}return 8456258;case 1077936157:{r(e);let d=e.currentChar;return d===61?r(e)===61?(r(e),8455996):8455998:d===62?(r(e),10):1077936157}case 16842800:return r(e)!==61?16842800:r(e)!==61?8455999:(r(e),8455997);case 8457015:return r(e)!==61?8457015:(r(e),4194342);case 8457014:{if(r(e),e.index>=e.end)return 8457014;let d=e.currentChar;return d===61?(r(e),4194340):d!==42?8457014:r(e)!==61?8457273:(r(e),4194337)}case 8455497:return r(e)!==61?8455497:(r(e),4194343);case 25233970:{r(e);let d=e.currentChar;return d===43?(r(e),33619995):d===61?(r(e),4194338):25233970}case 25233971:{r(e);let d=e.currentChar;if(d===45){if(r(e),(i&1||n)&&e.currentChar===62){u&256||f(e,108),r(e),i=V2(e,t,i,u,3,o,l,c),o=e.tokenPos,l=e.linePos,c=e.colPos;continue}return 33619996}return d===61?(r(e),4194339):25233971}case 8457016:{if(r(e),e.index<e.end){let d=e.currentChar;if(d===47){r(e),i=f2(e,t,i,0,e.tokenPos,e.linePos,e.colPos),o=e.tokenPos,l=e.linePos,c=e.colPos;continue}if(d===42){r(e),i=N2(e,t,i),o=e.tokenPos,l=e.linePos,c=e.colPos;continue}if(u&32768)return q2(e,u);if(d===61)return r(e),4259877}return 8457016}case 67108877:let h=r(e);if(h>=48&&h<=57)return Ke(e,u,80);if(h===46){let d=e.index+1;if(d<e.end&&t.charCodeAt(d)===46)return e.column+=2,e.currentChar=t.charCodeAt(e.index+=2),14}return 67108877;case 8455240:{r(e);let d=e.currentChar;return d===124?(r(e),e.currentChar===61?(r(e),4194346):8979003):d===61?(r(e),4194344):8455240}case 8456259:{r(e);let d=e.currentChar;if(d===61)return r(e),8456257;if(d!==62)return 8456259;if(r(e),e.index<e.end){let y=e.currentChar;if(y===62)return r(e)===61?(r(e),4194336):8456518;if(y===61)return r(e),4194335}return 8456517}case 8455751:{r(e);let d=e.currentChar;return d===38?(r(e),e.currentChar===61?(r(e),4194347):8979258):d===61?(r(e),4194345):8455751}case 22:{let d=r(e);if(d===63)return r(e),e.currentChar===61?(r(e),4194348):276889982;if(d===46){let y=e.index+1;if(y<e.end&&(d=t.charCodeAt(y),!(d>=48&&d<=57)))return r(e),67108991}return 22}}}else{if((s^8232)<=1){i=i&-5|1,G(e);continue}if((s&64512)===55296||V[(s>>>5)+34816]>>>s&31&1)return(s&64512)===56320&&(s=(s&1023)<<10|s&1023|65536,V[(s>>>5)+0]>>>s&31&1||f(e,18,T(s)),e.index++,e.currentChar=s),e.column++,e.tokenValue="",j1(e,u,0,0);if(u2(s)){r(e);continue}f(e,18,T(s))}}return 1048576}var j0={AElig:"\xC6",AMP:"&",Aacute:"\xC1",Abreve:"\u0102",Acirc:"\xC2",Acy:"\u0410",Afr:"\u{1D504}",Agrave:"\xC0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2A53",Aogon:"\u0104",Aopf:"\u{1D538}",ApplyFunction:"\u2061",Aring:"\xC5",Ascr:"\u{1D49C}",Assign:"\u2254",Atilde:"\xC3",Auml:"\xC4",Backslash:"\u2216",Barv:"\u2AE7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212C",Beta:"\u0392",Bfr:"\u{1D505}",Bopf:"\u{1D539}",Breve:"\u02D8",Bscr:"\u212C",Bumpeq:"\u224E",CHcy:"\u0427",COPY:"\xA9",Cacute:"\u0106",Cap:"\u22D2",CapitalDifferentialD:"\u2145",Cayleys:"\u212D",Ccaron:"\u010C",Ccedil:"\xC7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010A",Cedilla:"\xB8",CenterDot:"\xB7",Cfr:"\u212D",Chi:"\u03A7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2A74",Congruent:"\u2261",Conint:"\u222F",ContourIntegral:"\u222E",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2A2F",Cscr:"\u{1D49E}",Cup:"\u22D3",CupCap:"\u224D",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040F",Dagger:"\u2021",Darr:"\u21A1",Dashv:"\u2AE4",Dcaron:"\u010E",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\u{1D507}",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",Diamond:"\u22C4",DifferentialD:"\u2146",Dopf:"\u{1D53B}",Dot:"\xA8",DotDot:"\u20DC",DotEqual:"\u2250",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",Downarrow:"\u21D3",Dscr:"\u{1D49F}",Dstrok:"\u0110",ENG:"\u014A",ETH:"\xD0",Eacute:"\xC9",Ecaron:"\u011A",Ecirc:"\xCA",Ecy:"\u042D",Edot:"\u0116",Efr:"\u{1D508}",Egrave:"\xC8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25FB",EmptyVerySmallSquare:"\u25AB",Eogon:"\u0118",Eopf:"\u{1D53C}",Epsilon:"\u0395",Equal:"\u2A75",EqualTilde:"\u2242",Equilibrium:"\u21CC",Escr:"\u2130",Esim:"\u2A73",Eta:"\u0397",Euml:"\xCB",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\u{1D509}",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",Fopf:"\u{1D53D}",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",GT:">",Gamma:"\u0393",Gammad:"\u03DC",Gbreve:"\u011E",Gcedil:"\u0122",Gcirc:"\u011C",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\u{1D50A}",Gg:"\u22D9",Gopf:"\u{1D53E}",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",Gt:"\u226B",HARDcy:"\u042A",Hacek:"\u02C7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210C",HilbertSpace:"\u210B",Hopf:"\u210D",HorizontalLine:"\u2500",Hscr:"\u210B",Hstrok:"\u0126",HumpDownHump:"\u224E",HumpEqual:"\u224F",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacute:"\xCD",Icirc:"\xCE",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrave:"\xCC",Im:"\u2111",Imacr:"\u012A",ImaginaryI:"\u2148",Implies:"\u21D2",Int:"\u222C",Integral:"\u222B",Intersection:"\u22C2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012E",Iopf:"\u{1D540}",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Iuml:"\xCF",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\u{1D50D}",Jopf:"\u{1D541}",Jscr:"\u{1D4A5}",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040C",Kappa:"\u039A",Kcedil:"\u0136",Kcy:"\u041A",Kfr:"\u{1D50E}",Kopf:"\u{1D542}",Kscr:"\u{1D4A6}",LJcy:"\u0409",LT:"<",Lacute:"\u0139",Lambda:"\u039B",Lang:"\u27EA",Laplacetrf:"\u2112",Larr:"\u219E",Lcaron:"\u013D",Lcedil:"\u013B",Lcy:"\u041B",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",LeftRightArrow:"\u2194",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",Leftarrow:"\u21D0",Leftrightarrow:"\u21D4",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2AA1",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",Lfr:"\u{1D50F}",Ll:"\u22D8",Lleftarrow:"\u21DA",Lmidot:"\u013F",LongLeftArrow:"\u27F5",LongLeftRightArrow:"\u27F7",LongRightArrow:"\u27F6",Longleftarrow:"\u27F8",Longleftrightarrow:"\u27FA",Longrightarrow:"\u27F9",Lopf:"\u{1D543}",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21B0",Lstrok:"\u0141",Lt:"\u226A",Map:"\u2905",Mcy:"\u041C",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",MinusPlus:"\u2213",Mopf:"\u{1D544}",Mscr:"\u2133",Mu:"\u039C",NJcy:"\u040A",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041D",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,Nfr:"\u{1D511}",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",Not:"\u2AEC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\u{1D4A9}",Ntilde:"\xD1",Nu:"\u039D",OElig:"\u0152",Oacute:"\xD3",Ocirc:"\xD4",Ocy:"\u041E",Odblac:"\u0150",Ofr:"\u{1D512}",Ograve:"\xD2",Omacr:"\u014C",Omega:"\u03A9",Omicron:"\u039F",Oopf:"\u{1D546}",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oscr:"\u{1D4AA}",Oslash:"\xD8",Otilde:"\xD5",Otimes:"\u2A37",Ouml:"\xD6",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",PartialD:"\u2202",Pcy:"\u041F",Pfr:"\u{1D513}",Phi:"\u03A6",Pi:"\u03A0",PlusMinus:"\xB1",Poincareplane:"\u210C",Popf:"\u2119",Pr:"\u2ABB",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",Prime:"\u2033",Product:"\u220F",Proportion:"\u2237",Proportional:"\u221D",Pscr:"\u{1D4AB}",Psi:"\u03A8",QUOT:'"',Qfr:"\u{1D514}",Qopf:"\u211A",Qscr:"\u{1D4AC}",RBarr:"\u2910",REG:"\xAE",Racute:"\u0154",Rang:"\u27EB",Rarr:"\u21A0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211C",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",Rfr:"\u211C",Rho:"\u03A1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",Rightarrow:"\u21D2",Ropf:"\u211D",RoundImplies:"\u2970",Rrightarrow:"\u21DB",Rscr:"\u211B",Rsh:"\u21B1",RuleDelayed:"\u29F4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042C",Sacute:"\u015A",Sc:"\u2ABC",Scaron:"\u0160",Scedil:"\u015E",Scirc:"\u015C",Scy:"\u0421",Sfr:"\u{1D516}",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03A3",SmallCircle:"\u2218",Sopf:"\u{1D54A}",Sqrt:"\u221A",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\u{1D4AE}",Star:"\u22C6",Sub:"\u22D0",Subset:"\u22D0",SubsetEqual:"\u2286",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",SuchThat:"\u220B",Sum:"\u2211",Sup:"\u22D1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22D1",THORN:"\xDE",TRADE:"\u2122",TSHcy:"\u040B",TScy:"\u0426",Tab:"	",Tau:"\u03A4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\u{1D517}",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\u{1D54B}",TripleDot:"\u20DB",Tscr:"\u{1D4AF}",Tstrok:"\u0166",Uacute:"\xDA",Uarr:"\u219F",Uarrocir:"\u2949",Ubrcy:"\u040E",Ubreve:"\u016C",Ucirc:"\xDB",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\u{1D518}",Ugrave:"\xD9",Umacr:"\u016A",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",Uopf:"\u{1D54C}",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",UpEquilibrium:"\u296E",UpTee:"\u22A5",UpTeeArrow:"\u21A5",Uparrow:"\u21D1",Updownarrow:"\u21D5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",Upsilon:"\u03A5",Uring:"\u016E",Uscr:"\u{1D4B0}",Utilde:"\u0168",Uuml:"\xDC",VDash:"\u22AB",Vbar:"\u2AEB",Vcy:"\u0412",Vdash:"\u22A9",Vdashl:"\u2AE6",Vee:"\u22C1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",Vopf:"\u{1D54D}",Vscr:"\u{1D4B1}",Vvdash:"\u22AA",Wcirc:"\u0174",Wedge:"\u22C0",Wfr:"\u{1D51A}",Wopf:"\u{1D54E}",Wscr:"\u{1D4B2}",Xfr:"\u{1D51B}",Xi:"\u039E",Xopf:"\u{1D54F}",Xscr:"\u{1D4B3}",YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacute:"\xDD",Ycirc:"\u0176",Ycy:"\u042B",Yfr:"\u{1D51C}",Yopf:"\u{1D550}",Yscr:"\u{1D4B4}",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017D",Zcy:"\u0417",Zdot:"\u017B",ZeroWidthSpace:"\u200B",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\u{1D4B5}",aacute:"\xE1",abreve:"\u0103",ac:"\u223E",acE:"\u223E\u0333",acd:"\u223F",acirc:"\xE2",acute:"\xB4",acy:"\u0430",aelig:"\xE6",af:"\u2061",afr:"\u{1D51E}",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03B1",amacr:"\u0101",amalg:"\u2A3F",amp:"&",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",aogon:"\u0105",aopf:"\u{1D552}",ap:"\u2248",apE:"\u2A70",apacir:"\u2A6F",ape:"\u224A",apid:"\u224B",apos:"'",approx:"\u2248",approxeq:"\u224A",aring:"\xE5",ascr:"\u{1D4B6}",ast:"*",asymp:"\u2248",asympeq:"\u224D",atilde:"\xE3",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",bNot:"\u2AED",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",barvee:"\u22BD",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",beta:"\u03B2",beth:"\u2136",between:"\u226C",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bnot:"\u2310",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255D",boxUR:"\u255A",boxUl:"\u255C",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256C",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256B",boxVl:"\u2562",boxVr:"\u255F",boxbox:"\u29C9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250C",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252C",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxuL:"\u255B",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256A",boxvL:"\u2561",boxvR:"\u255E",boxvh:"\u253C",boxvl:"\u2524",boxvr:"\u251C",bprime:"\u2035",breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",bumpeq:"\u224F",cacute:"\u0107",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",ccaps:"\u2A4D",ccaron:"\u010D",ccedil:"\xE7",ccirc:"\u0109",ccups:"\u2A4C",ccupssm:"\u2A50",cdot:"\u010B",cedil:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",cfr:"\u{1D520}",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03C7",cir:"\u25CB",cirE:"\u29C3",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledR:"\xAE",circledS:"\u24C8",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",conint:"\u222E",copf:"\u{1D554}",coprod:"\u2210",copy:"\xA9",copysr:"\u2117",crarr:"\u21B5",cross:"\u2717",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cup:"\u222A",cupbrcap:"\u2A48",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dArr:"\u21D3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",dcaron:"\u010F",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",ddotseq:"\u2A77",deg:"\xB0",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",dfr:"\u{1D521}",dharl:"\u21C3",dharr:"\u21C2",diam:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",dopf:"\u{1D555}",dot:"\u02D9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",dscr:"\u{1D4B9}",dscy:"\u0455",dsol:"\u29F6",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",dzcy:"\u045F",dzigrarr:"\u27FF",eDDot:"\u2A77",eDot:"\u2251",eacute:"\xE9",easter:"\u2A6E",ecaron:"\u011B",ecir:"\u2256",ecirc:"\xEA",ecolon:"\u2255",ecy:"\u044D",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\u{1D522}",eg:"\u2A9A",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014B",ensp:"\u2002",eogon:"\u0119",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",equals:"=",equest:"\u225F",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erDot:"\u2253",erarr:"\u2971",escr:"\u212F",esdot:"\u2250",esim:"\u2242",eta:"\u03B7",eth:"\xF0",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",ffr:"\u{1D523}",filig:"\uFB01",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",fopf:"\u{1D557}",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",gE:"\u2267",gEl:"\u2A8C",gacute:"\u01F5",gamma:"\u03B3",gammad:"\u03DD",gap:"\u2A86",gbreve:"\u011F",gcirc:"\u011D",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",gfr:"\u{1D524}",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2A92",gla:"\u2AA5",glj:"\u2AA4",gnE:"\u2269",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",gopf:"\u{1D558}",grave:"`",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",hArr:"\u21D4",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",hardcy:"\u044A",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",hbar:"\u210F",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",horbar:"\u2015",hscr:"\u{1D4BD}",hslash:"\u210F",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacute:"\xED",ic:"\u2063",icirc:"\xEE",icy:"\u0438",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012B",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",int:"\u222B",intcal:"\u22BA",integers:"\u2124",intercal:"\u22BA",intlarhk:"\u2A17",intprod:"\u2A3C",iocy:"\u0451",iogon:"\u012F",iopf:"\u{1D55A}",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",isin:"\u2208",isinE:"\u22F9",isindot:"\u22F5",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",iuml:"\xEF",jcirc:"\u0135",jcy:"\u0439",jfr:"\u{1D527}",jmath:"\u0237",jopf:"\u{1D55B}",jscr:"\u{1D4BF}",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03BA",kappav:"\u03F0",kcedil:"\u0137",kcy:"\u043A",kfr:"\u{1D528}",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045C",kopf:"\u{1D55C}",kscr:"\u{1D4C0}",lAarr:"\u21DA",lArr:"\u21D0",lAtail:"\u291B",lBarr:"\u290E",lE:"\u2266",lEg:"\u2A8B",lHar:"\u2962",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",lambda:"\u03BB",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",laquo:"\xAB",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",lcaron:"\u013E",lcedil:"\u013C",lceil:"\u2308",lcub:"{",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21A2",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",leftthreetimes:"\u22CB",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297C",lfloor:"\u230A",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",llhard:"\u296B",lltri:"\u25FA",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnE:"\u2268",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",lstrok:"\u0142",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltrPar:"\u2996",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",mDDot:"\u223A",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",mcy:"\u043C",mdash:"\u2014",measuredangle:"\u2221",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",mstpos:"\u223E",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nGg:"\u22D9\u0338",nGt:"\u226B\u20D2",nGtv:"\u226B\u0338",nLeftarrow:"\u21CD",nLeftrightarrow:"\u21CE",nLl:"\u22D8\u0338",nLt:"\u226A\u20D2",nLtv:"\u226A\u0338",nRightarrow:"\u21CF",nVDash:"\u22AF",nVdash:"\u22AE",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",neArr:"\u21D7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",ngsim:"\u2275",ngt:"\u226F",ngtr:"\u226F",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",njcy:"\u045A",nlArr:"\u21CD",nlE:"\u2266\u0338",nlarr:"\u219A",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219A",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nlsim:"\u2274",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nmid:"\u2224",nopf:"\u{1D55F}",not:"\xAC",notin:"\u2209",notinE:"\u22F9\u0338",notindot:"\u22F5\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22AD",nvHarr:"\u2904",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwArr:"\u21D6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24C8",oacute:"\xF3",oast:"\u229B",ocir:"\u229A",ocirc:"\xF4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ofr:"\u{1D52C}",ogon:"\u02DB",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omega:"\u03C9",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",oopf:"\u{1D560}",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",or:"\u2228",orarr:"\u21BB",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oscr:"\u2134",oslash:"\xF8",osol:"\u2298",otilde:"\xF5",otimes:"\u2297",otimesas:"\u2A36",ouml:"\xF6",ovbar:"\u233D",par:"\u2225",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",pfr:"\u{1D52D}",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",pointint:"\u2A15",popf:"\u{1D561}",pound:"\xA3",pr:"\u227A",prE:"\u2AB3",prap:"\u2AB7",prcue:"\u227C",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",prime:"\u2032",primes:"\u2119",prnE:"\u2AB5",prnap:"\u2AB9",prnsim:"\u22E8",prod:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",pscr:"\u{1D4C5}",psi:"\u03C8",puncsp:"\u2008",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",qprime:"\u2057",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',rAarr:"\u21DB",rArr:"\u21D2",rAtail:"\u291C",rBarr:"\u290F",rHar:"\u2964",race:"\u223D\u0331",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",reg:"\xAE",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",rho:"\u03C1",rhov:"\u03F1",rightarrow:"\u2192",rightarrowtail:"\u21A3",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",rightthreetimes:"\u22CC",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",rsaquo:"\u203A",rscr:"\u{1D4C7}",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",ruluhar:"\u2968",rx:"\u211E",sacute:"\u015B",sbquo:"\u201A",sc:"\u227B",scE:"\u2AB4",scap:"\u2AB8",scaron:"\u0161",sccue:"\u227D",sce:"\u2AB0",scedil:"\u015F",scirc:"\u015D",scnE:"\u2AB6",scnap:"\u2ABA",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",seArr:"\u21D8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",shy:"\xAD",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",square:"\u25A1",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",subE:"\u2AC5",subdot:"\u2ABD",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",supE:"\u2AC6",supdot:"\u2ABE",supdsub:"\u2AD8",supe:"\u2287",supedot:"\u2AC4",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swArr:"\u21D9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",target:"\u2316",tau:"\u03C4",tbrk:"\u23B4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",thorn:"\xFE",tilde:"\u02DC",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",tscr:"\u{1D4C9}",tscy:"\u0446",tshcy:"\u045B",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",uArr:"\u21D1",uHar:"\u2963",uacute:"\xFA",uarr:"\u2191",ubrcy:"\u045E",ubreve:"\u016D",ucirc:"\xFB",ucy:"\u0443",udarr:"\u21C5",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",ufr:"\u{1D532}",ugrave:"\xF9",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",umacr:"\u016B",uml:"\xA8",uogon:"\u0173",uopf:"\u{1D566}",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",upsi:"\u03C5",upsih:"\u03D2",upsilon:"\u03C5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",uring:"\u016F",urtri:"\u25F9",uscr:"\u{1D4CA}",utdot:"\u22F0",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",uuml:"\xFC",uwangle:"\u29A7",vArr:"\u21D5",vBar:"\u2AE8",vBarv:"\u2AE9",vDash:"\u22A8",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vcy:"\u0432",vdash:"\u22A2",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",vert:"|",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",vzigzag:"\u299A",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\u{1D534}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",yacute:"\xFD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",yen:"\xA5",yfr:"\u{1D536}",yicy:"\u0457",yopf:"\u{1D56A}",yscr:"\u{1D4CE}",yucy:"\u044E",yuml:"\xFF",zacute:"\u017A",zcaron:"\u017E",zcy:"\u0437",zdot:"\u017C",zeetrf:"\u2128",zeta:"\u03B6",zfr:"\u{1D537}",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"},pe={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376};function _0(e){return e.replace(/&(?:[a-zA-Z]+|#[xX][\da-fA-F]+|#\d+);/g,u=>{if(u.charAt(1)==="#"){let i=u.charAt(2),n=i==="X"||i==="x"?parseInt(u.slice(3),16):parseInt(u.slice(2),10);return M0(n)}return j0[u.slice(1,-1)]||u})}function M0(e){return e>=55296&&e<=57343||e>1114111?"\uFFFD":(e in pe&&(e=pe[e]),String.fromCodePoint(e))}function U0(e,u){return e.startPos=e.tokenPos=e.index,e.startColumn=e.colPos=e.column,e.startLine=e.linePos=e.line,e.token=C[e.currentChar]&8192?J0(e,u):xe(e,u,0),e.token}function J0(e,u){let i=e.currentChar,n=r(e),t=e.index;for(;n!==i;)e.index>=e.end&&f(e,14),n=r(e);return n!==i&&f(e,14),e.tokenValue=e.source.slice(t,e.index),r(e),u&512&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),134283267}function j2(e,u){if(e.startPos=e.tokenPos=e.index,e.startColumn=e.colPos=e.column,e.startLine=e.linePos=e.line,e.index>=e.end)return e.token=1048576;switch(Ge[e.source.charCodeAt(e.index)]){case 8456258:{r(e),e.currentChar===47?(r(e),e.token=25):e.token=8456258;break}case 2162700:{r(e),e.token=2162700;break}default:{let n=0;for(;e.index<e.end;){let o=C[e.source.charCodeAt(e.index)];if(o&1024?(n|=5,G(e)):o&2048?(Y(e,n),n=n&-5|1):r(e),C[e.currentChar]&16384)break}let t=e.source.slice(e.tokenPos,e.index);u&512&&(e.tokenRaw=t),e.tokenValue=_0(t),e.token=138}}return e.token}function _1(e){if((e.token&143360)===143360){let{index:u}=e,i=e.currentChar;for(;C[i]&32770;)i=r(e);e.tokenValue+=e.source.slice(u,e.index)}return e.token=208897,e.token}function s2(e,u,i){!(e.flags&1)&&(e.token&1048576)!==1048576&&!i&&f(e,28,Z[e.token&255]),M(e,u,1074790417)}function eu(e,u,i,n){return u-i<13&&n==="use strict"&&((e.token&1048576)===1048576||e.flags&1)?1:0}function M1(e,u,i){return e.token!==i?0:(E(e,u),1)}function M(e,u,i){return e.token!==i?!1:(E(e,u),!0)}function q(e,u,i){e.token!==i&&f(e,23,Z[i&255]),E(e,u)}function r2(e,u){switch(u.type){case"ArrayExpression":u.type="ArrayPattern";let i=u.elements;for(let t=0,o=i.length;t<o;++t){let l=i[t];l&&r2(e,l)}return;case"ObjectExpression":u.type="ObjectPattern";let n=u.properties;for(let t=0,o=n.length;t<o;++t)r2(e,n[t]);return;case"AssignmentExpression":u.type="AssignmentPattern",u.operator!=="="&&f(e,68),delete u.operator,r2(e,u.left);return;case"Property":r2(e,u.value);return;case"SpreadElement":u.type="RestElement",r2(e,u.argument)}}function l1(e,u,i,n,t){u&1024&&((n&36864)===36864&&f(e,114),!t&&(n&537079808)===537079808&&f(e,115)),(n&20480)===20480&&f(e,99),i&24&&n===241739&&f(e,97),u&4196352&&n===209008&&f(e,95),u&2098176&&n===241773&&f(e,94,"yield")}function uu(e,u,i){u&1024&&((i&36864)===36864&&f(e,114),(i&537079808)===537079808&&f(e,115),i===122&&f(e,92),i===121&&f(e,92)),(i&20480)===20480&&f(e,99),u&4196352&&i===209008&&f(e,95),u&2098176&&i===241773&&f(e,94,"yield")}function iu(e,u,i){return i===209008&&(u&4196352&&f(e,95),e.destructible|=128),i===241773&&u&2097152&&f(e,94,"yield"),(i&20480)===20480||(i&36864)===36864||i==122}function $0(e){return e.property?e.property.type==="PrivateIdentifier":!1}function nu(e,u,i,n){for(;u;){if(u["$"+i])return n&&f(e,133),1;n&&u.loop&&(n=0),u=u.$}return 0}function H0(e,u,i){let n=u;for(;n;)n["$"+i]&&f(e,132,i),n=n.$;u["$"+i]=1}function v(e,u,i,n,t,o){return u&2&&(o.start=i,o.end=e.startPos,o.range=[i,e.startPos]),u&4&&(o.loc={start:{line:n,column:t},end:{line:e.startLine,column:e.startColumn}},e.sourceFile&&(o.loc.source=e.sourceFile)),o}function f1(e){switch(e.type){case"JSXIdentifier":return e.name;case"JSXNamespacedName":return e.namespace+":"+e.name;case"JSXMemberExpression":return f1(e.object)+"."+f1(e.property)}}function c1(e,u,i){let n=i2(_2(),1024);return L2(e,u,n,i,1,0),n}function U1(e,u){let{index:i,line:n,column:t}=e;for(var o=arguments.length,l=new Array(o>2?o-2:0),c=2;c<o;c++)l[c-2]=arguments[c];return{type:u,params:l,index:i,line:n,column:t}}function _2(){return{parent:void 0,type:2}}function i2(e,u){return{parent:e,type:u,scopeError:void 0}}function B2(e,u,i,n,t,o){t&4?tu(e,u,i,n,t):L2(e,u,i,n,t,o),o&64&&M2(e,n)}function L2(e,u,i,n,t,o){let l=i["#"+n];l&&!(l&2)&&(t&1?i.scopeError=U1(e,140,n):u&256&&l&64&&o&2||f(e,140,n)),i.type&128&&i.parent["#"+n]&&!(i.parent["#"+n]&2)&&f(e,140,n),i.type&1024&&l&&!(l&2)&&t&1&&(i.scopeError=U1(e,140,n)),i.type&64&&i.parent["#"+n]&768&&f(e,153,n),i["#"+n]=t}function tu(e,u,i,n,t){let o=i;for(;o&&!(o.type&256);){let l=o["#"+n];l&248&&(u&256&&!(u&1024)&&(t&128&&l&68||l&128&&t&68)||f(e,140,n)),o===i&&l&1&&t&1&&(o.scopeError=U1(e,140,n)),l&768&&(!(l&512)||!(u&256)||u&1024)&&f(e,140,n),o["#"+n]=t,o=o.parent}}function M2(e,u){e.exportedNames!==void 0&&u!==""&&(e.exportedNames["#"+u]&&f(e,141,u),e.exportedNames["#"+u]=1)}function X0(e,u){e.exportedBindings!==void 0&&u!==""&&(e.exportedBindings["#"+u]=1)}function z0(e,u){return function(i,n,t,o,l){let c={type:i,value:n};e&2&&(c.start=t,c.end=o,c.range=[t,o]),e&4&&(c.loc=l),u.push(c)}}function W0(e,u){return function(i,n,t,o){let l={token:i};e&2&&(l.start=n,l.end=t,l.range=[n,t]),e&4&&(l.loc=o),u.push(l)}}function J1(e,u){return e&2098176?e&2048&&u===209008||e&2097152&&u===241773?!1:(u&143360)===143360||(u&12288)===12288:(u&143360)===143360||(u&12288)===12288||(u&36864)===36864}function $1(e,u,i,n){(i&537079808)===537079808&&(u&1024&&f(e,115),n&&(e.flags|=512)),J1(u,i)||f(e,0)}function K0(e,u,i,n){return{source:e,flags:0,index:0,line:1,column:0,startPos:0,end:e.length,tokenPos:0,startColumn:0,colPos:0,linePos:1,startLine:1,sourceFile:u,tokenValue:"",token:1048576,tokenRaw:"",tokenRegExp:void 0,currentChar:e.charCodeAt(0),exportedNames:[],exportedBindings:[],assignable:1,destructible:0,onComment:i,onToken:n,leadingDecorators:[]}}function H1(e,u,i){let n="",t,o;u!=null&&(u.module&&(i|=3072),u.next&&(i|=1),u.loc&&(i|=4),u.ranges&&(i|=2),u.uniqueKeyInPattern&&(i|=-2147483648),u.lexical&&(i|=64),u.webcompat&&(i|=256),u.directives&&(i|=520),u.globalReturn&&(i|=32),u.raw&&(i|=512),u.preserveParens&&(i|=128),u.impliedStrict&&(i|=1024),u.jsx&&(i|=16),u.identifierPattern&&(i|=268435456),u.specDeviation&&(i|=536870912),u.source&&(n=u.source),u.onComment!=null&&(t=Array.isArray(u.onComment)?z0(i,u.onComment):u.onComment),u.onToken!=null&&(o=Array.isArray(u.onToken)?W0(i,u.onToken):u.onToken));let l=K0(e,n,t,o);i&1&&l2(l);let c=i&64?_2():void 0,s=[],m="script";if(i&2048){if(m="module",s=Z0(l,i|8192,c),c)for(let h in l.exportedBindings)h[0]==="#"&&!c[h]&&f(l,142,h.slice(1))}else s=Y0(l,i|8192,c);let k={type:"Program",sourceType:m,body:s};return i&2&&(k.start=0,k.end=e.length,k.range=[0,e.length]),i&4&&(k.loc={start:{line:1,column:0},end:{line:l.line,column:l.column}},l.sourceFile&&(k.loc.source=n)),k}function Y0(e,u,i){E(e,u|32768|1073741824);let n=[];for(;e.token===134283267;){let{index:t,tokenPos:o,tokenValue:l,linePos:c,colPos:s,token:m}=e,k=c2(e,u);eu(e,t,o,l)&&(u|=1024),n.push(z1(e,u,k,m,o,c,s))}for(;e.token!==1048576;)n.push(G2(e,u,i,4,{}));return n}function Z0(e,u,i){E(e,u|32768);let n=[];if(u&8)for(;e.token===134283267;){let{tokenPos:t,linePos:o,colPos:l,token:c}=e;n.push(z1(e,u,c2(e,u),c,t,o,l))}for(;e.token!==1048576;)n.push(Q0(e,u,i));return n}function Q0(e,u,i){e.leadingDecorators=k1(e,u);let n;switch(e.token){case 20566:n=bt(e,u,i);break;case 86108:n=ht(e,u,i);break;default:n=G2(e,u,i,4,{})}return e.leadingDecorators.length&&f(e,164),n}function G2(e,u,i,n,t){let o=e.tokenPos,l=e.linePos,c=e.colPos;switch(e.token){case 86106:return I2(e,u,i,n,1,0,0,o,l,c);case 133:case 86096:return x1(e,u,i,0,o,l,c);case 86092:return W1(e,u,i,16,0,o,l,c);case 241739:return dt(e,u,i,n,o,l,c);case 20566:f(e,100,"export");case 86108:switch(E(e,u),e.token){case 67174411:return hu(e,u,o,l,c);case 67108877:return gu(e,u,o,l,c);default:f(e,100,"import")}case 209007:return ou(e,u,i,n,t,1,o,l,c);default:return x2(e,u,i,n,t,1,o,l,c)}}function x2(e,u,i,n,t,o,l,c,s){switch(e.token){case 86090:return fu(e,u,i,0,l,c,s);case 20574:return x0(e,u,l,c,s);case 20571:return ut(e,u,i,t,l,c,s);case 20569:return gt(e,u,i,t,l,c,s);case 20564:return at(e,u,i,t,l,c,s);case 20580:return nt(e,u,i,t,l,c,s);case 86112:return it(e,u,i,t,l,c,s);case 1074790417:return p0(e,u,l,c,s);case 2162700:return s1(e,u,i&&i2(i,2),t,l,c,s);case 86114:return et(e,u,l,c,s);case 20557:return ot(e,u,t,l,c,s);case 20561:return tt(e,u,t,l,c,s);case 20579:return ct(e,u,i,t,l,c,s);case 20581:return lt(e,u,i,t,l,c,s);case 20562:return ft(e,u,l,c,s);case 209007:return ou(e,u,i,n,t,0,l,c,s);case 20559:f(e,156);case 20568:f(e,157);case 86106:f(e,u&1024?73:(u&256)<1?75:74);case 86096:f(e,76);default:return G0(e,u,i,n,t,o,l,c,s)}}function G0(e,u,i,n,t,o,l,c,s){let{tokenValue:m,token:k}=e,h;switch(k){case 241739:h=$(e,u,0),u&1024&&f(e,82),e.token===69271571&&f(e,81);break;default:h=d2(e,u,2,0,1,0,0,1,e.tokenPos,e.linePos,e.colPos)}return k&143360&&e.token===21?X1(e,u,i,n,t,m,h,k,o,l,c,s):(h=H(e,u,h,0,0,l,c,s),h=Q(e,u,0,0,l,c,s,h),e.token===18&&(h=O2(e,u,0,l,c,s,h)),X2(e,u,h,l,c,s))}function s1(e,u,i,n,t,o,l){let c=[];for(q(e,u|32768,2162700);e.token!==1074790415;)c.push(G2(e,u,i,2,{$:n}));return q(e,u|32768,1074790415),v(e,u,t,o,l,{type:"BlockStatement",body:c})}function x0(e,u,i,n,t){(u&32)<1&&u&8192&&f(e,89),E(e,u|32768);let o=e.flags&1||e.token&1048576?null:o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);return s2(e,u|32768),v(e,u,i,n,t,{type:"ReturnStatement",argument:o})}function X2(e,u,i,n,t,o){return s2(e,u|32768),v(e,u,n,t,o,{type:"ExpressionStatement",expression:i})}function X1(e,u,i,n,t,o,l,c,s,m,k,h){l1(e,u,0,c,1),H0(e,t,o),E(e,u|32768);let d=s&&(u&1024)<1&&u&256&&e.token===86106?I2(e,u,i2(i,2),n,0,0,0,e.tokenPos,e.linePos,e.colPos):x2(e,u,i,n,t,s,e.tokenPos,e.linePos,e.colPos);return v(e,u,m,k,h,{type:"LabeledStatement",label:l,body:d})}function ou(e,u,i,n,t,o,l,c,s){let{token:m,tokenValue:k}=e,h=$(e,u,0);if(e.token===21)return X1(e,u,i,n,t,k,h,m,1,l,c,s);let d=e.flags&1;if(!d){if(e.token===86106)return o||f(e,119),I2(e,u,i,n,1,0,1,l,c,s);if((e.token&143360)===143360)return h=Pu(e,u,1,l,c,s),e.token===18&&(h=O2(e,u,0,l,c,s,h)),X2(e,u,h,l,c,s)}return e.token===67174411?h=G1(e,u,h,1,1,0,d,l,c,s):(e.token===10&&($1(e,u,m,1),h=h1(e,u,e.tokenValue,h,0,1,0,l,c,s)),e.assignable=1),h=H(e,u,h,0,0,l,c,s),e.token===18&&(h=O2(e,u,0,l,c,s,h)),h=Q(e,u,0,0,l,c,s,h),e.assignable=1,X2(e,u,h,l,c,s)}function z1(e,u,i,n,t,o,l){return n!==1074790417&&(e.assignable=2,i=H(e,u,i,0,0,t,o,l),e.token!==1074790417&&(i=Q(e,u,0,0,t,o,l,i),e.token===18&&(i=O2(e,u,0,t,o,l,i))),s2(e,u|32768)),u&8&&i.type==="Literal"&&typeof i.value=="string"?v(e,u,t,o,l,{type:"ExpressionStatement",expression:i,directive:i.raw.slice(1,-1)}):v(e,u,t,o,l,{type:"ExpressionStatement",expression:i})}function p0(e,u,i,n,t){return E(e,u|32768),v(e,u,i,n,t,{type:"EmptyStatement"})}function et(e,u,i,n,t){E(e,u|32768),e.flags&1&&f(e,87);let o=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);return s2(e,u|32768),v(e,u,i,n,t,{type:"ThrowStatement",argument:o})}function ut(e,u,i,n,t,o,l){E(e,u),q(e,u|32768,67174411),e.assignable=1;let c=o2(e,u,0,1,e.tokenPos,e.line,e.colPos);q(e,u|32768,16);let s=lu(e,u,i,n,e.tokenPos,e.linePos,e.colPos),m=null;return e.token===20565&&(E(e,u|32768),m=lu(e,u,i,n,e.tokenPos,e.linePos,e.colPos)),v(e,u,t,o,l,{type:"IfStatement",test:c,consequent:s,alternate:m})}function lu(e,u,i,n,t,o,l){return u&1024||(u&256)<1||e.token!==86106?x2(e,u,i,0,{$:n},0,e.tokenPos,e.linePos,e.colPos):I2(e,u,i2(i,2),0,0,0,0,t,o,l)}function it(e,u,i,n,t,o,l){E(e,u),q(e,u|32768,67174411);let c=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);q(e,u,16),q(e,u,2162700);let s=[],m=0;for(i&&(i=i2(i,8));e.token!==1074790415;){let{tokenPos:k,linePos:h,colPos:d}=e,y=null,w=[];for(M(e,u|32768,20558)?y=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos):(q(e,u|32768,20563),m&&f(e,86),m=1),q(e,u|32768,21);e.token!==20558&&e.token!==1074790415&&e.token!==20563;)w.push(G2(e,u|4096,i,2,{$:n}));s.push(v(e,u,k,h,d,{type:"SwitchCase",test:y,consequent:w}))}return q(e,u|32768,1074790415),v(e,u,t,o,l,{type:"SwitchStatement",discriminant:c,cases:s})}function nt(e,u,i,n,t,o,l){E(e,u),q(e,u|32768,67174411);let c=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);q(e,u|32768,16);let s=p2(e,u,i,n);return v(e,u,t,o,l,{type:"WhileStatement",test:c,body:s})}function p2(e,u,i,n){return x2(e,(u|134217728)^134217728|131072,i,0,{loop:1,$:n},0,e.tokenPos,e.linePos,e.colPos)}function tt(e,u,i,n,t,o){(u&131072)<1&&f(e,65),E(e,u);let l=null;if((e.flags&1)<1&&e.token&143360){let{tokenValue:c}=e;l=$(e,u|32768,0),nu(e,i,c,1)||f(e,134,c)}return s2(e,u|32768),v(e,u,n,t,o,{type:"ContinueStatement",label:l})}function ot(e,u,i,n,t,o){E(e,u|32768);let l=null;if((e.flags&1)<1&&e.token&143360){let{tokenValue:c}=e;l=$(e,u|32768,0),nu(e,i,c,0)||f(e,134,c)}else(u&135168)<1&&f(e,66);return s2(e,u|32768),v(e,u,n,t,o,{type:"BreakStatement",label:l})}function lt(e,u,i,n,t,o,l){E(e,u),u&1024&&f(e,88),q(e,u|32768,67174411);let c=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);q(e,u|32768,16);let s=x2(e,u,i,2,n,0,e.tokenPos,e.linePos,e.colPos);return v(e,u,t,o,l,{type:"WithStatement",object:c,body:s})}function ft(e,u,i,n,t){return E(e,u|32768),s2(e,u|32768),v(e,u,i,n,t,{type:"DebuggerStatement"})}function ct(e,u,i,n,t,o,l){E(e,u|32768);let c=i?i2(i,32):void 0,s=s1(e,u,c,{$:n},e.tokenPos,e.linePos,e.colPos),{tokenPos:m,linePos:k,colPos:h}=e,d=M(e,u|32768,20559)?st(e,u,i,n,m,k,h):null,y=null;if(e.token===20568){E(e,u|32768);let w=c?i2(i,4):void 0;y=s1(e,u,w,{$:n},e.tokenPos,e.linePos,e.colPos)}return!d&&!y&&f(e,85),v(e,u,t,o,l,{type:"TryStatement",block:s,handler:d,finalizer:y})}function st(e,u,i,n,t,o,l){let c=null,s=i;M(e,u,67174411)&&(i&&(i=i2(i,4)),c=Du(e,u,i,(e.token&2097152)===2097152?256:512,0,e.tokenPos,e.linePos,e.colPos),e.token===18?f(e,83):e.token===1077936157&&f(e,84),q(e,u|32768,16),i&&(s=i2(i,64)));let m=s1(e,u,s,{$:n},e.tokenPos,e.linePos,e.colPos);return v(e,u,t,o,l,{type:"CatchClause",param:c,body:m})}function at(e,u,i,n,t,o,l){E(e,u|32768);let c=p2(e,u,i,n);q(e,u,20580),q(e,u|32768,67174411);let s=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos);return q(e,u|32768,16),M(e,u,1074790417),v(e,u,t,o,l,{type:"DoWhileStatement",body:c,test:s})}function dt(e,u,i,n,t,o,l){let{token:c,tokenValue:s}=e,m=$(e,u,0);if(e.token&2240512){let k=z2(e,u,i,8,0);return s2(e,u|32768),v(e,u,t,o,l,{type:"VariableDeclaration",kind:"let",declarations:k})}if(e.assignable=1,u&1024&&f(e,82),e.token===21)return X1(e,u,i,n,{},s,m,c,0,t,o,l);if(e.token===10){let k;u&64&&(k=c1(e,u,s)),e.flags=(e.flags|128)^128,m=e1(e,u,k,[m],0,t,o,l)}else m=H(e,u,m,0,0,t,o,l),m=Q(e,u,0,0,t,o,l,m);return e.token===18&&(m=O2(e,u,0,t,o,l,m)),X2(e,u,m,t,o,l)}function W1(e,u,i,n,t,o,l,c){E(e,u);let s=z2(e,u,i,n,t);return s2(e,u|32768),v(e,u,o,l,c,{type:"VariableDeclaration",kind:n&8?"let":"const",declarations:s})}function fu(e,u,i,n,t,o,l){E(e,u);let c=z2(e,u,i,4,n);return s2(e,u|32768),v(e,u,t,o,l,{type:"VariableDeclaration",kind:"var",declarations:c})}function z2(e,u,i,n,t){let o=1,l=[cu(e,u,i,n,t)];for(;M(e,u,18);)o++,l.push(cu(e,u,i,n,t));return o>1&&t&32&&e.token&262144&&f(e,58,Z[e.token&255]),l}function cu(e,u,i,n,t){let{token:o,tokenPos:l,linePos:c,colPos:s}=e,m=null,k=Du(e,u,i,n,t,l,c,s);return e.token===1077936157?(E(e,u|32768),m=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),(t&32||(o&2097152)<1)&&(e.token===274549||e.token===8738868&&(o&2097152||(n&4)<1||u&1024))&&L(l,e.line,e.index-3,57,e.token===274549?"of":"in")):(n&16||(o&2097152)>0)&&(e.token&262144)!==262144&&f(e,56,n&16?"const":"destructuring"),v(e,u,l,c,s,{type:"VariableDeclarator",id:k,init:m})}function gt(e,u,i,n,t,o,l){E(e,u);let c=(u&4194304)>0&&M(e,u,209008);q(e,u|32768,67174411),i&&(i=i2(i,1));let s=null,m=null,k=0,h=null,d=e.token===86090||e.token===241739||e.token===86092,y,{token:w,tokenPos:D,linePos:F,colPos:O}=e;if(d?w===241739?(h=$(e,u,0),e.token&2240512?(e.token===8738868?u&1024&&f(e,64):h=v(e,u,D,F,O,{type:"VariableDeclaration",kind:"let",declarations:z2(e,u|134217728,i,8,32)}),e.assignable=1):u&1024?f(e,64):(d=!1,e.assignable=1,h=H(e,u,h,0,0,D,F,O),e.token===274549&&f(e,111))):(E(e,u),h=v(e,u,D,F,O,w===86090?{type:"VariableDeclaration",kind:"var",declarations:z2(e,u|134217728,i,4,32)}:{type:"VariableDeclaration",kind:"const",declarations:z2(e,u|134217728,i,16,32)}),e.assignable=1):w===1074790417?c&&f(e,79):(w&2097152)===2097152?(h=w===2162700?b2(e,u,void 0,1,0,0,2,32,D,F,O):m2(e,u,void 0,1,0,0,2,32,D,F,O),k=e.destructible,u&256&&k&64&&f(e,60),e.assignable=k&16?2:1,h=H(e,u|134217728,h,0,0,e.tokenPos,e.linePos,e.colPos)):h=h2(e,u|134217728,1,0,1,D,F,O),(e.token&262144)===262144){if(e.token===274549){e.assignable&2&&f(e,77,c?"await":"of"),r2(e,h),E(e,u|32768),y=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),q(e,u|32768,16);let I=p2(e,u,i,n);return v(e,u,t,o,l,{type:"ForOfStatement",left:h,right:y,body:I,await:c})}e.assignable&2&&f(e,77,"in"),r2(e,h),E(e,u|32768),c&&f(e,79),y=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos),q(e,u|32768,16);let x=p2(e,u,i,n);return v(e,u,t,o,l,{type:"ForInStatement",body:x,left:h,right:y})}c&&f(e,79),d||(k&8&&e.token!==1077936157&&f(e,77,"loop"),h=Q(e,u|134217728,0,0,D,F,O,h)),e.token===18&&(h=O2(e,u,0,e.tokenPos,e.linePos,e.colPos,h)),q(e,u|32768,1074790417),e.token!==1074790417&&(s=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos)),q(e,u|32768,1074790417),e.token!==16&&(m=o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos)),q(e,u|32768,16);let N=p2(e,u,i,n);return v(e,u,t,o,l,{type:"ForStatement",init:h,test:s,update:m,body:N})}function su(e,u,i){return J1(u,e.token)||f(e,114),(e.token&537079808)===537079808&&f(e,115),i&&L2(e,u,i,e.tokenValue,8,0),$(e,u,0)}function ht(e,u,i){let n=e.tokenPos,t=e.linePos,o=e.colPos;E(e,u);let l=null,{tokenPos:c,linePos:s,colPos:m}=e,k=[];if(e.token===134283267)l=c2(e,u);else{if(e.token&143360){let h=su(e,u,i);if(k=[v(e,u,c,s,m,{type:"ImportDefaultSpecifier",local:h})],M(e,u,18))switch(e.token){case 8457014:k.push(au(e,u,i));break;case 2162700:du(e,u,i,k);break;default:f(e,104)}}else switch(e.token){case 8457014:k=[au(e,u,i)];break;case 2162700:du(e,u,i,k);break;case 67174411:return hu(e,u,n,t,o);case 67108877:return gu(e,u,n,t,o);default:f(e,28,Z[e.token&255])}l=mt(e,u)}return s2(e,u|32768),v(e,u,n,t,o,{type:"ImportDeclaration",specifiers:k,source:l})}function au(e,u,i){let{tokenPos:n,linePos:t,colPos:o}=e;return E(e,u),q(e,u,77934),(e.token&134217728)===134217728&&L(n,e.line,e.index,28,Z[e.token&255]),v(e,u,n,t,o,{type:"ImportNamespaceSpecifier",local:su(e,u,i)})}function mt(e,u){return M(e,u,12404),e.token!==134283267&&f(e,102,"Import"),c2(e,u)}function du(e,u,i,n){for(E(e,u);e.token&143360;){let{token:t,tokenValue:o,tokenPos:l,linePos:c,colPos:s}=e,m=$(e,u,0),k;M(e,u,77934)?((e.token&134217728)===134217728||e.token===18?f(e,103):l1(e,u,16,e.token,0),o=e.tokenValue,k=$(e,u,0)):(l1(e,u,16,t,0),k=m),i&&L2(e,u,i,o,8,0),n.push(v(e,u,l,c,s,{type:"ImportSpecifier",local:k,imported:m})),e.token!==1074790415&&q(e,u,18)}return q(e,u,1074790415),n}function gu(e,u,i,n,t){let o=bu(e,u,v(e,u,i,n,t,{type:"Identifier",name:"import"}),i,n,t);return o=H(e,u,o,0,0,i,n,t),o=Q(e,u,0,0,i,n,t,o),X2(e,u,o,i,n,t)}function hu(e,u,i,n,t){let o=ku(e,u,0,i,n,t);return o=H(e,u,o,0,0,i,n,t),X2(e,u,o,i,n,t)}function bt(e,u,i){let n=e.tokenPos,t=e.linePos,o=e.colPos;E(e,u|32768);let l=[],c=null,s=null,m;if(M(e,u|32768,20563)){switch(e.token){case 86106:{c=I2(e,u,i,4,1,1,0,e.tokenPos,e.linePos,e.colPos);break}case 133:case 86096:c=x1(e,u,i,1,e.tokenPos,e.linePos,e.colPos);break;case 209007:let{tokenPos:k,linePos:h,colPos:d}=e;c=$(e,u,0);let{flags:y}=e;(y&1)<1&&(e.token===86106?c=I2(e,u,i,4,1,1,1,k,h,d):e.token===67174411?(c=G1(e,u,c,1,1,0,y,k,h,d),c=H(e,u,c,0,0,k,h,d),c=Q(e,u,0,0,k,h,d,c)):e.token&143360&&(i&&(i=c1(e,u,e.tokenValue)),c=$(e,u,0),c=e1(e,u,i,[c],1,k,h,d)));break;default:c=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),s2(e,u|32768)}return i&&M2(e,"default"),v(e,u,n,t,o,{type:"ExportDefaultDeclaration",declaration:c})}switch(e.token){case 8457014:{E(e,u);let y=null;return M(e,u,77934)&&(i&&M2(e,e.tokenValue),y=$(e,u,0)),q(e,u,12404),e.token!==134283267&&f(e,102,"Export"),s=c2(e,u),s2(e,u|32768),v(e,u,n,t,o,{type:"ExportAllDeclaration",source:s,exported:y})}case 2162700:{E(e,u);let y=[],w=[];for(;e.token&143360;){let{tokenPos:D,tokenValue:F,linePos:O,colPos:N}=e,x=$(e,u,0),I;e.token===77934?(E(e,u),(e.token&134217728)===134217728&&f(e,103),i&&(y.push(e.tokenValue),w.push(F)),I=$(e,u,0)):(i&&(y.push(e.tokenValue),w.push(e.tokenValue)),I=x),l.push(v(e,u,D,O,N,{type:"ExportSpecifier",local:x,exported:I})),e.token!==1074790415&&q(e,u,18)}if(q(e,u,1074790415),M(e,u,12404))e.token!==134283267&&f(e,102,"Export"),s=c2(e,u);else if(i){let D=0,F=y.length;for(;D<F;D++)M2(e,y[D]);for(D=0,F=w.length;D<F;D++)X0(e,w[D])}s2(e,u|32768);break}case 86096:c=x1(e,u,i,2,e.tokenPos,e.linePos,e.colPos);break;case 86106:c=I2(e,u,i,4,1,2,0,e.tokenPos,e.linePos,e.colPos);break;case 241739:c=W1(e,u,i,8,64,e.tokenPos,e.linePos,e.colPos);break;case 86092:c=W1(e,u,i,16,64,e.tokenPos,e.linePos,e.colPos);break;case 86090:c=fu(e,u,i,64,e.tokenPos,e.linePos,e.colPos);break;case 209007:let{tokenPos:k,linePos:h,colPos:d}=e;if(E(e,u),(e.flags&1)<1&&e.token===86106){c=I2(e,u,i,4,1,2,1,k,h,d),i&&(m=c.id?c.id.name:"",M2(e,m));break}default:f(e,28,Z[e.token&255])}return v(e,u,n,t,o,{type:"ExportNamedDeclaration",declaration:c,specifiers:l,source:s})}function K(e,u,i,n,t,o,l,c){let s=d2(e,u,2,0,i,n,t,1,o,l,c);return s=H(e,u,s,t,0,o,l,c),Q(e,u,t,0,o,l,c,s)}function O2(e,u,i,n,t,o,l){let c=[l];for(;M(e,u|32768,18);)c.push(K(e,u,1,0,i,e.tokenPos,e.linePos,e.colPos));return v(e,u,n,t,o,{type:"SequenceExpression",expressions:c})}function o2(e,u,i,n,t,o,l){let c=K(e,u,n,0,i,t,o,l);return e.token===18?O2(e,u,i,t,o,l,c):c}function Q(e,u,i,n,t,o,l,c){let{token:s}=e;if((s&4194304)===4194304){e.assignable&2&&f(e,24),(!n&&s===1077936157&&c.type==="ArrayExpression"||c.type==="ObjectExpression")&&r2(e,c),E(e,u|32768);let m=K(e,u,1,1,i,e.tokenPos,e.linePos,e.colPos);return e.assignable=2,v(e,u,t,o,l,n?{type:"AssignmentPattern",left:c,right:m}:{type:"AssignmentExpression",left:c,operator:Z[s&255],right:m})}return(s&8454144)===8454144&&(c=T2(e,u,i,t,o,l,4,s,c)),M(e,u|32768,22)&&(c=U2(e,u,c,t,o,l)),c}function a1(e,u,i,n,t,o,l,c){let{token:s}=e;E(e,u|32768);let m=K(e,u,1,1,i,e.tokenPos,e.linePos,e.colPos);return c=v(e,u,t,o,l,n?{type:"AssignmentPattern",left:c,right:m}:{type:"AssignmentExpression",left:c,operator:Z[s&255],right:m}),e.assignable=2,c}function U2(e,u,i,n,t,o){let l=K(e,(u|134217728)^134217728,1,0,0,e.tokenPos,e.linePos,e.colPos);q(e,u|32768,21),e.assignable=1;let c=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return e.assignable=2,v(e,u,n,t,o,{type:"ConditionalExpression",test:i,consequent:l,alternate:c})}function T2(e,u,i,n,t,o,l,c,s){let m=-((u&134217728)>0)&8738868,k,h;for(e.assignable=2;e.token&8454144&&(k=e.token,h=k&3840,(k&524288&&c&268435456||c&524288&&k&268435456)&&f(e,159),!(h+((k===8457273)<<8)-((m===k)<<12)<=l));)E(e,u|32768),s=v(e,u,n,t,o,{type:k&524288||k&268435456?"LogicalExpression":"BinaryExpression",left:s,right:T2(e,u,i,e.tokenPos,e.linePos,e.colPos,h,k,h2(e,u,0,i,1,e.tokenPos,e.linePos,e.colPos)),operator:Z[k&255]});return e.token===1077936157&&f(e,24),s}function kt(e,u,i,n,t,o,l){i||f(e,0);let c=e.token;E(e,u|32768);let s=h2(e,u,0,l,1,e.tokenPos,e.linePos,e.colPos);return e.token===8457273&&f(e,31),u&1024&&c===16863278&&(s.type==="Identifier"?f(e,117):$0(s)&&f(e,123)),e.assignable=2,v(e,u,n,t,o,{type:"UnaryExpression",operator:Z[c&255],argument:s,prefix:!0})}function rt(e,u,i,n,t,o,l,c,s,m){let{token:k}=e,h=$(e,u,o),{flags:d}=e;if((d&1)<1){if(e.token===86106)return vu(e,u,1,i,c,s,m);if((e.token&143360)===143360)return n||f(e,0),Pu(e,u,t,c,s,m)}return!l&&e.token===67174411?G1(e,u,h,t,1,0,d,c,s,m):e.token===10?($1(e,u,k,1),l&&f(e,48),h1(e,u,e.tokenValue,h,l,t,0,c,s,m)):h}function vt(e,u,i,n,t,o,l){if(i&&(e.destructible|=256),u&2097152){E(e,u|32768),u&8388608&&f(e,30),n||f(e,24),e.token===22&&f(e,120);let c=null,s=!1;return(e.flags&1)<1&&(s=M(e,u|32768,8457014),(e.token&77824||s)&&(c=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos))),e.assignable=2,v(e,u,t,o,l,{type:"YieldExpression",argument:c,delegate:s})}return u&1024&&f(e,94,"yield"),Q1(e,u,t,o,l)}function yt(e,u,i,n,t,o,l){if(n&&(e.destructible|=128),u&4194304||u&2048&&u&8192){i&&f(e,0),u&8388608&&L(e.index,e.line,e.index,29),E(e,u|32768);let c=h2(e,u,0,0,1,e.tokenPos,e.linePos,e.colPos);return e.token===8457273&&f(e,31),e.assignable=2,v(e,u,t,o,l,{type:"AwaitExpression",argument:c})}return u&2048&&f(e,95),Q1(e,u,t,o,l)}function d1(e,u,i,n,t,o){let{tokenPos:l,linePos:c,colPos:s}=e;q(e,u|32768,2162700);let m=[],k=u;if(e.token!==1074790415){for(;e.token===134283267;){let{index:h,tokenPos:d,tokenValue:y,token:w}=e,D=c2(e,u);eu(e,h,d,y)&&(u|=1024,e.flags&128&&L(e.index,e.line,e.tokenPos,63),e.flags&64&&L(e.index,e.line,e.tokenPos,8)),m.push(z1(e,u,D,w,d,e.linePos,e.colPos))}u&1024&&(t&&((t&537079808)===537079808&&f(e,115),(t&36864)===36864&&f(e,38)),e.flags&512&&f(e,115),e.flags&256&&f(e,114)),u&64&&i&&o!==void 0&&(k&1024)<1&&(u&8192)<1&&A(o)}for(e.flags=(e.flags|512|256|64)^832,e.destructible=(e.destructible|256)^256;e.token!==1074790415;)m.push(G2(e,u,i,4,{}));return q(e,n&24?u|32768:u,1074790415),e.flags&=-193,e.token===1077936157&&f(e,24),v(e,u,l,c,s,{type:"BlockStatement",body:m})}function At(e,u,i,n,t){switch(E(e,u),e.token){case 67108991:f(e,161);case 67174411:{(u&524288)<1&&f(e,26),u&16384&&f(e,27),e.assignable=2;break}case 69271571:case 67108877:{(u&262144)<1&&f(e,27),u&16384&&f(e,27),e.assignable=1;break}default:f(e,28,"super")}return v(e,u,i,n,t,{type:"Super"})}function h2(e,u,i,n,t,o,l,c){let s=d2(e,u,2,0,i,0,n,t,o,l,c);return H(e,u,s,n,0,o,l,c)}function Pt(e,u,i,n,t,o){e.assignable&2&&f(e,52);let{token:l}=e;return E(e,u),e.assignable=2,v(e,u,n,t,o,{type:"UpdateExpression",argument:i,operator:Z[l&255],prefix:!1})}function H(e,u,i,n,t,o,l,c){if((e.token&33619968)===33619968&&(e.flags&1)<1)i=Pt(e,u,i,o,l,c);else if((e.token&67108864)===67108864){switch(u=(u|134217728)^134217728,e.token){case 67108877:{E(e,(u|1073741824|8192)^8192),e.assignable=1;let s=mu(e,u);i=v(e,u,o,l,c,{type:"MemberExpression",object:i,computed:!1,property:s});break}case 69271571:{let s=!1;(e.flags&2048)===2048&&(s=!0,e.flags=(e.flags|2048)^2048),E(e,u|32768);let{tokenPos:m,linePos:k,colPos:h}=e,d=o2(e,u,n,1,m,k,h);q(e,u,20),e.assignable=1,i=v(e,u,o,l,c,{type:"MemberExpression",object:i,computed:!0,property:d}),s&&(e.flags|=2048);break}case 67174411:{if((e.flags&1024)===1024)return e.flags=(e.flags|1024)^1024,i;let s=!1;(e.flags&2048)===2048&&(s=!0,e.flags=(e.flags|2048)^2048);let m=Z1(e,u,n);e.assignable=2,i=v(e,u,o,l,c,{type:"CallExpression",callee:i,arguments:m}),s&&(e.flags|=2048);break}case 67108991:{E(e,(u|1073741824|8192)^8192),e.flags|=2048,e.assignable=2,i=Et(e,u,i,o,l,c);break}default:(e.flags&2048)===2048&&f(e,160),e.assignable=2,i=v(e,u,o,l,c,{type:"TaggedTemplateExpression",tag:i,quasi:e.token===67174408?Y1(e,u|65536):K1(e,u,e.tokenPos,e.linePos,e.colPos)})}i=H(e,u,i,0,1,o,l,c)}return t===0&&(e.flags&2048)===2048&&(e.flags=(e.flags|2048)^2048,i=v(e,u,o,l,c,{type:"ChainExpression",expression:i})),i}function Et(e,u,i,n,t,o){let l=!1,c;if((e.token===69271571||e.token===67174411)&&(e.flags&2048)===2048&&(l=!0,e.flags=(e.flags|2048)^2048),e.token===69271571){E(e,u|32768);let{tokenPos:s,linePos:m,colPos:k}=e,h=o2(e,u,0,1,s,m,k);q(e,u,20),e.assignable=2,c=v(e,u,n,t,o,{type:"MemberExpression",object:i,computed:!0,optional:!0,property:h})}else if(e.token===67174411){let s=Z1(e,u,0);e.assignable=2,c=v(e,u,n,t,o,{type:"CallExpression",callee:i,arguments:s,optional:!0})}else{(e.token&143360)<1&&f(e,154);let s=$(e,u,0);e.assignable=2,c=v(e,u,n,t,o,{type:"MemberExpression",object:i,computed:!1,optional:!0,property:s})}return l&&(e.flags|=2048),c}function mu(e,u){return(e.token&143360)<1&&e.token!==131&&f(e,154),u&1&&e.token===131?r1(e,u,e.tokenPos,e.linePos,e.colPos):$(e,u,0)}function Ct(e,u,i,n,t,o,l){i&&f(e,53),n||f(e,0);let{token:c}=e;E(e,u|32768);let s=h2(e,u,0,0,1,e.tokenPos,e.linePos,e.colPos);return e.assignable&2&&f(e,52),e.assignable=2,v(e,u,t,o,l,{type:"UpdateExpression",argument:s,operator:Z[c&255],prefix:!0})}function d2(e,u,i,n,t,o,l,c,s,m,k){if((e.token&143360)===143360){switch(e.token){case 209008:return yt(e,u,n,l,s,m,k);case 241773:return vt(e,u,l,t,s,m,k);case 209007:return rt(e,u,l,c,t,o,n,s,m,k)}let{token:h,tokenValue:d}=e,y=$(e,u|65536,o);return e.token===10?(c||f(e,0),$1(e,u,h,1),h1(e,u,d,y,n,t,0,s,m,k)):(u&16384&&h===537079928&&f(e,126),h===241739&&(u&1024&&f(e,109),i&24&&f(e,97)),e.assignable=u&1024&&(h&537079808)===537079808?2:1,y)}if((e.token&134217728)===134217728)return c2(e,u);switch(e.token){case 33619995:case 33619996:return Ct(e,u,n,c,s,m,k);case 16863278:case 16842800:case 16842801:case 25233970:case 25233971:case 16863277:case 16863279:return kt(e,u,c,s,m,k,l);case 86106:return vu(e,u,0,l,s,m,k);case 2162700:return Ft(e,u,t?0:1,l,s,m,k);case 69271571:return St(e,u,t?0:1,l,s,m,k);case 67174411:return Ot(e,u,t,1,0,s,m,k);case 86021:case 86022:case 86023:return qt(e,u,s,m,k);case 86113:return Bt(e,u);case 65540:return Rt(e,u,s,m,k);case 133:case 86096:return Vt(e,u,l,s,m,k);case 86111:return At(e,u,s,m,k);case 67174409:return K1(e,u,s,m,k);case 67174408:return Y1(e,u);case 86109:return Tt(e,u,l,s,m,k);case 134283389:return ru(e,u,s,m,k);case 131:return r1(e,u,s,m,k);case 86108:return Dt(e,u,n,l,s,m,k);case 8456258:if(u&16)return ee(e,u,1,s,m,k);default:if(J1(u,e.token))return Q1(e,u,s,m,k);f(e,28,Z[e.token&255])}}function Dt(e,u,i,n,t,o,l){let c=$(e,u,0);return e.token===67108877?bu(e,u,c,t,o,l):(i&&f(e,137),c=ku(e,u,n,t,o,l),e.assignable=2,H(e,u,c,n,0,t,o,l))}function bu(e,u,i,n,t,o){return u&2048||f(e,163),E(e,u),e.token!==143495&&e.tokenValue!=="meta"&&f(e,28,Z[e.token&255]),e.assignable=2,v(e,u,n,t,o,{type:"MetaProperty",meta:i,property:$(e,u,0)})}function ku(e,u,i,n,t,o){q(e,u|32768,67174411),e.token===14&&f(e,138);let l=K(e,u,1,0,i,e.tokenPos,e.linePos,e.colPos);return q(e,u,16),v(e,u,n,t,o,{type:"ImportExpression",source:l})}function ru(e,u,i,n,t){let{tokenRaw:o,tokenValue:l}=e;return E(e,u),e.assignable=2,v(e,u,i,n,t,u&512?{type:"Literal",value:l,bigint:o.slice(0,-1),raw:o}:{type:"Literal",value:l,bigint:o.slice(0,-1)})}function K1(e,u,i,n,t){e.assignable=2;let{tokenValue:o,tokenRaw:l,tokenPos:c,linePos:s,colPos:m}=e;q(e,u,67174409);let k=[g1(e,u,o,l,c,s,m,!0)];return v(e,u,i,n,t,{type:"TemplateLiteral",expressions:[],quasis:k})}function Y1(e,u){u=(u|134217728)^134217728;let{tokenValue:i,tokenRaw:n,tokenPos:t,linePos:o,colPos:l}=e;q(e,u|32768,67174408);let c=[g1(e,u,i,n,t,o,l,!1)],s=[o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos)];for(e.token!==1074790415&&f(e,80);(e.token=I0(e,u))!==67174409;){let{tokenValue:m,tokenRaw:k,tokenPos:h,linePos:d,colPos:y}=e;q(e,u|32768,67174408),c.push(g1(e,u,m,k,h,d,y,!1)),s.push(o2(e,u,0,1,e.tokenPos,e.linePos,e.colPos)),e.token!==1074790415&&f(e,80)}{let{tokenValue:m,tokenRaw:k,tokenPos:h,linePos:d,colPos:y}=e;q(e,u,67174409),c.push(g1(e,u,m,k,h,d,y,!0))}return v(e,u,t,o,l,{type:"TemplateLiteral",expressions:s,quasis:c})}function g1(e,u,i,n,t,o,l,c){let s=v(e,u,t,o,l,{type:"TemplateElement",value:{cooked:i,raw:n},tail:c}),m=c?1:2;return u&2&&(s.start+=1,s.range[0]+=1,s.end-=m,s.range[1]-=m),u&4&&(s.loc.start.column+=1,s.loc.end.column-=m),s}function wt(e,u,i,n,t){u=(u|134217728)^134217728,q(e,u|32768,14);let o=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return e.assignable=1,v(e,u,i,n,t,{type:"SpreadElement",argument:o})}function Z1(e,u,i){E(e,u|32768);let n=[];if(e.token===16)return E(e,u),n;for(;e.token!==16&&(e.token===14?n.push(wt(e,u,e.tokenPos,e.linePos,e.colPos)):n.push(K(e,u,1,0,i,e.tokenPos,e.linePos,e.colPos)),!(e.token!==18||(E(e,u|32768),e.token===16))););return q(e,u,16),n}function $(e,u,i){let{tokenValue:n,tokenPos:t,linePos:o,colPos:l}=e;return E(e,u),v(e,u,t,o,l,u&268435456?{type:"Identifier",name:n,pattern:i===1}:{type:"Identifier",name:n})}function c2(e,u){let{tokenValue:i,tokenRaw:n,tokenPos:t,linePos:o,colPos:l}=e;return e.token===134283389?ru(e,u,t,o,l):(E(e,u),e.assignable=2,v(e,u,t,o,l,u&512?{type:"Literal",value:i,raw:n}:{type:"Literal",value:i}))}function qt(e,u,i,n,t){let o=Z[e.token&255],l=e.token===86023?null:o==="true";return E(e,u),e.assignable=2,v(e,u,i,n,t,u&512?{type:"Literal",value:l,raw:o}:{type:"Literal",value:l})}function Bt(e,u){let{tokenPos:i,linePos:n,colPos:t}=e;return E(e,u),e.assignable=2,v(e,u,i,n,t,{type:"ThisExpression"})}function I2(e,u,i,n,t,o,l,c,s,m){E(e,u|32768);let k=t?M1(e,u,8457014):0,h=null,d,y=i?_2():void 0;if(e.token===67174411)(o&1)<1&&f(e,37,"Function");else{let F=n&4&&((u&8192)<1||(u&2048)<1)?4:64;uu(e,u|(u&3072)<<11,e.token),i&&(F&4?tu(e,u,i,e.tokenValue,F):L2(e,u,i,e.tokenValue,F,n),y=i2(y,256),o&&o&2&&M2(e,e.tokenValue)),d=e.token,e.token&143360?h=$(e,u,0):f(e,28,Z[e.token&255])}u=(u|32243712)^32243712|67108864|l*2+k<<21|(k?0:1073741824),i&&(y=i2(y,512));let w=Au(e,u|8388608,y,0,1),D=d1(e,(u|8192|4096|131072)^143360,i?i2(y,128):y,8,d,i?y.scopeError:void 0);return v(e,u,c,s,m,{type:"FunctionDeclaration",id:h,params:w,body:D,async:l===1,generator:k===1})}function vu(e,u,i,n,t,o,l){E(e,u|32768);let c=M1(e,u,8457014),s=i*2+c<<21,m=null,k,h=u&64?_2():void 0;(e.token&176128)>0&&(uu(e,(u|32243712)^32243712|s,e.token),h&&(h=i2(h,256)),k=e.token,m=$(e,u,0)),u=(u|32243712)^32243712|67108864|s|(c?0:1073741824),h&&(h=i2(h,512));let d=Au(e,u|8388608,h,n,1),y=d1(e,u&-134377473,h&&i2(h,128),0,k,void 0);return e.assignable=2,v(e,u,t,o,l,{type:"FunctionExpression",id:m,params:d,body:y,async:i===1,generator:c===1})}function St(e,u,i,n,t,o,l){let c=m2(e,u,void 0,i,n,0,2,0,t,o,l);return u&256&&e.destructible&64&&f(e,60),e.destructible&8&&f(e,59),c}function m2(e,u,i,n,t,o,l,c,s,m,k){E(e,u|32768);let h=[],d=0;for(u=(u|134217728)^134217728;e.token!==20;)if(M(e,u|32768,18))h.push(null);else{let w,{token:D,tokenPos:F,linePos:O,colPos:N,tokenValue:x}=e;if(D&143360)if(w=d2(e,u,l,0,1,0,t,1,F,O,N),e.token===1077936157){e.assignable&2&&f(e,24),E(e,u|32768),i&&B2(e,u,i,x,l,c);let I=K(e,u,1,1,t,e.tokenPos,e.linePos,e.colPos);w=v(e,u,F,O,N,o?{type:"AssignmentPattern",left:w,right:I}:{type:"AssignmentExpression",operator:"=",left:w,right:I}),d|=e.destructible&256?256:0|e.destructible&128?128:0}else e.token===18||e.token===20?(e.assignable&2?d|=16:i&&B2(e,u,i,x,l,c),d|=e.destructible&256?256:0|e.destructible&128?128:0):(d|=l&1?32:(l&2)<1?16:0,w=H(e,u,w,t,0,F,O,N),e.token!==18&&e.token!==20?(e.token!==1077936157&&(d|=16),w=Q(e,u,t,o,F,O,N,w)):e.token!==1077936157&&(d|=e.assignable&2?16:32));else D&2097152?(w=e.token===2162700?b2(e,u,i,0,t,o,l,c,F,O,N):m2(e,u,i,0,t,o,l,c,F,O,N),d|=e.destructible,e.assignable=e.destructible&16?2:1,e.token===18||e.token===20?e.assignable&2&&(d|=16):e.destructible&8?f(e,68):(w=H(e,u,w,t,0,F,O,N),d=e.assignable&2?16:0,e.token!==18&&e.token!==20?w=Q(e,u,t,o,F,O,N,w):e.token!==1077936157&&(d|=e.assignable&2?16:32))):D===14?(w=W2(e,u,i,20,l,c,0,t,o,F,O,N),d|=e.destructible,e.token!==18&&e.token!==20&&f(e,28,Z[e.token&255])):(w=h2(e,u,1,0,1,F,O,N),e.token!==18&&e.token!==20?(w=Q(e,u,t,o,F,O,N,w),(l&3)<1&&D===67174411&&(d|=16)):e.assignable&2?d|=16:D===67174411&&(d|=e.assignable&1&&l&3?32:16));if(h.push(w),M(e,u|32768,18)){if(e.token===20)break}else break}q(e,u,20);let y=v(e,u,s,m,k,{type:o?"ArrayPattern":"ArrayExpression",elements:h});return!n&&e.token&4194304?yu(e,u,d,t,o,s,m,k,y):(e.destructible=d,y)}function yu(e,u,i,n,t,o,l,c,s){e.token!==1077936157&&f(e,24),E(e,u|32768),i&16&&f(e,24),t||r2(e,s);let{tokenPos:m,linePos:k,colPos:h}=e,d=K(e,u,1,1,n,m,k,h);return e.destructible=(i|64|8)^72|(e.destructible&128?128:0)|(e.destructible&256?256:0),v(e,u,o,l,c,t?{type:"AssignmentPattern",left:s,right:d}:{type:"AssignmentExpression",left:s,operator:"=",right:d})}function W2(e,u,i,n,t,o,l,c,s,m,k,h){E(e,u|32768);let d=null,y=0,{token:w,tokenValue:D,tokenPos:F,linePos:O,colPos:N}=e;if(w&143360)e.assignable=1,d=d2(e,u,t,0,1,0,c,1,F,O,N),w=e.token,d=H(e,u,d,c,0,F,O,N),e.token!==18&&e.token!==n&&(e.assignable&2&&e.token===1077936157&&f(e,68),y|=16,d=Q(e,u,c,s,F,O,N,d)),e.assignable&2?y|=16:w===n||w===18?i&&B2(e,u,i,D,t,o):y|=32,y|=e.destructible&128?128:0;else if(w===n)f(e,39);else if(w&2097152)d=e.token===2162700?b2(e,u,i,1,c,s,t,o,F,O,N):m2(e,u,i,1,c,s,t,o,F,O,N),w=e.token,w!==1077936157&&w!==n&&w!==18?(e.destructible&8&&f(e,68),d=H(e,u,d,c,0,F,O,N),y|=e.assignable&2?16:0,(e.token&4194304)===4194304?(e.token!==1077936157&&(y|=16),d=Q(e,u,c,s,F,O,N,d)):((e.token&8454144)===8454144&&(d=T2(e,u,1,F,O,N,4,w,d)),M(e,u|32768,22)&&(d=U2(e,u,d,F,O,N)),y|=e.assignable&2?16:32)):y|=n===1074790415&&w!==1077936157?16:e.destructible;else{y|=32,d=h2(e,u,1,c,1,e.tokenPos,e.linePos,e.colPos);let{token:x,tokenPos:I,linePos:W,colPos:P}=e;return x===1077936157&&x!==n&&x!==18?(e.assignable&2&&f(e,24),d=Q(e,u,c,s,I,W,P,d),y|=16):(x===18?y|=16:x!==n&&(d=Q(e,u,c,s,I,W,P,d)),y|=e.assignable&1?32:16),e.destructible=y,e.token!==n&&e.token!==18&&f(e,155),v(e,u,m,k,h,{type:s?"RestElement":"SpreadElement",argument:d})}if(e.token!==n)if(t&1&&(y|=l?16:32),M(e,u|32768,1077936157)){y&16&&f(e,24),r2(e,d);let x=K(e,u,1,1,c,e.tokenPos,e.linePos,e.colPos);d=v(e,u,F,O,N,s?{type:"AssignmentPattern",left:d,right:x}:{type:"AssignmentExpression",left:d,operator:"=",right:x}),y=16}else y|=16;return e.destructible=y,v(e,u,m,k,h,{type:s?"RestElement":"SpreadElement",argument:d})}function v2(e,u,i,n,t,o,l){let c=(i&64)<1?31981568:14680064;u=(u|c)^c|(i&88)<<18|100925440;let s=u&64?i2(_2(),512):void 0,m=Lt(e,u|8388608,s,i,1,n);s&&(s=i2(s,128));let k=d1(e,u&-134230017,s,0,void 0,void 0);return v(e,u,t,o,l,{type:"FunctionExpression",params:m,body:k,async:(i&16)>0,generator:(i&8)>0,id:null})}function Ft(e,u,i,n,t,o,l){let c=b2(e,u,void 0,i,n,0,2,0,t,o,l);return u&256&&e.destructible&64&&f(e,60),e.destructible&8&&f(e,59),c}function b2(e,u,i,n,t,o,l,c,s,m,k){E(e,u);let h=[],d=0,y=0;for(u=(u|134217728)^134217728;e.token!==1074790415;){let{token:D,tokenValue:F,linePos:O,colPos:N,tokenPos:x}=e;if(D===14)h.push(W2(e,u,i,1074790415,l,c,0,t,o,x,O,N));else{let I=0,W=null,P,y2=e.token;if(e.token&143360||e.token===121)if(W=$(e,u,0),e.token===18||e.token===1074790415||e.token===1077936157)if(I|=4,u&1024&&(D&537079808)===537079808?d|=16:l1(e,u,l,D,0),i&&B2(e,u,i,F,l,c),M(e,u|32768,1077936157)){d|=8;let R=K(e,u,1,1,t,e.tokenPos,e.linePos,e.colPos);d|=e.destructible&256?256:0|e.destructible&128?128:0,P=v(e,u,x,O,N,{type:"AssignmentPattern",left:u&-2147483648?Object.assign({},W):W,right:R})}else d|=(D===209008?128:0)|(D===121?16:0),P=u&-2147483648?Object.assign({},W):W;else if(M(e,u|32768,21)){let{tokenPos:R,linePos:_,colPos:j}=e;if(F==="__proto__"&&y++,e.token&143360){let J2=e.token,Y2=e.tokenValue;d|=y2===121?16:0,P=d2(e,u,l,0,1,0,t,1,R,_,j);let{token:C2}=e;P=H(e,u,P,t,0,R,_,j),e.token===18||e.token===1074790415?C2===1077936157||C2===1074790415||C2===18?(d|=e.destructible&128?128:0,e.assignable&2?d|=16:i&&(J2&143360)===143360&&B2(e,u,i,Y2,l,c)):d|=e.assignable&1?32:16:(e.token&4194304)===4194304?(e.assignable&2?d|=16:C2!==1077936157?d|=32:i&&B2(e,u,i,Y2,l,c),P=Q(e,u,t,o,R,_,j,P)):(d|=16,(e.token&8454144)===8454144&&(P=T2(e,u,1,R,_,j,4,C2,P)),M(e,u|32768,22)&&(P=U2(e,u,P,R,_,j)))}else(e.token&2097152)===2097152?(P=e.token===69271571?m2(e,u,i,0,t,o,l,c,R,_,j):b2(e,u,i,0,t,o,l,c,R,_,j),d=e.destructible,e.assignable=d&16?2:1,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):e.destructible&8?f(e,68):(P=H(e,u,P,t,0,R,_,j),d=e.assignable&2?16:0,(e.token&4194304)===4194304?P=a1(e,u,t,o,R,_,j,P):((e.token&8454144)===8454144&&(P=T2(e,u,1,R,_,j,4,D,P)),M(e,u|32768,22)&&(P=U2(e,u,P,R,_,j)),d|=e.assignable&2?16:32))):(P=h2(e,u,1,t,1,R,_,j),d|=e.assignable&1?32:16,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):(P=H(e,u,P,t,0,R,_,j),d=e.assignable&2?16:0,e.token!==18&&D!==1074790415&&(e.token!==1077936157&&(d|=16),P=Q(e,u,t,o,R,_,j,P))))}else e.token===69271571?(d|=16,D===209007&&(I|=16),I|=(D===12402?256:D===12403?512:1)|2,W=K2(e,u,t),d|=e.assignable,P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):e.token&143360?(d|=16,D===121&&f(e,92),D===209007&&(e.flags&1&&f(e,128),I|=16),W=$(e,u,0),I|=D===12402?256:D===12403?512:1,P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):e.token===67174411?(d|=16,I|=1,P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):e.token===8457014?(d|=16,D===12402||D===12403?f(e,40):D===143483&&f(e,92),E(e,u),I|=9|(D===209007?16:0),e.token&143360?W=$(e,u,0):(e.token&134217728)===134217728?W=c2(e,u):e.token===69271571?(I|=2,W=K2(e,u,t),d|=e.assignable):f(e,28,Z[e.token&255]),P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):(e.token&134217728)===134217728?(D===209007&&(I|=16),I|=D===12402?256:D===12403?512:1,d|=16,W=c2(e,u),P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):f(e,129);else if((e.token&134217728)===134217728)if(W=c2(e,u),e.token===21){q(e,u|32768,21);let{tokenPos:R,linePos:_,colPos:j}=e;if(F==="__proto__"&&y++,e.token&143360){P=d2(e,u,l,0,1,0,t,1,R,_,j);let{token:J2,tokenValue:Y2}=e;P=H(e,u,P,t,0,R,_,j),e.token===18||e.token===1074790415?J2===1077936157||J2===1074790415||J2===18?e.assignable&2?d|=16:i&&B2(e,u,i,Y2,l,c):d|=e.assignable&1?32:16:e.token===1077936157?(e.assignable&2&&(d|=16),P=Q(e,u,t,o,R,_,j,P)):(d|=16,P=Q(e,u,t,o,R,_,j,P))}else(e.token&2097152)===2097152?(P=e.token===69271571?m2(e,u,i,0,t,o,l,c,R,_,j):b2(e,u,i,0,t,o,l,c,R,_,j),d=e.destructible,e.assignable=d&16?2:1,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):(e.destructible&8)!==8&&(P=H(e,u,P,t,0,R,_,j),d=e.assignable&2?16:0,(e.token&4194304)===4194304?P=a1(e,u,t,o,R,_,j,P):((e.token&8454144)===8454144&&(P=T2(e,u,1,R,_,j,4,D,P)),M(e,u|32768,22)&&(P=U2(e,u,P,R,_,j)),d|=e.assignable&2?16:32))):(P=h2(e,u,1,0,1,R,_,j),d|=e.assignable&1?32:16,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):(P=H(e,u,P,t,0,R,_,j),d=e.assignable&1?0:16,e.token!==18&&e.token!==1074790415&&(e.token!==1077936157&&(d|=16),P=Q(e,u,t,o,R,_,j,P))))}else e.token===67174411?(I|=1,P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos),d=e.assignable|16):f(e,130);else if(e.token===69271571)if(W=K2(e,u,t),d|=e.destructible&256?256:0,I|=2,e.token===21){E(e,u|32768);let{tokenPos:R,linePos:_,colPos:j,tokenValue:J2,token:Y2}=e;if(e.token&143360){P=d2(e,u,l,0,1,0,t,1,R,_,j);let{token:C2}=e;P=H(e,u,P,t,0,R,_,j),(e.token&4194304)===4194304?(d|=e.assignable&2?16:C2===1077936157?0:32,P=a1(e,u,t,o,R,_,j,P)):e.token===18||e.token===1074790415?C2===1077936157||C2===1074790415||C2===18?e.assignable&2?d|=16:i&&(Y2&143360)===143360&&B2(e,u,i,J2,l,c):d|=e.assignable&1?32:16:(d|=16,P=Q(e,u,t,o,R,_,j,P))}else(e.token&2097152)===2097152?(P=e.token===69271571?m2(e,u,i,0,t,o,l,c,R,_,j):b2(e,u,i,0,t,o,l,c,R,_,j),d=e.destructible,e.assignable=d&16?2:1,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):d&8?f(e,59):(P=H(e,u,P,t,0,R,_,j),d=e.assignable&2?d|16:0,(e.token&4194304)===4194304?(e.token!==1077936157&&(d|=16),P=a1(e,u,t,o,R,_,j,P)):((e.token&8454144)===8454144&&(P=T2(e,u,1,R,_,j,4,D,P)),M(e,u|32768,22)&&(P=U2(e,u,P,R,_,j)),d|=e.assignable&2?16:32))):(P=h2(e,u,1,0,1,R,_,j),d|=e.assignable&1?32:16,e.token===18||e.token===1074790415?e.assignable&2&&(d|=16):(P=H(e,u,P,t,0,R,_,j),d=e.assignable&1?0:16,e.token!==18&&e.token!==1074790415&&(e.token!==1077936157&&(d|=16),P=Q(e,u,t,o,R,_,j,P))))}else e.token===67174411?(I|=1,P=v2(e,u,I,t,e.tokenPos,O,N),d=16):f(e,41);else if(D===8457014)if(q(e,u|32768,8457014),I|=8,e.token&143360){let{token:R,line:_,index:j}=e;W=$(e,u,0),I|=1,e.token===67174411?(d|=16,P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):L(j,_,j,R===209007?43:R===12402||e.token===12403?42:44,Z[R&255])}else(e.token&134217728)===134217728?(d|=16,W=c2(e,u),I|=1,P=v2(e,u,I,t,x,O,N)):e.token===69271571?(d|=16,I|=3,W=K2(e,u,t),P=v2(e,u,I,t,e.tokenPos,e.linePos,e.colPos)):f(e,122);else f(e,28,Z[D&255]);d|=e.destructible&128?128:0,e.destructible=d,h.push(v(e,u,x,O,N,{type:"Property",key:W,value:P,kind:I&768?I&512?"set":"get":"init",computed:(I&2)>0,method:(I&1)>0,shorthand:(I&4)>0}))}if(d|=e.destructible,e.token!==18)break;E(e,u)}q(e,u,1074790415),y>1&&(d|=64);let w=v(e,u,s,m,k,{type:o?"ObjectPattern":"ObjectExpression",properties:h});return!n&&e.token&4194304?yu(e,u,d,t,o,s,m,k,w):(e.destructible=d,w)}function Lt(e,u,i,n,t,o){q(e,u,67174411);let l=[];if(e.flags=(e.flags|128)^128,e.token===16)return n&512&&f(e,35,"Setter","one",""),E(e,u),l;n&256&&f(e,35,"Getter","no","s"),n&512&&e.token===14&&f(e,36),u=(u|134217728)^134217728;let c=0,s=0;for(;e.token!==18;){let m=null,{tokenPos:k,linePos:h,colPos:d}=e;if(e.token&143360?((u&1024)<1&&((e.token&36864)===36864&&(e.flags|=256),(e.token&537079808)===537079808&&(e.flags|=512)),m=p1(e,u,i,n|1,0,k,h,d)):(e.token===2162700?m=b2(e,u,i,1,o,1,t,0,k,h,d):e.token===69271571?m=m2(e,u,i,1,o,1,t,0,k,h,d):e.token===14&&(m=W2(e,u,i,16,t,0,0,o,1,k,h,d)),s=1,e.destructible&48&&f(e,47)),e.token===1077936157){E(e,u|32768),s=1;let y=K(e,u,1,1,0,e.tokenPos,e.linePos,e.colPos);m=v(e,u,k,h,d,{type:"AssignmentPattern",left:m,right:y})}if(c++,l.push(m),!M(e,u,18)||e.token===16)break}return n&512&&c!==1&&f(e,35,"Setter","one",""),i&&i.scopeError!==void 0&&A(i.scopeError),s&&(e.flags|=128),q(e,u,16),l}function K2(e,u,i){E(e,u|32768);let n=K(e,(u|134217728)^134217728,1,0,i,e.tokenPos,e.linePos,e.colPos);return q(e,u,20),n}function Ot(e,u,i,n,t,o,l,c){e.flags=(e.flags|128)^128;let{tokenPos:s,linePos:m,colPos:k}=e;E(e,u|32768|1073741824);let h=u&64?i2(_2(),1024):void 0;if(u=(u|134217728)^134217728,M(e,u,16))return m1(e,u,h,[],i,0,o,l,c);let d=0;e.destructible&=-385;let y,w=[],D=0,F=0,{tokenPos:O,linePos:N,colPos:x}=e;for(e.assignable=1;e.token!==16;){let{token:I,tokenPos:W,linePos:P,colPos:y2}=e;if(I&143360)h&&L2(e,u,h,e.tokenValue,1,0),y=d2(e,u,n,0,1,0,1,1,W,P,y2),e.token===16||e.token===18?e.assignable&2?(d|=16,F=1):((I&537079808)===537079808||(I&36864)===36864)&&(F=1):(e.token===1077936157?F=1:d|=16,y=H(e,u,y,1,0,W,P,y2),e.token!==16&&e.token!==18&&(y=Q(e,u,1,0,W,P,y2,y)));else if((I&2097152)===2097152)y=I===2162700?b2(e,u|1073741824,h,0,1,0,n,t,W,P,y2):m2(e,u|1073741824,h,0,1,0,n,t,W,P,y2),d|=e.destructible,F=1,e.assignable=2,e.token!==16&&e.token!==18&&(d&8&&f(e,118),y=H(e,u,y,0,0,W,P,y2),d|=16,e.token!==16&&e.token!==18&&(y=Q(e,u,0,0,W,P,y2,y)));else if(I===14){y=W2(e,u,h,16,n,t,0,1,0,W,P,y2),e.destructible&16&&f(e,71),F=1,D&&(e.token===16||e.token===18)&&w.push(y),d|=8;break}else{if(d|=16,y=K(e,u,1,0,1,W,P,y2),D&&(e.token===16||e.token===18)&&w.push(y),e.token===18&&(D||(D=1,w=[y])),D){for(;M(e,u|32768,18);)w.push(K(e,u,1,0,1,e.tokenPos,e.linePos,e.colPos));e.assignable=2,y=v(e,u,O,N,x,{type:"SequenceExpression",expressions:w})}return q(e,u,16),e.destructible=d,y}if(D&&(e.token===16||e.token===18)&&w.push(y),!M(e,u|32768,18))break;if(D||(D=1,w=[y]),e.token===16){d|=8;break}}return D&&(e.assignable=2,y=v(e,u,O,N,x,{type:"SequenceExpression",expressions:w})),q(e,u,16),d&16&&d&8&&f(e,145),d|=e.destructible&256?256:0|e.destructible&128?128:0,e.token===10?(d&48&&f(e,46),u&4196352&&d&128&&f(e,29),u&2098176&&d&256&&f(e,30),F&&(e.flags|=128),m1(e,u,h,D?w:[y],i,0,o,l,c)):(d&8&&f(e,139),e.destructible=(e.destructible|256)^256|d,u&128?v(e,u,s,m,k,{type:"ParenthesizedExpression",expression:y}):y)}function Q1(e,u,i,n,t){let{tokenValue:o}=e,l=$(e,u,0);if(e.assignable=1,e.token===10){let c;return u&64&&(c=c1(e,u,o)),e.flags=(e.flags|128)^128,e1(e,u,c,[l],0,i,n,t)}return l}function h1(e,u,i,n,t,o,l,c,s,m){o||f(e,54),t&&f(e,48),e.flags&=-129;let k=u&64?c1(e,u,i):void 0;return e1(e,u,k,[n],l,c,s,m)}function m1(e,u,i,n,t,o,l,c,s){t||f(e,54);for(let m=0;m<n.length;++m)r2(e,n[m]);return e1(e,u,i,n,o,l,c,s)}function e1(e,u,i,n,t,o,l,c){e.flags&1&&f(e,45),q(e,u|32768,10),u=(u|15728640)^15728640|t<<22;let s=e.token!==2162700,m;if(i&&i.scopeError!==void 0&&A(i.scopeError),s)m=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);else{switch(i&&(i=i2(i,128)),m=d1(e,(u|134221824|8192|16384)^134246400,i,16,void 0,void 0),e.token){case 69271571:(e.flags&1)<1&&f(e,112);break;case 67108877:case 67174409:case 22:f(e,113);case 67174411:(e.flags&1)<1&&f(e,112),e.flags|=1024;break}(e.token&8454144)===8454144&&(e.flags&1)<1&&f(e,28,Z[e.token&255]),(e.token&33619968)===33619968&&f(e,121)}return e.assignable=2,v(e,u,o,l,c,{type:"ArrowFunctionExpression",params:n,body:m,async:t===1,expression:s})}function Au(e,u,i,n,t){q(e,u,67174411),e.flags=(e.flags|128)^128;let o=[];if(M(e,u,16))return o;u=(u|134217728)^134217728;let l=0;for(;e.token!==18;){let c,{tokenPos:s,linePos:m,colPos:k}=e;if(e.token&143360?((u&1024)<1&&((e.token&36864)===36864&&(e.flags|=256),(e.token&537079808)===537079808&&(e.flags|=512)),c=p1(e,u,i,t|1,0,s,m,k)):(e.token===2162700?c=b2(e,u,i,1,n,1,t,0,s,m,k):e.token===69271571?c=m2(e,u,i,1,n,1,t,0,s,m,k):e.token===14?c=W2(e,u,i,16,t,0,0,n,1,s,m,k):f(e,28,Z[e.token&255]),l=1,e.destructible&48&&f(e,47)),e.token===1077936157){E(e,u|32768),l=1;let h=K(e,u,1,1,n,e.tokenPos,e.linePos,e.colPos);c=v(e,u,s,m,k,{type:"AssignmentPattern",left:c,right:h})}if(o.push(c),!M(e,u,18)||e.token===16)break}return l&&(e.flags|=128),i&&(l||u&1024)&&i.scopeError!==void 0&&A(i.scopeError),q(e,u,16),o}function b1(e,u,i,n,t,o,l){let{token:c}=e;if(c&67108864){if(c===67108877){E(e,u|1073741824),e.assignable=1;let s=mu(e,u);return b1(e,u,v(e,u,t,o,l,{type:"MemberExpression",object:i,computed:!1,property:s}),0,t,o,l)}else if(c===69271571){E(e,u|32768);let{tokenPos:s,linePos:m,colPos:k}=e,h=o2(e,u,n,1,s,m,k);return q(e,u,20),e.assignable=1,b1(e,u,v(e,u,t,o,l,{type:"MemberExpression",object:i,computed:!0,property:h}),0,t,o,l)}else if(c===67174408||c===67174409)return e.assignable=2,b1(e,u,v(e,u,t,o,l,{type:"TaggedTemplateExpression",tag:i,quasi:e.token===67174408?Y1(e,u|65536):K1(e,u,e.tokenPos,e.linePos,e.colPos)}),0,t,o,l)}return i}function Tt(e,u,i,n,t,o){let l=$(e,u|32768,0),{tokenPos:c,linePos:s,colPos:m}=e;if(M(e,u,67108877)){if(u&67108864&&e.token===143494)return e.assignable=2,It(e,u,l,n,t,o);f(e,91)}e.assignable=2,(e.token&16842752)===16842752&&f(e,62,Z[e.token&255]);let k=d2(e,u,2,1,0,0,i,1,c,s,m);u=(u|134217728)^134217728,e.token===67108991&&f(e,162);let h=b1(e,u,k,i,c,s,m);return e.assignable=2,v(e,u,n,t,o,{type:"NewExpression",callee:h,arguments:e.token===67174411?Z1(e,u,i):[]})}function It(e,u,i,n,t,o){let l=$(e,u,0);return v(e,u,n,t,o,{type:"MetaProperty",meta:i,property:l})}function Pu(e,u,i,n,t,o){return e.token===209008&&f(e,29),u&2098176&&e.token===241773&&f(e,30),(e.token&537079808)===537079808&&(e.flags|=512),h1(e,u,e.tokenValue,$(e,u,0),0,i,1,n,t,o)}function G1(e,u,i,n,t,o,l,c,s,m){E(e,u|32768);let k=u&64?i2(_2(),1024):void 0;if(u=(u|134217728)^134217728,M(e,u,16))return e.token===10?(l&1&&f(e,45),m1(e,u,k,[],n,1,c,s,m)):v(e,u,c,s,m,{type:"CallExpression",callee:i,arguments:[]});let h=0,d=null,y=0;e.destructible=(e.destructible|256|128)^384;let w=[];for(;e.token!==16;){let{token:D,tokenPos:F,linePos:O,colPos:N}=e;if(D&143360)k&&L2(e,u,k,e.tokenValue,t,0),d=d2(e,u,t,0,1,0,1,1,F,O,N),e.token===16||e.token===18?e.assignable&2?(h|=16,y=1):(D&537079808)===537079808?e.flags|=512:(D&36864)===36864&&(e.flags|=256):(e.token===1077936157?y=1:h|=16,d=H(e,u,d,1,0,F,O,N),e.token!==16&&e.token!==18&&(d=Q(e,u,1,0,F,O,N,d)));else if(D&2097152)d=D===2162700?b2(e,u,k,0,1,0,t,o,F,O,N):m2(e,u,k,0,1,0,t,o,F,O,N),h|=e.destructible,y=1,e.token!==16&&e.token!==18&&(h&8&&f(e,118),d=H(e,u,d,0,0,F,O,N),h|=16,(e.token&8454144)===8454144&&(d=T2(e,u,1,c,s,m,4,D,d)),M(e,u|32768,22)&&(d=U2(e,u,d,c,s,m)));else if(D===14)d=W2(e,u,k,16,t,o,1,1,0,F,O,N),h|=(e.token===16?0:16)|e.destructible,y=1;else{for(d=K(e,u,1,0,0,F,O,N),h=e.assignable,w.push(d);M(e,u|32768,18);)w.push(K(e,u,1,0,0,F,O,N));return h|=e.assignable,q(e,u,16),e.destructible=h|16,e.assignable=2,v(e,u,c,s,m,{type:"CallExpression",callee:i,arguments:w})}if(w.push(d),!M(e,u|32768,18))break}return q(e,u,16),h|=e.destructible&256?256:0|e.destructible&128?128:0,e.token===10?(h&48&&f(e,25),(e.flags&1||l&1)&&f(e,45),h&128&&f(e,29),u&2098176&&h&256&&f(e,30),y&&(e.flags|=128),m1(e,u,k,w,n,1,c,s,m)):(h&8&&f(e,59),e.assignable=2,v(e,u,c,s,m,{type:"CallExpression",callee:i,arguments:w}))}function Rt(e,u,i,n,t){let{tokenRaw:o,tokenRegExp:l,tokenValue:c}=e;return E(e,u),e.assignable=2,u&512?v(e,u,i,n,t,{type:"Literal",value:c,regex:l,raw:o}):v(e,u,i,n,t,{type:"Literal",value:c,regex:l})}function x1(e,u,i,n,t,o,l){u=(u|16777216|1024)^16777216;let c=k1(e,u);c.length&&(t=e.tokenPos,o=e.linePos,l=e.colPos),e.leadingDecorators.length&&(e.leadingDecorators.push(...c),c=e.leadingDecorators,e.leadingDecorators=[]),E(e,u);let s=null,m=null,{tokenValue:k}=e;e.token&4096&&e.token!==20567?(iu(e,u,e.token)&&f(e,114),(e.token&537079808)===537079808&&f(e,115),i&&(L2(e,u,i,k,32,0),n&&n&2&&M2(e,k)),s=$(e,u,0)):(n&1)<1&&f(e,37,"Class");let h=u;M(e,u|32768,20567)?(m=h2(e,u,0,0,0,e.tokenPos,e.linePos,e.colPos),h|=524288):h=(h|524288)^524288;let d=Eu(e,h,u,i,2,8,0);return v(e,u,t,o,l,u&1?{type:"ClassDeclaration",id:s,superClass:m,decorators:c,body:d}:{type:"ClassDeclaration",id:s,superClass:m,body:d})}function Vt(e,u,i,n,t,o){let l=null,c=null;u=(u|1024|16777216)^16777216;let s=k1(e,u);s.length&&(n=e.tokenPos,t=e.linePos,o=e.colPos),E(e,u),e.token&4096&&e.token!==20567&&(iu(e,u,e.token)&&f(e,114),(e.token&537079808)===537079808&&f(e,115),l=$(e,u,0));let m=u;M(e,u|32768,20567)?(c=h2(e,u,0,i,0,e.tokenPos,e.linePos,e.colPos),m|=524288):m=(m|524288)^524288;let k=Eu(e,m,u,void 0,2,0,i);return e.assignable=2,v(e,u,n,t,o,u&1?{type:"ClassExpression",id:l,superClass:c,decorators:s,body:k}:{type:"ClassExpression",id:l,superClass:c,body:k})}function k1(e,u){let i=[];if(u&1)for(;e.token===133;)i.push(Nt(e,u,e.tokenPos,e.linePos,e.colPos));return i}function Nt(e,u,i,n,t){E(e,u|32768);let o=d2(e,u,2,0,1,0,0,1,i,n,t);return o=H(e,u,o,0,0,i,n,t),v(e,u,i,n,t,{type:"Decorator",expression:o})}function Eu(e,u,i,n,t,o,l){let{tokenPos:c,linePos:s,colPos:m}=e;q(e,u|32768,2162700),u=(u|134217728)^134217728,e.flags=(e.flags|32)^32;let k=[],h;for(;e.token!==1074790415;){let d=0;if(h=k1(e,u),d=h.length,d>0&&e.tokenValue==="constructor"&&f(e,106),e.token===1074790415&&f(e,105),M(e,u,1074790417)){d>0&&f(e,116);continue}k.push(Cu(e,u,n,i,t,h,0,l,e.tokenPos,e.linePos,e.colPos))}return q(e,o&8?u|32768:u,1074790415),v(e,u,c,s,m,{type:"ClassBody",body:k})}function Cu(e,u,i,n,t,o,l,c,s,m,k){let h=l?32:0,d=null,{token:y,tokenPos:w,linePos:D,colPos:F}=e;if(y&176128)switch(d=$(e,u,0),y){case 36972:if(!l&&e.token!==67174411)return Cu(e,u,i,n,t,o,1,c,s,m,k);break;case 209007:if(e.token!==67174411&&(e.flags&1)<1){if(u&1&&(e.token&1073741824)===1073741824)return v1(e,u,d,h,o,w,D,F);h|=16|(M1(e,u,8457014)?8:0)}break;case 12402:if(e.token!==67174411){if(u&1&&(e.token&1073741824)===1073741824)return v1(e,u,d,h,o,w,D,F);h|=256}break;case 12403:if(e.token!==67174411){if(u&1&&(e.token&1073741824)===1073741824)return v1(e,u,d,h,o,w,D,F);h|=512}break}else y===69271571?(h|=2,d=K2(e,n,c)):(y&134217728)===134217728?d=c2(e,u):y===8457014?(h|=8,E(e,u)):u&1&&e.token===131?(h|=4096,d=r1(e,u|16384,w,D,F)):u&1&&(e.token&1073741824)===1073741824?h|=128:y===122?(d=$(e,u,0),e.token!==67174411&&f(e,28,Z[e.token&255])):f(e,28,Z[e.token&255]);if(h&792&&(e.token&143360?d=$(e,u,0):(e.token&134217728)===134217728?d=c2(e,u):e.token===69271571?(h|=2,d=K2(e,u,0)):e.token===122?d=$(e,u,0):u&1&&e.token===131?(h|=4096,d=r1(e,u,w,D,F)):f(e,131)),(h&2)<1&&(e.tokenValue==="constructor"?((e.token&1073741824)===1073741824?f(e,125):(h&32)<1&&e.token===67174411&&(h&920?f(e,50,"accessor"):(u&524288)<1&&(e.flags&32?f(e,51):e.flags|=32)),h|=64):(h&4096)<1&&h&824&&e.tokenValue==="prototype"&&f(e,49)),u&1&&e.token!==67174411)return v1(e,u,d,h,o,w,D,F);let O=v2(e,u,h,c,e.tokenPos,e.linePos,e.colPos);return v(e,u,s,m,k,u&1?{type:"MethodDefinition",kind:(h&32)<1&&h&64?"constructor":h&256?"get":h&512?"set":"method",static:(h&32)>0,computed:(h&2)>0,key:d,decorators:o,value:O}:{type:"MethodDefinition",kind:(h&32)<1&&h&64?"constructor":h&256?"get":h&512?"set":"method",static:(h&32)>0,computed:(h&2)>0,key:d,value:O})}function r1(e,u,i,n,t){E(e,u);let{tokenValue:o}=e;return o==="constructor"&&f(e,124),E(e,u),v(e,u,i,n,t,{type:"PrivateIdentifier",name:o})}function v1(e,u,i,n,t,o,l,c){let s=null;if(n&8&&f(e,0),e.token===1077936157){E(e,u|32768);let{tokenPos:m,linePos:k,colPos:h}=e;e.token===537079928&&f(e,115),s=d2(e,u|16384,2,0,1,0,0,1,m,k,h),(e.token&1073741824)!==1073741824&&(s=H(e,u|16384,s,0,0,m,k,h),s=Q(e,u|16384,0,0,m,k,h,s),e.token===18&&(s=O2(e,u,0,o,l,c,s)))}return v(e,u,o,l,c,{type:"PropertyDefinition",key:i,value:s,static:(n&32)>0,computed:(n&2)>0,decorators:t})}function Du(e,u,i,n,t,o,l,c){if(e.token&143360)return p1(e,u,i,n,t,o,l,c);(e.token&2097152)!==2097152&&f(e,28,Z[e.token&255]);let s=e.token===69271571?m2(e,u,i,1,0,1,n,t,o,l,c):b2(e,u,i,1,0,1,n,t,o,l,c);return e.destructible&16&&f(e,47),e.destructible&32&&f(e,47),s}function p1(e,u,i,n,t,o,l,c){let{tokenValue:s,token:m}=e;return u&1024&&((m&537079808)===537079808?f(e,115):(m&36864)===36864&&f(e,114)),(m&20480)===20480&&f(e,99),u&2099200&&m===241773&&f(e,30),m===241739&&n&24&&f(e,97),u&4196352&&m===209008&&f(e,95),E(e,u),i&&B2(e,u,i,s,n,t),v(e,u,o,l,c,{type:"Identifier",name:s})}function ee(e,u,i,n,t,o){if(E(e,u),e.token===8456259)return v(e,u,n,t,o,{type:"JSXFragment",openingFragment:jt(e,u,n,t,o),children:wu(e,u),closingFragment:Mt(e,u,i,e.tokenPos,e.linePos,e.colPos)});let l=null,c=[],s=$t(e,u,i,n,t,o);if(!s.selfClosing){c=wu(e,u),l=_t(e,u,i,e.tokenPos,e.linePos,e.colPos);let m=f1(l.name);f1(s.name)!==m&&f(e,149,m)}return v(e,u,n,t,o,{type:"JSXElement",children:c,openingElement:s,closingElement:l})}function jt(e,u,i,n,t){return j2(e,u),v(e,u,i,n,t,{type:"JSXOpeningFragment"})}function _t(e,u,i,n,t,o){q(e,u,25);let l=qu(e,u,e.tokenPos,e.linePos,e.colPos);return i?q(e,u,8456259):e.token=j2(e,u),v(e,u,n,t,o,{type:"JSXClosingElement",name:l})}function Mt(e,u,i,n,t,o){return q(e,u,25),q(e,u,8456259),v(e,u,n,t,o,{type:"JSXClosingFragment"})}function wu(e,u){let i=[];for(;e.token!==25;)e.index=e.tokenPos=e.startPos,e.column=e.colPos=e.startColumn,e.line=e.linePos=e.startLine,j2(e,u),i.push(Ut(e,u,e.tokenPos,e.linePos,e.colPos));return i}function Ut(e,u,i,n,t){if(e.token===138)return Jt(e,u,i,n,t);if(e.token===2162700)return Su(e,u,0,0,i,n,t);if(e.token===8456258)return ee(e,u,0,i,n,t);f(e,0)}function Jt(e,u,i,n,t){j2(e,u);let o={type:"JSXText",value:e.tokenValue};return u&512&&(o.raw=e.tokenRaw),v(e,u,i,n,t,o)}function $t(e,u,i,n,t,o){(e.token&143360)!==143360&&(e.token&4096)!==4096&&f(e,0);let l=qu(e,u,e.tokenPos,e.linePos,e.colPos),c=Xt(e,u),s=e.token===8457016;return e.token===8456259?j2(e,u):(q(e,u,8457016),i?q(e,u,8456259):j2(e,u)),v(e,u,n,t,o,{type:"JSXOpeningElement",name:l,attributes:c,selfClosing:s})}function qu(e,u,i,n,t){_1(e);let o=y1(e,u,i,n,t);if(e.token===21)return Bu(e,u,o,i,n,t);for(;M(e,u,67108877);)_1(e),o=Ht(e,u,o,i,n,t);return o}function Ht(e,u,i,n,t,o){let l=y1(e,u,e.tokenPos,e.linePos,e.colPos);return v(e,u,n,t,o,{type:"JSXMemberExpression",object:i,property:l})}function Xt(e,u){let i=[];for(;e.token!==8457016&&e.token!==8456259&&e.token!==1048576;)i.push(Wt(e,u,e.tokenPos,e.linePos,e.colPos));return i}function zt(e,u,i,n,t){E(e,u),q(e,u,14);let o=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return q(e,u,1074790415),v(e,u,i,n,t,{type:"JSXSpreadAttribute",argument:o})}function Wt(e,u,i,n,t){if(e.token===2162700)return zt(e,u,i,n,t);_1(e);let o=null,l=y1(e,u,i,n,t);if(e.token===21&&(l=Bu(e,u,l,i,n,t)),e.token===1077936157){let c=U0(e,u),{tokenPos:s,linePos:m,colPos:k}=e;switch(c){case 134283267:o=c2(e,u);break;case 8456258:o=ee(e,u,1,s,m,k);break;case 2162700:o=Su(e,u,1,1,s,m,k);break;default:f(e,148)}}return v(e,u,i,n,t,{type:"JSXAttribute",value:o,name:l})}function Bu(e,u,i,n,t,o){q(e,u,21);let l=y1(e,u,e.tokenPos,e.linePos,e.colPos);return v(e,u,n,t,o,{type:"JSXNamespacedName",namespace:i,name:l})}function Su(e,u,i,n,t,o,l){E(e,u|32768);let{tokenPos:c,linePos:s,colPos:m}=e;if(e.token===14)return Kt(e,u,c,s,m);let k=null;return e.token===1074790415?(n&&f(e,151),k=Yt(e,u,e.startPos,e.startLine,e.startColumn)):k=K(e,u,1,0,0,c,s,m),i?q(e,u,1074790415):j2(e,u),v(e,u,t,o,l,{type:"JSXExpressionContainer",expression:k})}function Kt(e,u,i,n,t){q(e,u,14);let o=K(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return q(e,u,1074790415),v(e,u,i,n,t,{type:"JSXSpreadChild",expression:o})}function Yt(e,u,i,n,t){return e.startPos=e.tokenPos,e.startLine=e.linePos,e.startColumn=e.colPos,v(e,u,i,n,t,{type:"JSXEmptyExpression"})}function y1(e,u,i,n,t){let{tokenValue:o}=e;return E(e,u),v(e,u,i,n,t,{type:"JSXIdentifier",name:o})}var Zt=Object.freeze({__proto__:null}),Qt="4.2.1",Gt=Qt;function xt(e,u){return H1(e,u,0)}function pt(e,u){return H1(e,u,3072)}function eo(e,u){return H1(e,u,0)}a.ESTree=Zt,a.parse=eo,a.parseModule=pt,a.parseScript=xt,a.version=Gt}});n2();var V3=k0(),N3=b3(),j3=q3(),_3=I3(),M3={module:!0,next:!0,ranges:!0,webcompat:!0,loc:!0,raw:!0,directives:!0,globalReturn:!0,impliedStrict:!1,preserveParens:!1,lexical:!1,identifierPattern:!1,jsx:!0,specDeviation:!0,uniqueKeyInPattern:!1};function m0(a,g){let{parse:b}=R3(),f=[],A=[],L=b(a,Object.assign(Object.assign({},M3),{},{module:g,onComment:f,onToken:A}));return L.comments=f,L.tokens=A,L}function U3(a){let{message:g,line:b,column:f}=a,A=(g.match(/^\[(?<line>\d+):(?<column>\d+)]: (?<message>.*)$/)||{}).groups;return A&&(g=A.message,typeof b!="number"&&(b=Number(A.line),f=Number(A.column))),typeof b!="number"?a:V3(g,{start:{line:b,column:f}})}function J3(a,g){let b=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{result:f,error:A}=N3(()=>m0(a,!0),()=>m0(a,!1));if(!f)throw U3(A);return b.originalText=a,_3(f,b)}O0.exports={parsers:{meriyah:j3(J3)}}});return $3();});