(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.angular=e()}})(function(){"use strict";var cr=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Br=cr((Mr,ar)=>{var ze=Object.defineProperty,ur=Object.getOwnPropertyDescriptor,He=Object.getOwnPropertyNames,lr=Object.prototype.hasOwnProperty,Y=(e,t)=>function(){return e&&(t=(0,e[He(e)[0]])(e=0)),t},q=(e,t)=>function(){return t||(0,e[He(e)[0]])((t={exports:{}}).exports,t),t.exports},Xe=(e,t)=>{for(var r in t)ze(e,r,{get:t[r],enumerable:!0})},hr=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of He(t))!lr.call(e,s)&&s!==r&&ze(e,s,{get:()=>t[s],enumerable:!(n=ur(t,s))||n.enumerable});return e},be=e=>hr(ze({},"__esModule",{value:!0}),e),L=Y({"<define:process>"(){}}),pr=q({"src/utils/is-non-empty-array.js"(e,t){"use strict";L();function r(n){return Array.isArray(n)&&n.length>0}t.exports=r}}),dr=q({"src/language-js/loc.js"(e,t){"use strict";L();var r=pr();function n(l){var P,p;let x=l.range?l.range[0]:l.start,C=(P=(p=l.declaration)===null||p===void 0?void 0:p.decorators)!==null&&P!==void 0?P:l.decorators;return r(C)?Math.min(n(C[0]),x):x}function s(l){return l.range?l.range[1]:l.end}function a(l,P){let p=n(l);return Number.isInteger(p)&&p===n(P)}function i(l,P){let p=s(l);return Number.isInteger(p)&&p===s(P)}function h(l,P){return a(l,P)&&i(l,P)}t.exports={locStart:n,locEnd:s,hasSameLocStart:a,hasSameLoc:h}}}),fr=q({"node_modules/angular-estree-parser/node_modules/lines-and-columns/build/index.js"(e){"use strict";L(),e.__esModule=!0,e.LinesAndColumns=void 0;var t=`
`,r="\r",n=function(){function s(a){this.string=a;for(var i=[0],h=0;h<a.length;)switch(a[h]){case t:h+=t.length,i.push(h);break;case r:h+=r.length,a[h]===t&&(h+=t.length),i.push(h);break;default:h++;break}this.offsets=i}return s.prototype.locationForIndex=function(a){if(a<0||a>this.string.length)return null;for(var i=0,h=this.offsets;h[i+1]<=a;)i++;var l=a-h[i];return{line:i,column:l}},s.prototype.indexForLocation=function(a){var i=a.line,h=a.column;return i<0||i>=this.offsets.length||h<0||h>this.lengthOfLine(i)?null:this.offsets[i]+h},s.prototype.lengthOfLine=function(a){var i=this.offsets[a],h=a===this.offsets.length-1?this.string.length:this.offsets[a+1];return h-i},s}();e.LinesAndColumns=n,e.default=n}}),gr=q({"node_modules/angular-estree-parser/lib/context.js"(e){"use strict";L(),Object.defineProperty(e,"__esModule",{value:!0}),e.Context=void 0;var t=fr(),r=class{constructor(s){this.text=s,this.locator=new n(this.text)}};e.Context=r;var n=class{constructor(s){this._lineAndColumn=new t.default(s)}locationForIndex(s){let{line:a,column:i}=this._lineAndColumn.locationForIndex(s);return{line:a+1,column:i}}}}}),Je={};Xe(Je,{AST:()=>k,ASTWithName:()=>W,ASTWithSource:()=>G,AbsoluteSourceSpan:()=>U,AstMemoryEfficientTransformer:()=>Ct,AstTransformer:()=>Pt,Binary:()=>B,BindingPipe:()=>fe,BoundElementProperty:()=>It,Chain:()=>oe,Conditional:()=>ce,EmptyExpr:()=>K,ExpressionBinding:()=>Ze,FunctionCall:()=>Pe,ImplicitReceiver:()=>Oe,Interpolation:()=>me,KeyedRead:()=>he,KeyedWrite:()=>de,LiteralArray:()=>ge,LiteralMap:()=>ve,LiteralPrimitive:()=>$,MethodCall:()=>ye,NonNullAssert:()=>Se,ParseSpan:()=>V,ParsedEvent:()=>At,ParsedProperty:()=>Et,ParsedPropertyType:()=>se,ParsedVariable:()=>_t,ParserError:()=>ae,PrefixNot:()=>xe,PropertyRead:()=>ne,PropertyWrite:()=>ue,Quote:()=>Le,RecursiveAstVisitor:()=>et,SafeKeyedRead:()=>pe,SafeMethodCall:()=>we,SafePropertyRead:()=>le,ThisReceiver:()=>Ye,Unary:()=>F,VariableBinding:()=>Re});var ae,V,k,W,Le,K,Oe,Ye,oe,ce,ne,ue,le,he,pe,de,fe,$,ge,ve,me,B,F,xe,Se,ye,we,Pe,U,G,Re,Ze,et,Pt,Ct,Et,se,At,_t,It,tt=Y({"node_modules/@angular/compiler/esm2015/src/expression_parser/ast.js"(){L(),ae=class{constructor(e,t,r,n){this.input=t,this.errLocation=r,this.ctxLocation=n,this.message=`Parser Error: ${e} ${r} [${t}] in ${n}`}},V=class{constructor(e,t){this.start=e,this.end=t}toAbsolute(e){return new U(e+this.start,e+this.end)}},k=class{constructor(e,t){this.span=e,this.sourceSpan=t}toString(){return"AST"}},W=class extends k{constructor(e,t,r){super(e,t),this.nameSpan=r}},Le=class extends k{constructor(e,t,r,n,s){super(e,t),this.prefix=r,this.uninterpretedExpression=n,this.location=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitQuote(this,t)}toString(){return"Quote"}},K=class extends k{visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null}},Oe=class extends k{visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitImplicitReceiver(this,t)}},Ye=class extends Oe{visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;var r;return(r=e.visitThisReceiver)===null||r===void 0?void 0:r.call(e,this,t)}},oe=class extends k{constructor(e,t,r){super(e,t),this.expressions=r}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitChain(this,t)}},ce=class extends k{constructor(e,t,r,n,s){super(e,t),this.condition=r,this.trueExp=n,this.falseExp=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitConditional(this,t)}},ne=class extends W{constructor(e,t,r,n,s){super(e,t,r),this.receiver=n,this.name=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitPropertyRead(this,t)}},ue=class extends W{constructor(e,t,r,n,s,a){super(e,t,r),this.receiver=n,this.name=s,this.value=a}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitPropertyWrite(this,t)}},le=class extends W{constructor(e,t,r,n,s){super(e,t,r),this.receiver=n,this.name=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitSafePropertyRead(this,t)}},he=class extends k{constructor(e,t,r,n){super(e,t),this.receiver=r,this.key=n}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitKeyedRead(this,t)}},pe=class extends k{constructor(e,t,r,n){super(e,t),this.receiver=r,this.key=n}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitSafeKeyedRead(this,t)}},de=class extends k{constructor(e,t,r,n,s){super(e,t),this.receiver=r,this.key=n,this.value=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitKeyedWrite(this,t)}},fe=class extends W{constructor(e,t,r,n,s,a){super(e,t,a),this.exp=r,this.name=n,this.args=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitPipe(this,t)}},$=class extends k{constructor(e,t,r){super(e,t),this.value=r}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitLiteralPrimitive(this,t)}},ge=class extends k{constructor(e,t,r){super(e,t),this.expressions=r}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitLiteralArray(this,t)}},ve=class extends k{constructor(e,t,r,n){super(e,t),this.keys=r,this.values=n}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitLiteralMap(this,t)}},me=class extends k{constructor(e,t,r,n){super(e,t),this.strings=r,this.expressions=n}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitInterpolation(this,t)}},B=class extends k{constructor(e,t,r,n,s){super(e,t),this.operation=r,this.left=n,this.right=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitBinary(this,t)}},F=class extends B{constructor(e,t,r,n,s,a,i){super(e,t,s,a,i),this.operator=r,this.expr=n}static createMinus(e,t,r){return new F(e,t,"-",r,"-",new $(e,t,0),r)}static createPlus(e,t,r){return new F(e,t,"+",r,"-",r,new $(e,t,0))}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitUnary!==void 0?e.visitUnary(this,t):e.visitBinary(this,t)}},xe=class extends k{constructor(e,t,r){super(e,t),this.expression=r}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitPrefixNot(this,t)}},Se=class extends k{constructor(e,t,r){super(e,t),this.expression=r}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitNonNullAssert(this,t)}},ye=class extends W{constructor(e,t,r,n,s,a,i){super(e,t,r),this.receiver=n,this.name=s,this.args=a,this.argumentSpan=i}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitMethodCall(this,t)}},we=class extends W{constructor(e,t,r,n,s,a,i){super(e,t,r),this.receiver=n,this.name=s,this.args=a,this.argumentSpan=i}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitSafeMethodCall(this,t)}},Pe=class extends k{constructor(e,t,r,n){super(e,t),this.target=r,this.args=n}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitFunctionCall(this,t)}},U=class{constructor(e,t){this.start=e,this.end=t}},G=class extends k{constructor(e,t,r,n,s){super(new V(0,t===null?0:t.length),new U(n,t===null?n:n+t.length)),this.ast=e,this.source=t,this.location=r,this.errors=s}visit(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.visitASTWithSource?e.visitASTWithSource(this,t):this.ast.visit(e,t)}toString(){return`${this.source} in ${this.location}`}},Re=class{constructor(e,t,r){this.sourceSpan=e,this.key=t,this.value=r}},Ze=class{constructor(e,t,r){this.sourceSpan=e,this.key=t,this.value=r}},et=class{visit(e,t){e.visit(this,t)}visitUnary(e,t){this.visit(e.expr,t)}visitBinary(e,t){this.visit(e.left,t),this.visit(e.right,t)}visitChain(e,t){this.visitAll(e.expressions,t)}visitConditional(e,t){this.visit(e.condition,t),this.visit(e.trueExp,t),this.visit(e.falseExp,t)}visitPipe(e,t){this.visit(e.exp,t),this.visitAll(e.args,t)}visitFunctionCall(e,t){e.target&&this.visit(e.target,t),this.visitAll(e.args,t)}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){this.visitAll(e.expressions,t)}visitKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitKeyedWrite(e,t){this.visit(e.receiver,t),this.visit(e.key,t),this.visit(e.value,t)}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitLiteralPrimitive(e,t){}visitMethodCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitPrefixNot(e,t){this.visit(e.expression,t)}visitNonNullAssert(e,t){this.visit(e.expression,t)}visitPropertyRead(e,t){this.visit(e.receiver,t)}visitPropertyWrite(e,t){this.visit(e.receiver,t),this.visit(e.value,t)}visitSafePropertyRead(e,t){this.visit(e.receiver,t)}visitSafeMethodCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitSafeKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitQuote(e,t){}visitAll(e,t){for(let r of e)this.visit(r,t)}},Pt=class{visitImplicitReceiver(e,t){return e}visitThisReceiver(e,t){return e}visitInterpolation(e,t){return new me(e.span,e.sourceSpan,e.strings,this.visitAll(e.expressions))}visitLiteralPrimitive(e,t){return new $(e.span,e.sourceSpan,e.value)}visitPropertyRead(e,t){return new ne(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name)}visitPropertyWrite(e,t){return new ue(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,e.value.visit(this))}visitSafePropertyRead(e,t){return new le(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name)}visitMethodCall(e,t){return new ye(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,this.visitAll(e.args),e.argumentSpan)}visitSafeMethodCall(e,t){return new we(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,this.visitAll(e.args),e.argumentSpan)}visitFunctionCall(e,t){return new Pe(e.span,e.sourceSpan,e.target.visit(this),this.visitAll(e.args))}visitLiteralArray(e,t){return new ge(e.span,e.sourceSpan,this.visitAll(e.expressions))}visitLiteralMap(e,t){return new ve(e.span,e.sourceSpan,e.keys,this.visitAll(e.values))}visitUnary(e,t){switch(e.operator){case"+":return F.createPlus(e.span,e.sourceSpan,e.expr.visit(this));case"-":return F.createMinus(e.span,e.sourceSpan,e.expr.visit(this));default:throw new Error(`Unknown unary operator ${e.operator}`)}}visitBinary(e,t){return new B(e.span,e.sourceSpan,e.operation,e.left.visit(this),e.right.visit(this))}visitPrefixNot(e,t){return new xe(e.span,e.sourceSpan,e.expression.visit(this))}visitNonNullAssert(e,t){return new Se(e.span,e.sourceSpan,e.expression.visit(this))}visitConditional(e,t){return new ce(e.span,e.sourceSpan,e.condition.visit(this),e.trueExp.visit(this),e.falseExp.visit(this))}visitPipe(e,t){return new fe(e.span,e.sourceSpan,e.exp.visit(this),e.name,this.visitAll(e.args),e.nameSpan)}visitKeyedRead(e,t){return new he(e.span,e.sourceSpan,e.receiver.visit(this),e.key.visit(this))}visitKeyedWrite(e,t){return new de(e.span,e.sourceSpan,e.receiver.visit(this),e.key.visit(this),e.value.visit(this))}visitAll(e){let t=[];for(let r=0;r<e.length;++r)t[r]=e[r].visit(this);return t}visitChain(e,t){return new oe(e.span,e.sourceSpan,this.visitAll(e.expressions))}visitQuote(e,t){return new Le(e.span,e.sourceSpan,e.prefix,e.uninterpretedExpression,e.location)}visitSafeKeyedRead(e,t){return new pe(e.span,e.sourceSpan,e.receiver.visit(this),e.key.visit(this))}},Ct=class{visitImplicitReceiver(e,t){return e}visitThisReceiver(e,t){return e}visitInterpolation(e,t){let r=this.visitAll(e.expressions);return r!==e.expressions?new me(e.span,e.sourceSpan,e.strings,r):e}visitLiteralPrimitive(e,t){return e}visitPropertyRead(e,t){let r=e.receiver.visit(this);return r!==e.receiver?new ne(e.span,e.sourceSpan,e.nameSpan,r,e.name):e}visitPropertyWrite(e,t){let r=e.receiver.visit(this),n=e.value.visit(this);return r!==e.receiver||n!==e.value?new ue(e.span,e.sourceSpan,e.nameSpan,r,e.name,n):e}visitSafePropertyRead(e,t){let r=e.receiver.visit(this);return r!==e.receiver?new le(e.span,e.sourceSpan,e.nameSpan,r,e.name):e}visitMethodCall(e,t){let r=e.receiver.visit(this),n=this.visitAll(e.args);return r!==e.receiver||n!==e.args?new ye(e.span,e.sourceSpan,e.nameSpan,r,e.name,n,e.argumentSpan):e}visitSafeMethodCall(e,t){let r=e.receiver.visit(this),n=this.visitAll(e.args);return r!==e.receiver||n!==e.args?new we(e.span,e.sourceSpan,e.nameSpan,r,e.name,n,e.argumentSpan):e}visitFunctionCall(e,t){let r=e.target&&e.target.visit(this),n=this.visitAll(e.args);return r!==e.target||n!==e.args?new Pe(e.span,e.sourceSpan,r,n):e}visitLiteralArray(e,t){let r=this.visitAll(e.expressions);return r!==e.expressions?new ge(e.span,e.sourceSpan,r):e}visitLiteralMap(e,t){let r=this.visitAll(e.values);return r!==e.values?new ve(e.span,e.sourceSpan,e.keys,r):e}visitUnary(e,t){let r=e.expr.visit(this);if(r!==e.expr)switch(e.operator){case"+":return F.createPlus(e.span,e.sourceSpan,r);case"-":return F.createMinus(e.span,e.sourceSpan,r);default:throw new Error(`Unknown unary operator ${e.operator}`)}return e}visitBinary(e,t){let r=e.left.visit(this),n=e.right.visit(this);return r!==e.left||n!==e.right?new B(e.span,e.sourceSpan,e.operation,r,n):e}visitPrefixNot(e,t){let r=e.expression.visit(this);return r!==e.expression?new xe(e.span,e.sourceSpan,r):e}visitNonNullAssert(e,t){let r=e.expression.visit(this);return r!==e.expression?new Se(e.span,e.sourceSpan,r):e}visitConditional(e,t){let r=e.condition.visit(this),n=e.trueExp.visit(this),s=e.falseExp.visit(this);return r!==e.condition||n!==e.trueExp||s!==e.falseExp?new ce(e.span,e.sourceSpan,r,n,s):e}visitPipe(e,t){let r=e.exp.visit(this),n=this.visitAll(e.args);return r!==e.exp||n!==e.args?new fe(e.span,e.sourceSpan,r,e.name,n,e.nameSpan):e}visitKeyedRead(e,t){let r=e.receiver.visit(this),n=e.key.visit(this);return r!==e.receiver||n!==e.key?new he(e.span,e.sourceSpan,r,n):e}visitKeyedWrite(e,t){let r=e.receiver.visit(this),n=e.key.visit(this),s=e.value.visit(this);return r!==e.receiver||n!==e.key||s!==e.value?new de(e.span,e.sourceSpan,r,n,s):e}visitAll(e){let t=[],r=!1;for(let n=0;n<e.length;++n){let s=e[n],a=s.visit(this);t[n]=a,r=r||a!==s}return r?t:e}visitChain(e,t){let r=this.visitAll(e.expressions);return r!==e.expressions?new oe(e.span,e.sourceSpan,r):e}visitQuote(e,t){return e}visitSafeKeyedRead(e,t){let r=e.receiver.visit(this),n=e.key.visit(this);return r!==e.receiver||n!==e.key?new pe(e.span,e.sourceSpan,r,n):e}},Et=class{constructor(e,t,r,n,s,a){this.name=e,this.expression=t,this.type=r,this.sourceSpan=n,this.keySpan=s,this.valueSpan=a,this.isLiteral=this.type===se.LITERAL_ATTR,this.isAnimation=this.type===se.ANIMATION}},function(e){e[e.DEFAULT=0]="DEFAULT",e[e.LITERAL_ATTR=1]="LITERAL_ATTR",e[e.ANIMATION=2]="ANIMATION"}(se||(se={})),At=class{constructor(e,t,r,n,s,a,i){this.name=e,this.targetOrPhase=t,this.type=r,this.handler=n,this.sourceSpan=s,this.handlerSpan=a,this.keySpan=i}},_t=class{constructor(e,t,r,n,s){this.name=e,this.value=t,this.sourceSpan=r,this.keySpan=n,this.valueSpan=s}},It=class{constructor(e,t,r,n,s,a,i,h){this.name=e,this.type=t,this.securityContext=r,this.value=n,this.unit=s,this.sourceSpan=a,this.keySpan=i,this.valueSpan=h}}}});function vr(e){return e>=rt&&e<=nt||e==dt}function Q(e){return Mt<=e&&e<=jt}function mr(e){return e>=ht&&e<=pt||e>=ut&&e<=lt}function mt(e){return e===at||e===st||e===Xt}var Ce,rt,Ot,kt,Nt,bt,nt,Lt,st,Rt,it,Tt,je,at,Ee,z,$t,ot,ee,ct,H,Te,X,te,Bt,ie,Kt,Fe,Mt,jt,ut,Ft,lt,Ae,Ut,re,Wt,Be,ht,Gt,Vt,qt,Qt,Dt,zt,Ht,pt,$e,Ue,_e,dt,Xt,Jt=Y({"node_modules/@angular/compiler/esm2015/src/chars.js"(){L(),Ce=0,rt=9,Ot=10,kt=11,Nt=12,bt=13,nt=32,Lt=33,st=34,Rt=35,it=36,Tt=37,je=38,at=39,Ee=40,z=41,$t=42,ot=43,ee=44,ct=45,H=46,Te=47,X=58,te=59,Bt=60,ie=61,Kt=62,Fe=63,Mt=48,jt=57,ut=65,Ft=69,lt=90,Ae=91,Ut=92,re=93,Wt=94,Be=95,ht=97,Gt=101,Vt=102,qt=110,Qt=114,Dt=116,zt=117,Ht=118,pt=122,$e=123,Ue=124,_e=125,dt=160,Xt=96}}),Yt={};Xe(Yt,{EOF:()=>Ie,Lexer:()=>er,Token:()=>M,TokenType:()=>S,isIdentifier:()=>Zt});function xt(e,t,r){return new M(e,t,S.Character,r,String.fromCharCode(r))}function xr(e,t,r){return new M(e,t,S.Identifier,0,r)}function Sr(e,t,r){return new M(e,t,S.PrivateIdentifier,0,r)}function yr(e,t,r){return new M(e,t,S.Keyword,0,r)}function Ke(e,t,r){return new M(e,t,S.Operator,0,r)}function wr(e,t,r){return new M(e,t,S.String,0,r)}function Pr(e,t,r){return new M(e,t,S.Number,r,"")}function Cr(e,t,r){return new M(e,t,S.Error,0,r)}function We(e){return ht<=e&&e<=pt||ut<=e&&e<=lt||e==Be||e==it}function Zt(e){if(e.length==0)return!1;let t=new Ve(e);if(!We(t.peek))return!1;for(t.advance();t.peek!==Ce;){if(!Ge(t.peek))return!1;t.advance()}return!0}function Ge(e){return mr(e)||Q(e)||e==Be||e==it}function Er(e){return e==Gt||e==Ft}function Ar(e){return e==ct||e==ot}function _r(e){switch(e){case qt:return Ot;case Vt:return Nt;case Qt:return bt;case Dt:return rt;case Ht:return kt;default:return e}}function Ir(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}var S,St,er,M,Ie,Ve,tr=Y({"node_modules/@angular/compiler/esm2015/src/expression_parser/lexer.js"(){L(),Jt(),function(e){e[e.Character=0]="Character",e[e.Identifier=1]="Identifier",e[e.PrivateIdentifier=2]="PrivateIdentifier",e[e.Keyword=3]="Keyword",e[e.String=4]="String",e[e.Operator=5]="Operator",e[e.Number=6]="Number",e[e.Error=7]="Error"}(S||(S={})),St=["var","let","as","null","undefined","true","false","if","else","this"],er=class{tokenize(e){let t=new Ve(e),r=[],n=t.scanToken();for(;n!=null;)r.push(n),n=t.scanToken();return r}},M=class{constructor(e,t,r,n,s){this.index=e,this.end=t,this.type=r,this.numValue=n,this.strValue=s}isCharacter(e){return this.type==S.Character&&this.numValue==e}isNumber(){return this.type==S.Number}isString(){return this.type==S.String}isOperator(e){return this.type==S.Operator&&this.strValue==e}isIdentifier(){return this.type==S.Identifier}isPrivateIdentifier(){return this.type==S.PrivateIdentifier}isKeyword(){return this.type==S.Keyword}isKeywordLet(){return this.type==S.Keyword&&this.strValue=="let"}isKeywordAs(){return this.type==S.Keyword&&this.strValue=="as"}isKeywordNull(){return this.type==S.Keyword&&this.strValue=="null"}isKeywordUndefined(){return this.type==S.Keyword&&this.strValue=="undefined"}isKeywordTrue(){return this.type==S.Keyword&&this.strValue=="true"}isKeywordFalse(){return this.type==S.Keyword&&this.strValue=="false"}isKeywordThis(){return this.type==S.Keyword&&this.strValue=="this"}isError(){return this.type==S.Error}toNumber(){return this.type==S.Number?this.numValue:-1}toString(){switch(this.type){case S.Character:case S.Identifier:case S.Keyword:case S.Operator:case S.PrivateIdentifier:case S.String:case S.Error:return this.strValue;case S.Number:return this.numValue.toString();default:return null}}},Ie=new M(-1,-1,S.Character,0,""),Ve=class{constructor(e){this.input=e,this.peek=0,this.index=-1,this.length=e.length,this.advance()}advance(){this.peek=++this.index>=this.length?Ce:this.input.charCodeAt(this.index)}scanToken(){let e=this.input,t=this.length,r=this.peek,n=this.index;for(;r<=nt;)if(++n>=t){r=Ce;break}else r=e.charCodeAt(n);if(this.peek=r,this.index=n,n>=t)return null;if(We(r))return this.scanIdentifier();if(Q(r))return this.scanNumber(n);let s=n;switch(r){case H:return this.advance(),Q(this.peek)?this.scanNumber(s):xt(s,this.index,H);case Ee:case z:case $e:case _e:case Ae:case re:case ee:case X:case te:return this.scanCharacter(s,r);case at:case st:return this.scanString();case Rt:return this.scanPrivateIdentifier();case ot:case ct:case $t:case Te:case Tt:case Wt:return this.scanOperator(s,String.fromCharCode(r));case Fe:return this.scanQuestion(s);case Bt:case Kt:return this.scanComplexOperator(s,String.fromCharCode(r),ie,"=");case Lt:case ie:return this.scanComplexOperator(s,String.fromCharCode(r),ie,"=",ie,"=");case je:return this.scanComplexOperator(s,"&",je,"&");case Ue:return this.scanComplexOperator(s,"|",Ue,"|");case dt:for(;vr(this.peek);)this.advance();return this.scanToken()}return this.advance(),this.error(`Unexpected character [${String.fromCharCode(r)}]`,0)}scanCharacter(e,t){return this.advance(),xt(e,this.index,t)}scanOperator(e,t){return this.advance(),Ke(e,this.index,t)}scanComplexOperator(e,t,r,n,s,a){this.advance();let i=t;return this.peek==r&&(this.advance(),i+=n),s!=null&&this.peek==s&&(this.advance(),i+=a),Ke(e,this.index,i)}scanIdentifier(){let e=this.index;for(this.advance();Ge(this.peek);)this.advance();let t=this.input.substring(e,this.index);return St.indexOf(t)>-1?yr(e,this.index,t):xr(e,this.index,t)}scanPrivateIdentifier(){let e=this.index;if(this.advance(),!We(this.peek))return this.error("Invalid character [#]",-1);for(;Ge(this.peek);)this.advance();let t=this.input.substring(e,this.index);return Sr(e,this.index,t)}scanNumber(e){let t=this.index===e,r=!1;for(this.advance();;){if(!Q(this.peek))if(this.peek===Be){if(!Q(this.input.charCodeAt(this.index-1))||!Q(this.input.charCodeAt(this.index+1)))return this.error("Invalid numeric separator",0);r=!0}else if(this.peek===H)t=!1;else if(Er(this.peek)){if(this.advance(),Ar(this.peek)&&this.advance(),!Q(this.peek))return this.error("Invalid exponent",-1);t=!1}else break;this.advance()}let n=this.input.substring(e,this.index);r&&(n=n.replace(/_/g,""));let s=t?Ir(n):parseFloat(n);return Pr(e,this.index,s)}scanString(){let e=this.index,t=this.peek;this.advance();let r="",n=this.index,s=this.input;for(;this.peek!=t;)if(this.peek==Ut){r+=s.substring(n,this.index),this.advance();let i;if(this.peek=this.peek,this.peek==zt){let h=s.substring(this.index+1,this.index+5);if(/^[0-9a-f]+$/i.test(h))i=parseInt(h,16);else return this.error(`Invalid unicode escape [\\u${h}]`,0);for(let l=0;l<5;l++)this.advance()}else i=_r(this.peek),this.advance();r+=String.fromCharCode(i),n=this.index}else{if(this.peek==Ce)return this.error("Unterminated quote",0);this.advance()}let a=s.substring(n,this.index);return this.advance(),wr(e,this.index,r+a)}scanQuestion(e){this.advance();let t="?";return(this.peek===Fe||this.peek===H)&&(t+=this.peek===H?".":"?",this.advance()),Ke(e,this.index,t)}error(e,t){let r=this.index+t;return Cr(r,this.index,`Lexer Error: ${e} at column ${r} in expression [${this.input}]`)}}}});function Or(e,t){if(t!=null&&!(Array.isArray(t)&&t.length==2))throw new Error(`Expected '${e}' to be an array, [start, end].`);if(t!=null){let r=t[0],n=t[1];rr.forEach(s=>{if(s.test(r)||s.test(n))throw new Error(`['${r}', '${n}'] contains unusable interpolation symbol.`)})}}var rr,kr=Y({"node_modules/@angular/compiler/esm2015/src/assertions.js"(){L(),rr=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//]}}),Me,J,Nr=Y({"node_modules/@angular/compiler/esm2015/src/ml_parser/interpolation_config.js"(){L(),kr(),Me=class{constructor(e,t){this.start=e,this.end=t}static fromArray(e){return e?(Or("interpolation",e),new Me(e[0],e[1])):J}},J=new Me("{{","}}")}}),nr={};Xe(nr,{IvyParser:()=>sr,Parser:()=>De,SplitInterpolation:()=>qe,TemplateBindingParseResult:()=>Qe,_ParseAST:()=>D});var qe,Qe,De,sr,Z,D,yt,wt,br=Y({"node_modules/@angular/compiler/esm2015/src/expression_parser/parser.js"(){L(),Jt(),Nr(),tt(),tr(),qe=class{constructor(e,t,r){this.strings=e,this.expressions=t,this.offsets=r}},Qe=class{constructor(e,t,r){this.templateBindings=e,this.warnings=t,this.errors=r}},De=class{constructor(e){this._lexer=e,this.errors=[],this.simpleExpressionChecker=yt}parseAction(e,t,r){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:J;this._checkNoInterpolation(e,t,n);let s=this._stripComments(e),a=this._lexer.tokenize(this._stripComments(e)),i=new D(e,t,r,a,s.length,!0,this.errors,e.length-s.length).parseChain();return new G(i,e,t,r,this.errors)}parseBinding(e,t,r){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:J,s=this._parseBindingAst(e,t,r,n);return new G(s,e,t,r,this.errors)}checkSimpleExpression(e){let t=new this.simpleExpressionChecker;return e.visit(t),t.errors}parseSimpleBinding(e,t,r){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:J,s=this._parseBindingAst(e,t,r,n),a=this.checkSimpleExpression(s);return a.length>0&&this._reportError(`Host binding expression cannot contain ${a.join(" ")}`,e,t),new G(s,e,t,r,this.errors)}_reportError(e,t,r,n){this.errors.push(new ae(e,t,r,n))}_parseBindingAst(e,t,r,n){let s=this._parseQuote(e,t,r);if(s!=null)return s;this._checkNoInterpolation(e,t,n);let a=this._stripComments(e),i=this._lexer.tokenize(a);return new D(e,t,r,i,a.length,!1,this.errors,e.length-a.length).parseChain()}_parseQuote(e,t,r){if(e==null)return null;let n=e.indexOf(":");if(n==-1)return null;let s=e.substring(0,n).trim();if(!Zt(s))return null;let a=e.substring(n+1),i=new V(0,e.length);return new Le(i,i.toAbsolute(r),s,a,t)}parseTemplateBindings(e,t,r,n,s){let a=this._lexer.tokenize(t);return new D(t,r,s,a,t.length,!1,this.errors,0).parseTemplateBindings({source:e,span:new U(n,n+e.length)})}parseInterpolation(e,t,r){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:J,{strings:s,expressions:a,offsets:i}=this.splitInterpolation(e,t,n);if(a.length===0)return null;let h=[];for(let l=0;l<a.length;++l){let P=a[l].text,p=this._stripComments(P),x=this._lexer.tokenize(p),C=new D(e,t,r,x,p.length,!1,this.errors,i[l]+(P.length-p.length)).parseChain();h.push(C)}return this.createInterpolationAst(s.map(l=>l.text),h,e,t,r)}parseInterpolationExpression(e,t,r){let n=this._stripComments(e),s=this._lexer.tokenize(n),a=new D(e,t,r,s,n.length,!1,this.errors,0).parseChain(),i=["",""];return this.createInterpolationAst(i,[a],e,t,r)}createInterpolationAst(e,t,r,n,s){let a=new V(0,r.length),i=new me(a,a.toAbsolute(s),e,t);return new G(i,r,n,s,this.errors)}splitInterpolation(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:J,n=[],s=[],a=[],i=0,h=!1,l=!1,{start:P,end:p}=r;for(;i<e.length;)if(h){let x=i,C=x+P.length,b=this._getInterpolationEndIndex(e,p,C);if(b===-1){h=!1,l=!0;break}let _=b+p.length,R=e.substring(C,b);R.trim().length===0&&this._reportError("Blank expressions are not allowed in interpolated strings",e,`at column ${i} in`,t),s.push({text:R,start:x,end:_}),a.push(C),i=_,h=!1}else{let x=i;i=e.indexOf(P,i),i===-1&&(i=e.length);let C=e.substring(x,i);n.push({text:C,start:x,end:i}),h=!0}if(!h)if(l){let x=n[n.length-1];x.text+=e.substring(i),x.end=e.length}else n.push({text:e.substring(i),start:i,end:e.length});return new qe(n,s,a)}wrapLiteralPrimitive(e,t,r){let n=new V(0,e==null?0:e.length);return new G(new $(n,n.toAbsolute(r),e),e,t,r,this.errors)}_stripComments(e){let t=this._commentStart(e);return t!=null?e.substring(0,t).trim():e}_commentStart(e){let t=null;for(let r=0;r<e.length-1;r++){let n=e.charCodeAt(r),s=e.charCodeAt(r+1);if(n===Te&&s==Te&&t==null)return r;t===n?t=null:t==null&&mt(n)&&(t=n)}return null}_checkNoInterpolation(e,t,r){let{start:n,end:s}=r,a=-1,i=-1;for(let h of this._forEachUnquotedChar(e,0))if(a===-1)e.startsWith(n)&&(a=h);else if(i=this._getInterpolationEndIndex(e,s,h),i>-1)break;a>-1&&i>-1&&this._reportError(`Got interpolation (${n}${s}) where expression was expected`,e,`at column ${a} in`,t)}_getInterpolationEndIndex(e,t,r){for(let n of this._forEachUnquotedChar(e,r)){if(e.startsWith(t,n))return n;if(e.startsWith("//",n))return e.indexOf(t,n)}return-1}*_forEachUnquotedChar(e,t){let r=null,n=0;for(let s=t;s<e.length;s++){let a=e[s];mt(e.charCodeAt(s))&&(r===null||r===a)&&n%2===0?r=r===null?a:null:r===null&&(yield s),n=a==="\\"?n+1:0}}},sr=class extends De{constructor(){super(...arguments),this.simpleExpressionChecker=wt}},function(e){e[e.None=0]="None",e[e.Writable=1]="Writable"}(Z||(Z={})),D=class{constructor(e,t,r,n,s,a,i,h){this.input=e,this.location=t,this.absoluteOffset=r,this.tokens=n,this.inputLength=s,this.parseAction=a,this.errors=i,this.offset=h,this.rparensExpected=0,this.rbracketsExpected=0,this.rbracesExpected=0,this.context=Z.None,this.sourceSpanCache=new Map,this.index=0}peek(e){let t=this.index+e;return t<this.tokens.length?this.tokens[t]:Ie}get next(){return this.peek(0)}get atEOF(){return this.index>=this.tokens.length}get inputIndex(){return this.atEOF?this.currentEndIndex:this.next.index+this.offset}get currentEndIndex(){return this.index>0?this.peek(-1).end+this.offset:this.tokens.length===0?this.inputLength+this.offset:this.next.index+this.offset}get currentAbsoluteOffset(){return this.absoluteOffset+this.inputIndex}span(e,t){let r=this.currentEndIndex;if(t!==void 0&&t>this.currentEndIndex&&(r=t),e>r){let n=r;r=e,e=n}return new V(e,r)}sourceSpan(e,t){let r=`${e}@${this.inputIndex}:${t}`;return this.sourceSpanCache.has(r)||this.sourceSpanCache.set(r,this.span(e,t).toAbsolute(this.absoluteOffset)),this.sourceSpanCache.get(r)}advance(){this.index++}withContext(e,t){this.context|=e;let r=t();return this.context^=e,r}consumeOptionalCharacter(e){return this.next.isCharacter(e)?(this.advance(),!0):!1}peekKeywordLet(){return this.next.isKeywordLet()}peekKeywordAs(){return this.next.isKeywordAs()}expectCharacter(e){this.consumeOptionalCharacter(e)||this.error(`Missing expected ${String.fromCharCode(e)}`)}consumeOptionalOperator(e){return this.next.isOperator(e)?(this.advance(),!0):!1}expectOperator(e){this.consumeOptionalOperator(e)||this.error(`Missing expected operator ${e}`)}prettyPrintToken(e){return e===Ie?"end of input":`token ${e}`}expectIdentifierOrKeyword(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier or keyword"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier or keyword`),null):(this.advance(),e.toString())}expectIdentifierOrKeywordOrString(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()&&!e.isString()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier, keyword or string"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier, keyword, or string`),""):(this.advance(),e.toString())}parseChain(){let e=[],t=this.inputIndex;for(;this.index<this.tokens.length;){let r=this.parsePipe();if(e.push(r),this.consumeOptionalCharacter(te))for(this.parseAction||this.error("Binding expression cannot contain chained expression");this.consumeOptionalCharacter(te););else this.index<this.tokens.length&&this.error(`Unexpected token '${this.next}'`)}if(e.length==0){let r=this.offset,n=this.offset+this.inputLength;return new K(this.span(r,n),this.sourceSpan(r,n))}return e.length==1?e[0]:new oe(this.span(t),this.sourceSpan(t),e)}parsePipe(){let e=this.inputIndex,t=this.parseExpression();if(this.consumeOptionalOperator("|")){this.parseAction&&this.error("Cannot have a pipe in an action expression");do{let r=this.inputIndex,n=this.expectIdentifierOrKeyword(),s,a;n!==null?s=this.sourceSpan(r):(n="",a=this.next.index!==-1?this.next.index:this.inputLength+this.offset,s=new V(a,a).toAbsolute(this.absoluteOffset));let i=[];for(;this.consumeOptionalCharacter(X);)i.push(this.parseExpression());t=new fe(this.span(e),this.sourceSpan(e,a),t,n,i,s)}while(this.consumeOptionalOperator("|"))}return t}parseExpression(){return this.parseConditional()}parseConditional(){let e=this.inputIndex,t=this.parseLogicalOr();if(this.consumeOptionalOperator("?")){let r=this.parsePipe(),n;if(this.consumeOptionalCharacter(X))n=this.parsePipe();else{let s=this.inputIndex,a=this.input.substring(e,s);this.error(`Conditional expression ${a} requires all 3 expressions`),n=new K(this.span(e),this.sourceSpan(e))}return new ce(this.span(e),this.sourceSpan(e),t,r,n)}else return t}parseLogicalOr(){let e=this.inputIndex,t=this.parseLogicalAnd();for(;this.consumeOptionalOperator("||");){let r=this.parseLogicalAnd();t=new B(this.span(e),this.sourceSpan(e),"||",t,r)}return t}parseLogicalAnd(){let e=this.inputIndex,t=this.parseNullishCoalescing();for(;this.consumeOptionalOperator("&&");){let r=this.parseNullishCoalescing();t=new B(this.span(e),this.sourceSpan(e),"&&",t,r)}return t}parseNullishCoalescing(){let e=this.inputIndex,t=this.parseEquality();for(;this.consumeOptionalOperator("??");){let r=this.parseEquality();t=new B(this.span(e),this.sourceSpan(e),"??",t,r)}return t}parseEquality(){let e=this.inputIndex,t=this.parseRelational();for(;this.next.type==S.Operator;){let r=this.next.strValue;switch(r){case"==":case"===":case"!=":case"!==":this.advance();let n=this.parseRelational();t=new B(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseRelational(){let e=this.inputIndex,t=this.parseAdditive();for(;this.next.type==S.Operator;){let r=this.next.strValue;switch(r){case"<":case">":case"<=":case">=":this.advance();let n=this.parseAdditive();t=new B(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseAdditive(){let e=this.inputIndex,t=this.parseMultiplicative();for(;this.next.type==S.Operator;){let r=this.next.strValue;switch(r){case"+":case"-":this.advance();let n=this.parseMultiplicative();t=new B(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseMultiplicative(){let e=this.inputIndex,t=this.parsePrefix();for(;this.next.type==S.Operator;){let r=this.next.strValue;switch(r){case"*":case"%":case"/":this.advance();let n=this.parsePrefix();t=new B(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parsePrefix(){if(this.next.type==S.Operator){let e=this.inputIndex,t=this.next.strValue,r;switch(t){case"+":return this.advance(),r=this.parsePrefix(),F.createPlus(this.span(e),this.sourceSpan(e),r);case"-":return this.advance(),r=this.parsePrefix(),F.createMinus(this.span(e),this.sourceSpan(e),r);case"!":return this.advance(),r=this.parsePrefix(),new xe(this.span(e),this.sourceSpan(e),r)}}return this.parseCallChain()}parseCallChain(){let e=this.inputIndex,t=this.parsePrimary();for(;;)if(this.consumeOptionalCharacter(H))t=this.parseAccessMemberOrMethodCall(t,e,!1);else if(this.consumeOptionalOperator("?."))t=this.consumeOptionalCharacter(Ae)?this.parseKeyedReadOrWrite(t,e,!0):this.parseAccessMemberOrMethodCall(t,e,!0);else if(this.consumeOptionalCharacter(Ae))t=this.parseKeyedReadOrWrite(t,e,!1);else if(this.consumeOptionalCharacter(Ee)){this.rparensExpected++;let r=this.parseCallArguments();this.rparensExpected--,this.expectCharacter(z),t=new Pe(this.span(e),this.sourceSpan(e),t,r)}else if(this.consumeOptionalOperator("!"))t=new Se(this.span(e),this.sourceSpan(e),t);else return t}parsePrimary(){let e=this.inputIndex;if(this.consumeOptionalCharacter(Ee)){this.rparensExpected++;let t=this.parsePipe();return this.rparensExpected--,this.expectCharacter(z),t}else{if(this.next.isKeywordNull())return this.advance(),new $(this.span(e),this.sourceSpan(e),null);if(this.next.isKeywordUndefined())return this.advance(),new $(this.span(e),this.sourceSpan(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new $(this.span(e),this.sourceSpan(e),!0);if(this.next.isKeywordFalse())return this.advance(),new $(this.span(e),this.sourceSpan(e),!1);if(this.next.isKeywordThis())return this.advance(),new Ye(this.span(e),this.sourceSpan(e));if(this.consumeOptionalCharacter(Ae)){this.rbracketsExpected++;let t=this.parseExpressionList(re);return this.rbracketsExpected--,this.expectCharacter(re),new ge(this.span(e),this.sourceSpan(e),t)}else{if(this.next.isCharacter($e))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMemberOrMethodCall(new Oe(this.span(e),this.sourceSpan(e)),e,!1);if(this.next.isNumber()){let t=this.next.toNumber();return this.advance(),new $(this.span(e),this.sourceSpan(e),t)}else if(this.next.isString()){let t=this.next.toString();return this.advance(),new $(this.span(e),this.sourceSpan(e),t)}else return this.next.isPrivateIdentifier()?(this._reportErrorForPrivateIdentifier(this.next,null),new K(this.span(e),this.sourceSpan(e))):this.index>=this.tokens.length?(this.error(`Unexpected end of expression: ${this.input}`),new K(this.span(e),this.sourceSpan(e))):(this.error(`Unexpected token ${this.next}`),new K(this.span(e),this.sourceSpan(e)))}}}parseExpressionList(e){let t=[];do if(!this.next.isCharacter(e))t.push(this.parsePipe());else break;while(this.consumeOptionalCharacter(ee));return t}parseLiteralMap(){let e=[],t=[],r=this.inputIndex;if(this.expectCharacter($e),!this.consumeOptionalCharacter(_e)){this.rbracesExpected++;do{let n=this.inputIndex,s=this.next.isString(),a=this.expectIdentifierOrKeywordOrString();if(e.push({key:a,quoted:s}),s)this.expectCharacter(X),t.push(this.parsePipe());else if(this.consumeOptionalCharacter(X))t.push(this.parsePipe());else{let i=this.span(n),h=this.sourceSpan(n);t.push(new ne(i,h,h,new Oe(i,h),a))}}while(this.consumeOptionalCharacter(ee));this.rbracesExpected--,this.expectCharacter(_e)}return new ve(this.span(r),this.sourceSpan(r),e,t)}parseAccessMemberOrMethodCall(e,t,r){let n=this.inputIndex,s=this.withContext(Z.Writable,()=>{var i;let h=(i=this.expectIdentifierOrKeyword())!==null&&i!==void 0?i:"";return h.length===0&&this.error("Expected identifier for property access",e.span.end),h}),a=this.sourceSpan(n);if(this.consumeOptionalCharacter(Ee)){let i=this.inputIndex;this.rparensExpected++;let h=this.parseCallArguments(),l=this.span(i,this.inputIndex).toAbsolute(this.absoluteOffset);this.expectCharacter(z),this.rparensExpected--;let P=this.span(t),p=this.sourceSpan(t);return r?new we(P,p,a,e,s,h,l):new ye(P,p,a,e,s,h,l)}else{if(r)return this.consumeOptionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),new K(this.span(t),this.sourceSpan(t))):new le(this.span(t),this.sourceSpan(t),a,e,s);if(this.consumeOptionalOperator("=")){if(!this.parseAction)return this.error("Bindings cannot contain assignments"),new K(this.span(t),this.sourceSpan(t));let i=this.parseConditional();return new ue(this.span(t),this.sourceSpan(t),a,e,s,i)}else return new ne(this.span(t),this.sourceSpan(t),a,e,s)}}parseCallArguments(){if(this.next.isCharacter(z))return[];let e=[];do e.push(this.parsePipe());while(this.consumeOptionalCharacter(ee));return e}expectTemplateBindingKey(){let e="",t=!1,r=this.currentAbsoluteOffset;do e+=this.expectIdentifierOrKeywordOrString(),t=this.consumeOptionalOperator("-"),t&&(e+="-");while(t);return{source:e,span:new U(r,r+e.length)}}parseTemplateBindings(e){let t=[];for(t.push(...this.parseDirectiveKeywordBindings(e));this.index<this.tokens.length;){let r=this.parseLetBinding();if(r)t.push(r);else{let n=this.expectTemplateBindingKey(),s=this.parseAsBinding(n);s?t.push(s):(n.source=e.source+n.source.charAt(0).toUpperCase()+n.source.substring(1),t.push(...this.parseDirectiveKeywordBindings(n)))}this.consumeStatementTerminator()}return new Qe(t,[],this.errors)}parseKeyedReadOrWrite(e,t,r){return this.withContext(Z.Writable,()=>{this.rbracketsExpected++;let n=this.parsePipe();if(n instanceof K&&this.error("Key access cannot be empty"),this.rbracketsExpected--,this.expectCharacter(re),this.consumeOptionalOperator("="))if(r)this.error("The '?.' operator cannot be used in the assignment");else{let s=this.parseConditional();return new de(this.span(t),this.sourceSpan(t),e,n,s)}else return r?new pe(this.span(t),this.sourceSpan(t),e,n):new he(this.span(t),this.sourceSpan(t),e,n);return new K(this.span(t),this.sourceSpan(t))})}parseDirectiveKeywordBindings(e){let t=[];this.consumeOptionalCharacter(X);let r=this.getDirectiveBoundTarget(),n=this.currentAbsoluteOffset,s=this.parseAsBinding(e);s||(this.consumeStatementTerminator(),n=this.currentAbsoluteOffset);let a=new U(e.span.start,n);return t.push(new Ze(a,e,r)),s&&t.push(s),t}getDirectiveBoundTarget(){if(this.next===Ie||this.peekKeywordAs()||this.peekKeywordLet())return null;let e=this.parsePipe(),{start:t,end:r}=e.span,n=this.input.substring(t,r);return new G(e,n,this.location,this.absoluteOffset+t,this.errors)}parseAsBinding(e){if(!this.peekKeywordAs())return null;this.advance();let t=this.expectTemplateBindingKey();this.consumeStatementTerminator();let r=new U(e.span.start,this.currentAbsoluteOffset);return new Re(r,t,e)}parseLetBinding(){if(!this.peekKeywordLet())return null;let e=this.currentAbsoluteOffset;this.advance();let t=this.expectTemplateBindingKey(),r=null;this.consumeOptionalOperator("=")&&(r=this.expectTemplateBindingKey()),this.consumeStatementTerminator();let n=new U(e,this.currentAbsoluteOffset);return new Re(n,t,r)}consumeStatementTerminator(){this.consumeOptionalCharacter(te)||this.consumeOptionalCharacter(ee)}error(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.errors.push(new ae(e,this.input,this.locationText(t),this.location)),this.skip()}locationText(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return e==null&&(e=this.index),e<this.tokens.length?`at column ${this.tokens[e].index+1} in`:"at the end of the expression"}_reportErrorForPrivateIdentifier(e,t){let r=`Private identifiers are not supported. Unexpected private identifier: ${e}`;t!==null&&(r+=`, ${t}`),this.error(r)}skip(){let e=this.next;for(;this.index<this.tokens.length&&!e.isCharacter(te)&&!e.isOperator("|")&&(this.rparensExpected<=0||!e.isCharacter(z))&&(this.rbracesExpected<=0||!e.isCharacter(_e))&&(this.rbracketsExpected<=0||!e.isCharacter(re))&&(!(this.context&Z.Writable)||!e.isOperator("="));)this.next.isError()&&this.errors.push(new ae(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}},yt=class{constructor(){this.errors=[]}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){}visitLiteralPrimitive(e,t){}visitPropertyRead(e,t){}visitPropertyWrite(e,t){}visitSafePropertyRead(e,t){}visitMethodCall(e,t){}visitSafeMethodCall(e,t){}visitFunctionCall(e,t){}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitUnary(e,t){}visitBinary(e,t){}visitPrefixNot(e,t){}visitNonNullAssert(e,t){}visitConditional(e,t){}visitPipe(e,t){this.errors.push("pipes")}visitKeyedRead(e,t){}visitKeyedWrite(e,t){}visitAll(e,t){return e.map(r=>r.visit(this,t))}visitChain(e,t){}visitQuote(e,t){}visitSafeKeyedRead(e,t){}},wt=class extends et{constructor(){super(...arguments),this.errors=[]}visitPipe(){this.errors.push("pipes")}}}}),ft=q({"node_modules/angular-estree-parser/lib/utils.js"(e){"use strict";L(),Object.defineProperty(e,"__esModule",{value:!0}),e.getLast=e.toLowerCamelCase=e.findBackChar=e.findFrontChar=e.fitSpans=e.getNgType=e.parseNgInterpolation=e.parseNgTemplateBindings=e.parseNgAction=e.parseNgSimpleBinding=e.parseNgBinding=e.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX=void 0;var t=(tt(),be(Je)),r=(tr(),be(Yt)),n=(br(),be(nr)),s="angular-estree-parser";e.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX="NgEstreeParser";var a=0,i=[s,a];function h(){return new n.Parser(new r.Lexer)}function l(o,d){let y=h(),{astInput:E,comments:A}=T(o,y),{ast:I,errors:j}=d(E,y);return R(j),{ast:I,comments:A}}function P(o){return l(o,(d,y)=>y.parseBinding(d,...i))}e.parseNgBinding=P;function p(o){return l(o,(d,y)=>y.parseSimpleBinding(d,...i))}e.parseNgSimpleBinding=p;function x(o){return l(o,(d,y)=>y.parseAction(d,...i))}e.parseNgAction=x;function C(o){let d=h(),{templateBindings:y,errors:E}=d.parseTemplateBindings(e.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX,o,s,a,a);return R(E),y}e.parseNgTemplateBindings=C;function b(o){let d=h(),{astInput:y,comments:E}=T(o,d),A="{{",I="}}",{ast:j,errors:or}=d.parseInterpolation(A+y+I,...i);R(or);let gt=j.expressions[0],vt=new Set;return _(gt,ke=>{vt.has(ke)||(ke.start-=A.length,ke.end-=A.length,vt.add(ke))}),{ast:gt,comments:E}}e.parseNgInterpolation=b;function _(o,d){if(!(!o||typeof o!="object")){if(Array.isArray(o))return o.forEach(y=>_(y,d));for(let y of Object.keys(o)){let E=o[y];y==="span"?d(E):_(E,d)}}}function R(o){if(o.length!==0){let[{message:d}]=o;throw new SyntaxError(d.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}}function T(o,d){let y=d._commentStart(o);return y===null?{astInput:o,comments:[]}:{astInput:o.slice(0,y),comments:[{type:"Comment",value:o.slice(y+2),span:{start:y,end:o.length}}]}}function O(o){return t.Unary&&o instanceof t.Unary?"Unary":o instanceof t.Binary?"Binary":o instanceof t.BindingPipe?"BindingPipe":o instanceof t.Chain?"Chain":o instanceof t.Conditional?"Conditional":o instanceof t.EmptyExpr?"EmptyExpr":o instanceof t.FunctionCall?"FunctionCall":o instanceof t.ImplicitReceiver?"ImplicitReceiver":o instanceof t.KeyedRead?"KeyedRead":o instanceof t.KeyedWrite?"KeyedWrite":o instanceof t.LiteralArray?"LiteralArray":o instanceof t.LiteralMap?"LiteralMap":o instanceof t.LiteralPrimitive?"LiteralPrimitive":o instanceof t.MethodCall?"MethodCall":o instanceof t.NonNullAssert?"NonNullAssert":o instanceof t.PrefixNot?"PrefixNot":o instanceof t.PropertyRead?"PropertyRead":o instanceof t.PropertyWrite?"PropertyWrite":o instanceof t.Quote?"Quote":o instanceof t.SafeMethodCall?"SafeMethodCall":o instanceof t.SafePropertyRead?"SafePropertyRead":o.type}e.getNgType=O;function N(o,d){let{start:y,end:E}=o,A=y,I=E;for(;I!==A&&/\s/.test(d[I-1]);)I--;for(;A!==I&&/\s/.test(d[A]);)A++;return{start:A,end:I}}function c(o,d){let{start:y,end:E}=o,A=y,I=E;for(;I!==d.length&&/\s/.test(d[I]);)I++;for(;A!==0&&/\s/.test(d[A-1]);)A--;return{start:A,end:I}}function g(o,d){return d[o.start-1]==="("&&d[o.end]===")"?{start:o.start-1,end:o.end+1}:o}function u(o,d,y){let E=0,A={start:o.start,end:o.end};for(;;){let I=c(A,d),j=g(I,d);if(I.start===j.start&&I.end===j.end)break;A.start=j.start,A.end=j.end,E++}return{hasParens:(y?E-1:E)!==0,outerSpan:N(y?{start:A.start+1,end:A.end-1}:A,d),innerSpan:N(o,d)}}e.fitSpans=u;function v(o,d,y){let E=d;for(;!o.test(y[E]);)if(--E<0)throw new Error(`Cannot find front char ${o} from index ${d} in ${JSON.stringify(y)}`);return E}e.findFrontChar=v;function m(o,d,y){let E=d;for(;!o.test(y[E]);)if(++E>=y.length)throw new Error(`Cannot find back char ${o} from index ${d} in ${JSON.stringify(y)}`);return E}e.findBackChar=m;function f(o){return o.slice(0,1).toLowerCase()+o.slice(1)}e.toLowerCamelCase=f;function w(o){return o.length===0?void 0:o[o.length-1]}e.getLast=w}}),ir=q({"node_modules/angular-estree-parser/lib/transform.js"(e){"use strict";L(),Object.defineProperty(e,"__esModule",{value:!0}),e.transformSpan=e.transform=void 0;var t=ft(),r=function(s,a){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,h=t.getNgType(s);switch(h){case"Unary":{let{operator:c,expr:g}=s,u=l(g);return p("UnaryExpression",{prefix:!0,argument:u,operator:c},s.span,{hasParentParens:i})}case"Binary":{let{left:c,operation:g,right:u}=s,v=u.span.start===u.span.end,m=c.span.start===c.span.end;if(v||m){let o=c.span.start===c.span.end?l(u):l(c);return p("UnaryExpression",{prefix:!0,argument:o,operator:v?"+":"-"},{start:s.span.start,end:N(o)},{hasParentParens:i})}let f=l(c),w=l(u);return p(g==="&&"||g==="||"?"LogicalExpression":"BinaryExpression",{left:f,right:w,operator:g},{start:O(f),end:N(w)},{hasParentParens:i})}case"BindingPipe":{let{exp:c,name:g,args:u}=s,v=l(c),m=b(/\S/,b(/\|/,N(v))+1),f=p("Identifier",{name:g},{start:m,end:m+g.length}),w=u.map(l);return p("NGPipeExpression",{left:v,right:f,arguments:w},{start:O(v),end:N(w.length===0?f:t.getLast(w))},{hasParentParens:i})}case"Chain":{let{expressions:c}=s;return p("NGChainedExpression",{expressions:c.map(l)},s.span,{hasParentParens:i})}case"Comment":{let{value:c}=s;return p("CommentLine",{value:c},s.span,{processSpan:!1})}case"Conditional":{let{condition:c,trueExp:g,falseExp:u}=s,v=l(c),m=l(g),f=l(u);return p("ConditionalExpression",{test:v,consequent:m,alternate:f},{start:O(v),end:N(f)},{hasParentParens:i})}case"EmptyExpr":return p("NGEmptyExpression",{},s.span,{hasParentParens:i});case"FunctionCall":{let{target:c,args:g}=s,u=g.length===1?[P(g[0])]:g.map(l),v=l(c);return p("CallExpression",{callee:v,arguments:u},{start:O(v),end:s.span.end},{hasParentParens:i})}case"ImplicitReceiver":return p("ThisExpression",{},s.span,{hasParentParens:i});case"KeyedRead":{let{key:c}=s,g=Object.prototype.hasOwnProperty.call(s,"receiver")?s.receiver:s.obj,u=l(c);return x(g,u,{computed:!0,optional:!1},{end:s.span.end,hasParentParens:i})}case"LiteralArray":{let{expressions:c}=s;return p("ArrayExpression",{elements:c.map(l)},s.span,{hasParentParens:i})}case"LiteralMap":{let{keys:c,values:g}=s,u=g.map(m=>l(m)),v=c.map((m,f)=>{let{key:w,quoted:o}=m,d=u[f],y=b(/\S/,f===0?s.span.start+1:b(/,/,N(u[f-1]))+1),E=C(/\S/,C(/:/,O(d)-1)-1)+1,A={start:y,end:E},I=o?p("StringLiteral",{value:w},A):p("Identifier",{name:w},A),j=I.end<I.start;return p("ObjectProperty",{key:I,value:d,method:!1,shorthand:j,computed:!1},{start:O(I),end:N(d)})});return p("ObjectExpression",{properties:v},s.span,{hasParentParens:i})}case"LiteralPrimitive":{let{value:c}=s;switch(typeof c){case"boolean":return p("BooleanLiteral",{value:c},s.span,{hasParentParens:i});case"number":return p("NumericLiteral",{value:c},s.span,{hasParentParens:i});case"object":return p("NullLiteral",{},s.span,{hasParentParens:i});case"string":return p("StringLiteral",{value:c},s.span,{hasParentParens:i});case"undefined":return p("Identifier",{name:"undefined"},s.span,{hasParentParens:i});default:throw new Error(`Unexpected LiteralPrimitive value type ${typeof c}`)}}case"MethodCall":case"SafeMethodCall":{let c=h==="SafeMethodCall",{receiver:g,name:u,args:v}=s,m=v.length===1?[P(v[0])]:v.map(l),f=C(/\S/,C(/\(/,(m.length===0?C(/\)/,s.span.end-1):O(m[0]))-1)-1)+1,w=p("Identifier",{name:u},{start:f-u.length,end:f}),o=x(g,w,{computed:!1,optional:c}),d=R(o);return p(c||d?"OptionalCallExpression":"CallExpression",{callee:o,arguments:m},{start:O(o),end:s.span.end},{hasParentParens:i})}case"NonNullAssert":{let{expression:c}=s,g=l(c);return p("TSNonNullExpression",{expression:g},{start:O(g),end:s.span.end},{hasParentParens:i})}case"PrefixNot":{let{expression:c}=s,g=l(c);return p("UnaryExpression",{prefix:!0,operator:"!",argument:g},{start:s.span.start,end:N(g)},{hasParentParens:i})}case"PropertyRead":case"SafePropertyRead":{let c=h==="SafePropertyRead",{receiver:g,name:u}=s,v=C(/\S/,s.span.end-1)+1,m=p("Identifier",{name:u},{start:v-u.length,end:v},_(g)?{hasParentParens:i}:{});return x(g,m,{computed:!1,optional:c},{hasParentParens:i})}case"KeyedWrite":{let{key:c,value:g}=s,u=Object.prototype.hasOwnProperty.call(s,"receiver")?s.receiver:s.obj,v=l(c),m=l(g),f=x(u,v,{computed:!0,optional:!1},{end:b(/\]/,N(v))+1});return p("AssignmentExpression",{left:f,operator:"=",right:m},{start:O(f),end:N(m)},{hasParentParens:i})}case"PropertyWrite":{let{receiver:c,name:g,value:u}=s,v=l(u),m=C(/\S/,C(/=/,O(v)-1)-1)+1,f=p("Identifier",{name:g},{start:m-g.length,end:m}),w=x(c,f,{computed:!1,optional:!1});return p("AssignmentExpression",{left:w,operator:"=",right:v},{start:O(w),end:N(v)},{hasParentParens:i})}case"Quote":{let{prefix:c,uninterpretedExpression:g}=s;return p("NGQuotedExpression",{prefix:c,value:g},s.span,{hasParentParens:i})}default:throw new Error(`Unexpected node ${h}`)}function l(c){return e.transform(c,a)}function P(c){return e.transform(c,a,!0)}function p(c,g,u){let{processSpan:v=!0,hasParentParens:m=!1}=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},f=Object.assign(Object.assign({type:c},n(u,a,v,m)),g);switch(c){case"Identifier":{let w=f;w.loc.identifierName=w.name;break}case"NumericLiteral":{let w=f;w.extra=Object.assign(Object.assign({},w.extra),{raw:a.text.slice(w.start,w.end),rawValue:w.value});break}case"StringLiteral":{let w=f;w.extra=Object.assign(Object.assign({},w.extra),{raw:a.text.slice(w.start,w.end),rawValue:w.value});break}}return f}function x(c,g,u){let{end:v=N(g),hasParentParens:m=!1}=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};if(_(c)||c.span.start===g.start)return g;let f=l(c),w=R(f);return p(u.optional||w?"OptionalMemberExpression":"MemberExpression",Object.assign({object:f,property:g,computed:u.computed},u.optional?{optional:!0}:w?{optional:!1}:null),{start:O(f),end:v},{hasParentParens:m})}function C(c,g){return t.findFrontChar(c,g,a.text)}function b(c,g){return t.findBackChar(c,g,a.text)}function _(c){return c.span.start>=c.span.end||/^\s+$/.test(a.text.slice(c.span.start,c.span.end))}function R(c){return(c.type==="OptionalCallExpression"||c.type==="OptionalMemberExpression")&&!T(c)}function T(c){return c.extra&&c.extra.parenthesized}function O(c){return T(c)?c.extra.parenStart:c.start}function N(c){return T(c)?c.extra.parenEnd:c.end}};e.transform=r;function n(s,a){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,h=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!i){let{start:x,end:C}=s;return{start:x,end:C,loc:{start:a.locator.locationForIndex(x),end:a.locator.locationForIndex(C)}}}let{outerSpan:l,innerSpan:P,hasParens:p}=t.fitSpans(s,a.text,h);return Object.assign({start:P.start,end:P.end,loc:{start:a.locator.locationForIndex(P.start),end:a.locator.locationForIndex(P.end)}},p&&{extra:{parenthesized:!0,parenStart:l.start,parenEnd:l.end}})}e.transformSpan=n}}),Lr=q({"node_modules/angular-estree-parser/lib/transform-microsyntax.js"(e){"use strict";L(),Object.defineProperty(e,"__esModule",{value:!0}),e.transformTemplateBindings=void 0;var t=(tt(),be(Je)),r=ir(),n=ft();function s(a,i){a.forEach(N);let[h]=a,{key:l}=h,P=i.text.slice(h.sourceSpan.start,h.sourceSpan.end).trim().length===0?a.slice(1):a,p=[],x=null;for(let u=0;u<P.length;u++){let v=P[u];if(x&&T(x)&&O(v)&&v.value&&v.value.source===x.key.source){let m=_("NGMicrosyntaxKey",{name:v.key.source},v.key.span),f=(d,y)=>Object.assign(Object.assign({},d),r.transformSpan({start:d.start,end:y},i)),w=d=>Object.assign(Object.assign({},f(d,m.end)),{alias:m}),o=p.pop();if(o.type==="NGMicrosyntaxExpression")p.push(w(o));else if(o.type==="NGMicrosyntaxKeyedExpression"){let d=w(o.expression);p.push(f(Object.assign(Object.assign({},o),{expression:d}),d.end))}else throw new Error(`Unexpected type ${o.type}`)}else p.push(C(v,u));x=v}return _("NGMicrosyntax",{body:p},p.length===0?a[0].sourceSpan:{start:p[0].start,end:p[p.length-1].end});function C(u,v){if(T(u)){let{key:m,value:f}=u;return f?v===0?_("NGMicrosyntaxExpression",{expression:b(f.ast),alias:null},f.sourceSpan):_("NGMicrosyntaxKeyedExpression",{key:_("NGMicrosyntaxKey",{name:R(m.source)},m.span),expression:_("NGMicrosyntaxExpression",{expression:b(f.ast),alias:null},f.sourceSpan)},{start:m.span.start,end:f.sourceSpan.end}):_("NGMicrosyntaxKey",{name:R(m.source)},m.span)}else{let{key:m,sourceSpan:f}=u;if(/^let\s$/.test(i.text.slice(f.start,f.start+4))){let{value:o}=u;return _("NGMicrosyntaxLet",{key:_("NGMicrosyntaxKey",{name:m.source},m.span),value:o?_("NGMicrosyntaxKey",{name:o.source},o.span):null},{start:f.start,end:o?o.span.end:m.span.end})}else{let o=g(u);return _("NGMicrosyntaxAs",{key:_("NGMicrosyntaxKey",{name:o.source},o.span),alias:_("NGMicrosyntaxKey",{name:m.source},m.span)},{start:o.span.start,end:m.span.end})}}}function b(u){return r.transform(u,i)}function _(u,v,m){let f=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;return Object.assign(Object.assign({type:u},r.transformSpan(m,i,f)),v)}function R(u){return n.toLowerCamelCase(u.slice(l.source.length))}function T(u){return u instanceof t.ExpressionBinding}function O(u){return u instanceof t.VariableBinding}function N(u){c(u.key.span),O(u)&&u.value&&c(u.value.span)}function c(u){if(i.text[u.start]!=='"'&&i.text[u.start]!=="'")return;let v=i.text[u.start],m=!1;for(let f=u.start+1;f<i.text.length;f++)switch(i.text[f]){case v:if(!m){u.end=f+1;return}default:m=!1;break;case"\\":m=!m;break}}function g(u){if(!u.value||u.value.source!==n.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX)return u.value;let v=n.findBackChar(/\S/,u.sourceSpan.start,i.text);return{source:"$implicit",span:{start:v,end:v}}}}e.transformTemplateBindings=s}}),Rr=q({"node_modules/angular-estree-parser/lib/index.js"(e){"use strict";L(),Object.defineProperty(e,"__esModule",{value:!0}),e.parseTemplateBindings=e.parseAction=e.parseInterpolation=e.parseSimpleBinding=e.parseBinding=void 0;var t=gr(),r=ir(),n=Lr(),s=ft();function a(x,C){let{ast:b,comments:_}=C(x),R=new t.Context(x),T=N=>r.transform(N,R),O=T(b);return O.comments=_.map(T),O}function i(x){return a(x,s.parseNgBinding)}e.parseBinding=i;function h(x){return a(x,s.parseNgSimpleBinding)}e.parseSimpleBinding=h;function l(x){return a(x,s.parseNgInterpolation)}e.parseInterpolation=l;function P(x){return a(x,s.parseNgAction)}e.parseAction=P;function p(x){return n.transformTemplateBindings(s.parseNgTemplateBindings(x),new t.Context(x))}e.parseTemplateBindings=p}});L();var{locStart:Tr,locEnd:$r}=dr();function Ne(e){return{astFormat:"estree",parse:(r,n,s)=>{let a=Rr(),i=e(r,a);return{type:"NGRoot",node:s.parser==="__ng_action"&&i.type!=="NGChainedExpression"?Object.assign(Object.assign({},i),{},{type:"NGChainedExpression",expressions:[i]}):i}},locStart:Tr,locEnd:$r}}ar.exports={parsers:{__ng_action:Ne((e,t)=>t.parseAction(e)),__ng_binding:Ne((e,t)=>t.parseBinding(e)),__ng_interpolation:Ne((e,t)=>t.parseInterpolation(e)),__ng_directive:Ne((e,t)=>t.parseTemplateBindings(e))}}});return Br();});