(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.html=e()}})(function(){"use strict";var S=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var ee=S((cc,Kr)=>{var Ne=function(e){return e&&e.Math==Math&&e};Kr.exports=Ne(typeof globalThis=="object"&&globalThis)||Ne(typeof window=="object"&&window)||Ne(typeof self=="object"&&self)||Ne(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var se=S((hc,<PERSON>)=>{Jr.exports=function(e){try{return!!e()}catch{return!0}}});var ae=S((pc,Zr)=>{var qs=se();Zr.exports=!qs(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var Oe=S((fc,eu)=>{var Is=se();eu.exports=!Is(function(){var e=function(){}.bind();return typeof e!="function"||e.hasOwnProperty("prototype")})});var De=S((dc,ru)=>{var Rs=Oe(),qe=Function.prototype.call;ru.exports=Rs?qe.bind(qe):function(){return qe.apply(qe,arguments)}});var su=S(nu=>{"use strict";var uu={}.propertyIsEnumerable,tu=Object.getOwnPropertyDescriptor,xs=tu&&!uu.call({1:2},1);nu.f=xs?function(r){var u=tu(this,r);return!!u&&u.enumerable}:uu});var Ie=S((Cc,iu)=>{iu.exports=function(e,r){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:r}}});var re=S((mc,Du)=>{var au=Oe(),ou=Function.prototype,er=ou.call,Ps=au&&ou.bind.bind(er,er);Du.exports=au?Ps:function(e){return function(){return er.apply(e,arguments)}}});var me=S((gc,cu)=>{var lu=re(),ks=lu({}.toString),Ls=lu("".slice);cu.exports=function(e){return Ls(ks(e),8,-1)}});var pu=S((Fc,hu)=>{var $s=re(),Ms=se(),js=me(),rr=Object,Us=$s("".split);hu.exports=Ms(function(){return!rr("z").propertyIsEnumerable(0)})?function(e){return js(e)=="String"?Us(e,""):rr(e)}:rr});var Re=S((Ac,fu)=>{fu.exports=function(e){return e==null}});var ur=S((vc,du)=>{var Gs=Re(),Vs=TypeError;du.exports=function(e){if(Gs(e))throw Vs("Can't call method on "+e);return e}});var xe=S((_c,Eu)=>{var Xs=pu(),Hs=ur();Eu.exports=function(e){return Xs(Hs(e))}});var nr=S((Sc,Cu)=>{var tr=typeof document=="object"&&document.all,zs=typeof tr>"u"&&tr!==void 0;Cu.exports={all:tr,IS_HTMLDDA:zs}});var Y=S((yc,gu)=>{var mu=nr(),Ws=mu.all;gu.exports=mu.IS_HTMLDDA?function(e){return typeof e=="function"||e===Ws}:function(e){return typeof e=="function"}});var le=S((Tc,vu)=>{var Fu=Y(),Au=nr(),Ys=Au.all;vu.exports=Au.IS_HTMLDDA?function(e){return typeof e=="object"?e!==null:Fu(e)||e===Ys}:function(e){return typeof e=="object"?e!==null:Fu(e)}});var ge=S((Bc,_u)=>{var sr=ee(),Qs=Y(),Ks=function(e){return Qs(e)?e:void 0};_u.exports=function(e,r){return arguments.length<2?Ks(sr[e]):sr[e]&&sr[e][r]}});var ir=S((bc,Su)=>{var Js=re();Su.exports=Js({}.isPrototypeOf)});var Tu=S((wc,yu)=>{var Zs=ge();yu.exports=Zs("navigator","userAgent")||""});var Iu=S((Nc,qu)=>{var Ou=ee(),ar=Tu(),Bu=Ou.process,bu=Ou.Deno,wu=Bu&&Bu.versions||bu&&bu.version,Nu=wu&&wu.v8,ue,Pe;Nu&&(ue=Nu.split("."),Pe=ue[0]>0&&ue[0]<4?1:+(ue[0]+ue[1]));!Pe&&ar&&(ue=ar.match(/Edge\/(\d+)/),(!ue||ue[1]>=74)&&(ue=ar.match(/Chrome\/(\d+)/),ue&&(Pe=+ue[1])));qu.exports=Pe});var or=S((Oc,xu)=>{var Ru=Iu(),ei=se();xu.exports=!!Object.getOwnPropertySymbols&&!ei(function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Ru&&Ru<41})});var Dr=S((qc,Pu)=>{var ri=or();Pu.exports=ri&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var lr=S((Ic,ku)=>{var ui=ge(),ti=Y(),ni=ir(),si=Dr(),ii=Object;ku.exports=si?function(e){return typeof e=="symbol"}:function(e){var r=ui("Symbol");return ti(r)&&ni(r.prototype,ii(e))}});var ke=S((Rc,Lu)=>{var ai=String;Lu.exports=function(e){try{return ai(e)}catch{return"Object"}}});var Fe=S((xc,$u)=>{var oi=Y(),Di=ke(),li=TypeError;$u.exports=function(e){if(oi(e))return e;throw li(Di(e)+" is not a function")}});var Le=S((Pc,Mu)=>{var ci=Fe(),hi=Re();Mu.exports=function(e,r){var u=e[r];return hi(u)?void 0:ci(u)}});var Uu=S((kc,ju)=>{var cr=De(),hr=Y(),pr=le(),pi=TypeError;ju.exports=function(e,r){var u,n;if(r==="string"&&hr(u=e.toString)&&!pr(n=cr(u,e))||hr(u=e.valueOf)&&!pr(n=cr(u,e))||r!=="string"&&hr(u=e.toString)&&!pr(n=cr(u,e)))return n;throw pi("Can't convert object to primitive value")}});var Vu=S((Lc,Gu)=>{Gu.exports=!1});var $e=S(($c,Hu)=>{var Xu=ee(),fi=Object.defineProperty;Hu.exports=function(e,r){try{fi(Xu,e,{value:r,configurable:!0,writable:!0})}catch{Xu[e]=r}return r}});var Me=S((Mc,Wu)=>{var di=ee(),Ei=$e(),zu="__core-js_shared__",Ci=di[zu]||Ei(zu,{});Wu.exports=Ci});var fr=S((jc,Qu)=>{var mi=Vu(),Yu=Me();(Qu.exports=function(e,r){return Yu[e]||(Yu[e]=r!==void 0?r:{})})("versions",[]).push({version:"3.26.1",mode:mi?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var dr=S((Uc,Ku)=>{var gi=ur(),Fi=Object;Ku.exports=function(e){return Fi(gi(e))}});var oe=S((Gc,Ju)=>{var Ai=re(),vi=dr(),_i=Ai({}.hasOwnProperty);Ju.exports=Object.hasOwn||function(r,u){return _i(vi(r),u)}});var Er=S((Vc,Zu)=>{var Si=re(),yi=0,Ti=Math.random(),Bi=Si(1 .toString);Zu.exports=function(e){return"Symbol("+(e===void 0?"":e)+")_"+Bi(++yi+Ti,36)}});var he=S((Xc,nt)=>{var bi=ee(),wi=fr(),et=oe(),Ni=Er(),rt=or(),tt=Dr(),fe=wi("wks"),ce=bi.Symbol,ut=ce&&ce.for,Oi=tt?ce:ce&&ce.withoutSetter||Ni;nt.exports=function(e){if(!et(fe,e)||!(rt||typeof fe[e]=="string")){var r="Symbol."+e;rt&&et(ce,e)?fe[e]=ce[e]:tt&&ut?fe[e]=ut(r):fe[e]=Oi(r)}return fe[e]}});var ot=S((Hc,at)=>{var qi=De(),st=le(),it=lr(),Ii=Le(),Ri=Uu(),xi=he(),Pi=TypeError,ki=xi("toPrimitive");at.exports=function(e,r){if(!st(e)||it(e))return e;var u=Ii(e,ki),n;if(u){if(r===void 0&&(r="default"),n=qi(u,e,r),!st(n)||it(n))return n;throw Pi("Can't convert object to primitive value")}return r===void 0&&(r="number"),Ri(e,r)}});var je=S((zc,Dt)=>{var Li=ot(),$i=lr();Dt.exports=function(e){var r=Li(e,"string");return $i(r)?r:r+""}});var ht=S((Wc,ct)=>{var Mi=ee(),lt=le(),Cr=Mi.document,ji=lt(Cr)&&lt(Cr.createElement);ct.exports=function(e){return ji?Cr.createElement(e):{}}});var mr=S((Yc,pt)=>{var Ui=ae(),Gi=se(),Vi=ht();pt.exports=!Ui&&!Gi(function(){return Object.defineProperty(Vi("div"),"a",{get:function(){return 7}}).a!=7})});var gr=S(dt=>{var Xi=ae(),Hi=De(),zi=su(),Wi=Ie(),Yi=xe(),Qi=je(),Ki=oe(),Ji=mr(),ft=Object.getOwnPropertyDescriptor;dt.f=Xi?ft:function(r,u){if(r=Yi(r),u=Qi(u),Ji)try{return ft(r,u)}catch{}if(Ki(r,u))return Wi(!Hi(zi.f,r,u),r[u])}});var Ct=S((Kc,Et)=>{var Zi=ae(),ea=se();Et.exports=Zi&&ea(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var de=S((Jc,mt)=>{var ra=le(),ua=String,ta=TypeError;mt.exports=function(e){if(ra(e))return e;throw ta(ua(e)+" is not an object")}});var Ae=S(Ft=>{var na=ae(),sa=mr(),ia=Ct(),Ue=de(),gt=je(),aa=TypeError,Fr=Object.defineProperty,oa=Object.getOwnPropertyDescriptor,Ar="enumerable",vr="configurable",_r="writable";Ft.f=na?ia?function(r,u,n){if(Ue(r),u=gt(u),Ue(n),typeof r=="function"&&u==="prototype"&&"value"in n&&_r in n&&!n[_r]){var D=oa(r,u);D&&D[_r]&&(r[u]=n.value,n={configurable:vr in n?n[vr]:D[vr],enumerable:Ar in n?n[Ar]:D[Ar],writable:!1})}return Fr(r,u,n)}:Fr:function(r,u,n){if(Ue(r),u=gt(u),Ue(n),sa)try{return Fr(r,u,n)}catch{}if("get"in n||"set"in n)throw aa("Accessors not supported");return"value"in n&&(r[u]=n.value),r}});var Sr=S((e2,At)=>{var Da=ae(),la=Ae(),ca=Ie();At.exports=Da?function(e,r,u){return la.f(e,r,ca(1,u))}:function(e,r,u){return e[r]=u,e}});var St=S((r2,_t)=>{var yr=ae(),ha=oe(),vt=Function.prototype,pa=yr&&Object.getOwnPropertyDescriptor,Tr=ha(vt,"name"),fa=Tr&&function(){}.name==="something",da=Tr&&(!yr||yr&&pa(vt,"name").configurable);_t.exports={EXISTS:Tr,PROPER:fa,CONFIGURABLE:da}});var br=S((u2,yt)=>{var Ea=re(),Ca=Y(),Br=Me(),ma=Ea(Function.toString);Ca(Br.inspectSource)||(Br.inspectSource=function(e){return ma(e)});yt.exports=Br.inspectSource});var bt=S((t2,Bt)=>{var ga=ee(),Fa=Y(),Tt=ga.WeakMap;Bt.exports=Fa(Tt)&&/native code/.test(String(Tt))});var Ot=S((n2,Nt)=>{var Aa=fr(),va=Er(),wt=Aa("keys");Nt.exports=function(e){return wt[e]||(wt[e]=va(e))}});var wr=S((s2,qt)=>{qt.exports={}});var Pt=S((i2,xt)=>{var _a=bt(),Rt=ee(),Sa=le(),ya=Sr(),Nr=oe(),Or=Me(),Ta=Ot(),Ba=wr(),It="Object already initialized",qr=Rt.TypeError,ba=Rt.WeakMap,Ge,ve,Ve,wa=function(e){return Ve(e)?ve(e):Ge(e,{})},Na=function(e){return function(r){var u;if(!Sa(r)||(u=ve(r)).type!==e)throw qr("Incompatible receiver, "+e+" required");return u}};_a||Or.state?(te=Or.state||(Or.state=new ba),te.get=te.get,te.has=te.has,te.set=te.set,Ge=function(e,r){if(te.has(e))throw qr(It);return r.facade=e,te.set(e,r),r},ve=function(e){return te.get(e)||{}},Ve=function(e){return te.has(e)}):(pe=Ta("state"),Ba[pe]=!0,Ge=function(e,r){if(Nr(e,pe))throw qr(It);return r.facade=e,ya(e,pe,r),r},ve=function(e){return Nr(e,pe)?e[pe]:{}},Ve=function(e){return Nr(e,pe)});var te,pe;xt.exports={set:Ge,get:ve,has:Ve,enforce:wa,getterFor:Na}});var $t=S((a2,Lt)=>{var Oa=se(),qa=Y(),Xe=oe(),Ir=ae(),Ia=St().CONFIGURABLE,Ra=br(),kt=Pt(),xa=kt.enforce,Pa=kt.get,He=Object.defineProperty,ka=Ir&&!Oa(function(){return He(function(){},"length",{value:8}).length!==8}),La=String(String).split("String"),$a=Lt.exports=function(e,r,u){String(r).slice(0,7)==="Symbol("&&(r="["+String(r).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),u&&u.getter&&(r="get "+r),u&&u.setter&&(r="set "+r),(!Xe(e,"name")||Ia&&e.name!==r)&&(Ir?He(e,"name",{value:r,configurable:!0}):e.name=r),ka&&u&&Xe(u,"arity")&&e.length!==u.arity&&He(e,"length",{value:u.arity});try{u&&Xe(u,"constructor")&&u.constructor?Ir&&He(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch{}var n=xa(e);return Xe(n,"source")||(n.source=La.join(typeof r=="string"?r:"")),e};Function.prototype.toString=$a(function(){return qa(this)&&Pa(this).source||Ra(this)},"toString")});var jt=S((o2,Mt)=>{var Ma=Y(),ja=Ae(),Ua=$t(),Ga=$e();Mt.exports=function(e,r,u,n){n||(n={});var D=n.enumerable,s=n.name!==void 0?n.name:r;if(Ma(u)&&Ua(u,s,n),n.global)D?e[r]=u:Ga(r,u);else{try{n.unsafe?e[r]&&(D=!0):delete e[r]}catch{}D?e[r]=u:ja.f(e,r,{value:u,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return e}});var Gt=S((D2,Ut)=>{var Va=Math.ceil,Xa=Math.floor;Ut.exports=Math.trunc||function(r){var u=+r;return(u>0?Xa:Va)(u)}});var Rr=S((l2,Vt)=>{var Ha=Gt();Vt.exports=function(e){var r=+e;return r!==r||r===0?0:Ha(r)}});var Ht=S((c2,Xt)=>{var za=Rr(),Wa=Math.max,Ya=Math.min;Xt.exports=function(e,r){var u=za(e);return u<0?Wa(u+r,0):Ya(u,r)}});var Wt=S((h2,zt)=>{var Qa=Rr(),Ka=Math.min;zt.exports=function(e){return e>0?Ka(Qa(e),9007199254740991):0}});var _e=S((p2,Yt)=>{var Ja=Wt();Yt.exports=function(e){return Ja(e.length)}});var Jt=S((f2,Kt)=>{var Za=xe(),eo=Ht(),ro=_e(),Qt=function(e){return function(r,u,n){var D=Za(r),s=ro(D),i=eo(n,s),f;if(e&&u!=u){for(;s>i;)if(f=D[i++],f!=f)return!0}else for(;s>i;i++)if((e||i in D)&&D[i]===u)return e||i||0;return!e&&-1}};Kt.exports={includes:Qt(!0),indexOf:Qt(!1)}});var rn=S((d2,en)=>{var uo=re(),xr=oe(),to=xe(),no=Jt().indexOf,so=wr(),Zt=uo([].push);en.exports=function(e,r){var u=to(e),n=0,D=[],s;for(s in u)!xr(so,s)&&xr(u,s)&&Zt(D,s);for(;r.length>n;)xr(u,s=r[n++])&&(~no(D,s)||Zt(D,s));return D}});var tn=S((E2,un)=>{un.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var sn=S(nn=>{var io=rn(),ao=tn(),oo=ao.concat("length","prototype");nn.f=Object.getOwnPropertyNames||function(r){return io(r,oo)}});var on=S(an=>{an.f=Object.getOwnPropertySymbols});var ln=S((g2,Dn)=>{var Do=ge(),lo=re(),co=sn(),ho=on(),po=de(),fo=lo([].concat);Dn.exports=Do("Reflect","ownKeys")||function(r){var u=co.f(po(r)),n=ho.f;return n?fo(u,n(r)):u}});var pn=S((F2,hn)=>{var cn=oe(),Eo=ln(),Co=gr(),mo=Ae();hn.exports=function(e,r,u){for(var n=Eo(r),D=mo.f,s=Co.f,i=0;i<n.length;i++){var f=n[i];!cn(e,f)&&!(u&&cn(u,f))&&D(e,f,s(r,f))}}});var dn=S((A2,fn)=>{var go=se(),Fo=Y(),Ao=/#|\.prototype\./,Se=function(e,r){var u=_o[vo(e)];return u==yo?!0:u==So?!1:Fo(r)?go(r):!!r},vo=Se.normalize=function(e){return String(e).replace(Ao,".").toLowerCase()},_o=Se.data={},So=Se.NATIVE="N",yo=Se.POLYFILL="P";fn.exports=Se});var ze=S((v2,En)=>{var Pr=ee(),To=gr().f,Bo=Sr(),bo=jt(),wo=$e(),No=pn(),Oo=dn();En.exports=function(e,r){var u=e.target,n=e.global,D=e.stat,s,i,f,c,F,a;if(n?i=Pr:D?i=Pr[u]||wo(u,{}):i=(Pr[u]||{}).prototype,i)for(f in r){if(F=r[f],e.dontCallGetSet?(a=To(i,f),c=a&&a.value):c=i[f],s=Oo(n?f:u+(D?".":"#")+f,e.forced),!s&&c!==void 0){if(typeof F==typeof c)continue;No(F,c)}(e.sham||c&&c.sham)&&Bo(F,"sham",!0),bo(i,f,F,e)}}});var Cn=S(()=>{var qo=ze(),kr=ee();qo({global:!0,forced:kr.globalThis!==kr},{globalThis:kr})});var mn=S(()=>{Cn()});var Lr=S((B2,gn)=>{var Io=me();gn.exports=Array.isArray||function(r){return Io(r)=="Array"}});var An=S((b2,Fn)=>{var Ro=TypeError,xo=9007199254740991;Fn.exports=function(e){if(e>xo)throw Ro("Maximum allowed index exceeded");return e}});var _n=S((w2,vn)=>{var Po=me(),ko=re();vn.exports=function(e){if(Po(e)==="Function")return ko(e)}});var $r=S((N2,yn)=>{var Sn=_n(),Lo=Fe(),$o=Oe(),Mo=Sn(Sn.bind);yn.exports=function(e,r){return Lo(e),r===void 0?e:$o?Mo(e,r):function(){return e.apply(r,arguments)}}});var bn=S((O2,Bn)=>{"use strict";var jo=Lr(),Uo=_e(),Go=An(),Vo=$r(),Tn=function(e,r,u,n,D,s,i,f){for(var c=D,F=0,a=i?Vo(i,f):!1,l,h;F<n;)F in u&&(l=a?a(u[F],F,r):u[F],s>0&&jo(l)?(h=Uo(l),c=Tn(e,r,l,h,c,s-1)-1):(Go(c+1),e[c]=l),c++),F++;return c};Bn.exports=Tn});var On=S((q2,Nn)=>{var Xo=he(),Ho=Xo("toStringTag"),wn={};wn[Ho]="z";Nn.exports=String(wn)==="[object z]"});var Mr=S((I2,qn)=>{var zo=On(),Wo=Y(),We=me(),Yo=he(),Qo=Yo("toStringTag"),Ko=Object,Jo=We(function(){return arguments}())=="Arguments",Zo=function(e,r){try{return e[r]}catch{}};qn.exports=zo?We:function(e){var r,u,n;return e===void 0?"Undefined":e===null?"Null":typeof(u=Zo(r=Ko(e),Qo))=="string"?u:Jo?We(r):(n=We(r))=="Object"&&Wo(r.callee)?"Arguments":n}});var Ln=S((R2,kn)=>{var eD=re(),rD=se(),In=Y(),uD=Mr(),tD=ge(),nD=br(),Rn=function(){},sD=[],xn=tD("Reflect","construct"),jr=/^\s*(?:class|function)\b/,iD=eD(jr.exec),aD=!jr.exec(Rn),ye=function(r){if(!In(r))return!1;try{return xn(Rn,sD,r),!0}catch{return!1}},Pn=function(r){if(!In(r))return!1;switch(uD(r)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return aD||!!iD(jr,nD(r))}catch{return!0}};Pn.sham=!0;kn.exports=!xn||rD(function(){var e;return ye(ye.call)||!ye(Object)||!ye(function(){e=!0})||e})?Pn:ye});var Un=S((x2,jn)=>{var $n=Lr(),oD=Ln(),DD=le(),lD=he(),cD=lD("species"),Mn=Array;jn.exports=function(e){var r;return $n(e)&&(r=e.constructor,oD(r)&&(r===Mn||$n(r.prototype))?r=void 0:DD(r)&&(r=r[cD],r===null&&(r=void 0))),r===void 0?Mn:r}});var Vn=S((P2,Gn)=>{var hD=Un();Gn.exports=function(e,r){return new(hD(e))(r===0?0:r)}});var Xn=S(()=>{"use strict";var pD=ze(),fD=bn(),dD=Fe(),ED=dr(),CD=_e(),mD=Vn();pD({target:"Array",proto:!0},{flatMap:function(r){var u=ED(this),n=CD(u),D;return dD(r),D=mD(u,0),D.length=fD(D,u,u,n,0,1,r,arguments.length>1?arguments[1]:void 0),D}})});var Ur=S(($2,Hn)=>{Hn.exports={}});var Wn=S((M2,zn)=>{var gD=he(),FD=Ur(),AD=gD("iterator"),vD=Array.prototype;zn.exports=function(e){return e!==void 0&&(FD.Array===e||vD[AD]===e)}});var Gr=S((j2,Qn)=>{var _D=Mr(),Yn=Le(),SD=Re(),yD=Ur(),TD=he(),BD=TD("iterator");Qn.exports=function(e){if(!SD(e))return Yn(e,BD)||Yn(e,"@@iterator")||yD[_D(e)]}});var Jn=S((U2,Kn)=>{var bD=De(),wD=Fe(),ND=de(),OD=ke(),qD=Gr(),ID=TypeError;Kn.exports=function(e,r){var u=arguments.length<2?qD(e):r;if(wD(u))return ND(bD(u,e));throw ID(OD(e)+" is not iterable")}});var rs=S((G2,es)=>{var RD=De(),Zn=de(),xD=Le();es.exports=function(e,r,u){var n,D;Zn(e);try{if(n=xD(e,"return"),!n){if(r==="throw")throw u;return u}n=RD(n,e)}catch(s){D=!0,n=s}if(r==="throw")throw u;if(D)throw n;return Zn(n),u}});var is=S((V2,ss)=>{var PD=$r(),kD=De(),LD=de(),$D=ke(),MD=Wn(),jD=_e(),us=ir(),UD=Jn(),GD=Gr(),ts=rs(),VD=TypeError,Ye=function(e,r){this.stopped=e,this.result=r},ns=Ye.prototype;ss.exports=function(e,r,u){var n=u&&u.that,D=!!(u&&u.AS_ENTRIES),s=!!(u&&u.IS_RECORD),i=!!(u&&u.IS_ITERATOR),f=!!(u&&u.INTERRUPTED),c=PD(r,n),F,a,l,h,C,d,m,T=function(g){return F&&ts(F,"normal",g),new Ye(!0,g)},w=function(g){return D?(LD(g),f?c(g[0],g[1],T):c(g[0],g[1])):f?c(g,T):c(g)};if(s)F=e.iterator;else if(i)F=e;else{if(a=GD(e),!a)throw VD($D(e)+" is not iterable");if(MD(a)){for(l=0,h=jD(e);h>l;l++)if(C=w(e[l]),C&&us(ns,C))return C;return new Ye(!1)}F=UD(e,a)}for(d=s?e.next:F.next;!(m=kD(d,F)).done;){try{C=w(m.value)}catch(g){ts(F,"throw",g)}if(typeof C=="object"&&C&&us(ns,C))return C}return new Ye(!1)}});var os=S((X2,as)=>{"use strict";var XD=je(),HD=Ae(),zD=Ie();as.exports=function(e,r,u){var n=XD(r);n in e?HD.f(e,n,zD(0,u)):e[n]=u}});var Ds=S(()=>{var WD=ze(),YD=is(),QD=os();WD({target:"Object",stat:!0},{fromEntries:function(r){var u={};return YD(r,function(n,D){QD(u,n,D)},{AS_ENTRIES:!0}),u}})});var Dc=S((W2,Os)=>{var KD=["cliName","cliCategory","cliDescription"];function JD(e,r){if(e==null)return{};var u=ZD(e,r),n,D;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(D=0;D<s.length;D++)n=s[D],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(u[n]=e[n])}return u}function ZD(e,r){if(e==null)return{};var u={},n=Object.keys(e),D,s;for(s=0;s<n.length;s++)D=n[s],!(r.indexOf(D)>=0)&&(u[D]=e[D]);return u}mn();Xn();Ds();var el=Object.create,Je=Object.defineProperty,rl=Object.getOwnPropertyDescriptor,Xr=Object.getOwnPropertyNames,ul=Object.getPrototypeOf,tl=Object.prototype.hasOwnProperty,Ee=(e,r)=>function(){return e&&(r=(0,e[Xr(e)[0]])(e=0)),r},I=(e,r)=>function(){return r||(0,e[Xr(e)[0]])((r={exports:{}}).exports,r),r.exports},ps=(e,r)=>{for(var u in r)Je(e,u,{get:r[u],enumerable:!0})},fs=(e,r,u,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let D of Xr(r))!tl.call(e,D)&&D!==u&&Je(e,D,{get:()=>r[D],enumerable:!(n=rl(r,D))||n.enumerable});return e},nl=(e,r,u)=>(u=e!=null?el(ul(e)):{},fs(r||!e||!e.__esModule?Je(u,"default",{value:e,enumerable:!0}):u,e)),ds=e=>fs(Je({},"__esModule",{value:!0}),e),Te,q=Ee({"<define:process>"(){Te={env:{},argv:[]}}}),Es=I({"node_modules/angular-html-parser/lib/compiler/src/chars.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0}),e.$EOF=0,e.$BSPACE=8,e.$TAB=9,e.$LF=10,e.$VTAB=11,e.$FF=12,e.$CR=13,e.$SPACE=32,e.$BANG=33,e.$DQ=34,e.$HASH=35,e.$$=36,e.$PERCENT=37,e.$AMPERSAND=38,e.$SQ=39,e.$LPAREN=40,e.$RPAREN=41,e.$STAR=42,e.$PLUS=43,e.$COMMA=44,e.$MINUS=45,e.$PERIOD=46,e.$SLASH=47,e.$COLON=58,e.$SEMICOLON=59,e.$LT=60,e.$EQ=61,e.$GT=62,e.$QUESTION=63,e.$0=48,e.$7=55,e.$9=57,e.$A=65,e.$E=69,e.$F=70,e.$X=88,e.$Z=90,e.$LBRACKET=91,e.$BACKSLASH=92,e.$RBRACKET=93,e.$CARET=94,e.$_=95,e.$a=97,e.$b=98,e.$e=101,e.$f=102,e.$n=110,e.$r=114,e.$t=116,e.$u=117,e.$v=118,e.$x=120,e.$z=122,e.$LBRACE=123,e.$BAR=124,e.$RBRACE=125,e.$NBSP=160,e.$PIPE=124,e.$TILDA=126,e.$AT=64,e.$BT=96;function r(f){return f>=e.$TAB&&f<=e.$SPACE||f==e.$NBSP}e.isWhitespace=r;function u(f){return e.$0<=f&&f<=e.$9}e.isDigit=u;function n(f){return f>=e.$a&&f<=e.$z||f>=e.$A&&f<=e.$Z}e.isAsciiLetter=n;function D(f){return f>=e.$a&&f<=e.$f||f>=e.$A&&f<=e.$F||u(f)}e.isAsciiHexDigit=D;function s(f){return f===e.$LF||f===e.$CR}e.isNewLine=s;function i(f){return e.$0<=f&&f<=e.$7}e.isOctalDigit=i}}),sl=I({"node_modules/angular-html-parser/lib/compiler/src/aot/static_symbol.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=class{constructor(n,D,s){this.filePath=n,this.name=D,this.members=s}assertNoMembers(){if(this.members.length)throw new Error(`Illegal state: symbol without members expected, but got ${JSON.stringify(this)}.`)}};e.StaticSymbol=r;var u=class{constructor(){this.cache=new Map}get(n,D,s){s=s||[];let i=s.length?`.${s.join(".")}`:"",f=`"${n}".${D}${i}`,c=this.cache.get(f);return c||(c=new r(n,D,s),this.cache.set(f,c)),c}};e.StaticSymbolCache=u}}),il=I({"node_modules/angular-html-parser/lib/compiler/src/util.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=/-+([a-z0-9])/g;function u(o){return o.replace(r,function(){for(var E=arguments.length,p=new Array(E),A=0;A<E;A++)p[A]=arguments[A];return p[1].toUpperCase()})}e.dashCaseToCamelCase=u;function n(o,E){return s(o,":",E)}e.splitAtColon=n;function D(o,E){return s(o,".",E)}e.splitAtPeriod=D;function s(o,E,p){let A=o.indexOf(E);return A==-1?p:[o.slice(0,A).trim(),o.slice(A+1).trim()]}function i(o,E,p){return Array.isArray(o)?E.visitArray(o,p):g(o)?E.visitStringMap(o,p):o==null||typeof o=="string"||typeof o=="number"||typeof o=="boolean"?E.visitPrimitive(o,p):E.visitOther(o,p)}e.visitValue=i;function f(o){return o!=null}e.isDefined=f;function c(o){return o===void 0?null:o}e.noUndefined=c;var F=class{visitArray(o,E){return o.map(p=>i(p,this,E))}visitStringMap(o,E){let p={};return Object.keys(o).forEach(A=>{p[A]=i(o[A],this,E)}),p}visitPrimitive(o,E){return o}visitOther(o,E){return o}};e.ValueTransformer=F,e.SyncAsync={assertSync:o=>{if(_(o))throw new Error("Illegal state: value cannot be a promise");return o},then:(o,E)=>_(o)?o.then(E):E(o),all:o=>o.some(_)?Promise.all(o):o};function a(o){throw new Error(`Internal Error: ${o}`)}e.error=a;function l(o,E){let p=Error(o);return p[h]=!0,E&&(p[C]=E),p}e.syntaxError=l;var h="ngSyntaxError",C="ngParseErrors";function d(o){return o[h]}e.isSyntaxError=d;function m(o){return o[C]||[]}e.getParseErrors=m;function T(o){return o.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}e.escapeRegExp=T;var w=Object.getPrototypeOf({});function g(o){return typeof o=="object"&&o!==null&&Object.getPrototypeOf(o)===w}function N(o){let E="";for(let p=0;p<o.length;p++){let A=o.charCodeAt(p);if(A>=55296&&A<=56319&&o.length>p+1){let P=o.charCodeAt(p+1);P>=56320&&P<=57343&&(p++,A=(A-55296<<10)+P-56320+65536)}A<=127?E+=String.fromCharCode(A):A<=2047?E+=String.fromCharCode(A>>6&31|192,A&63|128):A<=65535?E+=String.fromCharCode(A>>12|224,A>>6&63|128,A&63|128):A<=2097151&&(E+=String.fromCharCode(A>>18&7|240,A>>12&63|128,A>>6&63|128,A&63|128))}return E}e.utf8Encode=N;function R(o){if(typeof o=="string")return o;if(o instanceof Array)return"["+o.map(R).join(", ")+"]";if(o==null)return""+o;if(o.overriddenName)return`${o.overriddenName}`;if(o.name)return`${o.name}`;if(!o.toString)return"object";let E=o.toString();if(E==null)return""+E;let p=E.indexOf(`
`);return p===-1?E:E.substring(0,p)}e.stringify=R;function j(o){return typeof o=="function"&&o.hasOwnProperty("__forward_ref__")?o():o}e.resolveForwardRef=j;function _(o){return!!o&&typeof o.then=="function"}e.isPromise=_;var O=class{constructor(o){this.full=o;let E=o.split(".");this.major=E[0],this.minor=E[1],this.patch=E.slice(2).join(".")}};e.Version=O;var x=typeof window<"u"&&window,k=typeof self<"u"&&typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&self,$=typeof globalThis<"u"&&globalThis,t=$||x||k;e.global=t}}),al=I({"node_modules/angular-html-parser/lib/compiler/src/compile_metadata.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=sl(),u=il(),n=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/;function D(p){return p.replace(/\W/g,"_")}e.sanitizeIdentifier=D;var s=0;function i(p){if(!p||!p.reference)return null;let A=p.reference;if(A instanceof r.StaticSymbol)return A.name;if(A.__anonymousType)return A.__anonymousType;let P=u.stringify(A);return P.indexOf("(")>=0?(P=`anonymous_${s++}`,A.__anonymousType=P):P=D(P),P}e.identifierName=i;function f(p){let A=p.reference;return A instanceof r.StaticSymbol?A.filePath:`./${u.stringify(A)}`}e.identifierModuleUrl=f;function c(p,A){return`View_${i({reference:p})}_${A}`}e.viewClassName=c;function F(p){return`RenderType_${i({reference:p})}`}e.rendererTypeName=F;function a(p){return`HostView_${i({reference:p})}`}e.hostViewClassName=a;function l(p){return`${i({reference:p})}NgFactory`}e.componentFactoryName=l;var h;(function(p){p[p.Pipe=0]="Pipe",p[p.Directive=1]="Directive",p[p.NgModule=2]="NgModule",p[p.Injectable=3]="Injectable"})(h=e.CompileSummaryKind||(e.CompileSummaryKind={}));function C(p){return p.value!=null?D(p.value):i(p.identifier)}e.tokenName=C;function d(p){return p.identifier!=null?p.identifier.reference:p.value}e.tokenReference=d;var m=class{constructor(){let{moduleUrl:p,styles:A,styleUrls:P}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.moduleUrl=p||null,this.styles=_(A),this.styleUrls=_(P)}};e.CompileStylesheetMetadata=m;var T=class{constructor(p){let{encapsulation:A,template:P,templateUrl:M,htmlAst:z,styles:V,styleUrls:X,externalStylesheets:H,animations:Q,ngContentSelectors:K,interpolation:J,isInline:v,preserveWhitespaces:y}=p;if(this.encapsulation=A,this.template=P,this.templateUrl=M,this.htmlAst=z,this.styles=_(V),this.styleUrls=_(X),this.externalStylesheets=_(H),this.animations=Q?x(Q):[],this.ngContentSelectors=K||[],J&&J.length!=2)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=J,this.isInline=v,this.preserveWhitespaces=y}toSummary(){return{ngContentSelectors:this.ngContentSelectors,encapsulation:this.encapsulation,styles:this.styles,animations:this.animations}}};e.CompileTemplateMetadata=T;var w=class{static create(p){let{isHost:A,type:P,isComponent:M,selector:z,exportAs:V,changeDetection:X,inputs:H,outputs:Q,host:K,providers:J,viewProviders:v,queries:y,guards:B,viewQueries:b,entryComponents:L,template:U,componentViewType:G,rendererType:W,componentFactory:ne}=p,be={},we={},Wr={};K!=null&&Object.keys(K).forEach(Z=>{let ie=K[Z],Ce=Z.match(n);Ce===null?Wr[Z]=ie:Ce[1]!=null?we[Ce[1]]=ie:Ce[2]!=null&&(be[Ce[2]]=ie)});let Yr={};H!=null&&H.forEach(Z=>{let ie=u.splitAtColon(Z,[Z,Z]);Yr[ie[0]]=ie[1]});let Qr={};return Q!=null&&Q.forEach(Z=>{let ie=u.splitAtColon(Z,[Z,Z]);Qr[ie[0]]=ie[1]}),new w({isHost:A,type:P,isComponent:!!M,selector:z,exportAs:V,changeDetection:X,inputs:Yr,outputs:Qr,hostListeners:be,hostProperties:we,hostAttributes:Wr,providers:J,viewProviders:v,queries:y,guards:B,viewQueries:b,entryComponents:L,template:U,componentViewType:G,rendererType:W,componentFactory:ne})}constructor(p){let{isHost:A,type:P,isComponent:M,selector:z,exportAs:V,changeDetection:X,inputs:H,outputs:Q,hostListeners:K,hostProperties:J,hostAttributes:v,providers:y,viewProviders:B,queries:b,guards:L,viewQueries:U,entryComponents:G,template:W,componentViewType:ne,rendererType:be,componentFactory:we}=p;this.isHost=!!A,this.type=P,this.isComponent=M,this.selector=z,this.exportAs=V,this.changeDetection=X,this.inputs=H,this.outputs=Q,this.hostListeners=K,this.hostProperties=J,this.hostAttributes=v,this.providers=_(y),this.viewProviders=_(B),this.queries=_(b),this.guards=L,this.viewQueries=_(U),this.entryComponents=_(G),this.template=W,this.componentViewType=ne,this.rendererType=be,this.componentFactory=we}toSummary(){return{summaryKind:h.Directive,type:this.type,isComponent:this.isComponent,selector:this.selector,exportAs:this.exportAs,inputs:this.inputs,outputs:this.outputs,hostListeners:this.hostListeners,hostProperties:this.hostProperties,hostAttributes:this.hostAttributes,providers:this.providers,viewProviders:this.viewProviders,queries:this.queries,guards:this.guards,viewQueries:this.viewQueries,entryComponents:this.entryComponents,changeDetection:this.changeDetection,template:this.template&&this.template.toSummary(),componentViewType:this.componentViewType,rendererType:this.rendererType,componentFactory:this.componentFactory}}};e.CompileDirectiveMetadata=w;var g=class{constructor(p){let{type:A,name:P,pure:M}=p;this.type=A,this.name=P,this.pure=!!M}toSummary(){return{summaryKind:h.Pipe,type:this.type,name:this.name,pure:this.pure}}};e.CompilePipeMetadata=g;var N=class{};e.CompileShallowModuleMetadata=N;var R=class{constructor(p){let{type:A,providers:P,declaredDirectives:M,exportedDirectives:z,declaredPipes:V,exportedPipes:X,entryComponents:H,bootstrapComponents:Q,importedModules:K,exportedModules:J,schemas:v,transitiveModule:y,id:B}=p;this.type=A||null,this.declaredDirectives=_(M),this.exportedDirectives=_(z),this.declaredPipes=_(V),this.exportedPipes=_(X),this.providers=_(P),this.entryComponents=_(H),this.bootstrapComponents=_(Q),this.importedModules=_(K),this.exportedModules=_(J),this.schemas=_(v),this.id=B||null,this.transitiveModule=y||null}toSummary(){let p=this.transitiveModule;return{summaryKind:h.NgModule,type:this.type,entryComponents:p.entryComponents,providers:p.providers,modules:p.modules,exportedDirectives:p.exportedDirectives,exportedPipes:p.exportedPipes}}};e.CompileNgModuleMetadata=R;var j=class{constructor(){this.directivesSet=new Set,this.directives=[],this.exportedDirectivesSet=new Set,this.exportedDirectives=[],this.pipesSet=new Set,this.pipes=[],this.exportedPipesSet=new Set,this.exportedPipes=[],this.modulesSet=new Set,this.modules=[],this.entryComponentsSet=new Set,this.entryComponents=[],this.providers=[]}addProvider(p,A){this.providers.push({provider:p,module:A})}addDirective(p){this.directivesSet.has(p.reference)||(this.directivesSet.add(p.reference),this.directives.push(p))}addExportedDirective(p){this.exportedDirectivesSet.has(p.reference)||(this.exportedDirectivesSet.add(p.reference),this.exportedDirectives.push(p))}addPipe(p){this.pipesSet.has(p.reference)||(this.pipesSet.add(p.reference),this.pipes.push(p))}addExportedPipe(p){this.exportedPipesSet.has(p.reference)||(this.exportedPipesSet.add(p.reference),this.exportedPipes.push(p))}addModule(p){this.modulesSet.has(p.reference)||(this.modulesSet.add(p.reference),this.modules.push(p))}addEntryComponent(p){this.entryComponentsSet.has(p.componentType)||(this.entryComponentsSet.add(p.componentType),this.entryComponents.push(p))}};e.TransitiveCompileNgModuleMetadata=j;function _(p){return p||[]}var O=class{constructor(p,A){let{useClass:P,useValue:M,useExisting:z,useFactory:V,deps:X,multi:H}=A;this.token=p,this.useClass=P||null,this.useValue=M,this.useExisting=z,this.useFactory=V||null,this.dependencies=X||null,this.multi=!!H}};e.ProviderMeta=O;function x(p){return p.reduce((A,P)=>{let M=Array.isArray(P)?x(P):P;return A.concat(M)},[])}e.flatten=x;function k(p){return p.replace(/(\w+:\/\/[\w:-]+)?(\/+)?/,"ng:///")}function $(p,A,P){let M;return P.isInline?A.type.reference instanceof r.StaticSymbol?M=`${A.type.reference.filePath}.${A.type.reference.name}.html`:M=`${i(p)}/${i(A.type)}.html`:M=P.templateUrl,A.type.reference instanceof r.StaticSymbol?M:k(M)}e.templateSourceUrl=$;function t(p,A){let P=p.moduleUrl.split(/\/\\/g),M=P[P.length-1];return k(`css/${A}${M}.ngstyle.js`)}e.sharedStylesheetJitUrl=t;function o(p){return k(`${i(p.type)}/module.ngfactory.js`)}e.ngModuleJitUrl=o;function E(p,A){return k(`${i(p)}/${i(A.type)}.ngfactory.js`)}e.templateJitUrl=E}}),Be=I({"node_modules/angular-html-parser/lib/compiler/src/parse_util.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Es(),u=al(),n=class{constructor(a,l,h,C){this.file=a,this.offset=l,this.line=h,this.col=C}toString(){return this.offset!=null?`${this.file.url}@${this.line}:${this.col}`:this.file.url}moveBy(a){let l=this.file.content,h=l.length,C=this.offset,d=this.line,m=this.col;for(;C>0&&a<0;)if(C--,a++,l.charCodeAt(C)==r.$LF){d--;let w=l.substr(0,C-1).lastIndexOf(String.fromCharCode(r.$LF));m=w>0?C-w:C}else m--;for(;C<h&&a>0;){let T=l.charCodeAt(C);C++,a--,T==r.$LF?(d++,m=0):m++}return new n(this.file,C,d,m)}getContext(a,l){let h=this.file.content,C=this.offset;if(C!=null){C>h.length-1&&(C=h.length-1);let d=C,m=0,T=0;for(;m<a&&C>0&&(C--,m++,!(h[C]==`
`&&++T==l)););for(m=0,T=0;m<a&&d<h.length-1&&(d++,m++,!(h[d]==`
`&&++T==l)););return{before:h.substring(C,this.offset),after:h.substring(this.offset,d+1)}}return null}};e.ParseLocation=n;var D=class{constructor(a,l){this.content=a,this.url=l}};e.ParseSourceFile=D;var s=class{constructor(a,l){let h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;this.start=a,this.end=l,this.details=h}toString(){return this.start.file.content.substring(this.start.offset,this.end.offset)}};e.ParseSourceSpan=s,e.EMPTY_PARSE_LOCATION=new n(new D("",""),0,0,0),e.EMPTY_SOURCE_SPAN=new s(e.EMPTY_PARSE_LOCATION,e.EMPTY_PARSE_LOCATION);var i;(function(a){a[a.WARNING=0]="WARNING",a[a.ERROR=1]="ERROR"})(i=e.ParseErrorLevel||(e.ParseErrorLevel={}));var f=class{constructor(a,l){let h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:i.ERROR;this.span=a,this.msg=l,this.level=h}contextualMessage(){let a=this.span.start.getContext(100,3);return a?`${this.msg} ("${a.before}[${i[this.level]} ->]${a.after}")`:this.msg}toString(){let a=this.span.details?`, ${this.span.details}`:"";return`${this.contextualMessage()}: ${this.span.start}${a}`}};e.ParseError=f;function c(a,l){let h=u.identifierModuleUrl(l),C=h!=null?`in ${a} ${u.identifierName(l)} in ${h}`:`in ${a} ${u.identifierName(l)}`,d=new D("",C);return new s(new n(d,-1,-1,-1),new n(d,-1,-1,-1))}e.typeSourceSpan=c;function F(a,l,h){let C=`in ${a} ${l} in ${h}`,d=new D("",C);return new s(new n(d,-1,-1,-1),new n(d,-1,-1,-1))}e.r3JitTypeSourceSpan=F}}),ol=I({"src/utils/front-matter/parse.js"(e,r){"use strict";q();var u=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");function n(D){let s=D.match(u);if(!s)return{content:D};let{startDelimiter:i,language:f,value:c="",endDelimiter:F}=s.groups,a=f.trim()||"yaml";if(i==="+++"&&(a="toml"),a!=="yaml"&&i!==F)return{content:D};let[l]=s;return{frontMatter:{type:"front-matter",lang:a,value:c,startDelimiter:i,endDelimiter:F,raw:l.replace(/\n$/,"")},content:l.replace(/[^\n]/g," ")+D.slice(l.length)}}r.exports=n}}),Cs=I({"src/utils/get-last.js"(e,r){"use strict";q();var u=n=>n[n.length-1];r.exports=u}}),Dl=I({"src/common/parser-create-error.js"(e,r){"use strict";q();function u(n,D){let s=new SyntaxError(n+" ("+D.start.line+":"+D.start.column+")");return s.loc=D,s}r.exports=u}}),ms={};ps(ms,{default:()=>ll});function ll(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var cl=Ee({"node_modules/escape-string-regexp/index.js"(){q()}}),gs=I({"node_modules/semver/internal/debug.js"(e,r){q();var u=typeof Te=="object"&&Te.env&&Te.env.NODE_DEBUG&&/\bsemver\b/i.test(Te.env.NODE_DEBUG)?function(){for(var n=arguments.length,D=new Array(n),s=0;s<n;s++)D[s]=arguments[s];return console.error("SEMVER",...D)}:()=>{};r.exports=u}}),Fs=I({"node_modules/semver/internal/constants.js"(e,r){q();var u="2.0.0",n=256,D=Number.MAX_SAFE_INTEGER||9007199254740991,s=16;r.exports={SEMVER_SPEC_VERSION:u,MAX_LENGTH:n,MAX_SAFE_INTEGER:D,MAX_SAFE_COMPONENT_LENGTH:s}}}),hl=I({"node_modules/semver/internal/re.js"(e,r){q();var{MAX_SAFE_COMPONENT_LENGTH:u}=Fs(),n=gs();e=r.exports={};var D=e.re=[],s=e.src=[],i=e.t={},f=0,c=(F,a,l)=>{let h=f++;n(F,h,a),i[F]=h,s[h]=a,D[h]=new RegExp(a,l?"g":void 0)};c("NUMERICIDENTIFIER","0|[1-9]\\d*"),c("NUMERICIDENTIFIERLOOSE","[0-9]+"),c("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),c("MAINVERSION",`(${s[i.NUMERICIDENTIFIER]})\\.(${s[i.NUMERICIDENTIFIER]})\\.(${s[i.NUMERICIDENTIFIER]})`),c("MAINVERSIONLOOSE",`(${s[i.NUMERICIDENTIFIERLOOSE]})\\.(${s[i.NUMERICIDENTIFIERLOOSE]})\\.(${s[i.NUMERICIDENTIFIERLOOSE]})`),c("PRERELEASEIDENTIFIER",`(?:${s[i.NUMERICIDENTIFIER]}|${s[i.NONNUMERICIDENTIFIER]})`),c("PRERELEASEIDENTIFIERLOOSE",`(?:${s[i.NUMERICIDENTIFIERLOOSE]}|${s[i.NONNUMERICIDENTIFIER]})`),c("PRERELEASE",`(?:-(${s[i.PRERELEASEIDENTIFIER]}(?:\\.${s[i.PRERELEASEIDENTIFIER]})*))`),c("PRERELEASELOOSE",`(?:-?(${s[i.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${s[i.PRERELEASEIDENTIFIERLOOSE]})*))`),c("BUILDIDENTIFIER","[0-9A-Za-z-]+"),c("BUILD",`(?:\\+(${s[i.BUILDIDENTIFIER]}(?:\\.${s[i.BUILDIDENTIFIER]})*))`),c("FULLPLAIN",`v?${s[i.MAINVERSION]}${s[i.PRERELEASE]}?${s[i.BUILD]}?`),c("FULL",`^${s[i.FULLPLAIN]}$`),c("LOOSEPLAIN",`[v=\\s]*${s[i.MAINVERSIONLOOSE]}${s[i.PRERELEASELOOSE]}?${s[i.BUILD]}?`),c("LOOSE",`^${s[i.LOOSEPLAIN]}$`),c("GTLT","((?:<|>)?=?)"),c("XRANGEIDENTIFIERLOOSE",`${s[i.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),c("XRANGEIDENTIFIER",`${s[i.NUMERICIDENTIFIER]}|x|X|\\*`),c("XRANGEPLAIN",`[v=\\s]*(${s[i.XRANGEIDENTIFIER]})(?:\\.(${s[i.XRANGEIDENTIFIER]})(?:\\.(${s[i.XRANGEIDENTIFIER]})(?:${s[i.PRERELEASE]})?${s[i.BUILD]}?)?)?`),c("XRANGEPLAINLOOSE",`[v=\\s]*(${s[i.XRANGEIDENTIFIERLOOSE]})(?:\\.(${s[i.XRANGEIDENTIFIERLOOSE]})(?:\\.(${s[i.XRANGEIDENTIFIERLOOSE]})(?:${s[i.PRERELEASELOOSE]})?${s[i.BUILD]}?)?)?`),c("XRANGE",`^${s[i.GTLT]}\\s*${s[i.XRANGEPLAIN]}$`),c("XRANGELOOSE",`^${s[i.GTLT]}\\s*${s[i.XRANGEPLAINLOOSE]}$`),c("COERCE",`(^|[^\\d])(\\d{1,${u}})(?:\\.(\\d{1,${u}}))?(?:\\.(\\d{1,${u}}))?(?:$|[^\\d])`),c("COERCERTL",s[i.COERCE],!0),c("LONETILDE","(?:~>?)"),c("TILDETRIM",`(\\s*)${s[i.LONETILDE]}\\s+`,!0),e.tildeTrimReplace="$1~",c("TILDE",`^${s[i.LONETILDE]}${s[i.XRANGEPLAIN]}$`),c("TILDELOOSE",`^${s[i.LONETILDE]}${s[i.XRANGEPLAINLOOSE]}$`),c("LONECARET","(?:\\^)"),c("CARETTRIM",`(\\s*)${s[i.LONECARET]}\\s+`,!0),e.caretTrimReplace="$1^",c("CARET",`^${s[i.LONECARET]}${s[i.XRANGEPLAIN]}$`),c("CARETLOOSE",`^${s[i.LONECARET]}${s[i.XRANGEPLAINLOOSE]}$`),c("COMPARATORLOOSE",`^${s[i.GTLT]}\\s*(${s[i.LOOSEPLAIN]})$|^$`),c("COMPARATOR",`^${s[i.GTLT]}\\s*(${s[i.FULLPLAIN]})$|^$`),c("COMPARATORTRIM",`(\\s*)${s[i.GTLT]}\\s*(${s[i.LOOSEPLAIN]}|${s[i.XRANGEPLAIN]})`,!0),e.comparatorTrimReplace="$1$2$3",c("HYPHENRANGE",`^\\s*(${s[i.XRANGEPLAIN]})\\s+-\\s+(${s[i.XRANGEPLAIN]})\\s*$`),c("HYPHENRANGELOOSE",`^\\s*(${s[i.XRANGEPLAINLOOSE]})\\s+-\\s+(${s[i.XRANGEPLAINLOOSE]})\\s*$`),c("STAR","(<|>)?=?\\s*\\*"),c("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),c("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}}),pl=I({"node_modules/semver/internal/parse-options.js"(e,r){q();var u=["includePrerelease","loose","rtl"],n=D=>D?typeof D!="object"?{loose:!0}:u.filter(s=>D[s]).reduce((s,i)=>(s[i]=!0,s),{}):{};r.exports=n}}),fl=I({"node_modules/semver/internal/identifiers.js"(e,r){q();var u=/^[0-9]+$/,n=(s,i)=>{let f=u.test(s),c=u.test(i);return f&&c&&(s=+s,i=+i),s===i?0:f&&!c?-1:c&&!f?1:s<i?-1:1},D=(s,i)=>n(i,s);r.exports={compareIdentifiers:n,rcompareIdentifiers:D}}}),dl=I({"node_modules/semver/classes/semver.js"(e,r){q();var u=gs(),{MAX_LENGTH:n,MAX_SAFE_INTEGER:D}=Fs(),{re:s,t:i}=hl(),f=pl(),{compareIdentifiers:c}=fl(),F=class{constructor(a,l){if(l=f(l),a instanceof F){if(a.loose===!!l.loose&&a.includePrerelease===!!l.includePrerelease)return a;a=a.version}else if(typeof a!="string")throw new TypeError(`Invalid Version: ${a}`);if(a.length>n)throw new TypeError(`version is longer than ${n} characters`);u("SemVer",a,l),this.options=l,this.loose=!!l.loose,this.includePrerelease=!!l.includePrerelease;let h=a.trim().match(l.loose?s[i.LOOSE]:s[i.FULL]);if(!h)throw new TypeError(`Invalid Version: ${a}`);if(this.raw=a,this.major=+h[1],this.minor=+h[2],this.patch=+h[3],this.major>D||this.major<0)throw new TypeError("Invalid major version");if(this.minor>D||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>D||this.patch<0)throw new TypeError("Invalid patch version");h[4]?this.prerelease=h[4].split(".").map(C=>{if(/^[0-9]+$/.test(C)){let d=+C;if(d>=0&&d<D)return d}return C}):this.prerelease=[],this.build=h[5]?h[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(a){if(u("SemVer.compare",this.version,this.options,a),!(a instanceof F)){if(typeof a=="string"&&a===this.version)return 0;a=new F(a,this.options)}return a.version===this.version?0:this.compareMain(a)||this.comparePre(a)}compareMain(a){return a instanceof F||(a=new F(a,this.options)),c(this.major,a.major)||c(this.minor,a.minor)||c(this.patch,a.patch)}comparePre(a){if(a instanceof F||(a=new F(a,this.options)),this.prerelease.length&&!a.prerelease.length)return-1;if(!this.prerelease.length&&a.prerelease.length)return 1;if(!this.prerelease.length&&!a.prerelease.length)return 0;let l=0;do{let h=this.prerelease[l],C=a.prerelease[l];if(u("prerelease compare",l,h,C),h===void 0&&C===void 0)return 0;if(C===void 0)return 1;if(h===void 0)return-1;if(h===C)continue;return c(h,C)}while(++l)}compareBuild(a){a instanceof F||(a=new F(a,this.options));let l=0;do{let h=this.build[l],C=a.build[l];if(u("prerelease compare",l,h,C),h===void 0&&C===void 0)return 0;if(C===void 0)return 1;if(h===void 0)return-1;if(h===C)continue;return c(h,C)}while(++l)}inc(a,l){switch(a){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",l);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",l);break;case"prepatch":this.prerelease.length=0,this.inc("patch",l),this.inc("pre",l);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",l),this.inc("pre",l);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{let h=this.prerelease.length;for(;--h>=0;)typeof this.prerelease[h]=="number"&&(this.prerelease[h]++,h=-2);h===-1&&this.prerelease.push(0)}l&&(c(this.prerelease[0],l)===0?isNaN(this.prerelease[1])&&(this.prerelease=[l,0]):this.prerelease=[l,0]);break;default:throw new Error(`invalid increment argument: ${a}`)}return this.format(),this.raw=this.version,this}};r.exports=F}}),Hr=I({"node_modules/semver/functions/compare.js"(e,r){q();var u=dl(),n=(D,s,i)=>new u(D,i).compare(new u(s,i));r.exports=n}}),El=I({"node_modules/semver/functions/lt.js"(e,r){q();var u=Hr(),n=(D,s,i)=>u(D,s,i)<0;r.exports=n}}),Cl=I({"node_modules/semver/functions/gte.js"(e,r){q();var u=Hr(),n=(D,s,i)=>u(D,s,i)>=0;r.exports=n}}),ml=I({"src/utils/arrayify.js"(e,r){"use strict";q(),r.exports=(u,n)=>Object.entries(u).map(D=>{let[s,i]=D;return Object.assign({[n]:s},i)})}}),gl=I({"package.json"(e,r){r.exports={version:"2.8.8"}}}),Fl=I({"node_modules/outdent/lib/index.js"(e,r){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0}),e.outdent=void 0;function u(){for(var g=[],N=0;N<arguments.length;N++)g[N]=arguments[N]}function n(){return typeof WeakMap<"u"?new WeakMap:D()}function D(){return{add:u,delete:u,get:u,set:u,has:function(g){return!1}}}var s=Object.prototype.hasOwnProperty,i=function(g,N){return s.call(g,N)};function f(g,N){for(var R in N)i(N,R)&&(g[R]=N[R]);return g}var c=/^[ \t]*(?:\r\n|\r|\n)/,F=/(?:\r\n|\r|\n)[ \t]*$/,a=/^(?:[\r\n]|$)/,l=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,h=/^[ \t]*[\r\n][ \t\r\n]*$/;function C(g,N,R){var j=0,_=g[0].match(l);_&&(j=_[1].length);var O="(\\r\\n|\\r|\\n).{0,"+j+"}",x=new RegExp(O,"g");N&&(g=g.slice(1));var k=R.newline,$=R.trimLeadingNewline,t=R.trimTrailingNewline,o=typeof k=="string",E=g.length,p=g.map(function(A,P){return A=A.replace(x,"$1"),P===0&&$&&(A=A.replace(c,"")),P===E-1&&t&&(A=A.replace(F,"")),o&&(A=A.replace(/\r\n|\n|\r/g,function(M){return k})),A});return p}function d(g,N){for(var R="",j=0,_=g.length;j<_;j++)R+=g[j],j<_-1&&(R+=N[j]);return R}function m(g){return i(g,"raw")&&i(g,"length")}function T(g){var N=n(),R=n();function j(O){for(var x=[],k=1;k<arguments.length;k++)x[k-1]=arguments[k];if(m(O)){var $=O,t=(x[0]===j||x[0]===w)&&h.test($[0])&&a.test($[1]),o=t?R:N,E=o.get($);if(E||(E=C($,t,g),o.set($,E)),x.length===0)return E[0];var p=d(E,t?x.slice(1):x);return p}else return T(f(f({},g),O||{}))}var _=f(j,{string:function(O){return C([O],!1,g)[0]}});return _}var w=T({trimLeadingNewline:!0,trimTrailingNewline:!0});if(e.outdent=w,e.default=w,typeof r<"u")try{r.exports=w,Object.defineProperty(w,"__esModule",{value:!0}),w.default=w,w.outdent=w}catch{}}}),Al=I({"src/main/core-options.js"(e,r){"use strict";q();var{outdent:u}=Fl(),n="Config",D="Editor",s="Format",i="Other",f="Output",c="Global",F="Special",a={cursorOffset:{since:"1.4.0",category:F,type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:D},endOfLine:{since:"1.15.0",category:c,type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:u`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:F,type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:i,cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:F,type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:i},parser:{since:"0.0.10",category:c,type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:l=>typeof l=="string"||typeof l=="function",choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"acorn",since:"2.6.0",description:"JavaScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:c,description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:l=>typeof l=="string"||typeof l=="object",cliName:"plugin",cliCategory:n},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:c,description:u`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:l=>typeof l=="string"||typeof l=="object",cliName:"plugin-search-dir",cliCategory:n},printWidth:{since:"0.0.0",category:c,type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:F,type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:D},rangeStart:{since:"1.4.0",category:F,type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:D},requirePragma:{since:"1.7.0",category:F,type:"boolean",default:!1,description:u`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:i},tabWidth:{type:"int",category:c,default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:c,type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:c,type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};r.exports={CATEGORY_CONFIG:n,CATEGORY_EDITOR:D,CATEGORY_FORMAT:s,CATEGORY_OTHER:i,CATEGORY_OUTPUT:f,CATEGORY_GLOBAL:c,CATEGORY_SPECIAL:F,options:a}}}),vl=I({"src/main/support.js"(e,r){"use strict";q();var u={compare:Hr(),lt:El(),gte:Cl()},n=ml(),D=gl().version,s=Al().options;function i(){let{plugins:c=[],showUnreleased:F=!1,showDeprecated:a=!1,showInternal:l=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},h=D.split("-",1)[0],C=c.flatMap(g=>g.languages||[]).filter(m),d=n(Object.assign({},...c.map(g=>{let{options:N}=g;return N}),s),"name").filter(g=>m(g)&&T(g)).sort((g,N)=>g.name===N.name?0:g.name<N.name?-1:1).map(w).map(g=>{g=Object.assign({},g),Array.isArray(g.default)&&(g.default=g.default.length===1?g.default[0].value:g.default.filter(m).sort((R,j)=>u.compare(j.since,R.since))[0].value),Array.isArray(g.choices)&&(g.choices=g.choices.filter(R=>m(R)&&T(R)),g.name==="parser"&&f(g,C,c));let N=Object.fromEntries(c.filter(R=>R.defaultOptions&&R.defaultOptions[g.name]!==void 0).map(R=>[R.name,R.defaultOptions[g.name]]));return Object.assign(Object.assign({},g),{},{pluginDefaults:N})});return{languages:C,options:d};function m(g){return F||!("since"in g)||g.since&&u.gte(h,g.since)}function T(g){return a||!("deprecated"in g)||g.deprecated&&u.lt(h,g.deprecated)}function w(g){if(l)return g;let{cliName:N,cliCategory:R,cliDescription:j}=g;return JD(g,KD)}}function f(c,F,a){let l=new Set(c.choices.map(h=>h.value));for(let h of F)if(h.parsers){for(let C of h.parsers)if(!l.has(C)){l.add(C);let d=a.find(T=>T.parsers&&T.parsers[C]),m=h.name;d&&d.name&&(m+=` (plugin: ${d.name})`),c.choices.push({value:C,description:m})}}}r.exports={getSupportInfo:i}}}),_l=I({"src/utils/is-non-empty-array.js"(e,r){"use strict";q();function u(n){return Array.isArray(n)&&n.length>0}r.exports=u}});function Sl(){let{onlyFirst:e=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var yl=Ee({"node_modules/strip-ansi/node_modules/ansi-regex/index.js"(){q()}});function Tl(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(Sl(),"")}var Bl=Ee({"node_modules/strip-ansi/index.js"(){q(),yl()}});function bl(e){return Number.isInteger(e)?e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141):!1}var wl=Ee({"node_modules/is-fullwidth-code-point/index.js"(){q()}}),Nl=I({"node_modules/emoji-regex/index.js"(e,r){"use strict";q(),r.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}}}),As={};ps(As,{default:()=>Ol});function Ol(e){if(typeof e!="string"||e.length===0||(e=Tl(e),e.length===0))return 0;e=e.replace((0,vs.default)(),"  ");let r=0;for(let u=0;u<e.length;u++){let n=e.codePointAt(u);n<=31||n>=127&&n<=159||n>=768&&n<=879||(n>65535&&u++,r+=bl(n)?2:1)}return r}var vs,ql=Ee({"node_modules/string-width/index.js"(){q(),Bl(),wl(),vs=nl(Nl())}}),Il=I({"src/utils/get-string-width.js"(e,r){"use strict";q();var u=(ql(),ds(As)).default,n=/[^\x20-\x7F]/;function D(s){return s?n.test(s)?u(s):s.length:0}r.exports=D}}),zr=I({"src/utils/text/skip.js"(e,r){"use strict";q();function u(f){return(c,F,a)=>{let l=a&&a.backwards;if(F===!1)return!1;let{length:h}=c,C=F;for(;C>=0&&C<h;){let d=c.charAt(C);if(f instanceof RegExp){if(!f.test(d))return C}else if(!f.includes(d))return C;l?C--:C++}return C===-1||C===h?C:!1}}var n=u(/\s/),D=u(" 	"),s=u(",; 	"),i=u(/[^\n\r]/);r.exports={skipWhitespace:n,skipSpaces:D,skipToLineEnd:s,skipEverythingButNewLine:i}}}),_s=I({"src/utils/text/skip-inline-comment.js"(e,r){"use strict";q();function u(n,D){if(D===!1)return!1;if(n.charAt(D)==="/"&&n.charAt(D+1)==="*"){for(let s=D+2;s<n.length;++s)if(n.charAt(s)==="*"&&n.charAt(s+1)==="/")return s+2}return D}r.exports=u}}),Ss=I({"src/utils/text/skip-trailing-comment.js"(e,r){"use strict";q();var{skipEverythingButNewLine:u}=zr();function n(D,s){return s===!1?!1:D.charAt(s)==="/"&&D.charAt(s+1)==="/"?u(D,s):s}r.exports=n}}),ys=I({"src/utils/text/skip-newline.js"(e,r){"use strict";q();function u(n,D,s){let i=s&&s.backwards;if(D===!1)return!1;let f=n.charAt(D);if(i){if(n.charAt(D-1)==="\r"&&f===`
`)return D-2;if(f===`
`||f==="\r"||f==="\u2028"||f==="\u2029")return D-1}else{if(f==="\r"&&n.charAt(D+1)===`
`)return D+2;if(f===`
`||f==="\r"||f==="\u2028"||f==="\u2029")return D+1}return D}r.exports=u}}),Rl=I({"src/utils/text/get-next-non-space-non-comment-character-index-with-start-index.js"(e,r){"use strict";q();var u=_s(),n=ys(),D=Ss(),{skipSpaces:s}=zr();function i(f,c){let F=null,a=c;for(;a!==F;)F=a,a=s(f,a),a=u(f,a),a=D(f,a),a=n(f,a);return a}r.exports=i}}),xl=I({"src/common/util.js"(e,r){"use strict";q();var{default:u}=(cl(),ds(ms)),n=Cs(),{getSupportInfo:D}=vl(),s=_l(),i=Il(),{skipWhitespace:f,skipSpaces:c,skipToLineEnd:F,skipEverythingButNewLine:a}=zr(),l=_s(),h=Ss(),C=ys(),d=Rl(),m=v=>v[v.length-2];function T(v){return(y,B,b)=>{let L=b&&b.backwards;if(B===!1)return!1;let{length:U}=y,G=B;for(;G>=0&&G<U;){let W=y.charAt(G);if(v instanceof RegExp){if(!v.test(W))return G}else if(!v.includes(W))return G;L?G--:G++}return G===-1||G===U?G:!1}}function w(v,y){let B=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},b=c(v,B.backwards?y-1:y,B),L=C(v,b,B);return b!==L}function g(v,y,B){for(let b=y;b<B;++b)if(v.charAt(b)===`
`)return!0;return!1}function N(v,y,B){let b=B(y)-1;b=c(v,b,{backwards:!0}),b=C(v,b,{backwards:!0}),b=c(v,b,{backwards:!0});let L=C(v,b,{backwards:!0});return b!==L}function R(v,y){let B=null,b=y;for(;b!==B;)B=b,b=F(v,b),b=l(v,b),b=c(v,b);return b=h(v,b),b=C(v,b),b!==!1&&w(v,b)}function j(v,y,B){return R(v,B(y))}function _(v,y,B){return d(v,B(y))}function O(v,y,B){return v.charAt(_(v,y,B))}function x(v,y){let B=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return c(v,B.backwards?y-1:y,B)!==y}function k(v,y){let B=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,b=0;for(let L=B;L<v.length;++L)v[L]==="	"?b=b+y-b%y:b++;return b}function $(v,y){let B=v.lastIndexOf(`
`);return B===-1?0:k(v.slice(B+1).match(/^[\t ]*/)[0],y)}function t(v,y){let B={quote:'"',regex:/"/g,escaped:"&quot;"},b={quote:"'",regex:/'/g,escaped:"&apos;"},L=y==="'"?b:B,U=L===b?B:b,G=L;if(v.includes(L.quote)||v.includes(U.quote)){let W=(v.match(L.regex)||[]).length,ne=(v.match(U.regex)||[]).length;G=W>ne?U:L}return G}function o(v,y){let B=v.slice(1,-1),b=y.parser==="json"||y.parser==="json5"&&y.quoteProps==="preserve"&&!y.singleQuote?'"':y.__isInHtmlAttribute?"'":t(B,y.singleQuote?"'":'"').quote;return E(B,b,!(y.parser==="css"||y.parser==="less"||y.parser==="scss"||y.__embeddedInHtml))}function E(v,y,B){let b=y==='"'?"'":'"',L=/\\(.)|(["'])/gs,U=v.replace(L,(G,W,ne)=>W===b?W:ne===y?"\\"+ne:ne||(B&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(W)?W:"\\"+W));return y+U+y}function p(v){return v.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")}function A(v,y){let B=v.match(new RegExp(`(${u(y)})+`,"g"));return B===null?0:B.reduce((b,L)=>Math.max(b,L.length/y.length),0)}function P(v,y){let B=v.match(new RegExp(`(${u(y)})+`,"g"));if(B===null)return 0;let b=new Map,L=0;for(let U of B){let G=U.length/y.length;b.set(G,!0),G>L&&(L=G)}for(let U=1;U<L;U++)if(!b.get(U))return U;return L+1}function M(v,y){(v.comments||(v.comments=[])).push(y),y.printed=!1,y.nodeDescription=J(v)}function z(v,y){y.leading=!0,y.trailing=!1,M(v,y)}function V(v,y,B){y.leading=!1,y.trailing=!1,B&&(y.marker=B),M(v,y)}function X(v,y){y.leading=!1,y.trailing=!0,M(v,y)}function H(v,y){let{languages:B}=D({plugins:y.plugins}),b=B.find(L=>{let{name:U}=L;return U.toLowerCase()===v})||B.find(L=>{let{aliases:U}=L;return Array.isArray(U)&&U.includes(v)})||B.find(L=>{let{extensions:U}=L;return Array.isArray(U)&&U.includes(`.${v}`)});return b&&b.parsers[0]}function Q(v){return v&&v.type==="front-matter"}function K(v){let y=new WeakMap;return function(B){return y.has(B)||y.set(B,Symbol(v)),y.get(B)}}function J(v){let y=v.type||v.kind||"(unknown type)",B=String(v.name||v.id&&(typeof v.id=="object"?v.id.name:v.id)||v.key&&(typeof v.key=="object"?v.key.name:v.key)||v.value&&(typeof v.value=="object"?"":String(v.value))||v.operator||"");return B.length>20&&(B=B.slice(0,19)+"\u2026"),y+(B?" "+B:"")}r.exports={inferParserByLanguage:H,getStringWidth:i,getMaxContinuousCount:A,getMinNotPresentContinuousCount:P,getPenultimate:m,getLast:n,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:d,getNextNonSpaceNonCommentCharacterIndex:_,getNextNonSpaceNonCommentCharacter:O,skip:T,skipWhitespace:f,skipSpaces:c,skipToLineEnd:F,skipEverythingButNewLine:a,skipInlineComment:l,skipTrailingComment:h,skipNewline:C,isNextLineEmptyAfterIndex:R,isNextLineEmpty:j,isPreviousLineEmpty:N,hasNewline:w,hasNewlineInRange:g,hasSpaces:x,getAlignmentSize:k,getIndentSize:$,getPreferredQuote:t,printString:o,printNumber:p,makeString:E,addLeadingComment:z,addDanglingComment:V,addTrailingComment:X,isFrontMatterNode:Q,isNonEmptyArray:s,createGroupIdMapper:K}}}),Pl=I({"vendors/html-tag-names.json"(e,r){r.exports={htmlTagNames:["a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","bgsound","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","command","content","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","element","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","image","img","input","ins","isindex","kbd","keygen","label","legend","li","link","listing","main","map","mark","marquee","math","menu","menuitem","meta","meter","multicol","nav","nextid","nobr","noembed","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","plaintext","pre","progress","q","rb","rbc","rp","rt","rtc","ruby","s","samp","script","section","select","shadow","slot","small","source","spacer","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr","xmp"]}}}),Ts=I({"src/language-html/utils/array-to-map.js"(e,r){"use strict";q();function u(n){let D=Object.create(null);for(let s of n)D[s]=!0;return D}r.exports=u}}),kl=I({"src/language-html/utils/html-tag-names.js"(e,r){"use strict";q();var{htmlTagNames:u}=Pl(),n=Ts(),D=n(u);r.exports=D}}),Ll=I({"vendors/html-element-attributes.json"(e,r){r.exports={htmlElementAttributes:{"*":["accesskey","autocapitalize","autofocus","class","contenteditable","dir","draggable","enterkeyhint","hidden","id","inputmode","is","itemid","itemprop","itemref","itemscope","itemtype","lang","nonce","slot","spellcheck","style","tabindex","title","translate"],a:["charset","coords","download","href","hreflang","name","ping","referrerpolicy","rel","rev","shape","target","type"],applet:["align","alt","archive","code","codebase","height","hspace","name","object","vspace","width"],area:["alt","coords","download","href","hreflang","nohref","ping","referrerpolicy","rel","shape","target","type"],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],base:["href","target"],basefont:["color","face","size"],blockquote:["cite"],body:["alink","background","bgcolor","link","text","vlink"],br:["clear"],button:["disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","name","type","value"],canvas:["height","width"],caption:["align"],col:["align","char","charoff","span","valign","width"],colgroup:["align","char","charoff","span","valign","width"],data:["value"],del:["cite","datetime"],details:["open"],dialog:["open"],dir:["compact"],div:["align"],dl:["compact"],embed:["height","src","type","width"],fieldset:["disabled","form","name"],font:["color","face","size"],form:["accept","accept-charset","action","autocomplete","enctype","method","name","novalidate","target"],frame:["frameborder","longdesc","marginheight","marginwidth","name","noresize","scrolling","src"],frameset:["cols","rows"],h1:["align"],h2:["align"],h3:["align"],h4:["align"],h5:["align"],h6:["align"],head:["profile"],hr:["align","noshade","size","width"],html:["manifest","version"],iframe:["align","allow","allowfullscreen","allowpaymentrequest","allowusermedia","frameborder","height","loading","longdesc","marginheight","marginwidth","name","referrerpolicy","sandbox","scrolling","src","srcdoc","width"],img:["align","alt","border","crossorigin","decoding","height","hspace","ismap","loading","longdesc","name","referrerpolicy","sizes","src","srcset","usemap","vspace","width"],input:["accept","align","alt","autocomplete","checked","dirname","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","height","ismap","list","max","maxlength","min","minlength","multiple","name","pattern","placeholder","readonly","required","size","src","step","type","usemap","value","width"],ins:["cite","datetime"],isindex:["prompt"],label:["for","form"],legend:["align"],li:["type","value"],link:["as","charset","color","crossorigin","disabled","href","hreflang","imagesizes","imagesrcset","integrity","media","referrerpolicy","rel","rev","sizes","target","type"],map:["name"],menu:["compact"],meta:["charset","content","http-equiv","media","name","scheme"],meter:["high","low","max","min","optimum","value"],object:["align","archive","border","classid","codebase","codetype","data","declare","form","height","hspace","name","standby","type","typemustmatch","usemap","vspace","width"],ol:["compact","reversed","start","type"],optgroup:["disabled","label"],option:["disabled","label","selected","value"],output:["for","form","name"],p:["align"],param:["name","type","value","valuetype"],pre:["width"],progress:["max","value"],q:["cite"],script:["async","charset","crossorigin","defer","integrity","language","nomodule","referrerpolicy","src","type"],select:["autocomplete","disabled","form","multiple","name","required","size"],slot:["name"],source:["height","media","sizes","src","srcset","type","width"],style:["media","type"],table:["align","bgcolor","border","cellpadding","cellspacing","frame","rules","summary","width"],tbody:["align","char","charoff","valign"],td:["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],textarea:["autocomplete","cols","dirname","disabled","form","maxlength","minlength","name","placeholder","readonly","required","rows","wrap"],tfoot:["align","char","charoff","valign"],th:["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],thead:["align","char","charoff","valign"],time:["datetime"],tr:["align","bgcolor","char","charoff","valign"],track:["default","kind","label","src","srclang"],ul:["compact","type"],video:["autoplay","controls","crossorigin","height","loop","muted","playsinline","poster","preload","src","width"]}}}}),$l=I({"src/language-html/utils/map-object.js"(e,r){"use strict";q();function u(n,D){let s=Object.create(null);for(let[i,f]of Object.entries(n))s[i]=D(f,i);return s}r.exports=u}}),Ml=I({"src/language-html/utils/html-elements-attributes.js"(e,r){"use strict";q();var{htmlElementAttributes:u}=Ll(),n=$l(),D=Ts(),s=n(u,D);r.exports=s}}),jl=I({"src/language-html/utils/is-unknown-namespace.js"(e,r){"use strict";q();function u(n){return n.type==="element"&&!n.hasExplicitNamespace&&!["html","svg"].includes(n.namespace)}r.exports=u}}),Ul=I({"src/language-html/pragma.js"(e,r){"use strict";q();function u(D){return/^\s*<!--\s*@(?:format|prettier)\s*-->/.test(D)}function n(D){return`<!-- @format -->

`+D.replace(/^\s*\n/,"")}r.exports={hasPragma:u,insertPragma:n}}}),Gl=I({"src/language-html/ast.js"(e,r){"use strict";q();var u={attrs:!0,children:!0},n=new Set(["parent"]),D=class{constructor(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};for(let f of new Set([...n,...Object.keys(i)]))this.setProperty(f,i[f])}setProperty(i,f){if(this[i]!==f){if(i in u&&(f=f.map(c=>this.createChild(c))),!n.has(i)){this[i]=f;return}Object.defineProperty(this,i,{value:f,enumerable:!1,configurable:!0})}}map(i){let f;for(let c in u){let F=this[c];if(F){let a=s(F,l=>l.map(i));f!==F&&(f||(f=new D({parent:this.parent})),f.setProperty(c,a))}}if(f)for(let c in this)c in u||(f[c]=this[c]);return i(f||this)}walk(i){for(let f in u){let c=this[f];if(c)for(let F=0;F<c.length;F++)c[F].walk(i)}i(this)}createChild(i){let f=i instanceof D?i.clone():new D(i);return f.setProperty("parent",this),f}insertChildBefore(i,f){this.children.splice(this.children.indexOf(i),0,this.createChild(f))}removeChild(i){this.children.splice(this.children.indexOf(i),1)}replaceChild(i,f){this.children[this.children.indexOf(i)]=this.createChild(f)}clone(){return new D(this)}get firstChild(){var i;return(i=this.children)===null||i===void 0?void 0:i[0]}get lastChild(){var i;return(i=this.children)===null||i===void 0?void 0:i[this.children.length-1]}get prev(){var i;return(i=this.parent)===null||i===void 0?void 0:i.children[this.parent.children.indexOf(this)-1]}get next(){var i;return(i=this.parent)===null||i===void 0?void 0:i.children[this.parent.children.indexOf(this)+1]}get rawName(){return this.hasExplicitNamespace?this.fullName:this.name}get fullName(){return this.namespace?this.namespace+":"+this.name:this.name}get attrMap(){return Object.fromEntries(this.attrs.map(i=>[i.fullName,i.value]))}};function s(i,f){let c=i.map(f);return c.some((F,a)=>F!==i[a])?c:i}r.exports={Node:D}}}),Vl=I({"src/language-html/conditional-comment.js"(e,r){"use strict";q();var{ParseSourceSpan:u}=Be(),n=[{regex:/^(\[if([^\]]*)]>)(.*?)<!\s*\[endif]$/s,parse:s},{regex:/^\[if([^\]]*)]><!$/,parse:i},{regex:/^<!\s*\[endif]$/,parse:f}];function D(c,F){if(c.value)for(let{regex:a,parse:l}of n){let h=c.value.match(a);if(h)return l(c,F,h)}return null}function s(c,F,a){let[,l,h,C]=a,d=4+l.length,m=c.sourceSpan.start.moveBy(d),T=m.moveBy(C.length),[w,g]=(()=>{try{return[!0,F(C,m).children]}catch{return[!1,[{type:"text",value:C,sourceSpan:new u(m,T)}]]}})();return{type:"ieConditionalComment",complete:w,children:g,condition:h.trim().replace(/\s+/g," "),sourceSpan:c.sourceSpan,startSourceSpan:new u(c.sourceSpan.start,m),endSourceSpan:new u(T,c.sourceSpan.end)}}function i(c,F,a){let[,l]=a;return{type:"ieConditionalStartComment",condition:l.trim().replace(/\s+/g," "),sourceSpan:c.sourceSpan}}function f(c){return{type:"ieConditionalEndComment",sourceSpan:c.sourceSpan}}r.exports={parseIeConditionalComment:D}}}),Xl=I({"src/language-html/loc.js"(e,r){"use strict";q();function u(D){return D.sourceSpan.start.offset}function n(D){return D.sourceSpan.end.offset}r.exports={locStart:u,locEnd:n}}}),Ze=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/tags.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r;(function(c){c[c.RAW_TEXT=0]="RAW_TEXT",c[c.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",c[c.PARSABLE_DATA=2]="PARSABLE_DATA"})(r=e.TagContentType||(e.TagContentType={}));function u(c){if(c[0]!=":")return[null,c];let F=c.indexOf(":",1);if(F==-1)throw new Error(`Unsupported format "${c}" expecting ":namespace:name"`);return[c.slice(1,F),c.slice(F+1)]}e.splitNsName=u;function n(c){return u(c)[1]==="ng-container"}e.isNgContainer=n;function D(c){return u(c)[1]==="ng-content"}e.isNgContent=D;function s(c){return u(c)[1]==="ng-template"}e.isNgTemplate=s;function i(c){return c===null?null:u(c)[0]}e.getNsPrefix=i;function f(c,F){return c?`:${c}:${F}`:F}e.mergeNsAndName=f,e.NAMED_ENTITIES={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",AMP:"&",amp:"&",And:"\u2A53",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",ap:"\u2248",apacir:"\u2A6F",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",Barwed:"\u2306",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",Because:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxDL:"\u2557",boxDl:"\u2556",boxdL:"\u2555",boxdl:"\u2510",boxDR:"\u2554",boxDr:"\u2553",boxdR:"\u2552",boxdr:"\u250C",boxH:"\u2550",boxh:"\u2500",boxHD:"\u2566",boxHd:"\u2564",boxhD:"\u2565",boxhd:"\u252C",boxHU:"\u2569",boxHu:"\u2567",boxhU:"\u2568",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxUL:"\u255D",boxUl:"\u255C",boxuL:"\u255B",boxul:"\u2518",boxUR:"\u255A",boxUr:"\u2559",boxuR:"\u2558",boxur:"\u2514",boxV:"\u2551",boxv:"\u2502",boxVH:"\u256C",boxVh:"\u256B",boxvH:"\u256A",boxvh:"\u253C",boxVL:"\u2563",boxVl:"\u2562",boxvL:"\u2561",boxvl:"\u2524",boxVR:"\u2560",boxVr:"\u255F",boxvR:"\u255E",boxvr:"\u251C",bprime:"\u2035",Breve:"\u02D8",breve:"\u02D8",brvbar:"\xA6",Bscr:"\u212C",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",Cap:"\u22D2",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",CenterDot:"\xB7",centerdot:"\xB7",Cfr:"\u212D",cfr:"\u{1D520}",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",cir:"\u25CB",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",Colon:"\u2237",colon:":",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",Conint:"\u222F",conint:"\u222E",ContourIntegral:"\u222E",Copf:"\u2102",copf:"\u{1D554}",coprod:"\u2210",Coproduct:"\u2210",COPY:"\xA9",copy:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",Cross:"\u2A2F",cross:"\u2717",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",Cup:"\u22D3",cup:"\u222A",cupbrcap:"\u2A48",CupCap:"\u224D",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",Dagger:"\u2021",dagger:"\u2020",daleth:"\u2138",Darr:"\u21A1",dArr:"\u21D3",darr:"\u2193",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",DD:"\u2145",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",Diamond:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",Downarrow:"\u21D3",downarrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",ecir:"\u2256",Ecirc:"\xCA",ecirc:"\xEA",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",eDot:"\u2251",edot:"\u0117",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp:"\u2003",emsp13:"\u2004",emsp14:"\u2005",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",Escr:"\u2130",escr:"\u212F",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",ExponentialE:"\u2147",exponentiale:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",ForAll:"\u2200",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",Fscr:"\u2131",fscr:"\u{1D4BB}",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",gE:"\u2267",ge:"\u2265",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",Gg:"\u22D9",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gl:"\u2277",gla:"\u2AA5",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gnE:"\u2269",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",GT:">",Gt:"\u226B",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",hArr:"\u21D4",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",Hfr:"\u210C",hfr:"\u{1D525}",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",Hopf:"\u210D",hopf:"\u{1D559}",horbar:"\u2015",HorizontalLine:"\u2500",Hscr:"\u210B",hscr:"\u{1D4BD}",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",Ifr:"\u2111",ifr:"\u{1D526}",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Im:"\u2111",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",Int:"\u222C",int:"\u222B",intcal:"\u22BA",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",Iscr:"\u2110",iscr:"\u{1D4BE}",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",Lang:"\u27EA",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",Larr:"\u219E",lArr:"\u21D0",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",lAtail:"\u291B",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lBarr:"\u290E",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",lE:"\u2266",le:"\u2264",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",Leftarrow:"\u21D0",leftarrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",Ll:"\u22D8",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lnE:"\u2268",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftarrow:"\u27F5",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",Lscr:"\u2112",lscr:"\u{1D4C1}",Lsh:"\u21B0",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",LT:"<",Lt:"\u226A",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",Mscr:"\u2133",mscr:"\u{1D4C2}",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",nearhk:"\u2924",neArr:"\u21D7",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlArr:"\u21CD",nlarr:"\u219A",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nLeftarrow:"\u21CD",nleftarrow:"\u219A",nLeftrightarrow:"\u21CE",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",nopf:"\u{1D55F}",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nRightarrow:"\u21CF",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nVDash:"\u22AF",nVdash:"\u22AE",nvDash:"\u22AD",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwArr:"\u21D6",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",ocir:"\u229A",Ocirc:"\xD4",ocirc:"\xF4",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",Or:"\u2A54",or:"\u2228",orarr:"\u21BB",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",Otimes:"\u2A37",otimes:"\u2297",otimesas:"\u2A36",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",par:"\u2225",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",Popf:"\u2119",popf:"\u{1D561}",pound:"\xA3",Pr:"\u2ABB",pr:"\u227A",prap:"\u2AB7",prcue:"\u227C",prE:"\u2AB3",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",Prime:"\u2033",prime:"\u2032",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportion:"\u2237",Proportional:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",Qopf:"\u211A",qopf:"\u{1D562}",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",QUOT:'"',quot:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",Rang:"\u27EB",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",Rarr:"\u21A0",rArr:"\u21D2",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",rAtail:"\u291C",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",RBarr:"\u2910",rBarr:"\u290F",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",Re:"\u211C",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",REG:"\xAE",reg:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",Rfr:"\u211C",rfr:"\u{1D52F}",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",Rightarrow:"\u21D2",rightarrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",Ropf:"\u211D",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",Rscr:"\u211B",rscr:"\u{1D4C7}",Rsh:"\u21B1",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",Sc:"\u2ABC",sc:"\u227B",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",sccue:"\u227D",scE:"\u2AB4",sce:"\u2AB0",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",searhk:"\u2925",seArr:"\u21D8",searr:"\u2198",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",Square:"\u25A1",square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",Sub:"\u22D0",sub:"\u2282",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",Subset:"\u22D0",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",Sum:"\u2211",sum:"\u2211",sung:"\u266A",Sup:"\u22D1",sup:"\u2283",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",Supset:"\u22D1",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swArr:"\u21D9",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",Therefore:"\u2234",therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",thinsp:"\u2009",ThinSpace:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",Tilde:"\u223C",tilde:"\u02DC",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",TRADE:"\u2122",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",Uarr:"\u219F",uArr:"\u21D1",uarr:"\u2191",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrow:"\u2191",Uparrow:"\u21D1",uparrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",Updownarrow:"\u21D5",updownarrow:"\u2195",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",upsi:"\u03C5",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTee:"\u22A5",UpTeeArrow:"\u21A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",vArr:"\u21D5",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",Vbar:"\u2AEB",vBar:"\u2AE8",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",VDash:"\u22AB",Vdash:"\u22A9",vDash:"\u22A8",vdash:"\u22A2",Vdashl:"\u2AE6",Vee:"\u22C1",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",Verbar:"\u2016",verbar:"|",Vert:"\u2016",vert:"|",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",Wedge:"\u22C0",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",Xi:"\u039E",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",Yuml:"\u0178",yuml:"\xFF",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",Zfr:"\u2128",zfr:"\u{1D537}",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",Zopf:"\u2124",zopf:"\u{1D56B}",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"},e.NGSP_UNICODE="\uE500",e.NAMED_ENTITIES.ngsp=e.NGSP_UNICODE}}),Bs=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/html_tags.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Ze(),u=class{constructor(){let{closedByChildren:i,implicitNamespacePrefix:f,contentType:c=r.TagContentType.PARSABLE_DATA,closedByParent:F=!1,isVoid:a=!1,ignoreFirstLf:l=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.closedByChildren={},this.closedByParent=!1,this.canSelfClose=!1,i&&i.length>0&&i.forEach(h=>this.closedByChildren[h]=!0),this.isVoid=a,this.closedByParent=F||a,this.implicitNamespacePrefix=f||null,this.contentType=c,this.ignoreFirstLf=l}isClosedByChild(i){return this.isVoid||i.toLowerCase()in this.closedByChildren}};e.HtmlTagDefinition=u;var n,D;function s(i){return D||(n=new u,D={base:new u({isVoid:!0}),meta:new u({isVoid:!0}),area:new u({isVoid:!0}),embed:new u({isVoid:!0}),link:new u({isVoid:!0}),img:new u({isVoid:!0}),input:new u({isVoid:!0}),param:new u({isVoid:!0}),hr:new u({isVoid:!0}),br:new u({isVoid:!0}),source:new u({isVoid:!0}),track:new u({isVoid:!0}),wbr:new u({isVoid:!0}),p:new u({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new u({closedByChildren:["tbody","tfoot"]}),tbody:new u({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new u({closedByChildren:["tbody"],closedByParent:!0}),tr:new u({closedByChildren:["tr"],closedByParent:!0}),td:new u({closedByChildren:["td","th"],closedByParent:!0}),th:new u({closedByChildren:["td","th"],closedByParent:!0}),col:new u({isVoid:!0}),svg:new u({implicitNamespacePrefix:"svg"}),math:new u({implicitNamespacePrefix:"math"}),li:new u({closedByChildren:["li"],closedByParent:!0}),dt:new u({closedByChildren:["dt","dd"]}),dd:new u({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new u({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new u({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new u({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new u({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new u({closedByChildren:["optgroup"],closedByParent:!0}),option:new u({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new u({ignoreFirstLf:!0}),listing:new u({ignoreFirstLf:!0}),style:new u({contentType:r.TagContentType.RAW_TEXT}),script:new u({contentType:r.TagContentType.RAW_TEXT}),title:new u({contentType:r.TagContentType.ESCAPABLE_RAW_TEXT}),textarea:new u({contentType:r.TagContentType.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})}),D[i]||n}e.getHtmlTagDefinition=s}}),Hl=I({"node_modules/angular-html-parser/lib/compiler/src/ast_path.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=class{constructor(u){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:-1;this.path=u,this.position=n}get empty(){return!this.path||!this.path.length}get head(){return this.path[0]}get tail(){return this.path[this.path.length-1]}parentOf(u){return u&&this.path[this.path.indexOf(u)-1]}childOf(u){return this.path[this.path.indexOf(u)+1]}first(u){for(let n=this.path.length-1;n>=0;n--){let D=this.path[n];if(D instanceof u)return D}}push(u){this.path.push(u)}pop(){return this.path.pop()}};e.AstPath=r}}),bs=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/ast.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Hl(),u=class{constructor(d,m,T){this.value=d,this.sourceSpan=m,this.i18n=T,this.type="text"}visit(d,m){return d.visitText(this,m)}};e.Text=u;var n=class{constructor(d,m){this.value=d,this.sourceSpan=m,this.type="cdata"}visit(d,m){return d.visitCdata(this,m)}};e.CDATA=n;var D=class{constructor(d,m,T,w,g,N){this.switchValue=d,this.type=m,this.cases=T,this.sourceSpan=w,this.switchValueSourceSpan=g,this.i18n=N}visit(d,m){return d.visitExpansion(this,m)}};e.Expansion=D;var s=class{constructor(d,m,T,w,g){this.value=d,this.expression=m,this.sourceSpan=T,this.valueSourceSpan=w,this.expSourceSpan=g}visit(d,m){return d.visitExpansionCase(this,m)}};e.ExpansionCase=s;var i=class{constructor(d,m,T){let w=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,g=arguments.length>4&&arguments[4]!==void 0?arguments[4]:null,N=arguments.length>5&&arguments[5]!==void 0?arguments[5]:null;this.name=d,this.value=m,this.sourceSpan=T,this.valueSpan=w,this.nameSpan=g,this.i18n=N,this.type="attribute"}visit(d,m){return d.visitAttribute(this,m)}};e.Attribute=i;var f=class{constructor(d,m,T,w){let g=arguments.length>4&&arguments[4]!==void 0?arguments[4]:null,N=arguments.length>5&&arguments[5]!==void 0?arguments[5]:null,R=arguments.length>6&&arguments[6]!==void 0?arguments[6]:null,j=arguments.length>7&&arguments[7]!==void 0?arguments[7]:null;this.name=d,this.attrs=m,this.children=T,this.sourceSpan=w,this.startSourceSpan=g,this.endSourceSpan=N,this.nameSpan=R,this.i18n=j,this.type="element"}visit(d,m){return d.visitElement(this,m)}};e.Element=f;var c=class{constructor(d,m){this.value=d,this.sourceSpan=m,this.type="comment"}visit(d,m){return d.visitComment(this,m)}};e.Comment=c;var F=class{constructor(d,m){this.value=d,this.sourceSpan=m,this.type="docType"}visit(d,m){return d.visitDocType(this,m)}};e.DocType=F;function a(d,m){let T=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,w=[],g=d.visit?N=>d.visit(N,T)||N.visit(d,T):N=>N.visit(d,T);return m.forEach(N=>{let R=g(N);R&&w.push(R)}),w}e.visitAll=a;var l=class{constructor(){}visitElement(d,m){this.visitChildren(m,T=>{T(d.attrs),T(d.children)})}visitAttribute(d,m){}visitText(d,m){}visitCdata(d,m){}visitComment(d,m){}visitDocType(d,m){}visitExpansion(d,m){return this.visitChildren(m,T=>{T(d.cases)})}visitExpansionCase(d,m){}visitChildren(d,m){let T=[],w=this;function g(N){N&&T.push(a(w,N,d))}return m(g),Array.prototype.concat.apply([],T)}};e.RecursiveVisitor=l;function h(d){let m=d.sourceSpan.start.offset,T=d.sourceSpan.end.offset;return d instanceof f&&(d.endSourceSpan?T=d.endSourceSpan.end.offset:d.children&&d.children.length&&(T=h(d.children[d.children.length-1]).end)),{start:m,end:T}}function C(d,m){let T=[],w=new class extends l{visit(g,N){let R=h(g);if(R.start<=m&&m<R.end)T.push(g);else return!0}};return a(w,d),new r.AstPath(T,m)}e.findNode=C}}),zl=I({"node_modules/angular-html-parser/lib/compiler/src/assertions.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});function r(D,s){if(s!=null){if(!Array.isArray(s))throw new Error(`Expected '${D}' to be an array of strings.`);for(let i=0;i<s.length;i+=1)if(typeof s[i]!="string")throw new Error(`Expected '${D}' to be an array of strings.`)}}e.assertArrayOfStrings=r;var u=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];function n(D,s){if(s!=null&&!(Array.isArray(s)&&s.length==2))throw new Error(`Expected '${D}' to be an array, [start, end].`);if(s!=null){let i=s[0],f=s[1];u.forEach(c=>{if(c.test(i)||c.test(f))throw new Error(`['${i}', '${f}'] contains unusable interpolation symbol.`)})}}e.assertInterpolationSymbols=n}}),Wl=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/interpolation_config.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=zl(),u=class{constructor(n,D){this.start=n,this.end=D}static fromArray(n){return n?(r.assertInterpolationSymbols("interpolation",n),new u(n[0],n[1])):e.DEFAULT_INTERPOLATION_CONFIG}};e.InterpolationConfig=u,e.DEFAULT_INTERPOLATION_CONFIG=new u("{{","}}")}}),Yl=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/lexer.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Es(),u=Be(),n=Wl(),D=Ze(),s;(function(t){t[t.TAG_OPEN_START=0]="TAG_OPEN_START",t[t.TAG_OPEN_END=1]="TAG_OPEN_END",t[t.TAG_OPEN_END_VOID=2]="TAG_OPEN_END_VOID",t[t.TAG_CLOSE=3]="TAG_CLOSE",t[t.TEXT=4]="TEXT",t[t.ESCAPABLE_RAW_TEXT=5]="ESCAPABLE_RAW_TEXT",t[t.RAW_TEXT=6]="RAW_TEXT",t[t.COMMENT_START=7]="COMMENT_START",t[t.COMMENT_END=8]="COMMENT_END",t[t.CDATA_START=9]="CDATA_START",t[t.CDATA_END=10]="CDATA_END",t[t.ATTR_NAME=11]="ATTR_NAME",t[t.ATTR_QUOTE=12]="ATTR_QUOTE",t[t.ATTR_VALUE=13]="ATTR_VALUE",t[t.DOC_TYPE_START=14]="DOC_TYPE_START",t[t.DOC_TYPE_END=15]="DOC_TYPE_END",t[t.EXPANSION_FORM_START=16]="EXPANSION_FORM_START",t[t.EXPANSION_CASE_VALUE=17]="EXPANSION_CASE_VALUE",t[t.EXPANSION_CASE_EXP_START=18]="EXPANSION_CASE_EXP_START",t[t.EXPANSION_CASE_EXP_END=19]="EXPANSION_CASE_EXP_END",t[t.EXPANSION_FORM_END=20]="EXPANSION_FORM_END",t[t.EOF=21]="EOF"})(s=e.TokenType||(e.TokenType={}));var i=class{constructor(t,o,E){this.type=t,this.parts=o,this.sourceSpan=E}};e.Token=i;var f=class extends u.ParseError{constructor(t,o,E){super(E,t),this.tokenType=o}};e.TokenError=f;var c=class{constructor(t,o){this.tokens=t,this.errors=o}};e.TokenizeResult=c;function F(t,o,E){let p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return new d(new u.ParseSourceFile(t,o),E,p).tokenize()}e.tokenize=F;var a=/\r\n?/g;function l(t){return`Unexpected character "${t===r.$EOF?"EOF":String.fromCharCode(t)}"`}function h(t){return`Unknown entity "${t}" - use the "&#<decimal>;" or  "&#x<hex>;" syntax`}var C=class{constructor(t){this.error=t}},d=class{constructor(t,o,E){this._getTagContentType=o,this._currentTokenStart=null,this._currentTokenType=null,this._expansionCaseStack=[],this._inInterpolation=!1,this._fullNameStack=[],this.tokens=[],this.errors=[],this._tokenizeIcu=E.tokenizeExpansionForms||!1,this._interpolationConfig=E.interpolationConfig||n.DEFAULT_INTERPOLATION_CONFIG,this._leadingTriviaCodePoints=E.leadingTriviaChars&&E.leadingTriviaChars.map(A=>A.codePointAt(0)||0),this._canSelfClose=E.canSelfClose||!1,this._allowHtmComponentClosingTags=E.allowHtmComponentClosingTags||!1;let p=E.range||{endPos:t.content.length,startPos:0,startLine:0,startCol:0};this._cursor=E.escapedString?new k(t,p):new x(t,p);try{this._cursor.init()}catch(A){this.handleError(A)}}_processCarriageReturns(t){return t.replace(a,`
`)}tokenize(){for(;this._cursor.peek()!==r.$EOF;){let t=this._cursor.clone();try{if(this._attemptCharCode(r.$LT))if(this._attemptCharCode(r.$BANG))this._attemptStr("[CDATA[")?this._consumeCdata(t):this._attemptStr("--")?this._consumeComment(t):this._attemptStrCaseInsensitive("doctype")?this._consumeDocType(t):this._consumeBogusComment(t);else if(this._attemptCharCode(r.$SLASH))this._consumeTagClose(t);else{let o=this._cursor.clone();this._attemptCharCode(r.$QUESTION)?(this._cursor=o,this._consumeBogusComment(t)):this._consumeTagOpen(t)}else this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeText()}catch(o){this.handleError(o)}}return this._beginToken(s.EOF),this._endToken([]),new c(O(this.tokens),this.errors)}_tokenizeExpansionForm(){if(this.isExpansionFormStart())return this._consumeExpansionFormStart(),!0;if(R(this._cursor.peek())&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;if(this._cursor.peek()===r.$RBRACE){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1}_beginToken(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._cursor.clone();this._currentTokenStart=o,this._currentTokenType=t}_endToken(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._cursor.clone();if(this._currentTokenStart===null)throw new f("Programming error - attempted to end a token when there was no start to the token",this._currentTokenType,this._cursor.getSpan(o));if(this._currentTokenType===null)throw new f("Programming error - attempted to end a token which has no token type",null,this._cursor.getSpan(this._currentTokenStart));let E=new i(this._currentTokenType,t,this._cursor.getSpan(this._currentTokenStart,this._leadingTriviaCodePoints));return this.tokens.push(E),this._currentTokenStart=null,this._currentTokenType=null,E}_createError(t,o){this._isInExpansionForm()&&(t+=` (Do you have an unescaped "{" in your template? Use "{{ '{' }}") to escape it.)`);let E=new f(t,this._currentTokenType,o);return this._currentTokenStart=null,this._currentTokenType=null,new C(E)}handleError(t){if(t instanceof $&&(t=this._createError(t.msg,this._cursor.getSpan(t.cursor))),t instanceof C)this.errors.push(t.error);else throw t}_attemptCharCode(t){return this._cursor.peek()===t?(this._cursor.advance(),!0):!1}_attemptCharCodeCaseInsensitive(t){return j(this._cursor.peek(),t)?(this._cursor.advance(),!0):!1}_requireCharCode(t){let o=this._cursor.clone();if(!this._attemptCharCode(t))throw this._createError(l(this._cursor.peek()),this._cursor.getSpan(o))}_attemptStr(t){let o=t.length;if(this._cursor.charsLeft()<o)return!1;let E=this._cursor.clone();for(let p=0;p<o;p++)if(!this._attemptCharCode(t.charCodeAt(p)))return this._cursor=E,!1;return!0}_attemptStrCaseInsensitive(t){for(let o=0;o<t.length;o++)if(!this._attemptCharCodeCaseInsensitive(t.charCodeAt(o)))return!1;return!0}_requireStr(t){let o=this._cursor.clone();if(!this._attemptStr(t))throw this._createError(l(this._cursor.peek()),this._cursor.getSpan(o))}_requireStrCaseInsensitive(t){let o=this._cursor.clone();if(!this._attemptStrCaseInsensitive(t))throw this._createError(l(this._cursor.peek()),this._cursor.getSpan(o))}_attemptCharCodeUntilFn(t){for(;!t(this._cursor.peek());)this._cursor.advance()}_requireCharCodeUntilFn(t,o){let E=this._cursor.clone();if(this._attemptCharCodeUntilFn(t),this._cursor.clone().diff(E)<o)throw this._createError(l(this._cursor.peek()),this._cursor.getSpan(E))}_attemptUntilChar(t){for(;this._cursor.peek()!==t;)this._cursor.advance()}_readChar(t){if(t&&this._cursor.peek()===r.$AMPERSAND)return this._decodeEntity();{let o=String.fromCodePoint(this._cursor.peek());return this._cursor.advance(),o}}_decodeEntity(){let t=this._cursor.clone();if(this._cursor.advance(),this._attemptCharCode(r.$HASH)){let o=this._attemptCharCode(r.$x)||this._attemptCharCode(r.$X),E=this._cursor.clone();if(this._attemptCharCodeUntilFn(g),this._cursor.peek()!=r.$SEMICOLON)throw this._createError(l(this._cursor.peek()),this._cursor.getSpan());let p=this._cursor.getChars(E);this._cursor.advance();try{let A=parseInt(p,o?16:10);return String.fromCharCode(A)}catch{throw this._createError(h(this._cursor.getChars(t)),this._cursor.getSpan())}}else{let o=this._cursor.clone();if(this._attemptCharCodeUntilFn(N),this._cursor.peek()!=r.$SEMICOLON)return this._cursor=o,"&";let E=this._cursor.getChars(o);this._cursor.advance();let p=D.NAMED_ENTITIES[E];if(!p)throw this._createError(h(E),this._cursor.getSpan(t));return p}}_consumeRawText(t,o){this._beginToken(t?s.ESCAPABLE_RAW_TEXT:s.RAW_TEXT);let E=[];for(;;){let p=this._cursor.clone(),A=o();if(this._cursor=p,A)break;E.push(this._readChar(t))}return this._endToken([this._processCarriageReturns(E.join(""))])}_consumeComment(t){this._beginToken(s.COMMENT_START,t),this._endToken([]),this._consumeRawText(!1,()=>this._attemptStr("-->")),this._beginToken(s.COMMENT_END),this._requireStr("-->"),this._endToken([])}_consumeBogusComment(t){this._beginToken(s.COMMENT_START,t),this._endToken([]),this._consumeRawText(!1,()=>this._cursor.peek()===r.$GT),this._beginToken(s.COMMENT_END),this._cursor.advance(),this._endToken([])}_consumeCdata(t){this._beginToken(s.CDATA_START,t),this._endToken([]),this._consumeRawText(!1,()=>this._attemptStr("]]>")),this._beginToken(s.CDATA_END),this._requireStr("]]>"),this._endToken([])}_consumeDocType(t){this._beginToken(s.DOC_TYPE_START,t),this._endToken([]),this._consumeRawText(!1,()=>this._cursor.peek()===r.$GT),this._beginToken(s.DOC_TYPE_END),this._cursor.advance(),this._endToken([])}_consumePrefixAndName(){let t=this._cursor.clone(),o="";for(;this._cursor.peek()!==r.$COLON&&!w(this._cursor.peek());)this._cursor.advance();let E;this._cursor.peek()===r.$COLON?(o=this._cursor.getChars(t),this._cursor.advance(),E=this._cursor.clone()):E=t,this._requireCharCodeUntilFn(T,o===""?0:1);let p=this._cursor.getChars(E);return[o,p]}_consumeTagOpen(t){let o,E,p,A=this.tokens.length,P=this._cursor.clone(),M=[];try{if(!r.isAsciiLetter(this._cursor.peek()))throw this._createError(l(this._cursor.peek()),this._cursor.getSpan(t));for(p=this._consumeTagOpenStart(t),E=p.parts[0],o=p.parts[1],this._attemptCharCodeUntilFn(m);this._cursor.peek()!==r.$SLASH&&this._cursor.peek()!==r.$GT;){let[V,X]=this._consumeAttributeName();if(this._attemptCharCodeUntilFn(m),this._attemptCharCode(r.$EQ)){this._attemptCharCodeUntilFn(m);let H=this._consumeAttributeValue();M.push({prefix:V,name:X,value:H})}else M.push({prefix:V,name:X});this._attemptCharCodeUntilFn(m)}this._consumeTagOpenEnd()}catch(V){if(V instanceof C){this._cursor=P,p&&(this.tokens.length=A),this._beginToken(s.TEXT,t),this._endToken(["<"]);return}throw V}if(this._canSelfClose&&this.tokens[this.tokens.length-1].type===s.TAG_OPEN_END_VOID)return;let z=this._getTagContentType(o,E,this._fullNameStack.length>0,M);this._handleFullNameStackForTagOpen(E,o),z===D.TagContentType.RAW_TEXT?this._consumeRawTextWithTagClose(E,o,!1):z===D.TagContentType.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(E,o,!0)}_consumeRawTextWithTagClose(t,o,E){let p=this._consumeRawText(E,()=>!this._attemptCharCode(r.$LT)||!this._attemptCharCode(r.$SLASH)||(this._attemptCharCodeUntilFn(m),!this._attemptStrCaseInsensitive(t?`${t}:${o}`:o))?!1:(this._attemptCharCodeUntilFn(m),this._attemptCharCode(r.$GT)));this._beginToken(s.TAG_CLOSE),this._requireCharCodeUntilFn(A=>A===r.$GT,3),this._cursor.advance(),this._endToken([t,o]),this._handleFullNameStackForTagClose(t,o)}_consumeTagOpenStart(t){this._beginToken(s.TAG_OPEN_START,t);let o=this._consumePrefixAndName();return this._endToken(o)}_consumeAttributeName(){let t=this._cursor.peek();if(t===r.$SQ||t===r.$DQ)throw this._createError(l(t),this._cursor.getSpan());this._beginToken(s.ATTR_NAME);let o=this._consumePrefixAndName();return this._endToken(o),o}_consumeAttributeValue(){let t;if(this._cursor.peek()===r.$SQ||this._cursor.peek()===r.$DQ){this._beginToken(s.ATTR_QUOTE);let o=this._cursor.peek();this._cursor.advance(),this._endToken([String.fromCodePoint(o)]),this._beginToken(s.ATTR_VALUE);let E=[];for(;this._cursor.peek()!==o;)E.push(this._readChar(!0));t=this._processCarriageReturns(E.join("")),this._endToken([t]),this._beginToken(s.ATTR_QUOTE),this._cursor.advance(),this._endToken([String.fromCodePoint(o)])}else{this._beginToken(s.ATTR_VALUE);let o=this._cursor.clone();this._requireCharCodeUntilFn(T,1),t=this._processCarriageReturns(this._cursor.getChars(o)),this._endToken([t])}return t}_consumeTagOpenEnd(){let t=this._attemptCharCode(r.$SLASH)?s.TAG_OPEN_END_VOID:s.TAG_OPEN_END;this._beginToken(t),this._requireCharCode(r.$GT),this._endToken([])}_consumeTagClose(t){if(this._beginToken(s.TAG_CLOSE,t),this._attemptCharCodeUntilFn(m),this._allowHtmComponentClosingTags&&this._attemptCharCode(r.$SLASH))this._attemptCharCodeUntilFn(m),this._requireCharCode(r.$GT),this._endToken([]);else{let[o,E]=this._consumePrefixAndName();this._attemptCharCodeUntilFn(m),this._requireCharCode(r.$GT),this._endToken([o,E]),this._handleFullNameStackForTagClose(o,E)}}_consumeExpansionFormStart(){this._beginToken(s.EXPANSION_FORM_START),this._requireCharCode(r.$LBRACE),this._endToken([]),this._expansionCaseStack.push(s.EXPANSION_FORM_START),this._beginToken(s.RAW_TEXT);let t=this._readUntil(r.$COMMA);this._endToken([t]),this._requireCharCode(r.$COMMA),this._attemptCharCodeUntilFn(m),this._beginToken(s.RAW_TEXT);let o=this._readUntil(r.$COMMA);this._endToken([o]),this._requireCharCode(r.$COMMA),this._attemptCharCodeUntilFn(m)}_consumeExpansionCaseStart(){this._beginToken(s.EXPANSION_CASE_VALUE);let t=this._readUntil(r.$LBRACE).trim();this._endToken([t]),this._attemptCharCodeUntilFn(m),this._beginToken(s.EXPANSION_CASE_EXP_START),this._requireCharCode(r.$LBRACE),this._endToken([]),this._attemptCharCodeUntilFn(m),this._expansionCaseStack.push(s.EXPANSION_CASE_EXP_START)}_consumeExpansionCaseEnd(){this._beginToken(s.EXPANSION_CASE_EXP_END),this._requireCharCode(r.$RBRACE),this._endToken([]),this._attemptCharCodeUntilFn(m),this._expansionCaseStack.pop()}_consumeExpansionFormEnd(){this._beginToken(s.EXPANSION_FORM_END),this._requireCharCode(r.$RBRACE),this._endToken([]),this._expansionCaseStack.pop()}_consumeText(){let t=this._cursor.clone();this._beginToken(s.TEXT,t);let o=[];do this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(o.push(this._interpolationConfig.start),this._inInterpolation=!0):this._interpolationConfig&&this._inInterpolation&&this._attemptStr(this._interpolationConfig.end)?(o.push(this._interpolationConfig.end),this._inInterpolation=!1):o.push(this._readChar(!0));while(!this._isTextEnd());this._endToken([this._processCarriageReturns(o.join(""))])}_isTextEnd(){return!!(this._cursor.peek()===r.$LT||this._cursor.peek()===r.$EOF||this._tokenizeIcu&&!this._inInterpolation&&(this.isExpansionFormStart()||this._cursor.peek()===r.$RBRACE&&this._isInExpansionCase()))}_readUntil(t){let o=this._cursor.clone();return this._attemptUntilChar(t),this._cursor.getChars(o)}_isInExpansionCase(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===s.EXPANSION_CASE_EXP_START}_isInExpansionForm(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===s.EXPANSION_FORM_START}isExpansionFormStart(){if(this._cursor.peek()!==r.$LBRACE)return!1;if(this._interpolationConfig){let t=this._cursor.clone(),o=this._attemptStr(this._interpolationConfig.start);return this._cursor=t,!o}return!0}_handleFullNameStackForTagOpen(t,o){let E=D.mergeNsAndName(t,o);(this._fullNameStack.length===0||this._fullNameStack[this._fullNameStack.length-1]===E)&&this._fullNameStack.push(E)}_handleFullNameStackForTagClose(t,o){let E=D.mergeNsAndName(t,o);this._fullNameStack.length!==0&&this._fullNameStack[this._fullNameStack.length-1]===E&&this._fullNameStack.pop()}};function m(t){return!r.isWhitespace(t)||t===r.$EOF}function T(t){return r.isWhitespace(t)||t===r.$GT||t===r.$SLASH||t===r.$SQ||t===r.$DQ||t===r.$EQ}function w(t){return(t<r.$a||r.$z<t)&&(t<r.$A||r.$Z<t)&&(t<r.$0||t>r.$9)}function g(t){return t==r.$SEMICOLON||t==r.$EOF||!r.isAsciiHexDigit(t)}function N(t){return t==r.$SEMICOLON||t==r.$EOF||!r.isAsciiLetter(t)}function R(t){return t===r.$EQ||r.isAsciiLetter(t)||r.isDigit(t)}function j(t,o){return _(t)==_(o)}function _(t){return t>=r.$a&&t<=r.$z?t-r.$a+r.$A:t}function O(t){let o=[],E;for(let p=0;p<t.length;p++){let A=t[p];E&&E.type==s.TEXT&&A.type==s.TEXT?(E.parts[0]+=A.parts[0],E.sourceSpan.end=A.sourceSpan.end):(E=A,o.push(E))}return o}var x=class{constructor(t,o){if(t instanceof x)this.file=t.file,this.input=t.input,this.end=t.end,this.state=Object.assign({},t.state);else{if(!o)throw new Error("Programming error: the range argument must be provided with a file argument.");this.file=t,this.input=t.content,this.end=o.endPos,this.state={peek:-1,offset:o.startPos,line:o.startLine,column:o.startCol}}}clone(){return new x(this)}peek(){return this.state.peek}charsLeft(){return this.end-this.state.offset}diff(t){return this.state.offset-t.state.offset}advance(){this.advanceState(this.state)}init(){this.updatePeek(this.state)}getSpan(t,o){if(t=t||this,o)for(t=t.clone();this.diff(t)>0&&o.indexOf(t.peek())!==-1;)t.advance();return new u.ParseSourceSpan(new u.ParseLocation(t.file,t.state.offset,t.state.line,t.state.column),new u.ParseLocation(this.file,this.state.offset,this.state.line,this.state.column))}getChars(t){return this.input.substring(t.state.offset,this.state.offset)}charAt(t){return this.input.charCodeAt(t)}advanceState(t){if(t.offset>=this.end)throw this.state=t,new $('Unexpected character "EOF"',this);let o=this.charAt(t.offset);o===r.$LF?(t.line++,t.column=0):r.isNewLine(o)||t.column++,t.offset++,this.updatePeek(t)}updatePeek(t){t.peek=t.offset>=this.end?r.$EOF:this.charAt(t.offset)}},k=class extends x{constructor(t,o){t instanceof k?(super(t),this.internalState=Object.assign({},t.internalState)):(super(t,o),this.internalState=this.state)}advance(){this.state=this.internalState,super.advance(),this.processEscapeSequence()}init(){super.init(),this.processEscapeSequence()}clone(){return new k(this)}getChars(t){let o=t.clone(),E="";for(;o.internalState.offset<this.internalState.offset;)E+=String.fromCodePoint(o.peek()),o.advance();return E}processEscapeSequence(){let t=()=>this.internalState.peek;if(t()===r.$BACKSLASH)if(this.internalState=Object.assign({},this.state),this.advanceState(this.internalState),t()===r.$n)this.state.peek=r.$LF;else if(t()===r.$r)this.state.peek=r.$CR;else if(t()===r.$v)this.state.peek=r.$VTAB;else if(t()===r.$t)this.state.peek=r.$TAB;else if(t()===r.$b)this.state.peek=r.$BSPACE;else if(t()===r.$f)this.state.peek=r.$FF;else if(t()===r.$u)if(this.advanceState(this.internalState),t()===r.$LBRACE){this.advanceState(this.internalState);let o=this.clone(),E=0;for(;t()!==r.$RBRACE;)this.advanceState(this.internalState),E++;this.state.peek=this.decodeHexDigits(o,E)}else{let o=this.clone();this.advanceState(this.internalState),this.advanceState(this.internalState),this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(o,4)}else if(t()===r.$x){this.advanceState(this.internalState);let o=this.clone();this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(o,2)}else if(r.isOctalDigit(t())){let o="",E=0,p=this.clone();for(;r.isOctalDigit(t())&&E<3;)p=this.clone(),o+=String.fromCodePoint(t()),this.advanceState(this.internalState),E++;this.state.peek=parseInt(o,8),this.internalState=p.internalState}else r.isNewLine(this.internalState.peek)?(this.advanceState(this.internalState),this.state=this.internalState):this.state.peek=this.internalState.peek}decodeHexDigits(t,o){let E=this.input.substr(t.internalState.offset,o),p=parseInt(E,16);if(isNaN(p))throw t.state=t.internalState,new $("Invalid hexadecimal escape sequence",t);return p}},$=class{constructor(t,o){this.msg=t,this.cursor=o}};e.CursorError=$}}),ls=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/parser.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Be(),u=bs(),n=Yl(),D=Ze(),s=class extends r.ParseError{constructor(a,l,h){super(l,h),this.elementName=a}static create(a,l,h){return new s(a,l,h)}};e.TreeError=s;var i=class{constructor(a,l){this.rootNodes=a,this.errors=l}};e.ParseTreeResult=i;var f=class{constructor(a){this.getTagDefinition=a}parse(a,l,h){let C=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,d=arguments.length>4?arguments[4]:void 0,m=x=>function(k){for(var $=arguments.length,t=new Array($>1?$-1:0),o=1;o<$;o++)t[o-1]=arguments[o];return x(k.toLowerCase(),...t)},T=C?this.getTagDefinition:m(this.getTagDefinition),w=x=>T(x).contentType,g=C?d:m(d),N=d?(x,k,$,t)=>{let o=g(x,k,$,t);return o!==void 0?o:w(x)}:w,R=n.tokenize(a,l,N,h),j=h&&h.canSelfClose||!1,_=h&&h.allowHtmComponentClosingTags||!1,O=new c(R.tokens,T,j,_,C).build();return new i(O.rootNodes,R.errors.concat(O.errors))}};e.Parser=f;var c=class{constructor(a,l,h,C,d){this.tokens=a,this.getTagDefinition=l,this.canSelfClose=h,this.allowHtmComponentClosingTags=C,this.isTagNameCaseSensitive=d,this._index=-1,this._rootNodes=[],this._errors=[],this._elementStack=[],this._advance()}build(){for(;this._peek.type!==n.TokenType.EOF;)this._peek.type===n.TokenType.TAG_OPEN_START?this._consumeStartTag(this._advance()):this._peek.type===n.TokenType.TAG_CLOSE?(this._closeVoidElement(),this._consumeEndTag(this._advance())):this._peek.type===n.TokenType.CDATA_START?(this._closeVoidElement(),this._consumeCdata(this._advance())):this._peek.type===n.TokenType.COMMENT_START?(this._closeVoidElement(),this._consumeComment(this._advance())):this._peek.type===n.TokenType.TEXT||this._peek.type===n.TokenType.RAW_TEXT||this._peek.type===n.TokenType.ESCAPABLE_RAW_TEXT?(this._closeVoidElement(),this._consumeText(this._advance())):this._peek.type===n.TokenType.EXPANSION_FORM_START?this._consumeExpansion(this._advance()):this._peek.type===n.TokenType.DOC_TYPE_START?this._consumeDocType(this._advance()):this._advance();return new i(this._rootNodes,this._errors)}_advance(){let a=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],a}_advanceIf(a){return this._peek.type===a?this._advance():null}_consumeCdata(a){let l=this._advance(),h=this._getText(l),C=this._advanceIf(n.TokenType.CDATA_END);this._addToParent(new u.CDATA(h,new r.ParseSourceSpan(a.sourceSpan.start,(C||l).sourceSpan.end)))}_consumeComment(a){let l=this._advanceIf(n.TokenType.RAW_TEXT),h=this._advanceIf(n.TokenType.COMMENT_END),C=l!=null?l.parts[0].trim():null,d=new r.ParseSourceSpan(a.sourceSpan.start,(h||l||a).sourceSpan.end);this._addToParent(new u.Comment(C,d))}_consumeDocType(a){let l=this._advanceIf(n.TokenType.RAW_TEXT),h=this._advanceIf(n.TokenType.DOC_TYPE_END),C=l!=null?l.parts[0].trim():null,d=new r.ParseSourceSpan(a.sourceSpan.start,(h||l||a).sourceSpan.end);this._addToParent(new u.DocType(C,d))}_consumeExpansion(a){let l=this._advance(),h=this._advance(),C=[];for(;this._peek.type===n.TokenType.EXPANSION_CASE_VALUE;){let m=this._parseExpansionCase();if(!m)return;C.push(m)}if(this._peek.type!==n.TokenType.EXPANSION_FORM_END){this._errors.push(s.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."));return}let d=new r.ParseSourceSpan(a.sourceSpan.start,this._peek.sourceSpan.end);this._addToParent(new u.Expansion(l.parts[0],h.parts[0],C,d,l.sourceSpan)),this._advance()}_parseExpansionCase(){let a=this._advance();if(this._peek.type!==n.TokenType.EXPANSION_CASE_EXP_START)return this._errors.push(s.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;let l=this._advance(),h=this._collectExpansionExpTokens(l);if(!h)return null;let C=this._advance();h.push(new n.Token(n.TokenType.EOF,[],C.sourceSpan));let d=new c(h,this.getTagDefinition,this.canSelfClose,this.allowHtmComponentClosingTags,this.isTagNameCaseSensitive).build();if(d.errors.length>0)return this._errors=this._errors.concat(d.errors),null;let m=new r.ParseSourceSpan(a.sourceSpan.start,C.sourceSpan.end),T=new r.ParseSourceSpan(l.sourceSpan.start,C.sourceSpan.end);return new u.ExpansionCase(a.parts[0],d.rootNodes,m,a.sourceSpan,T)}_collectExpansionExpTokens(a){let l=[],h=[n.TokenType.EXPANSION_CASE_EXP_START];for(;;){if((this._peek.type===n.TokenType.EXPANSION_FORM_START||this._peek.type===n.TokenType.EXPANSION_CASE_EXP_START)&&h.push(this._peek.type),this._peek.type===n.TokenType.EXPANSION_CASE_EXP_END)if(F(h,n.TokenType.EXPANSION_CASE_EXP_START)){if(h.pop(),h.length==0)return l}else return this._errors.push(s.create(null,a.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(this._peek.type===n.TokenType.EXPANSION_FORM_END)if(F(h,n.TokenType.EXPANSION_FORM_START))h.pop();else return this._errors.push(s.create(null,a.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(this._peek.type===n.TokenType.EOF)return this._errors.push(s.create(null,a.sourceSpan,"Invalid ICU message. Missing '}'.")),null;l.push(this._advance())}}_getText(a){let l=a.parts[0];if(l.length>0&&l[0]==`
`){let h=this._getParentElement();h!=null&&h.children.length==0&&this.getTagDefinition(h.name).ignoreFirstLf&&(l=l.substring(1))}return l}_consumeText(a){let l=this._getText(a);l.length>0&&this._addToParent(new u.Text(l,a.sourceSpan))}_closeVoidElement(){let a=this._getParentElement();a&&this.getTagDefinition(a.name).isVoid&&this._elementStack.pop()}_consumeStartTag(a){let l=a.parts[0],h=a.parts[1],C=[];for(;this._peek.type===n.TokenType.ATTR_NAME;)C.push(this._consumeAttr(this._advance()));let d=this._getElementFullName(l,h,this._getParentElement()),m=!1;if(this._peek.type===n.TokenType.TAG_OPEN_END_VOID){this._advance(),m=!0;let R=this.getTagDefinition(d);this.canSelfClose||R.canSelfClose||D.getNsPrefix(d)!==null||R.isVoid||this._errors.push(s.create(d,a.sourceSpan,`Only void and foreign elements can be self closed "${a.parts[1]}"`))}else this._peek.type===n.TokenType.TAG_OPEN_END&&(this._advance(),m=!1);let T=this._peek.sourceSpan.start,w=new r.ParseSourceSpan(a.sourceSpan.start,T),g=new r.ParseSourceSpan(a.sourceSpan.start.moveBy(1),a.sourceSpan.end),N=new u.Element(d,C,[],w,w,void 0,g);this._pushElement(N),m&&(this._popElement(d),N.endSourceSpan=w)}_pushElement(a){let l=this._getParentElement();l&&this.getTagDefinition(l.name).isClosedByChild(a.name)&&this._elementStack.pop(),this._addToParent(a),this._elementStack.push(a)}_consumeEndTag(a){let l=this.allowHtmComponentClosingTags&&a.parts.length===0?null:this._getElementFullName(a.parts[0],a.parts[1],this._getParentElement());if(this._getParentElement()&&(this._getParentElement().endSourceSpan=a.sourceSpan),l&&this.getTagDefinition(l).isVoid)this._errors.push(s.create(l,a.sourceSpan,`Void elements do not have end tags "${a.parts[1]}"`));else if(!this._popElement(l)){let h=`Unexpected closing tag "${l}". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags`;this._errors.push(s.create(l,a.sourceSpan,h))}}_popElement(a){for(let l=this._elementStack.length-1;l>=0;l--){let h=this._elementStack[l];if(!a||(D.getNsPrefix(h.name)?h.name==a:h.name.toLowerCase()==a.toLowerCase()))return this._elementStack.splice(l,this._elementStack.length-l),!0;if(!this.getTagDefinition(h.name).closedByParent)return!1}return!1}_consumeAttr(a){let l=D.mergeNsAndName(a.parts[0],a.parts[1]),h=a.sourceSpan.end,C="",d,m;if(this._peek.type===n.TokenType.ATTR_QUOTE&&(m=this._advance().sourceSpan.start),this._peek.type===n.TokenType.ATTR_VALUE){let T=this._advance();C=T.parts[0],h=T.sourceSpan.end,d=T.sourceSpan}return this._peek.type===n.TokenType.ATTR_QUOTE&&(h=this._advance().sourceSpan.end,d=new r.ParseSourceSpan(m,h)),new u.Attribute(l,C,new r.ParseSourceSpan(a.sourceSpan.start,h),d,a.sourceSpan)}_getParentElement(){return this._elementStack.length>0?this._elementStack[this._elementStack.length-1]:null}_getParentElementSkippingContainers(){let a=null;for(let l=this._elementStack.length-1;l>=0;l--){if(!D.isNgContainer(this._elementStack[l].name))return{parent:this._elementStack[l],container:a};a=this._elementStack[l]}return{parent:null,container:a}}_addToParent(a){let l=this._getParentElement();l!=null?l.children.push(a):this._rootNodes.push(a)}_insertBeforeContainer(a,l,h){if(!l)this._addToParent(h),this._elementStack.push(h);else{if(a){let C=a.children.indexOf(l);a.children[C]=h}else this._rootNodes.push(h);h.children.push(l),this._elementStack.splice(this._elementStack.indexOf(l),0,h)}}_getElementFullName(a,l,h){return a===""&&(a=this.getTagDefinition(l).implicitNamespacePrefix||"",a===""&&h!=null&&(a=D.getNsPrefix(h.name))),D.mergeNsAndName(a,l)}};function F(a,l){return a.length>0&&a[a.length-1]===l}}}),Ql=I({"node_modules/angular-html-parser/lib/compiler/src/ml_parser/html_parser.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Bs(),u=ls(),n=ls();e.ParseTreeResult=n.ParseTreeResult,e.TreeError=n.TreeError;var D=class extends u.Parser{constructor(){super(r.getHtmlTagDefinition)}parse(s,i,f){let c=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,F=arguments.length>4?arguments[4]:void 0;return super.parse(s,i,f,c,F)}};e.HtmlParser=D}}),ws=I({"node_modules/angular-html-parser/lib/angular-html-parser/src/index.js"(e){"use strict";q(),Object.defineProperty(e,"__esModule",{value:!0});var r=Ql(),u=Ze();e.TagContentType=u.TagContentType;var n=null,D=()=>(n||(n=new r.HtmlParser),n);function s(i){let f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{canSelfClose:c=!1,allowHtmComponentClosingTags:F=!1,isTagNameCaseSensitive:a=!1,getTagContentType:l}=f;return D().parse(i,"angular-html-parser",{tokenizeExpansionForms:!1,interpolationConfig:void 0,canSelfClose:c,allowHtmComponentClosingTags:F},a,l)}e.parse=s}});q();var{ParseSourceSpan:Qe,ParseLocation:cs,ParseSourceFile:Kl}=Be(),Jl=ol(),Zl=Cs(),ec=Dl(),{inferParserByLanguage:rc}=xl(),uc=kl(),Vr=Ml(),hs=jl(),{hasPragma:tc}=Ul(),{Node:nc}=Gl(),{parseIeConditionalComment:sc}=Vl(),{locStart:ic,locEnd:ac}=Xl();function oc(e,r,u){let{canSelfClose:n,normalizeTagName:D,normalizeAttributeName:s,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:f,getTagContentType:c}=r,F=ws(),{RecursiveVisitor:a,visitAll:l}=bs(),{ParseSourceSpan:h}=Be(),{getHtmlTagDefinition:C}=Bs(),{rootNodes:d,errors:m}=F.parse(e,{canSelfClose:n,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:f,getTagContentType:c});if(u.parser==="vue")if(d.some(O=>O.type==="docType"&&O.value==="html"||O.type==="element"&&O.name.toLowerCase()==="html")){n=!0,D=!0,s=!0,i=!0,f=!1;let O=F.parse(e,{canSelfClose:n,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:f});d=O.rootNodes,m=O.errors}else{let O=x=>{if(!x||x.type!=="element"||x.name!=="template")return!1;let k=x.attrs.find(t=>t.name==="lang"),$=k&&k.value;return!$||rc($,u)==="html"};if(d.some(O)){let x,k=()=>F.parse(e,{canSelfClose:n,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:f}),$=()=>x||(x=k()),t=o=>$().rootNodes.find(E=>{let{startSourceSpan:p}=E;return p&&p.start.offset===o.startSourceSpan.start.offset});for(let o=0;o<d.length;o++){let E=d[o],{endSourceSpan:p,startSourceSpan:A}=E;if(p===null)m=$().errors,d[o]=t(E)||E;else if(O(E)){let M=$(),z=A.end.offset,V=p.start.offset;for(let X of M.errors){let{offset:H}=X.span.start;if(z<H&&H<V){m=[X];break}}d[o]=t(E)||E}}}}if(m.length>0){let{msg:_,span:{start:O,end:x}}=m[0];throw ec(_,{start:{line:O.line+1,column:O.col+1},end:{line:x.line+1,column:x.col+1}})}let T=_=>{let O=_.name.startsWith(":")?_.name.slice(1).split(":")[0]:null,x=_.nameSpan.toString(),k=O!==null&&x.startsWith(`${O}:`),$=k?x.slice(O.length+1):x;_.name=$,_.namespace=O,_.hasExplicitNamespace=k},w=_=>{switch(_.type){case"element":T(_);for(let O of _.attrs)T(O),O.valueSpan?(O.value=O.valueSpan.toString(),/["']/.test(O.value[0])&&(O.value=O.value.slice(1,-1))):O.value=null;break;case"comment":_.value=_.sourceSpan.toString().slice(4,-3);break;case"text":_.value=_.sourceSpan.toString();break}},g=(_,O)=>{let x=_.toLowerCase();return O(x)?x:_},N=_=>{if(_.type==="element"&&(D&&(!_.namespace||_.namespace===_.tagDefinition.implicitNamespacePrefix||hs(_))&&(_.name=g(_.name,O=>O in uc)),s)){let O=Vr[_.name]||Object.create(null);for(let x of _.attrs)x.namespace||(x.name=g(x.name,k=>_.name in Vr&&(k in Vr["*"]||k in O)))}},R=_=>{_.sourceSpan&&_.endSourceSpan&&(_.sourceSpan=new h(_.sourceSpan.start,_.endSourceSpan.end))},j=_=>{if(_.type==="element"){let O=C(f?_.name:_.name.toLowerCase());!_.namespace||_.namespace===O.implicitNamespacePrefix||hs(_)?_.tagDefinition=O:_.tagDefinition=C("")}};return l(new class extends a{visit(_){w(_),j(_),N(_),R(_)}},d),d}function Ns(e,r,u){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,{frontMatter:D,content:s}=n?Jl(e):{frontMatter:null,content:e},i=new Kl(e,r.filepath),f=new cs(i,0,0,0),c=f.moveBy(e.length),F={type:"root",sourceSpan:new Qe(f,c),children:oc(s,u,r)};if(D){let h=new cs(i,0,0,0),C=h.moveBy(D.raw.length);D.sourceSpan=new Qe(h,C),F.children.unshift(D)}let a=new nc(F),l=(h,C)=>{let{offset:d}=C,m=e.slice(0,d).replace(/[^\n\r]/g," "),w=Ns(m+h,r,u,!1);w.sourceSpan=new Qe(C,Zl(w.children).sourceSpan.end);let g=w.children[0];return g.length===d?w.children.shift():(g.sourceSpan=new Qe(g.sourceSpan.start.moveBy(d),g.sourceSpan.end),g.value=g.value.slice(d)),w};return a.walk(h=>{if(h.type==="comment"){let C=sc(h,l);C&&h.parent.replaceChild(h,C)}}),a}function Ke(){let{name:e,canSelfClose:r=!1,normalizeTagName:u=!1,normalizeAttributeName:n=!1,allowHtmComponentClosingTags:D=!1,isTagNameCaseSensitive:s=!1,getTagContentType:i}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return{parse:(f,c,F)=>Ns(f,Object.assign({parser:e},F),{canSelfClose:r,normalizeTagName:u,normalizeAttributeName:n,allowHtmComponentClosingTags:D,isTagNameCaseSensitive:s,getTagContentType:i}),hasPragma:tc,astFormat:"html",locStart:ic,locEnd:ac}}Os.exports={parsers:{html:Ke({name:"html",canSelfClose:!0,normalizeTagName:!0,normalizeAttributeName:!0,allowHtmComponentClosingTags:!0}),angular:Ke({name:"angular",canSelfClose:!0}),vue:Ke({name:"vue",canSelfClose:!0,isTagNameCaseSensitive:!0,getTagContentType:(e,r,u,n)=>{if(e.toLowerCase()!=="html"&&!u&&(e!=="template"||n.some(D=>{let{name:s,value:i}=D;return s==="lang"&&i!=="html"&&i!==""&&i!==void 0})))return ws().TagContentType.RAW_TEXT}}),lwc:Ke({name:"lwc"})}}});return Dc();});