(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.babel=e()}})(function(){"use strict";var E=(l,h)=>()=>(h||l((h={exports:{}}).exports,h),h.exports);var re=E((xd,Zr)=>{var Ct=function(l){return l&&l.Math==Math&&l};Zr.exports=Ct(typeof globalThis=="object"&&globalThis)||Ct(typeof window=="object"&&window)||Ct(typeof self=="object"&&self)||Ct(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var ie=E((gd,ei)=>{ei.exports=function(l){try{return!!l()}catch{return!0}}});var ye=E((Pd,ti)=>{var kh=ie();ti.exports=!kh(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var bt=E((Ad,si)=>{var Dh=ie();si.exports=!Dh(function(){var l=function(){}.bind();return typeof l!="function"||l.hasOwnProperty("prototype")})});var wt=E((Td,ri)=>{var Fh=bt(),St=Function.prototype.call;ri.exports=Fh?St.bind(St):function(){return St.apply(St,arguments)}});var oi=E(ni=>{"use strict";var ii={}.propertyIsEnumerable,ai=Object.getOwnPropertyDescriptor,Lh=ai&&!ii.call({1:2},1);ni.f=Lh?function(h){var p=ai(this,h);return!!p&&p.enumerable}:ii});var fs=E((Ed,li)=>{li.exports=function(l,h){return{enumerable:!(l&1),configurable:!(l&2),writable:!(l&4),value:h}}});var ae=E((Cd,ci)=>{var hi=bt(),ui=Function.prototype,ds=ui.call,Oh=hi&&ui.bind.bind(ds,ds);ci.exports=hi?Oh:function(l){return function(){return ds.apply(l,arguments)}}});var Ye=E((bd,fi)=>{var pi=ae(),Bh=pi({}.toString),Mh=pi("".slice);fi.exports=function(l){return Mh(Bh(l),8,-1)}});var mi=E((Sd,di)=>{var _h=ae(),Rh=ie(),jh=Ye(),ms=Object,qh=_h("".split);di.exports=Rh(function(){return!ms("z").propertyIsEnumerable(0)})?function(l){return jh(l)=="String"?qh(l,""):ms(l)}:ms});var ys=E((wd,yi)=>{yi.exports=function(l){return l==null}});var xs=E((Id,xi)=>{var Uh=ys(),$h=TypeError;xi.exports=function(l){if(Uh(l))throw $h("Can't call method on "+l);return l}});var It=E((Nd,gi)=>{var Hh=mi(),zh=xs();gi.exports=function(l){return Hh(zh(l))}});var Ps=E((kd,Pi)=>{var gs=typeof document=="object"&&document.all,Vh=typeof gs>"u"&&gs!==void 0;Pi.exports={all:gs,IS_HTMLDDA:Vh}});var ee=E((Dd,Ti)=>{var Ai=Ps(),Kh=Ai.all;Ti.exports=Ai.IS_HTMLDDA?function(l){return typeof l=="function"||l===Kh}:function(l){return typeof l=="function"}});var Ie=E((Fd,Ci)=>{var vi=ee(),Ei=Ps(),Wh=Ei.all;Ci.exports=Ei.IS_HTMLDDA?function(l){return typeof l=="object"?l!==null:vi(l)||l===Wh}:function(l){return typeof l=="object"?l!==null:vi(l)}});var Qe=E((Ld,bi)=>{var As=re(),Gh=ee(),Jh=function(l){return Gh(l)?l:void 0};bi.exports=function(l,h){return arguments.length<2?Jh(As[l]):As[l]&&As[l][h]}});var wi=E((Od,Si)=>{var Xh=ae();Si.exports=Xh({}.isPrototypeOf)});var Ni=E((Bd,Ii)=>{var Yh=Qe();Ii.exports=Yh("navigator","userAgent")||""});var Mi=E((Md,Bi)=>{var Oi=re(),Ts=Ni(),ki=Oi.process,Di=Oi.Deno,Fi=ki&&ki.versions||Di&&Di.version,Li=Fi&&Fi.v8,ne,Nt;Li&&(ne=Li.split("."),Nt=ne[0]>0&&ne[0]<4?1:+(ne[0]+ne[1]));!Nt&&Ts&&(ne=Ts.match(/Edge\/(\d+)/),(!ne||ne[1]>=74)&&(ne=Ts.match(/Chrome\/(\d+)/),ne&&(Nt=+ne[1])));Bi.exports=Nt});var vs=E((_d,Ri)=>{var _i=Mi(),Qh=ie();Ri.exports=!!Object.getOwnPropertySymbols&&!Qh(function(){var l=Symbol();return!String(l)||!(Object(l)instanceof Symbol)||!Symbol.sham&&_i&&_i<41})});var Es=E((Rd,ji)=>{var Zh=vs();ji.exports=Zh&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Cs=E((jd,qi)=>{var eu=Qe(),tu=ee(),su=wi(),ru=Es(),iu=Object;qi.exports=ru?function(l){return typeof l=="symbol"}:function(l){var h=eu("Symbol");return tu(h)&&su(h.prototype,iu(l))}});var $i=E((qd,Ui)=>{var au=String;Ui.exports=function(l){try{return au(l)}catch{return"Object"}}});var kt=E((Ud,Hi)=>{var nu=ee(),ou=$i(),lu=TypeError;Hi.exports=function(l){if(nu(l))return l;throw lu(ou(l)+" is not a function")}});var Vi=E(($d,zi)=>{var hu=kt(),uu=ys();zi.exports=function(l,h){var p=l[h];return uu(p)?void 0:hu(p)}});var Wi=E((Hd,Ki)=>{var bs=wt(),Ss=ee(),ws=Ie(),cu=TypeError;Ki.exports=function(l,h){var p,d;if(h==="string"&&Ss(p=l.toString)&&!ws(d=bs(p,l))||Ss(p=l.valueOf)&&!ws(d=bs(p,l))||h!=="string"&&Ss(p=l.toString)&&!ws(d=bs(p,l)))return d;throw cu("Can't convert object to primitive value")}});var Ji=E((zd,Gi)=>{Gi.exports=!1});var Dt=E((Vd,Yi)=>{var Xi=re(),pu=Object.defineProperty;Yi.exports=function(l,h){try{pu(Xi,l,{value:h,configurable:!0,writable:!0})}catch{Xi[l]=h}return h}});var Ft=E((Kd,Zi)=>{var fu=re(),du=Dt(),Qi="__core-js_shared__",mu=fu[Qi]||du(Qi,{});Zi.exports=mu});var Is=E((Wd,ta)=>{var yu=Ji(),ea=Ft();(ta.exports=function(l,h){return ea[l]||(ea[l]=h!==void 0?h:{})})("versions",[]).push({version:"3.26.1",mode:yu?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var Ns=E((Gd,sa)=>{var xu=xs(),gu=Object;sa.exports=function(l){return gu(xu(l))}});var ve=E((Jd,ra)=>{var Pu=ae(),Au=Ns(),Tu=Pu({}.hasOwnProperty);ra.exports=Object.hasOwn||function(h,p){return Tu(Au(h),p)}});var ks=E((Xd,ia)=>{var vu=ae(),Eu=0,Cu=Math.random(),bu=vu(1 .toString);ia.exports=function(l){return"Symbol("+(l===void 0?"":l)+")_"+bu(++Eu+Cu,36)}});var Ze=E((Yd,ha)=>{var Su=re(),wu=Is(),aa=ve(),Iu=ks(),na=vs(),la=Es(),qe=wu("wks"),Ne=Su.Symbol,oa=Ne&&Ne.for,Nu=la?Ne:Ne&&Ne.withoutSetter||Iu;ha.exports=function(l){if(!aa(qe,l)||!(na||typeof qe[l]=="string")){var h="Symbol."+l;na&&aa(Ne,l)?qe[l]=Ne[l]:la&&oa?qe[l]=oa(h):qe[l]=Nu(h)}return qe[l]}});var fa=E((Qd,pa)=>{var ku=wt(),ua=Ie(),ca=Cs(),Du=Vi(),Fu=Wi(),Lu=Ze(),Ou=TypeError,Bu=Lu("toPrimitive");pa.exports=function(l,h){if(!ua(l)||ca(l))return l;var p=Du(l,Bu),d;if(p){if(h===void 0&&(h="default"),d=ku(p,l,h),!ua(d)||ca(d))return d;throw Ou("Can't convert object to primitive value")}return h===void 0&&(h="number"),Fu(l,h)}});var Ds=E((Zd,da)=>{var Mu=fa(),_u=Cs();da.exports=function(l){var h=Mu(l,"string");return _u(h)?h:h+""}});var xa=E((em,ya)=>{var Ru=re(),ma=Ie(),Fs=Ru.document,ju=ma(Fs)&&ma(Fs.createElement);ya.exports=function(l){return ju?Fs.createElement(l):{}}});var Ls=E((tm,ga)=>{var qu=ye(),Uu=ie(),$u=xa();ga.exports=!qu&&!Uu(function(){return Object.defineProperty($u("div"),"a",{get:function(){return 7}}).a!=7})});var Os=E(Aa=>{var Hu=ye(),zu=wt(),Vu=oi(),Ku=fs(),Wu=It(),Gu=Ds(),Ju=ve(),Xu=Ls(),Pa=Object.getOwnPropertyDescriptor;Aa.f=Hu?Pa:function(h,p){if(h=Wu(h),p=Gu(p),Xu)try{return Pa(h,p)}catch{}if(Ju(h,p))return Ku(!zu(Vu.f,h,p),h[p])}});var va=E((rm,Ta)=>{var Yu=ye(),Qu=ie();Ta.exports=Yu&&Qu(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var Lt=E((im,Ea)=>{var Zu=Ie(),ec=String,tc=TypeError;Ea.exports=function(l){if(Zu(l))return l;throw tc(ec(l)+" is not an object")}});var et=E(ba=>{var sc=ye(),rc=Ls(),ic=va(),Ot=Lt(),Ca=Ds(),ac=TypeError,Bs=Object.defineProperty,nc=Object.getOwnPropertyDescriptor,Ms="enumerable",_s="configurable",Rs="writable";ba.f=sc?ic?function(h,p,d){if(Ot(h),p=Ca(p),Ot(d),typeof h=="function"&&p==="prototype"&&"value"in d&&Rs in d&&!d[Rs]){var x=nc(h,p);x&&x[Rs]&&(h[p]=d.value,d={configurable:_s in d?d[_s]:x[_s],enumerable:Ms in d?d[Ms]:x[Ms],writable:!1})}return Bs(h,p,d)}:Bs:function(h,p,d){if(Ot(h),p=Ca(p),Ot(d),rc)try{return Bs(h,p,d)}catch{}if("get"in d||"set"in d)throw ac("Accessors not supported");return"value"in d&&(h[p]=d.value),h}});var js=E((nm,Sa)=>{var oc=ye(),lc=et(),hc=fs();Sa.exports=oc?function(l,h,p){return lc.f(l,h,hc(1,p))}:function(l,h,p){return l[h]=p,l}});var Na=E((om,Ia)=>{var qs=ye(),uc=ve(),wa=Function.prototype,cc=qs&&Object.getOwnPropertyDescriptor,Us=uc(wa,"name"),pc=Us&&function(){}.name==="something",fc=Us&&(!qs||qs&&cc(wa,"name").configurable);Ia.exports={EXISTS:Us,PROPER:pc,CONFIGURABLE:fc}});var Hs=E((lm,ka)=>{var dc=ae(),mc=ee(),$s=Ft(),yc=dc(Function.toString);mc($s.inspectSource)||($s.inspectSource=function(l){return yc(l)});ka.exports=$s.inspectSource});var La=E((hm,Fa)=>{var xc=re(),gc=ee(),Da=xc.WeakMap;Fa.exports=gc(Da)&&/native code/.test(String(Da))});var Ma=E((um,Ba)=>{var Pc=Is(),Ac=ks(),Oa=Pc("keys");Ba.exports=function(l){return Oa[l]||(Oa[l]=Ac(l))}});var zs=E((cm,_a)=>{_a.exports={}});var Ua=E((pm,qa)=>{var Tc=La(),ja=re(),vc=Ie(),Ec=js(),Vs=ve(),Ks=Ft(),Cc=Ma(),bc=zs(),Ra="Object already initialized",Ws=ja.TypeError,Sc=ja.WeakMap,Bt,tt,Mt,wc=function(l){return Mt(l)?tt(l):Bt(l,{})},Ic=function(l){return function(h){var p;if(!vc(h)||(p=tt(h)).type!==l)throw Ws("Incompatible receiver, "+l+" required");return p}};Tc||Ks.state?(oe=Ks.state||(Ks.state=new Sc),oe.get=oe.get,oe.has=oe.has,oe.set=oe.set,Bt=function(l,h){if(oe.has(l))throw Ws(Ra);return h.facade=l,oe.set(l,h),h},tt=function(l){return oe.get(l)||{}},Mt=function(l){return oe.has(l)}):(ke=Cc("state"),bc[ke]=!0,Bt=function(l,h){if(Vs(l,ke))throw Ws(Ra);return h.facade=l,Ec(l,ke,h),h},tt=function(l){return Vs(l,ke)?l[ke]:{}},Mt=function(l){return Vs(l,ke)});var oe,ke;qa.exports={set:Bt,get:tt,has:Mt,enforce:wc,getterFor:Ic}});var Js=E((fm,Ha)=>{var Nc=ie(),kc=ee(),_t=ve(),Gs=ye(),Dc=Na().CONFIGURABLE,Fc=Hs(),$a=Ua(),Lc=$a.enforce,Oc=$a.get,Rt=Object.defineProperty,Bc=Gs&&!Nc(function(){return Rt(function(){},"length",{value:8}).length!==8}),Mc=String(String).split("String"),_c=Ha.exports=function(l,h,p){String(h).slice(0,7)==="Symbol("&&(h="["+String(h).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),p&&p.getter&&(h="get "+h),p&&p.setter&&(h="set "+h),(!_t(l,"name")||Dc&&l.name!==h)&&(Gs?Rt(l,"name",{value:h,configurable:!0}):l.name=h),Bc&&p&&_t(p,"arity")&&l.length!==p.arity&&Rt(l,"length",{value:p.arity});try{p&&_t(p,"constructor")&&p.constructor?Gs&&Rt(l,"prototype",{writable:!1}):l.prototype&&(l.prototype=void 0)}catch{}var d=Lc(l);return _t(d,"source")||(d.source=Mc.join(typeof h=="string"?h:"")),l};Function.prototype.toString=_c(function(){return kc(this)&&Oc(this).source||Fc(this)},"toString")});var Va=E((dm,za)=>{var Rc=ee(),jc=et(),qc=Js(),Uc=Dt();za.exports=function(l,h,p,d){d||(d={});var x=d.enumerable,P=d.name!==void 0?d.name:h;if(Rc(p)&&qc(p,P,d),d.global)x?l[h]=p:Uc(h,p);else{try{d.unsafe?l[h]&&(x=!0):delete l[h]}catch{}x?l[h]=p:jc.f(l,h,{value:p,enumerable:!1,configurable:!d.nonConfigurable,writable:!d.nonWritable})}return l}});var Wa=E((mm,Ka)=>{var $c=Math.ceil,Hc=Math.floor;Ka.exports=Math.trunc||function(h){var p=+h;return(p>0?Hc:$c)(p)}});var Xs=E((ym,Ga)=>{var zc=Wa();Ga.exports=function(l){var h=+l;return h!==h||h===0?0:zc(h)}});var Xa=E((xm,Ja)=>{var Vc=Xs(),Kc=Math.max,Wc=Math.min;Ja.exports=function(l,h){var p=Vc(l);return p<0?Kc(p+h,0):Wc(p,h)}});var Qa=E((gm,Ya)=>{var Gc=Xs(),Jc=Math.min;Ya.exports=function(l){return l>0?Jc(Gc(l),9007199254740991):0}});var jt=E((Pm,Za)=>{var Xc=Qa();Za.exports=function(l){return Xc(l.length)}});var sn=E((Am,tn)=>{var Yc=It(),Qc=Xa(),Zc=jt(),en=function(l){return function(h,p,d){var x=Yc(h),P=Zc(x),m=Qc(d,P),v;if(l&&p!=p){for(;P>m;)if(v=x[m++],v!=v)return!0}else for(;P>m;m++)if((l||m in x)&&x[m]===p)return l||m||0;return!l&&-1}};tn.exports={includes:en(!0),indexOf:en(!1)}});var nn=E((Tm,an)=>{var ep=ae(),Ys=ve(),tp=It(),sp=sn().indexOf,rp=zs(),rn=ep([].push);an.exports=function(l,h){var p=tp(l),d=0,x=[],P;for(P in p)!Ys(rp,P)&&Ys(p,P)&&rn(x,P);for(;h.length>d;)Ys(p,P=h[d++])&&(~sp(x,P)||rn(x,P));return x}});var ln=E((vm,on)=>{on.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var un=E(hn=>{var ip=nn(),ap=ln(),np=ap.concat("length","prototype");hn.f=Object.getOwnPropertyNames||function(h){return ip(h,np)}});var pn=E(cn=>{cn.f=Object.getOwnPropertySymbols});var dn=E((bm,fn)=>{var op=Qe(),lp=ae(),hp=un(),up=pn(),cp=Lt(),pp=lp([].concat);fn.exports=op("Reflect","ownKeys")||function(h){var p=hp.f(cp(h)),d=up.f;return d?pp(p,d(h)):p}});var xn=E((Sm,yn)=>{var mn=ve(),fp=dn(),dp=Os(),mp=et();yn.exports=function(l,h,p){for(var d=fp(h),x=mp.f,P=dp.f,m=0;m<d.length;m++){var v=d[m];!mn(l,v)&&!(p&&mn(p,v))&&x(l,v,P(h,v))}}});var Pn=E((wm,gn)=>{var yp=ie(),xp=ee(),gp=/#|\.prototype\./,st=function(l,h){var p=Ap[Pp(l)];return p==vp?!0:p==Tp?!1:xp(h)?yp(h):!!h},Pp=st.normalize=function(l){return String(l).replace(gp,".").toLowerCase()},Ap=st.data={},Tp=st.NATIVE="N",vp=st.POLYFILL="P";gn.exports=st});var Zs=E((Im,An)=>{var Qs=re(),Ep=Os().f,Cp=js(),bp=Va(),Sp=Dt(),wp=xn(),Ip=Pn();An.exports=function(l,h){var p=l.target,d=l.global,x=l.stat,P,m,v,S,k,F;if(d?m=Qs:x?m=Qs[p]||Sp(p,{}):m=(Qs[p]||{}).prototype,m)for(v in h){if(k=h[v],l.dontCallGetSet?(F=Ep(m,v),S=F&&F.value):S=m[v],P=Ip(d?v:p+(x?".":"#")+v,l.forced),!P&&S!==void 0){if(typeof k==typeof S)continue;wp(k,S)}(l.sham||S&&S.sham)&&Cp(k,"sham",!0),bp(m,v,k,l)}}});var Tn=E(()=>{var Np=Zs(),er=re();Np({global:!0,forced:er.globalThis!==er},{globalThis:er})});var vn=E(()=>{Tn()});var bn=E((Lm,Cn)=>{var En=Js(),kp=et();Cn.exports=function(l,h,p){return p.get&&En(p.get,h,{getter:!0}),p.set&&En(p.set,h,{setter:!0}),kp.f(l,h,p)}});var wn=E((Om,Sn)=>{"use strict";var Dp=Lt();Sn.exports=function(){var l=Dp(this),h="";return l.hasIndices&&(h+="d"),l.global&&(h+="g"),l.ignoreCase&&(h+="i"),l.multiline&&(h+="m"),l.dotAll&&(h+="s"),l.unicode&&(h+="u"),l.unicodeSets&&(h+="v"),l.sticky&&(h+="y"),h}});var kn=E(()=>{var Fp=re(),Lp=ye(),Op=bn(),Bp=wn(),Mp=ie(),In=Fp.RegExp,Nn=In.prototype,_p=Lp&&Mp(function(){var l=!0;try{In(".","d")}catch{l=!1}var h={},p="",d=l?"dgimsy":"gimsy",x=function(S,k){Object.defineProperty(h,S,{get:function(){return p+=k,!0}})},P={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};l&&(P.hasIndices="d");for(var m in P)x(m,P[m]);var v=Object.getOwnPropertyDescriptor(Nn,"flags").get.call(h);return v!==d||p!==d});_p&&Op(Nn,"flags",{configurable:!0,get:Bp})});var tr=E((_m,Dn)=>{var Rp=Ye();Dn.exports=Array.isArray||function(h){return Rp(h)=="Array"}});var Ln=E((Rm,Fn)=>{var jp=TypeError,qp=9007199254740991;Fn.exports=function(l){if(l>qp)throw jp("Maximum allowed index exceeded");return l}});var Bn=E((jm,On)=>{var Up=Ye(),$p=ae();On.exports=function(l){if(Up(l)==="Function")return $p(l)}});var Rn=E((qm,_n)=>{var Mn=Bn(),Hp=kt(),zp=bt(),Vp=Mn(Mn.bind);_n.exports=function(l,h){return Hp(l),h===void 0?l:zp?Vp(l,h):function(){return l.apply(h,arguments)}}});var Un=E((Um,qn)=>{"use strict";var Kp=tr(),Wp=jt(),Gp=Ln(),Jp=Rn(),jn=function(l,h,p,d,x,P,m,v){for(var S=x,k=0,F=m?Jp(m,v):!1,w,L;k<d;)k in p&&(w=F?F(p[k],k,h):p[k],P>0&&Kp(w)?(L=Wp(w),S=jn(l,h,w,L,S,P-1)-1):(Gp(S+1),l[S]=w),S++),k++;return S};qn.exports=jn});var zn=E(($m,Hn)=>{var Xp=Ze(),Yp=Xp("toStringTag"),$n={};$n[Yp]="z";Hn.exports=String($n)==="[object z]"});var Kn=E((Hm,Vn)=>{var Qp=zn(),Zp=ee(),qt=Ye(),ef=Ze(),tf=ef("toStringTag"),sf=Object,rf=qt(function(){return arguments}())=="Arguments",af=function(l,h){try{return l[h]}catch{}};Vn.exports=Qp?qt:function(l){var h,p,d;return l===void 0?"Undefined":l===null?"Null":typeof(p=af(h=sf(l),tf))=="string"?p:rf?qt(h):(d=qt(h))=="Object"&&Zp(h.callee)?"Arguments":d}});var Qn=E((zm,Yn)=>{var nf=ae(),of=ie(),Wn=ee(),lf=Kn(),hf=Qe(),uf=Hs(),Gn=function(){},cf=[],Jn=hf("Reflect","construct"),sr=/^\s*(?:class|function)\b/,pf=nf(sr.exec),ff=!sr.exec(Gn),rt=function(h){if(!Wn(h))return!1;try{return Jn(Gn,cf,h),!0}catch{return!1}},Xn=function(h){if(!Wn(h))return!1;switch(lf(h)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ff||!!pf(sr,uf(h))}catch{return!0}};Xn.sham=!0;Yn.exports=!Jn||of(function(){var l;return rt(rt.call)||!rt(Object)||!rt(function(){l=!0})||l})?Xn:rt});var so=E((Vm,to)=>{var Zn=tr(),df=Qn(),mf=Ie(),yf=Ze(),xf=yf("species"),eo=Array;to.exports=function(l){var h;return Zn(l)&&(h=l.constructor,df(h)&&(h===eo||Zn(h.prototype))?h=void 0:mf(h)&&(h=h[xf],h===null&&(h=void 0))),h===void 0?eo:h}});var io=E((Km,ro)=>{var gf=so();ro.exports=function(l,h){return new(gf(l))(h===0?0:h)}});var ao=E(()=>{"use strict";var Pf=Zs(),Af=Un(),Tf=kt(),vf=Ns(),Ef=jt(),Cf=io();Pf({target:"Array",proto:!0},{flatMap:function(h){var p=vf(this),d=Ef(p),x;return Tf(h),x=Cf(p,0),x.length=Af(x,p,p,d,0,1,h,arguments.length>1?arguments[1]:void 0),x}})});var md=E((ty,Oo)=>{vn();kn();ao();var nr=Object.defineProperty,bf=Object.getOwnPropertyDescriptor,or=Object.getOwnPropertyNames,Sf=Object.prototype.hasOwnProperty,co=(l,h)=>function(){return l&&(h=(0,l[or(l)[0]])(l=0)),h},$=(l,h)=>function(){return h||(0,l[or(l)[0]])((h={exports:{}}).exports,h),h.exports},wf=(l,h)=>{for(var p in h)nr(l,p,{get:h[p],enumerable:!0})},If=(l,h,p,d)=>{if(h&&typeof h=="object"||typeof h=="function")for(let x of or(h))!Sf.call(l,x)&&x!==p&&nr(l,x,{get:()=>h[x],enumerable:!(d=bf(h,x))||d.enumerable});return l},Nf=l=>If(nr({},"__esModule",{value:!0}),l),U=co({"<define:process>"(){}}),kf=$({"src/utils/try-combinations.js"(l,h){"use strict";U();function p(){let d;for(var x=arguments.length,P=new Array(x),m=0;m<x;m++)P[m]=arguments[m];for(let[v,S]of P.entries())try{return{result:S()}}catch(k){v===0&&(d=k)}return{error:d}}h.exports=p}}),po=$({"src/language-js/utils/get-shebang.js"(l,h){"use strict";U();function p(d){if(!d.startsWith("#!"))return"";let x=d.indexOf(`
`);return x===-1?d:d.slice(0,x)}h.exports=p}}),Df=$({"src/utils/text/skip-inline-comment.js"(l,h){"use strict";U();function p(d,x){if(x===!1)return!1;if(d.charAt(x)==="/"&&d.charAt(x+1)==="*"){for(let P=x+2;P<d.length;++P)if(d.charAt(P)==="*"&&d.charAt(P+1)==="/")return P+2}return x}h.exports=p}}),Ff=$({"src/utils/text/skip-newline.js"(l,h){"use strict";U();function p(d,x,P){let m=P&&P.backwards;if(x===!1)return!1;let v=d.charAt(x);if(m){if(d.charAt(x-1)==="\r"&&v===`
`)return x-2;if(v===`
`||v==="\r"||v==="\u2028"||v==="\u2029")return x-1}else{if(v==="\r"&&d.charAt(x+1)===`
`)return x+2;if(v===`
`||v==="\r"||v==="\u2028"||v==="\u2029")return x+1}return x}h.exports=p}}),fo=$({"src/utils/text/skip.js"(l,h){"use strict";U();function p(v){return(S,k,F)=>{let w=F&&F.backwards;if(k===!1)return!1;let{length:L}=S,A=k;for(;A>=0&&A<L;){let _=S.charAt(A);if(v instanceof RegExp){if(!v.test(_))return A}else if(!v.includes(_))return A;w?A--:A++}return A===-1||A===L?A:!1}}var d=p(/\s/),x=p(" 	"),P=p(",; 	"),m=p(/[^\n\r]/);h.exports={skipWhitespace:d,skipSpaces:x,skipToLineEnd:P,skipEverythingButNewLine:m}}}),Lf=$({"src/utils/text/skip-trailing-comment.js"(l,h){"use strict";U();var{skipEverythingButNewLine:p}=fo();function d(x,P){return P===!1?!1:x.charAt(P)==="/"&&x.charAt(P+1)==="/"?p(x,P):P}h.exports=d}}),Of=$({"src/utils/text/get-next-non-space-non-comment-character-index-with-start-index.js"(l,h){"use strict";U();var p=Df(),d=Ff(),x=Lf(),{skipSpaces:P}=fo();function m(v,S){let k=null,F=S;for(;F!==k;)k=F,F=P(v,F),F=p(v,F),F=x(v,F),F=d(v,F);return F}h.exports=m}}),mo={};wf(mo,{EOL:()=>ar,arch:()=>Bf,cpus:()=>vo,default:()=>wo,endianness:()=>yo,freemem:()=>Ao,getNetworkInterfaces:()=>So,hostname:()=>xo,loadavg:()=>go,networkInterfaces:()=>bo,platform:()=>Mf,release:()=>Co,tmpDir:()=>rr,tmpdir:()=>ir,totalmem:()=>To,type:()=>Eo,uptime:()=>Po});function yo(){if(typeof Ut>"u"){var l=new ArrayBuffer(2),h=new Uint8Array(l),p=new Uint16Array(l);if(h[0]=1,h[1]=2,p[0]===258)Ut="BE";else if(p[0]===513)Ut="LE";else throw new Error("unable to figure out endianess")}return Ut}function xo(){return typeof globalThis.location<"u"?globalThis.location.hostname:""}function go(){return[]}function Po(){return 0}function Ao(){return Number.MAX_VALUE}function To(){return Number.MAX_VALUE}function vo(){return[]}function Eo(){return"Browser"}function Co(){return typeof globalThis.navigator<"u"?globalThis.navigator.appVersion:""}function bo(){}function So(){}function Bf(){return"javascript"}function Mf(){return"browser"}function rr(){return"/tmp"}var Ut,ir,ar,wo,_f=co({"node-modules-polyfills:os"(){U(),ir=rr,ar=`
`,wo={EOL:ar,tmpdir:ir,tmpDir:rr,networkInterfaces:bo,getNetworkInterfaces:So,release:Co,type:Eo,cpus:vo,totalmem:To,freemem:Ao,uptime:Po,loadavg:go,hostname:xo,endianness:yo}}}),Rf=$({"node-modules-polyfills-commonjs:os"(l,h){U();var p=(_f(),Nf(mo));if(p&&p.default){h.exports=p.default;for(let d in p)h.exports[d]=p[d]}else p&&(h.exports=p)}}),jf=$({"node_modules/detect-newline/index.js"(l,h){"use strict";U();var p=d=>{if(typeof d!="string")throw new TypeError("Expected a string");let x=d.match(/(?:\r?\n)/g)||[];if(x.length===0)return;let P=x.filter(v=>v===`\r
`).length,m=x.length-P;return P>m?`\r
`:`
`};h.exports=p,h.exports.graceful=d=>typeof d=="string"&&p(d)||`
`}}),qf=$({"node_modules/jest-docblock/build/index.js"(l){"use strict";U(),Object.defineProperty(l,"__esModule",{value:!0}),l.extract=A,l.parse=G,l.parseWithComments=N,l.print=O,l.strip=_;function h(){let R=Rf();return h=function(){return R},R}function p(){let R=d(jf());return p=function(){return R},R}function d(R){return R&&R.__esModule?R:{default:R}}var x=/\*\/$/,P=/^\/\*\*?/,m=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,v=/(^|\s+)\/\/([^\r\n]*)/g,S=/^(\r?\n)+/,k=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,F=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,w=/(\r?\n|^) *\* ?/g,L=[];function A(R){let z=R.match(m);return z?z[0].trimLeft():""}function _(R){let z=R.match(m);return z&&z[0]?R.substring(z[0].length):R}function G(R){return N(R).pragmas}function N(R){let z=(0,p().default)(R)||h().EOL;R=R.replace(P,"").replace(x,"").replace(w,"$1");let Q="";for(;Q!==R;)Q=R,R=R.replace(k,`${z}$1 $2${z}`);R=R.replace(S,"").trimRight();let b=Object.create(null),B=R.replace(F,"").replace(S,"").trimRight(),Z;for(;Z=F.exec(R);){let q=Z[2].replace(v,"");typeof b[Z[1]]=="string"||Array.isArray(b[Z[1]])?b[Z[1]]=L.concat(b[Z[1]],q):b[Z[1]]=q}return{comments:B,pragmas:b}}function O(R){let{comments:z="",pragmas:Q={}}=R,b=(0,p().default)(z)||h().EOL,B="/**",Z=" *",q=" */",ue=Object.keys(Q),te=ue.map(se=>H(se,Q[se])).reduce((se,He)=>se.concat(He),[]).map(se=>`${Z} ${se}${b}`).join("");if(!z){if(ue.length===0)return"";if(ue.length===1&&!Array.isArray(Q[ue[0]])){let se=Q[ue[0]];return`${B} ${H(ue[0],se)[0]}${q}`}}let it=z.split(b).map(se=>`${Z} ${se}`).join(b)+b;return B+b+(z?it:"")+(z&&ue.length?Z+b:"")+te+q}function H(R,z){return L.concat(z).map(Q=>`@${R} ${Q}`.trim())}}}),Uf=$({"src/common/end-of-line.js"(l,h){"use strict";U();function p(m){let v=m.indexOf("\r");return v>=0?m.charAt(v+1)===`
`?"crlf":"cr":"lf"}function d(m){switch(m){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function x(m,v){let S;switch(v){case`
`:S=/\n/g;break;case"\r":S=/\r/g;break;case`\r
`:S=/\r\n/g;break;default:throw new Error(`Unexpected "eol" ${JSON.stringify(v)}.`)}let k=m.match(S);return k?k.length:0}function P(m){return m.replace(/\r\n?/g,`
`)}h.exports={guessEndOfLine:p,convertEndOfLineToChars:d,countEndOfLineChars:x,normalizeEndOfLine:P}}}),$f=$({"src/language-js/pragma.js"(l,h){"use strict";U();var{parseWithComments:p,strip:d,extract:x,print:P}=qf(),{normalizeEndOfLine:m}=Uf(),v=po();function S(w){let L=v(w);L&&(w=w.slice(L.length+1));let A=x(w),{pragmas:_,comments:G}=p(A);return{shebang:L,text:w,pragmas:_,comments:G}}function k(w){let L=Object.keys(S(w).pragmas);return L.includes("prettier")||L.includes("format")}function F(w){let{shebang:L,text:A,pragmas:_,comments:G}=S(w),N=d(A),O=P({pragmas:Object.assign({format:""},_),comments:G.trimStart()});return(L?`${L}
`:"")+m(O)+(N.startsWith(`
`)?`
`:`

`)+N}h.exports={hasPragma:k,insertPragma:F}}}),Io=$({"src/utils/is-non-empty-array.js"(l,h){"use strict";U();function p(d){return Array.isArray(d)&&d.length>0}h.exports=p}}),No=$({"src/language-js/loc.js"(l,h){"use strict";U();var p=Io();function d(S){var k,F;let w=S.range?S.range[0]:S.start,L=(k=(F=S.declaration)===null||F===void 0?void 0:F.decorators)!==null&&k!==void 0?k:S.decorators;return p(L)?Math.min(d(L[0]),w):w}function x(S){return S.range?S.range[1]:S.end}function P(S,k){let F=d(S);return Number.isInteger(F)&&F===d(k)}function m(S,k){let F=x(S);return Number.isInteger(F)&&F===x(k)}function v(S,k){return P(S,k)&&m(S,k)}h.exports={locStart:d,locEnd:x,hasSameLocStart:P,hasSameLoc:v}}}),ko=$({"src/language-js/parse/utils/create-parser.js"(l,h){"use strict";U();var{hasPragma:p}=$f(),{locStart:d,locEnd:x}=No();function P(m){return m=typeof m=="function"?{parse:m}:m,Object.assign({astFormat:"estree",hasPragma:p,locStart:d,locEnd:x},m)}h.exports=P}}),lr=$({"src/common/parser-create-error.js"(l,h){"use strict";U();function p(d,x){let P=new SyntaxError(d+" ("+x.start.line+":"+x.start.column+")");return P.loc=x,P}h.exports=p}}),Do=$({"src/language-js/parse/utils/create-babel-parse-error.js"(l,h){"use strict";U();var p=lr();function d(x){let{message:P,loc:m}=x;return p(P.replace(/ \(.*\)/,""),{start:{line:m?m.line:0,column:m?m.column+1:0}})}h.exports=d}}),Hf=$({"src/language-js/utils/is-ts-keyword-type.js"(l,h){"use strict";U();function p(d){let{type:x}=d;return x.startsWith("TS")&&x.endsWith("Keyword")}h.exports=p}}),zf=$({"src/language-js/utils/is-block-comment.js"(l,h){"use strict";U();var p=new Set(["Block","CommentBlock","MultiLine"]),d=x=>p.has(x==null?void 0:x.type);h.exports=d}}),Vf=$({"src/language-js/utils/is-type-cast-comment.js"(l,h){"use strict";U();var p=zf();function d(x){return p(x)&&x.value[0]==="*"&&/@(?:type|satisfies)\b/.test(x.value)}h.exports=d}}),Kf=$({"src/utils/get-last.js"(l,h){"use strict";U();var p=d=>d[d.length-1];h.exports=p}}),Wf=$({"src/language-js/parse/postprocess/visit-node.js"(l,h){"use strict";U();function p(d,x){if(Array.isArray(d)){for(let P=0;P<d.length;P++)d[P]=p(d[P],x);return d}if(d&&typeof d=="object"&&typeof d.type=="string"){let P=Object.keys(d);for(let m=0;m<P.length;m++)d[P[m]]=p(d[P[m]],x);return x(d)||d}return d}h.exports=p}}),Gf=$({"src/language-js/parse/postprocess/throw-syntax-error.js"(l,h){"use strict";U();var p=lr();function d(x,P){let{start:m,end:v}=x.loc;throw p(P,{start:{line:m.line,column:m.column+1},end:{line:v.line,column:v.column+1}})}h.exports=d}}),Jf=$({"src/language-js/parse/postprocess/index.js"(l,h){"use strict";U();var{locStart:p,locEnd:d}=No(),x=Hf(),P=Vf(),m=Kf(),v=Wf(),S=Gf();function k(A,_){if(_.parser!=="typescript"&&_.parser!=="flow"&&_.parser!=="acorn"&&_.parser!=="espree"&&_.parser!=="meriyah"){let N=new Set;A=v(A,O=>{O.leadingComments&&O.leadingComments.some(P)&&N.add(p(O))}),A=v(A,O=>{if(O.type==="ParenthesizedExpression"){let{expression:H}=O;if(H.type==="TypeCastExpression")return H.range=O.range,H;let R=p(O);if(!N.has(R))return H.extra=Object.assign(Object.assign({},H.extra),{},{parenthesized:!0}),H}})}return A=v(A,N=>{switch(N.type){case"ChainExpression":return F(N.expression);case"LogicalExpression":{if(w(N))return L(N);break}case"VariableDeclaration":{let O=m(N.declarations);O&&O.init&&G(N,O);break}case"TSParenthesizedType":return x(N.typeAnnotation)||N.typeAnnotation.type==="TSThisType"||(N.typeAnnotation.range=[p(N),d(N)]),N.typeAnnotation;case"TSTypeParameter":if(typeof N.name=="string"){let O=p(N);N.name={type:"Identifier",name:N.name,range:[O,O+N.name.length]}}break;case"ObjectExpression":if(_.parser==="typescript"){let O=N.properties.find(H=>H.type==="Property"&&H.value.type==="TSEmptyBodyFunctionExpression");O&&S(O.value,"Unexpected token.")}break;case"SequenceExpression":{let O=m(N.expressions);N.range=[p(N),Math.min(d(O),d(N))];break}case"TopicReference":_.__isUsingHackPipeline=!0;break;case"ExportAllDeclaration":{let{exported:O}=N;if(_.parser==="meriyah"&&O&&O.type==="Identifier"){let H=_.originalText.slice(p(O),d(O));(H.startsWith('"')||H.startsWith("'"))&&(N.exported=Object.assign(Object.assign({},N.exported),{},{type:"Literal",value:N.exported.name,raw:H}))}break}case"PropertyDefinition":if(_.parser==="meriyah"&&N.static&&!N.computed&&!N.key){let O="static",H=p(N);Object.assign(N,{static:!1,key:{type:"Identifier",name:O,range:[H,H+O.length]}})}break}}),A;function G(N,O){_.originalText[d(O)]!==";"&&(N.range=[p(N),d(O)])}}function F(A){switch(A.type){case"CallExpression":A.type="OptionalCallExpression",A.callee=F(A.callee);break;case"MemberExpression":A.type="OptionalMemberExpression",A.object=F(A.object);break;case"TSNonNullExpression":A.expression=F(A.expression);break}return A}function w(A){return A.type==="LogicalExpression"&&A.right.type==="LogicalExpression"&&A.operator===A.right.operator}function L(A){return w(A)?L({type:"LogicalExpression",operator:A.operator,left:L({type:"LogicalExpression",operator:A.operator,left:A.left,right:A.right.left,range:[p(A.left),d(A.right.left)]}),right:A.right.right,range:[p(A),d(A)]}):A}h.exports=k}}),Fo=$({"node_modules/@babel/parser/lib/index.js"(l){"use strict";U(),Object.defineProperty(l,"__esModule",{value:!0});var h={sourceType:"script",sourceFilename:void 0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowNewTargetOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0,annexB:!0};function p(t){if(t&&t.annexB!=null&&t.annexB!==!1)throw new Error("The `annexB` option can only be set to `false`.");let r={};for(let e of Object.keys(h))r[e]=t&&t[e]!=null?t[e]:h[e];return r}var d=class{constructor(t,r){this.token=void 0,this.preserveSpace=void 0,this.token=t,this.preserveSpace=!!r}},x={brace:new d("{"),j_oTag:new d("<tag"),j_cTag:new d("</tag"),j_expr:new d("<tag>...</tag>",!0)};x.template=new d("`",!0);var P=!0,m=!0,v=!0,S=!0,k=!0,F=!0,w=class{constructor(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=t,this.keyword=r.keyword,this.beforeExpr=!!r.beforeExpr,this.startsExpr=!!r.startsExpr,this.rightAssociative=!!r.rightAssociative,this.isLoop=!!r.isLoop,this.isAssign=!!r.isAssign,this.prefix=!!r.prefix,this.postfix=!!r.postfix,this.binop=r.binop!=null?r.binop:null,this.updateContext=null}},L=new Map;function A(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};r.keyword=t;let e=b(t,r);return L.set(t,e),e}function _(t,r){return b(t,{beforeExpr:P,binop:r})}var G=-1,N=[],O=[],H=[],R=[],z=[],Q=[];function b(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};var e,s,i,a;return++G,O.push(t),H.push((e=r.binop)!=null?e:-1),R.push((s=r.beforeExpr)!=null?s:!1),z.push((i=r.startsExpr)!=null?i:!1),Q.push((a=r.prefix)!=null?a:!1),N.push(new w(t,r)),G}function B(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};var e,s,i,a;return++G,L.set(t,G),O.push(t),H.push((e=r.binop)!=null?e:-1),R.push((s=r.beforeExpr)!=null?s:!1),z.push((i=r.startsExpr)!=null?i:!1),Q.push((a=r.prefix)!=null?a:!1),N.push(new w("name",r)),G}var Z={bracketL:b("[",{beforeExpr:P,startsExpr:m}),bracketHashL:b("#[",{beforeExpr:P,startsExpr:m}),bracketBarL:b("[|",{beforeExpr:P,startsExpr:m}),bracketR:b("]"),bracketBarR:b("|]"),braceL:b("{",{beforeExpr:P,startsExpr:m}),braceBarL:b("{|",{beforeExpr:P,startsExpr:m}),braceHashL:b("#{",{beforeExpr:P,startsExpr:m}),braceR:b("}"),braceBarR:b("|}"),parenL:b("(",{beforeExpr:P,startsExpr:m}),parenR:b(")"),comma:b(",",{beforeExpr:P}),semi:b(";",{beforeExpr:P}),colon:b(":",{beforeExpr:P}),doubleColon:b("::",{beforeExpr:P}),dot:b("."),question:b("?",{beforeExpr:P}),questionDot:b("?."),arrow:b("=>",{beforeExpr:P}),template:b("template"),ellipsis:b("...",{beforeExpr:P}),backQuote:b("`",{startsExpr:m}),dollarBraceL:b("${",{beforeExpr:P,startsExpr:m}),templateTail:b("...`",{startsExpr:m}),templateNonTail:b("...${",{beforeExpr:P,startsExpr:m}),at:b("@"),hash:b("#",{startsExpr:m}),interpreterDirective:b("#!..."),eq:b("=",{beforeExpr:P,isAssign:S}),assign:b("_=",{beforeExpr:P,isAssign:S}),slashAssign:b("_=",{beforeExpr:P,isAssign:S}),xorAssign:b("_=",{beforeExpr:P,isAssign:S}),moduloAssign:b("_=",{beforeExpr:P,isAssign:S}),incDec:b("++/--",{prefix:k,postfix:F,startsExpr:m}),bang:b("!",{beforeExpr:P,prefix:k,startsExpr:m}),tilde:b("~",{beforeExpr:P,prefix:k,startsExpr:m}),doubleCaret:b("^^",{startsExpr:m}),doubleAt:b("@@",{startsExpr:m}),pipeline:_("|>",0),nullishCoalescing:_("??",1),logicalOR:_("||",1),logicalAND:_("&&",2),bitwiseOR:_("|",3),bitwiseXOR:_("^",4),bitwiseAND:_("&",5),equality:_("==/!=/===/!==",6),lt:_("</>/<=/>=",7),gt:_("</>/<=/>=",7),relational:_("</>/<=/>=",7),bitShift:_("<</>>/>>>",8),bitShiftL:_("<</>>/>>>",8),bitShiftR:_("<</>>/>>>",8),plusMin:b("+/-",{beforeExpr:P,binop:9,prefix:k,startsExpr:m}),modulo:b("%",{binop:10,startsExpr:m}),star:b("*",{binop:10}),slash:_("/",10),exponent:b("**",{beforeExpr:P,binop:11,rightAssociative:!0}),_in:A("in",{beforeExpr:P,binop:7}),_instanceof:A("instanceof",{beforeExpr:P,binop:7}),_break:A("break"),_case:A("case",{beforeExpr:P}),_catch:A("catch"),_continue:A("continue"),_debugger:A("debugger"),_default:A("default",{beforeExpr:P}),_else:A("else",{beforeExpr:P}),_finally:A("finally"),_function:A("function",{startsExpr:m}),_if:A("if"),_return:A("return",{beforeExpr:P}),_switch:A("switch"),_throw:A("throw",{beforeExpr:P,prefix:k,startsExpr:m}),_try:A("try"),_var:A("var"),_const:A("const"),_with:A("with"),_new:A("new",{beforeExpr:P,startsExpr:m}),_this:A("this",{startsExpr:m}),_super:A("super",{startsExpr:m}),_class:A("class",{startsExpr:m}),_extends:A("extends",{beforeExpr:P}),_export:A("export"),_import:A("import",{startsExpr:m}),_null:A("null",{startsExpr:m}),_true:A("true",{startsExpr:m}),_false:A("false",{startsExpr:m}),_typeof:A("typeof",{beforeExpr:P,prefix:k,startsExpr:m}),_void:A("void",{beforeExpr:P,prefix:k,startsExpr:m}),_delete:A("delete",{beforeExpr:P,prefix:k,startsExpr:m}),_do:A("do",{isLoop:v,beforeExpr:P}),_for:A("for",{isLoop:v}),_while:A("while",{isLoop:v}),_as:B("as",{startsExpr:m}),_assert:B("assert",{startsExpr:m}),_async:B("async",{startsExpr:m}),_await:B("await",{startsExpr:m}),_from:B("from",{startsExpr:m}),_get:B("get",{startsExpr:m}),_let:B("let",{startsExpr:m}),_meta:B("meta",{startsExpr:m}),_of:B("of",{startsExpr:m}),_sent:B("sent",{startsExpr:m}),_set:B("set",{startsExpr:m}),_static:B("static",{startsExpr:m}),_using:B("using",{startsExpr:m}),_yield:B("yield",{startsExpr:m}),_asserts:B("asserts",{startsExpr:m}),_checks:B("checks",{startsExpr:m}),_exports:B("exports",{startsExpr:m}),_global:B("global",{startsExpr:m}),_implements:B("implements",{startsExpr:m}),_intrinsic:B("intrinsic",{startsExpr:m}),_infer:B("infer",{startsExpr:m}),_is:B("is",{startsExpr:m}),_mixins:B("mixins",{startsExpr:m}),_proto:B("proto",{startsExpr:m}),_require:B("require",{startsExpr:m}),_satisfies:B("satisfies",{startsExpr:m}),_keyof:B("keyof",{startsExpr:m}),_readonly:B("readonly",{startsExpr:m}),_unique:B("unique",{startsExpr:m}),_abstract:B("abstract",{startsExpr:m}),_declare:B("declare",{startsExpr:m}),_enum:B("enum",{startsExpr:m}),_module:B("module",{startsExpr:m}),_namespace:B("namespace",{startsExpr:m}),_interface:B("interface",{startsExpr:m}),_type:B("type",{startsExpr:m}),_opaque:B("opaque",{startsExpr:m}),name:b("name",{startsExpr:m}),string:b("string",{startsExpr:m}),num:b("num",{startsExpr:m}),bigint:b("bigint",{startsExpr:m}),decimal:b("decimal",{startsExpr:m}),regexp:b("regexp",{startsExpr:m}),privateName:b("#name",{startsExpr:m}),eof:b("eof"),jsxName:b("jsxName"),jsxText:b("jsxText",{beforeExpr:!0}),jsxTagStart:b("jsxTagStart",{startsExpr:!0}),jsxTagEnd:b("jsxTagEnd"),placeholder:b("%%",{startsExpr:!0})};function q(t){return t>=93&&t<=130}function ue(t){return t<=92}function te(t){return t>=58&&t<=130}function it(t){return t>=58&&t<=134}function se(t){return R[t]}function He(t){return z[t]}function Bo(t){return t>=29&&t<=33}function hr(t){return t>=127&&t<=129}function Mo(t){return t>=90&&t<=92}function $t(t){return t>=58&&t<=92}function _o(t){return t>=39&&t<=59}function Ro(t){return t===34}function jo(t){return Q[t]}function qo(t){return t>=119&&t<=121}function Uo(t){return t>=122&&t<=128}function xe(t){return O[t]}function at(t){return H[t]}function $o(t){return t===57}function nt(t){return t>=24&&t<=25}function ce(t){return N[t]}N[8].updateContext=t=>{t.pop()},N[5].updateContext=N[7].updateContext=N[23].updateContext=t=>{t.push(x.brace)},N[22].updateContext=t=>{t[t.length-1]===x.template?t.pop():t.push(x.template)},N[140].updateContext=t=>{t.push(x.j_expr,x.j_oTag)};function ot(t,r){if(t==null)return{};var e={},s=Object.keys(t),i,a;for(a=0;a<s.length;a++)i=s[a],!(r.indexOf(i)>=0)&&(e[i]=t[i]);return e}var ge=class{constructor(t,r,e){this.line=void 0,this.column=void 0,this.index=void 0,this.line=t,this.column=r,this.index=e}},lt=class{constructor(t,r){this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=t,this.end=r}};function Y(t,r){let{line:e,column:s,index:i}=t;return new ge(e,s+r,i+r)}var Ht={SyntaxError:"BABEL_PARSER_SYNTAX_ERROR",SourceTypeModuleError:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"},Ho=function(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.length-1;return{get(){return t.reduce((e,s)=>e[s],this)},set(e){t.reduce((s,i,a)=>a===r?s[i]=e:s[i],this)}}},zo=(t,r,e)=>Object.keys(e).map(s=>[s,e[s]]).filter(s=>{let[,i]=s;return!!i}).map(s=>{let[i,a]=s;return[i,typeof a=="function"?{value:a,enumerable:!1}:typeof a.reflect=="string"?Object.assign({},a,Ho(a.reflect.split("."))):a]}).reduce((s,i)=>{let[a,n]=i;return Object.defineProperty(s,a,Object.assign({configurable:!0},n))},Object.assign(new t,r)),Vo={ImportMetaOutsideModule:{message:`import.meta may appear only with 'sourceType: "module"'`,code:Ht.SourceTypeModuleError},ImportOutsideModule:{message:`'import' and 'export' may appear only with 'sourceType: "module"'`,code:Ht.SourceTypeModuleError}},ur={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},zt=t=>{let{type:r,prefix:e}=t;return r==="UpdateExpression"?ur.UpdateExpression[String(e)]:ur[r]},Ko={AccessorIsGenerator:t=>{let{kind:r}=t;return`A ${r}ter cannot be a generator.`},ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitInUsingBinding:"'await' is not allowed to be used as a name in 'using' declarations.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncFunction:"'await' is only allowed within async functions.",BadGetterArity:"A 'get' accessor must not have any formal parameters.",BadSetterArity:"A 'set' accessor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accessor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:t=>{let{kind:r}=t;return`Missing initializer in ${r} declaration.`},DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.",DecoratorsBeforeAfterExport:"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:t=>{let{exportName:r}=t;return`\`${r}\` has already been exported. Exported identifiers must be unique.`},DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:t=>{let{localName:r,exportName:e}=t;return`A string literal cannot be used as an exported binding without \`from\`.
- Did you mean \`export { '${r}' as '${e}' } from 'some-module'\`?`},ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:t=>{let{type:r}=t;return`'${r==="ForInStatement"?"for-in":"for-of"}' loop variable declaration may not have an initializer.`},ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:t=>{let{type:r}=t;return`Unsyntactic ${r==="BreakStatement"?"break":"continue"}.`},IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportBindingIsString:t=>{let{importName:r}=t;return`A string literal cannot be used as an imported binding.
- Did you mean \`import { "${r}" as foo }\`?`},ImportCallArgumentTrailingComma:"Trailing comma is disallowed inside import(...) arguments.",ImportCallArity:t=>{let{maxArgumentCount:r}=t;return`\`import()\` requires exactly ${r===1?"one argument":"one or two arguments"}.`},ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:t=>{let{radix:r}=t;return`Expected number in radix ${r}.`},InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:t=>{let{reservedWord:r}=t;return`Escape sequence in keyword ${r}.`},InvalidIdentifier:t=>{let{identifierName:r}=t;return`Invalid identifier ${r}.`},InvalidLhs:t=>{let{ancestor:r}=t;return`Invalid left-hand side in ${zt(r)}.`},InvalidLhsBinding:t=>{let{ancestor:r}=t;return`Binding invalid left-hand side in ${zt(r)}.`},InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:t=>{let{unexpected:r}=t;return`Unexpected character '${r}'.`},InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:t=>{let{identifierName:r}=t;return`Private name #${r} is not defined.`},InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:t=>{let{labelName:r}=t;return`Label '${r}' is already declared.`},LetInLexicalBinding:"'let' is not allowed to be used as a name in 'let' or 'const' declarations.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:t=>{let{missingPlugin:r}=t;return`This experimental syntax requires enabling the parser plugin: ${r.map(e=>JSON.stringify(e)).join(", ")}.`},MissingOneOfPlugins:t=>{let{missingPlugin:r}=t;return`This experimental syntax requires enabling one of the following parser plugin(s): ${r.map(e=>JSON.stringify(e)).join(", ")}.`},MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:t=>{let{key:r}=t;return`Duplicate key "${r}" is not allowed in module attributes.`},ModuleExportNameHasLoneSurrogate:t=>{let{surrogateCharCode:r}=t;return`An export name cannot include a lone surrogate, found '\\u${r.toString(16)}'.`},ModuleExportUndefined:t=>{let{localName:r}=t;return`Export '${r}' is not defined.`},MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:t=>{let{identifierName:r}=t;return`Private names are only allowed in property accesses (\`obj.#${r}\`) or in \`in\` expressions (\`#${r} in obj\`).`},PrivateNameRedeclaration:t=>{let{identifierName:r}=t;return`Duplicate private name #${r}.`},RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level or inside a block.",SloppyFunctionAnnexB:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:t=>{let{keyword:r}=t;return`Unexpected keyword '${r}'.`},UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:t=>{let{reservedWord:r}=t;return`Unexpected reserved word '${r}'.`},UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:t=>{let{expected:r,unexpected:e}=t;return`Unexpected token${e?` '${e}'.`:""}${r?`, expected "${r}"`:""}`},UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script`.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:t=>{let{target:r,onlyValidPropertyName:e}=t;return`The only valid meta property for ${r} is ${r}.${e}.`},UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:t=>{let{identifierName:r}=t;return`Identifier '${r}' has already been declared.`},YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},Wo={StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:t=>{let{referenceName:r}=t;return`Assigning to '${r}' in strict mode.`},StrictEvalArgumentsBinding:t=>{let{bindingName:r}=t;return`Binding '${r}' in strict mode.`},StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."},Go=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]),Jo={PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:t=>{let{token:r}=t;return`Invalid topic token ${r}. In order to use ${r} as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "${r}" }.`},PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:t=>{let{type:r}=t;return`Hack-style pipe body cannot be an unparenthesized ${zt({type:r})}; please wrap it in parentheses.`},PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'},Xo=["toMessage"],Yo=["message"];function Qo(t){let{toMessage:r}=t,e=ot(t,Xo);return function s(i){let{loc:a,details:n}=i;return zo(SyntaxError,Object.assign({},e,{loc:a}),{clone(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=o.loc||{};return s({loc:new ge("line"in u?u.line:this.loc.line,"column"in u?u.column:this.loc.column,"index"in u?u.index:this.loc.index),details:Object.assign({},this.details,o.details)})},details:{value:n,enumerable:!1},message:{get(){return`${r(this.details)} (${this.loc.line}:${this.loc.column})`},set(o){Object.defineProperty(this,"message",{value:o})}},pos:{reflect:"loc.index",enumerable:!0},missingPlugin:"missingPlugin"in n&&{reflect:"details.missingPlugin",enumerable:!0}})}}function pe(t,r){if(Array.isArray(t))return s=>pe(s,t[0]);let e={};for(let s of Object.keys(t)){let i=t[s],a=typeof i=="string"?{message:()=>i}:typeof i=="function"?{message:i}:i,{message:n}=a,o=ot(a,Yo),u=typeof n=="string"?()=>n:n;e[s]=Qo(Object.assign({code:Ht.SyntaxError,reasonCode:s,toMessage:u},r?{syntaxPlugin:r}:{},o))}return e}var f=Object.assign({},pe(Vo),pe(Ko),pe(Wo),pe`pipelineOperator`(Jo)),{defineProperty:Zo}=Object,cr=(t,r)=>Zo(t,r,{enumerable:!1,value:t[r]});function ze(t){return t.loc.start&&cr(t.loc.start,"index"),t.loc.end&&cr(t.loc.end,"index"),t}var el=t=>class extends t{parse(){let e=ze(super.parse());return this.options.tokens&&(e.tokens=e.tokens.map(ze)),e}parseRegExpLiteral(e){let{pattern:s,flags:i}=e,a=null;try{a=new RegExp(s,i)}catch{}let n=this.estreeParseLiteral(a);return n.regex={pattern:s,flags:i},n}parseBigIntLiteral(e){let s;try{s=BigInt(e)}catch{s=null}let i=this.estreeParseLiteral(s);return i.bigint=String(i.value||e),i}parseDecimalLiteral(e){let i=this.estreeParseLiteral(null);return i.decimal=String(i.value||e),i}estreeParseLiteral(e){return this.parseLiteral(e,"Literal")}parseStringLiteral(e){return this.estreeParseLiteral(e)}parseNumericLiteral(e){return this.estreeParseLiteral(e)}parseNullLiteral(){return this.estreeParseLiteral(null)}parseBooleanLiteral(e){return this.estreeParseLiteral(e)}directiveToStmt(e){let s=e.value;delete e.value,s.type="Literal",s.raw=s.extra.raw,s.value=s.extra.expressionValue;let i=e;return i.type="ExpressionStatement",i.expression=s,i.directive=s.extra.rawValue,delete s.extra,i}initFunction(e,s){super.initFunction(e,s),e.expression=!1}checkDeclaration(e){e!=null&&this.isObjectProperty(e)?this.checkDeclaration(e.value):super.checkDeclaration(e)}getObjectOrClassMethodParams(e){return e.value.params}isValidDirective(e){var s;return e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value=="string"&&!((s=e.expression.extra)!=null&&s.parenthesized)}parseBlockBody(e,s,i,a,n){super.parseBlockBody(e,s,i,a,n);let o=e.directives.map(u=>this.directiveToStmt(u));e.body=o.concat(e.body),delete e.directives}pushClassMethod(e,s,i,a,n,o){this.parseMethod(s,i,a,n,o,"ClassMethod",!0),s.typeParameters&&(s.value.typeParameters=s.typeParameters,delete s.typeParameters),e.body.push(s)}parsePrivateName(){let e=super.parsePrivateName();return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(e):e}convertPrivateNameToPrivateIdentifier(e){let s=super.getPrivateNameSV(e);return e=e,delete e.id,e.name=s,e.type="PrivateIdentifier",e}isPrivateName(e){return this.getPluginOption("estree","classFeatures")?e.type==="PrivateIdentifier":super.isPrivateName(e)}getPrivateNameSV(e){return this.getPluginOption("estree","classFeatures")?e.name:super.getPrivateNameSV(e)}parseLiteral(e,s){let i=super.parseLiteral(e,s);return i.raw=i.extra.raw,delete i.extra,i}parseFunctionBody(e,s){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;super.parseFunctionBody(e,s,i),e.expression=e.body.type!=="BlockStatement"}parseMethod(e,s,i,a,n,o){let u=arguments.length>6&&arguments[6]!==void 0?arguments[6]:!1,c=this.startNode();return c.kind=e.kind,c=super.parseMethod(c,s,i,a,n,o,u),c.type="FunctionExpression",delete c.kind,e.value=c,o==="ClassPrivateMethod"&&(e.computed=!1),this.finishNode(e,"MethodDefinition")}parseClassProperty(){let e=super.parseClassProperty(...arguments);return this.getPluginOption("estree","classFeatures")&&(e.type="PropertyDefinition"),e}parseClassPrivateProperty(){let e=super.parseClassPrivateProperty(...arguments);return this.getPluginOption("estree","classFeatures")&&(e.type="PropertyDefinition",e.computed=!1),e}parseObjectMethod(e,s,i,a,n){let o=super.parseObjectMethod(e,s,i,a,n);return o&&(o.type="Property",o.kind==="method"&&(o.kind="init"),o.shorthand=!1),o}parseObjectProperty(e,s,i,a){let n=super.parseObjectProperty(e,s,i,a);return n&&(n.kind="init",n.type="Property"),n}isValidLVal(e,s,i){return e==="Property"?"value":super.isValidLVal(e,s,i)}isAssignable(e,s){return e!=null&&this.isObjectProperty(e)?this.isAssignable(e.value,s):super.isAssignable(e,s)}toAssignable(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(e!=null&&this.isObjectProperty(e)){let{key:i,value:a}=e;this.isPrivateName(i)&&this.classScope.usePrivateName(this.getPrivateNameSV(i),i.loc.start),this.toAssignable(a,s)}else super.toAssignable(e,s)}toAssignableObjectExpressionProp(e,s,i){e.kind==="get"||e.kind==="set"?this.raise(f.PatternHasAccessor,{at:e.key}):e.method?this.raise(f.PatternHasMethod,{at:e.key}):super.toAssignableObjectExpressionProp(e,s,i)}finishCallExpression(e,s){let i=super.finishCallExpression(e,s);if(i.callee.type==="Import"){if(i.type="ImportExpression",i.source=i.arguments[0],this.hasPlugin("importAssertions")){var a;i.attributes=(a=i.arguments[1])!=null?a:null}delete i.arguments,delete i.callee}return i}toReferencedArguments(e){e.type!=="ImportExpression"&&super.toReferencedArguments(e)}parseExport(e,s){let i=this.state.lastTokStartLoc,a=super.parseExport(e,s);switch(a.type){case"ExportAllDeclaration":a.exported=null;break;case"ExportNamedDeclaration":a.specifiers.length===1&&a.specifiers[0].type==="ExportNamespaceSpecifier"&&(a.type="ExportAllDeclaration",a.exported=a.specifiers[0].exported,delete a.specifiers);case"ExportDefaultDeclaration":{var n;let{declaration:o}=a;(o==null?void 0:o.type)==="ClassDeclaration"&&((n=o.decorators)==null?void 0:n.length)>0&&o.start===a.start&&this.resetStartLocation(a,i)}break}return a}parseSubscript(e,s,i,a){let n=super.parseSubscript(e,s,i,a);if(a.optionalChainMember){if((n.type==="OptionalMemberExpression"||n.type==="OptionalCallExpression")&&(n.type=n.type.substring(8)),a.stop){let o=this.startNodeAtNode(n);return o.expression=n,this.finishNode(o,"ChainExpression")}}else(n.type==="MemberExpression"||n.type==="CallExpression")&&(n.optional=!1);return n}hasPropertyAsPrivateName(e){return e.type==="ChainExpression"&&(e=e.expression),super.hasPropertyAsPrivateName(e)}isObjectProperty(e){return e.type==="Property"&&e.kind==="init"&&!e.method}isObjectMethod(e){return e.method||e.kind==="get"||e.kind==="set"}finishNodeAt(e,s,i){return ze(super.finishNodeAt(e,s,i))}resetStartLocation(e,s){super.resetStartLocation(e,s),ze(e)}resetEndLocation(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state.lastTokEndLoc;super.resetEndLocation(e,s),ze(e)}},Vt="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",pr="\u200C\u200D\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F",tl=new RegExp("["+Vt+"]"),sl=new RegExp("["+Vt+pr+"]");Vt=pr=null;var fr=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,4026,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,757,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,3104,541,1507,4938,6,4191],rl=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,81,2,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,9,5351,0,7,14,13835,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,983,6,110,6,6,9,4759,9,787719,239];function Kt(t,r){let e=65536;for(let s=0,i=r.length;s<i;s+=2){if(e+=r[s],e>t)return!1;if(e+=r[s+1],e>=t)return!0}return!1}function fe(t){return t<65?t===36:t<=90?!0:t<97?t===95:t<=122?!0:t<=65535?t>=170&&tl.test(String.fromCharCode(t)):Kt(t,fr)}function De(t){return t<48?t===36:t<58?!0:t<65?!1:t<=90?!0:t<97?t===95:t<=122?!0:t<=65535?t>=170&&sl.test(String.fromCharCode(t)):Kt(t,fr)||Kt(t,rl)}var Wt={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},il=new Set(Wt.keyword),al=new Set(Wt.strict),nl=new Set(Wt.strictBind);function dr(t,r){return r&&t==="await"||t==="enum"}function mr(t,r){return dr(t,r)||al.has(t)}function yr(t){return nl.has(t)}function xr(t,r){return mr(t,r)||yr(t)}function ol(t){return il.has(t)}function ll(t,r,e){return t===64&&r===64&&fe(e)}var hl=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);function ul(t){return hl.has(t)}var Fe=0,Le=1,de=2,Gt=4,gr=8,ht=16,Pr=32,Ee=64,ut=128,Oe=256,ct=Le|de|ut|Oe,le=1,Ce=2,Ar=4,be=8,pt=16,Tr=64,ft=128,Jt=256,Xt=512,Yt=1024,Qt=2048,Ve=4096,dt=8192,vr=le|Ce|be|ft|dt,Be=le|0|be|dt,cl=le|0|be|0,mt=le|0|Ar|0,Er=le|0|pt|0,pl=0|Ce|0|ft,fl=0|Ce|0|0,Cr=le|Ce|be|Jt|dt,br=0|Yt,Pe=0|Tr,dl=le|0|0|Tr,ml=Cr|Xt,yl=0|Yt,Sr=0|Ce|0|Ve,xl=Qt,yt=4,Zt=2,es=1,ts=Zt|es,gl=Zt|yt,Pl=es|yt,Al=Zt,Tl=es,ss=0,rs=class{constructor(t){this.var=new Set,this.lexical=new Set,this.functions=new Set,this.flags=t}},is=class{constructor(t,r){this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=t,this.inModule=r}get inTopLevel(){return(this.currentScope().flags&Le)>0}get inFunction(){return(this.currentVarScopeFlags()&de)>0}get allowSuper(){return(this.currentThisScopeFlags()&ht)>0}get allowDirectSuper(){return(this.currentThisScopeFlags()&Pr)>0}get inClass(){return(this.currentThisScopeFlags()&Ee)>0}get inClassAndNotInNonArrowFunction(){let t=this.currentThisScopeFlags();return(t&Ee)>0&&(t&de)===0}get inStaticBlock(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&ut)return!0;if(r&(ct|Ee))return!1}}get inNonArrowFunction(){return(this.currentThisScopeFlags()&de)>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(t){return new rs(t)}enter(t){this.scopeStack.push(this.createScope(t))}exit(){return this.scopeStack.pop().flags}treatFunctionsAsVarInScope(t){return!!(t.flags&(de|ut)||!this.parser.inModule&&t.flags&Le)}declareName(t,r,e){let s=this.currentScope();if(r&be||r&pt)this.checkRedeclarationInScope(s,t,r,e),r&pt?s.functions.add(t):s.lexical.add(t),r&be&&this.maybeExportDefined(s,t);else if(r&Ar)for(let i=this.scopeStack.length-1;i>=0&&(s=this.scopeStack[i],this.checkRedeclarationInScope(s,t,r,e),s.var.add(t),this.maybeExportDefined(s,t),!(s.flags&ct));--i);this.parser.inModule&&s.flags&Le&&this.undefinedExports.delete(t)}maybeExportDefined(t,r){this.parser.inModule&&t.flags&Le&&this.undefinedExports.delete(r)}checkRedeclarationInScope(t,r,e,s){this.isRedeclaredInScope(t,r,e)&&this.parser.raise(f.VarRedeclaration,{at:s,identifierName:r})}isRedeclaredInScope(t,r,e){return e&le?e&be?t.lexical.has(r)||t.functions.has(r)||t.var.has(r):e&pt?t.lexical.has(r)||!this.treatFunctionsAsVarInScope(t)&&t.var.has(r):t.lexical.has(r)&&!(t.flags&gr&&t.lexical.values().next().value===r)||!this.treatFunctionsAsVarInScope(t)&&t.functions.has(r):!1}checkLocalExport(t){let{name:r}=t,e=this.scopeStack[0];!e.lexical.has(r)&&!e.var.has(r)&&!e.functions.has(r)&&this.undefinedExports.set(r,t.loc.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScopeFlags(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&ct)return r}}currentThisScopeFlags(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&(ct|Ee)&&!(r&Gt))return r}}},vl=class extends rs{constructor(){super(...arguments),this.declareFunctions=new Set}},El=class extends is{createScope(t){return new vl(t)}declareName(t,r,e){let s=this.currentScope();if(r&Qt){this.checkRedeclarationInScope(s,t,r,e),this.maybeExportDefined(s,t),s.declareFunctions.add(t);return}super.declareName(t,r,e)}isRedeclaredInScope(t,r,e){return super.isRedeclaredInScope(t,r,e)?!0:e&Qt?!t.declareFunctions.has(r)&&(t.lexical.has(r)||t.functions.has(r)):!1}checkLocalExport(t){this.scopeStack[0].declareFunctions.has(t.name)||super.checkLocalExport(t)}},Cl=class{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}hasPlugin(t){if(typeof t=="string")return this.plugins.has(t);{let[r,e]=t;if(!this.hasPlugin(r))return!1;let s=this.plugins.get(r);for(let i of Object.keys(e))if((s==null?void 0:s[i])!==e[i])return!1;return!0}}getPluginOption(t,r){var e;return(e=this.plugins.get(t))==null?void 0:e[r]}};function wr(t,r){t.trailingComments===void 0?t.trailingComments=r:t.trailingComments.unshift(...r)}function bl(t,r){t.leadingComments===void 0?t.leadingComments=r:t.leadingComments.unshift(...r)}function Ke(t,r){t.innerComments===void 0?t.innerComments=r:t.innerComments.unshift(...r)}function We(t,r,e){let s=null,i=r.length;for(;s===null&&i>0;)s=r[--i];s===null||s.start>e.start?Ke(t,e.comments):wr(s,e.comments)}var Sl=class extends Cl{addComment(t){this.filename&&(t.loc.filename=this.filename),this.state.comments.push(t)}processComment(t){let{commentStack:r}=this.state,e=r.length;if(e===0)return;let s=e-1,i=r[s];i.start===t.end&&(i.leadingNode=t,s--);let{start:a}=t;for(;s>=0;s--){let n=r[s],o=n.end;if(o>a)n.containingNode=t,this.finalizeComment(n),r.splice(s,1);else{o===a&&(n.trailingNode=t);break}}}finalizeComment(t){let{comments:r}=t;if(t.leadingNode!==null||t.trailingNode!==null)t.leadingNode!==null&&wr(t.leadingNode,r),t.trailingNode!==null&&bl(t.trailingNode,r);else{let{containingNode:e,start:s}=t;if(this.input.charCodeAt(s-1)===44)switch(e.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":We(e,e.properties,t);break;case"CallExpression":case"OptionalCallExpression":We(e,e.arguments,t);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":We(e,e.params,t);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":We(e,e.elements,t);break;case"ExportNamedDeclaration":case"ImportDeclaration":We(e,e.specifiers,t);break;default:Ke(e,r)}else Ke(e,r)}}finalizeRemainingComments(){let{commentStack:t}=this.state;for(let r=t.length-1;r>=0;r--)this.finalizeComment(t[r]);this.state.commentStack=[]}resetPreviousNodeTrailingComments(t){let{commentStack:r}=this.state,{length:e}=r;if(e===0)return;let s=r[e-1];s.leadingNode===t&&(s.leadingNode=null)}takeSurroundingComments(t,r,e){let{commentStack:s}=this.state,i=s.length;if(i===0)return;let a=i-1;for(;a>=0;a--){let n=s[a],o=n.end;if(n.start===e)n.leadingNode=t;else if(o===r)n.trailingNode=t;else if(o<r)break}}},as=/\r\n?|[\n\u2028\u2029]/,xt=new RegExp(as.source,"g");function Ge(t){switch(t){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}var ns=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,wl=/(?:[^\S\n\r\u2028\u2029]|\/\/.*|\/\*.*?\*\/)*/y,Ir=new RegExp("(?=("+wl.source+"))\\1"+/(?=[\n\r\u2028\u2029]|\/\*(?!.*?\*\/)|$)/.source,"y");function Il(t){switch(t){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}var Nr=class{constructor(){this.strict=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.maybeInArrowParameters=!1,this.inType=!1,this.noAnonFunctionType=!1,this.hasFlowComment=!1,this.isAmbientContext=!1,this.inAbstractClass=!1,this.inDisallowConditionalTypesContext=!1,this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.soloAwait=!1,this.inFSharpPipelineDirectBody=!1,this.labels=[],this.comments=[],this.commentStack=[],this.pos=0,this.type=137,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.lastTokStart=0,this.context=[x.brace],this.canStartJSXElement=!0,this.containsEsc=!1,this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}init(t){let{strictMode:r,sourceType:e,startLine:s,startColumn:i}=t;this.strict=r===!1?!1:r===!0?!0:e==="module",this.curLine=s,this.lineStart=-i,this.startLoc=this.endLoc=new ge(s,i,0)}curPosition(){return new ge(this.curLine,this.pos-this.lineStart,this.pos)}clone(t){let r=new Nr,e=Object.keys(this);for(let s=0,i=e.length;s<i;s++){let a=e[s],n=this[a];!t&&Array.isArray(n)&&(n=n.slice()),r[a]=n}return r}},Nl=function(r){return r>=48&&r<=57},kr={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},gt={bin:t=>t===48||t===49,oct:t=>t>=48&&t<=55,dec:t=>t>=48&&t<=57,hex:t=>t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102};function Dr(t,r,e,s,i,a){let n=e,o=s,u=i,c="",y=null,g=e,{length:T}=r;for(;;){if(e>=T){a.unterminated(n,o,u),c+=r.slice(g,e);break}let C=r.charCodeAt(e);if(kl(t,C,r,e)){c+=r.slice(g,e);break}if(C===92){c+=r.slice(g,e);let M=Dl(r,e,s,i,t==="template",a);M.ch===null&&!y?y={pos:e,lineStart:s,curLine:i}:c+=M.ch,{pos:e,lineStart:s,curLine:i}=M,g=e}else C===8232||C===8233?(++e,++i,s=e):C===10||C===13?t==="template"?(c+=r.slice(g,e)+`
`,++e,C===13&&r.charCodeAt(e)===10&&++e,++i,g=s=e):a.unterminated(n,o,u):++e}return{pos:e,str:c,firstInvalidLoc:y,lineStart:s,curLine:i,containsInvalid:!!y}}function kl(t,r,e,s){return t==="template"?r===96||r===36&&e.charCodeAt(s+1)===123:r===(t==="double"?34:39)}function Dl(t,r,e,s,i,a){let n=!i;r++;let o=c=>({pos:r,ch:c,lineStart:e,curLine:s}),u=t.charCodeAt(r++);switch(u){case 110:return o(`
`);case 114:return o("\r");case 120:{let c;return{code:c,pos:r}=os(t,r,e,s,2,!1,n,a),o(c===null?null:String.fromCharCode(c))}case 117:{let c;return{code:c,pos:r}=Lr(t,r,e,s,n,a),o(c===null?null:String.fromCodePoint(c))}case 116:return o("	");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:t.charCodeAt(r)===10&&++r;case 10:e=r,++s;case 8232:case 8233:return o("");case 56:case 57:if(i)return o(null);a.strictNumericEscape(r-1,e,s);default:if(u>=48&&u<=55){let c=r-1,g=t.slice(c,r+2).match(/^[0-7]+/)[0],T=parseInt(g,8);T>255&&(g=g.slice(0,-1),T=parseInt(g,8)),r+=g.length-1;let C=t.charCodeAt(r);if(g!=="0"||C===56||C===57){if(i)return o(null);a.strictNumericEscape(c,e,s)}return o(String.fromCharCode(T))}return o(String.fromCharCode(u))}}function os(t,r,e,s,i,a,n,o){let u=r,c;return{n:c,pos:r}=Fr(t,r,e,s,16,i,a,!1,o,!n),c===null&&(n?o.invalidEscapeSequence(u,e,s):r=u-1),{code:c,pos:r}}function Fr(t,r,e,s,i,a,n,o,u,c){let y=r,g=i===16?kr.hex:kr.decBinOct,T=i===16?gt.hex:i===10?gt.dec:i===8?gt.oct:gt.bin,C=!1,M=0;for(let j=0,K=a==null?1/0:a;j<K;++j){let W=t.charCodeAt(r),V;if(W===95&&o!=="bail"){let X=t.charCodeAt(r-1),je=t.charCodeAt(r+1);if(o){if(Number.isNaN(je)||!T(je)||g.has(X)||g.has(je)){if(c)return{n:null,pos:r};u.unexpectedNumericSeparator(r,e,s)}}else{if(c)return{n:null,pos:r};u.numericSeparatorInEscapeSequence(r,e,s)}++r;continue}if(W>=97?V=W-97+10:W>=65?V=W-65+10:Nl(W)?V=W-48:V=1/0,V>=i){if(V<=9&&c)return{n:null,pos:r};if(V<=9&&u.invalidDigit(r,e,s,i))V=0;else if(n)V=0,C=!0;else break}++r,M=M*i+V}return r===y||a!=null&&r-y!==a||C?{n:null,pos:r}:{n:M,pos:r}}function Lr(t,r,e,s,i,a){let n=t.charCodeAt(r),o;if(n===123){if(++r,{code:o,pos:r}=os(t,r,e,s,t.indexOf("}",r)-r,!0,i,a),++r,o!==null&&o>1114111)if(i)a.invalidCodePoint(r,e,s);else return{code:null,pos:r}}else({code:o,pos:r}=os(t,r,e,s,4,!1,i,a));return{code:o,pos:r}}var Fl=["at"],Ll=["at"];function Je(t,r,e){return new ge(e,t-r,t)}var Ol=new Set([103,109,115,105,121,117,100,118]),Ae=class{constructor(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,this.loc=new lt(t.startLoc,t.endLoc)}},Bl=class extends Sl{constructor(t,r){super(),this.isLookahead=void 0,this.tokens=[],this.errorHandlers_readInt={invalidDigit:(e,s,i,a)=>this.options.errorRecovery?(this.raise(f.InvalidDigit,{at:Je(e,s,i),radix:a}),!0):!1,numericSeparatorInEscapeSequence:this.errorBuilder(f.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:this.errorBuilder(f.UnexpectedNumericSeparator)},this.errorHandlers_readCodePoint=Object.assign({},this.errorHandlers_readInt,{invalidEscapeSequence:this.errorBuilder(f.InvalidEscapeSequence),invalidCodePoint:this.errorBuilder(f.InvalidCodePoint)}),this.errorHandlers_readStringContents_string=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:(e,s,i)=>{this.recordStrictModeErrors(f.StrictNumericEscape,{at:Je(e,s,i)})},unterminated:(e,s,i)=>{throw this.raise(f.UnterminatedString,{at:Je(e-1,s,i)})}}),this.errorHandlers_readStringContents_template=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:this.errorBuilder(f.StrictNumericEscape),unterminated:(e,s,i)=>{throw this.raise(f.UnterminatedTemplate,{at:Je(e,s,i)})}}),this.state=new Nr,this.state.init(t),this.input=r,this.length=r.length,this.isLookahead=!1}pushToken(t){this.tokens.length=this.state.tokensLength,this.tokens.push(t),++this.state.tokensLength}next(){this.checkKeywordEscapes(),this.options.tokens&&this.pushToken(new Ae(this.state)),this.state.lastTokStart=this.state.start,this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(t){return this.match(t)?(this.next(),!0):!1}match(t){return this.state.type===t}createLookaheadState(t){return{pos:t.pos,value:null,type:t.type,start:t.start,end:t.end,context:[this.curContext()],inType:t.inType,startLoc:t.startLoc,lastTokEndLoc:t.lastTokEndLoc,curLine:t.curLine,lineStart:t.lineStart,curPosition:t.curPosition}}lookahead(){let t=this.state;this.state=this.createLookaheadState(t),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;let r=this.state;return this.state=t,r}nextTokenStart(){return this.nextTokenStartSince(this.state.pos)}nextTokenStartSince(t){return ns.lastIndex=t,ns.test(this.input)?ns.lastIndex:t}lookaheadCharCode(){return this.input.charCodeAt(this.nextTokenStart())}codePointAtPos(t){let r=this.input.charCodeAt(t);if((r&64512)===55296&&++t<this.input.length){let e=this.input.charCodeAt(t);(e&64512)===56320&&(r=65536+((r&1023)<<10)+(e&1023))}return r}setStrict(t){this.state.strict=t,t&&(this.state.strictErrors.forEach(r=>{let[e,s]=r;return this.raise(e,{at:s})}),this.state.strictErrors.clear())}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){if(this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length){this.finishToken(137);return}this.getTokenFromCode(this.codePointAtPos(this.state.pos))}skipBlockComment(t){let r;this.isLookahead||(r=this.state.curPosition());let e=this.state.pos,s=this.input.indexOf(t,e+2);if(s===-1)throw this.raise(f.UnterminatedComment,{at:this.state.curPosition()});for(this.state.pos=s+t.length,xt.lastIndex=e+2;xt.test(this.input)&&xt.lastIndex<=s;)++this.state.curLine,this.state.lineStart=xt.lastIndex;if(this.isLookahead)return;let i={type:"CommentBlock",value:this.input.slice(e+2,s),start:e,end:s+t.length,loc:new lt(r,this.state.curPosition())};return this.options.tokens&&this.pushToken(i),i}skipLineComment(t){let r=this.state.pos,e;this.isLookahead||(e=this.state.curPosition());let s=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)for(;!Ge(s)&&++this.state.pos<this.length;)s=this.input.charCodeAt(this.state.pos);if(this.isLookahead)return;let i=this.state.pos,n={type:"CommentLine",value:this.input.slice(r+t,i),start:r,end:i,loc:new lt(e,this.state.curPosition())};return this.options.tokens&&this.pushToken(n),n}skipSpace(){let t=this.state.pos,r=[];e:for(;this.state.pos<this.length;){let e=this.input.charCodeAt(this.state.pos);switch(e){case 32:case 160:case 9:++this.state.pos;break;case 13:this.input.charCodeAt(this.state.pos+1)===10&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:{let s=this.skipBlockComment("*/");s!==void 0&&(this.addComment(s),this.options.attachComment&&r.push(s));break}case 47:{let s=this.skipLineComment(2);s!==void 0&&(this.addComment(s),this.options.attachComment&&r.push(s));break}default:break e}break;default:if(Il(e))++this.state.pos;else if(e===45&&!this.inModule&&this.options.annexB){let s=this.state.pos;if(this.input.charCodeAt(s+1)===45&&this.input.charCodeAt(s+2)===62&&(t===0||this.state.lineStart>t)){let i=this.skipLineComment(3);i!==void 0&&(this.addComment(i),this.options.attachComment&&r.push(i))}else break e}else if(e===60&&!this.inModule&&this.options.annexB){let s=this.state.pos;if(this.input.charCodeAt(s+1)===33&&this.input.charCodeAt(s+2)===45&&this.input.charCodeAt(s+3)===45){let i=this.skipLineComment(4);i!==void 0&&(this.addComment(i),this.options.attachComment&&r.push(i))}else break e}else break e}}if(r.length>0){let e=this.state.pos,s={start:t,end:e,comments:r,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(s)}}finishToken(t,r){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();let e=this.state.type;this.state.type=t,this.state.value=r,this.isLookahead||this.updateContext(e)}replaceToken(t){this.state.type=t,this.updateContext()}readToken_numberSign(){if(this.state.pos===0&&this.readToken_interpreter())return;let t=this.state.pos+1,r=this.codePointAtPos(t);if(r>=48&&r<=57)throw this.raise(f.UnexpectedDigitAfterHash,{at:this.state.curPosition()});if(r===123||r===91&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),this.getPluginOption("recordAndTuple","syntaxType")==="bar")throw this.raise(r===123?f.RecordExpressionHashIncorrectStartSyntaxType:f.TupleExpressionHashIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,r===123?this.finishToken(7):this.finishToken(1)}else fe(r)?(++this.state.pos,this.finishToken(136,this.readWord1(r))):r===92?(++this.state.pos,this.finishToken(136,this.readWord1())):this.finishOp(27,1)}readToken_dot(){let t=this.input.charCodeAt(this.state.pos+1);if(t>=48&&t<=57){this.readNumber(!0);return}t===46&&this.input.charCodeAt(this.state.pos+2)===46?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}readToken_slash(){this.input.charCodeAt(this.state.pos+1)===61?this.finishOp(31,2):this.finishOp(56,1)}readToken_interpreter(){if(this.state.pos!==0||this.length<2)return!1;let t=this.input.charCodeAt(this.state.pos+1);if(t!==33)return!1;let r=this.state.pos;for(this.state.pos+=1;!Ge(t)&&++this.state.pos<this.length;)t=this.input.charCodeAt(this.state.pos);let e=this.input.slice(r+2,this.state.pos);return this.finishToken(28,e),!0}readToken_mult_modulo(t){let r=t===42?55:54,e=1,s=this.input.charCodeAt(this.state.pos+1);t===42&&s===42&&(e++,s=this.input.charCodeAt(this.state.pos+2),r=57),s===61&&!this.state.inType&&(e++,r=t===37?33:30),this.finishOp(r,e)}readToken_pipe_amp(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===t){this.input.charCodeAt(this.state.pos+2)===61?this.finishOp(30,3):this.finishOp(t===124?41:42,2);return}if(t===124){if(r===62){this.finishOp(39,2);return}if(this.hasPlugin("recordAndTuple")&&r===125){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(f.RecordExpressionBarIncorrectEndSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(9);return}if(this.hasPlugin("recordAndTuple")&&r===93){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(f.TupleExpressionBarIncorrectEndSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(4);return}}if(r===61){this.finishOp(30,2);return}this.finishOp(t===124?43:45,1)}readToken_caret(){let t=this.input.charCodeAt(this.state.pos+1);t===61&&!this.state.inType?this.finishOp(32,2):t===94&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])?(this.finishOp(37,2),this.input.codePointAt(this.state.pos)===94&&this.unexpected()):this.finishOp(44,1)}readToken_atSign(){this.input.charCodeAt(this.state.pos+1)===64&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}readToken_plus_min(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===t){this.finishOp(34,2);return}r===61?this.finishOp(30,2):this.finishOp(53,1)}readToken_lt(){let{pos:t}=this.state,r=this.input.charCodeAt(t+1);if(r===60){if(this.input.charCodeAt(t+2)===61){this.finishOp(30,3);return}this.finishOp(51,2);return}if(r===61){this.finishOp(49,2);return}this.finishOp(47,1)}readToken_gt(){let{pos:t}=this.state,r=this.input.charCodeAt(t+1);if(r===62){let e=this.input.charCodeAt(t+2)===62?3:2;if(this.input.charCodeAt(t+e)===61){this.finishOp(30,e+1);return}this.finishOp(52,e);return}if(r===61){this.finishOp(49,2);return}this.finishOp(48,1)}readToken_eq_excl(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===61){this.finishOp(46,this.input.charCodeAt(this.state.pos+2)===61?3:2);return}if(t===61&&r===62){this.state.pos+=2,this.finishToken(19);return}this.finishOp(t===61?29:35,1)}readToken_question(){let t=this.input.charCodeAt(this.state.pos+1),r=this.input.charCodeAt(this.state.pos+2);t===63?r===61?this.finishOp(30,3):this.finishOp(40,2):t===46&&!(r>=48&&r<=57)?(this.state.pos+=2,this.finishToken(18)):(++this.state.pos,this.finishToken(17))}getTokenFromCode(t){switch(t){case 46:this.readToken_dot();return;case 40:++this.state.pos,this.finishToken(10);return;case 41:++this.state.pos,this.finishToken(11);return;case 59:++this.state.pos,this.finishToken(13);return;case 44:++this.state.pos,this.finishToken(12);return;case 91:if(this.hasPlugin("recordAndTuple")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(f.TupleExpressionBarIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:++this.state.pos,this.finishToken(3);return;case 123:if(this.hasPlugin("recordAndTuple")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(f.RecordExpressionBarIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:++this.state.pos,this.finishToken(8);return;case 58:this.hasPlugin("functionBind")&&this.input.charCodeAt(this.state.pos+1)===58?this.finishOp(15,2):(++this.state.pos,this.finishToken(14));return;case 63:this.readToken_question();return;case 96:this.readTemplateToken();return;case 48:{let r=this.input.charCodeAt(this.state.pos+1);if(r===120||r===88){this.readRadixNumber(16);return}if(r===111||r===79){this.readRadixNumber(8);return}if(r===98||r===66){this.readRadixNumber(2);return}}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:this.readNumber(!1);return;case 34:case 39:this.readString(t);return;case 47:this.readToken_slash();return;case 37:case 42:this.readToken_mult_modulo(t);return;case 124:case 38:this.readToken_pipe_amp(t);return;case 94:this.readToken_caret();return;case 43:case 45:this.readToken_plus_min(t);return;case 60:this.readToken_lt();return;case 62:this.readToken_gt();return;case 61:case 33:this.readToken_eq_excl(t);return;case 126:this.finishOp(36,1);return;case 64:this.readToken_atSign();return;case 35:this.readToken_numberSign();return;case 92:this.readWord();return;default:if(fe(t)){this.readWord(t);return}}throw this.raise(f.InvalidOrUnexpectedToken,{at:this.state.curPosition(),unexpected:String.fromCodePoint(t)})}finishOp(t,r){let e=this.input.slice(this.state.pos,this.state.pos+r);this.state.pos+=r,this.finishToken(t,e)}readRegexp(){let t=this.state.startLoc,r=this.state.start+1,e,s,{pos:i}=this.state;for(;;++i){if(i>=this.length)throw this.raise(f.UnterminatedRegExp,{at:Y(t,1)});let u=this.input.charCodeAt(i);if(Ge(u))throw this.raise(f.UnterminatedRegExp,{at:Y(t,1)});if(e)e=!1;else{if(u===91)s=!0;else if(u===93&&s)s=!1;else if(u===47&&!s)break;e=u===92}}let a=this.input.slice(r,i);++i;let n="",o=()=>Y(t,i+2-r);for(;i<this.length;){let u=this.codePointAtPos(i),c=String.fromCharCode(u);if(Ol.has(u))u===118?(this.expectPlugin("regexpUnicodeSets",o()),n.includes("u")&&this.raise(f.IncompatibleRegExpUVFlags,{at:o()})):u===117&&n.includes("v")&&this.raise(f.IncompatibleRegExpUVFlags,{at:o()}),n.includes(c)&&this.raise(f.DuplicateRegExpFlags,{at:o()});else if(De(u)||u===92)this.raise(f.MalformedRegExpFlags,{at:o()});else break;++i,n+=c}this.state.pos=i,this.finishToken(135,{pattern:a,flags:n})}readInt(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,{n:i,pos:a}=Fr(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,r,e,s,this.errorHandlers_readInt,!1);return this.state.pos=a,i}readRadixNumber(t){let r=this.state.curPosition(),e=!1;this.state.pos+=2;let s=this.readInt(t);s==null&&this.raise(f.InvalidDigit,{at:Y(r,2),radix:t});let i=this.input.charCodeAt(this.state.pos);if(i===110)++this.state.pos,e=!0;else if(i===109)throw this.raise(f.InvalidDecimal,{at:r});if(fe(this.codePointAtPos(this.state.pos)))throw this.raise(f.NumberIdentifier,{at:this.state.curPosition()});if(e){let a=this.input.slice(r.index,this.state.pos).replace(/[_n]/g,"");this.finishToken(133,a);return}this.finishToken(132,s)}readNumber(t){let r=this.state.pos,e=this.state.curPosition(),s=!1,i=!1,a=!1,n=!1,o=!1;!t&&this.readInt(10)===null&&this.raise(f.InvalidNumber,{at:this.state.curPosition()});let u=this.state.pos-r>=2&&this.input.charCodeAt(r)===48;if(u){let T=this.input.slice(r,this.state.pos);if(this.recordStrictModeErrors(f.StrictOctalLiteral,{at:e}),!this.state.strict){let C=T.indexOf("_");C>0&&this.raise(f.ZeroDigitNumericSeparator,{at:Y(e,C)})}o=u&&!/[89]/.test(T)}let c=this.input.charCodeAt(this.state.pos);if(c===46&&!o&&(++this.state.pos,this.readInt(10),s=!0,c=this.input.charCodeAt(this.state.pos)),(c===69||c===101)&&!o&&(c=this.input.charCodeAt(++this.state.pos),(c===43||c===45)&&++this.state.pos,this.readInt(10)===null&&this.raise(f.InvalidOrMissingExponent,{at:e}),s=!0,n=!0,c=this.input.charCodeAt(this.state.pos)),c===110&&((s||u)&&this.raise(f.InvalidBigIntLiteral,{at:e}),++this.state.pos,i=!0),c===109&&(this.expectPlugin("decimal",this.state.curPosition()),(n||u)&&this.raise(f.InvalidDecimal,{at:e}),++this.state.pos,a=!0),fe(this.codePointAtPos(this.state.pos)))throw this.raise(f.NumberIdentifier,{at:this.state.curPosition()});let y=this.input.slice(r,this.state.pos).replace(/[_mn]/g,"");if(i){this.finishToken(133,y);return}if(a){this.finishToken(134,y);return}let g=o?parseInt(y,8):parseFloat(y);this.finishToken(132,g)}readCodePoint(t){let{code:r,pos:e}=Lr(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,this.errorHandlers_readCodePoint);return this.state.pos=e,r}readString(t){let{str:r,pos:e,curLine:s,lineStart:i}=Dr(t===34?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string);this.state.pos=e+1,this.state.lineStart=i,this.state.curLine=s,this.finishToken(131,r)}readTemplateContinuation(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}readTemplateToken(){let t=this.input[this.state.pos],{str:r,firstInvalidLoc:e,pos:s,curLine:i,lineStart:a}=Dr("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template);this.state.pos=s+1,this.state.lineStart=a,this.state.curLine=i,e&&(this.state.firstInvalidTemplateEscapePos=new ge(e.curLine,e.pos-e.lineStart,e.pos)),this.input.codePointAt(s)===96?this.finishToken(24,e?null:t+r+"`"):(this.state.pos++,this.finishToken(25,e?null:t+r+"${"))}recordStrictModeErrors(t,r){let{at:e}=r,s=e.index;this.state.strict&&!this.state.strictErrors.has(s)?this.raise(t,{at:e}):this.state.strictErrors.set(s,[t,e])}readWord1(t){this.state.containsEsc=!1;let r="",e=this.state.pos,s=this.state.pos;for(t!==void 0&&(this.state.pos+=t<=65535?1:2);this.state.pos<this.length;){let i=this.codePointAtPos(this.state.pos);if(De(i))this.state.pos+=i<=65535?1:2;else if(i===92){this.state.containsEsc=!0,r+=this.input.slice(s,this.state.pos);let a=this.state.curPosition(),n=this.state.pos===e?fe:De;if(this.input.charCodeAt(++this.state.pos)!==117){this.raise(f.MissingUnicodeEscape,{at:this.state.curPosition()}),s=this.state.pos-1;continue}++this.state.pos;let o=this.readCodePoint(!0);o!==null&&(n(o)||this.raise(f.EscapedCharNotAnIdentifier,{at:a}),r+=String.fromCodePoint(o)),s=this.state.pos}else break}return r+this.input.slice(s,this.state.pos)}readWord(t){let r=this.readWord1(t),e=L.get(r);e!==void 0?this.finishToken(e,xe(e)):this.finishToken(130,r)}checkKeywordEscapes(){let{type:t}=this.state;$t(t)&&this.state.containsEsc&&this.raise(f.InvalidEscapedReservedWord,{at:this.state.startLoc,reservedWord:xe(t)})}raise(t,r){let{at:e}=r,s=ot(r,Fl),i=e instanceof ge?e:e.loc.start,a=t({loc:i,details:s});if(!this.options.errorRecovery)throw a;return this.isLookahead||this.state.errors.push(a),a}raiseOverwrite(t,r){let{at:e}=r,s=ot(r,Ll),i=e instanceof ge?e:e.loc.start,a=i.index,n=this.state.errors;for(let o=n.length-1;o>=0;o--){let u=n[o];if(u.loc.index===a)return n[o]=t({loc:i,details:s});if(u.loc.index<a)break}return this.raise(t,r)}updateContext(t){}unexpected(t,r){throw this.raise(f.UnexpectedToken,{expected:r?xe(r):null,at:t!=null?t:this.state.startLoc})}expectPlugin(t,r){if(this.hasPlugin(t))return!0;throw this.raise(f.MissingPlugin,{at:r!=null?r:this.state.startLoc,missingPlugin:[t]})}expectOnePlugin(t){if(!t.some(r=>this.hasPlugin(r)))throw this.raise(f.MissingOneOfPlugins,{at:this.state.startLoc,missingPlugin:t})}errorBuilder(t){return(r,e,s)=>{this.raise(t,{at:Je(r,e,s)})}}},Ml=class{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}},_l=class{constructor(t){this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=t}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new Ml)}exit(){let t=this.stack.pop(),r=this.current();for(let[e,s]of Array.from(t.undefinedPrivateNames))r?r.undefinedPrivateNames.has(e)||r.undefinedPrivateNames.set(e,s):this.parser.raise(f.InvalidPrivateFieldResolution,{at:s,identifierName:e})}declarePrivateName(t,r,e){let{privateNames:s,loneAccessors:i,undefinedPrivateNames:a}=this.current(),n=s.has(t);if(r&ts){let o=n&&i.get(t);if(o){let u=o&yt,c=r&yt,y=o&ts,g=r&ts;n=y===g||u!==c,n||i.delete(t)}else n||i.set(t,r)}n&&this.parser.raise(f.PrivateNameRedeclaration,{at:e,identifierName:t}),s.add(t),a.delete(t)}usePrivateName(t,r){let e;for(e of this.stack)if(e.privateNames.has(t))return;e?e.undefinedPrivateNames.set(t,r):this.parser.raise(f.InvalidPrivateFieldResolution,{at:r,identifierName:t})}},Rl=0,Or=1,ls=2,Br=3,Pt=class{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Rl;this.type=void 0,this.type=t}canBeArrowParameterDeclaration(){return this.type===ls||this.type===Or}isCertainlyParameterDeclaration(){return this.type===Br}},Mr=class extends Pt{constructor(t){super(t),this.declarationErrors=new Map}recordDeclarationError(t,r){let{at:e}=r,s=e.index;this.declarationErrors.set(s,[t,e])}clearDeclarationError(t){this.declarationErrors.delete(t)}iterateErrors(t){this.declarationErrors.forEach(t)}},jl=class{constructor(t){this.parser=void 0,this.stack=[new Pt],this.parser=t}enter(t){this.stack.push(t)}exit(){this.stack.pop()}recordParameterInitializerError(t,r){let{at:e}=r,s={at:e.loc.start},{stack:i}=this,a=i.length-1,n=i[a];for(;!n.isCertainlyParameterDeclaration();){if(n.canBeArrowParameterDeclaration())n.recordDeclarationError(t,s);else return;n=i[--a]}this.parser.raise(t,s)}recordArrowParameterBindingError(t,r){let{at:e}=r,{stack:s}=this,i=s[s.length-1],a={at:e.loc.start};if(i.isCertainlyParameterDeclaration())this.parser.raise(t,a);else if(i.canBeArrowParameterDeclaration())i.recordDeclarationError(t,a);else return}recordAsyncArrowParametersError(t){let{at:r}=t,{stack:e}=this,s=e.length-1,i=e[s];for(;i.canBeArrowParameterDeclaration();)i.type===ls&&i.recordDeclarationError(f.AwaitBindingIdentifier,{at:r}),i=e[--s]}validateAsPattern(){let{stack:t}=this,r=t[t.length-1];r.canBeArrowParameterDeclaration()&&r.iterateErrors(e=>{let[s,i]=e;this.parser.raise(s,{at:i});let a=t.length-2,n=t[a];for(;n.canBeArrowParameterDeclaration();)n.clearDeclarationError(i.index),n=t[--a]})}};function ql(){return new Pt(Br)}function Ul(){return new Mr(Or)}function $l(){return new Mr(ls)}function _r(){return new Pt}var Me=0,Rr=1,At=2,jr=4,_e=8,Hl=class{constructor(){this.stacks=[]}enter(t){this.stacks.push(t)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(this.currentFlags()&At)>0}get hasYield(){return(this.currentFlags()&Rr)>0}get hasReturn(){return(this.currentFlags()&jr)>0}get hasIn(){return(this.currentFlags()&_e)>0}};function Tt(t,r){return(t?At:0)|(r?Rr:0)}var zl=class extends Bl{addExtra(t,r,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;if(!t)return;let i=t.extra=t.extra||{};s?i[r]=e:Object.defineProperty(i,r,{enumerable:s,value:e})}isContextual(t){return this.state.type===t&&!this.state.containsEsc}isUnparsedContextual(t,r){let e=t+r.length;if(this.input.slice(t,e)===r){let s=this.input.charCodeAt(e);return!(De(s)||(s&64512)===55296)}return!1}isLookaheadContextual(t){let r=this.nextTokenStart();return this.isUnparsedContextual(r,t)}eatContextual(t){return this.isContextual(t)?(this.next(),!0):!1}expectContextual(t,r){if(!this.eatContextual(t)){if(r!=null)throw this.raise(r,{at:this.state.startLoc});this.unexpected(null,t)}}canInsertSemicolon(){return this.match(137)||this.match(8)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return as.test(this.input.slice(this.state.lastTokEndLoc.index,this.state.start))}hasFollowingLineBreak(){return Ir.lastIndex=this.state.end,Ir.test(this.input)}isLineTerminator(){return this.eat(13)||this.canInsertSemicolon()}semicolon(){((arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)?this.isLineTerminator():this.eat(13))||this.raise(f.MissingSemicolon,{at:this.state.lastTokEndLoc})}expect(t,r){this.eat(t)||this.unexpected(r,t)}tryParse(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state.clone(),e={node:null};try{let s=t(function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;throw e.node=i,e});if(this.state.errors.length>r.errors.length){let i=this.state;return this.state=r,this.state.tokensLength=i.tokensLength,{node:s,error:i.errors[r.errors.length],thrown:!1,aborted:!1,failState:i}}return{node:s,error:null,thrown:!1,aborted:!1,failState:null}}catch(s){let i=this.state;if(this.state=r,s instanceof SyntaxError)return{node:null,error:s,thrown:!0,aborted:!1,failState:i};if(s===e)return{node:e.node,error:null,thrown:!1,aborted:!0,failState:i};throw s}}checkExpressionErrors(t,r){if(!t)return!1;let{shorthandAssignLoc:e,doubleProtoLoc:s,privateKeyLoc:i,optionalParametersLoc:a}=t,n=!!e||!!s||!!a||!!i;if(!r)return n;e!=null&&this.raise(f.InvalidCoverInitializedName,{at:e}),s!=null&&this.raise(f.DuplicateProto,{at:s}),i!=null&&this.raise(f.UnexpectedPrivateField,{at:i}),a!=null&&this.unexpected(a)}isLiteralPropertyName(){return it(this.state.type)}isPrivateName(t){return t.type==="PrivateName"}getPrivateNameSV(t){return t.id.name}hasPropertyAsPrivateName(t){return(t.type==="MemberExpression"||t.type==="OptionalMemberExpression")&&this.isPrivateName(t.property)}isObjectProperty(t){return t.type==="ObjectProperty"}isObjectMethod(t){return t.type==="ObjectMethod"}initializeScopes(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.sourceType==="module",r=this.state.labels;this.state.labels=[];let e=this.exportedIdentifiers;this.exportedIdentifiers=new Set;let s=this.inModule;this.inModule=t;let i=this.scope,a=this.getScopeHandler();this.scope=new a(this,t);let n=this.prodParam;this.prodParam=new Hl;let o=this.classScope;this.classScope=new _l(this);let u=this.expressionScope;return this.expressionScope=new jl(this),()=>{this.state.labels=r,this.exportedIdentifiers=e,this.inModule=s,this.scope=i,this.prodParam=n,this.classScope=o,this.expressionScope=u}}enterInitialScopes(){let t=Me;this.inModule&&(t|=At),this.scope.enter(Le),this.prodParam.enter(t)}checkDestructuringPrivate(t){let{privateKeyLoc:r}=t;r!==null&&this.expectPlugin("destructuringPrivate",r)}},vt=class{constructor(){this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null}},Et=class{constructor(t,r,e){this.type="",this.start=r,this.end=0,this.loc=new lt(e),t!=null&&t.options.ranges&&(this.range=[r,0]),t!=null&&t.filename&&(this.loc.filename=t.filename)}},hs=Et.prototype;hs.__clone=function(){let t=new Et(void 0,this.start,this.loc.start),r=Object.keys(this);for(let e=0,s=r.length;e<s;e++){let i=r[e];i!=="leadingComments"&&i!=="trailingComments"&&i!=="innerComments"&&(t[i]=this[i])}return t};function Vl(t){return me(t)}function me(t){let{type:r,start:e,end:s,loc:i,range:a,extra:n,name:o}=t,u=Object.create(hs);return u.type=r,u.start=e,u.end=s,u.loc=i,u.range=a,u.extra=n,u.name=o,r==="Placeholder"&&(u.expectedNode=t.expectedNode),u}function Kl(t){let{type:r,start:e,end:s,loc:i,range:a,extra:n}=t;if(r==="Placeholder")return Vl(t);let o=Object.create(hs);return o.type=r,o.start=e,o.end=s,o.loc=i,o.range=a,t.raw!==void 0?o.raw=t.raw:o.extra=n,o.value=t.value,o}var Wl=class extends zl{startNode(){return new Et(this,this.state.start,this.state.startLoc)}startNodeAt(t){return new Et(this,t.index,t)}startNodeAtNode(t){return this.startNodeAt(t.loc.start)}finishNode(t,r){return this.finishNodeAt(t,r,this.state.lastTokEndLoc)}finishNodeAt(t,r,e){return t.type=r,t.end=e.index,t.loc.end=e,this.options.ranges&&(t.range[1]=e.index),this.options.attachComment&&this.processComment(t),t}resetStartLocation(t,r){t.start=r.index,t.loc.start=r,this.options.ranges&&(t.range[0]=r.index)}resetEndLocation(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state.lastTokEndLoc;t.end=r.index,t.loc.end=r,this.options.ranges&&(t.range[1]=r.index)}resetStartLocationFromNode(t,r){this.resetStartLocation(t,r.loc.start)}},Gl=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),D=pe`flow`({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:t=>{let{reservedType:r}=t;return`Cannot overwrite reserved type ${r}.`},DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:t=>{let{memberName:r,enumName:e}=t;return`Boolean enum members need to be initialized. Use either \`${r} = true,\` or \`${r} = false,\` in enum \`${e}\`.`},EnumDuplicateMemberName:t=>{let{memberName:r,enumName:e}=t;return`Enum member names need to be unique, but the name \`${r}\` has already been used before in enum \`${e}\`.`},EnumInconsistentMemberValues:t=>{let{enumName:r}=t;return`Enum \`${r}\` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.`},EnumInvalidExplicitType:t=>{let{invalidEnumType:r,enumName:e}=t;return`Enum type \`${r}\` is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${e}\`.`},EnumInvalidExplicitTypeUnknownSupplied:t=>{let{enumName:r}=t;return`Supplied enum type is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${r}\`.`},EnumInvalidMemberInitializerPrimaryType:t=>{let{enumName:r,memberName:e,explicitType:s}=t;return`Enum \`${r}\` has type \`${s}\`, so the initializer of \`${e}\` needs to be a ${s} literal.`},EnumInvalidMemberInitializerSymbolType:t=>{let{enumName:r,memberName:e}=t;return`Symbol enum members cannot be initialized. Use \`${e},\` in enum \`${r}\`.`},EnumInvalidMemberInitializerUnknownType:t=>{let{enumName:r,memberName:e}=t;return`The enum member initializer for \`${e}\` needs to be a literal (either a boolean, number, or string) in enum \`${r}\`.`},EnumInvalidMemberName:t=>{let{enumName:r,memberName:e,suggestion:s}=t;return`Enum member names cannot start with lowercase 'a' through 'z'. Instead of using \`${e}\`, consider using \`${s}\`, in enum \`${r}\`.`},EnumNumberMemberNotInitialized:t=>{let{enumName:r,memberName:e}=t;return`Number enum members need to be initialized, e.g. \`${e} = 1\` in enum \`${r}\`.`},EnumStringMemberInconsistentlyInitailized:t=>{let{enumName:r}=t;return`String enum members need to consistently either all use initializers, or use no initializers, in enum \`${r}\`.`},GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:t=>{let{reservedType:r}=t;return`Unexpected reserved type ${r}.`},UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:t=>{let{unsupportedExportKind:r,suggestion:e}=t;return`\`declare export ${r}\` is not supported. Use \`${e}\` instead.`},UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function Jl(t){return t.type==="DeclareExportAllDeclaration"||t.type==="DeclareExportDeclaration"&&(!t.declaration||t.declaration.type!=="TypeAlias"&&t.declaration.type!=="InterfaceDeclaration")}function us(t){return t.importKind==="type"||t.importKind==="typeof"}function qr(t){return te(t)&&t!==97}var Xl={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function Yl(t,r){let e=[],s=[];for(let i=0;i<t.length;i++)(r(t[i],i,t)?e:s).push(t[i]);return[e,s]}var Ql=/\*?\s*@((?:no)?flow)\b/,Zl=t=>class extends t{constructor(){super(...arguments),this.flowPragma=void 0}getScopeHandler(){return El}shouldParseTypes(){return this.getPluginOption("flow","all")||this.flowPragma==="flow"}shouldParseEnums(){return!!this.getPluginOption("flow","enums")}finishToken(e,s){e!==131&&e!==13&&e!==28&&this.flowPragma===void 0&&(this.flowPragma=null),super.finishToken(e,s)}addComment(e){if(this.flowPragma===void 0){let s=Ql.exec(e.value);if(s)if(s[1]==="flow")this.flowPragma="flow";else if(s[1]==="noflow")this.flowPragma="noflow";else throw new Error("Unexpected flow pragma")}super.addComment(e)}flowParseTypeInitialiser(e){let s=this.state.inType;this.state.inType=!0,this.expect(e||14);let i=this.flowParseType();return this.state.inType=s,i}flowParsePredicate(){let e=this.startNode(),s=this.state.startLoc;return this.next(),this.expectContextual(108),this.state.lastTokStart>s.index+1&&this.raise(D.UnexpectedSpaceBetweenModuloChecks,{at:s}),this.eat(10)?(e.value=super.parseExpression(),this.expect(11),this.finishNode(e,"DeclaredPredicate")):this.finishNode(e,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){let e=this.state.inType;this.state.inType=!0,this.expect(14);let s=null,i=null;return this.match(54)?(this.state.inType=e,i=this.flowParsePredicate()):(s=this.flowParseType(),this.state.inType=e,this.match(54)&&(i=this.flowParsePredicate())),[s,i]}flowParseDeclareClass(e){return this.next(),this.flowParseInterfaceish(e,!0),this.finishNode(e,"DeclareClass")}flowParseDeclareFunction(e){this.next();let s=e.id=this.parseIdentifier(),i=this.startNode(),a=this.startNode();this.match(47)?i.typeParameters=this.flowParseTypeParameterDeclaration():i.typeParameters=null,this.expect(10);let n=this.flowParseFunctionTypeParams();return i.params=n.params,i.rest=n.rest,i.this=n._this,this.expect(11),[i.returnType,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),a.typeAnnotation=this.finishNode(i,"FunctionTypeAnnotation"),s.typeAnnotation=this.finishNode(a,"TypeAnnotation"),this.resetEndLocation(s),this.semicolon(),this.scope.declareName(e.id.name,xl,e.id.loc.start),this.finishNode(e,"DeclareFunction")}flowParseDeclare(e,s){if(this.match(80))return this.flowParseDeclareClass(e);if(this.match(68))return this.flowParseDeclareFunction(e);if(this.match(74))return this.flowParseDeclareVariable(e);if(this.eatContextual(125))return this.match(16)?this.flowParseDeclareModuleExports(e):(s&&this.raise(D.NestedDeclareModule,{at:this.state.lastTokStartLoc}),this.flowParseDeclareModule(e));if(this.isContextual(128))return this.flowParseDeclareTypeAlias(e);if(this.isContextual(129))return this.flowParseDeclareOpaqueType(e);if(this.isContextual(127))return this.flowParseDeclareInterface(e);if(this.match(82))return this.flowParseDeclareExportDeclaration(e,s);this.unexpected()}flowParseDeclareVariable(e){return this.next(),e.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(e.id.name,mt,e.id.loc.start),this.semicolon(),this.finishNode(e,"DeclareVariable")}flowParseDeclareModule(e){this.scope.enter(Fe),this.match(131)?e.id=super.parseExprAtom():e.id=this.parseIdentifier();let s=e.body=this.startNode(),i=s.body=[];for(this.expect(5);!this.match(8);){let o=this.startNode();this.match(83)?(this.next(),!this.isContextual(128)&&!this.match(87)&&this.raise(D.InvalidNonTypeImportInDeclareModule,{at:this.state.lastTokStartLoc}),super.parseImport(o)):(this.expectContextual(123,D.UnsupportedStatementInDeclareModule),o=this.flowParseDeclare(o,!0)),i.push(o)}this.scope.exit(),this.expect(8),this.finishNode(s,"BlockStatement");let a=null,n=!1;return i.forEach(o=>{Jl(o)?(a==="CommonJS"&&this.raise(D.AmbiguousDeclareModuleKind,{at:o}),a="ES"):o.type==="DeclareModuleExports"&&(n&&this.raise(D.DuplicateDeclareModuleExports,{at:o}),a==="ES"&&this.raise(D.AmbiguousDeclareModuleKind,{at:o}),a="CommonJS",n=!0)}),e.kind=a||"CommonJS",this.finishNode(e,"DeclareModule")}flowParseDeclareExportDeclaration(e,s){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?e.declaration=this.flowParseDeclare(this.startNode()):(e.declaration=this.flowParseType(),this.semicolon()),e.default=!0,this.finishNode(e,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(128)||this.isContextual(127))&&!s){let i=this.state.value;throw this.raise(D.UnsupportedDeclareExportKind,{at:this.state.startLoc,unsupportedExportKind:i,suggestion:Xl[i]})}if(this.match(74)||this.match(68)||this.match(80)||this.isContextual(129))return e.declaration=this.flowParseDeclare(this.startNode()),e.default=!1,this.finishNode(e,"DeclareExportDeclaration");if(this.match(55)||this.match(5)||this.isContextual(127)||this.isContextual(128)||this.isContextual(129))return e=this.parseExport(e,null),e.type==="ExportNamedDeclaration"&&(e.type="ExportDeclaration",e.default=!1,delete e.exportKind),e.type="Declare"+e.type,e;this.unexpected()}flowParseDeclareModuleExports(e){return this.next(),this.expectContextual(109),e.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(e,"DeclareModuleExports")}flowParseDeclareTypeAlias(e){this.next();let s=this.flowParseTypeAlias(e);return s.type="DeclareTypeAlias",s}flowParseDeclareOpaqueType(e){this.next();let s=this.flowParseOpaqueType(e,!0);return s.type="DeclareOpaqueType",s}flowParseDeclareInterface(e){return this.next(),this.flowParseInterfaceish(e,!1),this.finishNode(e,"DeclareInterface")}flowParseInterfaceish(e,s){if(e.id=this.flowParseRestrictedIdentifier(!s,!0),this.scope.declareName(e.id.name,s?Er:Be,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.extends=[],e.implements=[],e.mixins=[],this.eat(81))do e.extends.push(this.flowParseInterfaceExtends());while(!s&&this.eat(12));if(s){if(this.eatContextual(115))do e.mixins.push(this.flowParseInterfaceExtends());while(this.eat(12));if(this.eatContextual(111))do e.implements.push(this.flowParseInterfaceExtends());while(this.eat(12))}e.body=this.flowParseObjectType({allowStatic:s,allowExact:!1,allowSpread:!1,allowProto:s,allowInexact:!1})}flowParseInterfaceExtends(){let e=this.startNode();return e.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,this.finishNode(e,"InterfaceExtends")}flowParseInterface(e){return this.flowParseInterfaceish(e,!1),this.finishNode(e,"InterfaceDeclaration")}checkNotUnderscore(e){e==="_"&&this.raise(D.UnexpectedReservedUnderscore,{at:this.state.startLoc})}checkReservedType(e,s,i){Gl.has(e)&&this.raise(i?D.AssignReservedType:D.UnexpectedReservedType,{at:s,reservedType:e})}flowParseRestrictedIdentifier(e,s){return this.checkReservedType(this.state.value,this.state.startLoc,s),this.parseIdentifier(e)}flowParseTypeAlias(e){return e.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(e.id.name,Be,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(e,"TypeAlias")}flowParseOpaqueType(e,s){return this.expectContextual(128),e.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(e.id.name,Be,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.supertype=null,this.match(14)&&(e.supertype=this.flowParseTypeInitialiser(14)),e.impltype=null,s||(e.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(e,"OpaqueType")}flowParseTypeParameter(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,s=this.state.startLoc,i=this.startNode(),a=this.flowParseVariance(),n=this.flowParseTypeAnnotatableIdentifier();return i.name=n.name,i.variance=a,i.bound=n.typeAnnotation,this.match(29)?(this.eat(29),i.default=this.flowParseType()):e&&this.raise(D.MissingTypeParamDefault,{at:s}),this.finishNode(i,"TypeParameter")}flowParseTypeParameterDeclaration(){let e=this.state.inType,s=this.startNode();s.params=[],this.state.inType=!0,this.match(47)||this.match(140)?this.next():this.unexpected();let i=!1;do{let a=this.flowParseTypeParameter(i);s.params.push(a),a.default&&(i=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=e,this.finishNode(s,"TypeParameterDeclaration")}flowParseTypeParameterInstantiation(){let e=this.startNode(),s=this.state.inType;e.params=[],this.state.inType=!0,this.expect(47);let i=this.state.noAnonFunctionType;for(this.state.noAnonFunctionType=!1;!this.match(48);)e.params.push(this.flowParseType()),this.match(48)||this.expect(12);return this.state.noAnonFunctionType=i,this.expect(48),this.state.inType=s,this.finishNode(e,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){let e=this.startNode(),s=this.state.inType;for(e.params=[],this.state.inType=!0,this.expect(47);!this.match(48);)e.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=s,this.finishNode(e,"TypeParameterInstantiation")}flowParseInterfaceType(){let e=this.startNode();if(this.expectContextual(127),e.extends=[],this.eat(81))do e.extends.push(this.flowParseInterfaceExtends());while(this.eat(12));return e.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(e,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(132)||this.match(131)?super.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(e,s,i){return e.static=s,this.lookahead().type===14?(e.id=this.flowParseObjectPropertyKey(),e.key=this.flowParseTypeInitialiser()):(e.id=null,e.key=this.flowParseType()),this.expect(3),e.value=this.flowParseTypeInitialiser(),e.variance=i,this.finishNode(e,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(e,s){return e.static=s,e.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(e.method=!0,e.optional=!1,e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start))):(e.method=!1,this.eat(17)&&(e.optional=!0),e.value=this.flowParseTypeInitialiser()),this.finishNode(e,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(e){for(e.params=[],e.rest=null,e.typeParameters=null,e.this=null,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(e.this=this.flowParseFunctionTypeParam(!0),e.this.name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)e.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(e.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),e.returnType=this.flowParseTypeInitialiser(),this.finishNode(e,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(e,s){let i=this.startNode();return e.static=s,e.value=this.flowParseObjectTypeMethodish(i),this.finishNode(e,"ObjectTypeCallProperty")}flowParseObjectType(e){let{allowStatic:s,allowExact:i,allowSpread:a,allowProto:n,allowInexact:o}=e,u=this.state.inType;this.state.inType=!0;let c=this.startNode();c.callProperties=[],c.properties=[],c.indexers=[],c.internalSlots=[];let y,g,T=!1;for(i&&this.match(6)?(this.expect(6),y=9,g=!0):(this.expect(5),y=8,g=!1),c.exact=g;!this.match(y);){let M=!1,j=null,K=null,W=this.startNode();if(n&&this.isContextual(116)){let X=this.lookahead();X.type!==14&&X.type!==17&&(this.next(),j=this.state.startLoc,s=!1)}if(s&&this.isContextual(104)){let X=this.lookahead();X.type!==14&&X.type!==17&&(this.next(),M=!0)}let V=this.flowParseVariance();if(this.eat(0))j!=null&&this.unexpected(j),this.eat(0)?(V&&this.unexpected(V.loc.start),c.internalSlots.push(this.flowParseObjectTypeInternalSlot(W,M))):c.indexers.push(this.flowParseObjectTypeIndexer(W,M,V));else if(this.match(10)||this.match(47))j!=null&&this.unexpected(j),V&&this.unexpected(V.loc.start),c.callProperties.push(this.flowParseObjectTypeCallProperty(W,M));else{let X="init";if(this.isContextual(98)||this.isContextual(103)){let Nh=this.lookahead();it(Nh.type)&&(X=this.state.value,this.next())}let je=this.flowParseObjectTypeProperty(W,M,j,V,X,a,o!=null?o:!g);je===null?(T=!0,K=this.state.lastTokStartLoc):c.properties.push(je)}this.flowObjectTypeSemicolon(),K&&!this.match(8)&&!this.match(9)&&this.raise(D.UnexpectedExplicitInexactInObject,{at:K})}this.expect(y),a&&(c.inexact=T);let C=this.finishNode(c,"ObjectTypeAnnotation");return this.state.inType=u,C}flowParseObjectTypeProperty(e,s,i,a,n,o,u){if(this.eat(21))return this.match(12)||this.match(13)||this.match(8)||this.match(9)?(o?u||this.raise(D.InexactInsideExact,{at:this.state.lastTokStartLoc}):this.raise(D.InexactInsideNonObject,{at:this.state.lastTokStartLoc}),a&&this.raise(D.InexactVariance,{at:a}),null):(o||this.raise(D.UnexpectedSpreadType,{at:this.state.lastTokStartLoc}),i!=null&&this.unexpected(i),a&&this.raise(D.SpreadVariance,{at:a}),e.argument=this.flowParseType(),this.finishNode(e,"ObjectTypeSpreadProperty"));{e.key=this.flowParseObjectPropertyKey(),e.static=s,e.proto=i!=null,e.kind=n;let c=!1;return this.match(47)||this.match(10)?(e.method=!0,i!=null&&this.unexpected(i),a&&this.unexpected(a.loc.start),e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start)),(n==="get"||n==="set")&&this.flowCheckGetterSetterParams(e),!o&&e.key.name==="constructor"&&e.value.this&&this.raise(D.ThisParamBannedInConstructor,{at:e.value.this})):(n!=="init"&&this.unexpected(),e.method=!1,this.eat(17)&&(c=!0),e.value=this.flowParseTypeInitialiser(),e.variance=a),e.optional=c,this.finishNode(e,"ObjectTypeProperty")}}flowCheckGetterSetterParams(e){let s=e.kind==="get"?0:1,i=e.value.params.length+(e.value.rest?1:0);e.value.this&&this.raise(e.kind==="get"?D.GetterMayNotHaveThisParam:D.SetterMayNotHaveThisParam,{at:e.value.this}),i!==s&&this.raise(e.kind==="get"?f.BadGetterArity:f.BadSetterArity,{at:e}),e.kind==="set"&&e.value.rest&&this.raise(f.BadSetterRestParameter,{at:e})}flowObjectTypeSemicolon(){!this.eat(13)&&!this.eat(12)&&!this.match(8)&&!this.match(9)&&this.unexpected()}flowParseQualifiedTypeIdentifier(e,s){var i;(i=e)!=null||(e=this.state.startLoc);let a=s||this.flowParseRestrictedIdentifier(!0);for(;this.eat(16);){let n=this.startNodeAt(e);n.qualification=a,n.id=this.flowParseRestrictedIdentifier(!0),a=this.finishNode(n,"QualifiedTypeIdentifier")}return a}flowParseGenericType(e,s){let i=this.startNodeAt(e);return i.typeParameters=null,i.id=this.flowParseQualifiedTypeIdentifier(e,s),this.match(47)&&(i.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(i,"GenericTypeAnnotation")}flowParseTypeofType(){let e=this.startNode();return this.expect(87),e.argument=this.flowParsePrimaryType(),this.finishNode(e,"TypeofTypeAnnotation")}flowParseTupleType(){let e=this.startNode();for(e.types=[],this.expect(0);this.state.pos<this.length&&!this.match(3)&&(e.types.push(this.flowParseType()),!this.match(3));)this.expect(12);return this.expect(3),this.finishNode(e,"TupleTypeAnnotation")}flowParseFunctionTypeParam(e){let s=null,i=!1,a=null,n=this.startNode(),o=this.lookahead(),u=this.state.type===78;return o.type===14||o.type===17?(u&&!e&&this.raise(D.ThisParamMustBeFirst,{at:n}),s=this.parseIdentifier(u),this.eat(17)&&(i=!0,u&&this.raise(D.ThisParamMayNotBeOptional,{at:n})),a=this.flowParseTypeInitialiser()):a=this.flowParseType(),n.name=s,n.optional=i,n.typeAnnotation=a,this.finishNode(n,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(e){let s=this.startNodeAt(e.loc.start);return s.name=null,s.optional=!1,s.typeAnnotation=e,this.finishNode(s,"FunctionTypeParam")}flowParseFunctionTypeParams(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],s=null,i=null;for(this.match(78)&&(i=this.flowParseFunctionTypeParam(!0),i.name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)e.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(s=this.flowParseFunctionTypeParam(!1)),{params:e,rest:s,_this:i}}flowIdentToTypeAnnotation(e,s,i){switch(i.name){case"any":return this.finishNode(s,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(s,"BooleanTypeAnnotation");case"mixed":return this.finishNode(s,"MixedTypeAnnotation");case"empty":return this.finishNode(s,"EmptyTypeAnnotation");case"number":return this.finishNode(s,"NumberTypeAnnotation");case"string":return this.finishNode(s,"StringTypeAnnotation");case"symbol":return this.finishNode(s,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(i.name),this.flowParseGenericType(e,i)}}flowParsePrimaryType(){let e=this.state.startLoc,s=this.startNode(),i,a,n=!1,o=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,a=this.flowParseTupleType(),this.state.noAnonFunctionType=o,a;case 47:return s.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),i=this.flowParseFunctionTypeParams(),s.params=i.params,s.rest=i.rest,s.this=i._this,this.expect(11),this.expect(19),s.returnType=this.flowParseType(),this.finishNode(s,"FunctionTypeAnnotation");case 10:if(this.next(),!this.match(11)&&!this.match(21))if(q(this.state.type)||this.match(78)){let u=this.lookahead().type;n=u!==17&&u!==14}else n=!0;if(n){if(this.state.noAnonFunctionType=!1,a=this.flowParseType(),this.state.noAnonFunctionType=o,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&this.lookahead().type===19))return this.expect(11),a;this.eat(12)}return a?i=this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(a)]):i=this.flowParseFunctionTypeParams(),s.params=i.params,s.rest=i.rest,s.this=i._this,this.expect(11),this.expect(19),s.returnType=this.flowParseType(),s.typeParameters=null,this.finishNode(s,"FunctionTypeAnnotation");case 131:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return s.value=this.match(85),this.next(),this.finishNode(s,"BooleanLiteralTypeAnnotation");case 53:if(this.state.value==="-"){if(this.next(),this.match(132))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",s);if(this.match(133))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",s);throw this.raise(D.UnexpectedSubtractionOperand,{at:this.state.startLoc})}this.unexpected();return;case 132:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 133:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(s,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(s,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(s,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(s,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if($t(this.state.type)){let u=xe(this.state.type);return this.next(),super.createIdentifier(s,u)}else if(q(this.state.type))return this.isContextual(127)?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(e,s,this.parseIdentifier())}this.unexpected()}flowParsePostfixType(){let e=this.state.startLoc,s=this.flowParsePrimaryType(),i=!1;for(;(this.match(0)||this.match(18))&&!this.canInsertSemicolon();){let a=this.startNodeAt(e),n=this.eat(18);i=i||n,this.expect(0),!n&&this.match(3)?(a.elementType=s,this.next(),s=this.finishNode(a,"ArrayTypeAnnotation")):(a.objectType=s,a.indexType=this.flowParseType(),this.expect(3),i?(a.optional=n,s=this.finishNode(a,"OptionalIndexedAccessType")):s=this.finishNode(a,"IndexedAccessType"))}return s}flowParsePrefixType(){let e=this.startNode();return this.eat(17)?(e.typeAnnotation=this.flowParsePrefixType(),this.finishNode(e,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){let e=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){let s=this.startNodeAt(e.loc.start);return s.params=[this.reinterpretTypeAsFunctionTypeParam(e)],s.rest=null,s.this=null,s.returnType=this.flowParseType(),s.typeParameters=null,this.finishNode(s,"FunctionTypeAnnotation")}return e}flowParseIntersectionType(){let e=this.startNode();this.eat(45);let s=this.flowParseAnonFunctionWithoutParens();for(e.types=[s];this.eat(45);)e.types.push(this.flowParseAnonFunctionWithoutParens());return e.types.length===1?s:this.finishNode(e,"IntersectionTypeAnnotation")}flowParseUnionType(){let e=this.startNode();this.eat(43);let s=this.flowParseIntersectionType();for(e.types=[s];this.eat(43);)e.types.push(this.flowParseIntersectionType());return e.types.length===1?s:this.finishNode(e,"UnionTypeAnnotation")}flowParseType(){let e=this.state.inType;this.state.inType=!0;let s=this.flowParseUnionType();return this.state.inType=e,s}flowParseTypeOrImplicitInstantiation(){if(this.state.type===130&&this.state.value==="_"){let e=this.state.startLoc,s=this.parseIdentifier();return this.flowParseGenericType(e,s)}else return this.flowParseType()}flowParseTypeAnnotation(){let e=this.startNode();return e.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(e,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(e){let s=e?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(s.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(s)),s}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}flowParseVariance(){let e=null;return this.match(53)?(e=this.startNode(),this.state.value==="+"?e.kind="plus":e.kind="minus",this.next(),this.finishNode(e,"Variance")):e}parseFunctionBody(e,s){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(s){this.forwardNoArrowParamsConversionAt(e,()=>super.parseFunctionBody(e,!0,i));return}super.parseFunctionBody(e,!1,i)}parseFunctionBodyAndFinish(e,s){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.match(14)){let a=this.startNode();[a.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),e.returnType=a.typeAnnotation?this.finishNode(a,"TypeAnnotation"):null}return super.parseFunctionBodyAndFinish(e,s,i)}parseStatementLike(e){if(this.state.strict&&this.isContextual(127)){let i=this.lookahead();if(te(i.type)){let a=this.startNode();return this.next(),this.flowParseInterface(a)}}else if(this.shouldParseEnums()&&this.isContextual(124)){let i=this.startNode();return this.next(),this.flowParseEnumDeclaration(i)}let s=super.parseStatementLike(e);return this.flowPragma===void 0&&!this.isValidDirective(s)&&(this.flowPragma=null),s}parseExpressionStatement(e,s,i){if(s.type==="Identifier"){if(s.name==="declare"){if(this.match(80)||q(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(e)}else if(q(this.state.type)){if(s.name==="interface")return this.flowParseInterface(e);if(s.name==="type")return this.flowParseTypeAlias(e);if(s.name==="opaque")return this.flowParseOpaqueType(e,!1)}}return super.parseExpressionStatement(e,s,i)}shouldParseExportDeclaration(){let{type:e}=this.state;return hr(e)||this.shouldParseEnums()&&e===124?!this.state.containsEsc:super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){let{type:e}=this.state;return hr(e)||this.shouldParseEnums()&&e===124?this.state.containsEsc:super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.shouldParseEnums()&&this.isContextual(124)){let e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return super.parseExportDefaultExpression()}parseConditional(e,s,i){if(!this.match(17))return e;if(this.state.maybeInArrowParameters){let T=this.lookaheadCharCode();if(T===44||T===61||T===58||T===41)return this.setOptionalParametersError(i),e}this.expect(17);let a=this.state.clone(),n=this.state.noArrowAt,o=this.startNodeAt(s),{consequent:u,failed:c}=this.tryParseConditionalConsequent(),[y,g]=this.getArrowLikeExpressions(u);if(c||g.length>0){let T=[...n];if(g.length>0){this.state=a,this.state.noArrowAt=T;for(let C=0;C<g.length;C++)T.push(g[C].start);({consequent:u,failed:c}=this.tryParseConditionalConsequent()),[y,g]=this.getArrowLikeExpressions(u)}c&&y.length>1&&this.raise(D.AmbiguousConditionalArrow,{at:a.startLoc}),c&&y.length===1&&(this.state=a,T.push(y[0].start),this.state.noArrowAt=T,{consequent:u,failed:c}=this.tryParseConditionalConsequent())}return this.getArrowLikeExpressions(u,!0),this.state.noArrowAt=n,this.expect(14),o.test=e,o.consequent=u,o.alternate=this.forwardNoArrowParamsConversionAt(o,()=>this.parseMaybeAssign(void 0,void 0)),this.finishNode(o,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);let e=this.parseMaybeAssignAllowIn(),s=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:e,failed:s}}getArrowLikeExpressions(e,s){let i=[e],a=[];for(;i.length!==0;){let n=i.pop();n.type==="ArrowFunctionExpression"?(n.typeParameters||!n.returnType?this.finishArrowValidation(n):a.push(n),i.push(n.body)):n.type==="ConditionalExpression"&&(i.push(n.consequent),i.push(n.alternate))}return s?(a.forEach(n=>this.finishArrowValidation(n)),[a,[]]):Yl(a,n=>n.params.every(o=>this.isAssignable(o,!0)))}finishArrowValidation(e){var s;this.toAssignableList(e.params,(s=e.extra)==null?void 0:s.trailingCommaLoc,!1),this.scope.enter(de|Gt),super.checkParams(e,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(e,s){let i;return this.state.noArrowParamsConversionAt.indexOf(e.start)!==-1?(this.state.noArrowParamsConversionAt.push(this.state.start),i=s(),this.state.noArrowParamsConversionAt.pop()):i=s(),i}parseParenItem(e,s){if(e=super.parseParenItem(e,s),this.eat(17)&&(e.optional=!0,this.resetEndLocation(e)),this.match(14)){let i=this.startNodeAt(s);return i.expression=e,i.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(i,"TypeCastExpression")}return e}assertModuleNodeAllowed(e){e.type==="ImportDeclaration"&&(e.importKind==="type"||e.importKind==="typeof")||e.type==="ExportNamedDeclaration"&&e.exportKind==="type"||e.type==="ExportAllDeclaration"&&e.exportKind==="type"||super.assertModuleNodeAllowed(e)}parseExport(e,s){let i=super.parseExport(e,s);return(i.type==="ExportNamedDeclaration"||i.type==="ExportAllDeclaration")&&(i.exportKind=i.exportKind||"value"),i}parseExportDeclaration(e){if(this.isContextual(128)){e.exportKind="type";let s=this.startNode();return this.next(),this.match(5)?(e.specifiers=this.parseExportSpecifiers(!0),super.parseExportFrom(e),null):this.flowParseTypeAlias(s)}else if(this.isContextual(129)){e.exportKind="type";let s=this.startNode();return this.next(),this.flowParseOpaqueType(s,!1)}else if(this.isContextual(127)){e.exportKind="type";let s=this.startNode();return this.next(),this.flowParseInterface(s)}else if(this.shouldParseEnums()&&this.isContextual(124)){e.exportKind="value";let s=this.startNode();return this.next(),this.flowParseEnumDeclaration(s)}else return super.parseExportDeclaration(e)}eatExportStar(e){return super.eatExportStar(e)?!0:this.isContextual(128)&&this.lookahead().type===55?(e.exportKind="type",this.next(),this.next(),!0):!1}maybeParseExportNamespaceSpecifier(e){let{startLoc:s}=this.state,i=super.maybeParseExportNamespaceSpecifier(e);return i&&e.exportKind==="type"&&this.unexpected(s),i}parseClassId(e,s,i){super.parseClassId(e,s,i),this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(e,s,i){let{startLoc:a}=this.state;if(this.isContextual(123)){if(super.parseClassMemberFromModifier(e,s))return;s.declare=!0}super.parseClassMember(e,s,i),s.declare&&(s.type!=="ClassProperty"&&s.type!=="ClassPrivateProperty"&&s.type!=="PropertyDefinition"?this.raise(D.DeclareClassElement,{at:a}):s.value&&this.raise(D.DeclareClassFieldInitializer,{at:s.value}))}isIterator(e){return e==="iterator"||e==="asyncIterator"}readIterator(){let e=super.readWord1(),s="@@"+e;(!this.isIterator(e)||!this.state.inType)&&this.raise(f.InvalidIdentifier,{at:this.state.curPosition(),identifierName:s}),this.finishToken(130,s)}getTokenFromCode(e){let s=this.input.charCodeAt(this.state.pos+1);e===123&&s===124?this.finishOp(6,2):this.state.inType&&(e===62||e===60)?this.finishOp(e===62?48:47,1):this.state.inType&&e===63?s===46?this.finishOp(18,2):this.finishOp(17,1):ll(e,s,this.input.charCodeAt(this.state.pos+2))?(this.state.pos+=2,this.readIterator()):super.getTokenFromCode(e)}isAssignable(e,s){return e.type==="TypeCastExpression"?this.isAssignable(e.expression,s):super.isAssignable(e,s)}toAssignable(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;!s&&e.type==="AssignmentExpression"&&e.left.type==="TypeCastExpression"&&(e.left=this.typeCastToParameter(e.left)),super.toAssignable(e,s)}toAssignableList(e,s,i){for(let a=0;a<e.length;a++){let n=e[a];(n==null?void 0:n.type)==="TypeCastExpression"&&(e[a]=this.typeCastToParameter(n))}super.toAssignableList(e,s,i)}toReferencedList(e,s){for(let a=0;a<e.length;a++){var i;let n=e[a];n&&n.type==="TypeCastExpression"&&!((i=n.extra)!=null&&i.parenthesized)&&(e.length>1||!s)&&this.raise(D.TypeCastInPattern,{at:n.typeAnnotation})}return e}parseArrayLike(e,s,i,a){let n=super.parseArrayLike(e,s,i,a);return s&&!this.state.maybeInArrowParameters&&this.toReferencedList(n.elements),n}isValidLVal(e,s,i){return e==="TypeCastExpression"||super.isValidLVal(e,s,i)}parseClassProperty(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(e)}parseClassPrivateProperty(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(e)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(14)||super.isClassProperty()}isNonstaticConstructor(e){return!this.match(14)&&super.isNonstaticConstructor(e)}pushClassMethod(e,s,i,a,n,o){if(s.variance&&this.unexpected(s.variance.loc.start),delete s.variance,this.match(47)&&(s.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(e,s,i,a,n,o),s.params&&n){let u=s.params;u.length>0&&this.isThisParam(u[0])&&this.raise(D.ThisParamBannedInConstructor,{at:s})}else if(s.type==="MethodDefinition"&&n&&s.value.params){let u=s.value.params;u.length>0&&this.isThisParam(u[0])&&this.raise(D.ThisParamBannedInConstructor,{at:s})}}pushClassPrivateMethod(e,s,i,a){s.variance&&this.unexpected(s.variance.loc.start),delete s.variance,this.match(47)&&(s.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(e,s,i,a)}parseClassSuper(e){if(super.parseClassSuper(e),e.superClass&&this.match(47)&&(e.superTypeParameters=this.flowParseTypeParameterInstantiation()),this.isContextual(111)){this.next();let s=e.implements=[];do{let i=this.startNode();i.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?i.typeParameters=this.flowParseTypeParameterInstantiation():i.typeParameters=null,s.push(this.finishNode(i,"ClassImplements"))}while(this.eat(12))}}checkGetterSetterParams(e){super.checkGetterSetterParams(e);let s=this.getObjectOrClassMethodParams(e);if(s.length>0){let i=s[0];this.isThisParam(i)&&e.kind==="get"?this.raise(D.GetterMayNotHaveThisParam,{at:i}):this.isThisParam(i)&&this.raise(D.SetterMayNotHaveThisParam,{at:i})}}parsePropertyNamePrefixOperator(e){e.variance=this.flowParseVariance()}parseObjPropValue(e,s,i,a,n,o,u){e.variance&&this.unexpected(e.variance.loc.start),delete e.variance;let c;this.match(47)&&!o&&(c=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());let y=super.parseObjPropValue(e,s,i,a,n,o,u);return c&&((y.value||y).typeParameters=c),y}parseAssignableListItemTypes(e){return this.eat(17)&&(e.type!=="Identifier"&&this.raise(D.PatternIsOptional,{at:e}),this.isThisParam(e)&&this.raise(D.ThisParamMayNotBeOptional,{at:e}),e.optional=!0),this.match(14)?e.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(e)&&this.raise(D.ThisParamAnnotationRequired,{at:e}),this.match(29)&&this.isThisParam(e)&&this.raise(D.ThisParamNoDefault,{at:e}),this.resetEndLocation(e),e}parseMaybeDefault(e,s){let i=super.parseMaybeDefault(e,s);return i.type==="AssignmentPattern"&&i.typeAnnotation&&i.right.start<i.typeAnnotation.start&&this.raise(D.TypeBeforeInitializer,{at:i.typeAnnotation}),i}shouldParseDefaultImport(e){return us(e)?qr(this.state.type):super.shouldParseDefaultImport(e)}checkImportReflection(e){super.checkImportReflection(e),e.module&&e.importKind!=="value"&&this.raise(D.ImportReflectionHasImportType,{at:e.specifiers[0].loc.start})}parseImportSpecifierLocal(e,s,i){s.local=us(e)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),e.specifiers.push(this.finishImportSpecifier(s,i))}maybeParseDefaultImportSpecifier(e){e.importKind="value";let s=null;if(this.match(87)?s="typeof":this.isContextual(128)&&(s="type"),s){let i=this.lookahead(),{type:a}=i;s==="type"&&a===55&&this.unexpected(null,i.type),(qr(a)||a===5||a===55)&&(this.next(),e.importKind=s)}return super.maybeParseDefaultImportSpecifier(e)}parseImportSpecifier(e,s,i,a,n){let o=e.imported,u=null;o.type==="Identifier"&&(o.name==="type"?u="type":o.name==="typeof"&&(u="typeof"));let c=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){let g=this.parseIdentifier(!0);u!==null&&!te(this.state.type)?(e.imported=g,e.importKind=u,e.local=me(g)):(e.imported=o,e.importKind=null,e.local=this.parseIdentifier())}else{if(u!==null&&te(this.state.type))e.imported=this.parseIdentifier(!0),e.importKind=u;else{if(s)throw this.raise(f.ImportBindingIsString,{at:e,importName:o.value});e.imported=o,e.importKind=null}this.eatContextual(93)?e.local=this.parseIdentifier():(c=!0,e.local=me(e.imported))}let y=us(e);return i&&y&&this.raise(D.ImportTypeShorthandOnlyInPureImport,{at:e}),(i||y)&&this.checkReservedType(e.local.name,e.local.loc.start,!0),c&&!i&&!y&&this.checkReservedWord(e.local.name,e.loc.start,!0,!0),this.finishImportSpecifier(e,"ImportSpecifier")}parseBindingAtom(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return super.parseBindingAtom()}}parseFunctionParams(e,s){let i=e.kind;i!=="get"&&i!=="set"&&this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(e,s)}parseVarId(e,s){super.parseVarId(e,s),this.match(14)&&(e.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,s){if(this.match(14)){let i=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,e.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=i}return super.parseAsyncArrowFromCallExpression(e,s)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}parseMaybeAssign(e,s){var i;let a=null,n;if(this.hasPlugin("jsx")&&(this.match(140)||this.match(47))){if(a=this.state.clone(),n=this.tryParse(()=>super.parseMaybeAssign(e,s),a),!n.error)return n.node;let{context:c}=this.state,y=c[c.length-1];(y===x.j_oTag||y===x.j_expr)&&c.pop()}if((i=n)!=null&&i.error||this.match(47)){var o,u;a=a||this.state.clone();let c,y=this.tryParse(T=>{var C;c=this.flowParseTypeParameterDeclaration();let M=this.forwardNoArrowParamsConversionAt(c,()=>{let K=super.parseMaybeAssign(e,s);return this.resetStartLocationFromNode(K,c),K});(C=M.extra)!=null&&C.parenthesized&&T();let j=this.maybeUnwrapTypeCastExpression(M);return j.type!=="ArrowFunctionExpression"&&T(),j.typeParameters=c,this.resetStartLocationFromNode(j,c),M},a),g=null;if(y.node&&this.maybeUnwrapTypeCastExpression(y.node).type==="ArrowFunctionExpression"){if(!y.error&&!y.aborted)return y.node.async&&this.raise(D.UnexpectedTypeParameterBeforeAsyncArrowFunction,{at:c}),y.node;g=y.node}if((o=n)!=null&&o.node)return this.state=n.failState,n.node;if(g)return this.state=y.failState,g;throw(u=n)!=null&&u.thrown?n.error:y.thrown?y.error:this.raise(D.UnexpectedTokenAfterTypeParameter,{at:c})}return super.parseMaybeAssign(e,s)}parseArrow(e){if(this.match(14)){let s=this.tryParse(()=>{let i=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;let a=this.startNode();return[a.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=i,this.canInsertSemicolon()&&this.unexpected(),this.match(19)||this.unexpected(),a});if(s.thrown)return null;s.error&&(this.state=s.failState),e.returnType=s.node.typeAnnotation?this.finishNode(s.node,"TypeAnnotation"):null}return super.parseArrow(e)}shouldParseArrow(e){return this.match(14)||super.shouldParseArrow(e)}setArrowFunctionParameters(e,s){this.state.noArrowParamsConversionAt.indexOf(e.start)!==-1?e.params=s:super.setArrowFunctionParameters(e,s)}checkParams(e,s,i){let a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;if(!(i&&this.state.noArrowParamsConversionAt.indexOf(e.start)!==-1)){for(let n=0;n<e.params.length;n++)this.isThisParam(e.params[n])&&n>0&&this.raise(D.ThisParamMustBeFirst,{at:e.params[n]});super.checkParams(e,s,i,a)}}parseParenAndDistinguishExpression(e){return super.parseParenAndDistinguishExpression(e&&this.state.noArrowAt.indexOf(this.state.start)===-1)}parseSubscripts(e,s,i){if(e.type==="Identifier"&&e.name==="async"&&this.state.noArrowAt.indexOf(s.index)!==-1){this.next();let a=this.startNodeAt(s);a.callee=e,a.arguments=super.parseCallExpressionArguments(11,!1),e=this.finishNode(a,"CallExpression")}else if(e.type==="Identifier"&&e.name==="async"&&this.match(47)){let a=this.state.clone(),n=this.tryParse(u=>this.parseAsyncArrowWithTypeParameters(s)||u(),a);if(!n.error&&!n.aborted)return n.node;let o=this.tryParse(()=>super.parseSubscripts(e,s,i),a);if(o.node&&!o.error)return o.node;if(n.node)return this.state=n.failState,n.node;if(o.node)return this.state=o.failState,o.node;throw n.error||o.error}return super.parseSubscripts(e,s,i)}parseSubscript(e,s,i,a){if(this.match(18)&&this.isLookaheadToken_lt()){if(a.optionalChainMember=!0,i)return a.stop=!0,e;this.next();let n=this.startNodeAt(s);return n.callee=e,n.typeArguments=this.flowParseTypeParameterInstantiation(),this.expect(10),n.arguments=this.parseCallExpressionArguments(11,!1),n.optional=!0,this.finishCallExpression(n,!0)}else if(!i&&this.shouldParseTypes()&&this.match(47)){let n=this.startNodeAt(s);n.callee=e;let o=this.tryParse(()=>(n.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(10),n.arguments=super.parseCallExpressionArguments(11,!1),a.optionalChainMember&&(n.optional=!1),this.finishCallExpression(n,a.optionalChainMember)));if(o.node)return o.error&&(this.state=o.failState),o.node}return super.parseSubscript(e,s,i,a)}parseNewCallee(e){super.parseNewCallee(e);let s=null;this.shouldParseTypes()&&this.match(47)&&(s=this.tryParse(()=>this.flowParseTypeParameterInstantiationCallOrNew()).node),e.typeArguments=s}parseAsyncArrowWithTypeParameters(e){let s=this.startNodeAt(e);if(this.parseFunctionParams(s,!1),!!this.parseArrow(s))return super.parseArrowExpression(s,void 0,!0)}readToken_mult_modulo(e){let s=this.input.charCodeAt(this.state.pos+1);if(e===42&&s===47&&this.state.hasFlowComment){this.state.hasFlowComment=!1,this.state.pos+=2,this.nextToken();return}super.readToken_mult_modulo(e)}readToken_pipe_amp(e){let s=this.input.charCodeAt(this.state.pos+1);if(e===124&&s===125){this.finishOp(9,2);return}super.readToken_pipe_amp(e)}parseTopLevel(e,s){let i=super.parseTopLevel(e,s);return this.state.hasFlowComment&&this.raise(D.UnterminatedFlowComment,{at:this.state.curPosition()}),i}skipBlockComment(){if(this.hasPlugin("flowComments")&&this.skipFlowComment()){if(this.state.hasFlowComment)throw this.raise(D.NestedFlowComment,{at:this.state.startLoc});this.hasFlowCommentCompletion();let e=this.skipFlowComment();e&&(this.state.pos+=e,this.state.hasFlowComment=!0);return}return super.skipBlockComment(this.state.hasFlowComment?"*-/":"*/")}skipFlowComment(){let{pos:e}=this.state,s=2;for(;[32,9].includes(this.input.charCodeAt(e+s));)s++;let i=this.input.charCodeAt(s+e),a=this.input.charCodeAt(s+e+1);return i===58&&a===58?s+2:this.input.slice(s+e,s+e+12)==="flow-include"?s+12:i===58&&a!==58?s:!1}hasFlowCommentCompletion(){if(this.input.indexOf("*/",this.state.pos)===-1)throw this.raise(f.UnterminatedComment,{at:this.state.curPosition()})}flowEnumErrorBooleanMemberNotInitialized(e,s){let{enumName:i,memberName:a}=s;this.raise(D.EnumBooleanMemberNotInitialized,{at:e,memberName:a,enumName:i})}flowEnumErrorInvalidMemberInitializer(e,s){return this.raise(s.explicitType?s.explicitType==="symbol"?D.EnumInvalidMemberInitializerSymbolType:D.EnumInvalidMemberInitializerPrimaryType:D.EnumInvalidMemberInitializerUnknownType,Object.assign({at:e},s))}flowEnumErrorNumberMemberNotInitialized(e,s){let{enumName:i,memberName:a}=s;this.raise(D.EnumNumberMemberNotInitialized,{at:e,enumName:i,memberName:a})}flowEnumErrorStringMemberInconsistentlyInitailized(e,s){let{enumName:i}=s;this.raise(D.EnumStringMemberInconsistentlyInitailized,{at:e,enumName:i})}flowEnumMemberInit(){let e=this.state.startLoc,s=()=>this.match(12)||this.match(8);switch(this.state.type){case 132:{let i=this.parseNumericLiteral(this.state.value);return s()?{type:"number",loc:i.loc.start,value:i}:{type:"invalid",loc:e}}case 131:{let i=this.parseStringLiteral(this.state.value);return s()?{type:"string",loc:i.loc.start,value:i}:{type:"invalid",loc:e}}case 85:case 86:{let i=this.parseBooleanLiteral(this.match(85));return s()?{type:"boolean",loc:i.loc.start,value:i}:{type:"invalid",loc:e}}default:return{type:"invalid",loc:e}}}flowEnumMemberRaw(){let e=this.state.startLoc,s=this.parseIdentifier(!0),i=this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:e};return{id:s,init:i}}flowEnumCheckExplicitTypeMismatch(e,s,i){let{explicitType:a}=s;a!==null&&a!==i&&this.flowEnumErrorInvalidMemberInitializer(e,s)}flowEnumMembers(e){let{enumName:s,explicitType:i}=e,a=new Set,n={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]},o=!1;for(;!this.match(8);){if(this.eat(21)){o=!0;break}let u=this.startNode(),{id:c,init:y}=this.flowEnumMemberRaw(),g=c.name;if(g==="")continue;/^[a-z]/.test(g)&&this.raise(D.EnumInvalidMemberName,{at:c,memberName:g,suggestion:g[0].toUpperCase()+g.slice(1),enumName:s}),a.has(g)&&this.raise(D.EnumDuplicateMemberName,{at:c,memberName:g,enumName:s}),a.add(g);let T={enumName:s,explicitType:i,memberName:g};switch(u.id=c,y.type){case"boolean":{this.flowEnumCheckExplicitTypeMismatch(y.loc,T,"boolean"),u.init=y.value,n.booleanMembers.push(this.finishNode(u,"EnumBooleanMember"));break}case"number":{this.flowEnumCheckExplicitTypeMismatch(y.loc,T,"number"),u.init=y.value,n.numberMembers.push(this.finishNode(u,"EnumNumberMember"));break}case"string":{this.flowEnumCheckExplicitTypeMismatch(y.loc,T,"string"),u.init=y.value,n.stringMembers.push(this.finishNode(u,"EnumStringMember"));break}case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(y.loc,T);case"none":switch(i){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(y.loc,T);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(y.loc,T);break;default:n.defaultedMembers.push(this.finishNode(u,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}return{members:n,hasUnknownMembers:o}}flowEnumStringMembers(e,s,i){let{enumName:a}=i;if(e.length===0)return s;if(s.length===0)return e;if(s.length>e.length){for(let n of e)this.flowEnumErrorStringMemberInconsistentlyInitailized(n,{enumName:a});return s}else{for(let n of s)this.flowEnumErrorStringMemberInconsistentlyInitailized(n,{enumName:a});return e}}flowEnumParseExplicitType(e){let{enumName:s}=e;if(!this.eatContextual(101))return null;if(!q(this.state.type))throw this.raise(D.EnumInvalidExplicitTypeUnknownSupplied,{at:this.state.startLoc,enumName:s});let{value:i}=this.state;return this.next(),i!=="boolean"&&i!=="number"&&i!=="string"&&i!=="symbol"&&this.raise(D.EnumInvalidExplicitType,{at:this.state.startLoc,enumName:s,invalidEnumType:i}),i}flowEnumBody(e,s){let i=s.name,a=s.loc.start,n=this.flowEnumParseExplicitType({enumName:i});this.expect(5);let{members:o,hasUnknownMembers:u}=this.flowEnumMembers({enumName:i,explicitType:n});switch(e.hasUnknownMembers=u,n){case"boolean":return e.explicitType=!0,e.members=o.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody");case"number":return e.explicitType=!0,e.members=o.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody");case"string":return e.explicitType=!0,e.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:i}),this.expect(8),this.finishNode(e,"EnumStringBody");case"symbol":return e.members=o.defaultedMembers,this.expect(8),this.finishNode(e,"EnumSymbolBody");default:{let c=()=>(e.members=[],this.expect(8),this.finishNode(e,"EnumStringBody"));e.explicitType=!1;let y=o.booleanMembers.length,g=o.numberMembers.length,T=o.stringMembers.length,C=o.defaultedMembers.length;if(!y&&!g&&!T&&!C)return c();if(!y&&!g)return e.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:i}),this.expect(8),this.finishNode(e,"EnumStringBody");if(!g&&!T&&y>=C){for(let M of o.defaultedMembers)this.flowEnumErrorBooleanMemberNotInitialized(M.loc.start,{enumName:i,memberName:M.id.name});return e.members=o.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody")}else if(!y&&!T&&g>=C){for(let M of o.defaultedMembers)this.flowEnumErrorNumberMemberNotInitialized(M.loc.start,{enumName:i,memberName:M.id.name});return e.members=o.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody")}else return this.raise(D.EnumInconsistentMemberValues,{at:a,enumName:i}),c()}}}flowParseEnumDeclaration(e){let s=this.parseIdentifier();return e.id=s,e.body=this.flowEnumBody(this.startNode(),s),this.finishNode(e,"EnumDeclaration")}isLookaheadToken_lt(){let e=this.nextTokenStart();if(this.input.charCodeAt(e)===60){let s=this.input.charCodeAt(e+1);return s!==60&&s!==61}return!1}maybeUnwrapTypeCastExpression(e){return e.type==="TypeCastExpression"?e.expression:e}},eh={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:"\xA0",iexcl:"\xA1",cent:"\xA2",pound:"\xA3",curren:"\xA4",yen:"\xA5",brvbar:"\xA6",sect:"\xA7",uml:"\xA8",copy:"\xA9",ordf:"\xAA",laquo:"\xAB",not:"\xAC",shy:"\xAD",reg:"\xAE",macr:"\xAF",deg:"\xB0",plusmn:"\xB1",sup2:"\xB2",sup3:"\xB3",acute:"\xB4",micro:"\xB5",para:"\xB6",middot:"\xB7",cedil:"\xB8",sup1:"\xB9",ordm:"\xBA",raquo:"\xBB",frac14:"\xBC",frac12:"\xBD",frac34:"\xBE",iquest:"\xBF",Agrave:"\xC0",Aacute:"\xC1",Acirc:"\xC2",Atilde:"\xC3",Auml:"\xC4",Aring:"\xC5",AElig:"\xC6",Ccedil:"\xC7",Egrave:"\xC8",Eacute:"\xC9",Ecirc:"\xCA",Euml:"\xCB",Igrave:"\xCC",Iacute:"\xCD",Icirc:"\xCE",Iuml:"\xCF",ETH:"\xD0",Ntilde:"\xD1",Ograve:"\xD2",Oacute:"\xD3",Ocirc:"\xD4",Otilde:"\xD5",Ouml:"\xD6",times:"\xD7",Oslash:"\xD8",Ugrave:"\xD9",Uacute:"\xDA",Ucirc:"\xDB",Uuml:"\xDC",Yacute:"\xDD",THORN:"\xDE",szlig:"\xDF",agrave:"\xE0",aacute:"\xE1",acirc:"\xE2",atilde:"\xE3",auml:"\xE4",aring:"\xE5",aelig:"\xE6",ccedil:"\xE7",egrave:"\xE8",eacute:"\xE9",ecirc:"\xEA",euml:"\xEB",igrave:"\xEC",iacute:"\xED",icirc:"\xEE",iuml:"\xEF",eth:"\xF0",ntilde:"\xF1",ograve:"\xF2",oacute:"\xF3",ocirc:"\xF4",otilde:"\xF5",ouml:"\xF6",divide:"\xF7",oslash:"\xF8",ugrave:"\xF9",uacute:"\xFA",ucirc:"\xFB",uuml:"\xFC",yacute:"\xFD",thorn:"\xFE",yuml:"\xFF",OElig:"\u0152",oelig:"\u0153",Scaron:"\u0160",scaron:"\u0161",Yuml:"\u0178",fnof:"\u0192",circ:"\u02C6",tilde:"\u02DC",Alpha:"\u0391",Beta:"\u0392",Gamma:"\u0393",Delta:"\u0394",Epsilon:"\u0395",Zeta:"\u0396",Eta:"\u0397",Theta:"\u0398",Iota:"\u0399",Kappa:"\u039A",Lambda:"\u039B",Mu:"\u039C",Nu:"\u039D",Xi:"\u039E",Omicron:"\u039F",Pi:"\u03A0",Rho:"\u03A1",Sigma:"\u03A3",Tau:"\u03A4",Upsilon:"\u03A5",Phi:"\u03A6",Chi:"\u03A7",Psi:"\u03A8",Omega:"\u03A9",alpha:"\u03B1",beta:"\u03B2",gamma:"\u03B3",delta:"\u03B4",epsilon:"\u03B5",zeta:"\u03B6",eta:"\u03B7",theta:"\u03B8",iota:"\u03B9",kappa:"\u03BA",lambda:"\u03BB",mu:"\u03BC",nu:"\u03BD",xi:"\u03BE",omicron:"\u03BF",pi:"\u03C0",rho:"\u03C1",sigmaf:"\u03C2",sigma:"\u03C3",tau:"\u03C4",upsilon:"\u03C5",phi:"\u03C6",chi:"\u03C7",psi:"\u03C8",omega:"\u03C9",thetasym:"\u03D1",upsih:"\u03D2",piv:"\u03D6",ensp:"\u2002",emsp:"\u2003",thinsp:"\u2009",zwnj:"\u200C",zwj:"\u200D",lrm:"\u200E",rlm:"\u200F",ndash:"\u2013",mdash:"\u2014",lsquo:"\u2018",rsquo:"\u2019",sbquo:"\u201A",ldquo:"\u201C",rdquo:"\u201D",bdquo:"\u201E",dagger:"\u2020",Dagger:"\u2021",bull:"\u2022",hellip:"\u2026",permil:"\u2030",prime:"\u2032",Prime:"\u2033",lsaquo:"\u2039",rsaquo:"\u203A",oline:"\u203E",frasl:"\u2044",euro:"\u20AC",image:"\u2111",weierp:"\u2118",real:"\u211C",trade:"\u2122",alefsym:"\u2135",larr:"\u2190",uarr:"\u2191",rarr:"\u2192",darr:"\u2193",harr:"\u2194",crarr:"\u21B5",lArr:"\u21D0",uArr:"\u21D1",rArr:"\u21D2",dArr:"\u21D3",hArr:"\u21D4",forall:"\u2200",part:"\u2202",exist:"\u2203",empty:"\u2205",nabla:"\u2207",isin:"\u2208",notin:"\u2209",ni:"\u220B",prod:"\u220F",sum:"\u2211",minus:"\u2212",lowast:"\u2217",radic:"\u221A",prop:"\u221D",infin:"\u221E",ang:"\u2220",and:"\u2227",or:"\u2228",cap:"\u2229",cup:"\u222A",int:"\u222B",there4:"\u2234",sim:"\u223C",cong:"\u2245",asymp:"\u2248",ne:"\u2260",equiv:"\u2261",le:"\u2264",ge:"\u2265",sub:"\u2282",sup:"\u2283",nsub:"\u2284",sube:"\u2286",supe:"\u2287",oplus:"\u2295",otimes:"\u2297",perp:"\u22A5",sdot:"\u22C5",lceil:"\u2308",rceil:"\u2309",lfloor:"\u230A",rfloor:"\u230B",lang:"\u2329",rang:"\u232A",loz:"\u25CA",spades:"\u2660",clubs:"\u2663",hearts:"\u2665",diams:"\u2666"},Se=pe`jsx`({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:t=>{let{openingTagName:r}=t;return`Expected corresponding JSX closing tag for <${r}>.`},MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:t=>{let{unexpected:r,HTMLEntity:e}=t;return`Unexpected token \`${r}\`. Did you mean \`${e}\` or \`{'${r}'}\`?`},UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function Te(t){return t?t.type==="JSXOpeningFragment"||t.type==="JSXClosingFragment":!1}function Re(t){if(t.type==="JSXIdentifier")return t.name;if(t.type==="JSXNamespacedName")return t.namespace.name+":"+t.name.name;if(t.type==="JSXMemberExpression")return Re(t.object)+"."+Re(t.property);throw new Error("Node had unexpected type: "+t.type)}var th=t=>class extends t{jsxReadToken(){let e="",s=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(Se.UnterminatedJsxContent,{at:this.state.startLoc});let i=this.input.charCodeAt(this.state.pos);switch(i){case 60:case 123:if(this.state.pos===this.state.start){i===60&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(140)):super.getTokenFromCode(i);return}e+=this.input.slice(s,this.state.pos),this.finishToken(139,e);return;case 38:e+=this.input.slice(s,this.state.pos),e+=this.jsxReadEntity(),s=this.state.pos;break;case 62:case 125:default:Ge(i)?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadNewLine(!0),s=this.state.pos):++this.state.pos}}}jsxReadNewLine(e){let s=this.input.charCodeAt(this.state.pos),i;return++this.state.pos,s===13&&this.input.charCodeAt(this.state.pos)===10?(++this.state.pos,i=e?`
`:`\r
`):i=String.fromCharCode(s),++this.state.curLine,this.state.lineStart=this.state.pos,i}jsxReadString(e){let s="",i=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(f.UnterminatedString,{at:this.state.startLoc});let a=this.input.charCodeAt(this.state.pos);if(a===e)break;a===38?(s+=this.input.slice(i,this.state.pos),s+=this.jsxReadEntity(),i=this.state.pos):Ge(a)?(s+=this.input.slice(i,this.state.pos),s+=this.jsxReadNewLine(!1),i=this.state.pos):++this.state.pos}s+=this.input.slice(i,this.state.pos++),this.finishToken(131,s)}jsxReadEntity(){let e=++this.state.pos;if(this.codePointAtPos(this.state.pos)===35){++this.state.pos;let s=10;this.codePointAtPos(this.state.pos)===120&&(s=16,++this.state.pos);let i=this.readInt(s,void 0,!1,"bail");if(i!==null&&this.codePointAtPos(this.state.pos)===59)return++this.state.pos,String.fromCodePoint(i)}else{let s=0,i=!1;for(;s++<10&&this.state.pos<this.length&&!(i=this.codePointAtPos(this.state.pos)==59);)++this.state.pos;if(i){let a=this.input.slice(e,this.state.pos),n=eh[a];if(++this.state.pos,n)return n}}return this.state.pos=e,"&"}jsxReadWord(){let e,s=this.state.pos;do e=this.input.charCodeAt(++this.state.pos);while(De(e)||e===45);this.finishToken(138,this.input.slice(s,this.state.pos))}jsxParseIdentifier(){let e=this.startNode();return this.match(138)?e.name=this.state.value:$t(this.state.type)?e.name=xe(this.state.type):this.unexpected(),this.next(),this.finishNode(e,"JSXIdentifier")}jsxParseNamespacedName(){let e=this.state.startLoc,s=this.jsxParseIdentifier();if(!this.eat(14))return s;let i=this.startNodeAt(e);return i.namespace=s,i.name=this.jsxParseIdentifier(),this.finishNode(i,"JSXNamespacedName")}jsxParseElementName(){let e=this.state.startLoc,s=this.jsxParseNamespacedName();if(s.type==="JSXNamespacedName")return s;for(;this.eat(16);){let i=this.startNodeAt(e);i.object=s,i.property=this.jsxParseIdentifier(),s=this.finishNode(i,"JSXMemberExpression")}return s}jsxParseAttributeValue(){let e;switch(this.state.type){case 5:return e=this.startNode(),this.setContext(x.brace),this.next(),e=this.jsxParseExpressionContainer(e,x.j_oTag),e.expression.type==="JSXEmptyExpression"&&this.raise(Se.AttributeIsEmpty,{at:e}),e;case 140:case 131:return this.parseExprAtom();default:throw this.raise(Se.UnsupportedJsxValue,{at:this.state.startLoc})}}jsxParseEmptyExpression(){let e=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(e,"JSXEmptyExpression",this.state.startLoc)}jsxParseSpreadChild(e){return this.next(),e.expression=this.parseExpression(),this.setContext(x.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadChild")}jsxParseExpressionContainer(e,s){if(this.match(8))e.expression=this.jsxParseEmptyExpression();else{let i=this.parseExpression();e.expression=i}return this.setContext(s),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXExpressionContainer")}jsxParseAttribute(){let e=this.startNode();return this.match(5)?(this.setContext(x.brace),this.next(),this.expect(21),e.argument=this.parseMaybeAssignAllowIn(),this.setContext(x.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadAttribute")):(e.name=this.jsxParseNamespacedName(),e.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(e,"JSXAttribute"))}jsxParseOpeningElementAt(e){let s=this.startNodeAt(e);return this.eat(141)?this.finishNode(s,"JSXOpeningFragment"):(s.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(s))}jsxParseOpeningElementAfterName(e){let s=[];for(;!this.match(56)&&!this.match(141);)s.push(this.jsxParseAttribute());return e.attributes=s,e.selfClosing=this.eat(56),this.expect(141),this.finishNode(e,"JSXOpeningElement")}jsxParseClosingElementAt(e){let s=this.startNodeAt(e);return this.eat(141)?this.finishNode(s,"JSXClosingFragment"):(s.name=this.jsxParseElementName(),this.expect(141),this.finishNode(s,"JSXClosingElement"))}jsxParseElementAt(e){let s=this.startNodeAt(e),i=[],a=this.jsxParseOpeningElementAt(e),n=null;if(!a.selfClosing){e:for(;;)switch(this.state.type){case 140:if(e=this.state.startLoc,this.next(),this.eat(56)){n=this.jsxParseClosingElementAt(e);break e}i.push(this.jsxParseElementAt(e));break;case 139:i.push(this.parseExprAtom());break;case 5:{let o=this.startNode();this.setContext(x.brace),this.next(),this.match(21)?i.push(this.jsxParseSpreadChild(o)):i.push(this.jsxParseExpressionContainer(o,x.j_expr));break}default:this.unexpected()}Te(a)&&!Te(n)&&n!==null?this.raise(Se.MissingClosingTagFragment,{at:n}):!Te(a)&&Te(n)?this.raise(Se.MissingClosingTagElement,{at:n,openingTagName:Re(a.name)}):!Te(a)&&!Te(n)&&Re(n.name)!==Re(a.name)&&this.raise(Se.MissingClosingTagElement,{at:n,openingTagName:Re(a.name)})}if(Te(a)?(s.openingFragment=a,s.closingFragment=n):(s.openingElement=a,s.closingElement=n),s.children=i,this.match(47))throw this.raise(Se.UnwrappedAdjacentJSXElements,{at:this.state.startLoc});return Te(a)?this.finishNode(s,"JSXFragment"):this.finishNode(s,"JSXElement")}jsxParseElement(){let e=this.state.startLoc;return this.next(),this.jsxParseElementAt(e)}setContext(e){let{context:s}=this.state;s[s.length-1]=e}parseExprAtom(e){return this.match(139)?this.parseLiteral(this.state.value,"JSXText"):this.match(140)?this.jsxParseElement():this.match(47)&&this.input.charCodeAt(this.state.pos)!==33?(this.replaceToken(140),this.jsxParseElement()):super.parseExprAtom(e)}skipSpace(){this.curContext().preserveSpace||super.skipSpace()}getTokenFromCode(e){let s=this.curContext();if(s===x.j_expr){this.jsxReadToken();return}if(s===x.j_oTag||s===x.j_cTag){if(fe(e)){this.jsxReadWord();return}if(e===62){++this.state.pos,this.finishToken(141);return}if((e===34||e===39)&&s===x.j_oTag){this.jsxReadString(e);return}}if(e===60&&this.state.canStartJSXElement&&this.input.charCodeAt(this.state.pos+1)!==33){++this.state.pos,this.finishToken(140);return}super.getTokenFromCode(e)}updateContext(e){let{context:s,type:i}=this.state;if(i===56&&e===140)s.splice(-2,2,x.j_cTag),this.state.canStartJSXElement=!1;else if(i===140)s.push(x.j_oTag);else if(i===141){let a=s[s.length-1];a===x.j_oTag&&e===56||a===x.j_cTag?(s.pop(),this.state.canStartJSXElement=s[s.length-1]===x.j_expr):(this.setContext(x.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=se(i)}},sh=class extends rs{constructor(){super(...arguments),this.types=new Set,this.enums=new Set,this.constEnums=new Set,this.classes=new Set,this.exportOnlyBindings=new Set}},rh=class extends is{constructor(){super(...arguments),this.importsStack=[]}createScope(t){return this.importsStack.push(new Set),new sh(t)}enter(t){t==Oe&&this.importsStack.push(new Set),super.enter(t)}exit(){let t=super.exit();return t==Oe&&this.importsStack.pop(),t}hasImport(t,r){let e=this.importsStack.length;if(this.importsStack[e-1].has(t))return!0;if(!r&&e>1){for(let s=0;s<e-1;s++)if(this.importsStack[s].has(t))return!0}return!1}declareName(t,r,e){if(r&Ve){this.hasImport(t,!0)&&this.parser.raise(f.VarRedeclaration,{at:e,identifierName:t}),this.importsStack[this.importsStack.length-1].add(t);return}let s=this.currentScope();if(r&Yt){this.maybeExportDefined(s,t),s.exportOnlyBindings.add(t);return}super.declareName(t,r,e),r&Ce&&(r&le||(this.checkRedeclarationInScope(s,t,r,e),this.maybeExportDefined(s,t)),s.types.add(t)),r&Jt&&s.enums.add(t),r&Xt&&s.constEnums.add(t),r&ft&&s.classes.add(t)}isRedeclaredInScope(t,r,e){if(t.enums.has(r)){if(e&Jt){let s=!!(e&Xt),i=t.constEnums.has(r);return s!==i}return!0}return e&ft&&t.classes.has(r)?t.lexical.has(r)?!!(e&le):!1:e&Ce&&t.types.has(r)?!0:super.isRedeclaredInScope(t,r,e)}checkLocalExport(t){let{name:r}=t;if(this.hasImport(r))return;let e=this.scopeStack.length;for(let s=e-1;s>=0;s--){let i=this.scopeStack[s];if(i.types.has(r)||i.exportOnlyBindings.has(r))return}super.checkLocalExport(t)}},ih=(t,r)=>Object.hasOwnProperty.call(t,r)&&t[r],Ur=t=>t.type==="ParenthesizedExpression"?Ur(t.expression):t,ah=class extends Wl{toAssignable(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;var e,s;let i;switch((t.type==="ParenthesizedExpression"||(e=t.extra)!=null&&e.parenthesized)&&(i=Ur(t),r?i.type==="Identifier"?this.expressionScope.recordArrowParameterBindingError(f.InvalidParenthesizedAssignment,{at:t}):i.type!=="MemberExpression"&&this.raise(f.InvalidParenthesizedAssignment,{at:t}):this.raise(f.InvalidParenthesizedAssignment,{at:t})),t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":t.type="ObjectPattern";for(let n=0,o=t.properties.length,u=o-1;n<o;n++){var a;let c=t.properties[n],y=n===u;this.toAssignableObjectExpressionProp(c,y,r),y&&c.type==="RestElement"&&(a=t.extra)!=null&&a.trailingCommaLoc&&this.raise(f.RestTrailingComma,{at:t.extra.trailingCommaLoc})}break;case"ObjectProperty":{let{key:n,value:o}=t;this.isPrivateName(n)&&this.classScope.usePrivateName(this.getPrivateNameSV(n),n.loc.start),this.toAssignable(o,r);break}case"SpreadElement":throw new Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":t.type="ArrayPattern",this.toAssignableList(t.elements,(s=t.extra)==null?void 0:s.trailingCommaLoc,r);break;case"AssignmentExpression":t.operator!=="="&&this.raise(f.MissingEqInAssignment,{at:t.left.loc.end}),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,r);break;case"ParenthesizedExpression":this.toAssignable(i,r);break}}toAssignableObjectExpressionProp(t,r,e){if(t.type==="ObjectMethod")this.raise(t.kind==="get"||t.kind==="set"?f.PatternHasAccessor:f.PatternHasMethod,{at:t.key});else if(t.type==="SpreadElement"){t.type="RestElement";let s=t.argument;this.checkToRestConversion(s,!1),this.toAssignable(s,e),r||this.raise(f.RestTrailingComma,{at:t})}else this.toAssignable(t,e)}toAssignableList(t,r,e){let s=t.length-1;for(let i=0;i<=s;i++){let a=t[i];if(a){if(a.type==="SpreadElement"){a.type="RestElement";let n=a.argument;this.checkToRestConversion(n,!0),this.toAssignable(n,e)}else this.toAssignable(a,e);a.type==="RestElement"&&(i<s?this.raise(f.RestTrailingComma,{at:a}):r&&this.raise(f.RestTrailingComma,{at:r}))}}}isAssignable(t,r){switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":return!0;case"ObjectExpression":{let e=t.properties.length-1;return t.properties.every((s,i)=>s.type!=="ObjectMethod"&&(i===e||s.type!=="SpreadElement")&&this.isAssignable(s))}case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every(e=>e===null||this.isAssignable(e));case"AssignmentExpression":return t.operator==="=";case"ParenthesizedExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!r;default:return!1}}toReferencedList(t,r){return t}toReferencedListDeep(t,r){this.toReferencedList(t,r);for(let e of t)(e==null?void 0:e.type)==="ArrayExpression"&&this.toReferencedListDeep(e.elements)}parseSpread(t){let r=this.startNode();return this.next(),r.argument=this.parseMaybeAssignAllowIn(t,void 0),this.finishNode(r,"SpreadElement")}parseRestBinding(){let t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}parseBindingAtom(){switch(this.state.type){case 0:{let t=this.startNode();return this.next(),t.elements=this.parseBindingList(3,93,1),this.finishNode(t,"ArrayPattern")}case 5:return this.parseObjectLike(8,!0)}return this.parseIdentifier()}parseBindingList(t,r,e){let s=e&1,i=[],a=!0;for(;!this.eat(t);)if(a?a=!1:this.expect(12),s&&this.match(12))i.push(null);else{if(this.eat(t))break;if(this.match(21)){if(i.push(this.parseAssignableListItemTypes(this.parseRestBinding(),e)),!this.checkCommaAfterRest(r)){this.expect(t);break}}else{let n=[];for(this.match(26)&&this.hasPlugin("decorators")&&this.raise(f.UnsupportedParameterDecorator,{at:this.state.startLoc});this.match(26);)n.push(this.parseDecorator());i.push(this.parseAssignableListItem(e,n))}}return i}parseBindingRestProperty(t){return this.next(),t.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(t,"RestElement")}parseBindingProperty(){let t=this.startNode(),{type:r,startLoc:e}=this.state;return r===21?this.parseBindingRestProperty(t):(r===136?(this.expectPlugin("destructuringPrivate",e),this.classScope.usePrivateName(this.state.value,e),t.key=this.parsePrivateName()):this.parsePropertyName(t),t.method=!1,this.parseObjPropValue(t,e,!1,!1,!0,!1))}parseAssignableListItem(t,r){let e=this.parseMaybeDefault();this.parseAssignableListItemTypes(e,t);let s=this.parseMaybeDefault(e.loc.start,e);return r.length&&(e.decorators=r),s}parseAssignableListItemTypes(t,r){return t}parseMaybeDefault(t,r){var e,s;if((e=t)!=null||(t=this.state.startLoc),r=(s=r)!=null?s:this.parseBindingAtom(),!this.eat(29))return r;let i=this.startNodeAt(t);return i.left=r,i.right=this.parseMaybeAssignAllowIn(),this.finishNode(i,"AssignmentPattern")}isValidLVal(t,r,e){return ih({AssignmentPattern:"left",RestElement:"argument",ObjectProperty:"value",ParenthesizedExpression:"expression",ArrayPattern:"elements",ObjectPattern:"properties"},t)}checkLVal(t,r){let{in:e,binding:s=Pe,checkClashes:i=!1,strictModeChanged:a=!1,hasParenthesizedAncestor:n=!1}=r;var o;let u=t.type;if(this.isObjectMethod(t))return;if(u==="MemberExpression"){s!==Pe&&this.raise(f.InvalidPropertyBindingPattern,{at:t});return}if(u==="Identifier"){this.checkIdentifier(t,s,a);let{name:C}=t;i&&(i.has(C)?this.raise(f.ParamDupe,{at:t}):i.add(C));return}let c=this.isValidLVal(u,!(n||(o=t.extra)!=null&&o.parenthesized)&&e.type==="AssignmentExpression",s);if(c===!0)return;if(c===!1){let C=s===Pe?f.InvalidLhs:f.InvalidLhsBinding;this.raise(C,{at:t,ancestor:e});return}let[y,g]=Array.isArray(c)?c:[c,u==="ParenthesizedExpression"],T=u==="ArrayPattern"||u==="ObjectPattern"||u==="ParenthesizedExpression"?{type:u}:e;for(let C of[].concat(t[y]))C&&this.checkLVal(C,{in:T,binding:s,checkClashes:i,strictModeChanged:a,hasParenthesizedAncestor:g})}checkIdentifier(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.state.strict&&(e?xr(t.name,this.inModule):yr(t.name))&&(r===Pe?this.raise(f.StrictEvalArguments,{at:t,referenceName:t.name}):this.raise(f.StrictEvalArgumentsBinding,{at:t,bindingName:t.name})),r&dt&&t.name==="let"&&this.raise(f.LetInLexicalBinding,{at:t}),r&Pe||this.declareNameFromIdentifier(t,r)}declareNameFromIdentifier(t,r){this.scope.declareName(t.name,r,t.loc.start)}checkToRestConversion(t,r){switch(t.type){case"ParenthesizedExpression":this.checkToRestConversion(t.expression,r);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(r)break;default:this.raise(f.InvalidRestAssignmentPattern,{at:t})}}checkCommaAfterRest(t){return this.match(12)?(this.raise(this.lookaheadCharCode()===t?f.RestTrailingComma:f.ElementAfterRest,{at:this.state.startLoc}),!0):!1}},nh=(t,r)=>Object.hasOwnProperty.call(t,r)&&t[r];function oh(t){if(t==null)throw new Error(`Unexpected ${t} value.`);return t}function $r(t){if(!t)throw new Error("Assert fail")}var I=pe`typescript`({AbstractMethodHasImplementation:t=>{let{methodName:r}=t;return`Method '${r}' cannot have an implementation because it is marked abstract.`},AbstractPropertyHasInitializer:t=>{let{propertyName:r}=t;return`Property '${r}' cannot have an initializer because it is marked abstract.`},AccesorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccesorCannotHaveTypeParameters:"An accessor cannot have type parameters.",AccessorCannotBeOptional:"An 'accessor' property cannot be declared optional.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:t=>{let{kind:r}=t;return`'declare' is not allowed in ${r}ters.`},DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:t=>{let{modifier:r}=t;return"Accessibility modifier already seen."},DuplicateModifier:t=>{let{modifier:r}=t;return`Duplicate modifier: '${r}'.`},EmptyHeritageClauseType:t=>{let{token:r}=t;return`'${r}' list cannot be empty.`},EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:t=>{let{modifiers:r}=t;return`'${r[0]}' modifier cannot be used with '${r[1]}' modifier.`},IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:t=>{let{modifier:r}=t;return`Index signatures cannot have an accessibility modifier ('${r}').`},IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidModifierOnTypeMember:t=>{let{modifier:r}=t;return`'${r}' modifier cannot appear on a type member.`},InvalidModifierOnTypeParameter:t=>{let{modifier:r}=t;return`'${r}' modifier cannot appear on a type parameter.`},InvalidModifierOnTypeParameterPositions:t=>{let{modifier:r}=t;return`'${r}' modifier can only appear on a type parameter of a class, interface or type alias.`},InvalidModifiersOrder:t=>{let{orderedModifiers:r}=t;return`'${r[0]}' modifier must precede '${r[1]}' modifier.`},InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",MixedLabeledAndUnlabeledElements:"Tuple members must all have names or all not have names.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:t=>{let{modifier:r}=t;return`Private elements cannot have an accessibility modifier ('${r}').`},ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccesorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccesorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccesorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:t=>{let{typeParameterName:r}=t;return`Single type parameter ${r} should have a trailing comma. Example usage: <${r},>.`},StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TupleOptionalAfterType:"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:t=>{let{type:r}=t;return`Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ${r}.`}});function lh(t){switch(t){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}function Hr(t){return t==="private"||t==="public"||t==="protected"}function hh(t){return t==="in"||t==="out"}var uh=t=>class extends t{constructor(){super(...arguments),this.tsParseInOutModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out"],disallowedModifiers:["const","public","private","protected","readonly","declare","abstract","override"],errorTemplate:I.InvalidModifierOnTypeParameter}),this.tsParseConstModifier=this.tsParseModifiers.bind(this,{allowedModifiers:["const"],disallowedModifiers:["in","out"],errorTemplate:I.InvalidModifierOnTypeParameterPositions}),this.tsParseInOutConstModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out","const"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:I.InvalidModifierOnTypeParameter})}getScopeHandler(){return rh}tsIsIdentifier(){return q(this.state.type)}tsTokenCanFollowModifier(){return(this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(136)||this.isLiteralPropertyName())&&!this.hasPrecedingLineBreak()}tsNextTokenCanFollowModifier(){return this.next(),this.tsTokenCanFollowModifier()}tsParseModifier(e,s){if(!q(this.state.type)&&this.state.type!==58&&this.state.type!==75)return;let i=this.state.value;if(e.indexOf(i)!==-1){if(s&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return i}}tsParseModifiers(e,s){let{allowedModifiers:i,disallowedModifiers:a,stopOnStartOfClassStaticBlock:n,errorTemplate:o=I.InvalidModifierOnTypeMember}=e,u=(y,g,T,C)=>{g===T&&s[C]&&this.raise(I.InvalidModifiersOrder,{at:y,orderedModifiers:[T,C]})},c=(y,g,T,C)=>{(s[T]&&g===C||s[C]&&g===T)&&this.raise(I.IncompatibleModifiers,{at:y,modifiers:[T,C]})};for(;;){let{startLoc:y}=this.state,g=this.tsParseModifier(i.concat(a!=null?a:[]),n);if(!g)break;Hr(g)?s.accessibility?this.raise(I.DuplicateAccessibilityModifier,{at:y,modifier:g}):(u(y,g,g,"override"),u(y,g,g,"static"),u(y,g,g,"readonly"),s.accessibility=g):hh(g)?(s[g]&&this.raise(I.DuplicateModifier,{at:y,modifier:g}),s[g]=!0,u(y,g,"in","out")):(Object.hasOwnProperty.call(s,g)?this.raise(I.DuplicateModifier,{at:y,modifier:g}):(u(y,g,"static","readonly"),u(y,g,"static","override"),u(y,g,"override","readonly"),u(y,g,"abstract","override"),c(y,g,"declare","override"),c(y,g,"static","abstract")),s[g]=!0),a!=null&&a.includes(g)&&this.raise(o,{at:y,modifier:g})}}tsIsListTerminator(e){switch(e){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}}tsParseList(e,s){let i=[];for(;!this.tsIsListTerminator(e);)i.push(s());return i}tsParseDelimitedList(e,s,i){return oh(this.tsParseDelimitedListWorker(e,s,!0,i))}tsParseDelimitedListWorker(e,s,i,a){let n=[],o=-1;for(;!this.tsIsListTerminator(e);){o=-1;let u=s();if(u==null)return;if(n.push(u),this.eat(12)){o=this.state.lastTokStart;continue}if(this.tsIsListTerminator(e))break;i&&this.expect(12);return}return a&&(a.value=o),n}tsParseBracketedList(e,s,i,a,n){a||(i?this.expect(0):this.expect(47));let o=this.tsParseDelimitedList(e,s,n);return i?this.expect(3):this.expect(48),o}tsParseImportType(){let e=this.startNode();return this.expect(83),this.expect(10),this.match(131)||this.raise(I.UnsupportedImportTypeArgument,{at:this.state.startLoc}),e.argument=super.parseExprAtom(),this.expect(11),this.eat(16)&&(e.qualifier=this.tsParseEntityName()),this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSImportType")}tsParseEntityName(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,s=this.parseIdentifier(e);for(;this.eat(16);){let i=this.startNodeAtNode(s);i.left=s,i.right=this.parseIdentifier(e),s=this.finishNode(i,"TSQualifiedName")}return s}tsParseTypeReference(){let e=this.startNode();return e.typeName=this.tsParseEntityName(),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeReference")}tsParseThisTypePredicate(e){this.next();let s=this.startNodeAtNode(e);return s.parameterName=e,s.typeAnnotation=this.tsParseTypeAnnotation(!1),s.asserts=!1,this.finishNode(s,"TSTypePredicate")}tsParseThisTypeNode(){let e=this.startNode();return this.next(),this.finishNode(e,"TSThisType")}tsParseTypeQuery(){let e=this.startNode();return this.expect(87),this.match(83)?e.exprName=this.tsParseImportType():e.exprName=this.tsParseEntityName(),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeQuery")}tsParseTypeParameter(e){let s=this.startNode();return e(s),s.name=this.tsParseTypeParameterName(),s.constraint=this.tsEatThenParseType(81),s.default=this.tsEatThenParseType(29),this.finishNode(s,"TSTypeParameter")}tsTryParseTypeParameters(e){if(this.match(47))return this.tsParseTypeParameters(e)}tsParseTypeParameters(e){let s=this.startNode();this.match(47)||this.match(140)?this.next():this.unexpected();let i={value:-1};return s.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,e),!1,!0,i),s.params.length===0&&this.raise(I.EmptyTypeParameters,{at:s}),i.value!==-1&&this.addExtra(s,"trailingComma",i.value),this.finishNode(s,"TSTypeParameterDeclaration")}tsFillSignature(e,s){let i=e===19,a="parameters",n="typeAnnotation";s.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier),this.expect(10),s[a]=this.tsParseBindingListForSignature(),i?s[n]=this.tsParseTypeOrTypePredicateAnnotation(e):this.match(e)&&(s[n]=this.tsParseTypeOrTypePredicateAnnotation(e))}tsParseBindingListForSignature(){return super.parseBindingList(11,41,2).map(e=>(e.type!=="Identifier"&&e.type!=="RestElement"&&e.type!=="ObjectPattern"&&e.type!=="ArrayPattern"&&this.raise(I.UnsupportedSignatureParameterKind,{at:e,type:e.type}),e))}tsParseTypeMemberSemicolon(){!this.eat(12)&&!this.isLineTerminator()&&this.expect(13)}tsParseSignatureMember(e,s){return this.tsFillSignature(14,s),this.tsParseTypeMemberSemicolon(),this.finishNode(s,e)}tsIsUnambiguouslyIndexSignature(){return this.next(),q(this.state.type)?(this.next(),this.match(14)):!1}tsTryParseIndexSignature(e){if(!(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))))return;this.expect(0);let s=this.parseIdentifier();s.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(s),this.expect(3),e.parameters=[s];let i=this.tsTryParseTypeAnnotation();return i&&(e.typeAnnotation=i),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSIndexSignature")}tsParsePropertyOrMethodSignature(e,s){this.eat(17)&&(e.optional=!0);let i=e;if(this.match(10)||this.match(47)){s&&this.raise(I.ReadonlyForMethodSignature,{at:e});let a=i;a.kind&&this.match(47)&&this.raise(I.AccesorCannotHaveTypeParameters,{at:this.state.curPosition()}),this.tsFillSignature(14,a),this.tsParseTypeMemberSemicolon();let n="parameters",o="typeAnnotation";if(a.kind==="get")a[n].length>0&&(this.raise(f.BadGetterArity,{at:this.state.curPosition()}),this.isThisParam(a[n][0])&&this.raise(I.AccesorCannotDeclareThisParameter,{at:this.state.curPosition()}));else if(a.kind==="set"){if(a[n].length!==1)this.raise(f.BadSetterArity,{at:this.state.curPosition()});else{let u=a[n][0];this.isThisParam(u)&&this.raise(I.AccesorCannotDeclareThisParameter,{at:this.state.curPosition()}),u.type==="Identifier"&&u.optional&&this.raise(I.SetAccesorCannotHaveOptionalParameter,{at:this.state.curPosition()}),u.type==="RestElement"&&this.raise(I.SetAccesorCannotHaveRestParameter,{at:this.state.curPosition()})}a[o]&&this.raise(I.SetAccesorCannotHaveReturnType,{at:a[o]})}else a.kind="method";return this.finishNode(a,"TSMethodSignature")}else{let a=i;s&&(a.readonly=!0);let n=this.tsTryParseTypeAnnotation();return n&&(a.typeAnnotation=n),this.tsParseTypeMemberSemicolon(),this.finishNode(a,"TSPropertySignature")}}tsParseTypeMember(){let e=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",e);if(this.match(77)){let i=this.startNode();return this.next(),this.match(10)||this.match(47)?this.tsParseSignatureMember("TSConstructSignatureDeclaration",e):(e.key=this.createIdentifier(i,"new"),this.tsParsePropertyOrMethodSignature(e,!1))}this.tsParseModifiers({allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]},e);let s=this.tsTryParseIndexSignature(e);return s||(super.parsePropertyName(e),!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&this.tsTokenCanFollowModifier()&&(e.kind=e.key.name,super.parsePropertyName(e)),this.tsParsePropertyOrMethodSignature(e,!!e.readonly))}tsParseTypeLiteral(){let e=this.startNode();return e.members=this.tsParseObjectTypeMembers(),this.finishNode(e,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(5);let e=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),e}tsIsStartOfMappedType(){return this.next(),this.eat(53)?this.isContextual(120):(this.isContextual(120)&&this.next(),!this.match(0)||(this.next(),!this.tsIsIdentifier())?!1:(this.next(),this.match(58)))}tsParseMappedTypeParameter(){let e=this.startNode();return e.name=this.tsParseTypeParameterName(),e.constraint=this.tsExpectThenParseType(58),this.finishNode(e,"TSTypeParameter")}tsParseMappedType(){let e=this.startNode();return this.expect(5),this.match(53)?(e.readonly=this.state.value,this.next(),this.expectContextual(120)):this.eatContextual(120)&&(e.readonly=!0),this.expect(0),e.typeParameter=this.tsParseMappedTypeParameter(),e.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(e.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(e.optional=!0),e.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(e,"TSMappedType")}tsParseTupleType(){let e=this.startNode();e.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let s=!1,i=null;return e.elementTypes.forEach(a=>{var n;let{type:o}=a;s&&o!=="TSRestType"&&o!=="TSOptionalType"&&!(o==="TSNamedTupleMember"&&a.optional)&&this.raise(I.OptionalTypeBeforeRequired,{at:a}),s||(s=o==="TSNamedTupleMember"&&a.optional||o==="TSOptionalType");let u=o;o==="TSRestType"&&(a=a.typeAnnotation,u=a.type);let c=u==="TSNamedTupleMember";(n=i)!=null||(i=c),i!==c&&this.raise(I.MixedLabeledAndUnlabeledElements,{at:a})}),this.finishNode(e,"TSTupleType")}tsParseTupleElementType(){let{startLoc:e}=this.state,s=this.eat(21),i,a,n,o,c=te(this.state.type)?this.lookaheadCharCode():null;if(c===58)i=!0,n=!1,a=this.parseIdentifier(!0),this.expect(14),o=this.tsParseType();else if(c===63){n=!0;let y=this.state.startLoc,g=this.state.value,T=this.tsParseNonArrayType();this.lookaheadCharCode()===58?(i=!0,a=this.createIdentifier(this.startNodeAt(y),g),this.expect(17),this.expect(14),o=this.tsParseType()):(i=!1,o=T,this.expect(17))}else o=this.tsParseType(),n=this.eat(17),i=this.eat(14);if(i){let y;a?(y=this.startNodeAtNode(a),y.optional=n,y.label=a,y.elementType=o,this.eat(17)&&(y.optional=!0,this.raise(I.TupleOptionalAfterType,{at:this.state.lastTokStartLoc}))):(y=this.startNodeAtNode(o),y.optional=n,this.raise(I.InvalidTupleMemberLabel,{at:o}),y.label=o,y.elementType=this.tsParseType()),o=this.finishNode(y,"TSNamedTupleMember")}else if(n){let y=this.startNodeAtNode(o);y.typeAnnotation=o,o=this.finishNode(y,"TSOptionalType")}if(s){let y=this.startNodeAt(e);y.typeAnnotation=o,o=this.finishNode(y,"TSRestType")}return o}tsParseParenthesizedType(){let e=this.startNode();return this.expect(10),e.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(e,"TSParenthesizedType")}tsParseFunctionOrConstructorType(e,s){let i=this.startNode();return e==="TSConstructorType"&&(i.abstract=!!s,s&&this.next(),this.next()),this.tsInAllowConditionalTypesContext(()=>this.tsFillSignature(19,i)),this.finishNode(i,e)}tsParseLiteralTypeNode(){let e=this.startNode();return e.literal=(()=>{switch(this.state.type){case 132:case 133:case 131:case 85:case 86:return super.parseExprAtom();default:this.unexpected()}})(),this.finishNode(e,"TSLiteralType")}tsParseTemplateLiteralType(){let e=this.startNode();return e.literal=super.parseTemplate(!1),this.finishNode(e,"TSLiteralType")}parseTemplateSubstitution(){return this.state.inType?this.tsParseType():super.parseTemplateSubstitution()}tsParseThisTypeOrThisTypePredicate(){let e=this.tsParseThisTypeNode();return this.isContextual(114)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(e):e}tsParseNonArrayType(){switch(this.state.type){case 131:case 132:case 133:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if(this.state.value==="-"){let e=this.startNode(),s=this.lookahead();return s.type!==132&&s.type!==133&&this.unexpected(),e.literal=this.parseMaybeUnary(),this.finishNode(e,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:{let{type:e}=this.state;if(q(e)||e===88||e===84){let s=e===88?"TSVoidKeyword":e===84?"TSNullKeyword":lh(this.state.value);if(s!==void 0&&this.lookaheadCharCode()!==46){let i=this.startNode();return this.next(),this.finishNode(i,s)}return this.tsParseTypeReference()}}}this.unexpected()}tsParseArrayTypeOrHigher(){let e=this.tsParseNonArrayType();for(;!this.hasPrecedingLineBreak()&&this.eat(0);)if(this.match(3)){let s=this.startNodeAtNode(e);s.elementType=e,this.expect(3),e=this.finishNode(s,"TSArrayType")}else{let s=this.startNodeAtNode(e);s.objectType=e,s.indexType=this.tsParseType(),this.expect(3),e=this.finishNode(s,"TSIndexedAccessType")}return e}tsParseTypeOperator(){let e=this.startNode(),s=this.state.value;return this.next(),e.operator=s,e.typeAnnotation=this.tsParseTypeOperatorOrHigher(),s==="readonly"&&this.tsCheckTypeAnnotationForReadOnly(e),this.finishNode(e,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(e){switch(e.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(I.UnexpectedReadonly,{at:e})}}tsParseInferType(){let e=this.startNode();this.expectContextual(113);let s=this.startNode();return s.name=this.tsParseTypeParameterName(),s.constraint=this.tsTryParse(()=>this.tsParseConstraintForInferType()),e.typeParameter=this.finishNode(s,"TSTypeParameter"),this.finishNode(e,"TSInferType")}tsParseConstraintForInferType(){if(this.eat(81)){let e=this.tsInDisallowConditionalTypesContext(()=>this.tsParseType());if(this.state.inDisallowConditionalTypesContext||!this.match(17))return e}}tsParseTypeOperatorOrHigher(){return qo(this.state.type)&&!this.state.containsEsc?this.tsParseTypeOperator():this.isContextual(113)?this.tsParseInferType():this.tsInAllowConditionalTypesContext(()=>this.tsParseArrayTypeOrHigher())}tsParseUnionOrIntersectionType(e,s,i){let a=this.startNode(),n=this.eat(i),o=[];do o.push(s());while(this.eat(i));return o.length===1&&!n?o[0]:(a.types=o,this.finishNode(a,e))}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}tsIsStartOfFunctionType(){return this.match(47)?!0:this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(q(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){let{errors:e}=this.state,s=e.length;try{return this.parseObjectLike(8,!0),e.length===s}catch{return!1}}if(this.match(0)){this.next();let{errors:e}=this.state,s=e.length;try{return super.parseBindingList(3,93,1),e.length===s}catch{return!1}}return!1}tsIsUnambiguouslyStartOfFunctionType(){return this.next(),!!(this.match(11)||this.match(21)||this.tsSkipParameterStart()&&(this.match(14)||this.match(12)||this.match(17)||this.match(29)||this.match(11)&&(this.next(),this.match(19))))}tsParseTypeOrTypePredicateAnnotation(e){return this.tsInType(()=>{let s=this.startNode();this.expect(e);let i=this.startNode(),a=!!this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(a&&this.match(78)){let u=this.tsParseThisTypeOrThisTypePredicate();return u.type==="TSThisType"?(i.parameterName=u,i.asserts=!0,i.typeAnnotation=null,u=this.finishNode(i,"TSTypePredicate")):(this.resetStartLocationFromNode(u,i),u.asserts=!0),s.typeAnnotation=u,this.finishNode(s,"TSTypeAnnotation")}let n=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!n)return a?(i.parameterName=this.parseIdentifier(),i.asserts=a,i.typeAnnotation=null,s.typeAnnotation=this.finishNode(i,"TSTypePredicate"),this.finishNode(s,"TSTypeAnnotation")):this.tsParseTypeAnnotation(!1,s);let o=this.tsParseTypeAnnotation(!1);return i.parameterName=n,i.typeAnnotation=o,i.asserts=a,s.typeAnnotation=this.finishNode(i,"TSTypePredicate"),this.finishNode(s,"TSTypeAnnotation")})}tsTryParseTypeOrTypePredicateAnnotation(){return this.match(14)?this.tsParseTypeOrTypePredicateAnnotation(14):void 0}tsTryParseTypeAnnotation(){return this.match(14)?this.tsParseTypeAnnotation():void 0}tsTryParseType(){return this.tsEatThenParseType(14)}tsParseTypePredicatePrefix(){let e=this.parseIdentifier();if(this.isContextual(114)&&!this.hasPrecedingLineBreak())return this.next(),e}tsParseTypePredicateAsserts(){if(this.state.type!==107)return!1;let e=this.state.containsEsc;return this.next(),!q(this.state.type)&&!this.match(78)?!1:(e&&this.raise(f.InvalidEscapedReservedWord,{at:this.state.lastTokStartLoc,reservedWord:"asserts"}),!0)}tsParseTypeAnnotation(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.startNode();return this.tsInType(()=>{e&&this.expect(14),s.typeAnnotation=this.tsParseType()}),this.finishNode(s,"TSTypeAnnotation")}tsParseType(){$r(this.state.inType);let e=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return e;let s=this.startNodeAtNode(e);return s.checkType=e,s.extendsType=this.tsInDisallowConditionalTypesContext(()=>this.tsParseNonConditionalType()),this.expect(17),s.trueType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.expect(14),s.falseType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.finishNode(s,"TSConditionalType")}isAbstractConstructorSignature(){return this.isContextual(122)&&this.lookahead().type===77}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(I.ReservedTypeAssertion,{at:this.state.startLoc});let e=this.startNode();return e.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?this.tsParseTypeReference():this.tsParseType())),this.expect(48),e.expression=this.parseMaybeUnary(),this.finishNode(e,"TSTypeAssertion")}tsParseHeritageClause(e){let s=this.state.startLoc,i=this.tsParseDelimitedList("HeritageClauseElement",()=>{let a=this.startNode();return a.expression=this.tsParseEntityName(),this.match(47)&&(a.typeParameters=this.tsParseTypeArguments()),this.finishNode(a,"TSExpressionWithTypeArguments")});return i.length||this.raise(I.EmptyHeritageClauseType,{at:s,token:e}),i}tsParseInterfaceDeclaration(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.hasFollowingLineBreak())return null;this.expectContextual(127),s.declare&&(e.declare=!0),q(this.state.type)?(e.id=this.parseIdentifier(),this.checkIdentifier(e.id,pl)):(e.id=null,this.raise(I.MissingInterfaceName,{at:this.state.startLoc})),e.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers),this.eat(81)&&(e.extends=this.tsParseHeritageClause("extends"));let i=this.startNode();return i.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),e.body=this.finishNode(i,"TSInterfaceBody"),this.finishNode(e,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(e){return e.id=this.parseIdentifier(),this.checkIdentifier(e.id,fl),e.typeAnnotation=this.tsInType(()=>{if(e.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutModifiers),this.expect(29),this.isContextual(112)&&this.lookahead().type!==16){let s=this.startNode();return this.next(),this.finishNode(s,"TSIntrinsicKeyword")}return this.tsParseType()}),this.semicolon(),this.finishNode(e,"TSTypeAliasDeclaration")}tsInNoContext(e){let s=this.state.context;this.state.context=[s[0]];try{return e()}finally{this.state.context=s}}tsInType(e){let s=this.state.inType;this.state.inType=!0;try{return e()}finally{this.state.inType=s}}tsInDisallowConditionalTypesContext(e){let s=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return e()}finally{this.state.inDisallowConditionalTypesContext=s}}tsInAllowConditionalTypesContext(e){let s=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return e()}finally{this.state.inDisallowConditionalTypesContext=s}}tsEatThenParseType(e){return this.match(e)?this.tsNextThenParseType():void 0}tsExpectThenParseType(e){return this.tsDoThenParseType(()=>this.expect(e))}tsNextThenParseType(){return this.tsDoThenParseType(()=>this.next())}tsDoThenParseType(e){return this.tsInType(()=>(e(),this.tsParseType()))}tsParseEnumMember(){let e=this.startNode();return e.id=this.match(131)?super.parseStringLiteral(this.state.value):this.parseIdentifier(!0),this.eat(29)&&(e.initializer=super.parseMaybeAssignAllowIn()),this.finishNode(e,"TSEnumMember")}tsParseEnumDeclaration(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return s.const&&(e.const=!0),s.declare&&(e.declare=!0),this.expectContextual(124),e.id=this.parseIdentifier(),this.checkIdentifier(e.id,e.const?ml:Cr),this.expect(5),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(e,"TSEnumDeclaration")}tsParseModuleBlock(){let e=this.startNode();return this.scope.enter(Fe),this.expect(5),super.parseBlockOrModuleBlockBody(e.body=[],void 0,!0,8),this.scope.exit(),this.finishNode(e,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(e.id=this.parseIdentifier(),s||this.checkIdentifier(e.id,yl),this.eat(16)){let i=this.startNode();this.tsParseModuleOrNamespaceDeclaration(i,!0),e.body=i}else this.scope.enter(Oe),this.prodParam.enter(Me),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(e,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(e){return this.isContextual(110)?(e.global=!0,e.id=this.parseIdentifier()):this.match(131)?e.id=super.parseStringLiteral(this.state.value):this.unexpected(),this.match(5)?(this.scope.enter(Oe),this.prodParam.enter(Me),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(e,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(e,s){e.isExport=s||!1,e.id=this.parseIdentifier(),this.checkIdentifier(e.id,Ve),this.expect(29);let i=this.tsParseModuleReference();return e.importKind==="type"&&i.type!=="TSExternalModuleReference"&&this.raise(I.ImportAliasHasImportType,{at:i}),e.moduleReference=i,this.semicolon(),this.finishNode(e,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual(117)&&this.lookaheadCharCode()===40}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(!1)}tsParseExternalModuleReference(){let e=this.startNode();return this.expectContextual(117),this.expect(10),this.match(131)||this.unexpected(),e.expression=super.parseExprAtom(),this.expect(11),this.finishNode(e,"TSExternalModuleReference")}tsLookAhead(e){let s=this.state.clone(),i=e();return this.state=s,i}tsTryParseAndCatch(e){let s=this.tryParse(i=>e()||i());if(!(s.aborted||!s.node))return s.error&&(this.state=s.failState),s.node}tsTryParse(e){let s=this.state.clone(),i=e();if(i!==void 0&&i!==!1)return i;this.state=s}tsTryParseDeclare(e){if(this.isLineTerminator())return;let s=this.state.type,i;return this.isContextual(99)&&(s=74,i="let"),this.tsInAmbientContext(()=>{if(s===68)return e.declare=!0,super.parseFunctionStatement(e,!1,!1);if(s===80)return e.declare=!0,this.parseClass(e,!0,!1);if(s===124)return this.tsParseEnumDeclaration(e,{declare:!0});if(s===110)return this.tsParseAmbientExternalModuleDeclaration(e);if(s===75||s===74)return!this.match(75)||!this.isLookaheadContextual("enum")?(e.declare=!0,this.parseVarStatement(e,i||this.state.value,!0)):(this.expect(75),this.tsParseEnumDeclaration(e,{const:!0,declare:!0}));if(s===127){let a=this.tsParseInterfaceDeclaration(e,{declare:!0});if(a)return a}if(q(s))return this.tsParseDeclaration(e,this.state.value,!0,null)})}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}tsParseExpressionStatement(e,s,i){switch(s.name){case"declare":{let a=this.tsTryParseDeclare(e);if(a)return a.declare=!0,a;break}case"global":if(this.match(5)){this.scope.enter(Oe),this.prodParam.enter(Me);let a=e;return a.global=!0,a.id=s,a.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(a,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(e,s.name,!1,i)}}tsParseDeclaration(e,s,i,a){switch(s){case"abstract":if(this.tsCheckLineTerminator(i)&&(this.match(80)||q(this.state.type)))return this.tsParseAbstractDeclaration(e,a);break;case"module":if(this.tsCheckLineTerminator(i)){if(this.match(131))return this.tsParseAmbientExternalModuleDeclaration(e);if(q(this.state.type))return this.tsParseModuleOrNamespaceDeclaration(e)}break;case"namespace":if(this.tsCheckLineTerminator(i)&&q(this.state.type))return this.tsParseModuleOrNamespaceDeclaration(e);break;case"type":if(this.tsCheckLineTerminator(i)&&q(this.state.type))return this.tsParseTypeAliasDeclaration(e);break}}tsCheckLineTerminator(e){return e?this.hasFollowingLineBreak()?!1:(this.next(),!0):!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(e){if(!this.match(47))return;let s=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;let i=this.tsTryParseAndCatch(()=>{let a=this.startNodeAt(e);return a.typeParameters=this.tsParseTypeParameters(this.tsParseConstModifier),super.parseFunctionParams(a),a.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(19),a});if(this.state.maybeInArrowParameters=s,!!i)return super.parseArrowExpression(i,null,!0)}tsParseTypeArgumentsInExpression(){if(this.reScan_lt()===47)return this.tsParseTypeArguments()}tsParseTypeArguments(){let e=this.startNode();return e.params=this.tsInType(()=>this.tsInNoContext(()=>(this.expect(47),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))),e.params.length===0&&this.raise(I.EmptyTypeArguments,{at:e}),this.expect(48),this.finishNode(e,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){return Uo(this.state.type)}isExportDefaultSpecifier(){return this.tsIsDeclarationStart()?!1:super.isExportDefaultSpecifier()}parseAssignableListItem(e,s){let i=this.state.startLoc,a={};this.tsParseModifiers({allowedModifiers:["public","private","protected","override","readonly"]},a);let n=a.accessibility,o=a.override,u=a.readonly;!(e&4)&&(n||u||o)&&this.raise(I.UnexpectedParameterModifier,{at:i});let c=this.parseMaybeDefault();this.parseAssignableListItemTypes(c,e);let y=this.parseMaybeDefault(c.loc.start,c);if(n||u||o){let g=this.startNodeAt(i);return s.length&&(g.decorators=s),n&&(g.accessibility=n),u&&(g.readonly=u),o&&(g.override=o),y.type!=="Identifier"&&y.type!=="AssignmentPattern"&&this.raise(I.UnsupportedParameterPropertyKind,{at:g}),g.parameter=y,this.finishNode(g,"TSParameterProperty")}return s.length&&(c.decorators=s),y}isSimpleParameter(e){return e.type==="TSParameterProperty"&&super.isSimpleParameter(e.parameter)||super.isSimpleParameter(e)}tsDisallowOptionalPattern(e){for(let s of e.params)s.type!=="Identifier"&&s.optional&&!this.state.isAmbientContext&&this.raise(I.PatternIsOptional,{at:s})}setArrowFunctionParameters(e,s,i){super.setArrowFunctionParameters(e,s,i),this.tsDisallowOptionalPattern(e)}parseFunctionBodyAndFinish(e,s){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.match(14)&&(e.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));let a=s==="FunctionDeclaration"?"TSDeclareFunction":s==="ClassMethod"||s==="ClassPrivateMethod"?"TSDeclareMethod":void 0;return a&&!this.match(5)&&this.isLineTerminator()?this.finishNode(e,a):a==="TSDeclareFunction"&&this.state.isAmbientContext&&(this.raise(I.DeclareFunctionHasImplementation,{at:e}),e.declare)?super.parseFunctionBodyAndFinish(e,a,i):(this.tsDisallowOptionalPattern(e),super.parseFunctionBodyAndFinish(e,s,i))}registerFunctionStatementId(e){!e.body&&e.id?this.checkIdentifier(e.id,br):super.registerFunctionStatementId(e)}tsCheckForInvalidTypeCasts(e){e.forEach(s=>{(s==null?void 0:s.type)==="TSTypeCastExpression"&&this.raise(I.UnexpectedTypeAnnotation,{at:s.typeAnnotation})})}toReferencedList(e,s){return this.tsCheckForInvalidTypeCasts(e),e}parseArrayLike(e,s,i,a){let n=super.parseArrayLike(e,s,i,a);return n.type==="ArrayExpression"&&this.tsCheckForInvalidTypeCasts(n.elements),n}parseSubscript(e,s,i,a){if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();let o=this.startNodeAt(s);return o.expression=e,this.finishNode(o,"TSNonNullExpression")}let n=!1;if(this.match(18)&&this.lookaheadCharCode()===60){if(i)return a.stop=!0,e;a.optionalChainMember=n=!0,this.next()}if(this.match(47)||this.match(51)){let o,u=this.tsTryParseAndCatch(()=>{if(!i&&this.atPossibleAsyncArrow(e)){let T=this.tsTryParseGenericAsyncArrowFunction(s);if(T)return T}let c=this.tsParseTypeArgumentsInExpression();if(!c)return;if(n&&!this.match(10)){o=this.state.curPosition();return}if(nt(this.state.type)){let T=super.parseTaggedTemplateExpression(e,s,a);return T.typeParameters=c,T}if(!i&&this.eat(10)){let T=this.startNodeAt(s);return T.callee=e,T.arguments=this.parseCallExpressionArguments(11,!1),this.tsCheckForInvalidTypeCasts(T.arguments),T.typeParameters=c,a.optionalChainMember&&(T.optional=n),this.finishCallExpression(T,a.optionalChainMember)}let y=this.state.type;if(y===48||y===52||y!==10&&He(y)&&!this.hasPrecedingLineBreak())return;let g=this.startNodeAt(s);return g.expression=e,g.typeParameters=c,this.finishNode(g,"TSInstantiationExpression")});if(o&&this.unexpected(o,10),u)return u.type==="TSInstantiationExpression"&&(this.match(16)||this.match(18)&&this.lookaheadCharCode()!==40)&&this.raise(I.InvalidPropertyAccessAfterInstantiationExpression,{at:this.state.startLoc}),u}return super.parseSubscript(e,s,i,a)}parseNewCallee(e){var s;super.parseNewCallee(e);let{callee:i}=e;i.type==="TSInstantiationExpression"&&!((s=i.extra)!=null&&s.parenthesized)&&(e.typeParameters=i.typeParameters,e.callee=i.expression)}parseExprOp(e,s,i){let a;if(at(58)>i&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(a=this.isContextual(118)))){let n=this.startNodeAt(s);return n.expression=e,n.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?(a&&this.raise(f.UnexpectedKeyword,{at:this.state.startLoc,keyword:"const"}),this.tsParseTypeReference()):this.tsParseType())),this.finishNode(n,a?"TSSatisfiesExpression":"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(n,s,i)}return super.parseExprOp(e,s,i)}checkReservedWord(e,s,i,a){this.state.isAmbientContext||super.checkReservedWord(e,s,i,a)}checkImportReflection(e){super.checkImportReflection(e),e.module&&e.importKind!=="value"&&this.raise(I.ImportReflectionHasImportType,{at:e.specifiers[0].loc.start})}checkDuplicateExports(){}parseImport(e){if(e.importKind="value",q(this.state.type)||this.match(55)||this.match(5)){let i=this.lookahead();if(this.isContextual(128)&&i.type!==12&&i.type!==97&&i.type!==29&&(e.importKind="type",this.next(),i=this.lookahead()),q(this.state.type)&&i.type===29)return this.tsParseImportEqualsDeclaration(e)}let s=super.parseImport(e);return s.importKind==="type"&&s.specifiers.length>1&&s.specifiers[0].type==="ImportDefaultSpecifier"&&this.raise(I.TypeImportCannotSpecifyDefaultAndNamed,{at:s}),s}parseExport(e,s){if(this.match(83))return this.next(),this.isContextual(128)&&this.lookaheadCharCode()!==61?(e.importKind="type",this.next()):e.importKind="value",this.tsParseImportEqualsDeclaration(e,!0);if(this.eat(29)){let i=e;return i.expression=super.parseExpression(),this.semicolon(),this.finishNode(i,"TSExportAssignment")}else if(this.eatContextual(93)){let i=e;return this.expectContextual(126),i.id=this.parseIdentifier(),this.semicolon(),this.finishNode(i,"TSNamespaceExportDeclaration")}else{if(e.exportKind="value",this.isContextual(128)){let i=this.lookaheadCharCode();(i===123||i===42)&&(this.next(),e.exportKind="type")}return super.parseExport(e,s)}}isAbstractClass(){return this.isContextual(122)&&this.lookahead().type===80}parseExportDefaultExpression(){if(this.isAbstractClass()){let e=this.startNode();return this.next(),e.abstract=!0,this.parseClass(e,!0,!0)}if(this.match(127)){let e=this.tsParseInterfaceDeclaration(this.startNode());if(e)return e}return super.parseExportDefaultExpression()}parseVarStatement(e,s){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,{isAmbientContext:a}=this.state,n=super.parseVarStatement(e,s,i||a);if(!a)return n;for(let{id:o,init:u}of n.declarations)u&&(s!=="const"||o.typeAnnotation?this.raise(I.InitializerNotAllowedInAmbientContext,{at:u}):ph(u,this.hasPlugin("estree"))||this.raise(I.ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference,{at:u}));return n}parseStatementContent(e,s){if(this.match(75)&&this.isLookaheadContextual("enum")){let i=this.startNode();return this.expect(75),this.tsParseEnumDeclaration(i,{const:!0})}if(this.isContextual(124))return this.tsParseEnumDeclaration(this.startNode());if(this.isContextual(127)){let i=this.tsParseInterfaceDeclaration(this.startNode());if(i)return i}return super.parseStatementContent(e,s)}parseAccessModifier(){return this.tsParseModifier(["public","protected","private"])}tsHasSomeModifiers(e,s){return s.some(i=>Hr(i)?e.accessibility===i:!!e[i])}tsIsStartOfStaticBlocks(){return this.isContextual(104)&&this.lookaheadCharCode()===123}parseClassMember(e,s,i){let a=["declare","private","public","protected","override","abstract","readonly","static"];this.tsParseModifiers({allowedModifiers:a,disallowedModifiers:["in","out"],stopOnStartOfClassStaticBlock:!0,errorTemplate:I.InvalidModifierOnTypeParameterPositions},s);let n=()=>{this.tsIsStartOfStaticBlocks()?(this.next(),this.next(),this.tsHasSomeModifiers(s,a)&&this.raise(I.StaticBlockCannotHaveModifier,{at:this.state.curPosition()}),super.parseClassStaticBlock(e,s)):this.parseClassMemberWithIsStatic(e,s,i,!!s.static)};s.declare?this.tsInAmbientContext(n):n()}parseClassMemberWithIsStatic(e,s,i,a){let n=this.tsTryParseIndexSignature(s);if(n){e.body.push(n),s.abstract&&this.raise(I.IndexSignatureHasAbstract,{at:s}),s.accessibility&&this.raise(I.IndexSignatureHasAccessibility,{at:s,modifier:s.accessibility}),s.declare&&this.raise(I.IndexSignatureHasDeclare,{at:s}),s.override&&this.raise(I.IndexSignatureHasOverride,{at:s});return}!this.state.inAbstractClass&&s.abstract&&this.raise(I.NonAbstractClassHasAbstractMethod,{at:s}),s.override&&(i.hadSuperClass||this.raise(I.OverrideNotInSubClass,{at:s})),super.parseClassMemberWithIsStatic(e,s,i,a)}parsePostMemberNameModifiers(e){this.eat(17)&&(e.optional=!0),e.readonly&&this.match(10)&&this.raise(I.ClassMethodHasReadonly,{at:e}),e.declare&&this.match(10)&&this.raise(I.ClassMethodHasDeclare,{at:e})}parseExpressionStatement(e,s,i){return(s.type==="Identifier"?this.tsParseExpressionStatement(e,s,i):void 0)||super.parseExpressionStatement(e,s,i)}shouldParseExportDeclaration(){return this.tsIsDeclarationStart()?!0:super.shouldParseExportDeclaration()}parseConditional(e,s,i){if(!this.state.maybeInArrowParameters||!this.match(17))return super.parseConditional(e,s,i);let a=this.tryParse(()=>super.parseConditional(e,s));return a.node?(a.error&&(this.state=a.failState),a.node):(a.error&&super.setOptionalParametersError(i,a.error),e)}parseParenItem(e,s){if(e=super.parseParenItem(e,s),this.eat(17)&&(e.optional=!0,this.resetEndLocation(e)),this.match(14)){let i=this.startNodeAt(s);return i.expression=e,i.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(i,"TSTypeCastExpression")}return e}parseExportDeclaration(e){if(!this.state.isAmbientContext&&this.isContextual(123))return this.tsInAmbientContext(()=>this.parseExportDeclaration(e));let s=this.state.startLoc,i=this.eatContextual(123);if(i&&(this.isContextual(123)||!this.shouldParseExportDeclaration()))throw this.raise(I.ExpectedAmbientAfterExportDeclare,{at:this.state.startLoc});let n=q(this.state.type)&&this.tsTryParseExportDeclaration()||super.parseExportDeclaration(e);return n?((n.type==="TSInterfaceDeclaration"||n.type==="TSTypeAliasDeclaration"||i)&&(e.exportKind="type"),i&&(this.resetStartLocation(n,s),n.declare=!0),n):null}parseClassId(e,s,i,a){if((!s||i)&&this.isContextual(111))return;super.parseClassId(e,s,i,e.declare?br:vr);let n=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);n&&(e.typeParameters=n)}parseClassPropertyAnnotation(e){e.optional||(this.eat(35)?e.definite=!0:this.eat(17)&&(e.optional=!0));let s=this.tsTryParseTypeAnnotation();s&&(e.typeAnnotation=s)}parseClassProperty(e){if(this.parseClassPropertyAnnotation(e),this.state.isAmbientContext&&!(e.readonly&&!e.typeAnnotation)&&this.match(29)&&this.raise(I.DeclareClassFieldHasInitializer,{at:this.state.startLoc}),e.abstract&&this.match(29)){let{key:s}=e;this.raise(I.AbstractPropertyHasInitializer,{at:this.state.startLoc,propertyName:s.type==="Identifier"&&!e.computed?s.name:`[${this.input.slice(s.start,s.end)}]`})}return super.parseClassProperty(e)}parseClassPrivateProperty(e){return e.abstract&&this.raise(I.PrivateElementHasAbstract,{at:e}),e.accessibility&&this.raise(I.PrivateElementHasAccessibility,{at:e,modifier:e.accessibility}),this.parseClassPropertyAnnotation(e),super.parseClassPrivateProperty(e)}parseClassAccessorProperty(e){return this.parseClassPropertyAnnotation(e),e.optional&&this.raise(I.AccessorCannotBeOptional,{at:e}),super.parseClassAccessorProperty(e)}pushClassMethod(e,s,i,a,n,o){let u=this.tsTryParseTypeParameters(this.tsParseConstModifier);u&&n&&this.raise(I.ConstructorHasTypeParameters,{at:u});let{declare:c=!1,kind:y}=s;c&&(y==="get"||y==="set")&&this.raise(I.DeclareAccessor,{at:s,kind:y}),u&&(s.typeParameters=u),super.pushClassMethod(e,s,i,a,n,o)}pushClassPrivateMethod(e,s,i,a){let n=this.tsTryParseTypeParameters(this.tsParseConstModifier);n&&(s.typeParameters=n),super.pushClassPrivateMethod(e,s,i,a)}declareClassPrivateMethodInScope(e,s){e.type!=="TSDeclareMethod"&&(e.type==="MethodDefinition"&&!e.value.body||super.declareClassPrivateMethodInScope(e,s))}parseClassSuper(e){super.parseClassSuper(e),e.superClass&&(this.match(47)||this.match(51))&&(e.superTypeParameters=this.tsParseTypeArgumentsInExpression()),this.eatContextual(111)&&(e.implements=this.tsParseHeritageClause("implements"))}parseObjPropValue(e,s,i,a,n,o,u){let c=this.tsTryParseTypeParameters(this.tsParseConstModifier);return c&&(e.typeParameters=c),super.parseObjPropValue(e,s,i,a,n,o,u)}parseFunctionParams(e,s){let i=this.tsTryParseTypeParameters(this.tsParseConstModifier);i&&(e.typeParameters=i),super.parseFunctionParams(e,s)}parseVarId(e,s){super.parseVarId(e,s),e.id.type==="Identifier"&&!this.hasPrecedingLineBreak()&&this.eat(35)&&(e.definite=!0);let i=this.tsTryParseTypeAnnotation();i&&(e.id.typeAnnotation=i,this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,s){return this.match(14)&&(e.returnType=this.tsParseTypeAnnotation()),super.parseAsyncArrowFromCallExpression(e,s)}parseMaybeAssign(e,s){var i,a,n,o,u,c,y;let g,T,C;if(this.hasPlugin("jsx")&&(this.match(140)||this.match(47))){if(g=this.state.clone(),T=this.tryParse(()=>super.parseMaybeAssign(e,s),g),!T.error)return T.node;let{context:K}=this.state,W=K[K.length-1];(W===x.j_oTag||W===x.j_expr)&&K.pop()}if(!((i=T)!=null&&i.error)&&!this.match(47))return super.parseMaybeAssign(e,s);(!g||g===this.state)&&(g=this.state.clone());let M,j=this.tryParse(K=>{var W,V;M=this.tsParseTypeParameters(this.tsParseConstModifier);let X=super.parseMaybeAssign(e,s);return(X.type!=="ArrowFunctionExpression"||(W=X.extra)!=null&&W.parenthesized)&&K(),((V=M)==null?void 0:V.params.length)!==0&&this.resetStartLocationFromNode(X,M),X.typeParameters=M,X},g);if(!j.error&&!j.aborted)return M&&this.reportReservedArrowTypeParam(M),j.node;if(!T&&($r(!this.hasPlugin("jsx")),C=this.tryParse(()=>super.parseMaybeAssign(e,s),g),!C.error))return C.node;if((a=T)!=null&&a.node)return this.state=T.failState,T.node;if(j.node)return this.state=j.failState,M&&this.reportReservedArrowTypeParam(M),j.node;if((n=C)!=null&&n.node)return this.state=C.failState,C.node;throw(o=T)!=null&&o.thrown?T.error:j.thrown?j.error:(u=C)!=null&&u.thrown?C.error:((c=T)==null?void 0:c.error)||j.error||((y=C)==null?void 0:y.error)}reportReservedArrowTypeParam(e){var s;e.params.length===1&&!e.params[0].constraint&&!((s=e.extra)!=null&&s.trailingComma)&&this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(I.ReservedArrowTypeParam,{at:e})}parseMaybeUnary(e,s){return!this.hasPlugin("jsx")&&this.match(47)?this.tsParseTypeAssertion():super.parseMaybeUnary(e,s)}parseArrow(e){if(this.match(14)){let s=this.tryParse(i=>{let a=this.tsParseTypeOrTypePredicateAnnotation(14);return(this.canInsertSemicolon()||!this.match(19))&&i(),a});if(s.aborted)return;s.thrown||(s.error&&(this.state=s.failState),e.returnType=s.node)}return super.parseArrow(e)}parseAssignableListItemTypes(e,s){if(!(s&2))return e;this.eat(17)&&(e.optional=!0);let i=this.tsTryParseTypeAnnotation();return i&&(e.typeAnnotation=i),this.resetEndLocation(e),e}isAssignable(e,s){switch(e.type){case"TSTypeCastExpression":return this.isAssignable(e.expression,s);case"TSParameterProperty":return!0;default:return super.isAssignable(e,s)}}toAssignable(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;switch(e.type){case"ParenthesizedExpression":this.toAssignableParenthesizedExpression(e,s);break;case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":s?this.expressionScope.recordArrowParameterBindingError(I.UnexpectedTypeCastInParameter,{at:e}):this.raise(I.UnexpectedTypeCastInParameter,{at:e}),this.toAssignable(e.expression,s);break;case"AssignmentExpression":!s&&e.left.type==="TSTypeCastExpression"&&(e.left=this.typeCastToParameter(e.left));default:super.toAssignable(e,s)}}toAssignableParenthesizedExpression(e,s){switch(e.expression.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":this.toAssignable(e.expression,s);break;default:super.toAssignable(e,s)}}checkToRestConversion(e,s){switch(e.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":this.checkToRestConversion(e.expression,!1);break;default:super.checkToRestConversion(e,s)}}isValidLVal(e,s,i){return nh({TSTypeCastExpression:!0,TSParameterProperty:"parameter",TSNonNullExpression:"expression",TSAsExpression:(i!==Pe||!s)&&["expression",!0],TSSatisfiesExpression:(i!==Pe||!s)&&["expression",!0],TSTypeAssertion:(i!==Pe||!s)&&["expression",!0]},e)||super.isValidLVal(e,s,i)}parseBindingAtom(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return super.parseBindingAtom()}}parseMaybeDecoratorArguments(e){if(this.match(47)||this.match(51)){let s=this.tsParseTypeArgumentsInExpression();if(this.match(10)){let i=super.parseMaybeDecoratorArguments(e);return i.typeParameters=s,i}this.unexpected(null,10)}return super.parseMaybeDecoratorArguments(e)}checkCommaAfterRest(e){return this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===e?(this.next(),!1):super.checkCommaAfterRest(e)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(35)||this.match(14)||super.isClassProperty()}parseMaybeDefault(e,s){let i=super.parseMaybeDefault(e,s);return i.type==="AssignmentPattern"&&i.typeAnnotation&&i.right.start<i.typeAnnotation.start&&this.raise(I.TypeAnnotationAfterAssign,{at:i.typeAnnotation}),i}getTokenFromCode(e){if(this.state.inType){if(e===62){this.finishOp(48,1);return}if(e===60){this.finishOp(47,1);return}}super.getTokenFromCode(e)}reScan_lt_gt(){let{type:e}=this.state;e===47?(this.state.pos-=1,this.readToken_lt()):e===48&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){let{type:e}=this.state;return e===51?(this.state.pos-=2,this.finishOp(47,1),47):e}toAssignableList(e,s,i){for(let a=0;a<e.length;a++){let n=e[a];(n==null?void 0:n.type)==="TSTypeCastExpression"&&(e[a]=this.typeCastToParameter(n))}super.toAssignableList(e,s,i)}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}shouldParseArrow(e){return this.match(14)?e.every(s=>this.isAssignable(s,!0)):super.shouldParseArrow(e)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}canHaveLeadingDecorator(){return super.canHaveLeadingDecorator()||this.isAbstractClass()}jsxParseOpeningElementAfterName(e){if(this.match(47)||this.match(51)){let s=this.tsTryParseAndCatch(()=>this.tsParseTypeArgumentsInExpression());s&&(e.typeParameters=s)}return super.jsxParseOpeningElementAfterName(e)}getGetterSetterExpectedParamCount(e){let s=super.getGetterSetterExpectedParamCount(e),a=this.getObjectOrClassMethodParams(e)[0];return a&&this.isThisParam(a)?s+1:s}parseCatchClauseParam(){let e=super.parseCatchClauseParam(),s=this.tsTryParseTypeAnnotation();return s&&(e.typeAnnotation=s,this.resetEndLocation(e)),e}tsInAmbientContext(e){let s=this.state.isAmbientContext;this.state.isAmbientContext=!0;try{return e()}finally{this.state.isAmbientContext=s}}parseClass(e,s,i){let a=this.state.inAbstractClass;this.state.inAbstractClass=!!e.abstract;try{return super.parseClass(e,s,i)}finally{this.state.inAbstractClass=a}}tsParseAbstractDeclaration(e,s){if(this.match(80))return e.abstract=!0,this.maybeTakeDecorators(s,this.parseClass(e,!0,!1));if(this.isContextual(127)){if(!this.hasFollowingLineBreak())return e.abstract=!0,this.raise(I.NonClassMethodPropertyHasAbstractModifer,{at:e}),this.tsParseInterfaceDeclaration(e)}else this.unexpected(null,80)}parseMethod(e,s,i,a,n,o,u){let c=super.parseMethod(e,s,i,a,n,o,u);if(c.abstract&&(this.hasPlugin("estree")?!!c.value.body:!!c.body)){let{key:g}=c;this.raise(I.AbstractMethodHasImplementation,{at:c,methodName:g.type==="Identifier"&&!c.computed?g.name:`[${this.input.slice(g.start,g.end)}]`})}return c}tsParseTypeParameterName(){return this.parseIdentifier().name}shouldParseAsAmbientContext(){return!!this.getPluginOption("typescript","dts")}parse(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.parse()}getExpression(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.getExpression()}parseExportSpecifier(e,s,i,a){return!s&&a?(this.parseTypeOnlyImportExportSpecifier(e,!1,i),this.finishNode(e,"ExportSpecifier")):(e.exportKind="value",super.parseExportSpecifier(e,s,i,a))}parseImportSpecifier(e,s,i,a,n){return!s&&a?(this.parseTypeOnlyImportExportSpecifier(e,!0,i),this.finishNode(e,"ImportSpecifier")):(e.importKind="value",super.parseImportSpecifier(e,s,i,a,i?Sr:Ve))}parseTypeOnlyImportExportSpecifier(e,s,i){let a=s?"imported":"local",n=s?"local":"exported",o=e[a],u,c=!1,y=!0,g=o.loc.start;if(this.isContextual(93)){let C=this.parseIdentifier();if(this.isContextual(93)){let M=this.parseIdentifier();te(this.state.type)?(c=!0,o=C,u=s?this.parseIdentifier():this.parseModuleExportName(),y=!1):(u=M,y=!1)}else te(this.state.type)?(y=!1,u=s?this.parseIdentifier():this.parseModuleExportName()):(c=!0,o=C)}else te(this.state.type)&&(c=!0,s?(o=this.parseIdentifier(!0),this.isContextual(93)||this.checkReservedWord(o.name,o.loc.start,!0,!0)):o=this.parseModuleExportName());c&&i&&this.raise(s?I.TypeModifierIsUsedInTypeImports:I.TypeModifierIsUsedInTypeExports,{at:g}),e[a]=o,e[n]=u;let T=s?"importKind":"exportKind";e[T]=c?"type":"value",y&&this.eatContextual(93)&&(e[n]=s?this.parseIdentifier():this.parseModuleExportName()),e[n]||(e[n]=me(e[a])),s&&this.checkIdentifier(e[n],c?Sr:Ve)}};function ch(t){if(t.type!=="MemberExpression")return!1;let{computed:r,property:e}=t;return r&&e.type!=="StringLiteral"&&(e.type!=="TemplateLiteral"||e.expressions.length>0)?!1:Vr(t.object)}function ph(t,r){var e;let{type:s}=t;if((e=t.extra)!=null&&e.parenthesized)return!1;if(r){if(s==="Literal"){let{value:i}=t;if(typeof i=="string"||typeof i=="boolean")return!0}}else if(s==="StringLiteral"||s==="BooleanLiteral")return!0;return!!(zr(t,r)||fh(t,r)||s==="TemplateLiteral"&&t.expressions.length===0||ch(t))}function zr(t,r){return r?t.type==="Literal"&&(typeof t.value=="number"||"bigint"in t):t.type==="NumericLiteral"||t.type==="BigIntLiteral"}function fh(t,r){if(t.type==="UnaryExpression"){let{operator:e,argument:s}=t;if(e==="-"&&zr(s,r))return!0}return!1}function Vr(t){return t.type==="Identifier"?!0:t.type!=="MemberExpression"||t.computed?!1:Vr(t.object)}var Kr=pe`placeholders`({ClassNameIsRequired:"A class name is required.",UnexpectedSpace:"Unexpected space in placeholder."}),dh=t=>class extends t{parsePlaceholder(e){if(this.match(142)){let s=this.startNode();return this.next(),this.assertNoSpace(),s.name=super.parseIdentifier(!0),this.assertNoSpace(),this.expect(142),this.finishPlaceholder(s,e)}}finishPlaceholder(e,s){let i=!!(e.expectedNode&&e.type==="Placeholder");return e.expectedNode=s,i?e:this.finishNode(e,"Placeholder")}getTokenFromCode(e){e===37&&this.input.charCodeAt(this.state.pos+1)===37?this.finishOp(142,2):super.getTokenFromCode(e)}parseExprAtom(e){return this.parsePlaceholder("Expression")||super.parseExprAtom(e)}parseIdentifier(e){return this.parsePlaceholder("Identifier")||super.parseIdentifier(e)}checkReservedWord(e,s,i,a){e!==void 0&&super.checkReservedWord(e,s,i,a)}parseBindingAtom(){return this.parsePlaceholder("Pattern")||super.parseBindingAtom()}isValidLVal(e,s,i){return e==="Placeholder"||super.isValidLVal(e,s,i)}toAssignable(e,s){e&&e.type==="Placeholder"&&e.expectedNode==="Expression"?e.expectedNode="Pattern":super.toAssignable(e,s)}chStartsBindingIdentifier(e,s){return!!(super.chStartsBindingIdentifier(e,s)||this.lookahead().type===142)}verifyBreakContinue(e,s){e.label&&e.label.type==="Placeholder"||super.verifyBreakContinue(e,s)}parseExpressionStatement(e,s){if(s.type!=="Placeholder"||s.extra&&s.extra.parenthesized)return super.parseExpressionStatement(e,s);if(this.match(14)){let i=e;return i.label=this.finishPlaceholder(s,"Identifier"),this.next(),i.body=super.parseStatementOrSloppyAnnexBFunctionDeclaration(),this.finishNode(i,"LabeledStatement")}return this.semicolon(),e.name=s.name,this.finishPlaceholder(e,"Statement")}parseBlock(e,s,i){return this.parsePlaceholder("BlockStatement")||super.parseBlock(e,s,i)}parseFunctionId(e){return this.parsePlaceholder("Identifier")||super.parseFunctionId(e)}parseClass(e,s,i){let a=s?"ClassDeclaration":"ClassExpression";this.next();let n=this.state.strict,o=this.parsePlaceholder("Identifier");if(o)if(this.match(81)||this.match(142)||this.match(5))e.id=o;else{if(i||!s)return e.id=null,e.body=this.finishPlaceholder(o,"ClassBody"),this.finishNode(e,a);throw this.raise(Kr.ClassNameIsRequired,{at:this.state.startLoc})}else this.parseClassId(e,s,i);return super.parseClassSuper(e),e.body=this.parsePlaceholder("ClassBody")||super.parseClassBody(!!e.superClass,n),this.finishNode(e,a)}parseExport(e,s){let i=this.parsePlaceholder("Identifier");if(!i)return super.parseExport(e,s);if(!this.isContextual(97)&&!this.match(12))return e.specifiers=[],e.source=null,e.declaration=this.finishPlaceholder(i,"Declaration"),this.finishNode(e,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");let a=this.startNode();return a.exported=i,e.specifiers=[this.finishNode(a,"ExportDefaultSpecifier")],super.parseExport(e,s)}isExportDefaultSpecifier(){if(this.match(65)){let e=this.nextTokenStart();if(this.isUnparsedContextual(e,"from")&&this.input.startsWith(xe(142),this.nextTokenStartSince(e+4)))return!0}return super.isExportDefaultSpecifier()}maybeParseExportDefaultSpecifier(e){return e.specifiers&&e.specifiers.length>0?!0:super.maybeParseExportDefaultSpecifier(e)}checkExport(e){let{specifiers:s}=e;s!=null&&s.length&&(e.specifiers=s.filter(i=>i.exported.type==="Placeholder")),super.checkExport(e),e.specifiers=s}parseImport(e){let s=this.parsePlaceholder("Identifier");if(!s)return super.parseImport(e);if(e.specifiers=[],!this.isContextual(97)&&!this.match(12))return e.source=this.finishPlaceholder(s,"StringLiteral"),this.semicolon(),this.finishNode(e,"ImportDeclaration");let i=this.startNodeAtNode(s);return i.local=s,e.specifiers.push(this.finishNode(i,"ImportDefaultSpecifier")),this.eat(12)&&(this.maybeParseStarImportSpecifier(e)||this.parseNamedImportSpecifiers(e)),this.expectContextual(97),e.source=this.parseImportSource(),this.semicolon(),this.finishNode(e,"ImportDeclaration")}parseImportSource(){return this.parsePlaceholder("StringLiteral")||super.parseImportSource()}assertNoSpace(){this.state.start>this.state.lastTokEndLoc.index&&this.raise(Kr.UnexpectedSpace,{at:this.state.lastTokEndLoc})}},mh=t=>class extends t{parseV8Intrinsic(){if(this.match(54)){let e=this.state.startLoc,s=this.startNode();if(this.next(),q(this.state.type)){let i=this.parseIdentifierName(),a=this.createIdentifier(s,i);if(a.type="V8IntrinsicIdentifier",this.match(10))return a}this.unexpected(e)}}parseExprAtom(e){return this.parseV8Intrinsic()||super.parseExprAtom(e)}};function J(t,r){let[e,s]=typeof r=="string"?[r,{}]:r,i=Object.keys(s),a=i.length===0;return t.some(n=>{if(typeof n=="string")return a&&n===e;{let[o,u]=n;if(o!==e)return!1;for(let c of i)if(u[c]!==s[c])return!1;return!0}})}function we(t,r,e){let s=t.find(i=>Array.isArray(i)?i[0]===r:i===r);return s&&Array.isArray(s)&&s.length>1?s[1][e]:null}var Wr=["minimal","fsharp","hack","smart"],Gr=["^^","@@","^","%","#"],Jr=["hash","bar"];function yh(t){if(J(t,"decorators")){if(J(t,"decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");let r=we(t,"decorators","decoratorsBeforeExport");if(r!=null&&typeof r!="boolean")throw new Error("'decoratorsBeforeExport' must be a boolean, if specified.");let e=we(t,"decorators","allowCallParenthesized");if(e!=null&&typeof e!="boolean")throw new Error("'allowCallParenthesized' must be a boolean.")}if(J(t,"flow")&&J(t,"typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(J(t,"placeholders")&&J(t,"v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(J(t,"pipelineOperator")){let r=we(t,"pipelineOperator","proposal");if(!Wr.includes(r)){let s=Wr.map(i=>`"${i}"`).join(", ");throw new Error(`"pipelineOperator" requires "proposal" option whose value must be one of: ${s}.`)}let e=J(t,["recordAndTuple",{syntaxType:"hash"}]);if(r==="hack"){if(J(t,"placeholders"))throw new Error("Cannot combine placeholders plugin and Hack-style pipes.");if(J(t,"v8intrinsic"))throw new Error("Cannot combine v8intrinsic plugin and Hack-style pipes.");let s=we(t,"pipelineOperator","topicToken");if(!Gr.includes(s)){let i=Gr.map(a=>`"${a}"`).join(", ");throw new Error(`"pipelineOperator" in "proposal": "hack" mode also requires a "topicToken" option whose value must be one of: ${i}.`)}if(s==="#"&&e)throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "hack", topicToken: "#" }]` and `["recordAndtuple", { syntaxType: "hash"}]`.')}else if(r==="smart"&&e)throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "smart" }]` and `["recordAndtuple", { syntaxType: "hash"}]`.')}if(J(t,"moduleAttributes")){if(J(t,"importAssertions"))throw new Error("Cannot combine importAssertions and moduleAttributes plugins.");if(we(t,"moduleAttributes","version")!=="may-2020")throw new Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(J(t,"recordAndTuple")&&we(t,"recordAndTuple","syntaxType")!=null&&!Jr.includes(we(t,"recordAndTuple","syntaxType")))throw new Error("The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: "+Jr.map(r=>`'${r}'`).join(", "));if(J(t,"asyncDoExpressions")&&!J(t,"doExpressions")){let r=new Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw r.missingPlugins="doExpressions",r}}var Xr={estree:el,jsx:th,flow:Zl,typescript:uh,v8intrinsic:mh,placeholders:dh},xh=Object.keys(Xr),gh=class extends ah{checkProto(t,r,e,s){if(t.type==="SpreadElement"||this.isObjectMethod(t)||t.computed||t.shorthand)return;let i=t.key;if((i.type==="Identifier"?i.name:i.value)==="__proto__"){if(r){this.raise(f.RecordNoProto,{at:i});return}e.used&&(s?s.doubleProtoLoc===null&&(s.doubleProtoLoc=i.loc.start):this.raise(f.DuplicateProto,{at:i})),e.used=!0}}shouldExitDescending(t,r){return t.type==="ArrowFunctionExpression"&&t.start===r}getExpression(){this.enterInitialScopes(),this.nextToken();let t=this.parseExpression();return this.match(137)||this.unexpected(),this.finalizeRemainingComments(),t.comments=this.state.comments,t.errors=this.state.errors,this.options.tokens&&(t.tokens=this.tokens),t}parseExpression(t,r){return t?this.disallowInAnd(()=>this.parseExpressionBase(r)):this.allowInAnd(()=>this.parseExpressionBase(r))}parseExpressionBase(t){let r=this.state.startLoc,e=this.parseMaybeAssign(t);if(this.match(12)){let s=this.startNodeAt(r);for(s.expressions=[e];this.eat(12);)s.expressions.push(this.parseMaybeAssign(t));return this.toReferencedList(s.expressions),this.finishNode(s,"SequenceExpression")}return e}parseMaybeAssignDisallowIn(t,r){return this.disallowInAnd(()=>this.parseMaybeAssign(t,r))}parseMaybeAssignAllowIn(t,r){return this.allowInAnd(()=>this.parseMaybeAssign(t,r))}setOptionalParametersError(t,r){var e;t.optionalParametersLoc=(e=r==null?void 0:r.loc)!=null?e:this.state.startLoc}parseMaybeAssign(t,r){let e=this.state.startLoc;if(this.isContextual(106)&&this.prodParam.hasYield){let n=this.parseYield();return r&&(n=r.call(this,n,e)),n}let s;t?s=!1:(t=new vt,s=!0);let{type:i}=this.state;(i===10||q(i))&&(this.state.potentialArrowAt=this.state.start);let a=this.parseMaybeConditional(t);if(r&&(a=r.call(this,a,e)),Bo(this.state.type)){let n=this.startNodeAt(e),o=this.state.value;if(n.operator=o,this.match(29)){this.toAssignable(a,!0),n.left=a;let u=e.index;t.doubleProtoLoc!=null&&t.doubleProtoLoc.index>=u&&(t.doubleProtoLoc=null),t.shorthandAssignLoc!=null&&t.shorthandAssignLoc.index>=u&&(t.shorthandAssignLoc=null),t.privateKeyLoc!=null&&t.privateKeyLoc.index>=u&&(this.checkDestructuringPrivate(t),t.privateKeyLoc=null)}else n.left=a;return this.next(),n.right=this.parseMaybeAssign(),this.checkLVal(a,{in:this.finishNode(n,"AssignmentExpression")}),n}else s&&this.checkExpressionErrors(t,!0);return a}parseMaybeConditional(t){let r=this.state.startLoc,e=this.state.potentialArrowAt,s=this.parseExprOps(t);return this.shouldExitDescending(s,e)?s:this.parseConditional(s,r,t)}parseConditional(t,r,e){if(this.eat(17)){let s=this.startNodeAt(r);return s.test=t,s.consequent=this.parseMaybeAssignAllowIn(),this.expect(14),s.alternate=this.parseMaybeAssign(),this.finishNode(s,"ConditionalExpression")}return t}parseMaybeUnaryOrPrivate(t){return this.match(136)?this.parsePrivateName():this.parseMaybeUnary(t)}parseExprOps(t){let r=this.state.startLoc,e=this.state.potentialArrowAt,s=this.parseMaybeUnaryOrPrivate(t);return this.shouldExitDescending(s,e)?s:this.parseExprOp(s,r,-1)}parseExprOp(t,r,e){if(this.isPrivateName(t)){let i=this.getPrivateNameSV(t);(e>=at(58)||!this.prodParam.hasIn||!this.match(58))&&this.raise(f.PrivateInExpectedIn,{at:t,identifierName:i}),this.classScope.usePrivateName(i,t.loc.start)}let s=this.state.type;if(_o(s)&&(this.prodParam.hasIn||!this.match(58))){let i=at(s);if(i>e){if(s===39){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return t;this.checkPipelineAtInfixOperator(t,r)}let a=this.startNodeAt(r);a.left=t,a.operator=this.state.value;let n=s===41||s===42,o=s===40;if(o&&(i=at(42)),this.next(),s===39&&this.hasPlugin(["pipelineOperator",{proposal:"minimal"}])&&this.state.type===96&&this.prodParam.hasAwait)throw this.raise(f.UnexpectedAwaitAfterPipelineBody,{at:this.state.startLoc});a.right=this.parseExprOpRightExpr(s,i);let u=this.finishNode(a,n||o?"LogicalExpression":"BinaryExpression"),c=this.state.type;if(o&&(c===41||c===42)||n&&c===40)throw this.raise(f.MixingCoalesceWithLogical,{at:this.state.startLoc});return this.parseExprOp(u,r,e)}}return t}parseExprOpRightExpr(t,r){let e=this.state.startLoc;switch(t){case 39:switch(this.getPluginOption("pipelineOperator","proposal")){case"hack":return this.withTopicBindingContext(()=>this.parseHackPipeBody());case"smart":return this.withTopicBindingContext(()=>{if(this.prodParam.hasYield&&this.isContextual(106))throw this.raise(f.PipeBodyIsTighter,{at:this.state.startLoc});return this.parseSmartPipelineBodyInStyle(this.parseExprOpBaseRightExpr(t,r),e)});case"fsharp":return this.withSoloAwaitPermittingContext(()=>this.parseFSharpPipelineBody(r))}default:return this.parseExprOpBaseRightExpr(t,r)}}parseExprOpBaseRightExpr(t,r){let e=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),e,$o(t)?r-1:r)}parseHackPipeBody(){var t;let{startLoc:r}=this.state,e=this.parseMaybeAssign();return Go.has(e.type)&&!((t=e.extra)!=null&&t.parenthesized)&&this.raise(f.PipeUnparenthesizedBody,{at:r,type:e.type}),this.topicReferenceWasUsedInCurrentContext()||this.raise(f.PipeTopicUnused,{at:r}),e}checkExponentialAfterUnary(t){this.match(57)&&this.raise(f.UnexpectedTokenUnaryExponentiation,{at:t.argument})}parseMaybeUnary(t,r){let e=this.state.startLoc,s=this.isContextual(96);if(s&&this.isAwaitAllowed()){this.next();let o=this.parseAwait(e);return r||this.checkExponentialAfterUnary(o),o}let i=this.match(34),a=this.startNode();if(jo(this.state.type)){a.operator=this.state.value,a.prefix=!0,this.match(72)&&this.expectPlugin("throwExpressions");let o=this.match(89);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(t,!0),this.state.strict&&o){let u=a.argument;u.type==="Identifier"?this.raise(f.StrictDelete,{at:a}):this.hasPropertyAsPrivateName(u)&&this.raise(f.DeletePrivateField,{at:a})}if(!i)return r||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}let n=this.parseUpdate(a,i,t);if(s){let{type:o}=this.state;if((this.hasPlugin("v8intrinsic")?He(o):He(o)&&!this.match(54))&&!this.isAmbiguousAwait())return this.raiseOverwrite(f.AwaitNotInAsyncContext,{at:e}),this.parseAwait(e)}return n}parseUpdate(t,r,e){if(r){let a=t;return this.checkLVal(a.argument,{in:this.finishNode(a,"UpdateExpression")}),t}let s=this.state.startLoc,i=this.parseExprSubscripts(e);if(this.checkExpressionErrors(e,!1))return i;for(;Ro(this.state.type)&&!this.canInsertSemicolon();){let a=this.startNodeAt(s);a.operator=this.state.value,a.prefix=!1,a.argument=i,this.next(),this.checkLVal(i,{in:i=this.finishNode(a,"UpdateExpression")})}return i}parseExprSubscripts(t){let r=this.state.startLoc,e=this.state.potentialArrowAt,s=this.parseExprAtom(t);return this.shouldExitDescending(s,e)?s:this.parseSubscripts(s,r)}parseSubscripts(t,r,e){let s={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(t),stop:!1};do t=this.parseSubscript(t,r,e,s),s.maybeAsyncArrow=!1;while(!s.stop);return t}parseSubscript(t,r,e,s){let{type:i}=this.state;if(!e&&i===15)return this.parseBind(t,r,e,s);if(nt(i))return this.parseTaggedTemplateExpression(t,r,s);let a=!1;if(i===18){if(e&&(this.raise(f.OptionalChainingNoNew,{at:this.state.startLoc}),this.lookaheadCharCode()===40))return s.stop=!0,t;s.optionalChainMember=a=!0,this.next()}if(!e&&this.match(10))return this.parseCoverCallAndAsyncArrowHead(t,r,s,a);{let n=this.eat(0);return n||a||this.eat(16)?this.parseMember(t,r,s,n,a):(s.stop=!0,t)}}parseMember(t,r,e,s,i){let a=this.startNodeAt(r);return a.object=t,a.computed=s,s?(a.property=this.parseExpression(),this.expect(3)):this.match(136)?(t.type==="Super"&&this.raise(f.SuperPrivateField,{at:r}),this.classScope.usePrivateName(this.state.value,this.state.startLoc),a.property=this.parsePrivateName()):a.property=this.parseIdentifier(!0),e.optionalChainMember?(a.optional=i,this.finishNode(a,"OptionalMemberExpression")):this.finishNode(a,"MemberExpression")}parseBind(t,r,e,s){let i=this.startNodeAt(r);return i.object=t,this.next(),i.callee=this.parseNoCallExpr(),s.stop=!0,this.parseSubscripts(this.finishNode(i,"BindExpression"),r,e)}parseCoverCallAndAsyncArrowHead(t,r,e,s){let i=this.state.maybeInArrowParameters,a=null;this.state.maybeInArrowParameters=!0,this.next();let n=this.startNodeAt(r);n.callee=t;let{maybeAsyncArrow:o,optionalChainMember:u}=e;o&&(this.expressionScope.enter($l()),a=new vt),u&&(n.optional=s),s?n.arguments=this.parseCallExpressionArguments(11):n.arguments=this.parseCallExpressionArguments(11,t.type==="Import",t.type!=="Super",n,a);let c=this.finishCallExpression(n,u);return o&&this.shouldParseAsyncArrow()&&!s?(e.stop=!0,this.checkDestructuringPrivate(a),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),c=this.parseAsyncArrowFromCallExpression(this.startNodeAt(r),c)):(o&&(this.checkExpressionErrors(a,!0),this.expressionScope.exit()),this.toReferencedArguments(c)),this.state.maybeInArrowParameters=i,c}toReferencedArguments(t,r){this.toReferencedListDeep(t.arguments,r)}parseTaggedTemplateExpression(t,r,e){let s=this.startNodeAt(r);return s.tag=t,s.quasi=this.parseTemplate(!0),e.optionalChainMember&&this.raise(f.OptionalChainingNoTemplate,{at:r}),this.finishNode(s,"TaggedTemplateExpression")}atPossibleAsyncArrow(t){return t.type==="Identifier"&&t.name==="async"&&this.state.lastTokEndLoc.index===t.end&&!this.canInsertSemicolon()&&t.end-t.start===5&&t.start===this.state.potentialArrowAt}finishCallExpression(t,r){if(t.callee.type==="Import")if(t.arguments.length===2&&(this.hasPlugin("moduleAttributes")||this.expectPlugin("importAssertions")),t.arguments.length===0||t.arguments.length>2)this.raise(f.ImportCallArity,{at:t,maxArgumentCount:this.hasPlugin("importAssertions")||this.hasPlugin("moduleAttributes")?2:1});else for(let e of t.arguments)e.type==="SpreadElement"&&this.raise(f.ImportCallSpreadArgument,{at:e});return this.finishNode(t,r?"OptionalCallExpression":"CallExpression")}parseCallExpressionArguments(t,r,e,s,i){let a=[],n=!0,o=this.state.inFSharpPipelineDirectBody;for(this.state.inFSharpPipelineDirectBody=!1;!this.eat(t);){if(n)n=!1;else if(this.expect(12),this.match(t)){r&&!this.hasPlugin("importAssertions")&&!this.hasPlugin("moduleAttributes")&&this.raise(f.ImportCallArgumentTrailingComma,{at:this.state.lastTokStartLoc}),s&&this.addTrailingCommaExtraToNode(s),this.next();break}a.push(this.parseExprListItem(!1,i,e))}return this.state.inFSharpPipelineDirectBody=o,a}shouldParseAsyncArrow(){return this.match(19)&&!this.canInsertSemicolon()}parseAsyncArrowFromCallExpression(t,r){var e;return this.resetPreviousNodeTrailingComments(r),this.expect(19),this.parseArrowExpression(t,r.arguments,!0,(e=r.extra)==null?void 0:e.trailingCommaLoc),r.innerComments&&Ke(t,r.innerComments),r.callee.trailingComments&&Ke(t,r.callee.trailingComments),t}parseNoCallExpr(){let t=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),t,!0)}parseExprAtom(t){let r,e=null,{type:s}=this.state;switch(s){case 79:return this.parseSuper();case 83:return r=this.startNode(),this.next(),this.match(16)?this.parseImportMetaProperty(r):(this.match(10)||this.raise(f.UnsupportedImport,{at:this.state.lastTokStartLoc}),this.finishNode(r,"Import"));case 78:return r=this.startNode(),this.next(),this.finishNode(r,"ThisExpression");case 90:return this.parseDo(this.startNode(),!1);case 56:case 31:return this.readRegexp(),this.parseRegExpLiteral(this.state.value);case 132:return this.parseNumericLiteral(this.state.value);case 133:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseDecimalLiteral(this.state.value);case 131:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(!0);case 86:return this.parseBooleanLiteral(!1);case 10:{let i=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(i)}case 2:case 1:return this.parseArrayLike(this.state.type===2?4:3,!1,!0);case 0:return this.parseArrayLike(3,!0,!1,t);case 6:case 7:return this.parseObjectLike(this.state.type===6?9:8,!1,!0);case 5:return this.parseObjectLike(8,!1,!1,t);case 68:return this.parseFunctionOrFunctionSent();case 26:e=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(e,this.startNode()),!1);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(!1);case 15:{r=this.startNode(),this.next(),r.object=null;let i=r.callee=this.parseNoCallExpr();if(i.type==="MemberExpression")return this.finishNode(r,"BindExpression");throw this.raise(f.UnsupportedBind,{at:i})}case 136:return this.raise(f.PrivateInExpectedIn,{at:this.state.startLoc,identifierName:this.state.value}),this.parsePrivateName();case 33:return this.parseTopicReferenceThenEqualsSign(54,"%");case 32:return this.parseTopicReferenceThenEqualsSign(44,"^");case 37:case 38:return this.parseTopicReference("hack");case 44:case 54:case 27:{let i=this.getPluginOption("pipelineOperator","proposal");if(i)return this.parseTopicReference(i);this.unexpected();break}case 47:{let i=this.input.codePointAt(this.nextTokenStart());fe(i)||i===62?this.expectOnePlugin(["jsx","flow","typescript"]):this.unexpected();break}default:if(q(s)){if(this.isContextual(125)&&this.lookaheadCharCode()===123&&!this.hasFollowingLineBreak())return this.parseModuleExpression();let i=this.state.potentialArrowAt===this.state.start,a=this.state.containsEsc,n=this.parseIdentifier();if(!a&&n.name==="async"&&!this.canInsertSemicolon()){let{type:o}=this.state;if(o===68)return this.resetPreviousNodeTrailingComments(n),this.next(),this.parseAsyncFunctionExpression(this.startNodeAtNode(n));if(q(o))return this.lookaheadCharCode()===61?this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(n)):n;if(o===90)return this.resetPreviousNodeTrailingComments(n),this.parseDo(this.startNodeAtNode(n),!0)}return i&&this.match(19)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(this.startNodeAtNode(n),[n],!1)):n}else this.unexpected()}}parseTopicReferenceThenEqualsSign(t,r){let e=this.getPluginOption("pipelineOperator","proposal");if(e)return this.state.type=t,this.state.value=r,this.state.pos--,this.state.end--,this.state.endLoc=Y(this.state.endLoc,-1),this.parseTopicReference(e);this.unexpected()}parseTopicReference(t){let r=this.startNode(),e=this.state.startLoc,s=this.state.type;return this.next(),this.finishTopicReference(r,e,t,s)}finishTopicReference(t,r,e,s){if(this.testTopicReferenceConfiguration(e,r,s)){let i=e==="smart"?"PipelinePrimaryTopicReference":"TopicReference";return this.topicReferenceIsAllowedInCurrentContext()||this.raise(e==="smart"?f.PrimaryTopicNotAllowed:f.PipeTopicUnbound,{at:r}),this.registerTopicReference(),this.finishNode(t,i)}else throw this.raise(f.PipeTopicUnconfiguredToken,{at:r,token:xe(s)})}testTopicReferenceConfiguration(t,r,e){switch(t){case"hack":return this.hasPlugin(["pipelineOperator",{topicToken:xe(e)}]);case"smart":return e===27;default:throw this.raise(f.PipeTopicRequiresHackPipes,{at:r})}}parseAsyncArrowUnaryFunction(t){this.prodParam.enter(Tt(!0,this.prodParam.hasYield));let r=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(f.LineTerminatorBeforeArrow,{at:this.state.curPosition()}),this.expect(19),this.parseArrowExpression(t,r,!0)}parseDo(t,r){this.expectPlugin("doExpressions"),r&&this.expectPlugin("asyncDoExpressions"),t.async=r,this.next();let e=this.state.labels;return this.state.labels=[],r?(this.prodParam.enter(At),t.body=this.parseBlock(),this.prodParam.exit()):t.body=this.parseBlock(),this.state.labels=e,this.finishNode(t,"DoExpression")}parseSuper(){let t=this.startNode();return this.next(),this.match(10)&&!this.scope.allowDirectSuper&&!this.options.allowSuperOutsideMethod?this.raise(f.SuperNotAllowed,{at:t}):!this.scope.allowSuper&&!this.options.allowSuperOutsideMethod&&this.raise(f.UnexpectedSuper,{at:t}),!this.match(10)&&!this.match(0)&&!this.match(16)&&this.raise(f.UnsupportedSuper,{at:t}),this.finishNode(t,"Super")}parsePrivateName(){let t=this.startNode(),r=this.startNodeAt(Y(this.state.startLoc,1)),e=this.state.value;return this.next(),t.id=this.createIdentifier(r,e),this.finishNode(t,"PrivateName")}parseFunctionOrFunctionSent(){let t=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(16)){let r=this.createIdentifier(this.startNodeAtNode(t),"function");return this.next(),this.match(102)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected(),this.parseMetaProperty(t,r,"sent")}return this.parseFunction(t)}parseMetaProperty(t,r,e){t.meta=r;let s=this.state.containsEsc;return t.property=this.parseIdentifier(!0),(t.property.name!==e||s)&&this.raise(f.UnsupportedMetaProperty,{at:t.property,target:r.name,onlyValidPropertyName:e}),this.finishNode(t,"MetaProperty")}parseImportMetaProperty(t){let r=this.createIdentifier(this.startNodeAtNode(t),"import");return this.next(),this.isContextual(100)&&(this.inModule||this.raise(f.ImportMetaOutsideModule,{at:r}),this.sawUnambiguousESM=!0),this.parseMetaProperty(t,r,"meta")}parseLiteralAtNode(t,r,e){return this.addExtra(e,"rawValue",t),this.addExtra(e,"raw",this.input.slice(e.start,this.state.end)),e.value=t,this.next(),this.finishNode(e,r)}parseLiteral(t,r){let e=this.startNode();return this.parseLiteralAtNode(t,r,e)}parseStringLiteral(t){return this.parseLiteral(t,"StringLiteral")}parseNumericLiteral(t){return this.parseLiteral(t,"NumericLiteral")}parseBigIntLiteral(t){return this.parseLiteral(t,"BigIntLiteral")}parseDecimalLiteral(t){return this.parseLiteral(t,"DecimalLiteral")}parseRegExpLiteral(t){let r=this.parseLiteral(t.value,"RegExpLiteral");return r.pattern=t.pattern,r.flags=t.flags,r}parseBooleanLiteral(t){let r=this.startNode();return r.value=t,this.next(),this.finishNode(r,"BooleanLiteral")}parseNullLiteral(){let t=this.startNode();return this.next(),this.finishNode(t,"NullLiteral")}parseParenAndDistinguishExpression(t){let r=this.state.startLoc,e;this.next(),this.expressionScope.enter(Ul());let s=this.state.maybeInArrowParameters,i=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;let a=this.state.startLoc,n=[],o=new vt,u=!0,c,y;for(;!this.match(11);){if(u)u=!1;else if(this.expect(12,o.optionalParametersLoc===null?null:o.optionalParametersLoc),this.match(11)){y=this.state.startLoc;break}if(this.match(21)){let C=this.state.startLoc;if(c=this.state.startLoc,n.push(this.parseParenItem(this.parseRestBinding(),C)),!this.checkCommaAfterRest(41))break}else n.push(this.parseMaybeAssignAllowIn(o,this.parseParenItem))}let g=this.state.lastTokEndLoc;this.expect(11),this.state.maybeInArrowParameters=s,this.state.inFSharpPipelineDirectBody=i;let T=this.startNodeAt(r);return t&&this.shouldParseArrow(n)&&(T=this.parseArrow(T))?(this.checkDestructuringPrivate(o),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(T,n,!1),T):(this.expressionScope.exit(),n.length||this.unexpected(this.state.lastTokStartLoc),y&&this.unexpected(y),c&&this.unexpected(c),this.checkExpressionErrors(o,!0),this.toReferencedListDeep(n,!0),n.length>1?(e=this.startNodeAt(a),e.expressions=n,this.finishNode(e,"SequenceExpression"),this.resetEndLocation(e,g)):e=n[0],this.wrapParenthesis(r,e))}wrapParenthesis(t,r){if(!this.options.createParenthesizedExpressions)return this.addExtra(r,"parenthesized",!0),this.addExtra(r,"parenStart",t.index),this.takeSurroundingComments(r,t.index,this.state.lastTokEndLoc.index),r;let e=this.startNodeAt(t);return e.expression=r,this.finishNode(e,"ParenthesizedExpression")}shouldParseArrow(t){return!this.canInsertSemicolon()}parseArrow(t){if(this.eat(19))return t}parseParenItem(t,r){return t}parseNewOrNewTarget(){let t=this.startNode();if(this.next(),this.match(16)){let r=this.createIdentifier(this.startNodeAtNode(t),"new");this.next();let e=this.parseMetaProperty(t,r,"target");return!this.scope.inNonArrowFunction&&!this.scope.inClass&&!this.options.allowNewTargetOutsideFunction&&this.raise(f.UnexpectedNewTarget,{at:e}),e}return this.parseNew(t)}parseNew(t){if(this.parseNewCallee(t),this.eat(10)){let r=this.parseExprList(11);this.toReferencedList(r),t.arguments=r}else t.arguments=[];return this.finishNode(t,"NewExpression")}parseNewCallee(t){t.callee=this.parseNoCallExpr(),t.callee.type==="Import"&&this.raise(f.ImportCallNotNewExpression,{at:t.callee})}parseTemplateElement(t){let{start:r,startLoc:e,end:s,value:i}=this.state,a=r+1,n=this.startNodeAt(Y(e,1));i===null&&(t||this.raise(f.InvalidEscapeSequenceTemplate,{at:Y(this.state.firstInvalidTemplateEscapePos,1)}));let o=this.match(24),u=o?-1:-2,c=s+u;n.value={raw:this.input.slice(a,c).replace(/\r\n?/g,`
`),cooked:i===null?null:i.slice(1,u)},n.tail=o,this.next();let y=this.finishNode(n,"TemplateElement");return this.resetEndLocation(y,Y(this.state.lastTokEndLoc,u)),y}parseTemplate(t){let r=this.startNode();r.expressions=[];let e=this.parseTemplateElement(t);for(r.quasis=[e];!e.tail;)r.expressions.push(this.parseTemplateSubstitution()),this.readTemplateContinuation(),r.quasis.push(e=this.parseTemplateElement(t));return this.finishNode(r,"TemplateLiteral")}parseTemplateSubstitution(){return this.parseExpression()}parseObjectLike(t,r,e,s){e&&this.expectPlugin("recordAndTuple");let i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;let a=Object.create(null),n=!0,o=this.startNode();for(o.properties=[],this.next();!this.match(t);){if(n)n=!1;else if(this.expect(12),this.match(t)){this.addTrailingCommaExtraToNode(o);break}let c;r?c=this.parseBindingProperty():(c=this.parsePropertyDefinition(s),this.checkProto(c,e,a,s)),e&&!this.isObjectProperty(c)&&c.type!=="SpreadElement"&&this.raise(f.InvalidRecordProperty,{at:c}),c.shorthand&&this.addExtra(c,"shorthand",!0),o.properties.push(c)}this.next(),this.state.inFSharpPipelineDirectBody=i;let u="ObjectExpression";return r?u="ObjectPattern":e&&(u="RecordExpression"),this.finishNode(o,u)}addTrailingCommaExtraToNode(t){this.addExtra(t,"trailingComma",this.state.lastTokStart),this.addExtra(t,"trailingCommaLoc",this.state.lastTokStartLoc,!1)}maybeAsyncOrAccessorProp(t){return!t.computed&&t.key.type==="Identifier"&&(this.isLiteralPropertyName()||this.match(0)||this.match(55))}parsePropertyDefinition(t){let r=[];if(this.match(26))for(this.hasPlugin("decorators")&&this.raise(f.UnsupportedPropertyDecorator,{at:this.state.startLoc});this.match(26);)r.push(this.parseDecorator());let e=this.startNode(),s=!1,i=!1,a;if(this.match(21))return r.length&&this.unexpected(),this.parseSpread();r.length&&(e.decorators=r,r=[]),e.method=!1,t&&(a=this.state.startLoc);let n=this.eat(55);this.parsePropertyNamePrefixOperator(e);let o=this.state.containsEsc,u=this.parsePropertyName(e,t);if(!n&&!o&&this.maybeAsyncOrAccessorProp(e)){let c=u.name;c==="async"&&!this.hasPrecedingLineBreak()&&(s=!0,this.resetPreviousNodeTrailingComments(u),n=this.eat(55),this.parsePropertyName(e)),(c==="get"||c==="set")&&(i=!0,this.resetPreviousNodeTrailingComments(u),e.kind=c,this.match(55)&&(n=!0,this.raise(f.AccessorIsGenerator,{at:this.state.curPosition(),kind:c}),this.next()),this.parsePropertyName(e))}return this.parseObjPropValue(e,a,n,s,!1,i,t)}getGetterSetterExpectedParamCount(t){return t.kind==="get"?0:1}getObjectOrClassMethodParams(t){return t.params}checkGetterSetterParams(t){var r;let e=this.getGetterSetterExpectedParamCount(t),s=this.getObjectOrClassMethodParams(t);s.length!==e&&this.raise(t.kind==="get"?f.BadGetterArity:f.BadSetterArity,{at:t}),t.kind==="set"&&((r=s[s.length-1])==null?void 0:r.type)==="RestElement"&&this.raise(f.BadSetterRestParameter,{at:t})}parseObjectMethod(t,r,e,s,i){if(i){let a=this.parseMethod(t,r,!1,!1,!1,"ObjectMethod");return this.checkGetterSetterParams(a),a}if(e||r||this.match(10))return s&&this.unexpected(),t.kind="method",t.method=!0,this.parseMethod(t,r,e,!1,!1,"ObjectMethod")}parseObjectProperty(t,r,e,s){if(t.shorthand=!1,this.eat(14))return t.value=e?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowIn(s),this.finishNode(t,"ObjectProperty");if(!t.computed&&t.key.type==="Identifier"){if(this.checkReservedWord(t.key.name,t.key.loc.start,!0,!1),e)t.value=this.parseMaybeDefault(r,me(t.key));else if(this.match(29)){let i=this.state.startLoc;s!=null?s.shorthandAssignLoc===null&&(s.shorthandAssignLoc=i):this.raise(f.InvalidCoverInitializedName,{at:i}),t.value=this.parseMaybeDefault(r,me(t.key))}else t.value=me(t.key);return t.shorthand=!0,this.finishNode(t,"ObjectProperty")}}parseObjPropValue(t,r,e,s,i,a,n){let o=this.parseObjectMethod(t,e,s,i,a)||this.parseObjectProperty(t,r,i,n);return o||this.unexpected(),o}parsePropertyName(t,r){if(this.eat(0))t.computed=!0,t.key=this.parseMaybeAssignAllowIn(),this.expect(3);else{let{type:e,value:s}=this.state,i;if(te(e))i=this.parseIdentifier(!0);else switch(e){case 132:i=this.parseNumericLiteral(s);break;case 131:i=this.parseStringLiteral(s);break;case 133:i=this.parseBigIntLiteral(s);break;case 134:i=this.parseDecimalLiteral(s);break;case 136:{let a=this.state.startLoc;r!=null?r.privateKeyLoc===null&&(r.privateKeyLoc=a):this.raise(f.UnexpectedPrivateField,{at:a}),i=this.parsePrivateName();break}default:this.unexpected()}t.key=i,e!==136&&(t.computed=!1)}return t.key}initFunction(t,r){t.id=null,t.generator=!1,t.async=r}parseMethod(t,r,e,s,i,a){let n=arguments.length>6&&arguments[6]!==void 0?arguments[6]:!1;this.initFunction(t,e),t.generator=r,this.scope.enter(de|ht|(n?Ee:0)|(i?Pr:0)),this.prodParam.enter(Tt(e,t.generator)),this.parseFunctionParams(t,s);let o=this.parseFunctionBodyAndFinish(t,a,!0);return this.prodParam.exit(),this.scope.exit(),o}parseArrayLike(t,r,e,s){e&&this.expectPlugin("recordAndTuple");let i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;let a=this.startNode();return this.next(),a.elements=this.parseExprList(t,!e,s,a),this.state.inFSharpPipelineDirectBody=i,this.finishNode(a,e?"TupleExpression":"ArrayExpression")}parseArrowExpression(t,r,e,s){this.scope.enter(de|Gt);let i=Tt(e,!1);!this.match(5)&&this.prodParam.hasIn&&(i|=_e),this.prodParam.enter(i),this.initFunction(t,e);let a=this.state.maybeInArrowParameters;return r&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(t,r,s)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(t,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=a,this.finishNode(t,"ArrowFunctionExpression")}setArrowFunctionParameters(t,r,e){this.toAssignableList(r,e,!1),t.params=r}parseFunctionBodyAndFinish(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return this.parseFunctionBody(t,!1,e),this.finishNode(t,r)}parseFunctionBody(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,s=r&&!this.match(5);if(this.expressionScope.enter(_r()),s)t.body=this.parseMaybeAssign(),this.checkParams(t,!1,r,!1);else{let i=this.state.strict,a=this.state.labels;this.state.labels=[],this.prodParam.enter(this.prodParam.currentFlags()|jr),t.body=this.parseBlock(!0,!1,n=>{let o=!this.isSimpleParamList(t.params);n&&o&&this.raise(f.IllegalLanguageModeDirective,{at:(t.kind==="method"||t.kind==="constructor")&&t.key?t.key.loc.end:t});let u=!i&&this.state.strict;this.checkParams(t,!this.state.strict&&!r&&!e&&!o,r,u),this.state.strict&&t.id&&this.checkIdentifier(t.id,dl,u)}),this.prodParam.exit(),this.state.labels=a}this.expressionScope.exit()}isSimpleParameter(t){return t.type==="Identifier"}isSimpleParamList(t){for(let r=0,e=t.length;r<e;r++)if(!this.isSimpleParameter(t[r]))return!1;return!0}checkParams(t,r,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=!r&&new Set,a={type:"FormalParameters"};for(let n of t.params)this.checkLVal(n,{in:a,binding:mt,checkClashes:i,strictModeChanged:s})}parseExprList(t,r,e,s){let i=[],a=!0;for(;!this.eat(t);){if(a)a=!1;else if(this.expect(12),this.match(t)){s&&this.addTrailingCommaExtraToNode(s),this.next();break}i.push(this.parseExprListItem(r,e))}return i}parseExprListItem(t,r,e){let s;if(this.match(12))t||this.raise(f.UnexpectedToken,{at:this.state.curPosition(),unexpected:","}),s=null;else if(this.match(21)){let i=this.state.startLoc;s=this.parseParenItem(this.parseSpread(r),i)}else if(this.match(17)){this.expectPlugin("partialApplication"),e||this.raise(f.UnexpectedArgumentPlaceholder,{at:this.state.startLoc});let i=this.startNode();this.next(),s=this.finishNode(i,"ArgumentPlaceholder")}else s=this.parseMaybeAssignAllowIn(r,this.parseParenItem);return s}parseIdentifier(t){let r=this.startNode(),e=this.parseIdentifierName(t);return this.createIdentifier(r,e)}createIdentifier(t,r){return t.name=r,t.loc.identifierName=r,this.finishNode(t,"Identifier")}parseIdentifierName(t){let r,{startLoc:e,type:s}=this.state;te(s)?r=this.state.value:this.unexpected();let i=ue(s);return t?i&&this.replaceToken(130):this.checkReservedWord(r,e,i,!1),this.next(),r}checkReservedWord(t,r,e,s){if(t.length>10||!ul(t))return;if(e&&ol(t)){this.raise(f.UnexpectedKeyword,{at:r,keyword:t});return}if((this.state.strict?s?xr:mr:dr)(t,this.inModule)){this.raise(f.UnexpectedReservedWord,{at:r,reservedWord:t});return}else if(t==="yield"){if(this.prodParam.hasYield){this.raise(f.YieldBindingIdentifier,{at:r});return}}else if(t==="await"){if(this.prodParam.hasAwait){this.raise(f.AwaitBindingIdentifier,{at:r});return}if(this.scope.inStaticBlock){this.raise(f.AwaitBindingIdentifierInStaticBlock,{at:r});return}this.expressionScope.recordAsyncArrowParametersError({at:r})}else if(t==="arguments"&&this.scope.inClassAndNotInNonArrowFunction){this.raise(f.ArgumentsInClass,{at:r});return}}isAwaitAllowed(){return!!(this.prodParam.hasAwait||this.options.allowAwaitOutsideFunction&&!this.scope.inFunction)}parseAwait(t){let r=this.startNodeAt(t);return this.expressionScope.recordParameterInitializerError(f.AwaitExpressionFormalParameter,{at:r}),this.eat(55)&&this.raise(f.ObsoleteAwaitStar,{at:r}),!this.scope.inFunction&&!this.options.allowAwaitOutsideFunction&&(this.isAmbiguousAwait()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(r.argument=this.parseMaybeUnary(null,!0)),this.finishNode(r,"AwaitExpression")}isAmbiguousAwait(){if(this.hasPrecedingLineBreak())return!0;let{type:t}=this.state;return t===53||t===10||t===0||nt(t)||t===101&&!this.state.containsEsc||t===135||t===56||this.hasPlugin("v8intrinsic")&&t===54}parseYield(){let t=this.startNode();this.expressionScope.recordParameterInitializerError(f.YieldInParameter,{at:t}),this.next();let r=!1,e=null;if(!this.hasPrecedingLineBreak())switch(r=this.eat(55),this.state.type){case 13:case 137:case 8:case 11:case 3:case 9:case 14:case 12:if(!r)break;default:e=this.parseMaybeAssign()}return t.delegate=r,t.argument=e,this.finishNode(t,"YieldExpression")}checkPipelineAtInfixOperator(t,r){this.hasPlugin(["pipelineOperator",{proposal:"smart"}])&&t.type==="SequenceExpression"&&this.raise(f.PipelineHeadSequenceExpression,{at:r})}parseSmartPipelineBodyInStyle(t,r){if(this.isSimpleReference(t)){let e=this.startNodeAt(r);return e.callee=t,this.finishNode(e,"PipelineBareFunction")}else{let e=this.startNodeAt(r);return this.checkSmartPipeTopicBodyEarlyErrors(r),e.expression=t,this.finishNode(e,"PipelineTopicExpression")}}isSimpleReference(t){switch(t.type){case"MemberExpression":return!t.computed&&this.isSimpleReference(t.object);case"Identifier":return!0;default:return!1}}checkSmartPipeTopicBodyEarlyErrors(t){if(this.match(19))throw this.raise(f.PipelineBodyNoArrow,{at:this.state.startLoc});this.topicReferenceWasUsedInCurrentContext()||this.raise(f.PipelineTopicUnused,{at:t})}withTopicBindingContext(t){let r=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=r}}withSmartMixTopicForbiddingContext(t){if(this.hasPlugin(["pipelineOperator",{proposal:"smart"}])){let r=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=r}}else return t()}withSoloAwaitPermittingContext(t){let r=this.state.soloAwait;this.state.soloAwait=!0;try{return t()}finally{this.state.soloAwait=r}}allowInAnd(t){let r=this.prodParam.currentFlags();if(_e&~r){this.prodParam.enter(r|_e);try{return t()}finally{this.prodParam.exit()}}return t()}disallowInAnd(t){let r=this.prodParam.currentFlags();if(_e&r){this.prodParam.enter(r&~_e);try{return t()}finally{this.prodParam.exit()}}return t()}registerTopicReference(){this.state.topicContext.maxTopicIndex=0}topicReferenceIsAllowedInCurrentContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1}topicReferenceWasUsedInCurrentContext(){return this.state.topicContext.maxTopicIndex!=null&&this.state.topicContext.maxTopicIndex>=0}parseFSharpPipelineBody(t){let r=this.state.startLoc;this.state.potentialArrowAt=this.state.start;let e=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;let s=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),r,t);return this.state.inFSharpPipelineDirectBody=e,s}parseModuleExpression(){this.expectPlugin("moduleBlocks");let t=this.startNode();this.next(),this.match(5)||this.unexpected(null,5);let r=this.startNodeAt(this.state.endLoc);this.next();let e=this.initializeScopes(!0);this.enterInitialScopes();try{t.body=this.parseProgram(r,8,"module")}finally{e()}return this.finishNode(t,"ModuleExpression")}parsePropertyNamePrefixOperator(t){}},cs={kind:"loop"},Ph={kind:"switch"},Ah=/[\uD800-\uDFFF]/u,ps=/in(?:stanceof)?/y;function Th(t,r){for(let e=0;e<t.length;e++){let s=t[e],{type:i}=s;if(typeof i=="number"){{if(i===136){let{loc:a,start:n,value:o,end:u}=s,c=n+1,y=Y(a.start,1);t.splice(e,1,new Ae({type:ce(27),value:"#",start:n,end:c,startLoc:a.start,endLoc:y}),new Ae({type:ce(130),value:o,start:c,end:u,startLoc:y,endLoc:a.end})),e++;continue}if(nt(i)){let{loc:a,start:n,value:o,end:u}=s,c=n+1,y=Y(a.start,1),g;r.charCodeAt(n)===96?g=new Ae({type:ce(22),value:"`",start:n,end:c,startLoc:a.start,endLoc:y}):g=new Ae({type:ce(8),value:"}",start:n,end:c,startLoc:a.start,endLoc:y});let T,C,M,j;i===24?(C=u-1,M=Y(a.end,-1),T=o===null?null:o.slice(1,-1),j=new Ae({type:ce(22),value:"`",start:C,end:u,startLoc:M,endLoc:a.end})):(C=u-2,M=Y(a.end,-2),T=o===null?null:o.slice(1,-2),j=new Ae({type:ce(23),value:"${",start:C,end:u,startLoc:M,endLoc:a.end})),t.splice(e,1,g,new Ae({type:ce(20),value:T,start:c,end:C,startLoc:y,endLoc:M}),j),e+=2;continue}}s.type=ce(i)}}return t}var vh=class extends gh{parseTopLevel(t,r){return t.program=this.parseProgram(r),t.comments=this.state.comments,this.options.tokens&&(t.tokens=Th(this.tokens,this.input)),this.finishNode(t,"File")}parseProgram(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:137,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.options.sourceType;if(t.sourceType=e,t.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(t,!0,!0,r),this.inModule&&!this.options.allowUndeclaredExports&&this.scope.undefinedExports.size>0)for(let[i,a]of Array.from(this.scope.undefinedExports))this.raise(f.ModuleExportUndefined,{at:a,localName:i});let s;return r===137?s=this.finishNode(t,"Program"):s=this.finishNodeAt(t,"Program",Y(this.state.startLoc,-1)),s}stmtToDirective(t){let r=t;r.type="Directive",r.value=r.expression,delete r.expression;let e=r.value,s=e.value,i=this.input.slice(e.start,e.end),a=e.value=i.slice(1,-1);return this.addExtra(e,"raw",i),this.addExtra(e,"rawValue",a),this.addExtra(e,"expressionValue",s),e.type="DirectiveLiteral",r}parseInterpreterDirective(){if(!this.match(28))return null;let t=this.startNode();return t.value=this.state.value,this.next(),this.finishNode(t,"InterpreterDirective")}isLet(){return this.isContextual(99)?this.hasFollowingBindingAtom():!1}chStartsBindingIdentifier(t,r){if(fe(t)){if(ps.lastIndex=r,ps.test(this.input)){let e=this.codePointAtPos(ps.lastIndex);if(!De(e)&&e!==92)return!1}return!0}else return t===92}chStartsBindingPattern(t){return t===91||t===123}hasFollowingBindingAtom(){let t=this.nextTokenStart(),r=this.codePointAtPos(t);return this.chStartsBindingPattern(r)||this.chStartsBindingIdentifier(r,t)}hasFollowingBindingIdentifier(){let t=this.nextTokenStart(),r=this.codePointAtPos(t);return this.chStartsBindingIdentifier(r,t)}startsUsingForOf(){let t=this.lookahead();return t.type===101&&!t.containsEsc?!1:(this.expectPlugin("explicitResourceManagement"),!0)}parseModuleItem(){return this.parseStatementLike(15)}parseStatementListItem(){return this.parseStatementLike(6|(!this.options.annexB||this.state.strict?0:8))}parseStatementOrSloppyAnnexBFunctionDeclaration(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,r=0;return this.options.annexB&&!this.state.strict&&(r|=4,t&&(r|=8)),this.parseStatementLike(r)}parseStatement(){return this.parseStatementLike(0)}parseStatementLike(t){let r=null;return this.match(26)&&(r=this.parseDecorators(!0)),this.parseStatementContent(t,r)}parseStatementContent(t,r){let e=this.state.type,s=this.startNode(),i=!!(t&2),a=!!(t&4),n=t&1;switch(e){case 60:return this.parseBreakContinueStatement(s,!0);case 63:return this.parseBreakContinueStatement(s,!1);case 64:return this.parseDebuggerStatement(s);case 90:return this.parseDoWhileStatement(s);case 91:return this.parseForStatement(s);case 68:if(this.lookaheadCharCode()===46)break;return a||this.raise(this.state.strict?f.StrictFunction:this.options.annexB?f.SloppyFunctionAnnexB:f.SloppyFunction,{at:this.state.startLoc}),this.parseFunctionStatement(s,!1,!i&&a);case 80:return i||this.unexpected(),this.parseClass(this.maybeTakeDecorators(r,s),!0);case 69:return this.parseIfStatement(s);case 70:return this.parseReturnStatement(s);case 71:return this.parseSwitchStatement(s);case 72:return this.parseThrowStatement(s);case 73:return this.parseTryStatement(s);case 105:if(this.hasFollowingLineBreak()||this.state.containsEsc||!this.hasFollowingBindingIdentifier())break;return this.expectPlugin("explicitResourceManagement"),!this.scope.inModule&&this.scope.inTopLevel?this.raise(f.UnexpectedUsingDeclaration,{at:this.state.startLoc}):i||this.raise(f.UnexpectedLexicalDeclaration,{at:this.state.startLoc}),this.parseVarStatement(s,"using");case 99:{if(this.state.containsEsc)break;let c=this.nextTokenStart(),y=this.codePointAtPos(c);if(y!==91&&(!i&&this.hasFollowingLineBreak()||!this.chStartsBindingIdentifier(y,c)&&y!==123))break}case 75:i||this.raise(f.UnexpectedLexicalDeclaration,{at:this.state.startLoc});case 74:{let c=this.state.value;return this.parseVarStatement(s,c)}case 92:return this.parseWhileStatement(s);case 76:return this.parseWithStatement(s);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(s);case 83:{let c=this.lookaheadCharCode();if(c===40||c===46)break}case 82:{!this.options.allowImportExportEverywhere&&!n&&this.raise(f.UnexpectedImportExport,{at:this.state.startLoc}),this.next();let c;return e===83?(c=this.parseImport(s),c.type==="ImportDeclaration"&&(!c.importKind||c.importKind==="value")&&(this.sawUnambiguousESM=!0)):(c=this.parseExport(s,r),(c.type==="ExportNamedDeclaration"&&(!c.exportKind||c.exportKind==="value")||c.type==="ExportAllDeclaration"&&(!c.exportKind||c.exportKind==="value")||c.type==="ExportDefaultDeclaration")&&(this.sawUnambiguousESM=!0)),this.assertModuleNodeAllowed(c),c}default:if(this.isAsyncFunction())return i||this.raise(f.AsyncFunctionInSingleStatementContext,{at:this.state.startLoc}),this.next(),this.parseFunctionStatement(s,!0,!i&&a)}let o=this.state.value,u=this.parseExpression();return q(e)&&u.type==="Identifier"&&this.eat(14)?this.parseLabeledStatement(s,o,u,t):this.parseExpressionStatement(s,u,r)}assertModuleNodeAllowed(t){!this.options.allowImportExportEverywhere&&!this.inModule&&this.raise(f.ImportOutsideModule,{at:t})}decoratorsEnabledBeforeExport(){return this.hasPlugin("decorators-legacy")?!0:this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")!==!1}maybeTakeDecorators(t,r,e){return t&&(r.decorators&&r.decorators.length>0?(typeof this.getPluginOption("decorators","decoratorsBeforeExport")!="boolean"&&this.raise(f.DecoratorsBeforeAfterExport,{at:r.decorators[0]}),r.decorators.unshift(...t)):r.decorators=t,this.resetStartLocationFromNode(r,t[0]),e&&this.resetStartLocationFromNode(e,r)),r}canHaveLeadingDecorator(){return this.match(80)}parseDecorators(t){let r=[];do r.push(this.parseDecorator());while(this.match(26));if(this.match(82))t||this.unexpected(),this.decoratorsEnabledBeforeExport()||this.raise(f.DecoratorExportClass,{at:this.state.startLoc});else if(!this.canHaveLeadingDecorator())throw this.raise(f.UnexpectedLeadingDecorator,{at:this.state.startLoc});return r}parseDecorator(){this.expectOnePlugin(["decorators","decorators-legacy"]);let t=this.startNode();if(this.next(),this.hasPlugin("decorators")){let r=this.state.startLoc,e;if(this.match(10)){let s=this.state.startLoc;this.next(),e=this.parseExpression(),this.expect(11),e=this.wrapParenthesis(s,e);let i=this.state.startLoc;t.expression=this.parseMaybeDecoratorArguments(e),this.getPluginOption("decorators","allowCallParenthesized")===!1&&t.expression!==e&&this.raise(f.DecoratorArgumentsOutsideParentheses,{at:i})}else{for(e=this.parseIdentifier(!1);this.eat(16);){let s=this.startNodeAt(r);s.object=e,this.match(136)?(this.classScope.usePrivateName(this.state.value,this.state.startLoc),s.property=this.parsePrivateName()):s.property=this.parseIdentifier(!0),s.computed=!1,e=this.finishNode(s,"MemberExpression")}t.expression=this.parseMaybeDecoratorArguments(e)}}else t.expression=this.parseExprSubscripts();return this.finishNode(t,"Decorator")}parseMaybeDecoratorArguments(t){if(this.eat(10)){let r=this.startNodeAtNode(t);return r.callee=t,r.arguments=this.parseCallExpressionArguments(11,!1),this.toReferencedList(r.arguments),this.finishNode(r,"CallExpression")}return t}parseBreakContinueStatement(t,r){return this.next(),this.isLineTerminator()?t.label=null:(t.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(t,r),this.finishNode(t,r?"BreakStatement":"ContinueStatement")}verifyBreakContinue(t,r){let e;for(e=0;e<this.state.labels.length;++e){let s=this.state.labels[e];if((t.label==null||s.name===t.label.name)&&(s.kind!=null&&(r||s.kind==="loop")||t.label&&r))break}if(e===this.state.labels.length){let s=r?"BreakStatement":"ContinueStatement";this.raise(f.IllegalBreakContinue,{at:t,type:s})}}parseDebuggerStatement(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")}parseHeaderExpression(){this.expect(10);let t=this.parseExpression();return this.expect(11),t}parseDoWhileStatement(t){return this.next(),this.state.labels.push(cs),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.expect(92),t.test=this.parseHeaderExpression(),this.eat(13),this.finishNode(t,"DoWhileStatement")}parseForStatement(t){this.next(),this.state.labels.push(cs);let r=null;if(this.isAwaitAllowed()&&this.eatContextual(96)&&(r=this.state.lastTokStartLoc),this.scope.enter(Fe),this.expect(10),this.match(13))return r!==null&&this.unexpected(r),this.parseFor(t,null);let e=this.isContextual(99),s=this.isContextual(105)&&!this.hasFollowingLineBreak(),i=e&&this.hasFollowingBindingAtom()||s&&this.hasFollowingBindingIdentifier()&&this.startsUsingForOf();if(this.match(74)||this.match(75)||i){let c=this.startNode(),y=this.state.value;this.next(),this.parseVar(c,!0,y);let g=this.finishNode(c,"VariableDeclaration"),T=this.match(58);return T&&s&&this.raise(f.ForInUsing,{at:g}),(T||this.isContextual(101))&&g.declarations.length===1?this.parseForIn(t,g,r):(r!==null&&this.unexpected(r),this.parseFor(t,g))}let a=this.isContextual(95),n=new vt,o=this.parseExpression(!0,n),u=this.isContextual(101);if(u&&(e&&this.raise(f.ForOfLet,{at:o}),r===null&&a&&o.type==="Identifier"&&this.raise(f.ForOfAsync,{at:o})),u||this.match(58)){this.checkDestructuringPrivate(n),this.toAssignable(o,!0);let c=u?"ForOfStatement":"ForInStatement";return this.checkLVal(o,{in:{type:c}}),this.parseForIn(t,o,r)}else this.checkExpressionErrors(n,!0);return r!==null&&this.unexpected(r),this.parseFor(t,o)}parseFunctionStatement(t,r,e){return this.next(),this.parseFunction(t,1|(e?2:0)|(r?8:0))}parseIfStatement(t){return this.next(),t.test=this.parseHeaderExpression(),t.consequent=this.parseStatementOrSloppyAnnexBFunctionDeclaration(),t.alternate=this.eat(66)?this.parseStatementOrSloppyAnnexBFunctionDeclaration():null,this.finishNode(t,"IfStatement")}parseReturnStatement(t){return!this.prodParam.hasReturn&&!this.options.allowReturnOutsideFunction&&this.raise(f.IllegalReturn,{at:this.state.startLoc}),this.next(),this.isLineTerminator()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")}parseSwitchStatement(t){this.next(),t.discriminant=this.parseHeaderExpression();let r=t.cases=[];this.expect(5),this.state.labels.push(Ph),this.scope.enter(Fe);let e;for(let s;!this.match(8);)if(this.match(61)||this.match(65)){let i=this.match(61);e&&this.finishNode(e,"SwitchCase"),r.push(e=this.startNode()),e.consequent=[],this.next(),i?e.test=this.parseExpression():(s&&this.raise(f.MultipleDefaultsInSwitch,{at:this.state.lastTokStartLoc}),s=!0,e.test=null),this.expect(14)}else e?e.consequent.push(this.parseStatementListItem()):this.unexpected();return this.scope.exit(),e&&this.finishNode(e,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(t,"SwitchStatement")}parseThrowStatement(t){return this.next(),this.hasPrecedingLineBreak()&&this.raise(f.NewlineAfterThrow,{at:this.state.lastTokEndLoc}),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")}parseCatchClauseParam(){let t=this.parseBindingAtom();return this.scope.enter(this.options.annexB&&t.type==="Identifier"?gr:0),this.checkLVal(t,{in:{type:"CatchClause"},binding:cl}),t}parseTryStatement(t){if(this.next(),t.block=this.parseBlock(),t.handler=null,this.match(62)){let r=this.startNode();this.next(),this.match(10)?(this.expect(10),r.param=this.parseCatchClauseParam(),this.expect(11)):(r.param=null,this.scope.enter(Fe)),r.body=this.withSmartMixTopicForbiddingContext(()=>this.parseBlock(!1,!1)),this.scope.exit(),t.handler=this.finishNode(r,"CatchClause")}return t.finalizer=this.eat(67)?this.parseBlock():null,!t.handler&&!t.finalizer&&this.raise(f.NoCatchOrFinally,{at:t}),this.finishNode(t,"TryStatement")}parseVarStatement(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return this.next(),this.parseVar(t,!1,r,e),this.semicolon(),this.finishNode(t,"VariableDeclaration")}parseWhileStatement(t){return this.next(),t.test=this.parseHeaderExpression(),this.state.labels.push(cs),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.finishNode(t,"WhileStatement")}parseWithStatement(t){return this.state.strict&&this.raise(f.StrictWith,{at:this.state.startLoc}),this.next(),t.object=this.parseHeaderExpression(),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.finishNode(t,"WithStatement")}parseEmptyStatement(t){return this.next(),this.finishNode(t,"EmptyStatement")}parseLabeledStatement(t,r,e,s){for(let a of this.state.labels)a.name===r&&this.raise(f.LabelRedeclaration,{at:e,labelName:r});let i=Mo(this.state.type)?"loop":this.match(71)?"switch":null;for(let a=this.state.labels.length-1;a>=0;a--){let n=this.state.labels[a];if(n.statementStart===t.start)n.statementStart=this.state.start,n.kind=i;else break}return this.state.labels.push({name:r,kind:i,statementStart:this.state.start}),t.body=s&8?this.parseStatementOrSloppyAnnexBFunctionDeclaration(!0):this.parseStatement(),this.state.labels.pop(),t.label=e,this.finishNode(t,"LabeledStatement")}parseExpressionStatement(t,r,e){return t.expression=r,this.semicolon(),this.finishNode(t,"ExpressionStatement")}parseBlock(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,e=arguments.length>2?arguments[2]:void 0,s=this.startNode();return t&&this.state.strictErrors.clear(),this.expect(5),r&&this.scope.enter(Fe),this.parseBlockBody(s,t,!1,8,e),r&&this.scope.exit(),this.finishNode(s,"BlockStatement")}isValidDirective(t){return t.type==="ExpressionStatement"&&t.expression.type==="StringLiteral"&&!t.expression.extra.parenthesized}parseBlockBody(t,r,e,s,i){let a=t.body=[],n=t.directives=[];this.parseBlockOrModuleBlockBody(a,r?n:void 0,e,s,i)}parseBlockOrModuleBlockBody(t,r,e,s,i){let a=this.state.strict,n=!1,o=!1;for(;!this.match(s);){let u=e?this.parseModuleItem():this.parseStatementListItem();if(r&&!o){if(this.isValidDirective(u)){let c=this.stmtToDirective(u);r.push(c),!n&&c.value.value==="use strict"&&(n=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}t.push(u)}i&&i.call(this,n),a||this.setStrict(!1),this.next()}parseFor(t,r){return t.init=r,this.semicolon(!1),t.test=this.match(13)?null:this.parseExpression(),this.semicolon(!1),t.update=this.match(11)?null:this.parseExpression(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,"ForStatement")}parseForIn(t,r,e){let s=this.match(58);return this.next(),s?e!==null&&this.unexpected(e):t.await=e!==null,r.type==="VariableDeclaration"&&r.declarations[0].init!=null&&(!s||!this.options.annexB||this.state.strict||r.kind!=="var"||r.declarations[0].id.type!=="Identifier")&&this.raise(f.ForInOfLoopInitializer,{at:r,type:s?"ForInStatement":"ForOfStatement"}),r.type==="AssignmentPattern"&&this.raise(f.InvalidLhs,{at:r,ancestor:{type:"ForStatement"}}),t.left=r,t.right=s?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,s?"ForInStatement":"ForOfStatement")}parseVar(t,r,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,i=t.declarations=[];for(t.kind=e;;){let a=this.startNode();if(this.parseVarId(a,e),a.init=this.eat(29)?r?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():null,a.init===null&&!s&&(a.id.type!=="Identifier"&&!(r&&(this.match(58)||this.isContextual(101)))?this.raise(f.DeclarationMissingInitializer,{at:this.state.lastTokEndLoc,kind:"destructuring"}):e==="const"&&!(this.match(58)||this.isContextual(101))&&this.raise(f.DeclarationMissingInitializer,{at:this.state.lastTokEndLoc,kind:"const"})),i.push(this.finishNode(a,"VariableDeclarator")),!this.eat(12))break}return t}parseVarId(t,r){r==="using"&&!this.inModule&&this.match(96)&&this.raise(f.AwaitInUsingBinding,{at:this.state.startLoc});let e=this.parseBindingAtom();this.checkLVal(e,{in:{type:"VariableDeclarator"},binding:r==="var"?mt:Be}),t.id=e}parseAsyncFunctionExpression(t){return this.parseFunction(t,8)}parseFunction(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=r&2,s=!!(r&1),i=s&&!(r&4),a=!!(r&8);this.initFunction(t,a),this.match(55)&&(e&&this.raise(f.GeneratorInSingleStatementContext,{at:this.state.startLoc}),this.next(),t.generator=!0),s&&(t.id=this.parseFunctionId(i));let n=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(de),this.prodParam.enter(Tt(a,t.generator)),s||(t.id=this.parseFunctionId()),this.parseFunctionParams(t,!1),this.withSmartMixTopicForbiddingContext(()=>{this.parseFunctionBodyAndFinish(t,s?"FunctionDeclaration":"FunctionExpression")}),this.prodParam.exit(),this.scope.exit(),s&&!e&&this.registerFunctionStatementId(t),this.state.maybeInArrowParameters=n,t}parseFunctionId(t){return t||q(this.state.type)?this.parseIdentifier():null}parseFunctionParams(t,r){this.expect(10),this.expressionScope.enter(ql()),t.params=this.parseBindingList(11,41,2|(r?4:0)),this.expressionScope.exit()}registerFunctionStatementId(t){t.id&&this.scope.declareName(t.id.name,!this.options.annexB||this.state.strict||t.generator||t.async?this.scope.treatFunctionsAsVar?mt:Be:Er,t.id.loc.start)}parseClass(t,r,e){this.next();let s=this.state.strict;return this.state.strict=!0,this.parseClassId(t,r,e),this.parseClassSuper(t),t.body=this.parseClassBody(!!t.superClass,s),this.finishNode(t,r?"ClassDeclaration":"ClassExpression")}isClassProperty(){return this.match(29)||this.match(13)||this.match(8)}isClassMethod(){return this.match(10)}isNonstaticConstructor(t){return!t.computed&&!t.static&&(t.key.name==="constructor"||t.key.value==="constructor")}parseClassBody(t,r){this.classScope.enter();let e={hadConstructor:!1,hadSuperClass:t},s=[],i=this.startNode();if(i.body=[],this.expect(5),this.withSmartMixTopicForbiddingContext(()=>{for(;!this.match(8);){if(this.eat(13)){if(s.length>0)throw this.raise(f.DecoratorSemicolon,{at:this.state.lastTokEndLoc});continue}if(this.match(26)){s.push(this.parseDecorator());continue}let a=this.startNode();s.length&&(a.decorators=s,this.resetStartLocationFromNode(a,s[0]),s=[]),this.parseClassMember(i,a,e),a.kind==="constructor"&&a.decorators&&a.decorators.length>0&&this.raise(f.DecoratorConstructor,{at:a})}}),this.state.strict=r,this.next(),s.length)throw this.raise(f.TrailingDecorator,{at:this.state.startLoc});return this.classScope.exit(),this.finishNode(i,"ClassBody")}parseClassMemberFromModifier(t,r){let e=this.parseIdentifier(!0);if(this.isClassMethod()){let s=r;return s.kind="method",s.computed=!1,s.key=e,s.static=!1,this.pushClassMethod(t,s,!1,!1,!1,!1),!0}else if(this.isClassProperty()){let s=r;return s.computed=!1,s.key=e,s.static=!1,t.body.push(this.parseClassProperty(s)),!0}return this.resetPreviousNodeTrailingComments(e),!1}parseClassMember(t,r,e){let s=this.isContextual(104);if(s){if(this.parseClassMemberFromModifier(t,r))return;if(this.eat(5)){this.parseClassStaticBlock(t,r);return}}this.parseClassMemberWithIsStatic(t,r,e,s)}parseClassMemberWithIsStatic(t,r,e,s){let i=r,a=r,n=r,o=r,u=r,c=i,y=i;if(r.static=s,this.parsePropertyNamePrefixOperator(r),this.eat(55)){c.kind="method";let j=this.match(136);if(this.parseClassElementName(c),j){this.pushClassPrivateMethod(t,a,!0,!1);return}this.isNonstaticConstructor(i)&&this.raise(f.ConstructorIsGenerator,{at:i.key}),this.pushClassMethod(t,i,!0,!1,!1,!1);return}let g=q(this.state.type)&&!this.state.containsEsc,T=this.match(136),C=this.parseClassElementName(r),M=this.state.startLoc;if(this.parsePostMemberNameModifiers(y),this.isClassMethod()){if(c.kind="method",T){this.pushClassPrivateMethod(t,a,!1,!1);return}let j=this.isNonstaticConstructor(i),K=!1;j&&(i.kind="constructor",e.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(f.DuplicateConstructor,{at:C}),j&&this.hasPlugin("typescript")&&r.override&&this.raise(f.OverrideOnConstructor,{at:C}),e.hadConstructor=!0,K=e.hadSuperClass),this.pushClassMethod(t,i,!1,!1,j,K)}else if(this.isClassProperty())T?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,n);else if(g&&C.name==="async"&&!this.isLineTerminator()){this.resetPreviousNodeTrailingComments(C);let j=this.eat(55);y.optional&&this.unexpected(M),c.kind="method";let K=this.match(136);this.parseClassElementName(c),this.parsePostMemberNameModifiers(y),K?this.pushClassPrivateMethod(t,a,j,!0):(this.isNonstaticConstructor(i)&&this.raise(f.ConstructorIsAsync,{at:i.key}),this.pushClassMethod(t,i,j,!0,!1,!1))}else if(g&&(C.name==="get"||C.name==="set")&&!(this.match(55)&&this.isLineTerminator())){this.resetPreviousNodeTrailingComments(C),c.kind=C.name;let j=this.match(136);this.parseClassElementName(i),j?this.pushClassPrivateMethod(t,a,!1,!1):(this.isNonstaticConstructor(i)&&this.raise(f.ConstructorIsAccessor,{at:i.key}),this.pushClassMethod(t,i,!1,!1,!1,!1)),this.checkGetterSetterParams(i)}else if(g&&C.name==="accessor"&&!this.isLineTerminator()){this.expectPlugin("decoratorAutoAccessors"),this.resetPreviousNodeTrailingComments(C);let j=this.match(136);this.parseClassElementName(n),this.pushClassAccessorProperty(t,u,j)}else this.isLineTerminator()?T?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,n):this.unexpected()}parseClassElementName(t){let{type:r,value:e}=this.state;if((r===130||r===131)&&t.static&&e==="prototype"&&this.raise(f.StaticPrototype,{at:this.state.startLoc}),r===136){e==="constructor"&&this.raise(f.ConstructorClassPrivateField,{at:this.state.startLoc});let s=this.parsePrivateName();return t.key=s,s}return this.parsePropertyName(t)}parseClassStaticBlock(t,r){var e;this.scope.enter(Ee|ut|ht);let s=this.state.labels;this.state.labels=[],this.prodParam.enter(Me);let i=r.body=[];this.parseBlockOrModuleBlockBody(i,void 0,!1,8),this.prodParam.exit(),this.scope.exit(),this.state.labels=s,t.body.push(this.finishNode(r,"StaticBlock")),(e=r.decorators)!=null&&e.length&&this.raise(f.DecoratorStaticBlock,{at:r})}pushClassProperty(t,r){!r.computed&&(r.key.name==="constructor"||r.key.value==="constructor")&&this.raise(f.ConstructorClassField,{at:r.key}),t.body.push(this.parseClassProperty(r))}pushClassPrivateProperty(t,r){let e=this.parseClassPrivateProperty(r);t.body.push(e),this.classScope.declarePrivateName(this.getPrivateNameSV(e.key),ss,e.key.loc.start)}pushClassAccessorProperty(t,r,e){if(!e&&!r.computed){let i=r.key;(i.name==="constructor"||i.value==="constructor")&&this.raise(f.ConstructorClassField,{at:i})}let s=this.parseClassAccessorProperty(r);t.body.push(s),e&&this.classScope.declarePrivateName(this.getPrivateNameSV(s.key),ss,s.key.loc.start)}pushClassMethod(t,r,e,s,i,a){t.body.push(this.parseMethod(r,e,s,i,a,"ClassMethod",!0))}pushClassPrivateMethod(t,r,e,s){let i=this.parseMethod(r,e,s,!1,!1,"ClassPrivateMethod",!0);t.body.push(i);let a=i.kind==="get"?i.static?gl:Al:i.kind==="set"?i.static?Pl:Tl:ss;this.declareClassPrivateMethodInScope(i,a)}declareClassPrivateMethodInScope(t,r){this.classScope.declarePrivateName(this.getPrivateNameSV(t.key),r,t.key.loc.start)}parsePostMemberNameModifiers(t){}parseClassPrivateProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassPrivateProperty")}parseClassProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassProperty")}parseClassAccessorProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassAccessorProperty")}parseInitializer(t){this.scope.enter(Ee|ht),this.expressionScope.enter(_r()),this.prodParam.enter(Me),t.value=this.eat(29)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}parseClassId(t,r,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:vr;if(q(this.state.type))t.id=this.parseIdentifier(),r&&this.declareNameFromIdentifier(t.id,s);else if(e||!r)t.id=null;else throw this.raise(f.MissingClassName,{at:this.state.startLoc})}parseClassSuper(t){t.superClass=this.eat(81)?this.parseExprSubscripts():null}parseExport(t,r){let e=this.maybeParseExportDefaultSpecifier(t),s=!e||this.eat(12),i=s&&this.eatExportStar(t),a=i&&this.maybeParseExportNamespaceSpecifier(t),n=s&&(!a||this.eat(12)),o=e||i;if(i&&!a){if(e&&this.unexpected(),r)throw this.raise(f.UnsupportedDecoratorExport,{at:t});return this.parseExportFrom(t,!0),this.finishNode(t,"ExportAllDeclaration")}let u=this.maybeParseExportNamedSpecifiers(t);e&&s&&!i&&!u&&this.unexpected(null,5),a&&n&&this.unexpected(null,97);let c;if(o||u){if(c=!1,r)throw this.raise(f.UnsupportedDecoratorExport,{at:t});this.parseExportFrom(t,o)}else c=this.maybeParseExportDeclaration(t);if(o||u||c){var y;let g=t;if(this.checkExport(g,!0,!1,!!g.source),((y=g.declaration)==null?void 0:y.type)==="ClassDeclaration")this.maybeTakeDecorators(r,g.declaration,g);else if(r)throw this.raise(f.UnsupportedDecoratorExport,{at:t});return this.finishNode(g,"ExportNamedDeclaration")}if(this.eat(65)){let g=t,T=this.parseExportDefaultExpression();if(g.declaration=T,T.type==="ClassDeclaration")this.maybeTakeDecorators(r,T,g);else if(r)throw this.raise(f.UnsupportedDecoratorExport,{at:t});return this.checkExport(g,!0,!0),this.finishNode(g,"ExportDefaultDeclaration")}this.unexpected(null,5)}eatExportStar(t){return this.eat(55)}maybeParseExportDefaultSpecifier(t){if(this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom");let r=this.startNode();return r.exported=this.parseIdentifier(!0),t.specifiers=[this.finishNode(r,"ExportDefaultSpecifier")],!0}return!1}maybeParseExportNamespaceSpecifier(t){if(this.isContextual(93)){t.specifiers||(t.specifiers=[]);let r=this.startNodeAt(this.state.lastTokStartLoc);return this.next(),r.exported=this.parseModuleExportName(),t.specifiers.push(this.finishNode(r,"ExportNamespaceSpecifier")),!0}return!1}maybeParseExportNamedSpecifiers(t){if(this.match(5)){t.specifiers||(t.specifiers=[]);let r=t.exportKind==="type";return t.specifiers.push(...this.parseExportSpecifiers(r)),t.source=null,t.declaration=null,this.hasPlugin("importAssertions")&&(t.assertions=[]),!0}return!1}maybeParseExportDeclaration(t){return this.shouldParseExportDeclaration()?(t.specifiers=[],t.source=null,this.hasPlugin("importAssertions")&&(t.assertions=[]),t.declaration=this.parseExportDeclaration(t),!0):!1}isAsyncFunction(){if(!this.isContextual(95))return!1;let t=this.nextTokenStart();return!as.test(this.input.slice(this.state.pos,t))&&this.isUnparsedContextual(t,"function")}parseExportDefaultExpression(){let t=this.startNode();if(this.match(68))return this.next(),this.parseFunction(t,5);if(this.isAsyncFunction())return this.next(),this.next(),this.parseFunction(t,13);if(this.match(80))return this.parseClass(t,!0,!0);if(this.match(26))return this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")===!0&&this.raise(f.DecoratorBeforeExport,{at:this.state.startLoc}),this.parseClass(this.maybeTakeDecorators(this.parseDecorators(!1),this.startNode()),!0,!0);if(this.match(75)||this.match(74)||this.isLet())throw this.raise(f.UnsupportedDefaultExport,{at:this.state.startLoc});let r=this.parseMaybeAssignAllowIn();return this.semicolon(),r}parseExportDeclaration(t){return this.match(80)?this.parseClass(this.startNode(),!0,!1):this.parseStatementListItem()}isExportDefaultSpecifier(){let{type:t}=this.state;if(q(t)){if(t===95&&!this.state.containsEsc||t===99)return!1;if((t===128||t===127)&&!this.state.containsEsc){let{type:s}=this.lookahead();if(q(s)&&s!==97||s===5)return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(65))return!1;let r=this.nextTokenStart(),e=this.isUnparsedContextual(r,"from");if(this.input.charCodeAt(r)===44||q(this.state.type)&&e)return!0;if(this.match(65)&&e){let s=this.input.charCodeAt(this.nextTokenStartSince(r+4));return s===34||s===39}return!1}parseExportFrom(t,r){if(this.eatContextual(97)){t.source=this.parseImportSource(),this.checkExport(t);let e=this.maybeParseImportAssertions();e&&(t.assertions=e,this.checkJSONModuleImport(t))}else r&&this.unexpected();this.semicolon()}shouldParseExportDeclaration(){let{type:t}=this.state;return t===26&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))?(this.getPluginOption("decorators","decoratorsBeforeExport")===!0&&this.raise(f.DecoratorBeforeExport,{at:this.state.startLoc}),!0):t===74||t===75||t===68||t===80||this.isLet()||this.isAsyncFunction()}checkExport(t,r,e,s){if(r){if(e){if(this.checkDuplicateExports(t,"default"),this.hasPlugin("exportDefaultFrom")){var i;let a=t.declaration;a.type==="Identifier"&&a.name==="from"&&a.end-a.start===4&&!((i=a.extra)!=null&&i.parenthesized)&&this.raise(f.ExportDefaultFromAsIdentifier,{at:a})}}else if(t.specifiers&&t.specifiers.length)for(let a of t.specifiers){let{exported:n}=a,o=n.type==="Identifier"?n.name:n.value;if(this.checkDuplicateExports(a,o),!s&&a.local){let{local:u}=a;u.type!=="Identifier"?this.raise(f.ExportBindingIsString,{at:a,localName:u.value,exportName:o}):(this.checkReservedWord(u.name,u.loc.start,!0,!1),this.scope.checkLocalExport(u))}}else if(t.declaration){if(t.declaration.type==="FunctionDeclaration"||t.declaration.type==="ClassDeclaration"){let a=t.declaration.id;if(!a)throw new Error("Assertion failure");this.checkDuplicateExports(t,a.name)}else if(t.declaration.type==="VariableDeclaration")for(let a of t.declaration.declarations)this.checkDeclaration(a.id)}}}checkDeclaration(t){if(t.type==="Identifier")this.checkDuplicateExports(t,t.name);else if(t.type==="ObjectPattern")for(let r of t.properties)this.checkDeclaration(r);else if(t.type==="ArrayPattern")for(let r of t.elements)r&&this.checkDeclaration(r);else t.type==="ObjectProperty"?this.checkDeclaration(t.value):t.type==="RestElement"?this.checkDeclaration(t.argument):t.type==="AssignmentPattern"&&this.checkDeclaration(t.left)}checkDuplicateExports(t,r){this.exportedIdentifiers.has(r)&&(r==="default"?this.raise(f.DuplicateDefaultExport,{at:t}):this.raise(f.DuplicateExport,{at:t,exportName:r})),this.exportedIdentifiers.add(r)}parseExportSpecifiers(t){let r=[],e=!0;for(this.expect(5);!this.eat(8);){if(e)e=!1;else if(this.expect(12),this.eat(8))break;let s=this.isContextual(128),i=this.match(131),a=this.startNode();a.local=this.parseModuleExportName(),r.push(this.parseExportSpecifier(a,i,t,s))}return r}parseExportSpecifier(t,r,e,s){return this.eatContextual(93)?t.exported=this.parseModuleExportName():r?t.exported=Kl(t.local):t.exported||(t.exported=me(t.local)),this.finishNode(t,"ExportSpecifier")}parseModuleExportName(){if(this.match(131)){let t=this.parseStringLiteral(this.state.value),r=t.value.match(Ah);return r&&this.raise(f.ModuleExportNameHasLoneSurrogate,{at:t,surrogateCharCode:r[0].charCodeAt(0)}),t}return this.parseIdentifier(!0)}isJSONModuleImport(t){return t.assertions!=null?t.assertions.some(r=>{let{key:e,value:s}=r;return s.value==="json"&&(e.type==="Identifier"?e.name==="type":e.value==="type")}):!1}checkImportReflection(t){if(t.module){var r;(t.specifiers.length!==1||t.specifiers[0].type!=="ImportDefaultSpecifier")&&this.raise(f.ImportReflectionNotBinding,{at:t.specifiers[0].loc.start}),((r=t.assertions)==null?void 0:r.length)>0&&this.raise(f.ImportReflectionHasAssertion,{at:t.specifiers[0].loc.start})}}checkJSONModuleImport(t){if(this.isJSONModuleImport(t)&&t.type!=="ExportAllDeclaration"){let{specifiers:r}=t;if(r!=null){let e=r.find(s=>{let i;if(s.type==="ExportSpecifier"?i=s.local:s.type==="ImportSpecifier"&&(i=s.imported),i!==void 0)return i.type==="Identifier"?i.name!=="default":i.value!=="default"});e!==void 0&&this.raise(f.ImportJSONBindingNotDefault,{at:e.loc.start})}}}parseMaybeImportReflection(t){let r=!1;if(this.isContextual(125)){let e=this.lookahead(),s=e.type;q(s)?(s!==97||this.input.charCodeAt(this.nextTokenStartSince(e.end))===102)&&(r=!0):s!==12&&(r=!0)}r?(this.expectPlugin("importReflection"),this.next(),t.module=!0):this.hasPlugin("importReflection")&&(t.module=!1)}parseImport(t){if(t.specifiers=[],!this.match(131)){this.parseMaybeImportReflection(t);let s=!this.maybeParseDefaultImportSpecifier(t)||this.eat(12),i=s&&this.maybeParseStarImportSpecifier(t);s&&!i&&this.parseNamedImportSpecifiers(t),this.expectContextual(97)}t.source=this.parseImportSource();let r=this.maybeParseImportAssertions();if(r)t.assertions=r;else{let e=this.maybeParseModuleAttributes();e&&(t.attributes=e)}return this.checkImportReflection(t),this.checkJSONModuleImport(t),this.semicolon(),this.finishNode(t,"ImportDeclaration")}parseImportSource(){return this.match(131)||this.unexpected(),this.parseExprAtom()}shouldParseDefaultImport(t){return q(this.state.type)}parseImportSpecifierLocal(t,r,e){r.local=this.parseIdentifier(),t.specifiers.push(this.finishImportSpecifier(r,e))}finishImportSpecifier(t,r){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Be;return this.checkLVal(t.local,{in:{type:r},binding:e}),this.finishNode(t,r)}parseAssertEntries(){let t=[],r=new Set;do{if(this.match(8))break;let e=this.startNode(),s=this.state.value;if(r.has(s)&&this.raise(f.ModuleAttributesWithDuplicateKeys,{at:this.state.startLoc,key:s}),r.add(s),this.match(131)?e.key=this.parseStringLiteral(s):e.key=this.parseIdentifier(!0),this.expect(14),!this.match(131))throw this.raise(f.ModuleAttributeInvalidValue,{at:this.state.startLoc});e.value=this.parseStringLiteral(this.state.value),t.push(this.finishNode(e,"ImportAttribute"))}while(this.eat(12));return t}maybeParseModuleAttributes(){if(this.match(76)&&!this.hasPrecedingLineBreak())this.expectPlugin("moduleAttributes"),this.next();else return this.hasPlugin("moduleAttributes")?[]:null;let t=[],r=new Set;do{let e=this.startNode();if(e.key=this.parseIdentifier(!0),e.key.name!=="type"&&this.raise(f.ModuleAttributeDifferentFromType,{at:e.key}),r.has(e.key.name)&&this.raise(f.ModuleAttributesWithDuplicateKeys,{at:e.key,key:e.key.name}),r.add(e.key.name),this.expect(14),!this.match(131))throw this.raise(f.ModuleAttributeInvalidValue,{at:this.state.startLoc});e.value=this.parseStringLiteral(this.state.value),this.finishNode(e,"ImportAttribute"),t.push(e)}while(this.eat(12));return t}maybeParseImportAssertions(){if(this.isContextual(94)&&!this.hasPrecedingLineBreak())this.expectPlugin("importAssertions"),this.next();else return this.hasPlugin("importAssertions")?[]:null;this.eat(5);let t=this.parseAssertEntries();return this.eat(8),t}maybeParseDefaultImportSpecifier(t){return this.shouldParseDefaultImport(t)?(this.parseImportSpecifierLocal(t,this.startNode(),"ImportDefaultSpecifier"),!0):!1}maybeParseStarImportSpecifier(t){if(this.match(55)){let r=this.startNode();return this.next(),this.expectContextual(93),this.parseImportSpecifierLocal(t,r,"ImportNamespaceSpecifier"),!0}return!1}parseNamedImportSpecifiers(t){let r=!0;for(this.expect(5);!this.eat(8);){if(r)r=!1;else{if(this.eat(14))throw this.raise(f.DestructureNamedImport,{at:this.state.startLoc});if(this.expect(12),this.eat(8))break}let e=this.startNode(),s=this.match(131),i=this.isContextual(128);e.imported=this.parseModuleExportName();let a=this.parseImportSpecifier(e,s,t.importKind==="type"||t.importKind==="typeof",i,void 0);t.specifiers.push(a)}}parseImportSpecifier(t,r,e,s,i){if(this.eatContextual(93))t.local=this.parseIdentifier();else{let{imported:a}=t;if(r)throw this.raise(f.ImportBindingIsString,{at:t,importName:a.value});this.checkReservedWord(a.name,t.loc.start,!0,!0),t.local||(t.local=me(a))}return this.finishImportSpecifier(t,"ImportSpecifier",i)}isThisParam(t){return t.type==="Identifier"&&t.name==="this"}},Yr=class extends vh{constructor(t,r){t=p(t),super(t,r),this.options=t,this.initializeScopes(),this.plugins=Eh(this.options.plugins),this.filename=t.sourceFilename}getScopeHandler(){return is}parse(){this.enterInitialScopes();let t=this.startNode(),r=this.startNode();return this.nextToken(),t.errors=null,this.parseTopLevel(t,r),t.errors=this.state.errors,t}};function Eh(t){let r=new Map;for(let e of t){let[s,i]=Array.isArray(e)?e:[e,{}];r.has(s)||r.set(s,i||{})}return r}function Ch(t,r){var e;if(((e=r)==null?void 0:e.sourceType)==="unambiguous"){r=Object.assign({},r);try{r.sourceType="module";let s=Xe(r,t),i=s.parse();if(s.sawUnambiguousESM)return i;if(s.ambiguousScriptDifferentAst)try{return r.sourceType="script",Xe(r,t).parse()}catch{}else i.program.sourceType="script";return i}catch(s){try{return r.sourceType="script",Xe(r,t).parse()}catch{}throw s}}else return Xe(r,t).parse()}function bh(t,r){let e=Xe(r,t);return e.options.strictMode&&(e.state.strict=!0),e.getExpression()}function Sh(t){let r={};for(let e of Object.keys(t))r[e]=ce(t[e]);return r}var wh=Sh(Z);function Xe(t,r){let e=Yr;return t!=null&&t.plugins&&(yh(t.plugins),e=Ih(t.plugins)),new e(t,r)}var Qr={};function Ih(t){let r=xh.filter(i=>J(t,i)),e=r.join("/"),s=Qr[e];if(!s){s=Yr;for(let i of r)s=Xr[i](s);Qr[e]=s}return s}l.parse=Ch,l.parseExpression=bh,l.tokTypes=wh}}),Xf=$({"src/language-js/parse/json.js"(l,h){"use strict";U();var p=Io(),d=lr(),x=ko(),P=Do();function m(){let w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{allowComments:L=!0}=w;return function(_){let{parseExpression:G}=Fo(),N;try{N=G(_,{tokens:!0,ranges:!0})}catch(O){throw P(O)}if(!L&&p(N.comments))throw v(N.comments[0],"Comment");return S(N),N}}function v(w,L){let[A,_]=[w.loc.start,w.loc.end].map(G=>{let{line:N,column:O}=G;return{line:N,column:O+1}});return d(`${L} is not allowed in JSON.`,{start:A,end:_})}function S(w){switch(w.type){case"ArrayExpression":for(let L of w.elements)L!==null&&S(L);return;case"ObjectExpression":for(let L of w.properties)S(L);return;case"ObjectProperty":if(w.computed)throw v(w.key,"Computed key");if(w.shorthand)throw v(w.key,"Shorthand property");w.key.type!=="Identifier"&&S(w.key),S(w.value);return;case"UnaryExpression":{let{operator:L,argument:A}=w;if(L!=="+"&&L!=="-")throw v(w,`Operator '${w.operator}'`);if(A.type==="NumericLiteral"||A.type==="Identifier"&&(A.name==="Infinity"||A.name==="NaN"))return;throw v(A,`Operator '${L}' before '${A.type}'`)}case"Identifier":if(w.name!=="Infinity"&&w.name!=="NaN"&&w.name!=="undefined")throw v(w,`Identifier '${w.name}'`);return;case"TemplateLiteral":if(p(w.expressions))throw v(w.expressions[0],"'TemplateLiteral' with expression");for(let L of w.quasis)S(L);return;case"NullLiteral":case"BooleanLiteral":case"NumericLiteral":case"StringLiteral":case"TemplateElement":return;default:throw v(w,`'${w.type}'`)}}var k=m(),F={json:x({parse:k,hasPragma(){return!0}}),json5:x(k),"json-stringify":x({parse:m({allowComments:!1}),astFormat:"estree-json"})};h.exports=F}});U();var Yf=kf(),Qf=po(),Zf=Of(),Ue=ko(),ed=Do(),td=Jf(),sd=Xf(),rd={sourceType:"module",allowImportExportEverywhere:!0,allowReturnOutsideFunction:!0,allowSuperOutsideMethod:!0,allowUndeclaredExports:!0,errorRecovery:!0,createParenthesizedExpressions:!0,plugins:["doExpressions","exportDefaultFrom","functionBind","functionSent","throwExpressions","partialApplication",["decorators",{decoratorsBeforeExport:!1}],"importAssertions","decimal","moduleBlocks","asyncDoExpressions","regexpUnicodeSets","destructuringPrivate","decoratorAutoAccessors"],tokens:!0,ranges:!0},id=["recordAndTuple",{syntaxType:"hash"}],no="v8intrinsic",oo=[["pipelineOperator",{proposal:"hack",topicToken:"%"}],["pipelineOperator",{proposal:"minimal"}],["pipelineOperator",{proposal:"fsharp"}]],he=function(l){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:rd;return Object.assign(Object.assign({},h),{},{plugins:[...h.plugins,...l]})},ad=/@(?:no)?flow\b/;function nd(l,h){if(h.filepath&&h.filepath.endsWith(".js.flow"))return!0;let p=Qf(l);p&&(l=l.slice(p.length));let d=Zf(l,0);return d!==!1&&(l=l.slice(0,d)),ad.test(l)}function od(l,h,p){let d=Fo()[l],x=d(h,p),P=x.errors.find(m=>!fd.has(m.reasonCode));if(P)throw P;return x}function $e(l){for(var h=arguments.length,p=new Array(h>1?h-1:0),d=1;d<h;d++)p[d-1]=arguments[d];return function(x,P){let m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if((m.parser==="babel"||m.parser==="__babel_estree")&&nd(x,m))return m.parser="babel-flow",Lo(x,P,m);let v=p;m.__babelSourceType==="script"&&(v=v.map(w=>Object.assign(Object.assign({},w),{},{sourceType:"script"}))),/#[[{]/.test(x)&&(v=v.map(w=>he([id],w)));let S=/%[A-Z]/.test(x);x.includes("|>")?v=(S?[...oo,no]:oo).flatMap(L=>v.map(A=>he([L],A))):S&&(v=v.map(w=>he([no],w)));let{result:k,error:F}=Yf(...v.map(w=>()=>od(l,x,w)));if(!k)throw ed(F);return m.originalText=x,td(k,m)}}var ld=$e("parse",he(["jsx","flow"])),Lo=$e("parse",he(["jsx",["flow",{all:!0,enums:!0}]])),hd=$e("parse",he(["jsx","typescript"]),he(["typescript"])),ud=$e("parse",he(["jsx","flow","estree"])),cd=$e("parseExpression",he(["jsx"])),pd=$e("parseExpression",he(["typescript"])),fd=new Set(["StrictNumericEscape","StrictWith","StrictOctalLiteral","StrictDelete","StrictEvalArguments","StrictEvalArgumentsBinding","StrictFunction","EmptyTypeArguments","EmptyTypeParameters","ConstructorHasTypeParameters","UnsupportedParameterPropertyKind","UnexpectedParameterModifier","MixedLabeledAndUnlabeledElements","InvalidTupleMemberLabel","NonClassMethodPropertyHasAbstractModifer","ReadonlyForMethodSignature","ClassMethodHasDeclare","ClassMethodHasReadonly","InvalidModifierOnTypeMember","DuplicateAccessibilityModifier","IndexSignatureHasDeclare","DecoratorExportClass","ParamDupe","InvalidDecimal","RestTrailingComma","UnsupportedParameterDecorator","UnterminatedJsxContent","UnexpectedReservedWord","ModuleAttributesWithDuplicateKeys","LineTerminatorBeforeArrow","InvalidEscapeSequenceTemplate","NonAbstractClassHasAbstractMethod","UnsupportedPropertyDecorator","OptionalTypeBeforeRequired","PatternIsOptional","OptionalBindingPattern","DeclareClassFieldHasInitializer","TypeImportCannotSpecifyDefaultAndNamed","DeclareFunctionHasImplementation","ConstructorClassField","VarRedeclaration","InvalidPrivateFieldResolution","DuplicateExport"]),lo=Ue(ld),ho=Ue(hd),uo=Ue(cd),dd=Ue(pd);Oo.exports={parsers:Object.assign(Object.assign({babel:lo,"babel-flow":Ue(Lo),"babel-ts":ho},sd),{},{__js_expression:uo,__vue_expression:uo,__vue_ts_expression:dd,__vue_event_binding:lo,__vue_ts_event_binding:ho,__babel_estree:Ue(ud)})}});return md();});