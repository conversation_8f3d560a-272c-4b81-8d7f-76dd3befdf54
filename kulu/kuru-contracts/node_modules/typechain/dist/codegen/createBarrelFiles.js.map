{"version": 3, "file": "createBarrelFiles.js", "sourceRoot": "", "sources": ["../../src/codegen/createBarrelFiles.ts"], "names": [], "mappings": ";;;AAAA,mCAA4D;AAC5D,+BAA4B;AAE5B,2DAAuD;AAGvD;;;;GAIG;AACH,SAAgB,iBAAiB,CAC/B,KAAe,EACf,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,EAAkE;IAE7G,MAAM,aAAa,GAAyC,IAAA,kBAAS,EACnE,IAAA,gBAAO,EAAC,KAAK,CAAC,GAAG,CAAC,YAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAC7C,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,CAAA;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;IACtF,MAAM,kBAAkB,GAAyC,IAAA,kBAAS,EACxE,IAAA,gBAAO,EAAC,cAAc,CAAC,GAAG,CAAC,YAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EACtD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,CAAA;IAED,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;IAC/F,MAAM,QAAQ,GAAa,EAAE,CAAA;IAE7B,KAAK,MAAM,SAAS,IAAI,WAAW,EAAE;QACnC,IAAI,CAAC,SAAS;YAAE,SAAQ;QAExB,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjC,OAAO,IAAI,CAAC,MAAM,EAAE;YAClB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACvC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAElC,IAAI,CAAC,CAAC,GAAG,IAAI,kBAAkB,CAAC,EAAE;gBAChC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACnB;iBAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC5D,kBAAkB,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACpC;YAED,IAAI,CAAC,GAAG,EAAE,CAAA;SACX;KACF;IAED,OAAO,IAAA,aAAI,EAAC,CAAC,GAAG,WAAW,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACtD,MAAM,UAAU,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QAE1D,MAAM,iBAAiB,GAAG,UAAU;aACjC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,MAAM,mBAAmB,GAAG,IAAA,kBAAS,EAAC,CAAC,CAAC,CAAA;YAExC,IAAI,QAAQ;gBACV,OAAO;oBACL,oBAAoB,mBAAmB,YAAY,CAAC,IAAI;oBACxD,iBAAiB,mBAAmB,KAAK;iBAC1C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEd,OAAO,eAAe,mBAAmB,YAAY,CAAC,IAAI,CAAA;QAC5D,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEb,MAAM,SAAS,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QACpD,MAAM,YAAY,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAA;YACzD,MAAM,IAAI,GAAG,GAAG,IAAA,6BAAa,EAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAA;YAC5C,iEAAiE;YACjE,qDAAqD;YACrD,OAAO,GAAG,aAAa,MAAM,IAAI,cAAc,IAAI,GAAG,YAAY,IAAI,CAAA;QACxE,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEb,OAAO;YACL,IAAI,EAAE,YAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;YAClC,QAAQ,EAAE,CAAC,iBAAiB,GAAG,IAAI,GAAG,YAAY,CAAC,CAAC,IAAI,EAAE;SAC3D,CAAA;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAtED,8CAsEC"}