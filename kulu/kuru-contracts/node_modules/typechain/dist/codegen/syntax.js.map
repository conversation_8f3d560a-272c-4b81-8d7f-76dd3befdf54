{"version": 3, "file": "syntax.js", "sourceRoot": "", "sources": ["../../src/codegen/syntax.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACH,kBAAkB;AAClB,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC;IAC7B,OAAO,EAAK,OAAO,EAAO,MAAM;IAChC,OAAO,EAAK,OAAO,EAAO,OAAO;IACjC,UAAU,EAAE,UAAU,EAAI,SAAS;IACnC,QAAQ,EAAI,IAAI,EAAU,MAAM;IAChC,MAAM,EAAM,QAAQ,EAAM,SAAS;IACnC,OAAO,EAAK,SAAS,EAAK,KAAK;IAC/B,UAAU,EAAE,IAAI,EAAU,QAAQ;IAClC,IAAI,EAAQ,YAAY,EAAE,KAAK;IAC/B,MAAM,EAAM,QAAQ,EAAM,OAAO;IACjC,QAAQ,EAAI,MAAM,EAAQ,OAAO;IACjC,MAAM,EAAM,KAAK,EAAS,QAAQ;IAClC,KAAK,EAAO,MAAM,EAAQ,OAAO;IACjC,MAAM,EAAM,OAAO;CACpB,CAAC,CAAA;AAEF;;;;;;GAMG;AACH,SAAgB,0BAA0B,CAAC,cAAsB;IAC/D,IAAI,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;QACtC,OAAO,IAAI,cAAc,EAAE,CAAA;KAC5B;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AALD,gEAKC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,WAAqB,EAAE,UAAkB;IAC1E,MAAM,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAA;IAC9C,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;QACjC,IAAI,IAAI,MAAM,CAAC,MAAM,UAAU,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;YAAE,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;IAC7F,CAAC,CAAC,CAAA;IACF,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;AACpC,CAAC;AAND,gDAMC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,WAAqB,EAAE,eAAuB;IAC7E,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,eAAe,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;AACvG,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CAAC,WAAqB,EAAE,eAAuB;IACxF,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,eAAe,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;AAC5G,CAAC;AAFD,kEAEC;AAID;;GAEG;AACH,SAAgB,+BAA+B,CAC7C,eAAsD,EACtD,UAAkB;IAElB,MAAM,UAAU,GAAG,OAAO,CAAA;IAC1B,OAAO,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;SACnC,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,EAAE,EAAE,CACtC,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC;QACpC,CAAC,CAAC,2BAA2B,CACzB,kBAAkB,CAAC,WAAW,EAAE,UAAU,CAAC,EAC3C,eAAe,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAC7C;QACH,CAAC,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,eAAe,CAAC,CAC1F;SACA,IAAI,CAAC,IAAI,CAAC,CAAA;AACf,CAAC;AAfD,0EAeC"}