{"version": 3, "file": "parseArgs.js", "sourceRoot": "", "sources": ["../../src/cli/parseArgs.ts"], "names": [], "mappings": ";;;AAAA,+DAA+D;AAE/D,MAAM,oBAAoB,GAAG,UAAU,CAAA;AAevC,SAAgB,SAAS;IACvB,MAAM,UAAU,GAAG,IAAA,4BAAe,EAChC;QACE,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,IAAI;YACnB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,CAAC,oBAAoB,CAAC;YACpC,WAAW,EACT,8JAA8J;SACjK;QACD,MAAM,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,WAAW,EACT,4MAA4M;SAC/M;QACD,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,uCAAuC,EAAE;QACjG,WAAW,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,+FAA+F;SAC7G;QACD,2BAA2B,EAAE;YAC3B,IAAI,EAAE,OAAO;YACb,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,gIAAgI;SAC9I;QACD,wDAAwD;QACxD,mBAAmB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE;QAC3D,YAAY,EAAE;YACZ,IAAI,EAAE,OAAO;YACb,YAAY,EAAE,KAAK;YACnB,WAAW,EACT,mGAAmG;SACtG;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,OAAO;YACb,YAAY,EAAE,KAAK;YACnB,WAAW,EACT,wGAAwG;SAC3G;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAE,OAAO;YACb,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,sFAAsF;SACpG;QACD,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE,sBAAsB,EAAE;KAC9F,EACD;QACE,OAAO,EAAE,MAAM;QACf,qBAAqB,EAAE;YACrB;gBACE,OAAO,EAAE;;kCAEe;aACzB;SACF;QACD,qBAAqB,EAAE;YACrB;gBACE,MAAM,EAAE,eAAe;gBACvB,OAAO,EAAE;;;;oGAIiF;aAC3F;SACF;KACF,CACF,CAAA;IAED,OAAO;QACL,KAAK,EAAE,UAAU,CAAC,IAAI;QACtB,MAAM,EAAE,UAAU,CAAC,SAAS,CAAC;QAC7B,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;QACjC,MAAM,EAAE,UAAU,CAAC,MAAM;QACzB,KAAK,EAAE;YACL,uBAAuB,EAAE,UAAU,CAAC,2BAA2B,CAAC;YAChE,iBAAiB,EAAE,UAAU,CAAC,oBAAoB,CAAC;YACnD,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC;YACnC,aAAa,EAAE,UAAU,CAAC,gBAAgB,CAAC;SAC5C;KACF,CAAA;AACH,CAAC;AAlFD,8BAkFC"}