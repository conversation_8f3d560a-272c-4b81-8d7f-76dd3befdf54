{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/cli/cli.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mDAAoC;AAEpC,4DAAwD;AAExD,0CAAiD;AACjD,wCAAoC;AACpC,4CAAwC;AACxC,2CAAuC;AAEvC,KAAK,UAAU,IAAI;IACjB,CAAC;IAAC,MAAc,CAAC,MAAM,GAAG,IAAI,CAAA;IAC9B,MAAM,SAAS,GAAG,IAAA,qBAAS,GAAE,CAAA;IAC7B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IAEzB,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK,CAAC,CAAA;IACrD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,GAAG,KAAK,SAAS,CAAC,KAAK,sCAAsC,GAAG,EAAE,CAAC,CAAA;KAC7G;IAED,MAAM,MAAM,GAAW;QACrB,GAAG;QACH,MAAM,EAAE,SAAS,CAAC,MAAM;QACxB,MAAM,EAAE,SAAS,CAAC,MAAM;QACxB,QAAQ,EAAE,KAAK;QACf,cAAc,EAAE,KAAK;QACrB,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,IAAA,wBAAgB,EAAC,KAAK,CAAC;QACvD,QAAQ;QACR,KAAK,EAAE;YACL,GAAG,SAAS,CAAC,KAAK;YAClB,WAAW,EAAE,SAAS;SACvB;KACF,CAAA;IAED,MAAM,MAAM,GAAG,MAAM,IAAA,2BAAY,EAAC,MAAM,CAAC,CAAA;IACzC,sCAAsC;IACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,cAAc,WAAW,CAAC,CAAA;AACzE,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAA;IAE1C,MAAM,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAA;IACvE,IAAI,kBAAkB,EAAE;QACtB,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;KACvC;SAAM;QACL,eAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAA;KACxE;IACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAEF,SAAS,iBAAiB,CAAC,GAAW,EAAE,cAAwB;;IAC9D,IAAI,GAAG,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,cAAc,CAAC,CAAA;IAEnC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,iHAAiH;QACjH,MAAM,KAAK,GAAG,MAAA,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,0CAAG,CAAC,CAAC,CAAA;QACzD,IAAI,KAAK;YAAE,GAAG,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;KACpC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC"}