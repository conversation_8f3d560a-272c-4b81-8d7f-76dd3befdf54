{"name": "@nomiclabs/hardhat-ethers", "version": "2.2.3", "description": "Hardhat plugin for ethers", "homepage": "https://github.com/nomiclabs/hardhat/tree/main/packages/hardhat-ethers", "repository": "github:nomiclabs/hardhat", "author": "Nomic Labs LLC", "license": "MIT", "main": "internal/index.js", "types": "internal/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "ethers.js"], "scripts": {"lint": "yarn prettier --check && yarn eslint", "lint:fix": "yarn prettier --write && yarn eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "prepublishOnly": "yarn build", "clean": "rimraf dist internal types *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo"}, "files": ["dist/src/", "src/", "internal/", "types/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "devDependencies": {"@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.3", "@types/mocha": ">=9.1.0", "@types/node": "^14.0.0", "@typescript-eslint/eslint-plugin": "5.53.0", "@typescript-eslint/parser": "5.53.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^7.29.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.1", "eslint-plugin-no-only-tests": "3.0.0", "eslint-plugin-prettier": "3.4.0", "ethers": "^5.0.0", "hardhat": "^2.0.0", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^10.8.0", "typescript": "~4.7.4"}, "peerDependencies": {"ethers": "^5.0.0", "hardhat": "^2.0.0"}}