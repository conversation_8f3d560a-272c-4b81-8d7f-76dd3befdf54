{"name": "@nomiclabs/hardhat-etherscan", "version": "3.1.7", "description": "Hardhat plugin for verifying contracts on etherscan", "repository": "github:nomiclabs/hardhat", "homepage": "https://github.com/nomiclabs/hardhat/tree/main/packages/hardhat-etherscan", "author": "Nomic Labs LLC", "contributors": ["Nomic Labs LLC", "<PERSON> <<EMAIL>>"], "license": "MIT", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "etherscan"], "scripts": {"lint": "yarn prettier --check && yarn eslint", "lint:fix": "yarn prettier --write && yarn eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "prepublishOnly": "yarn build", "clean": "<PERSON><PERSON><PERSON> dist"}, "files": ["dist/src/", "src/", "LICENSE", "README.md"], "dependencies": {"@ethersproject/abi": "^5.1.2", "@ethersproject/address": "^5.0.2", "cbor": "^8.1.0", "chalk": "^2.4.2", "debug": "^4.1.1", "fs-extra": "^7.0.1", "lodash": "^4.17.11", "semver": "^6.3.0", "table": "^6.8.0", "undici": "^5.14.0"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.0", "@types/chai": "^4.2.0", "@types/fs-extra": "^5.1.0", "@types/mocha": ">=9.1.0", "@types/node": "^14.0.0", "@types/semver": "^6.0.2", "@typescript-eslint/eslint-plugin": "5.53.0", "@typescript-eslint/parser": "5.53.0", "chai": "^4.2.0", "eslint": "^7.29.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.1", "eslint-plugin-no-only-tests": "3.0.0", "eslint-plugin-prettier": "3.4.0", "ethers": "^5.0.8", "hardhat": "^2.0.4", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^10.8.0", "typescript": "~4.7.4"}, "peerDependencies": {"hardhat": "^2.0.4"}}