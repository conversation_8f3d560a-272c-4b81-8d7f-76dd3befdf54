"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.retrieveContractBytecode = exports.getEtherscanEndpoints = void 0;
const plugins_1 = require("hardhat/plugins");
const constants_1 = require("../constants");
const errors_1 = require("../errors");
async function getEtherscanEndpoints(provider, networkName, chainConfig, customChains) {
    const chainIdsToNames = new Map(entries(chainConfig).map(([chainName, config]) => [
        config.chainId,
        chainName,
    ]));
    const chainID = parseInt(await provider.send("eth_chainId"), 16);
    const networkInCustomChains = [...customChains]
        .reverse() // the last entry wins
        .find((customChain) => customChain.chainId === chainID);
    // if there is a custom chain with the given chain id, that one is preferred
    // over the built-in ones
    if (networkInCustomChains !== undefined) {
        return networkInCustomChains;
    }
    const network = networkInCustomChains ?? chainIdsToNames.get(chainID);
    if (network === undefined) {
        (0, errors_1.throwUnsupportedNetwork)(networkName, chainID);
    }
    const chainConfigEntry = chainConfig[network];
    return { network, urls: chainConfigEntry.urls };
}
exports.getEtherscanEndpoints = getEtherscanEndpoints;
async function retrieveContractBytecode(address, provider, networkName) {
    const bytecodeString = (await provider.send("eth_getCode", [
        address,
        "latest",
    ]));
    const deployedBytecode = bytecodeString.startsWith("0x")
        ? bytecodeString.slice(2)
        : bytecodeString;
    if (deployedBytecode.length === 0) {
        throw new plugins_1.NomicLabsHardhatPluginError(constants_1.pluginName, `The address ${address} has no bytecode. Is the contract deployed to this network?
The selected network is ${networkName}.`);
    }
    return deployedBytecode;
}
exports.retrieveContractBytecode = retrieveContractBytecode;
function entries(o) {
    return Object.entries(o);
}
//# sourceMappingURL=prober.js.map