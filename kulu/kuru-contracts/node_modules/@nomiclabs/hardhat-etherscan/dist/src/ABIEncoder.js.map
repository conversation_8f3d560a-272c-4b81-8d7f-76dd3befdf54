{"version": 3, "file": "ABIEncoder.js", "sourceRoot": "", "sources": ["../../src/ABIEncoder.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA8D;AAE9D,yCAIoB;AACpB,2CAAyC;AAElC,KAAK,UAAU,eAAe,CACnC,GAAQ,EACR,UAAkB,EAClB,YAAoB,EACpB,oBAA2B;IAE3B,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,oBAAoB,GAAC,CAAC;IAEzD,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,sBAAsB,CAAC;IAC3B,IAAI;QACF,sBAAsB,GAAG,iBAAiB;aACvC,YAAY,CAAC,oBAAoB,CAAC;aAClC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACtB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,IAAA,mCAAwB,EAAC,KAAK,CAAC,EAAE;YACnC,4EAA4E;YAC5E,MAAM,OAAO,GAAG,uBAAuB,UAAU,IAAI,YAAY,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK;MAC1F,KAAK,CAAC,KAAK,CAAC,MAAM,mCAAmC,CAAC;YACtD,MAAM,IAAI,qCAA2B,CAAC,sBAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACnE;QACD,IAAI,IAAA,iCAAsB,EAAC,KAAK,CAAC,EAAE;YACjC,MAAM,OAAO,GAAG,SAAS,KAAK,CAAC,KAAK,wCAAwC,KAAK,CAAC,QAAQ;wBACxE,KAAK,CAAC,MAAM,EAAE,CAAC;YACjC,MAAM,IAAI,qCAA2B,CAAC,sBAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACnE;QACD,IAAI,IAAA,qCAA0B,EAAC,KAAK,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,SAAS,KAAK,CAAC,KAAK;;wBAElB,KAAK,CAAC,KAAK,aAAa,KAAK,CAAC,SAAS,EAAE,CAAC;YAC5D,MAAM,IAAI,qCAA2B,CAAC,sBAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACnE;QACD,yBAAyB;QACzB,MAAM,KAAK,CAAC;KACb;IAED,OAAO,sBAAsB,CAAC;AAChC,CAAC;AArCD,0CAqCC"}