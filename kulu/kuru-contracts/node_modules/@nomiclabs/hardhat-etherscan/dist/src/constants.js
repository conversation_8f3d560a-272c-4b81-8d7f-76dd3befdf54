"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TASK_VERIFY_GET_LIBRARIES = exports.TASK_VERIFY_VERIFY = exports.TASK_VERIFY_VERIFY_MINIMUM_BUILD = exports.TASK_VERIFY_GET_CONTRACT_INFORMATION = exports.TASK_VERIFY_GET_ETHERSCAN_ENDPOINT = exports.TASK_VERIFY_GET_COMPILER_VERSIONS = exports.TASK_VERIFY_GET_CONSTRUCTOR_ARGUMENTS = exports.TASK_VERIFY_GET_MINIMUM_BUILD = exports.TASK_VERIFY = exports.pluginName = void 0;
exports.pluginName = "@nomiclabs/hardhat-etherscan";
exports.TASK_VERIFY = "verify";
exports.TASK_VERIFY_GET_MINIMUM_BUILD = "verify:get-minimum-build";
exports.TASK_VERIFY_GET_CONSTRUCTOR_ARGUMENTS = "verify:get-constructor-arguments";
exports.TASK_VERIFY_GET_COMPILER_VERSIONS = "verify:get-compiler-versions";
exports.TASK_VERIFY_GET_ETHERSCAN_ENDPOINT = "verify:get-etherscan-endpoint";
exports.TASK_VERIFY_GET_CONTRACT_INFORMATION = "verify:get-contract-information";
exports.TASK_VERIFY_VERIFY_MINIMUM_BUILD = "verify:verify-minimum-build";
exports.TASK_VERIFY_VERIFY = "verify:verify";
exports.TASK_VERIFY_GET_LIBRARIES = "verify:get-libraries";
//# sourceMappingURL=constants.js.map