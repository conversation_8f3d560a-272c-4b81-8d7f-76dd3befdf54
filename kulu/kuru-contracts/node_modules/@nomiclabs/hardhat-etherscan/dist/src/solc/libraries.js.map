{"version": 3, "file": "libraries.js", "sourceRoot": "", "sources": ["../../../src/solc/libraries.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA8D;AAE9D,4CAA0C;AAoBnC,KAAK,UAAU,eAAe,CACnC,mBAAwC,EACxC,SAAoB;IAEpB,MAAM,YAAY,GAAG,eAAe,CAClC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CACzD,CAAC;IACF,MAAM,mBAAmB,GAAG,eAAe,CACzC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CACjE,CAAC;IACF,MAAM,qBAAqB,GAAiB,YAAY,CAAC,MAAM,CAC7D,CAAC,GAAG,EAAE,EAAE,CACN,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;QAC1C,OAAO,CACL,aAAa,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU;YAC3C,aAAa,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,CACtC,CAAC;IACJ,CAAC,CAAC,CACL,CAAC;IAEF,oDAAoD;IACpD,MAAM,mBAAmB,GAAG,MAAM,kBAAkB,CAClD,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,SAAS,EACT,mBAAmB,CAAC,YAAY,CACjC,CAAC;IAEF,sBAAsB;IACtB,MAAM,kBAAkB,GAAG,cAAc,CACvC,mBAAmB,EACnB,mBAAmB,CAAC,YAAY,CACjC,CAAC;IAEF,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAC5D,IAAI,eAAe,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE;QAChD,kDAAkD;QAClD,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAC1C,CAAC,GAAG,EAAE,EAAE,CACN,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAClC,OAAO,CACL,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU;gBACvC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,CAClC,CAAC;QACJ,CAAC,CAAC,CACL,CAAC;QACF,MAAM,mBAAmB,GAAG,gBAAgB;aACzC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,IAAI,OAAO,EAAE,CAAC;aAC5D,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;aACtB,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,IAAI,OAAO,GAAG,gBAAgB,mBAAmB,CAAC,UAAU,IAAI,mBAAmB,CAAC,YAAY;;EAElG,mBAAmB,EAAE,CAAC;QACpB,+GAA+G;QAC/G,IAAI,gBAAgB,CAAC,MAAM,KAAK,qBAAqB,CAAC,MAAM,EAAE;YAC5D,OAAO,IAAI;;+IAE8H,CAAC;SAC3I;aAAM;YACL,OAAO,IAAI;;mGAEkF,CAAC;SAC/F;QACD,MAAM,IAAI,qCAA2B,CAAC,sBAAU,EAAE,OAAO,CAAC,CAAC;KAC5D;IACD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,CAAC;AACrE,CAAC;AAnED,0CAmEC;AAED,SAAS,cAAc,CACrB,mBAAkC,EAClC,iBAAgC;IAEhC,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;QACzE,KAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7D,IACE,UAAU,IAAI,iBAAiB;gBAC/B,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,EACxC;gBACA,MAAM,eAAe,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC/D,wDAAwD;gBACxD,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,eAAe,EAAE;oBAChD,SAAS,CAAC,IAAI,CAAC;wBACb,OAAO,EAAE,GAAG,UAAU,IAAI,OAAO,EAAE;wBACnC,eAAe;wBACf,YAAY,EAAE,UAAU;qBACzB,CAAC,CAAC;iBACJ;aACF;SACF;KACF;IAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,oBAAoB,GAAG,SAAS;aACnC,GAAG,CACF,CAAC,QAAQ,EAAE,EAAE,CACX,OAAO,QAAQ,CAAC,OAAO;qBACZ,QAAQ,CAAC,YAAY;wBAClB,QAAQ,CAAC,eAAe,EAAE,CAC3C;aACA,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,MAAM,IAAI,qCAA2B,CACnC,sBAAU,EACV;EACJ,oBAAoB;;yHAEmG,CACpH,CAAC;KACH;IAED,MAAM,eAAe,GAAkB,EAAE,CAAC;IAC1C,YAAY,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;IACnD,YAAY,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACjD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,YAAY,CACnB,eAA8B,EAC9B,YAA2B;IAE3B,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;QAClE,IAAI,eAAe,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YAC7C,eAAe,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAClC;QACD,KAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7D,eAAe,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC;SACnD;KACF;AACH,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,YAA0B,EAC1B,mBAAiC,EACjC,qBAAmC,EACnC,SAAoB,EACpB,YAAoB;IAEpB,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,wBAAwB,GAAC,CAAC;IAE7D,MAAM,WAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC3C,MAAM,mBAAmB,GAAkB,EAAE,CAAC;IAC9C,KAAK,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,IAAI,MAAM,CAAC,OAAO,CACpE,SAAS,CACV,EAAE;QACD,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE;YACpC,MAAM,IAAI,qCAA2B,CACnC,sBAAU,EACV,oCAAoC,YAAY,qBAAqB,iBAAiB,wCAAwC,oBAAoB,EAAE,CACrJ,CAAC;SACH;QAED,MAAM,aAAa,GAAG,aAAa,CACjC,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,iBAAiB,EACjB,YAAY,CACb,CAAC;QACF,MAAM,gBAAgB,GAAG,GAAG,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;QAEhF,wDAAwD;QACxD,wDAAwD;QACxD,wEAAwE;QACxE,IAAI,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;YACrC,MAAM,IAAI,qCAA2B,CACnC,sBAAU,EACV,qBAAqB,aAAa,CAAC,OAAO,QAAQ,gBAAgB;2EACC,CACpE,CAAC;SACH;QAED,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAClC,IAAI,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YAC/D,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SACpD;QACD,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;YAClE,oBAAoB,CAAC;KACxB;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,SAAS,aAAa,CACpB,YAA0B,EAC1B,mBAAiC,EACjC,qBAAmC,EACnC,iBAAyB,EACzB,YAAoB;IAEpB,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;QACpD,OAAO,CACL,GAAG,CAAC,OAAO,KAAK,iBAAiB;YACjC,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,iBAAiB,CACzD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAM,0BAA0B,GAAG,qBAAqB;iBACrD,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;iBAChD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;iBACtB,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,MAAM,wBAAwB,GAAG,mBAAmB;iBACjD,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;iBAChD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC;iBACjC,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,eAAe,IAAI;EACvB,0BAA0B;EAC1B,wBAAwB,EAAE,CAAC;YACvB,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,eAAe,IAAI;8GACmF,CAAC;aACxG;SACF;aAAM;YACL,eAAe,IAAI,mDAAmD,CAAC;SACxE;QACD,MAAM,IAAI,qCAA2B,CACnC,sBAAU,EACV,uCAAuC,iBAAiB,+EAA+E,YAAY;EACvJ,eAAe,EAAE,CACd,CAAC;KACH;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,MAAM,qBAAqB,GAAG,iBAAiB;aAC5C,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,IAAI,OAAO,EAAE,CAAC;aAC5D,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;aACtB,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,MAAM,IAAI,qCAA2B,CACnC,sBAAU,EACV,oBAAoB,iBAAiB,kCAAkC,YAAY;;EAEvF,qBAAqB;;4GAEqF,CACvG,CAAC;KACH;IAED,MAAM,CAAC,aAAa,CAAC,GAAG,iBAAiB,CAAC;IAC1C,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,eAAe,CAAC,SAA4B;IACnD,MAAM,YAAY,GAAiB,EAAE,CAAC;IACtC,KAAK,MAAM,CAAC,UAAU,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACrE,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;YAClD,YAAY,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;SAC5C;KACF;IAED,OAAO,YAAY,CAAC;AACtB,CAAC"}