{"version": 3, "file": "metadata.js", "sourceRoot": "", "sources": ["../../../src/solc/metadata.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,gDAAwB;AACxB,+BAAuC;AAO1B,QAAA,oBAAoB,GAAG,CAAC,CAAC;AACzB,QAAA,6CAA6C,GAAG,eAAe,CAAC;AAChE,QAAA,6BAA6B,GAAG,QAAQ,CAAC;AAEtD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,oCAAoC,CAAC,CAAC;AAExD,SAAgB,gBAAgB,CAAC,QAAgB;IAC/C,8CAA8C;IAC9C,0FAA0F;IAC1F,wFAAwF;IACxF,qFAAqF;IACrF,qHAAqH;IACrH,6GAA6G;IAC7G,iFAAiF;IACjF,4GAA4G;IAC5G,kDAAkD;IAClD,4GAA4G;IAC5G,IAAI,YAAY,CAAC;IACjB,IAAI,0BAA0B,CAAC;IAC/B,IAAI;QACF,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC9C,GAAG,CAAC,qBAAqB,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3D,0BAA0B,GAAG,QAAQ,CAAC,0BAA0B,CAAC;QACjE,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;KACtC;IAAC,MAAM;QACN,qHAAqH;QACrH,oGAAoG;QACpG,yEAAyE;QACzE,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAClC,OAAO;YACL,0BAA0B,EAAE,CAAC;YAC7B,WAAW,EAAE,qCAA6B;SAC3C,CAAC;KACH;IAED,IAAI,YAAY,YAAY,MAAM,EAAE;QAClC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC;YAC3C,MAAM,WAAW,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;YACjD,GAAG,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;YACzD,OAAO,EAAE,0BAA0B,EAAE,WAAW,EAAE,CAAC;SACpD;QACD,GAAG,CACD,iCAAiC,YAAY,CAAC,MAAM,6BAA6B,CAClF,CAAC;KACH;IAED,sFAAsF;IACtF,GAAG,CAAC,gDAAgD,CAAC,CAAC;IACtD,OAAO;QACL,0BAA0B;QAC1B,WAAW,EAAE,qDAA6C;KAC3D,CAAC;AACJ,CAAC;AA/CD,4CA+CC;AAED,SAAgB,kBAAkB,CAAC,QAAgB;IACjD,MAAM,qBAAqB,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAC;IACrE,yDAAyD;IACzD,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CACpC,CAAC,qBAAqB,EACtB,CAAC,4BAAoB,CACtB,CAAC;IAEF,GAAG,CAAC,wBAAwB,qBAAqB,EAAE,CAAC,CAAC;IAErD,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,GAAG,CACD,QACE,iBAAiB,CAAC,MACpB,uBAAuB,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAC3D,CAAC;IACF,MAAM,OAAO,GAAG,IAAA,sBAAe,EAAC,eAAe,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,OAAO;QACL,OAAO;QACP,0BAA0B,EAAE,qBAAqB;KAClD,CAAC;AACJ,CAAC;AArBD,gDAqBC;AAED,SAAgB,4BAA4B,CAAC,QAAgB;IAC3D,OAAO,CACL,QAAQ,CAAC,KAAK,CAAC,CAAC,4BAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,4BAAoB,CAC7E,CAAC;AACJ,CAAC;AAJD,oEAIC;AAED;;;;;;GAMG;AACH,SAAgB,8BAA8B,CAAC,QAAgB;IAC7D,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC7B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,2FAA2F;IAC3F,0EAA0E;IAC1E,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CACrC,QAAQ,CAAC,KAAK,CAAC,CAAC,4BAAoB,GAAG,CAAC,CAAC,EACzC,KAAK,CACN,CAAC;IAEF,8FAA8F;IAC9F,0CAA0C;IAC1C,IAAI,mBAAmB,CAAC,MAAM,KAAK,4BAAoB,EAAE;QACvD,OAAO,QAAQ,CAAC,MAAM,CAAC;KACxB;IAED,MAAM,4BAA4B,GAChC,4BAA4B,CAAC,mBAAmB,CAAC,CAAC;IAEpD,OAAO,QAAQ,CAAC,MAAM,GAAG,4BAA4B,GAAG,CAAC,CAAC;AAC5D,CAAC;AAtBD,wEAsBC"}