{"version": 3, "file": "bytecode.js", "sourceRoot": "", "sources": ["../../../src/solc/bytecode.ts"], "names": [], "mappings": ";;;AAOA,iEAAuE;AAEvE,yCAA8E;AAwC9E,kHAAkH;AAClH,0HAA0H;AAC1H,iHAAiH;AACjH,yGAAyG;AACzG,iHAAiH;AACjH,kHAAkH;AAClH,sLAAsL;AACtL,MAAM,cAAc,GAClB,4EAA4E,CAAC;AAC/E,MAAM,iBAAiB,GACrB,4EAA4E,CAAC;AAE/E,MAAa,QAAQ;IAQnB,YAAY,QAAgB;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,MAAM,EAAE,WAAW,EAAE,0BAA0B,EAAE,GAAG,IAAA,2BAAgB,EAClE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAC7B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG;YACxB,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,0BAA0B,GAAG,CAAC;SACzD,CAAC;QACF,IAAI,CAAC,gBAAgB,GAAG;YACtB,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;YACrC,MAAM,EAAE,0BAA0B,GAAG,CAAC;SACvC,CAAC;QAEF,kGAAkG;QAClG,wLAAwL;QACxL,sSAAsS;QACtS,iGAAiG;QACjG,kGAAkG;QAClG,8BAA8B;QAC9B,+FAA+F;QAC/F,+FAA+F;QAC/F,uBAAuB;QACvB,iFAAiF;QACjF,uGAAuG;QACvG,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAEM,sBAAsB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,oBAAoB;QACzB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,CAAC;CACF;AArDD,4BAqDC;AAEM,KAAK,UAAU,sBAAsB,CAC1C,SAAoB,EACpB,wBAAkC,EAClC,gBAA0B;IAE1B,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,yBAAyB,EAAE,CAAC;IAE5D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,SAAS;SACV;QAED,IACE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC;YACzD,2HAA2H;YAC3H,CAAC,gBAAgB,CAAC,aAAa,EAAE,EACjC;YACA,SAAS;SACV;QAED,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,IAAA,wCAAuB,EAAC,MAAM,CAAC,CAAC;QAErE,MAAM,mBAAmB,GAAG,MAAM,kCAAkC,CAClE,UAAU,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,CACjB,CAAC;QACF,IAAI,mBAAmB,KAAK,IAAI,EAAE;YAChC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC3C;KACF;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AArCD,wDAqCC;AAEM,KAAK,UAAU,kCAAkC,CACtD,UAAsB,EACtB,YAA0B,EAC1B,SAAoB,EACpB,gBAA0B;IAE1B,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC;IACtE,0DAA0D;IAC1D,MAAM,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC;IAElE,+FAA+F;IAC/F,IAAI,gBAAgB,CAAC,aAAa,EAAE,EAAE;QACpC,sBAAsB,CAAC,MAAM,GAAG,sBAAsB,CAAC,MAAM;aAC1D,KAAK,CAAC,cAAc,CAAC;aACrB,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5B;IAED,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAC5C,gBAAgB,EAChB,sBAAsB,CACvB,CAAC;IAEF,IAAI,gBAAgB,KAAK,IAAI,EAAE;QAC7B,OAAO;YACL,GAAG,gBAAgB;YACnB,aAAa,EAAE,SAAS,CAAC,KAAK;YAC9B,cAAc,EAAE,SAAS,CAAC,MAAM;YAChC,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,UAAU;YACV,YAAY;YACZ,QAAQ;SACT,CAAC;KACH;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAnCD,gFAmCC;AAEM,KAAK,UAAU,eAAe,CACnC,gBAA0B,EAC1B,sBAA8C;IAE9C,sFAAsF;IACtF,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;IAC1E,MAAM,sCAAsC,GAAG,IAAA,yCAA8B,EAC3E,sBAAsB,CAAC,MAAM,CAC9B,CAAC;IAEF,IACE,yBAAyB,CAAC,MAAM;QAC9B,sCAAsC;QACxC,yFAAyF;QACzF,CAAC,gBAAgB,CAAC,aAAa,EAAE,EACjC;QACA,OAAO,IAAI,CAAC;KACb;IAED,0DAA0D;IAC1D,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,GACzD,MAAM,iBAAiB,CAAC,yBAAyB,EAAE,sBAAsB,CAAC,CAAC;IAE7E,gGAAgG;IAChG,4CAA4C;IAC5C,MAAM,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAAG,MAAM,iBAAiB,CACvE,sBAAsB,CAAC,MAAM,EAC7B,sBAAsB,CACvB,CAAC;IAEF,IACE,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,yBAAyB,CAAC,MAAM,CAAC;QAC7D,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,yBAAyB,CAAC,MAAM,CAAC,EAC5D;QACA,uBAAuB;QACvB,OAAO;YACL,eAAe;YACf,YAAY;YACZ,kBAAkB;SACnB,CAAC;KACH;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA3CD,0CA2CC;AAEM,KAAK,UAAU,iBAAiB,CACrC,QAAgB,EAChB,OAA+B;IAE/B,MAAM,qBAAqB,GAA0B,EAAE,CAAC;IACxD,MAAM,YAAY,GAAkB,EAAE,CAAC;IACvC,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAClD,OAAO,CAAC,cAAc,CACvB,EAAE;QACD,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACrE,8BAA8B;YAC9B,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/B,SAAS;aACV;YAED,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;gBAC1C,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;aAC/B;YACD,+CAA+C;YAC/C,YAAY,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,KAAK,QAAQ,CAAC,KAAK,CACzD,KAAK,GAAG,CAAC,EACT,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CACrB,EAAE,CAAC;YACJ,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC5C;KACF;IAED,MAAM,eAAe,GAAoB,EAAE,CAAC;IAC5C,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS;QACzC,OAAO,CAAC,mBAAmB,KAAK,IAAI,EACpC;QACA,KAAK,MAAM,CAAC,GAAG,EAAE,mBAAmB,CAAC,IAAI,MAAM,CAAC,OAAO,CACrD,OAAO,CAAC,mBAAmB,CAC5B,EAAE;YACD,8BAA8B;YAC9B,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpC,SAAS;aACV;YAED,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACjD,eAAe,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACvE,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACjD;KACF;IAED,4FAA4F;IAC5F,6FAA6F;IAC7F,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,MAAM,eAAe,GAAG,IAAI,CAAC;IAC7B,MAAM,eAAe,GAAG,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IACtE,IACE,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC;QAC1C,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,EACpC;QACA,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;KACjE;IAED,MAAM,eAAe,GAAG,aAAa,CAAC,qBAAqB,CAAC,CAAC;IAC7D,MAAM,kBAAkB,GAAG,aAAa,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;IAEpE,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAE,CAAC;AAC/D,CAAC;AA/DD,8CA+DC;AAED,SAAS,aAAa,CAAC,MAA6B;IAClD,OAAQ,EAAsB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,aAAa,CACpB,IAAY,EACZ,MAAgD;IAEhD,KAAK,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,MAAM,EAAE;QACtC,IAAI,GAAG;YACL,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;YACxB,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SACjC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACZ;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}