export const pluginName = "@nomiclabs/hardhat-etherscan";
export const TASK_VERIFY = "verify";
export const TASK_VERIFY_GET_MINIMUM_BUILD = "verify:get-minimum-build";
export const TASK_VERIFY_GET_CONSTRUCTOR_ARGUMENTS =
  "verify:get-constructor-arguments";
export const TASK_VERIFY_GET_COMPILER_VERSIONS = "verify:get-compiler-versions";
export const TASK_VERIFY_GET_ETHERSCAN_ENDPOINT =
  "verify:get-etherscan-endpoint";
export const TASK_VERIFY_GET_CONTRACT_INFORMATION =
  "verify:get-contract-information";
export const TASK_VERIFY_VERIFY_MINIMUM_BUILD = "verify:verify-minimum-build";
export const TASK_VERIFY_VERIFY = "verify:verify";
export const TASK_VERIFY_GET_LIBRARIES = "verify:get-libraries";
