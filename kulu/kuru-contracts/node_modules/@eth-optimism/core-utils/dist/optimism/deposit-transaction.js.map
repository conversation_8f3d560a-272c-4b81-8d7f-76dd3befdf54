{"version": 3, "file": "deposit-transaction.js", "sourceRoot": "", "sources": ["../../src/optimism/deposit-transaction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAAmD;AAEnD,wDAAkE;AAClE,wDAAoD;AACpD,wDAA+C;AAC/C,wDAAyC;AACzC,gDAO6B;AAE7B,MAAM,aAAa,GAAG,CAAC,KAAc,EAAc,EAAE;IACnD,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAA;AACzD,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,KAAmB,EAAE,IAAY,EAAc,EAAE;IACrE,MAAM,MAAM,GAAG,IAAA,kBAAU,EAAC,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAA;IAC9D,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAA;KAC9C;IACD,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CAAC,KAAa,EAAW,EAAE;IAC/C,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,KAAK,CAAA;KACb;IACD,IAAI,KAAK,KAAK,MAAM,EAAE;QACpB,OAAO,IAAI,CAAA;KACZ;IACD,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAA;AAC3D,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,KAAa,EAAa,EAAE;IAChD,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,gBAAI,CAAA;KACZ;IACD,OAAO,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC9B,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;IAC9C,IAAI,KAAK,KAAK,IAAI,EAAE;QAElB,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAA;AAC1B,CAAC,CAAA;AAED,IAAY,gBAGX;AAHD,WAAY,gBAAgB;IAC1B,qEAAe,CAAA;IACf,yEAAiB,CAAA;AACnB,CAAC,EAHW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAG3B;AAwBD,MAAa,SAAS;IAiBpB,YAAY,OAA+B,EAAE;QAhBtC,SAAI,GAAG,IAAI,CAAA;QACX,YAAO,GAAG,IAAI,CAAA;QAgBnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;QACtB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAG,CAAA;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAM,CAAA;QACxB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAI,CAAA;QACpB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAA;QAC5D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;IAC3C,CAAC;IAED,IAAI;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAC7B,OAAO,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAA;IAC3B,CAAC;IAED,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,MAAc,CAAA;YAClB,QAAQ,IAAI,CAAC,MAAM,EAAE;gBACnB,KAAK,gBAAgB,CAAC,WAAW;oBAC/B,MAAM,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAA;oBACpD,MAAK;gBACP,KAAK,gBAAgB,CAAC,aAAa;oBACjC,MAAM,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAA;oBAC1D,MAAK;gBACP;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;aACpD;YAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;aAC1D;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;YACpC,MAAM,KAAK,GAAG,IAAA,iBAAS,EAAC,CAAC,WAAW,EAAE,IAAA,eAAO,EAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;YAC3D,MAAM,aAAa,GAAG,IAAA,qBAAS,EAAC,KAAK,CAAC,CAAA;YACtC,MAAM,MAAM,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAA;YACxD,MAAM,WAAW,GAAG,IAAA,iBAAS,EAAC,CAAC,IAAA,eAAO,EAAC,MAAM,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAA;YACnE,IAAI,CAAC,WAAW,GAAG,IAAA,qBAAS,EAAC,WAAW,CAAC,CAAA;SAC1C;QACD,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED,MAAM;QACJ,MAAM,MAAM,GAAQ;YAClB,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI;YACzB,IAAA,oBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;YAC7B,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,IAAA,oBAAU,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;YAC5C,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC;YACtC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC;YAClC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,IAAI,IAAI,IAAI;SAClB,CAAA;QAED,OAAO,IAAA,iBAAS,EAAC;YACf,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;YACvC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;SACnB,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,CAAC,GAAc,EAAE,QAA4B,EAAE;QACnD,MAAM,OAAO,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAA;QAC7B,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;SAC9C;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACzB,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACzC,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACzC,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACxD,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAE1B,IAAI,aAAa,IAAI,KAAK,EAAE;YAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAA;SACrC;QACD,IAAI,QAAQ,IAAI,KAAK,EAAE;YACrB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;SAC3B;QACD,IAAI,UAAU,IAAI,KAAK,EAAE;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;SAC/B;QACD,IAAI,gBAAgB,IAAI,KAAK,EAAE;YAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,CAAA;SAC3C;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,GAAc,EAAE,KAA0B;QACtD,OAAO,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IACtC,CAAC;IAED,aAAa,CAAC,OAAwB,EAAE,KAAa;QACnD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;SACxC;QACD,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,eAAe,KAAK,iBAAiB,CAAC,CAAA;SACvD;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAChC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAwB,EAAE,KAAa;QAC1D,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IACnD,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,IAAI,KAAK,CAAC,KAAK,KAAK,sBAAsB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;SACxD;QACD,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;SACjC;QACD,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAA;QAC3B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;SAClC;QACD,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,KAAK,WAAW,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;SACxE;QACD,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,WAAW,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;SAC1C;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAA;QACxC,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,4BAA4B,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;SACjE;QAED,IAAI,MAAM,GAAG,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAA;QACzE,MAAM,IAAI,EAAE,CAAA;QACZ,IAAI,CAAC,KAAK,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAA;QAC1E,MAAM,IAAI,EAAE,CAAA;QACZ,IAAI,CAAC,GAAG,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;QACvE,MAAM,IAAI,CAAC,CAAA;QACX,MAAM,UAAU,GAAG,qBAAS,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAC3D,MAAM,IAAI,CAAC,CAAA;QACX,IAAI,CAAC,EAAE,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAA;QACpD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,MAAM,CAAA;QACzC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAA;QAC7D,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,WAAW,CAAA;QAC1C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;QAC9B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAY;QAC7B,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IACxC,CAAC;CACF;AAxLD,8BAwLC"}