{"version": 3, "file": "op-provider.js", "sourceRoot": "", "sources": ["../../src/optimism/op-provider.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAAiC;AAEjC,wDAAoD;AACpD,0DAAoD;AACpD,4CAA8D;AAE9D,MAAM,SAAS,GAAG,CAAC,OAGlB,EAAO,EAAE;IACR,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACnD,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAA;QAC/B,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAA;QAC/B,MAAM,KAAK,CAAA;KACZ;IACD,OAAO,OAAO,CAAC,MAAM,CAAA;AACvB,CAAC,CAAA;AAyBD,MAAa,cAAe,SAAQ,gBAAY;IAI9C,YAAY,GAA6B;QACvC,KAAK,EAAE,CAAA;QAHD,YAAO,GAAW,CAAC,CAAA;QAKzB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,CAAA;SAC1B;aAAM;YACL,IAAI,CAAC,UAAU,GAAG,GAAG,CAAA;SACtB;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAA;QAEzD,OAAO;YACL,SAAS,EAAE;gBACT,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;gBAC5B,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBAChD,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,UAAU;gBACxC,SAAS,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;aACvD;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;gBACzB,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC7C,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;gBACrC,SAAS,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;aACpD;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;gBAC3B,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC/C,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU;gBACvC,SAAS,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;gBACrD,QAAQ,EAAE;oBACR,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;oBACpC,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;iBACzD;gBACD,eAAe,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;aACjE;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;gBACzB,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC7C,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;gBACrC,SAAS,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACnD,QAAQ,EAAE;oBACR,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI;oBAClC,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;iBACvD;gBACD,eAAe,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;aAC/D;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI;gBAC9B,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAClD,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,UAAU;gBAC1C,SAAS,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBACxD,QAAQ,EAAE;oBACR,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI;oBACvC,MAAM,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;iBAC5D;gBACD,eAAe,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC;aACpE;SACF,CAAA;IACH,CAAC;IAGD,KAAK,CAAC,YAAY;QAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;QAC3D,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,CAAC,MAAc,EAAE,MAAkB;QACrC,MAAM,OAAO,GAAG;YACd,MAAM;YACN,MAAM;YACN,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE;YAClB,OAAO,EAAE,KAAK;SACf,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,IAAA,qBAAQ,EAAC,OAAO,CAAC;YAC1B,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,IAAA,eAAS,EACtB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACvB,SAAS,CACV,CAAC,IAAI,CACJ,CAAC,GAAG,EAAE,EAAE;YACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,EAAE,UAAU;gBAClB,OAAO;gBACP,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,IAAI;aACf,CAAC,CAAA;YAEF,OAAO,GAAG,CAAA;QACZ,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,EAAE,UAAU;gBAClB,KAAK;gBACL,OAAO;gBACP,QAAQ,EAAE,IAAI;aACf,CAAC,CAAA;YAEF,MAAM,KAAK,CAAA;QACb,CAAC,CACF,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AAnHD,wCAmHC"}