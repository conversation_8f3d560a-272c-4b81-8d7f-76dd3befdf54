{"version": 3, "file": "fees.js", "sourceRoot": "", "sources": ["../../src/optimism/fees.ts"], "names": [], "mappings": ";;;AAIA,wDAAoD;AAEpD,sCAAoC;AAEvB,QAAA,aAAa,GAAG,CAAC,CAAA;AACjB,QAAA,uBAAuB,GAAG,EAAE,CAAA;AACzC,MAAM,KAAK,GAAG,qBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AAEzB,MAAM,aAAa,GAAG,CAC3B,KAAyB,EACzB,QAA4B,EACjB,EAAE;IACb,KAAK,GAAG,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC7B,QAAQ,GAAG,qBAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAEnC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACnC,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;AAC3B,CAAC,CAAA;AATY,QAAA,aAAa,iBASzB;AAGM,MAAM,kBAAkB,GAAG,CAChC,IAAqB,EACrB,QAA4B,EACjB,EAAE;IACb,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,qBAAa,EAAC,IAAI,CAAC,CAAA;IAC1C,MAAM,UAAU,GAAG,MAAM,GAAG,qBAAa,CAAA;IAEzC,MAAM,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,+BAAuB,CAAA;IACtD,OAAO,qBAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;AAC/D,CAAC,CAAA;AATY,QAAA,kBAAkB,sBAS9B;AAEM,MAAM,cAAc,GAAG,CAC5B,IAAqB,EACrB,QAA4B,EAC5B,UAA8B,EAC9B,MAA0B,EAC1B,QAA4B,EACjB,EAAE;IACb,MAAM,SAAS,GAAG,IAAA,0BAAkB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IACpD,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;IACvC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAChC,MAAM,MAAM,GAAG,IAAA,qBAAa,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC9C,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAZY,QAAA,cAAc,kBAY1B;AAGM,MAAM,aAAa,GAAG,CAAC,IAAqB,EAAiB,EAAE;IACpE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAA;KAC1C;IACD,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,IAAI,IAAI,GAAG,CAAC,CAAA;IACZ,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,KAAK,EAAE,CAAA;SACR;aAAM;YACL,IAAI,EAAE,CAAA;SACP;KACF;IACD,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACtB,CAAC,CAAA;AAdY,QAAA,aAAa,iBAczB;AASM,MAAM,YAAY,GAAG,CAAC,IAAqB,EAAa,EAAE;IAC/D,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAA,qBAAa,EAAC,IAAI,CAAC,CAAA;IACzC,MAAM,QAAQ,GAAG,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,qBAAa,CAAC,CAAA;IACzD,MAAM,WAAW,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,+BAAuB,CAAC,CAAA;IACrE,OAAO,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;AAClC,CAAC,CAAA;AALY,QAAA,YAAY,gBAKxB"}