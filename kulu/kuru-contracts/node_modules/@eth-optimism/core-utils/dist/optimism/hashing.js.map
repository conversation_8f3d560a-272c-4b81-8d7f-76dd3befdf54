{"version": 3, "file": "hashing.js", "sourceRoot": "", "sources": ["../../src/optimism/hashing.ts"], "names": [], "mappings": ";;;AACA,wDAAoD;AACpD,4CAAoD;AAEpD,yCAImB;AAkDZ,MAAM,sBAAsB,GAAG,CACpC,KAAgB,EAChB,MAAc,EACd,MAAc,EACd,KAAgB,EAChB,QAAmB,EACnB,IAAY,EACZ,EAAE;IACF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,+BAAoB,EAAC,KAAK,CAAC,CAAA;IAC/C,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACjB,OAAO,IAAA,gCAAwB,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;KAC7D;SAAM,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACxB,OAAO,IAAA,gCAAwB,EAC7B,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,IAAI,CACL,CAAA;KACF;IACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;AAC1D,CAAC,CAAA;AAtBY,QAAA,sBAAsB,0BAsBlC;AAUM,MAAM,wBAAwB,GAAG,CACtC,MAAc,EACd,MAAc,EACd,IAAY,EACZ,KAAgB,EAChB,EAAE;IACF,OAAO,IAAA,qBAAS,EAAC,IAAA,qCAA0B,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;AAC3E,CAAC,CAAA;AAPY,QAAA,wBAAwB,4BAOpC;AAYM,MAAM,wBAAwB,GAAG,CACtC,KAAgB,EAChB,MAAc,EACd,MAAc,EACd,KAAmB,EACnB,QAAsB,EACtB,IAAY,EACZ,EAAE;IACF,OAAO,IAAA,qBAAS,EACd,IAAA,qCAA0B,EAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,CACzE,CAAA;AACH,CAAC,CAAA;AAXY,QAAA,wBAAwB,4BAWpC;AAYM,MAAM,cAAc,GAAG,CAC5B,KAAgB,EAChB,MAAc,EACd,MAAc,EACd,KAAgB,EAChB,QAAmB,EACnB,IAAY,EACJ,EAAE;IACV,MAAM,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;IAC9E,MAAM,OAAO,GAAG,qBAAe,CAAC,MAAM,CAAC,KAAK,EAAE;QAC5C,KAAK;QACL,MAAM;QACN,MAAM;QACN,KAAK;QACL,QAAQ;QACR,IAAI;KACL,CAAC,CAAA;IACF,OAAO,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAA;AAC3B,CAAC,CAAA;AAlBY,QAAA,cAAc,kBAkB1B;AAOM,MAAM,mBAAmB,GAAG,CAAC,KAAsB,EAAU,EAAE;IACpE,OAAO,IAAA,qBAAS,EACd,qBAAe,CAAC,MAAM,CACpB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,EAC5C;QACE,KAAK,CAAC,OAAO;QACb,KAAK,CAAC,SAAS;QACf,KAAK,CAAC,wBAAwB;QAC9B,KAAK,CAAC,eAAe;KACtB,CACF,CACF,CAAA;AACH,CAAC,CAAA;AAZY,QAAA,mBAAmB,uBAY/B"}