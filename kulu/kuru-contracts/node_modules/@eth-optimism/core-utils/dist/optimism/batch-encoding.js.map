{"version": 3, "file": "batch-encoding.js", "sourceRoot": "", "sources": ["../../src/optimism/batch-encoding.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAuB;AAEvB,8DAA2E;AAC3E,iCAA0D;AAC1D,8CAAwC;AAExC,sCAAoC;AASpC,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,8CAAW,CAAA;IACX,yCAAQ,CAAA;AACV,CAAC,EAHW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAGpB;AAUD,MAAM,gCAAgC,GAAG,wBAAwB,CAAA;AACjE,MAAM,gCAAgC,GAAG,MAAM,CAAC,IAAI,CAClD,IAAA,SAAE,EAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EACjD,KAAK,CACN,CAAA;AAMM,MAAM,0BAA0B,GAAG,CACxC,CAA6B,EACrB,EAAE;IACV,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,YAAY,EAAE;QAC/B,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACvD;KACF;IACD,MAAM,KAAK,GAAG,sBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACtC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACrC,IAAI,UAAU,KAAK,gCAAgC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACnE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;KAChD;IACD,OAAO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;AACxB,CAAC,CAAA;AAdY,QAAA,0BAA0B,8BActC;AAKM,MAAM,0BAA0B,GAAG,CACxC,CAAS,EACmB,EAAE;IAC9B,MAAM,QAAQ,GACZ,IAAI,GAAG,gCAAgC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAA,iBAAQ,EAAC,CAAC,CAAC,CAAA;IACvE,OAAO,sBAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;AACxC,CAAC,CAAA;AANY,QAAA,0BAA0B,8BAMtC;AAGY,QAAA,cAAc,GAAG;IAC5B,MAAM,EAAE,CAAC,MAAkC,EAAU,EAAE;QACrD,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC;YAC/B,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;YACjD,qBAAqB,EAAE,MAAM,CAAC,qBAAqB;YACnD,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YACpD,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC1C,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAC7B;YACD,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC,CAAA;QACF,OAAO,KAAK,CAAC,KAAK,EAAE,CAAA;IACtB,CAAC;IACD,MAAM,EAAE,CAAC,CAAS,EAA8B,EAAE;QAChD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAC3C,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAClC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,gCAAgC,CAAC,KAAK,CAAC,EAAE;YACtE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;SAChD;QAED,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAiB,GAAG,CAAC,CAAA;QACxD,MAAM,MAAM,GAA+B;YACzC,oBAAoB,EAAE,KAAK,CAAC,oBAAoB;YAChD,qBAAqB,EAAE,KAAK,CAAC,qBAAqB;YAClD,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnC,wBAAwB,EAAE,CAAC,CAAC,wBAAwB;gBACpD,8BAA8B,EAAE,CAAC,CAAC,8BAA8B;gBAChE,SAAS,EAAE,CAAC,CAAC,SAAS;gBACtB,WAAW,EAAE,CAAC,CAAC,WAAW;aAC3B,CAAC,CAAC;YACH,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACjE,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;CACF,CAAA;AAED,MAAa,OAAQ,SAAQ,cAAM;IAUjC,YAAY,UAA4B,EAAE;QACxC,KAAK,EAAE,CAAA;QATF,6BAAwB,GAAW,CAAC,CAAA;QAEpC,mCAA8B,GAAW,CAAC,CAAA;QAE1C,cAAS,GAAW,CAAC,CAAA;QAErB,gBAAW,GAAW,CAAC,CAAA;QAK5B,IAAI,OAAO,OAAO,CAAC,wBAAwB,KAAK,QAAQ,EAAE;YACxD,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAA;SACjE;QACD,IAAI,OAAO,OAAO,CAAC,8BAA8B,KAAK,QAAQ,EAAE;YAC9D,IAAI,CAAC,8BAA8B;gBACjC,OAAO,CAAC,8BAA8B,CAAA;SACzC;QACD,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;YACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;SACnC;QACD,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,QAAQ,EAAE;YAC3C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;SACvC;IACH,CAAC;IAED,OAAO;QACL,OAAO,EAAE,CAAA;IACX,CAAC;IAED,KAAK,CAAC,EAAgB;QACpB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;QAC5C,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;QAClD,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC7B,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC/B,OAAO,EAAE,CAAA;IACX,CAAC;IAED,IAAI,CAAC,EAAgB;QACnB,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAC9C,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QACpD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QACjC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM;QACJ,OAAO;YACL,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,8BAA8B,EAAE,IAAI,CAAC,8BAA8B;YACnE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAA;IACH,CAAC;CACF;AAxDD,0BAwDC;AAGD,MAAa,SAAU,SAAQ,cAAM;IAOnC,YAAY,EAAgB;QAC1B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;IACd,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA;SAC3B;QACD,MAAM,EAAE,GAAG,IAAA,wBAAS,EAClB;YACE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ;YAC1B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ;YAC1B,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;YACd,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YACpB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;SACnB,EACD;YACE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SACb,CACF,CAAA;QAGD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;QAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA;IAC5B,CAAC;IAED,KAAK,CAAC,EAAgB;QACpB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1B,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvB,OAAO,EAAE,CAAA;IACX,CAAC;IAED,IAAI,CAAC,EAAgB;QACnB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAC5B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,aAAa;QACX,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,OAAO,IAAI,CAAC,EAAE,CAAA;SACf;QACD,OAAO,IAAA,oBAAK,EAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SACvC;QACD,OAAO,IAAA,wBAAS,EACd;YACE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ;YAC1B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ;YAC1B,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;YACd,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YACpB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;SACnB,EACD;YACE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SACb,CACF,CAAA;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACZ,IAAI,CAAC,EAAE,GAAG,IAAA,oBAAK,EAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC1B;QAED,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACrC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;YACd,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE;YAC/B,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;YAClB,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO;YACxB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;YAClB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;SACnB,CAAA;IACH,CAAC;IAKD,eAAe,CAAC,EAAU;QACxB,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;QAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAA;QAC7B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,KAAc;QAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACnC,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,CAAS;QAC9B,OAAO,IAAI,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;IACtC,CAAC;CACF;AAnHD,8BAmHC;AAED,MAAa,cAAe,SAAQ,cAAM;IAcxC,YAAY,UAAmC,EAAE;QAC/C,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QAEtB,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAA;SACzD;QACD,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,EAAE;YACrD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAA;SAC3D;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;SACjC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACvC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;SACzC;QACD,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;SACzB;IACH,CAAC;IAED,KAAK,CAAC,EAAgB;QACpB,EAAE,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAA;QAE/C,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;QACxC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QACtC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;YAChC,QAAQ,CAAC,OAAO,CACd,IAAI,OAAO,CAAC;gBACV,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;gBACZ,wBAAwB,EAAE,CAAC;gBAC3B,8BAA8B,EAAE,CAAC;aAClC,CAAC,CACH,CAAA;SACF;QACD,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAE9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SAClB;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,oBAAY,EAAE,CAAA;YACjC,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;gBAClC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;aACjB;YACD,MAAM,UAAU,GAAG,cAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAA;YACpD,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;SAC1B;aAAM;YAEL,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;gBAClC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;aACb;SACF;QAED,OAAO,EAAE,CAAA;IACX,CAAC;IAED,IAAI,CAAC,EAAgB;QACnB,MAAM,QAAQ,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAChC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,gCAAgC,CAAC,KAAK,CAAC,EAAE;YACpE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SACZ;QAED,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAC1C,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAE3C,MAAM,QAAQ,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAU,EAAE,CAAC,CAAA;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SAC5B;QAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE;YAChE,QAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBACpC,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;oBAC1B,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;oBACrC,MAAM,QAAQ,GAAG,cAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;oBACxC,EAAE,GAAG,IAAI,oBAAY,CAAC,QAAQ,CAAC,CAAA;oBAG/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBACtC,MAAK;iBACN;aACF;SACF;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,wBAAwB,EAAE,CAAC,EAAE,EAAE;gBACzD,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CAAY,EAAE,CAAC,CAAA;gBACxC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aAC3B;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;YAChC,OAAO,CAAC,CAAC,CAAA;SACV;QAED,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACpB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACnC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,CAAA;SAC1B;QAED,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;YAClC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAA;SACrB;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,KAAc;QAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACnC,CAAC;IAED,KAAK;QACH,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM;QACJ,OAAO;YACL,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAC9C,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;SACzD,CAAA;IACH,CAAC;CACF;AAvJD,wCAuJC"}