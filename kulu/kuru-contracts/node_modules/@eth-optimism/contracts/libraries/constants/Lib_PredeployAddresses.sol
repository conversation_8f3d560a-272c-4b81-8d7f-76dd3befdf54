// SPDX-License-Identifier: MIT
pragma solidity ^0.8.9;

/**
 * @title Lib_PredeployAddresses
 */
library Lib_PredeployAddresses {
    address internal constant L2_TO_L1_MESSAGE_PASSER = ******************************************;
    address internal constant L1_MESSAGE_SENDER = ******************************************;
    address internal constant DEPLOYER_WHITELIST = ******************************************;
    address payable internal constant OVM_ETH = payable(******************************************);
    address internal constant L2_CROSS_DOMAIN_MESSENGER =
        ******************************************;
    address internal constant LIB_ADDRESS_MANAGER = ******************************************;
    address internal constant PROXY_EOA = ******************************************;
    address internal constant L2_STANDARD_BRIDGE = ******************************************;
    address internal constant SEQUENCER_FEE_WALLET = ******************************************;
    address internal constant L2_STANDARD_TOKEN_FACTORY =
        ******************************************;
    address internal constant L1_BLOCK_NUMBER = ******************************************;
}
