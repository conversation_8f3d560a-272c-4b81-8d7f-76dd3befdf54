{"name": "@eth-optimism/contracts", "version": "0.5.40", "description": "[Optimism] L1 and L2 smart contracts for Optimism", "main": "dist/index", "types": "dist/index", "files": ["dist/**/*.js", "dist/**/*.d.ts", "dist/types", "artifacts/contracts/chugsplash/**/*.json", "artifacts/contracts/L1/**/*.json", "artifacts/contracts/L2/**/*.json", "artifacts/contracts/libraries/**/*.json", "artifacts/contracts/standards/**/*.json", "chugsplash", "L1", "L2", "libraries", "standards"], "scripts": {"build": "yarn build:contracts && yarn copy:contracts && yarn autogen:artifacts && yarn build:typescript", "build:typescript": "tsc -p ./tsconfig.json", "build:contracts": "hardhat compile --show-stack-traces", "autogen:markdown": "ts-node scripts/generate-markdown.ts", "autogen:artifacts": "ts-node scripts/generate-artifacts.ts && ts-node scripts/generate-deployed-artifacts.ts", "copy:contracts": "yarn copyfiles -u 1 -e \"**/test-*/**/*\" \"contracts/**/*\" ./", "test": "yarn test:contracts", "test:contracts": "hardhat test --show-stack-traces", "test:coverage": "NODE_OPTIONS=--max_old_space_size=8192 hardhat coverage && istanbul check-coverage --statements 90 --branches 84 --functions 88 --lines 90", "test:slither": "slither .", "pretest:slither": "rm -f @openzeppelin && rm -f @ens && rm -f hardhat && ln -s ../../node_modules/@openzeppelin @openzeppelin && ln -s ../../node_modules/@ens @ens && ln -s ../../node_modules/hardhat hardhat", "posttest:slither": "rm -f @openzeppelin && rm -f @ens && rm -f hardhat", "lint:ts:check": "eslint . --max-warnings=0", "lint:contracts:check": "yarn solhint -f table 'contracts/**/*.sol'", "lint:check": "yarn lint:contracts:check && yarn lint:ts:check", "lint:ts:fix": "eslint --fix .", "lint:contracts:fix": "yarn prettier --write 'contracts/**/*.sol'", "lint:fix": "yarn lint:contracts:fix && yarn lint:ts:fix", "lint": "yarn lint:fix && yarn lint:check", "clean": "rm -rf ./dist ./artifacts ./cache ./coverage ./tsconfig.tsbuildinfo ./chugsplash ./L1 ./L2 ./libraries ./standards", "pre-commit": "lint-staged", "validateDocs": "hardhat validateOutput"}, "keywords": ["optimism", "ethereum", "contracts", "solidity"], "homepage": "https://github.com/ethereum-optimism/optimism/tree/develop/packages/contracts#readme", "license": "MIT", "author": "Optimism PBC", "repository": {"type": "git", "url": "https://github.com/ethereum-optimism/optimism.git"}, "dependencies": {"@eth-optimism/core-utils": "0.12.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0"}, "devDependencies": {"@codechecks/client": "^0.1.11", "@defi-wonderland/smock": "^2.0.2", "@eth-optimism/hardhat-deploy-config": "^0.2.5", "@ethersproject/abi": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hardware-wallets": "^5.7.0", "@ethersproject/providers": "^5.7.0", "@nomiclabs/ethereumjs-vm": "^4.2.2", "@nomiclabs/hardhat-ethers": "^2.0.2", "@nomiclabs/hardhat-etherscan": "^3.0.3", "@nomiclabs/hardhat-waffle": "^2.0.1", "@openzeppelin/contracts": "4.3.2", "@openzeppelin/contracts-upgradeable": "4.7.1", "@primitivefi/hardhat-dodoc": "^0.1.3", "@typechain/ethers-v5": "^10.1.0", "@typechain/hardhat": "^6.1.2", "@types/chai": "^4.2.18", "@types/lodash": "^4.14.168", "@types/mkdirp": "^1.0.1", "@types/mocha": "^8.2.2", "@types/node": "^17.0.21", "chai": "^4.3.4", "copyfiles": "^2.3.0", "directory-tree": "^2.2.7", "dotenv": "^10.0.0", "ethereum-waffle": "^3.3.0", "ethers": "^5.7.0", "glob": "^7.1.6", "hardhat": "^2.9.6", "hardhat-deploy": "^0.11.10", "hardhat-gas-reporter": "^1.0.4", "hardhat-output-validator": "^0.1.18", "istanbul": "^0.4.5", "lint-staged": "11.0.0", "lodash": "^4.17.21", "merkle-patricia-tree": "^4.0.0", "merkletreejs": "^0.2.18", "mkdirp": "^1.0.4", "mocha": "^8.4.0", "prettier": "^2.8.0", "prettier-plugin-solidity": "^1.0.0-beta.18", "random-bytes-seed": "^1.0.3", "rlp": "^2.2.6", "solhint": "^3.3.6", "solhint-plugin-prettier": "^0.0.5", "solidity-coverage": "^0.7.17", "squirrelly": "^8.0.8", "ts-generator": "0.0.8", "ts-node": "^10.9.1", "typechain": "^8.1.0", "typescript": "^4.9.3"}, "peerDependencies": {"ethers": "^5"}}