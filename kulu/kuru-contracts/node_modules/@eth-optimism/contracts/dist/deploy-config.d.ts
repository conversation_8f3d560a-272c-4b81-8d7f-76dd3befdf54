export interface DeployConfig {
    isForkedNetwork?: boolean;
    numDeployConfirmations?: number;
    gasPrice?: number;
    l1BlockTimeSeconds: number;
    l2BlockGasLimit: number;
    l2ChainId: number;
    ctcL2GasDiscountDivisor: number;
    ctcEnqueueGasCost: number;
    sccFaultProofWindowSeconds: number;
    sccSequencerPublishWindowSeconds: number;
    ovmSequencerAddress: string;
    ovmProposerAddress: string;
    ovmBlockSignerAddress: string;
    ovmFeeWalletAddress: string;
    ovmAddressManagerOwner: string;
    ovmGasPriceOracleOwner: string;
    ovmWhitelistOwner?: string;
    gasPriceOracleOverhead?: number;
    gasPriceOracleScalar?: number;
    gasPriceOracleDecimals?: number;
    gasPriceOracleL1BaseFee?: number;
    gasPriceOracleL2GasPrice?: number;
    hfBerlinBlock?: number;
}
export declare const getDeployConfig: (network: string) => Required<DeployConfig>;
export declare const parseDeployConfig: (config: DeployConfig) => Required<DeployConfig>;
