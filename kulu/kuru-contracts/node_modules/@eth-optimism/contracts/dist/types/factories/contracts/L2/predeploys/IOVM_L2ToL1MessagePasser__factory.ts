/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from "ethers";
import type { Provider } from "@ethersproject/providers";
import type {
  IOVM_L2ToL1MessagePasser,
  IOVM_L2ToL1MessagePasserInterface,
} from "../../../../contracts/L2/predeploys/IOVM_L2ToL1MessagePasser";

const _abi = [
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "uint256",
        name: "_nonce",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "address",
        name: "_sender",
        type: "address",
      },
      {
        indexed: false,
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "L2ToL1Message",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_message",
        type: "bytes",
      },
    ],
    name: "passMessageToL1",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

export class IOVM_L2ToL1MessagePasser__factory {
  static readonly abi = _abi;
  static createInterface(): IOVM_L2ToL1MessagePasserInterface {
    return new utils.Interface(_abi) as IOVM_L2ToL1MessagePasserInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): IOVM_L2ToL1MessagePasser {
    return new Contract(
      address,
      _abi,
      signerOrProvider
    ) as IOVM_L2ToL1MessagePasser;
  }
}
