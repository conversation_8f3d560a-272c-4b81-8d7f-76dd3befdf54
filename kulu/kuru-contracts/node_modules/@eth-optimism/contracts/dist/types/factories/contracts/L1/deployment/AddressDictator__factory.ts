/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  AddressDictator,
  AddressDictatorInterface,
} from "../../../../contracts/L1/deployment/AddressDictator";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract Lib_AddressManager",
        name: "_manager",
        type: "address",
      },
      {
        internalType: "address",
        name: "_finalOwner",
        type: "address",
      },
      {
        internalType: "string[]",
        name: "_names",
        type: "string[]",
      },
      {
        internalType: "address[]",
        name: "_addresses",
        type: "address[]",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "finalOwner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getNamedAddresses",
    outputs: [
      {
        components: [
          {
            internalType: "string",
            name: "name",
            type: "string",
          },
          {
            internalType: "address",
            name: "addr",
            type: "address",
          },
        ],
        internalType: "struct AddressDictator.NamedAddress[]",
        name: "",
        type: "tuple[]",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "manager",
    outputs: [
      {
        internalType: "contract Lib_AddressManager",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "returnOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "setAddresses",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

const _bytecode =
  "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";

type AddressDictatorConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: AddressDictatorConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class AddressDictator__factory extends ContractFactory {
  constructor(...args: AddressDictatorConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    _manager: PromiseOrValue<string>,
    _finalOwner: PromiseOrValue<string>,
    _names: PromiseOrValue<string>[],
    _addresses: PromiseOrValue<string>[],
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<AddressDictator> {
    return super.deploy(
      _manager,
      _finalOwner,
      _names,
      _addresses,
      overrides || {}
    ) as Promise<AddressDictator>;
  }
  override getDeployTransaction(
    _manager: PromiseOrValue<string>,
    _finalOwner: PromiseOrValue<string>,
    _names: PromiseOrValue<string>[],
    _addresses: PromiseOrValue<string>[],
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(
      _manager,
      _finalOwner,
      _names,
      _addresses,
      overrides || {}
    );
  }
  override attach(address: string): AddressDictator {
    return super.attach(address) as AddressDictator;
  }
  override connect(signer: Signer): AddressDictator__factory {
    return super.connect(signer) as AddressDictator__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): AddressDictatorInterface {
    return new utils.Interface(_abi) as AddressDictatorInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): AddressDictator {
    return new Contract(address, _abi, signerOrProvider) as AddressDictator;
  }
}
