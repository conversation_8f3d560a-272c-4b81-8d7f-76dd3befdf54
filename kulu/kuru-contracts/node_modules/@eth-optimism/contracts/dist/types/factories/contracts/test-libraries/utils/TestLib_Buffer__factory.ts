/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_Buffer,
  TestLib_BufferInterface,
} from "../../../../contracts/test-libraries/utils/TestLib_Buffer";

const _abi = [
  {
    inputs: [
      {
        internalType: "uint40",
        name: "_index",
        type: "uint40",
      },
      {
        internalType: "bytes27",
        name: "_extraData",
        type: "bytes27",
      },
    ],
    name: "deleteElementsAfterInclusive",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint40",
        name: "_index",
        type: "uint40",
      },
    ],
    name: "deleteElementsAfterInclusive",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_index",
        type: "uint256",
      },
    ],
    name: "get",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getContext",
    outputs: [
      {
        components: [
          {
            internalType: "uint40",
            name: "length",
            type: "uint40",
          },
          {
            internalType: "bytes27",
            name: "extraData",
            type: "bytes27",
          },
        ],
        internalType: "struct Lib_Buffer.BufferContext",
        name: "",
        type: "tuple",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getExtraData",
    outputs: [
      {
        internalType: "bytes27",
        name: "",
        type: "bytes27",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getLength",
    outputs: [
      {
        internalType: "uint40",
        name: "",
        type: "uint40",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "_value",
        type: "bytes32",
      },
      {
        internalType: "bytes27",
        name: "_extraData",
        type: "bytes27",
      },
    ],
    name: "push",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "_value",
        type: "bytes32",
      },
    ],
    name: "push",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint40",
        name: "_index",
        type: "uint40",
      },
      {
        internalType: "bytes27",
        name: "_extraData",
        type: "bytes27",
      },
    ],
    name: "setContext",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes27",
        name: "_extraData",
        type: "bytes27",
      },
    ],
    name: "setExtraData",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_BufferConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_BufferConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_Buffer__factory extends ContractFactory {
  constructor(...args: TestLib_BufferConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_Buffer> {
    return super.deploy(overrides || {}) as Promise<TestLib_Buffer>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_Buffer {
    return super.attach(address) as TestLib_Buffer;
  }
  override connect(signer: Signer): TestLib_Buffer__factory {
    return super.connect(signer) as TestLib_Buffer__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_BufferInterface {
    return new utils.Interface(_abi) as TestLib_BufferInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_Buffer {
    return new Contract(address, _abi, signerOrProvider) as TestLib_Buffer;
  }
}
