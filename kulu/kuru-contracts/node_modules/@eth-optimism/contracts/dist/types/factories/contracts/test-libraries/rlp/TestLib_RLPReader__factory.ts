/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_RLPReader,
  TestLib_RLPReaderInterface,
} from "../../../../contracts/test-libraries/rlp/TestLib_RLPReader";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readAddress",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readBool",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readBytes",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readBytes32",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readList",
    outputs: [
      {
        internalType: "bytes[]",
        name: "",
        type: "bytes[]",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readString",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_in",
        type: "bytes",
      },
    ],
    name: "readUint256",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_RLPReaderConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_RLPReaderConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_RLPReader__factory extends ContractFactory {
  constructor(...args: TestLib_RLPReaderConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_RLPReader> {
    return super.deploy(overrides || {}) as Promise<TestLib_RLPReader>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_RLPReader {
    return super.attach(address) as TestLib_RLPReader;
  }
  override connect(signer: Signer): TestLib_RLPReader__factory {
    return super.connect(signer) as TestLib_RLPReader__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_RLPReaderInterface {
    return new utils.Interface(_abi) as TestLib_RLPReaderInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_RLPReader {
    return new Contract(address, _abi, signerOrProvider) as TestLib_RLPReader;
  }
}
