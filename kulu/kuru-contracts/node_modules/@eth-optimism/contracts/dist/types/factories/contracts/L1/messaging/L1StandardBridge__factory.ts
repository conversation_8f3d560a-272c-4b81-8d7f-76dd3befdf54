/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  L1StandardBridge,
  L1StandardBridgeInterface,
} from "../../../../contracts/L1/messaging/L1StandardBridge";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "_l1Token",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_l2Token",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        indexed: false,
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "ERC20DepositInitiated",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "_l1Token",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_l2Token",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        indexed: false,
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "ERC20WithdrawalFinalized",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "ETHDepositInitiated",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "ETHWithdrawalFinalized",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_l1Token",
        type: "address",
      },
      {
        internalType: "address",
        name: "_l2Token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        internalType: "uint32",
        name: "_l2Gas",
        type: "uint32",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "depositERC20",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_l1Token",
        type: "address",
      },
      {
        internalType: "address",
        name: "_l2Token",
        type: "address",
      },
      {
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        internalType: "uint32",
        name: "_l2Gas",
        type: "uint32",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "depositERC20To",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint32",
        name: "_l2Gas",
        type: "uint32",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "depositETH",
    outputs: [],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        internalType: "uint32",
        name: "_l2Gas",
        type: "uint32",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "depositETHTo",
    outputs: [],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "deposits",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "donateETH",
    outputs: [],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_l1Token",
        type: "address",
      },
      {
        internalType: "address",
        name: "_l2Token",
        type: "address",
      },
      {
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "finalizeERC20Withdrawal",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_from",
        type: "address",
      },
      {
        internalType: "address",
        name: "_to",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "_amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "_data",
        type: "bytes",
      },
    ],
    name: "finalizeETHWithdrawal",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_l1messenger",
        type: "address",
      },
      {
        internalType: "address",
        name: "_l2TokenBridge",
        type: "address",
      },
    ],
    name: "initialize",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "l2TokenBridge",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "messenger",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
];

const _bytecode =
  "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";

type L1StandardBridgeConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: L1StandardBridgeConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class L1StandardBridge__factory extends ContractFactory {
  constructor(...args: L1StandardBridgeConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<L1StandardBridge> {
    return super.deploy(overrides || {}) as Promise<L1StandardBridge>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): L1StandardBridge {
    return super.attach(address) as L1StandardBridge;
  }
  override connect(signer: Signer): L1StandardBridge__factory {
    return super.connect(signer) as L1StandardBridge__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): L1StandardBridgeInterface {
    return new utils.Interface(_abi) as L1StandardBridgeInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): L1StandardBridge {
    return new Contract(address, _abi, signerOrProvider) as L1StandardBridge;
  }
}
