/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_BytesUtils,
  TestLib_BytesUtilsInterface,
} from "../../../../contracts/test-libraries/utils/TestLib_BytesUtils";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_preBytes",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_postBytes",
        type: "bytes",
      },
    ],
    name: "concat",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_other",
        type: "bytes",
      },
    ],
    name: "equal",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
    ],
    name: "fromNibbles",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
      {
        internalType: "uint256",
        name: "_start",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "_length",
        type: "uint256",
      },
    ],
    name: "slice",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
      {
        internalType: "uint256",
        name: "_start",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "_length",
        type: "uint256",
      },
    ],
    name: "sliceWithTaintedMemory",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
    ],
    name: "toBytes32",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
    ],
    name: "toNibbles",
    outputs: [
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_bytes",
        type: "bytes",
      },
    ],
    name: "toUint256",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_BytesUtilsConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_BytesUtilsConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_BytesUtils__factory extends ContractFactory {
  constructor(...args: TestLib_BytesUtilsConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_BytesUtils> {
    return super.deploy(overrides || {}) as Promise<TestLib_BytesUtils>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_BytesUtils {
    return super.attach(address) as TestLib_BytesUtils;
  }
  override connect(signer: Signer): TestLib_BytesUtils__factory {
    return super.connect(signer) as TestLib_BytesUtils__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_BytesUtilsInterface {
    return new utils.Interface(_abi) as TestLib_BytesUtilsInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_BytesUtils {
    return new Contract(address, _abi, signerOrProvider) as TestLib_BytesUtils;
  }
}
