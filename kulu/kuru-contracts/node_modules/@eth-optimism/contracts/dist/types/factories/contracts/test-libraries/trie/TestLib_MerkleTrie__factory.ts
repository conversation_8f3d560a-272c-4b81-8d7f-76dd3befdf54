/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_MerkleTrie,
  TestLib_MerkleTrieInterface,
} from "../../../../contracts/test-libraries/trie/TestLib_MerkleTrie";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_key",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_proof",
        type: "bytes",
      },
      {
        internalType: "bytes32",
        name: "_root",
        type: "bytes32",
      },
    ],
    name: "get",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_key",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_value",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_proof",
        type: "bytes",
      },
      {
        internalType: "bytes32",
        name: "_root",
        type: "bytes32",
      },
    ],
    name: "verifyInclusionProof",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_MerkleTrieConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_MerkleTrieConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_MerkleTrie__factory extends ContractFactory {
  constructor(...args: TestLib_MerkleTrieConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_MerkleTrie> {
    return super.deploy(overrides || {}) as Promise<TestLib_MerkleTrie>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_MerkleTrie {
    return super.attach(address) as TestLib_MerkleTrie;
  }
  override connect(signer: Signer): TestLib_MerkleTrie__factory {
    return super.connect(signer) as TestLib_MerkleTrie__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_MerkleTrieInterface {
    return new utils.Interface(_abi) as TestLib_MerkleTrieInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_MerkleTrie {
    return new Contract(address, _abi, signerOrProvider) as TestLib_MerkleTrie;
  }
}
