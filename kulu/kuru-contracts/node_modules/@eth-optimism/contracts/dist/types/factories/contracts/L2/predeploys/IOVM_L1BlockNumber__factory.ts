/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from "ethers";
import type { Provider } from "@ethersproject/providers";
import type {
  IOVM_L1BlockNumber,
  IOVM_L1BlockNumberInterface,
} from "../../../../contracts/L2/predeploys/IOVM_L1BlockNumber";

const _abi = [
  {
    inputs: [],
    name: "getL1BlockNumber",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
];

export class IOVM_L1BlockNumber__factory {
  static readonly abi = _abi;
  static createInterface(): IOVM_L1BlockNumberInterface {
    return new utils.Interface(_abi) as IOVM_L1BlockNumberInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): IOVM_L1BlockNumber {
    return new Contract(address, _abi, signerOr<PERSON>rovider) as IOVM_L1BlockNumber;
  }
}
