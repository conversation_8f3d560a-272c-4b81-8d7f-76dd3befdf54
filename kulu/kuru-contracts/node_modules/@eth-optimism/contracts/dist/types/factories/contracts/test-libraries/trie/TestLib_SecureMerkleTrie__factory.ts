/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_SecureMerkleTrie,
  TestLib_SecureMerkleTrieInterface,
} from "../../../../contracts/test-libraries/trie/TestLib_SecureMerkleTrie";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_key",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_proof",
        type: "bytes",
      },
      {
        internalType: "bytes32",
        name: "_root",
        type: "bytes32",
      },
    ],
    name: "get",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "_key",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_value",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "_proof",
        type: "bytes",
      },
      {
        internalType: "bytes32",
        name: "_root",
        type: "bytes32",
      },
    ],
    name: "verifyInclusionProof",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_SecureMerkleTrieConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_SecureMerkleTrieConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_SecureMerkleTrie__factory extends ContractFactory {
  constructor(...args: TestLib_SecureMerkleTrieConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_SecureMerkleTrie> {
    return super.deploy(overrides || {}) as Promise<TestLib_SecureMerkleTrie>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_SecureMerkleTrie {
    return super.attach(address) as TestLib_SecureMerkleTrie;
  }
  override connect(signer: Signer): TestLib_SecureMerkleTrie__factory {
    return super.connect(signer) as TestLib_SecureMerkleTrie__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_SecureMerkleTrieInterface {
    return new utils.Interface(_abi) as TestLib_SecureMerkleTrieInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_SecureMerkleTrie {
    return new Contract(
      address,
      _abi,
      signerOrProvider
    ) as TestLib_SecureMerkleTrie;
  }
}
