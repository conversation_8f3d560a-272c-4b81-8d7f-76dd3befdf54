/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_MerkleTree,
  TestLib_MerkleTreeInterface,
} from "../../../../contracts/test-libraries/utils/TestLib_MerkleTree";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes32[]",
        name: "_elements",
        type: "bytes32[]",
      },
    ],
    name: "getMerkleRoot",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "_root",
        type: "bytes32",
      },
      {
        internalType: "bytes32",
        name: "_leaf",
        type: "bytes32",
      },
      {
        internalType: "uint256",
        name: "_index",
        type: "uint256",
      },
      {
        internalType: "bytes32[]",
        name: "_siblings",
        type: "bytes32[]",
      },
      {
        internalType: "uint256",
        name: "_totalLeaves",
        type: "uint256",
      },
    ],
    name: "verify",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "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";

type TestLib_MerkleTreeConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_MerkleTreeConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_MerkleTree__factory extends ContractFactory {
  constructor(...args: TestLib_MerkleTreeConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_MerkleTree> {
    return super.deploy(overrides || {}) as Promise<TestLib_MerkleTree>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_MerkleTree {
    return super.attach(address) as TestLib_MerkleTree;
  }
  override connect(signer: Signer): TestLib_MerkleTree__factory {
    return super.connect(signer) as TestLib_MerkleTree__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_MerkleTreeInterface {
    return new utils.Interface(_abi) as TestLib_MerkleTreeInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_MerkleTree {
    return new Contract(address, _abi, signerOrProvider) as TestLib_MerkleTree;
  }
}
