/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  TestLib_OVMCodec,
  TestLib_OVMCodecInterface,
} from "../../../../contracts/test-libraries/codec/TestLib_OVMCodec";

const _abi = [
  {
    inputs: [
      {
        components: [
          {
            internalType: "uint256",
            name: "timestamp",
            type: "uint256",
          },
          {
            internalType: "uint256",
            name: "blockNumber",
            type: "uint256",
          },
          {
            internalType: "enum Lib_OVMCodec.QueueOrigin",
            name: "l1QueueOrigin",
            type: "uint8",
          },
          {
            internalType: "address",
            name: "l1Tx<PERSON>rigin",
            type: "address",
          },
          {
            internalType: "address",
            name: "entrypoint",
            type: "address",
          },
          {
            internalType: "uint256",
            name: "gasLimit",
            type: "uint256",
          },
          {
            internalType: "bytes",
            name: "data",
            type: "bytes",
          },
        ],
        internalType: "struct Lib_OVMCodec.Transaction",
        name: "_transaction",
        type: "tuple",
      },
    ],
    name: "encodeTransaction",
    outputs: [
      {
        internalType: "bytes",
        name: "_encoded",
        type: "bytes",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: "uint256",
            name: "timestamp",
            type: "uint256",
          },
          {
            internalType: "uint256",
            name: "blockNumber",
            type: "uint256",
          },
          {
            internalType: "enum Lib_OVMCodec.QueueOrigin",
            name: "l1QueueOrigin",
            type: "uint8",
          },
          {
            internalType: "address",
            name: "l1TxOrigin",
            type: "address",
          },
          {
            internalType: "address",
            name: "entrypoint",
            type: "address",
          },
          {
            internalType: "uint256",
            name: "gasLimit",
            type: "uint256",
          },
          {
            internalType: "bytes",
            name: "data",
            type: "bytes",
          },
        ],
        internalType: "struct Lib_OVMCodec.Transaction",
        name: "_transaction",
        type: "tuple",
      },
    ],
    name: "hashTransaction",
    outputs: [
      {
        internalType: "bytes32",
        name: "_hash",
        type: "bytes32",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
];

const _bytecode =
  "0x608060405234801561001057600080fd5b50610466806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c80633170de2a1461003b578063a3e6d2fc14610061575b600080fd5b61004e610049366004610242565b610081565b6040519081526020015b60405180910390f35b61007461006f366004610242565b610092565b6040516100589190610330565b600061008c8261009d565b92915050565b606061008c826100b6565b60006100a8826100b6565b805190602001209050919050565b6060816000015182602001518360400151846060015185608001518660a001518760c001516040516020016100f19796959493929190610381565b6040516020818303038152906040529050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b60405160e0810167ffffffffffffffff8111828210171561015957610159610107565b60405290565b80356002811061016e57600080fd5b919050565b803573ffffffffffffffffffffffffffffffffffffffff8116811461016e57600080fd5b600082601f8301126101a857600080fd5b813567ffffffffffffffff808211156101c3576101c3610107565b604051601f83017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0908116603f0116810190828211818310171561020957610209610107565b8160405283815286602085880101111561022257600080fd5b836020870160208301376000602085830101528094505050505092915050565b60006020828403121561025457600080fd5b813567ffffffffffffffff8082111561026c57600080fd5b9083019060e0828603121561028057600080fd5b610288610136565b82358152602083013560208201526102a26040840161015f565b60408201526102b360608401610173565b60608201526102c460808401610173565b608082015260a083013560a082015260c0830135828111156102e557600080fd5b6102f187828601610197565b60c08301525095945050505050565b60005b8381101561031b578181015183820152602001610303565b8381111561032a576000848401525b50505050565b602081526000825180602084015261034f816040850160208701610300565b601f017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0169190910160400192915050565b8781528660208201526000600287106103c3577f4e487b7100000000000000000000000000000000000000000000000000000000600052602160045260246000fd5b8660f81b60408301527fffffffffffffffffffffffffffffffffffffffff000000000000000000000000808760601b166041840152808660601b16605584015250836069830152825161041d816089850160208701610300565b919091016089019897505050505050505056fea26469706673582212204aa64f4a9c5361a76172bb8f6b571eb4ab45a1cd31fdaaf5cadba5e0a864ea7264736f6c63430008090033";

type TestLib_OVMCodecConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestLib_OVMCodecConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestLib_OVMCodec__factory extends ContractFactory {
  constructor(...args: TestLib_OVMCodecConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<TestLib_OVMCodec> {
    return super.deploy(overrides || {}) as Promise<TestLib_OVMCodec>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): TestLib_OVMCodec {
    return super.attach(address) as TestLib_OVMCodec;
  }
  override connect(signer: Signer): TestLib_OVMCodec__factory {
    return super.connect(signer) as TestLib_OVMCodec__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestLib_OVMCodecInterface {
    return new utils.Interface(_abi) as TestLib_OVMCodecInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): TestLib_OVMCodec {
    return new Contract(address, _abi, signerOrProvider) as TestLib_OVMCodec;
  }
}
