/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Signer, utils } from "ethers";
import type { Provider } from "@ethersproject/providers";
import type {
  IChainStorageContainer,
  IChainStorageContainerInterface,
} from "../../../../contracts/L1/rollup/IChainStorageContainer";

const _abi = [
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_index",
        type: "uint256",
      },
      {
        internalType: "bytes27",
        name: "_globalMetadata",
        type: "bytes27",
      },
    ],
    name: "deleteElementsAfterInclusive",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_index",
        type: "uint256",
      },
    ],
    name: "deleteElementsAfterInclusive",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_index",
        type: "uint256",
      },
    ],
    name: "get",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getGlobalMetadata",
    outputs: [
      {
        internalType: "bytes27",
        name: "",
        type: "bytes27",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "length",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "_object",
        type: "bytes32",
      },
      {
        internalType: "bytes27",
        name: "_globalMetadata",
        type: "bytes27",
      },
    ],
    name: "push",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "_object",
        type: "bytes32",
      },
    ],
    name: "push",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes27",
        name: "_globalMetadata",
        type: "bytes27",
      },
    ],
    name: "setGlobalMetadata",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

export class IChainStorageContainer__factory {
  static readonly abi = _abi;
  static createInterface(): IChainStorageContainerInterface {
    return new utils.Interface(_abi) as IChainStorageContainerInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): IChainStorageContainer {
    return new Contract(
      address,
      _abi,
      signerOrProvider
    ) as IChainStorageContainer;
  }
}
