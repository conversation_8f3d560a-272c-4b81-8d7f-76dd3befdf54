/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../../common";
import type {
  CrossDomainEnabled,
  CrossDomainEnabledInterface,
} from "../../../../contracts/libraries/bridge/CrossDomainEnabled";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "_messenger",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "messenger",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
];

const _bytecode =
  "0x608060405234801561001057600080fd5b5060405161013d38038061013d83398101604081905261002f91610054565b600080546001600160a01b0319166001600160a01b0392909216919091179055610084565b60006020828403121561006657600080fd5b81516001600160a01b038116811461007d57600080fd5b9392505050565b60ab806100926000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c80633cb747bf14602d575b600080fd5b600054604c9073ffffffffffffffffffffffffffffffffffffffff1681565b60405173ffffffffffffffffffffffffffffffffffffffff909116815260200160405180910390f3fea264697066735822122007712a382b03013a51c1c5af10d9e7b9b516e09be353a5f94af5d0263a013ea064736f6c63430008090033";

type CrossDomainEnabledConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: CrossDomainEnabledConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class CrossDomainEnabled__factory extends ContractFactory {
  constructor(...args: CrossDomainEnabledConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    _messenger: PromiseOrValue<string>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<CrossDomainEnabled> {
    return super.deploy(
      _messenger,
      overrides || {}
    ) as Promise<CrossDomainEnabled>;
  }
  override getDeployTransaction(
    _messenger: PromiseOrValue<string>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(_messenger, overrides || {});
  }
  override attach(address: string): CrossDomainEnabled {
    return super.attach(address) as CrossDomainEnabled;
  }
  override connect(signer: Signer): CrossDomainEnabled__factory {
    return super.connect(signer) as CrossDomainEnabled__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): CrossDomainEnabledInterface {
    return new utils.Interface(_abi) as CrossDomainEnabledInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): CrossDomainEnabled {
    return new Contract(address, _abi, signerOrProvider) as CrossDomainEnabled;
  }
}
