/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import { Signer, utils, Contract, ContractFactory, Overrides } from "ethers";
import type { Provider, TransactionRequest } from "@ethersproject/providers";
import type { PromiseOrValue } from "../../../common";
import type {
  FailingReceiver,
  FailingReceiverInterface,
} from "../../../contracts/test-helpers/FailingReceiver";

const _abi = [
  {
    stateMutability: "payable",
    type: "receive",
  },
];

const _bytecode =
  "0x6080604052348015600f57600080fd5b5060a98061001e6000396000f3fe608060405236606e576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152600f60248201527f4661696c696e6752656365697665720000000000000000000000000000000000604482015260640160405180910390fd5b600080fdfea2646970667358221220c9b1eb7272b94e8a06dfbd10541b4fafa56faae126d5224ff13e47739b07781a64736f6c63430008090033";

type FailingReceiverConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: FailingReceiverConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class FailingReceiver__factory extends ContractFactory {
  constructor(...args: FailingReceiverConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override deploy(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<FailingReceiver> {
    return super.deploy(overrides || {}) as Promise<FailingReceiver>;
  }
  override getDeployTransaction(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): TransactionRequest {
    return super.getDeployTransaction(overrides || {});
  }
  override attach(address: string): FailingReceiver {
    return super.attach(address) as FailingReceiver;
  }
  override connect(signer: Signer): FailingReceiver__factory {
    return super.connect(signer) as FailingReceiver__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): FailingReceiverInterface {
    return new utils.Interface(_abi) as FailingReceiverInterface;
  }
  static connect(
    address: string,
    signerOrProvider: Signer | Provider
  ): FailingReceiver {
    return new Contract(address, _abi, signerOrProvider) as FailingReceiver;
  }
}
