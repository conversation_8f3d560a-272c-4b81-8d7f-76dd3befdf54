/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_MerkleTreeInterface extends utils.Interface {
  functions: {
    "getMerkleRoot(bytes32[])": FunctionFragment;
    "verify(bytes32,bytes32,uint256,bytes32[],uint256)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic: "getMerkleRoot" | "verify"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "getMerkleRoot",
    values: [PromiseOrValue<BytesLike>[]]
  ): string;
  encodeFunctionData(
    functionFragment: "verify",
    values: [
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>,
      PromiseOrValue<BytesLike>[],
      PromiseOrValue<BigNumberish>
    ]
  ): string;

  decodeFunctionResult(
    functionFragment: "getMerkleRoot",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "verify", data: BytesLike): Result;

  events: {};
}

export interface TestLib_MerkleTree extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_MerkleTreeInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    getMerkleRoot(
      _elements: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<[string]>;

    verify(
      _root: PromiseOrValue<BytesLike>,
      _leaf: PromiseOrValue<BytesLike>,
      _index: PromiseOrValue<BigNumberish>,
      _siblings: PromiseOrValue<BytesLike>[],
      _totalLeaves: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<[boolean]>;
  };

  getMerkleRoot(
    _elements: PromiseOrValue<BytesLike>[],
    overrides?: CallOverrides
  ): Promise<string>;

  verify(
    _root: PromiseOrValue<BytesLike>,
    _leaf: PromiseOrValue<BytesLike>,
    _index: PromiseOrValue<BigNumberish>,
    _siblings: PromiseOrValue<BytesLike>[],
    _totalLeaves: PromiseOrValue<BigNumberish>,
    overrides?: CallOverrides
  ): Promise<boolean>;

  callStatic: {
    getMerkleRoot(
      _elements: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<string>;

    verify(
      _root: PromiseOrValue<BytesLike>,
      _leaf: PromiseOrValue<BytesLike>,
      _index: PromiseOrValue<BigNumberish>,
      _siblings: PromiseOrValue<BytesLike>[],
      _totalLeaves: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<boolean>;
  };

  filters: {};

  estimateGas: {
    getMerkleRoot(
      _elements: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    verify(
      _root: PromiseOrValue<BytesLike>,
      _leaf: PromiseOrValue<BytesLike>,
      _index: PromiseOrValue<BigNumberish>,
      _siblings: PromiseOrValue<BytesLike>[],
      _totalLeaves: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    getMerkleRoot(
      _elements: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    verify(
      _root: PromiseOrValue<BytesLike>,
      _leaf: PromiseOrValue<BytesLike>,
      _index: PromiseOrValue<BigNumberish>,
      _siblings: PromiseOrValue<BytesLike>[],
      _totalLeaves: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
