/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export declare namespace Lib_Buffer {
  export type BufferContextStruct = {
    length: PromiseOrValue<BigNumberish>;
    extraData: PromiseOrValue<BytesLike>;
  };

  export type BufferContextStructOutput = [number, string] & {
    length: number;
    extraData: string;
  };
}

export interface TestLib_BufferInterface extends utils.Interface {
  functions: {
    "deleteElementsAfterInclusive(uint40,bytes27)": FunctionFragment;
    "deleteElementsAfterInclusive(uint40)": FunctionFragment;
    "get(uint256)": FunctionFragment;
    "getContext()": FunctionFragment;
    "getExtraData()": FunctionFragment;
    "getLength()": FunctionFragment;
    "push(bytes32,bytes27)": FunctionFragment;
    "push(bytes32)": FunctionFragment;
    "setContext(uint40,bytes27)": FunctionFragment;
    "setExtraData(bytes27)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "deleteElementsAfterInclusive(uint40,bytes27)"
      | "deleteElementsAfterInclusive(uint40)"
      | "get"
      | "getContext"
      | "getExtraData"
      | "getLength"
      | "push(bytes32,bytes27)"
      | "push(bytes32)"
      | "setContext"
      | "setExtraData"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "deleteElementsAfterInclusive(uint40,bytes27)",
    values: [PromiseOrValue<BigNumberish>, PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "deleteElementsAfterInclusive(uint40)",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "get",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "getContext",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getExtraData",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "getLength", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "push(bytes32,bytes27)",
    values: [PromiseOrValue<BytesLike>, PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "push(bytes32)",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "setContext",
    values: [PromiseOrValue<BigNumberish>, PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "setExtraData",
    values: [PromiseOrValue<BytesLike>]
  ): string;

  decodeFunctionResult(
    functionFragment: "deleteElementsAfterInclusive(uint40,bytes27)",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "deleteElementsAfterInclusive(uint40)",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "get", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "getContext", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getExtraData",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "getLength", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "push(bytes32,bytes27)",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "push(bytes32)",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "setContext", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "setExtraData",
    data: BytesLike
  ): Result;

  events: {};
}

export interface TestLib_Buffer extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_BufferInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    "deleteElementsAfterInclusive(uint40,bytes27)"(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    "deleteElementsAfterInclusive(uint40)"(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    get(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    getContext(
      overrides?: CallOverrides
    ): Promise<[Lib_Buffer.BufferContextStructOutput]>;

    getExtraData(overrides?: CallOverrides): Promise<[string]>;

    getLength(overrides?: CallOverrides): Promise<[number]>;

    "push(bytes32,bytes27)"(
      _value: PromiseOrValue<BytesLike>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    "push(bytes32)"(
      _value: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setContext(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setExtraData(
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;
  };

  "deleteElementsAfterInclusive(uint40,bytes27)"(
    _index: PromiseOrValue<BigNumberish>,
    _extraData: PromiseOrValue<BytesLike>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  "deleteElementsAfterInclusive(uint40)"(
    _index: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  get(
    _index: PromiseOrValue<BigNumberish>,
    overrides?: CallOverrides
  ): Promise<string>;

  getContext(
    overrides?: CallOverrides
  ): Promise<Lib_Buffer.BufferContextStructOutput>;

  getExtraData(overrides?: CallOverrides): Promise<string>;

  getLength(overrides?: CallOverrides): Promise<number>;

  "push(bytes32,bytes27)"(
    _value: PromiseOrValue<BytesLike>,
    _extraData: PromiseOrValue<BytesLike>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  "push(bytes32)"(
    _value: PromiseOrValue<BytesLike>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setContext(
    _index: PromiseOrValue<BigNumberish>,
    _extraData: PromiseOrValue<BytesLike>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setExtraData(
    _extraData: PromiseOrValue<BytesLike>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  callStatic: {
    "deleteElementsAfterInclusive(uint40,bytes27)"(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<void>;

    "deleteElementsAfterInclusive(uint40)"(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    get(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<string>;

    getContext(
      overrides?: CallOverrides
    ): Promise<Lib_Buffer.BufferContextStructOutput>;

    getExtraData(overrides?: CallOverrides): Promise<string>;

    getLength(overrides?: CallOverrides): Promise<number>;

    "push(bytes32,bytes27)"(
      _value: PromiseOrValue<BytesLike>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<void>;

    "push(bytes32)"(
      _value: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<void>;

    setContext(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<void>;

    setExtraData(
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<void>;
  };

  filters: {};

  estimateGas: {
    "deleteElementsAfterInclusive(uint40,bytes27)"(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    "deleteElementsAfterInclusive(uint40)"(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    get(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getContext(overrides?: CallOverrides): Promise<BigNumber>;

    getExtraData(overrides?: CallOverrides): Promise<BigNumber>;

    getLength(overrides?: CallOverrides): Promise<BigNumber>;

    "push(bytes32,bytes27)"(
      _value: PromiseOrValue<BytesLike>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    "push(bytes32)"(
      _value: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setContext(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setExtraData(
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    "deleteElementsAfterInclusive(uint40,bytes27)"(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    "deleteElementsAfterInclusive(uint40)"(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    get(
      _index: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getContext(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getExtraData(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getLength(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    "push(bytes32,bytes27)"(
      _value: PromiseOrValue<BytesLike>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    "push(bytes32)"(
      _value: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setContext(
      _index: PromiseOrValue<BigNumberish>,
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setExtraData(
      _extraData: PromiseOrValue<BytesLike>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;
  };
}
