/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
export type { OVM_DeployerWhitelist } from "./OVM_DeployerWhitelist";
export type { OVM_ETH } from "./OVM_ETH";
export type { OVM_GasPriceOracle } from "./OVM_GasPriceOracle";
export type { OVM_L2ToL1MessagePasser } from "./OVM_L2ToL1MessagePasser";
export type { OVM_SequencerFeeVault } from "./OVM_SequencerFeeVault";
export type { WETH9 } from "./WETH9";
export type { IOVM_L1BlockNumber } from "./IOVM_L1BlockNumber";
export type { IOVM_L2ToL1MessagePasser } from "./IOVM_L2ToL1MessagePasser";
