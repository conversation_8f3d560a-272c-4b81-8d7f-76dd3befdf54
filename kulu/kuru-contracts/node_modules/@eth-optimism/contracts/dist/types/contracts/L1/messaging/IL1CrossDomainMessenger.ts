/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type {
  FunctionFragment,
  Result,
  EventFragment,
} from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export declare namespace Lib_OVMCodec {
  export type ChainBatchHeaderStruct = {
    batchIndex: PromiseOrValue<BigNumberish>;
    batchRoot: PromiseOrValue<BytesLike>;
    batchSize: PromiseOrValue<BigNumberish>;
    prevTotalElements: PromiseOrValue<BigNumberish>;
    extraData: PromiseOrValue<BytesLike>;
  };

  export type ChainBatchHeaderStructOutput = [
    BigNumber,
    string,
    BigNumber,
    BigNumber,
    string
  ] & {
    batchIndex: BigNumber;
    batchRoot: string;
    batchSize: BigNumber;
    prevTotalElements: BigNumber;
    extraData: string;
  };

  export type ChainInclusionProofStruct = {
    index: PromiseOrValue<BigNumberish>;
    siblings: PromiseOrValue<BytesLike>[];
  };

  export type ChainInclusionProofStructOutput = [BigNumber, string[]] & {
    index: BigNumber;
    siblings: string[];
  };
}

export declare namespace IL1CrossDomainMessenger {
  export type L2MessageInclusionProofStruct = {
    stateRoot: PromiseOrValue<BytesLike>;
    stateRootBatchHeader: Lib_OVMCodec.ChainBatchHeaderStruct;
    stateRootProof: Lib_OVMCodec.ChainInclusionProofStruct;
    stateTrieWitness: PromiseOrValue<BytesLike>;
    storageTrieWitness: PromiseOrValue<BytesLike>;
  };

  export type L2MessageInclusionProofStructOutput = [
    string,
    Lib_OVMCodec.ChainBatchHeaderStructOutput,
    Lib_OVMCodec.ChainInclusionProofStructOutput,
    string,
    string
  ] & {
    stateRoot: string;
    stateRootBatchHeader: Lib_OVMCodec.ChainBatchHeaderStructOutput;
    stateRootProof: Lib_OVMCodec.ChainInclusionProofStructOutput;
    stateTrieWitness: string;
    storageTrieWitness: string;
  };
}

export interface IL1CrossDomainMessengerInterface extends utils.Interface {
  functions: {
    "relayMessage(address,address,bytes,uint256,(bytes32,(uint256,bytes32,uint256,uint256,bytes),(uint256,bytes32[]),bytes,bytes))": FunctionFragment;
    "replayMessage(address,address,bytes,uint256,uint32,uint32)": FunctionFragment;
    "sendMessage(address,bytes,uint32)": FunctionFragment;
    "xDomainMessageSender()": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "relayMessage"
      | "replayMessage"
      | "sendMessage"
      | "xDomainMessageSender"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "relayMessage",
    values: [
      PromiseOrValue<string>,
      PromiseOrValue<string>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>,
      IL1CrossDomainMessenger.L2MessageInclusionProofStruct
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "replayMessage",
    values: [
      PromiseOrValue<string>,
      PromiseOrValue<string>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>,
      PromiseOrValue<BigNumberish>,
      PromiseOrValue<BigNumberish>
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "sendMessage",
    values: [
      PromiseOrValue<string>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "xDomainMessageSender",
    values?: undefined
  ): string;

  decodeFunctionResult(
    functionFragment: "relayMessage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "replayMessage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "sendMessage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "xDomainMessageSender",
    data: BytesLike
  ): Result;

  events: {
    "FailedRelayedMessage(bytes32)": EventFragment;
    "RelayedMessage(bytes32)": EventFragment;
    "SentMessage(address,address,bytes,uint256,uint256)": EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: "FailedRelayedMessage"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "RelayedMessage"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "SentMessage"): EventFragment;
}

export interface FailedRelayedMessageEventObject {
  msgHash: string;
}
export type FailedRelayedMessageEvent = TypedEvent<
  [string],
  FailedRelayedMessageEventObject
>;

export type FailedRelayedMessageEventFilter =
  TypedEventFilter<FailedRelayedMessageEvent>;

export interface RelayedMessageEventObject {
  msgHash: string;
}
export type RelayedMessageEvent = TypedEvent<
  [string],
  RelayedMessageEventObject
>;

export type RelayedMessageEventFilter = TypedEventFilter<RelayedMessageEvent>;

export interface SentMessageEventObject {
  target: string;
  sender: string;
  message: string;
  messageNonce: BigNumber;
  gasLimit: BigNumber;
}
export type SentMessageEvent = TypedEvent<
  [string, string, string, BigNumber, BigNumber],
  SentMessageEventObject
>;

export type SentMessageEventFilter = TypedEventFilter<SentMessageEvent>;

export interface IL1CrossDomainMessenger extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: IL1CrossDomainMessengerInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    relayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      _proof: IL1CrossDomainMessenger.L2MessageInclusionProofStruct,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    replayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _queueIndex: PromiseOrValue<BigNumberish>,
      _oldGasLimit: PromiseOrValue<BigNumberish>,
      _newGasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    sendMessage(
      _target: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _gasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    xDomainMessageSender(overrides?: CallOverrides): Promise<[string]>;
  };

  relayMessage(
    _target: PromiseOrValue<string>,
    _sender: PromiseOrValue<string>,
    _message: PromiseOrValue<BytesLike>,
    _messageNonce: PromiseOrValue<BigNumberish>,
    _proof: IL1CrossDomainMessenger.L2MessageInclusionProofStruct,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  replayMessage(
    _target: PromiseOrValue<string>,
    _sender: PromiseOrValue<string>,
    _message: PromiseOrValue<BytesLike>,
    _queueIndex: PromiseOrValue<BigNumberish>,
    _oldGasLimit: PromiseOrValue<BigNumberish>,
    _newGasLimit: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  sendMessage(
    _target: PromiseOrValue<string>,
    _message: PromiseOrValue<BytesLike>,
    _gasLimit: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  xDomainMessageSender(overrides?: CallOverrides): Promise<string>;

  callStatic: {
    relayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      _proof: IL1CrossDomainMessenger.L2MessageInclusionProofStruct,
      overrides?: CallOverrides
    ): Promise<void>;

    replayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _queueIndex: PromiseOrValue<BigNumberish>,
      _oldGasLimit: PromiseOrValue<BigNumberish>,
      _newGasLimit: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    sendMessage(
      _target: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _gasLimit: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    xDomainMessageSender(overrides?: CallOverrides): Promise<string>;
  };

  filters: {
    "FailedRelayedMessage(bytes32)"(
      msgHash?: PromiseOrValue<BytesLike> | null
    ): FailedRelayedMessageEventFilter;
    FailedRelayedMessage(
      msgHash?: PromiseOrValue<BytesLike> | null
    ): FailedRelayedMessageEventFilter;

    "RelayedMessage(bytes32)"(
      msgHash?: PromiseOrValue<BytesLike> | null
    ): RelayedMessageEventFilter;
    RelayedMessage(
      msgHash?: PromiseOrValue<BytesLike> | null
    ): RelayedMessageEventFilter;

    "SentMessage(address,address,bytes,uint256,uint256)"(
      target?: PromiseOrValue<string> | null,
      sender?: null,
      message?: null,
      messageNonce?: null,
      gasLimit?: null
    ): SentMessageEventFilter;
    SentMessage(
      target?: PromiseOrValue<string> | null,
      sender?: null,
      message?: null,
      messageNonce?: null,
      gasLimit?: null
    ): SentMessageEventFilter;
  };

  estimateGas: {
    relayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      _proof: IL1CrossDomainMessenger.L2MessageInclusionProofStruct,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    replayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _queueIndex: PromiseOrValue<BigNumberish>,
      _oldGasLimit: PromiseOrValue<BigNumberish>,
      _newGasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    sendMessage(
      _target: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _gasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    xDomainMessageSender(overrides?: CallOverrides): Promise<BigNumber>;
  };

  populateTransaction: {
    relayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      _proof: IL1CrossDomainMessenger.L2MessageInclusionProofStruct,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    replayMessage(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _queueIndex: PromiseOrValue<BigNumberish>,
      _oldGasLimit: PromiseOrValue<BigNumberish>,
      _newGasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    sendMessage(
      _target: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _gasLimit: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    xDomainMessageSender(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
