/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_RLPWriterInterface extends utils.Interface {
  functions: {
    "writeAddress(address)": FunctionFragment;
    "writeAddressWithTaintedMemory(address)": FunctionFragment;
    "writeBool(bool)": FunctionFragment;
    "writeBytes(bytes)": FunctionFragment;
    "writeList(bytes[])": FunctionFragment;
    "writeString(string)": FunctionFragment;
    "writeUint(uint256)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "writeAddress"
      | "writeAddressWithTaintedMemory"
      | "writeBool"
      | "writeBytes"
      | "writeList"
      | "writeString"
      | "writeUint"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "writeAddress",
    values: [PromiseOrValue<string>]
  ): string;
  encodeFunctionData(
    functionFragment: "writeAddressWithTaintedMemory",
    values: [PromiseOrValue<string>]
  ): string;
  encodeFunctionData(
    functionFragment: "writeBool",
    values: [PromiseOrValue<boolean>]
  ): string;
  encodeFunctionData(
    functionFragment: "writeBytes",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "writeList",
    values: [PromiseOrValue<BytesLike>[]]
  ): string;
  encodeFunctionData(
    functionFragment: "writeString",
    values: [PromiseOrValue<string>]
  ): string;
  encodeFunctionData(
    functionFragment: "writeUint",
    values: [PromiseOrValue<BigNumberish>]
  ): string;

  decodeFunctionResult(
    functionFragment: "writeAddress",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "writeAddressWithTaintedMemory",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "writeBool", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "writeBytes", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "writeList", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "writeString",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "writeUint", data: BytesLike): Result;

  events: {};
}

export interface TestLib_RLPWriter extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_RLPWriterInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    writeAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    writeAddressWithTaintedMemory(
      _in: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    writeBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    writeBytes(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    writeList(
      _in: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    writeString(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    writeUint(
      _in: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;
  };

  writeAddress(
    _in: PromiseOrValue<string>,
    overrides?: CallOverrides
  ): Promise<string>;

  writeAddressWithTaintedMemory(
    _in: PromiseOrValue<string>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  writeBool(
    _in: PromiseOrValue<boolean>,
    overrides?: CallOverrides
  ): Promise<string>;

  writeBytes(
    _in: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  writeList(
    _in: PromiseOrValue<BytesLike>[],
    overrides?: CallOverrides
  ): Promise<string>;

  writeString(
    _in: PromiseOrValue<string>,
    overrides?: CallOverrides
  ): Promise<string>;

  writeUint(
    _in: PromiseOrValue<BigNumberish>,
    overrides?: CallOverrides
  ): Promise<string>;

  callStatic: {
    writeAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<string>;

    writeAddressWithTaintedMemory(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<string>;

    writeBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<string>;

    writeBytes(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    writeList(
      _in: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<string>;

    writeString(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<string>;

    writeUint(
      _in: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<string>;
  };

  filters: {};

  estimateGas: {
    writeAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    writeAddressWithTaintedMemory(
      _in: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    writeBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    writeBytes(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    writeList(
      _in: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    writeString(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    writeUint(
      _in: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    writeAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    writeAddressWithTaintedMemory(
      _in: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    writeBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    writeBytes(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    writeList(
      _in: PromiseOrValue<BytesLike>[],
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    writeString(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    writeUint(
      _in: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
