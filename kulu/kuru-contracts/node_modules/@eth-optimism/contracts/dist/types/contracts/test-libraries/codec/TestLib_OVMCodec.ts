/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export declare namespace Lib_OVMCodec {
  export type TransactionStruct = {
    timestamp: PromiseOrValue<BigNumberish>;
    blockNumber: PromiseOrValue<BigNumberish>;
    l1QueueOrigin: PromiseOrValue<BigNumberish>;
    l1TxOrigin: PromiseOrValue<string>;
    entrypoint: PromiseOrValue<string>;
    gasLimit: PromiseOrValue<BigNumberish>;
    data: PromiseOrValue<BytesLike>;
  };

  export type TransactionStructOutput = [
    BigNumber,
    BigNumber,
    number,
    string,
    string,
    BigNumber,
    string
  ] & {
    timestamp: BigNumber;
    blockNumber: BigNumber;
    l1QueueOrigin: number;
    l1TxOrigin: string;
    entrypoint: string;
    gasLimit: BigNumber;
    data: string;
  };
}

export interface TestLib_OVMCodecInterface extends utils.Interface {
  functions: {
    "encodeTransaction((uint256,uint256,uint8,address,address,uint256,bytes))": FunctionFragment;
    "hashTransaction((uint256,uint256,uint8,address,address,uint256,bytes))": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic: "encodeTransaction" | "hashTransaction"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "encodeTransaction",
    values: [Lib_OVMCodec.TransactionStruct]
  ): string;
  encodeFunctionData(
    functionFragment: "hashTransaction",
    values: [Lib_OVMCodec.TransactionStruct]
  ): string;

  decodeFunctionResult(
    functionFragment: "encodeTransaction",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "hashTransaction",
    data: BytesLike
  ): Result;

  events: {};
}

export interface TestLib_OVMCodec extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_OVMCodecInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    encodeTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<[string] & { _encoded: string }>;

    hashTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<[string] & { _hash: string }>;
  };

  encodeTransaction(
    _transaction: Lib_OVMCodec.TransactionStruct,
    overrides?: CallOverrides
  ): Promise<string>;

  hashTransaction(
    _transaction: Lib_OVMCodec.TransactionStruct,
    overrides?: CallOverrides
  ): Promise<string>;

  callStatic: {
    encodeTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<string>;

    hashTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<string>;
  };

  filters: {};

  estimateGas: {
    encodeTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    hashTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    encodeTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    hashTransaction(
      _transaction: Lib_OVMCodec.TransactionStruct,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
