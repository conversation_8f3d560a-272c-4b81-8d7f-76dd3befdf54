/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export declare namespace AddressDictator {
  export type NamedAddressStruct = {
    name: PromiseOrValue<string>;
    addr: PromiseOrValue<string>;
  };

  export type NamedAddressStructOutput = [string, string] & {
    name: string;
    addr: string;
  };
}

export interface AddressDictatorInterface extends utils.Interface {
  functions: {
    "finalOwner()": FunctionFragment;
    "getNamedAddresses()": FunctionFragment;
    "manager()": FunctionFragment;
    "returnOwnership()": FunctionFragment;
    "setAddresses()": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "finalOwner"
      | "getNamedAddresses"
      | "manager"
      | "returnOwnership"
      | "setAddresses"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "finalOwner",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getNamedAddresses",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "manager", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "returnOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "setAddresses",
    values?: undefined
  ): string;

  decodeFunctionResult(functionFragment: "finalOwner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getNamedAddresses",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "manager", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "returnOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setAddresses",
    data: BytesLike
  ): Result;

  events: {};
}

export interface AddressDictator extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: AddressDictatorInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    finalOwner(overrides?: CallOverrides): Promise<[string]>;

    getNamedAddresses(
      overrides?: CallOverrides
    ): Promise<[AddressDictator.NamedAddressStructOutput[]]>;

    manager(overrides?: CallOverrides): Promise<[string]>;

    returnOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setAddresses(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;
  };

  finalOwner(overrides?: CallOverrides): Promise<string>;

  getNamedAddresses(
    overrides?: CallOverrides
  ): Promise<AddressDictator.NamedAddressStructOutput[]>;

  manager(overrides?: CallOverrides): Promise<string>;

  returnOwnership(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setAddresses(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  callStatic: {
    finalOwner(overrides?: CallOverrides): Promise<string>;

    getNamedAddresses(
      overrides?: CallOverrides
    ): Promise<AddressDictator.NamedAddressStructOutput[]>;

    manager(overrides?: CallOverrides): Promise<string>;

    returnOwnership(overrides?: CallOverrides): Promise<void>;

    setAddresses(overrides?: CallOverrides): Promise<void>;
  };

  filters: {};

  estimateGas: {
    finalOwner(overrides?: CallOverrides): Promise<BigNumber>;

    getNamedAddresses(overrides?: CallOverrides): Promise<BigNumber>;

    manager(overrides?: CallOverrides): Promise<BigNumber>;

    returnOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setAddresses(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    finalOwner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getNamedAddresses(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    manager(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    returnOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setAddresses(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;
  };
}
