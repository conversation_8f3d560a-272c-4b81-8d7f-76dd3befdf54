/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface IL1ChugSplashDeployerInterface extends utils.Interface {
  functions: {
    "isUpgrading()": FunctionFragment;
  };

  getFunction(nameOrSignatureOrTopic: "isUpgrading"): FunctionFragment;

  encodeFunctionData(
    functionFragment: "isUpgrading",
    values?: undefined
  ): string;

  decodeFunctionResult(
    functionFragment: "isUpgrading",
    data: BytesLike
  ): Result;

  events: {};
}

export interface IL1ChugSplashDeployer extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: IL1ChugSplashDeployerInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    isUpgrading(overrides?: CallOverrides): Promise<[boolean]>;
  };

  isUpgrading(overrides?: CallOverrides): Promise<boolean>;

  callStatic: {
    isUpgrading(overrides?: CallOverrides): Promise<boolean>;
  };

  filters: {};

  estimateGas: {
    isUpgrading(overrides?: CallOverrides): Promise<BigNumber>;
  };

  populateTransaction: {
    isUpgrading(overrides?: CallOverrides): Promise<PopulatedTransaction>;
  };
}
