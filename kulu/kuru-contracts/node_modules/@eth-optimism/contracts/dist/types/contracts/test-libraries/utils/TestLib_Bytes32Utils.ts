/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_Bytes32UtilsInterface extends utils.Interface {
  functions: {
    "fromAddress(address)": FunctionFragment;
    "fromBool(bool)": FunctionFragment;
    "toAddress(bytes32)": FunctionFragment;
    "toBool(bytes32)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic: "fromAddress" | "fromBool" | "toAddress" | "toBool"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "fromAddress",
    values: [PromiseOrValue<string>]
  ): string;
  encodeFunctionData(
    functionFragment: "fromBool",
    values: [PromiseOrValue<boolean>]
  ): string;
  encodeFunctionData(
    functionFragment: "toAddress",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "toBool",
    values: [PromiseOrValue<BytesLike>]
  ): string;

  decodeFunctionResult(
    functionFragment: "fromAddress",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "fromBool", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "toAddress", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "toBool", data: BytesLike): Result;

  events: {};
}

export interface TestLib_Bytes32Utils extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_Bytes32UtilsInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    fromAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    fromBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    toAddress(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string] & { _out: string }>;

    toBool(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[boolean] & { _out: boolean }>;
  };

  fromAddress(
    _in: PromiseOrValue<string>,
    overrides?: CallOverrides
  ): Promise<string>;

  fromBool(
    _in: PromiseOrValue<boolean>,
    overrides?: CallOverrides
  ): Promise<string>;

  toAddress(
    _in: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  toBool(
    _in: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<boolean>;

  callStatic: {
    fromAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<string>;

    fromBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<string>;

    toAddress(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    toBool(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<boolean>;
  };

  filters: {};

  estimateGas: {
    fromAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    fromBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    toAddress(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    toBool(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    fromAddress(
      _in: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    fromBool(
      _in: PromiseOrValue<boolean>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    toAddress(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    toBool(
      _in: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
