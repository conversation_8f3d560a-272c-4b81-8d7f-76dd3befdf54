/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_BytesUtilsInterface extends utils.Interface {
  functions: {
    "concat(bytes,bytes)": FunctionFragment;
    "equal(bytes,bytes)": FunctionFragment;
    "fromNibbles(bytes)": FunctionFragment;
    "slice(bytes,uint256,uint256)": FunctionFragment;
    "sliceWithTaintedMemory(bytes,uint256,uint256)": FunctionFragment;
    "toBytes32(bytes)": FunctionFragment;
    "toNibbles(bytes)": FunctionFragment;
    "toUint256(bytes)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "concat"
      | "equal"
      | "fromNibbles"
      | "slice"
      | "sliceWithTaintedMemory"
      | "toBytes32"
      | "toNibbles"
      | "toUint256"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "concat",
    values: [PromiseOrValue<BytesLike>, PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "equal",
    values: [PromiseOrValue<BytesLike>, PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "fromNibbles",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "slice",
    values: [
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>,
      PromiseOrValue<BigNumberish>
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "sliceWithTaintedMemory",
    values: [
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>,
      PromiseOrValue<BigNumberish>
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "toBytes32",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "toNibbles",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "toUint256",
    values: [PromiseOrValue<BytesLike>]
  ): string;

  decodeFunctionResult(functionFragment: "concat", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "equal", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "fromNibbles",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "slice", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "sliceWithTaintedMemory",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "toBytes32", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "toNibbles", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "toUint256", data: BytesLike): Result;

  events: {};
}

export interface TestLib_BytesUtils extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_BytesUtilsInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    concat(
      _preBytes: PromiseOrValue<BytesLike>,
      _postBytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    equal(
      _bytes: PromiseOrValue<BytesLike>,
      _other: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    fromNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    slice(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    sliceWithTaintedMemory(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    toBytes32(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    toNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[string]>;

    toUint256(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;
  };

  concat(
    _preBytes: PromiseOrValue<BytesLike>,
    _postBytes: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  equal(
    _bytes: PromiseOrValue<BytesLike>,
    _other: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<boolean>;

  fromNibbles(
    _bytes: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  slice(
    _bytes: PromiseOrValue<BytesLike>,
    _start: PromiseOrValue<BigNumberish>,
    _length: PromiseOrValue<BigNumberish>,
    overrides?: CallOverrides
  ): Promise<string>;

  sliceWithTaintedMemory(
    _bytes: PromiseOrValue<BytesLike>,
    _start: PromiseOrValue<BigNumberish>,
    _length: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  toBytes32(
    _bytes: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  toNibbles(
    _bytes: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<string>;

  toUint256(
    _bytes: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  callStatic: {
    concat(
      _preBytes: PromiseOrValue<BytesLike>,
      _postBytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    equal(
      _bytes: PromiseOrValue<BytesLike>,
      _other: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<boolean>;

    fromNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    slice(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<string>;

    sliceWithTaintedMemory(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<string>;

    toBytes32(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    toNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<string>;

    toUint256(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  filters: {};

  estimateGas: {
    concat(
      _preBytes: PromiseOrValue<BytesLike>,
      _postBytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    equal(
      _bytes: PromiseOrValue<BytesLike>,
      _other: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    fromNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    slice(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    sliceWithTaintedMemory(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    toBytes32(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    toNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    toUint256(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    concat(
      _preBytes: PromiseOrValue<BytesLike>,
      _postBytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    equal(
      _bytes: PromiseOrValue<BytesLike>,
      _other: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    fromNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    slice(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    sliceWithTaintedMemory(
      _bytes: PromiseOrValue<BytesLike>,
      _start: PromiseOrValue<BigNumberish>,
      _length: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    toBytes32(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    toNibbles(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    toUint256(
      _bytes: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
