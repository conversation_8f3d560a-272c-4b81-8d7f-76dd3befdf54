/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_MerkleTrieInterface extends utils.Interface {
  functions: {
    "get(bytes,bytes,bytes32)": FunctionFragment;
    "verifyInclusionProof(bytes,bytes,bytes,bytes32)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic: "get" | "verifyInclusionProof"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "get",
    values: [
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "verifyInclusionProof",
    values: [
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BytesLike>
    ]
  ): string;

  decodeFunctionResult(functionFragment: "get", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "verifyInclusionProof",
    data: BytesLike
  ): Result;

  events: {};
}

export interface TestLib_MerkleTrie extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_MerkleTrieInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    get(
      _key: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[boolean, string]>;

    verifyInclusionProof(
      _key: PromiseOrValue<BytesLike>,
      _value: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[boolean]>;
  };

  get(
    _key: PromiseOrValue<BytesLike>,
    _proof: PromiseOrValue<BytesLike>,
    _root: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<[boolean, string]>;

  verifyInclusionProof(
    _key: PromiseOrValue<BytesLike>,
    _value: PromiseOrValue<BytesLike>,
    _proof: PromiseOrValue<BytesLike>,
    _root: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<boolean>;

  callStatic: {
    get(
      _key: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[boolean, string]>;

    verifyInclusionProof(
      _key: PromiseOrValue<BytesLike>,
      _value: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<boolean>;
  };

  filters: {};

  estimateGas: {
    get(
      _key: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    verifyInclusionProof(
      _key: PromiseOrValue<BytesLike>,
      _value: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    get(
      _key: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    verifyInclusionProof(
      _key: PromiseOrValue<BytesLike>,
      _value: PromiseOrValue<BytesLike>,
      _proof: PromiseOrValue<BytesLike>,
      _root: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
