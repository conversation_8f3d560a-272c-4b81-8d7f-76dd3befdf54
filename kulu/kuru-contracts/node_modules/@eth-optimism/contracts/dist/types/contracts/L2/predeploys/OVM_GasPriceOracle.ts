/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type {
  FunctionFragment,
  Result,
  EventFragment,
} from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface OVM_GasPriceOracleInterface extends utils.Interface {
  functions: {
    "decimals()": FunctionFragment;
    "gasPrice()": FunctionFragment;
    "getL1Fee(bytes)": FunctionFragment;
    "getL1GasUsed(bytes)": FunctionFragment;
    "l1BaseFee()": FunctionFragment;
    "overhead()": FunctionFragment;
    "owner()": FunctionFragment;
    "renounceOwnership()": FunctionFragment;
    "scalar()": FunctionFragment;
    "setDecimals(uint256)": FunctionFragment;
    "setGasPrice(uint256)": FunctionFragment;
    "setL1BaseFee(uint256)": FunctionFragment;
    "setOverhead(uint256)": FunctionFragment;
    "setScalar(uint256)": FunctionFragment;
    "transferOwnership(address)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | "decimals"
      | "gasPrice"
      | "getL1Fee"
      | "getL1GasUsed"
      | "l1BaseFee"
      | "overhead"
      | "owner"
      | "renounceOwnership"
      | "scalar"
      | "setDecimals"
      | "setGasPrice"
      | "setL1BaseFee"
      | "setOverhead"
      | "setScalar"
      | "transferOwnership"
  ): FunctionFragment;

  encodeFunctionData(functionFragment: "decimals", values?: undefined): string;
  encodeFunctionData(functionFragment: "gasPrice", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "getL1Fee",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(
    functionFragment: "getL1GasUsed",
    values: [PromiseOrValue<BytesLike>]
  ): string;
  encodeFunctionData(functionFragment: "l1BaseFee", values?: undefined): string;
  encodeFunctionData(functionFragment: "overhead", values?: undefined): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "scalar", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "setDecimals",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "setGasPrice",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "setL1BaseFee",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "setOverhead",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "setScalar",
    values: [PromiseOrValue<BigNumberish>]
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [PromiseOrValue<string>]
  ): string;

  decodeFunctionResult(functionFragment: "decimals", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "gasPrice", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "getL1Fee", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getL1GasUsed",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "l1BaseFee", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "overhead", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "scalar", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "setDecimals",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setGasPrice",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setL1BaseFee",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setOverhead",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "setScalar", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;

  events: {
    "DecimalsUpdated(uint256)": EventFragment;
    "GasPriceUpdated(uint256)": EventFragment;
    "L1BaseFeeUpdated(uint256)": EventFragment;
    "OverheadUpdated(uint256)": EventFragment;
    "OwnershipTransferred(address,address)": EventFragment;
    "ScalarUpdated(uint256)": EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: "DecimalsUpdated"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "GasPriceUpdated"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "L1BaseFeeUpdated"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "OverheadUpdated"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "OwnershipTransferred"): EventFragment;
  getEvent(nameOrSignatureOrTopic: "ScalarUpdated"): EventFragment;
}

export interface DecimalsUpdatedEventObject {
  arg0: BigNumber;
}
export type DecimalsUpdatedEvent = TypedEvent<
  [BigNumber],
  DecimalsUpdatedEventObject
>;

export type DecimalsUpdatedEventFilter = TypedEventFilter<DecimalsUpdatedEvent>;

export interface GasPriceUpdatedEventObject {
  arg0: BigNumber;
}
export type GasPriceUpdatedEvent = TypedEvent<
  [BigNumber],
  GasPriceUpdatedEventObject
>;

export type GasPriceUpdatedEventFilter = TypedEventFilter<GasPriceUpdatedEvent>;

export interface L1BaseFeeUpdatedEventObject {
  arg0: BigNumber;
}
export type L1BaseFeeUpdatedEvent = TypedEvent<
  [BigNumber],
  L1BaseFeeUpdatedEventObject
>;

export type L1BaseFeeUpdatedEventFilter =
  TypedEventFilter<L1BaseFeeUpdatedEvent>;

export interface OverheadUpdatedEventObject {
  arg0: BigNumber;
}
export type OverheadUpdatedEvent = TypedEvent<
  [BigNumber],
  OverheadUpdatedEventObject
>;

export type OverheadUpdatedEventFilter = TypedEventFilter<OverheadUpdatedEvent>;

export interface OwnershipTransferredEventObject {
  previousOwner: string;
  newOwner: string;
}
export type OwnershipTransferredEvent = TypedEvent<
  [string, string],
  OwnershipTransferredEventObject
>;

export type OwnershipTransferredEventFilter =
  TypedEventFilter<OwnershipTransferredEvent>;

export interface ScalarUpdatedEventObject {
  arg0: BigNumber;
}
export type ScalarUpdatedEvent = TypedEvent<
  [BigNumber],
  ScalarUpdatedEventObject
>;

export type ScalarUpdatedEventFilter = TypedEventFilter<ScalarUpdatedEvent>;

export interface OVM_GasPriceOracle extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: OVM_GasPriceOracleInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    decimals(overrides?: CallOverrides): Promise<[BigNumber]>;

    gasPrice(overrides?: CallOverrides): Promise<[BigNumber]>;

    getL1Fee(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    getL1GasUsed(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    l1BaseFee(overrides?: CallOverrides): Promise<[BigNumber]>;

    overhead(overrides?: CallOverrides): Promise<[BigNumber]>;

    owner(overrides?: CallOverrides): Promise<[string]>;

    renounceOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    scalar(overrides?: CallOverrides): Promise<[BigNumber]>;

    setDecimals(
      _decimals: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setGasPrice(
      _gasPrice: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setL1BaseFee(
      _baseFee: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setOverhead(
      _overhead: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    setScalar(
      _scalar: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;

    transferOwnership(
      newOwner: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<ContractTransaction>;
  };

  decimals(overrides?: CallOverrides): Promise<BigNumber>;

  gasPrice(overrides?: CallOverrides): Promise<BigNumber>;

  getL1Fee(
    _data: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  getL1GasUsed(
    _data: PromiseOrValue<BytesLike>,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  l1BaseFee(overrides?: CallOverrides): Promise<BigNumber>;

  overhead(overrides?: CallOverrides): Promise<BigNumber>;

  owner(overrides?: CallOverrides): Promise<string>;

  renounceOwnership(
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  scalar(overrides?: CallOverrides): Promise<BigNumber>;

  setDecimals(
    _decimals: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setGasPrice(
    _gasPrice: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setL1BaseFee(
    _baseFee: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setOverhead(
    _overhead: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  setScalar(
    _scalar: PromiseOrValue<BigNumberish>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  transferOwnership(
    newOwner: PromiseOrValue<string>,
    overrides?: Overrides & { from?: PromiseOrValue<string> }
  ): Promise<ContractTransaction>;

  callStatic: {
    decimals(overrides?: CallOverrides): Promise<BigNumber>;

    gasPrice(overrides?: CallOverrides): Promise<BigNumber>;

    getL1Fee(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getL1GasUsed(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    l1BaseFee(overrides?: CallOverrides): Promise<BigNumber>;

    overhead(overrides?: CallOverrides): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<string>;

    renounceOwnership(overrides?: CallOverrides): Promise<void>;

    scalar(overrides?: CallOverrides): Promise<BigNumber>;

    setDecimals(
      _decimals: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    setGasPrice(
      _gasPrice: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    setL1BaseFee(
      _baseFee: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    setOverhead(
      _overhead: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    setScalar(
      _scalar: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<void>;

    transferOwnership(
      newOwner: PromiseOrValue<string>,
      overrides?: CallOverrides
    ): Promise<void>;
  };

  filters: {
    "DecimalsUpdated(uint256)"(arg0?: null): DecimalsUpdatedEventFilter;
    DecimalsUpdated(arg0?: null): DecimalsUpdatedEventFilter;

    "GasPriceUpdated(uint256)"(arg0?: null): GasPriceUpdatedEventFilter;
    GasPriceUpdated(arg0?: null): GasPriceUpdatedEventFilter;

    "L1BaseFeeUpdated(uint256)"(arg0?: null): L1BaseFeeUpdatedEventFilter;
    L1BaseFeeUpdated(arg0?: null): L1BaseFeeUpdatedEventFilter;

    "OverheadUpdated(uint256)"(arg0?: null): OverheadUpdatedEventFilter;
    OverheadUpdated(arg0?: null): OverheadUpdatedEventFilter;

    "OwnershipTransferred(address,address)"(
      previousOwner?: PromiseOrValue<string> | null,
      newOwner?: PromiseOrValue<string> | null
    ): OwnershipTransferredEventFilter;
    OwnershipTransferred(
      previousOwner?: PromiseOrValue<string> | null,
      newOwner?: PromiseOrValue<string> | null
    ): OwnershipTransferredEventFilter;

    "ScalarUpdated(uint256)"(arg0?: null): ScalarUpdatedEventFilter;
    ScalarUpdated(arg0?: null): ScalarUpdatedEventFilter;
  };

  estimateGas: {
    decimals(overrides?: CallOverrides): Promise<BigNumber>;

    gasPrice(overrides?: CallOverrides): Promise<BigNumber>;

    getL1Fee(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getL1GasUsed(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    l1BaseFee(overrides?: CallOverrides): Promise<BigNumber>;

    overhead(overrides?: CallOverrides): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    renounceOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    scalar(overrides?: CallOverrides): Promise<BigNumber>;

    setDecimals(
      _decimals: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setGasPrice(
      _gasPrice: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setL1BaseFee(
      _baseFee: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setOverhead(
      _overhead: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    setScalar(
      _scalar: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;

    transferOwnership(
      newOwner: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    decimals(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    gasPrice(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getL1Fee(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getL1GasUsed(
      _data: PromiseOrValue<BytesLike>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    l1BaseFee(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    overhead(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    renounceOwnership(
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    scalar(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    setDecimals(
      _decimals: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setGasPrice(
      _gasPrice: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setL1BaseFee(
      _baseFee: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setOverhead(
      _overhead: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    setScalar(
      _scalar: PromiseOrValue<BigNumberish>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;

    transferOwnership(
      newOwner: PromiseOrValue<string>,
      overrides?: Overrides & { from?: PromiseOrValue<string> }
    ): Promise<PopulatedTransaction>;
  };
}
