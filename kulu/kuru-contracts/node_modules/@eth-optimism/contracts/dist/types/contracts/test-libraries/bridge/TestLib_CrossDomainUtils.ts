/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from "ethers";
import type { FunctionFragment, Result } from "@ethersproject/abi";
import type { Listener, Provider } from "@ethersproject/providers";
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
  PromiseOrValue,
} from "../../../common";

export interface TestLib_CrossDomainUtilsInterface extends utils.Interface {
  functions: {
    "encodeXDomainCalldata(address,address,bytes,uint256)": FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic: "encodeXDomainCalldata"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "encodeXDomainCalldata",
    values: [
      PromiseOrValue<string>,
      PromiseOrValue<string>,
      PromiseOrValue<BytesLike>,
      PromiseOrValue<BigNumberish>
    ]
  ): string;

  decodeFunctionResult(
    functionFragment: "encodeXDomainCalldata",
    data: BytesLike
  ): Result;

  events: {};
}

export interface TestLib_CrossDomainUtils extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: TestLib_CrossDomainUtilsInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    encodeXDomainCalldata(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<[string]>;
  };

  encodeXDomainCalldata(
    _target: PromiseOrValue<string>,
    _sender: PromiseOrValue<string>,
    _message: PromiseOrValue<BytesLike>,
    _messageNonce: PromiseOrValue<BigNumberish>,
    overrides?: CallOverrides
  ): Promise<string>;

  callStatic: {
    encodeXDomainCalldata(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<string>;
  };

  filters: {};

  estimateGas: {
    encodeXDomainCalldata(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    encodeXDomainCalldata(
      _target: PromiseOrValue<string>,
      _sender: PromiseOrValue<string>,
      _message: PromiseOrValue<BytesLike>,
      _messageNonce: PromiseOrValue<BigNumberish>,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
