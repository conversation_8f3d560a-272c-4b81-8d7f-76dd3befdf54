/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { ethers } from "ethers";
import {
  FactoryOptions,
  HardhatEthersHelpers as HardhatEthersHelpersBase,
} from "@nomiclabs/hardhat-ethers/types";

import * as Contracts from ".";

declare module "hardhat/types/runtime" {
  interface HardhatEthersHelpers extends HardhatEthersHelpersBase {
    getContractFactory(
      name: "OwnableUpgradeable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OwnableUpgradeable__factory>;
    getContractFactory(
      name: "Initializable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Initializable__factory>;
    getContractFactory(
      name: "PausableUpgradeable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.PausableUpgradeable__factory>;
    getContractFactory(
      name: "ReentrancyGuardUpgradeable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ReentrancyGuardUpgradeable__factory>;
    getContractFactory(
      name: "ContextUpgradeable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ContextUpgradeable__factory>;
    getContractFactory(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Ownable__factory>;
    getContractFactory(
      name: "ERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ERC20__factory>;
    getContractFactory(
      name: "IERC20Metadata",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20Metadata__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "IERC165",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC165__factory>;
    getContractFactory(
      name: "IL1ChugSplashDeployer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL1ChugSplashDeployer__factory>;
    getContractFactory(
      name: "L1ChugSplashProxy",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L1ChugSplashProxy__factory>;
    getContractFactory(
      name: "AddressDictator",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.AddressDictator__factory>;
    getContractFactory(
      name: "ChugSplashDictator",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ChugSplashDictator__factory>;
    getContractFactory(
      name: "IL1CrossDomainMessenger",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL1CrossDomainMessenger__factory>;
    getContractFactory(
      name: "IL1ERC20Bridge",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL1ERC20Bridge__factory>;
    getContractFactory(
      name: "IL1StandardBridge",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL1StandardBridge__factory>;
    getContractFactory(
      name: "L1CrossDomainMessenger",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L1CrossDomainMessenger__factory>;
    getContractFactory(
      name: "L1StandardBridge",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L1StandardBridge__factory>;
    getContractFactory(
      name: "CanonicalTransactionChain",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.CanonicalTransactionChain__factory>;
    getContractFactory(
      name: "ChainStorageContainer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ChainStorageContainer__factory>;
    getContractFactory(
      name: "ICanonicalTransactionChain",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ICanonicalTransactionChain__factory>;
    getContractFactory(
      name: "IChainStorageContainer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IChainStorageContainer__factory>;
    getContractFactory(
      name: "IStateCommitmentChain",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IStateCommitmentChain__factory>;
    getContractFactory(
      name: "StateCommitmentChain",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.StateCommitmentChain__factory>;
    getContractFactory(
      name: "BondManager",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.BondManager__factory>;
    getContractFactory(
      name: "IBondManager",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBondManager__factory>;
    getContractFactory(
      name: "IL2CrossDomainMessenger",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL2CrossDomainMessenger__factory>;
    getContractFactory(
      name: "IL2ERC20Bridge",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL2ERC20Bridge__factory>;
    getContractFactory(
      name: "L2CrossDomainMessenger",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L2CrossDomainMessenger__factory>;
    getContractFactory(
      name: "L2StandardBridge",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L2StandardBridge__factory>;
    getContractFactory(
      name: "L2StandardTokenFactory",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L2StandardTokenFactory__factory>;
    getContractFactory(
      name: "IOVM_L1BlockNumber",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IOVM_L1BlockNumber__factory>;
    getContractFactory(
      name: "IOVM_L2ToL1MessagePasser",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IOVM_L2ToL1MessagePasser__factory>;
    getContractFactory(
      name: "OVM_DeployerWhitelist",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OVM_DeployerWhitelist__factory>;
    getContractFactory(
      name: "OVM_ETH",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OVM_ETH__factory>;
    getContractFactory(
      name: "OVM_GasPriceOracle",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OVM_GasPriceOracle__factory>;
    getContractFactory(
      name: "OVM_L2ToL1MessagePasser",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OVM_L2ToL1MessagePasser__factory>;
    getContractFactory(
      name: "OVM_SequencerFeeVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.OVM_SequencerFeeVault__factory>;
    getContractFactory(
      name: "WETH9",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.WETH9__factory>;
    getContractFactory(
      name: "CrossDomainEnabled",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.CrossDomainEnabled__factory>;
    getContractFactory(
      name: "ICrossDomainMessenger",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ICrossDomainMessenger__factory>;
    getContractFactory(
      name: "Lib_AddressManager",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Lib_AddressManager__factory>;
    getContractFactory(
      name: "Lib_AddressResolver",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Lib_AddressResolver__factory>;
    getContractFactory(
      name: "Lib_ResolvedDelegateProxy",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Lib_ResolvedDelegateProxy__factory>;
    getContractFactory(
      name: "IL2StandardERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IL2StandardERC20__factory>;
    getContractFactory(
      name: "L2StandardERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.L2StandardERC20__factory>;
    getContractFactory(
      name: "FailingReceiver",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FailingReceiver__factory>;
    getContractFactory(
      name: "TestERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestERC20__factory>;
    getContractFactory(
      name: "TestLib_CrossDomainUtils",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_CrossDomainUtils__factory>;
    getContractFactory(
      name: "TestLib_OVMCodec",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_OVMCodec__factory>;
    getContractFactory(
      name: "TestLib_RLPReader",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_RLPReader__factory>;
    getContractFactory(
      name: "TestLib_RLPWriter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_RLPWriter__factory>;
    getContractFactory(
      name: "TestLib_AddressAliasHelper",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_AddressAliasHelper__factory>;
    getContractFactory(
      name: "TestLib_MerkleTrie",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_MerkleTrie__factory>;
    getContractFactory(
      name: "TestLib_SecureMerkleTrie",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_SecureMerkleTrie__factory>;
    getContractFactory(
      name: "TestLib_Buffer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_Buffer__factory>;
    getContractFactory(
      name: "TestLib_Bytes32Utils",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_Bytes32Utils__factory>;
    getContractFactory(
      name: "TestLib_BytesUtils",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_BytesUtils__factory>;
    getContractFactory(
      name: "TestLib_MerkleTree",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestLib_MerkleTree__factory>;

    getContractAt(
      name: "OwnableUpgradeable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OwnableUpgradeable>;
    getContractAt(
      name: "Initializable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.Initializable>;
    getContractAt(
      name: "PausableUpgradeable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.PausableUpgradeable>;
    getContractAt(
      name: "ReentrancyGuardUpgradeable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ReentrancyGuardUpgradeable>;
    getContractAt(
      name: "ContextUpgradeable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ContextUpgradeable>;
    getContractAt(
      name: "Ownable",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.Ownable>;
    getContractAt(
      name: "ERC20",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ERC20>;
    getContractAt(
      name: "IERC20Metadata",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20Metadata>;
    getContractAt(
      name: "IERC20",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "IERC165",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC165>;
    getContractAt(
      name: "IL1ChugSplashDeployer",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL1ChugSplashDeployer>;
    getContractAt(
      name: "L1ChugSplashProxy",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L1ChugSplashProxy>;
    getContractAt(
      name: "AddressDictator",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.AddressDictator>;
    getContractAt(
      name: "ChugSplashDictator",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ChugSplashDictator>;
    getContractAt(
      name: "IL1CrossDomainMessenger",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL1CrossDomainMessenger>;
    getContractAt(
      name: "IL1ERC20Bridge",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL1ERC20Bridge>;
    getContractAt(
      name: "IL1StandardBridge",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL1StandardBridge>;
    getContractAt(
      name: "L1CrossDomainMessenger",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L1CrossDomainMessenger>;
    getContractAt(
      name: "L1StandardBridge",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L1StandardBridge>;
    getContractAt(
      name: "CanonicalTransactionChain",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.CanonicalTransactionChain>;
    getContractAt(
      name: "ChainStorageContainer",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ChainStorageContainer>;
    getContractAt(
      name: "ICanonicalTransactionChain",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ICanonicalTransactionChain>;
    getContractAt(
      name: "IChainStorageContainer",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IChainStorageContainer>;
    getContractAt(
      name: "IStateCommitmentChain",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IStateCommitmentChain>;
    getContractAt(
      name: "StateCommitmentChain",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.StateCommitmentChain>;
    getContractAt(
      name: "BondManager",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.BondManager>;
    getContractAt(
      name: "IBondManager",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IBondManager>;
    getContractAt(
      name: "IL2CrossDomainMessenger",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL2CrossDomainMessenger>;
    getContractAt(
      name: "IL2ERC20Bridge",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL2ERC20Bridge>;
    getContractAt(
      name: "L2CrossDomainMessenger",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L2CrossDomainMessenger>;
    getContractAt(
      name: "L2StandardBridge",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L2StandardBridge>;
    getContractAt(
      name: "L2StandardTokenFactory",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L2StandardTokenFactory>;
    getContractAt(
      name: "IOVM_L1BlockNumber",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IOVM_L1BlockNumber>;
    getContractAt(
      name: "IOVM_L2ToL1MessagePasser",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IOVM_L2ToL1MessagePasser>;
    getContractAt(
      name: "OVM_DeployerWhitelist",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OVM_DeployerWhitelist>;
    getContractAt(
      name: "OVM_ETH",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OVM_ETH>;
    getContractAt(
      name: "OVM_GasPriceOracle",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OVM_GasPriceOracle>;
    getContractAt(
      name: "OVM_L2ToL1MessagePasser",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OVM_L2ToL1MessagePasser>;
    getContractAt(
      name: "OVM_SequencerFeeVault",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.OVM_SequencerFeeVault>;
    getContractAt(
      name: "WETH9",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.WETH9>;
    getContractAt(
      name: "CrossDomainEnabled",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.CrossDomainEnabled>;
    getContractAt(
      name: "ICrossDomainMessenger",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.ICrossDomainMessenger>;
    getContractAt(
      name: "Lib_AddressManager",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.Lib_AddressManager>;
    getContractAt(
      name: "Lib_AddressResolver",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.Lib_AddressResolver>;
    getContractAt(
      name: "Lib_ResolvedDelegateProxy",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.Lib_ResolvedDelegateProxy>;
    getContractAt(
      name: "IL2StandardERC20",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.IL2StandardERC20>;
    getContractAt(
      name: "L2StandardERC20",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.L2StandardERC20>;
    getContractAt(
      name: "FailingReceiver",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.FailingReceiver>;
    getContractAt(
      name: "TestERC20",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestERC20>;
    getContractAt(
      name: "TestLib_CrossDomainUtils",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_CrossDomainUtils>;
    getContractAt(
      name: "TestLib_OVMCodec",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_OVMCodec>;
    getContractAt(
      name: "TestLib_RLPReader",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_RLPReader>;
    getContractAt(
      name: "TestLib_RLPWriter",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_RLPWriter>;
    getContractAt(
      name: "TestLib_AddressAliasHelper",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_AddressAliasHelper>;
    getContractAt(
      name: "TestLib_MerkleTrie",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_MerkleTrie>;
    getContractAt(
      name: "TestLib_SecureMerkleTrie",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_SecureMerkleTrie>;
    getContractAt(
      name: "TestLib_Buffer",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_Buffer>;
    getContractAt(
      name: "TestLib_Bytes32Utils",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_Bytes32Utils>;
    getContractAt(
      name: "TestLib_BytesUtils",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_BytesUtils>;
    getContractAt(
      name: "TestLib_MerkleTree",
      address: string,
      signer?: ethers.Signer
    ): Promise<Contracts.TestLib_MerkleTree>;

    // default types
    getContractFactory(
      name: string,
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<ethers.ContractFactory>;
    getContractFactory(
      abi: any[],
      bytecode: ethers.utils.BytesLike,
      signer?: ethers.Signer
    ): Promise<ethers.ContractFactory>;
    getContractAt(
      nameOrAbi: string | any[],
      address: string,
      signer?: ethers.Signer
    ): Promise<ethers.Contract>;
  }
}
