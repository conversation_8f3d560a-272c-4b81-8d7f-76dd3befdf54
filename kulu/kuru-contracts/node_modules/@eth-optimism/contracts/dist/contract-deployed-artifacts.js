"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDeployedContractArtifact = void 0;
const goerli__AddressDictator = { abi: [{ "inputs": [{ "internalType": "contract Lib_AddressManager", "name": "_manager", "type": "address" }, { "internalType": "address", "name": "_finalOwner", "type": "address" }, { "internalType": "string[]", "name": "_names", "type": "string[]" }, { "internalType": "address[]", "name": "_addresses", "type": "address[]" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "finalOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNamedAddresses", "outputs": [{ "components": [{ "internalType": "string", "name": "name", "type": "string" }, { "internalType": "address", "name": "addr", "type": "address" }], "internalType": "struct AddressDictator.NamedAddress[]", "name": "", "type": "tuple[]" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "manager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "returnOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "setAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x406905414D6c250C186F4616EFA38D5fc0759437' };
const goerli__BondManager = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "address", "name": "_who", "type": "address" }], "name": "isCollateralized", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0xfC2ab6987C578218f99E85d61Dcf4814A26637Bd' };
const goerli__CanonicalTransactionChain = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "uint256", "name": "_maxTransactionGasLimit", "type": "uint256" }, { "internalType": "uint256", "name": "_l2GasDiscountDivisor", "type": "uint256" }, { "internalType": "uint256", "name": "_enqueueGasCost", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "l2GasDiscountDivisor", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "enqueueGasCost", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "enqueueL2GasPrepaid", "type": "uint256" }], "name": "L2GasParamsUpdated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "_startingQueueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_numQueueElements", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "name": "QueueBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "_startingQueueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_numQueueElements", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "name": "SequencerBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }, { "indexed": false, "internalType": "uint256", "name": "_batchSize", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_prevTotalElements", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_extraData", "type": "bytes" }], "name": "TransactionBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1TxOrigin", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_target", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_gasLimit", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }, { "indexed": true, "internalType": "uint256", "name": "_queueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_timestamp", "type": "uint256" }], "name": "TransactionEnqueued", "type": "event" }, { "inputs": [], "name": "MAX_ROLLUP_TX_SIZE", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "MIN_ROLLUP_TX_GAS", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "appendSequencerBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "batches", "outputs": [{ "internalType": "contract IChainStorageContainer", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "uint256", "name": "_gasLimit", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "enqueue", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "enqueueGasCost", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "enqueueL2GasPrepaid", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getLastBlockNumber", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getLastTimestamp", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNextQueueIndex", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNumPendingQueueElements", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "getQueueElement", "outputs": [{ "components": [{ "internalType": "bytes32", "name": "transactionHash", "type": "bytes32" }, { "internalType": "uint40", "name": "timestamp", "type": "uint40" }, { "internalType": "uint40", "name": "blockNumber", "type": "uint40" }], "internalType": "struct Lib_OVMCodec.QueueElement", "name": "_element", "type": "tuple" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getQueueLength", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalBatches", "outputs": [{ "internalType": "uint256", "name": "_totalBatches", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalElements", "outputs": [{ "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "l2GasDiscountDivisor", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "maxTransactionGasLimit", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_l2GasDiscountDivisor", "type": "uint256" }, { "internalType": "uint256", "name": "_enqueueGasCost", "type": "uint256" }], "name": "setGasParams", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x607F755149cFEB3a14E1Dc3A4E2450Cde7dfb04D' };
const goerli__ChainStorageContainer_CTC_batches = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_owner", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "get", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getGlobalMetadata", "outputs": [{ "internalType": "bytes27", "name": "", "type": "bytes27" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "length", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "setGlobalMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x4325Ac17c7fF5Afc0d05335dD30Db3D010455813' };
const goerli__ChainStorageContainer_SCC_batches = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_owner", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "get", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getGlobalMetadata", "outputs": [{ "internalType": "bytes27", "name": "", "type": "bytes27" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "length", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "setGlobalMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x41eF5DaF4A7719bfe89A88BA3DD0DCFF5feCeD39' };
const goerli__ChugSplashDictator = { abi: [{ "inputs": [{ "internalType": "contract L1ChugSplashProxy", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_finalOwner", "type": "address" }, { "internalType": "bytes32", "name": "_codeHash", "type": "bytes32" }, { "internalType": "bytes32", "name": "_messengerSlotKey", "type": "bytes32" }, { "internalType": "bytes32", "name": "_messengerSlotVal", "type": "bytes32" }, { "internalType": "bytes32", "name": "_bridgeSlotKey", "type": "bytes32" }, { "internalType": "bytes32", "name": "_bridgeSlotVal", "type": "bytes32" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "bridgeSlotKey", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "bridgeSlotVal", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "codeHash", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes", "name": "_code", "type": "bytes" }], "name": "doActions", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "finalOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "isUpgrading", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messengerSlotKey", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messengerSlotVal", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "returnOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "target", "outputs": [{ "internalType": "contract L1ChugSplashProxy", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0x0e62FAf76a0239827f35f41478b521293e06195a' };
const goerli__L1StandardBridge_for_verification_only = { abi: [{ "inputs": [], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_l2Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ERC20DepositInitiated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_l2Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ERC20WithdrawalFinalized", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ETHDepositInitiated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ETHWithdrawalFinalized", "type": "event" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositERC20", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositERC20To", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositETH", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositETHTo", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "", "type": "address" }, { "internalType": "address", "name": "", "type": "address" }], "name": "deposits", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "donateETH", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "address", "name": "_from", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "finalizeERC20Withdrawal", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_from", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "finalizeETHWithdrawal", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1messenger", "type": "address" }, { "internalType": "address", "name": "_l2TokenBridge", "type": "address" }], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "l2TokenBridge", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messenger", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "stateMutability": "payable", "type": "receive" }], address: '******************************************' };
const goerli__Lib_AddressManager = { abi: [{ "anonymous": false, "inputs": [{ "indexed": true, "internalType": "string", "name": "_name", "type": "string" }, { "indexed": false, "internalType": "address", "name": "_newAddress", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_oldAddress", "type": "address" }], "name": "AddressSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "getAddress", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }, { "internalType": "address", "name": "_address", "type": "address" }], "name": "setAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0xa6f73589243a6A7a9023b1Fa0651b1d89c177111' };
const goerli__OVM_L1CrossDomainMessenger = { abi: [{ "inputs": [], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "msgHash", "type": "bytes32" }], "name": "FailedRelayedMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "MessageAllowed", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "MessageBlocked", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "address", "name": "account", "type": "address" }], "name": "Paused", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "msgHash", "type": "bytes32" }], "name": "RelayedMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "target", "type": "address" }, { "indexed": false, "internalType": "address", "name": "sender", "type": "address" }, { "indexed": false, "internalType": "bytes", "name": "message", "type": "bytes" }, { "indexed": false, "internalType": "uint256", "name": "messageNonce", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "gasLimit", "type": "uint256" }], "name": "SentMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "address", "name": "account", "type": "address" }], "name": "Unpaused", "type": "event" }, { "inputs": [{ "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "allowMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "blockMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "blockedMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "paused", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_sender", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint256", "name": "_messageNonce", "type": "uint256" }, { "components": [{ "internalType": "bytes32", "name": "stateRoot", "type": "bytes32" }, { "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "stateRootBatchHeader", "type": "tuple" }, { "components": [{ "internalType": "uint256", "name": "index", "type": "uint256" }, { "internalType": "bytes32[]", "name": "siblings", "type": "bytes32[]" }], "internalType": "struct Lib_OVMCodec.ChainInclusionProof", "name": "stateRootProof", "type": "tuple" }, { "internalType": "bytes", "name": "stateTrieWitness", "type": "bytes" }, { "internalType": "bytes", "name": "storageTrieWitness", "type": "bytes" }], "internalType": "struct IL1CrossDomainMessenger.L2MessageInclusionProof", "name": "_proof", "type": "tuple" }], "name": "relayMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "relayedMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_sender", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint256", "name": "_queueIndex", "type": "uint256" }, { "internalType": "uint32", "name": "_oldGasLimit", "type": "uint32" }, { "internalType": "uint32", "name": "_newGasLimit", "type": "uint32" }], "name": "replayMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint32", "name": "_gasLimit", "type": "uint32" }], "name": "sendMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "successfulMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "xDomainMessageSender", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0x2eB424e0930E93Cf250e488f6117a929714Bb928' };
const goerli__Proxy__OVM_L1CrossDomainMessenger = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_implementationName", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "stateMutability": "payable", "type": "fallback" }], address: '0x5086d1eEF304eb5284A0f6720f79403b4e9bE294' };
const goerli__Proxy__OVM_L1StandardBridge = { abi: [{ "inputs": [{ "internalType": "address", "name": "_owner", "type": "address" }], "stateMutability": "nonpayable", "type": "constructor" }, { "stateMutability": "payable", "type": "fallback" }, { "inputs": [], "name": "getImplementation", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "getOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes", "name": "_code", "type": "bytes" }], "name": "setCode", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_owner", "type": "address" }], "name": "setOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_key", "type": "bytes32" }, { "internalType": "bytes32", "name": "_value", "type": "bytes32" }], "name": "setStorage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x636Af16bf2f682dD3109e60102b8E1A089FedAa8' };
const goerli__StateCommitmentChain = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "uint256", "name": "_fraudProofWindow", "type": "uint256" }, { "internalType": "uint256", "name": "_sequencerPublishWindow", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }, { "indexed": false, "internalType": "uint256", "name": "_batchSize", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_prevTotalElements", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_extraData", "type": "bytes" }], "name": "StateBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }], "name": "StateBatchDeleted", "type": "event" }, { "inputs": [], "name": "FRAUD_PROOF_WINDOW", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "SEQUENCER_PUBLISH_WINDOW", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32[]", "name": "_batch", "type": "bytes32[]" }, { "internalType": "uint256", "name": "_shouldStartAtElement", "type": "uint256" }], "name": "appendStateBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "batches", "outputs": [{ "internalType": "contract IChainStorageContainer", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }], "name": "deleteStateBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "getLastSequencerTimestamp", "outputs": [{ "internalType": "uint256", "name": "_lastSequencerTimestamp", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalBatches", "outputs": [{ "internalType": "uint256", "name": "_totalBatches", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalElements", "outputs": [{ "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }], "name": "insideFraudProofWindow", "outputs": [{ "internalType": "bool", "name": "_inside", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_element", "type": "bytes32" }, { "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }, { "components": [{ "internalType": "uint256", "name": "index", "type": "uint256" }, { "internalType": "bytes32[]", "name": "siblings", "type": "bytes32[]" }], "internalType": "struct Lib_OVMCodec.ChainInclusionProof", "name": "_proof", "type": "tuple" }], "name": "verifyStateCommitment", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }], address: '0x9c945aC97Baf48cB784AbBB61399beB71aF7A378' };
const mainnet__AddressDictator = { abi: [{ "inputs": [{ "internalType": "contract Lib_AddressManager", "name": "_manager", "type": "address" }, { "internalType": "address", "name": "_finalOwner", "type": "address" }, { "internalType": "string[]", "name": "_names", "type": "string[]" }, { "internalType": "address[]", "name": "_addresses", "type": "address[]" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "finalOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNamedAddresses", "outputs": [{ "components": [{ "internalType": "string", "name": "name", "type": "string" }, { "internalType": "address", "name": "addr", "type": "address" }], "internalType": "struct AddressDictator.NamedAddress[]", "name": "", "type": "tuple[]" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "manager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "returnOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "setAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x7a74f7934a233e10E8757264132B2E4EbccF5098' };
const mainnet__BondManager = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "address", "name": "_who", "type": "address" }], "name": "isCollateralized", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0xcd626E1328b41fCF24737F137BcD4CE0c32bc8d1' };
const mainnet__CanonicalTransactionChain = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "uint256", "name": "_maxTransactionGasLimit", "type": "uint256" }, { "internalType": "uint256", "name": "_l2GasDiscountDivisor", "type": "uint256" }, { "internalType": "uint256", "name": "_enqueueGasCost", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "l2GasDiscountDivisor", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "enqueueGasCost", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "enqueueL2GasPrepaid", "type": "uint256" }], "name": "L2GasParamsUpdated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "_startingQueueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_numQueueElements", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "name": "QueueBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "_startingQueueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_numQueueElements", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "name": "SequencerBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }, { "indexed": false, "internalType": "uint256", "name": "_batchSize", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_prevTotalElements", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_extraData", "type": "bytes" }], "name": "TransactionBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1TxOrigin", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_target", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_gasLimit", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }, { "indexed": true, "internalType": "uint256", "name": "_queueIndex", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_timestamp", "type": "uint256" }], "name": "TransactionEnqueued", "type": "event" }, { "inputs": [], "name": "MAX_ROLLUP_TX_SIZE", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "MIN_ROLLUP_TX_GAS", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "appendSequencerBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "batches", "outputs": [{ "internalType": "contract IChainStorageContainer", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "uint256", "name": "_gasLimit", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "enqueue", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "enqueueGasCost", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "enqueueL2GasPrepaid", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getLastBlockNumber", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getLastTimestamp", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNextQueueIndex", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getNumPendingQueueElements", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "getQueueElement", "outputs": [{ "components": [{ "internalType": "bytes32", "name": "transactionHash", "type": "bytes32" }, { "internalType": "uint40", "name": "timestamp", "type": "uint40" }, { "internalType": "uint40", "name": "blockNumber", "type": "uint40" }], "internalType": "struct Lib_OVMCodec.QueueElement", "name": "_element", "type": "tuple" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getQueueLength", "outputs": [{ "internalType": "uint40", "name": "", "type": "uint40" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalBatches", "outputs": [{ "internalType": "uint256", "name": "_totalBatches", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalElements", "outputs": [{ "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "l2GasDiscountDivisor", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "maxTransactionGasLimit", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_l2GasDiscountDivisor", "type": "uint256" }, { "internalType": "uint256", "name": "_enqueueGasCost", "type": "uint256" }], "name": "setGasParams", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x5E4e65926BA27467555EB562121fac00D24E9dD2' };
const mainnet__ChainStorageContainer_CTC_batches = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_owner", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "get", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getGlobalMetadata", "outputs": [{ "internalType": "bytes27", "name": "", "type": "bytes27" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "length", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "setGlobalMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0xD16463EF9b0338CE3D73309028ef1714D220c024' };
const mainnet__ChainStorageContainer_SCC_batches = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_owner", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "deleteElementsAfterInclusive", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_index", "type": "uint256" }], "name": "get", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getGlobalMetadata", "outputs": [{ "internalType": "bytes27", "name": "", "type": "bytes27" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "length", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }, { "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_object", "type": "bytes32" }], "name": "push", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes27", "name": "_globalMetadata", "type": "bytes27" }], "name": "setGlobalMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0xb0ddFf09c4019e31960de11bD845E836078E8EbE' };
const mainnet__ChugSplashDictator = { abi: [{ "inputs": [{ "internalType": "contract L1ChugSplashProxy", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_finalOwner", "type": "address" }, { "internalType": "bytes32", "name": "_codeHash", "type": "bytes32" }, { "internalType": "bytes32", "name": "_messengerSlotKey", "type": "bytes32" }, { "internalType": "bytes32", "name": "_messengerSlotVal", "type": "bytes32" }, { "internalType": "bytes32", "name": "_bridgeSlotKey", "type": "bytes32" }, { "internalType": "bytes32", "name": "_bridgeSlotVal", "type": "bytes32" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "bridgeSlotKey", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "bridgeSlotVal", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "codeHash", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes", "name": "_code", "type": "bytes" }], "name": "doActions", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "finalOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "isUpgrading", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messengerSlotKey", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messengerSlotVal", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "returnOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "target", "outputs": [{ "internalType": "contract L1ChugSplashProxy", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0xD86065136E3ab1e3FCBbf47B59404c08A431051A' };
const mainnet__L1StandardBridge_for_verification_only = { abi: [{ "inputs": [], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_l2Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ERC20DepositInitiated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_l1Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_l2Token", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ERC20WithdrawalFinalized", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ETHDepositInitiated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "_from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "_to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "ETHWithdrawalFinalized", "type": "event" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositERC20", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositERC20To", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositETH", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint32", "name": "_l2Gas", "type": "uint32" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "depositETHTo", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "", "type": "address" }, { "internalType": "address", "name": "", "type": "address" }], "name": "deposits", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "donateETH", "outputs": [], "stateMutability": "payable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1Token", "type": "address" }, { "internalType": "address", "name": "_l2Token", "type": "address" }, { "internalType": "address", "name": "_from", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "finalizeERC20Withdrawal", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_from", "type": "address" }, { "internalType": "address", "name": "_to", "type": "address" }, { "internalType": "uint256", "name": "_amount", "type": "uint256" }, { "internalType": "bytes", "name": "_data", "type": "bytes" }], "name": "finalizeETHWithdrawal", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_l1messenger", "type": "address" }, { "internalType": "address", "name": "_l2TokenBridge", "type": "address" }], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "l2TokenBridge", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "messenger", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "stateMutability": "payable", "type": "receive" }], address: '******************************************' };
const mainnet__Lib_AddressManager = { abi: [{ "anonymous": false, "inputs": [{ "indexed": true, "internalType": "string", "name": "_name", "type": "string" }, { "indexed": false, "internalType": "address", "name": "_newAddress", "type": "address" }, { "indexed": false, "internalType": "address", "name": "_oldAddress", "type": "address" }], "name": "AddressSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "getAddress", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }, { "internalType": "address", "name": "_address", "type": "address" }], "name": "setAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0xdE1FCfB0851916CA5101820A69b13a4E276bd81F' };
const mainnet__OVM_L1CrossDomainMessenger = { abi: [{ "inputs": [], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "msgHash", "type": "bytes32" }], "name": "FailedRelayedMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "MessageAllowed", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "MessageBlocked", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "address", "name": "account", "type": "address" }], "name": "Paused", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "msgHash", "type": "bytes32" }], "name": "RelayedMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "target", "type": "address" }, { "indexed": false, "internalType": "address", "name": "sender", "type": "address" }, { "indexed": false, "internalType": "bytes", "name": "message", "type": "bytes" }, { "indexed": false, "internalType": "uint256", "name": "messageNonce", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "gasLimit", "type": "uint256" }], "name": "SentMessage", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "address", "name": "account", "type": "address" }], "name": "Unpaused", "type": "event" }, { "inputs": [{ "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "allowMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_xDomainCalldataHash", "type": "bytes32" }], "name": "blockMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "blockedMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "paused", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_sender", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint256", "name": "_messageNonce", "type": "uint256" }, { "components": [{ "internalType": "bytes32", "name": "stateRoot", "type": "bytes32" }, { "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "stateRootBatchHeader", "type": "tuple" }, { "components": [{ "internalType": "uint256", "name": "index", "type": "uint256" }, { "internalType": "bytes32[]", "name": "siblings", "type": "bytes32[]" }], "internalType": "struct Lib_OVMCodec.ChainInclusionProof", "name": "stateRootProof", "type": "tuple" }, { "internalType": "bytes", "name": "stateTrieWitness", "type": "bytes" }, { "internalType": "bytes", "name": "storageTrieWitness", "type": "bytes" }], "internalType": "struct IL1CrossDomainMessenger.L2MessageInclusionProof", "name": "_proof", "type": "tuple" }], "name": "relayMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "relayedMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "address", "name": "_sender", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint256", "name": "_queueIndex", "type": "uint256" }, { "internalType": "uint32", "name": "_oldGasLimit", "type": "uint32" }, { "internalType": "uint32", "name": "_newGasLimit", "type": "uint32" }], "name": "replayMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_target", "type": "address" }, { "internalType": "bytes", "name": "_message", "type": "bytes" }, { "internalType": "uint32", "name": "_gasLimit", "type": "uint32" }], "name": "sendMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "successfulMessages", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "xDomainMessageSender", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }], address: '0xd9166833FF12A5F900ccfBf2c8B62a90F1Ca1FD5' };
const mainnet__Proxy__OVM_L1CrossDomainMessenger = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "string", "name": "_implementationName", "type": "string" }], "stateMutability": "nonpayable", "type": "constructor" }, { "stateMutability": "payable", "type": "fallback" }], address: '0x25ace71c97B33Cc4729CF772ae268934F7ab5fA1' };
const mainnet__Proxy__OVM_L1StandardBridge = { abi: [{ "inputs": [{ "internalType": "address", "name": "_owner", "type": "address" }], "stateMutability": "nonpayable", "type": "constructor" }, { "stateMutability": "payable", "type": "fallback" }, { "inputs": [], "name": "getImplementation", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "getOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes", "name": "_code", "type": "bytes" }], "name": "setCode", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "_owner", "type": "address" }], "name": "setOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_key", "type": "bytes32" }, { "internalType": "bytes32", "name": "_value", "type": "bytes32" }], "name": "setStorage", "outputs": [], "stateMutability": "nonpayable", "type": "function" }], address: '0x99C9fc46f92E8a1c0deC1b1747d010903E884bE1' };
const mainnet__StateCommitmentChain = { abi: [{ "inputs": [{ "internalType": "address", "name": "_libAddressManager", "type": "address" }, { "internalType": "uint256", "name": "_fraudProofWindow", "type": "uint256" }, { "internalType": "uint256", "name": "_sequencerPublishWindow", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }, { "indexed": false, "internalType": "uint256", "name": "_batchSize", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "_prevTotalElements", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "_extraData", "type": "bytes" }], "name": "StateBatchAppended", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "_batchIndex", "type": "uint256" }, { "indexed": false, "internalType": "bytes32", "name": "_batchRoot", "type": "bytes32" }], "name": "StateBatchDeleted", "type": "event" }, { "inputs": [], "name": "FRAUD_PROOF_WINDOW", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "SEQUENCER_PUBLISH_WINDOW", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32[]", "name": "_batch", "type": "bytes32[]" }, { "internalType": "uint256", "name": "_shouldStartAtElement", "type": "uint256" }], "name": "appendStateBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "batches", "outputs": [{ "internalType": "contract IChainStorageContainer", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }], "name": "deleteStateBatch", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "getLastSequencerTimestamp", "outputs": [{ "internalType": "uint256", "name": "_lastSequencerTimestamp", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalBatches", "outputs": [{ "internalType": "uint256", "name": "_totalBatches", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getTotalElements", "outputs": [{ "internalType": "uint256", "name": "_totalElements", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }], "name": "insideFraudProofWindow", "outputs": [{ "internalType": "bool", "name": "_inside", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "libAddressManager", "outputs": [{ "internalType": "contract Lib_AddressManager", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_name", "type": "string" }], "name": "resolve", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "_element", "type": "bytes32" }, { "components": [{ "internalType": "uint256", "name": "batchIndex", "type": "uint256" }, { "internalType": "bytes32", "name": "batchRoot", "type": "bytes32" }, { "internalType": "uint256", "name": "batchSize", "type": "uint256" }, { "internalType": "uint256", "name": "prevTotalElements", "type": "uint256" }, { "internalType": "bytes", "name": "extraData", "type": "bytes" }], "internalType": "struct Lib_OVMCodec.ChainBatchHeader", "name": "_batchHeader", "type": "tuple" }, { "components": [{ "internalType": "uint256", "name": "index", "type": "uint256" }, { "internalType": "bytes32[]", "name": "siblings", "type": "bytes32[]" }], "internalType": "struct Lib_OVMCodec.ChainInclusionProof", "name": "_proof", "type": "tuple" }], "name": "verifyStateCommitment", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }], address: '0xBe5dAb4A2e9cd0F27300dB4aB94BeE3A233AEB19' };
const mainnet__TeleportrDeposit = { abi: [{ "inputs": [{ "internalType": "uint256", "name": "_minDepositAmount", "type": "uint256" }, { "internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256" }, { "internalType": "uint256", "name": "_maxBalance", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "balance", "type": "uint256" }], "name": "BalanceWithdrawn", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "depositId", "type": "uint256" }, { "indexed": true, "internalType": "address", "name": "emitter", "type": "address" }, { "indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "EtherReceived", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "previousBalance", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "newBalance", "type": "uint256" }], "name": "MaxBalanceSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "previousAmount", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256" }], "name": "MaxDepositAmountSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint256", "name": "previousAmount", "type": "uint256" }, { "indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256" }], "name": "MinDepositAmountSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "inputs": [], "name": "maxBalance", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "maxDepositAmount", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "minDepositAmount", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256" }], "name": "setMaxAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_maxBalance", "type": "uint256" }], "name": "setMaxBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "_minDepositAmount", "type": "uint256" }], "name": "setMinAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "totalDeposits", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "withdrawBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "stateMutability": "payable", "type": "receive" }], address: '0x52ec2F3d7C5977A8E558C8D9C6000B615098E8fC' };
const getDeployedContractArtifact = (name, network) => {
    return {
        goerli__AddressDictator,
        goerli__BondManager,
        goerli__CanonicalTransactionChain,
        goerli__ChainStorageContainer_CTC_batches,
        goerli__ChainStorageContainer_SCC_batches,
        goerli__ChugSplashDictator,
        goerli__L1StandardBridge_for_verification_only,
        goerli__Lib_AddressManager,
        goerli__OVM_L1CrossDomainMessenger,
        goerli__Proxy__OVM_L1CrossDomainMessenger,
        goerli__Proxy__OVM_L1StandardBridge,
        goerli__StateCommitmentChain,
        mainnet__AddressDictator,
        mainnet__BondManager,
        mainnet__CanonicalTransactionChain,
        mainnet__ChainStorageContainer_CTC_batches,
        mainnet__ChainStorageContainer_SCC_batches,
        mainnet__ChugSplashDictator,
        mainnet__L1StandardBridge_for_verification_only,
        mainnet__Lib_AddressManager,
        mainnet__OVM_L1CrossDomainMessenger,
        mainnet__Proxy__OVM_L1CrossDomainMessenger,
        mainnet__Proxy__OVM_L1StandardBridge,
        mainnet__StateCommitmentChain,
        mainnet__TeleportrDeposit
    }[(network + '__' + name).replace(/-/g, '_')];
};
exports.getDeployedContractArtifact = getDeployedContractArtifact;
//# sourceMappingURL=contract-deployed-artifacts.js.map