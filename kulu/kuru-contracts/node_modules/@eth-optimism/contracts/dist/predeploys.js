"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.futurePredeploys = exports.predeploys = void 0;
exports.predeploys = {
    OVM_L2ToL1MessagePasser: '0x4200000000000000000000000000000000000000',
    OVM_DeployerWhitelist: '******************************************',
    L2CrossDomainMessenger: '******************************************',
    OVM_GasPriceOracle: '******************************************',
    L2StandardBridge: '******************************************',
    OVM_SequencerFeeVault: '******************************************',
    L2StandardTokenFactory: '******************************************',
    OVM_L1BlockNumber: '******************************************',
    OVM_ETH: '******************************************',
    WETH9: '******************************************',
};
exports.futurePredeploys = {
    System0: '******************************************',
    System1: '******************************************',
};
//# sourceMappingURL=predeploys.js.map