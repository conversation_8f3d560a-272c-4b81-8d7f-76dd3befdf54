{"_format": "hh-sol-artifact-1", "contractName": "IL2ERC20Bridge", "sourceName": "contracts/L2/messaging/IL2ERC20Bridge.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_l1Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_l2Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "DepositFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_l1Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_l2Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "DepositFinalized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_l1Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_l2Token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "WithdrawalInitiated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_l1Token", "type": "address"}, {"internalType": "address", "name": "_l2Token", "type": "address"}, {"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "finalizeDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "l1TokenBridge", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_l2Token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint32", "name": "_l1Gas", "type": "uint32"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_l2Token", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint32", "name": "_l1Gas", "type": "uint32"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "withdrawTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}