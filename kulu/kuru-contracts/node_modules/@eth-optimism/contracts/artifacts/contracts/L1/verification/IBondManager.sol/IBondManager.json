{"_format": "hh-sol-artifact-1", "contractName": "IBondManager", "sourceName": "contracts/L1/verification/IBondManager.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_who", "type": "address"}], "name": "isCollateralized", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}