{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "CLI arguments parser. Native port of python's argparse.", "version": "2.0.1", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "main": "argparse.js", "files": ["argparse.js", "lib/"], "license": "Python-2.0", "repository": "nodeca/argparse", "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html"}, "devDependencies": {"@babel/eslint-parser": "^7.11.0", "@babel/plugin-syntax-class-properties": "^7.10.4", "eslint": "^7.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0"}}