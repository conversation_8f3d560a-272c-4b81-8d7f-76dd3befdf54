{"name": "node-emoji", "version": "1.11.0", "description": "simple emoji support for node.js projects", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/omnidan/node-emoji.git"}, "keywords": ["emoji", "simple", "emoticons", "emoticon", "emojis", "smiley", "smileys", "smilies", "ideogram", "ideograms"], "bugs": {"url": "https://github.com/omnidan/node-emoji/issues"}, "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"istanbul": "^0.4.5", "mocha": "^9.0.3", "should": "^13.2.3"}, "scripts": {"coverage": "./node_modules/.bin/istanbul cover _mocha test", "emojiparse": "node lib/emojiparse.js", "test": "./node_modules/.bin/mocha --require should --bail --reporter spec test/*", "watch": "./node_modules/.bin/mocha --require should --bail --reporter spec test/* --watch", "prepublish": "npm run test"}, "main": "index.js", "license": "MIT"}