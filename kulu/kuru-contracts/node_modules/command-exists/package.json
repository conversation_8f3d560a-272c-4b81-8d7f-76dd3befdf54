{"name": "command-exists", "version": "1.2.9", "description": "check whether a command line command exists in the current environment", "main": "index.js", "scripts": {"test": "mocha test/test.js"}, "repository": {"type": "git", "url": "http://github.com/mathisonian/command-exists"}, "keywords": ["cli", "command", "exists"], "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>> (https://arthursilber.de)"], "license": "MIT", "bugs": {"url": "https://github.com/mathisonian/command-exists/issues"}, "homepage": "https://github.com/mathisonian/command-exists", "devDependencies": {"expect.js": "^0.3.1", "jshint": "^2.9.1", "mocha": "^2.5.3"}}