{"name": "@scure/base", "version": "1.1.1", "description": "Secure, audited & 0-dep implementation of bech32, base64, base58, base32 & base16", "files": ["lib/index.js", "lib/esm/index.js", "lib/esm/package.json", "lib/index.d.ts"], "main": "lib/index.js", "module": "lib/esm/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/esm/index.js", "default": "./lib/index.js"}, "./index.d.ts": "./lib/index.d.ts"}, "scripts": {"bench": "node test/benchmark/index.js", "build": "tsc -d && tsc -p tsconfig.esm.json", "lint": "prettier --check index.ts", "test": "node test/index.js"}, "author": "<PERSON> (https://paulmillr.com)", "license": "MIT", "homepage": "https://github.com/paulmillr/scure-base", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/scure-base.git"}, "devDependencies": {"micro-should": "0.2.0", "prettier": "2.6.2", "typescript": "4.7.3"}, "keywords": ["bech32", "bech32m", "base64", "base58", "base32", "base16", "rfc4648", "rfc3548", "crockford", "encode", "encoder", "base-x", "base"], "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}