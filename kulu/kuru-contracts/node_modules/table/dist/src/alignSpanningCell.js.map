{"version": 3, "file": "alignSpanningCell.js", "sourceRoot": "", "sources": ["../../src/alignSpanningCell.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAuC;AACvC,+CAEuB;AACvB,qEAEkC;AAClC,iDAEwB;AAIxB,2DAE6B;AAI7B,mCAEiB;AACjB,yCAEoB;AAEpB;;GAEG;AACI,MAAM,gBAAgB,GAAG,CAAC,WAAwB,EAAE,UAAkB,EAAE,OAA4B,EAAY,EAAE;IACvH,MAAM,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAC,GAAG,WAAW,CAAC;IAExF,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,UAAU,GAAG,WAAW,GAAG,YAAY,CAAC;IAE7D,OAAO,IAAA,mBAAQ,EAAC,IAAA,kCAAc,EAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC9F,MAAM,WAAW,GAAG,IAAA,yBAAW,EAAC,IAAI,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAE/D,OAAO,IAAA,wBAAS,EAAC,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAXW,QAAA,gBAAgB,oBAW3B;AAEK,MAAM,yBAAyB,GAAG,CAAC,KAAkB,EAAE,OAAiB,EAAE,OAA4B,EAAE,EAAE;IAC/G,MAAM,EAAC,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAC,GAAG,OAAO,CAAC;IACvD,MAAM,EAAC,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAC,GAAG,KAAK,CAAC;IAExD,yDAAyD;IACzD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,EAAE,CAAC;KACX;IAED,MAAM,eAAe,GAAG,IAAA,gBAAQ,EAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IACrF,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IACxD,MAAM,2BAA2B,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,EAAE;QAC9G,OAAO,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC,MAAM,CAAC;IAEV,MAAM,oBAAoB,GAAG,eAAe,GAAG,iBAAiB,GAAG,2BAA2B,CAAC;IAE/F,OAAO,IAAA,0CAAiB,EAAC,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACtF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,IAAA,sBAAW,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5C;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAxBW,QAAA,yBAAyB,6BAwBpC"}