{"version": 3, "file": "spanningCellManager.js", "sourceRoot": "", "sources": ["../../src/spanningCellManager.ts"], "names": [], "mappings": ";;;AAAA,2DAE6B;AAC7B,6EAEsC;AACtC,uDAE2B;AAa3B,mCAIiB;AAqBjB,MAAM,eAAe,GAAG,CAAC,IAAqB,EAAE,YAA2B,EAA2B,EAAE;IACtG,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,EAAE;QAC3C,OAAO,IAAA,qBAAa,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,WAAwB,EAAE,OAA4B,EAAmC,EAAE;IACrH,MAAM,KAAK,GAAG,IAAA,uDAA0B,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAE/D,MAAM,cAAc,GAAG,IAAA,oCAAgB,EAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAErE,MAAM,cAAc,GAAG,IAAA,6CAAyB,EAAC,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAEvF,MAAM,cAAc,GAAG,CAAC,QAAgB,EAAE,EAAE;QAC1C,MAAM,EAAC,OAAO,EAAC,GAAG,WAAW,CAAC;QAC9B,MAAM,EAAC,kBAAkB,EAAE,UAAU,EAAC,GAAG,OAAO,CAAC;QAEjD,MAAM,iCAAiC,GAAG,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;QACjE,MAAM,iCAAiC,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7F,0BAA0B;YAC1B,OAAO,CAAC,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAG,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA,CAAC;QACzD,CAAC,CAAC,CAAC,MAAM,CAAC;QAEV,MAAM,MAAM,GAAG,IAAA,gBAAQ,EAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,iCAAiC,GAAG,iCAAiC,CAAC;QAEzI,OAAO,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,WAAmB,EAAE,EAAE;QAC/C,MAAM,EAAC,OAAO,EAAC,GAAG,WAAW,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAE9G,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO;QACL,GAAG,WAAW;QACd,oBAAoB,EAAE,gBAAgB;QACtC,kBAAkB,EAAE,cAAc;QAClC,MAAM,EAAE,cAAc,CAAC,MAAM;QAC7B,KAAK;KACN,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,KAAsB,EAAE,KAAsB,EAAE,MAAqB,EAAW,EAAE;IACrG,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC9C,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAE9C,IAAI,MAAM,IAAI,MAAM,EAAE;QACpB,OAAO,IAAA,oBAAY,EAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;KACrD;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,KAAkB,EAAU,EAAE;IAC/C,MAAM,EAAC,GAAG,EAAE,GAAG,EAAC,GAAG,KAAK,CAAC,OAAO,CAAC;IAEjC,OAAO,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;AACzB,CAAC,CAAC;AAEK,MAAM,yBAAyB,GAAG,CAAC,UAAkC,EAAuB,EAAE;IACnG,MAAM,EAAC,mBAAmB,EAAE,aAAa,EAAC,GAAG,UAAU,CAAC;IACxD,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAChD,OAAO,IAAA,iCAAe,EAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAoD,EAAE,CAAC;IAEvE,IAAI,UAAU,GAAa,EAAE,CAAC;IAE9B,OAAO,EAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE;;YAC5C,MAAM,WAAW,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAC,CAAC,CAAC,IAAA,4BAAoB,EAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YAE5F,MAAM,KAAK,GAAG,eAAe,CAAC,EAAC,GAAG,IAAI;gBACpC,GAAG,EAAE,WAAW,EAAC,EAAE,MAAM,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO,SAAS,CAAC;aAClB;YAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAO,kBAAkB,CAAC,KAAK,EAAE,EAAC,GAAG,UAAU;oBAC7C,UAAU,EAAC,CAAC,CAAC;aAChB;YAED,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAA,UAAU,CAAC,IAAI,qCAAf,UAAU,CAAC,IAAI,IAAM,kBAAkB,CAAC,KAAK,EAAE,EAAC,GAAG,UAAU;gBAC3D,UAAU,EAAC,CAAC,EAAC;YAEf,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QACD,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC5B,OAAO,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;QACD,UAAU;QACV,aAAa,EAAE,CAAC,WAAqB,EAAE,EAAE;YACvC,UAAU,GAAG,WAAW,CAAC;QAC3B,CAAC,EAAC,CAAC;AACL,CAAC,CAAC;AArCW,QAAA,yBAAyB,6BAqCpC"}