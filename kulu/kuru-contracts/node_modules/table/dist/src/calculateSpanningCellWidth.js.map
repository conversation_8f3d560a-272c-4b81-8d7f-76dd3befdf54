{"version": 3, "file": "calculateSpanningCellWidth.js", "sourceRoot": "", "sources": ["../../src/calculateSpanningCellWidth.ts"], "names": [], "mappings": ";;;AAMA,mCAEiB;AAEV,MAAM,0BAA0B,GAAG,CAAC,WAAwB,EAAE,YAAoC,EAAU,EAAE;IACnH,MAAM,EAAC,aAAa,EAAE,gBAAgB,EAAC,GAAG,YAAY,CAAC;IACvD,MAAM,EAAC,OAAO,EAAE,WAAW,EAAC,GAAG,WAAW,CAAC;IAE3C,MAAM,UAAU,GAAG,IAAA,gBAAQ,EACzB,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,KAAK,EAAC,EAAE,EAAE;QACpE,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CACH,CAAC;IAEF,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC;QAC/B,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY;YACvC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAA,gBAAQ,EACN,aAAa;aACV,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;aACvC,GAAG,CAAC,CAAC,EAAC,WAAW,EAAE,YAAY,EAAC,EAAE,EAAE;YACnC,OAAO,WAAW,GAAG,YAAY,CAAC;QACpC,CAAC,CAAC,CACL,CAAC;IACN,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAExD,MAAM,0BAA0B,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,EAAE;QAC3G,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC,MAAM,CAAC;IAEV,OAAO,UAAU,GAAG,YAAY,GAAG,iBAAiB,GAAG,0BAA0B,CAAC;AACpF,CAAC,CAAC;AA5BW,QAAA,0BAA0B,8BA4BrC"}