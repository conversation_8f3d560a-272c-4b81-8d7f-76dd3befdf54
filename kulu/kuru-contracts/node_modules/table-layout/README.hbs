[![view on npm](https://badgen.net/npm/v/table-layout)](https://www.npmjs.org/package/table-layout)
[![npm module downloads](https://badgen.net/npm/dt/table-layout)](https://www.npmjs.org/package/table-layout)
[![Gihub repo dependents](https://badgen.net/github/dependents-repo/75lb/table-layout)](https://github.com/75lb/table-layout/network/dependents?dependent_type=REPOSITORY)
[![Gihub package dependents](https://badgen.net/github/dependents-pkg/75lb/table-layout)](https://github.com/75lb/table-layout/network/dependents?dependent_type=PACKAGE)
[![Build Status](https://travis-ci.org/75lb/table-layout.svg?branch=master)](https://travis-ci.org/75lb/table-layout)
[![Coverage Status](https://coveralls.io/repos/github/75lb/table-layout/badge.svg)](https://coveralls.io/github/75lb/table-layout)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

# table-layout

Generates plain-text tables from JSON recordset input (array of objects). Useful for presenting text in column layout or data in table layout in text-based user interfaces. Also [available as a command-line tool](https://github.com/75lb/table-layout-cli).

## Synopsis

Where input looks like this:

```json
[
  {
    "number": 15134,
    "title": "Coveralls has no source available ",
    "login": "ndelangen",
    "comments": 0
  },
  {
    "number": 15133,
    "title": "Fixing --preserve-symlinks. Enhancing node to exploit.",
    "login": "phestermcs",
    "comments": 0
  },
  {
    "number": 15131,
    "title": "Question - Confused about NPM's local installation philosophy",
    "login": "the1mills",
    "comments": 0
  },
  {
    "number": 15130,
    "title": "Question - global npm cache directory if user is root?",
    "login": "ORESoftware",
    "comments": 0
  }
]
```

This code...

```js
const Table = require('table-layout')
const issues = require('./issues.json')
const table = new Table(issues, { maxWidth: 60 })
console.log(table.toString())
```

...produces this output:

```
 15134  Coveralls has no source available   ndelangen     0
 15133  Fixing --preserve-symlinks.         phestermcs    0
        Enhancing node to exploit.
 15131  Question - Confused about NPM's     the1mills     0
        local installation philosophy
 15130  Question - global npm cache         ORESoftware   0
        directory if user is root?
 15127  how to installa gulp fontfacegen    aramgreat     0
        on Windows 10
 15097  Cannot install package from         mastertinner  3
        tarball out of package.json entry
        generated by npm
 15067  npm "SELF_SIGNED_CERT_IN_CHAIN"     LegendsLyfe   3
        error when installing discord.js
        with .log
```

## API Reference
{{#module name="table-layout"}}
{{>body~}}
{{>member-index~}}
{{>members~}}
{{/module}}

* * *

&copy; 2015-21 Lloyd Brookes \<<EMAIL>\>. Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
