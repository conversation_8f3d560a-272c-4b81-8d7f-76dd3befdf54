{"name": "table-layout", "author": "<PERSON> <<EMAIL>>", "version": "1.0.2", "description": "Stylable text tables, handling ansi colour. Useful for console output.", "repository": "https://github.com/75lb/table-layout.git", "license": "MIT", "keywords": ["wrap", "terminal", "data", "columns", "format", "json", "command line"], "engines": {"node": ">=8.0.0"}, "scripts": {"test": "test-runner test/*.js", "docs": "jsdoc2md -t README.hbs index.js -p list --member-index-format list > README.md; echo", "cover": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"array-back": "^4.0.1", "deep-extend": "~0.6.0", "typical": "^5.2.0", "wordwrapjs": "^4.0.0"}, "devDependencies": {"coveralls": "^3.1.0", "jsdoc-to-markdown": "^7.0.0", "nyc": "^15.1.0", "test-runner": "^0.6.3"}, "files": ["index.js", "lib/*.js"]}