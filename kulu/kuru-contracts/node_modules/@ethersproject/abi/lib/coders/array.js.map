{"version": 3, "file": "array.js", "sourceRoot": "", "sources": ["../../src.ts/coders/array.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAEb,gDAA+C;AAC/C,wCAAsC;AACtC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,mDAAiE;AACjE,yCAA6C;AAE7C,SAAgB,IAAI,CAAC,MAAc,EAAE,MAA4B,EAAE,MAA8C;IAC7G,IAAI,WAAW,GAAe,IAAI,CAAC;IAEnC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACxB,WAAW,GAAG,MAAM,CAAC;KAEvB;SAAM,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC9C,IAAI,QAAM,GAAkC,EAAG,CAAC;QAEhD,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK;YAC3B,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE;gBACP,MAAM,CAAC,UAAU,CAAC,uDAAuD,EAAE,eAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACvG,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,MAAM;iBAChB,CAAC,CAAC;aACN;YAED,IAAI,QAAM,CAAC,IAAI,CAAC,EAAE;gBACd,MAAM,CAAC,UAAU,CAAC,yDAAyD,EAAE,eAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACzG,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,MAAM;iBAChB,CAAC,CAAC;aACN;YAED,QAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YAEpB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;KAEN;SAAM;QACH,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KACrE;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;QACtC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KAC7E;IAED,IAAI,YAAY,GAAG,IAAI,uBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/C,IAAI,aAAa,GAAG,IAAI,uBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEhD,IAAI,WAAW,GAAwC,EAAE,CAAC;IAC1D,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;QACxB,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,sDAAsD;YACtD,IAAI,eAAa,GAAG,aAAa,CAAC,MAAM,CAAC;YAEzC,kDAAkD;YAClD,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAEnC,0DAA0D;YAC1D,IAAI,YAAU,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACpD,WAAW,CAAC,IAAI,CAAC,UAAC,UAAkB;gBAChC,YAAU,CAAC,UAAU,GAAG,eAAa,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;SAEN;aAAM;YACH,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACrC;IACL,CAAC,CAAC,CAAC;IAEH,uEAAuE;IACvE,WAAW,CAAC,OAAO,CAAC,UAAC,IAAI,IAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC/C,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAC7C,OAAO,MAAM,CAAC;AAClB,CAAC;AAvED,oBAuEC;AAED,SAAgB,MAAM,CAAC,MAAc,EAAE,MAAoB;IACvD,IAAI,MAAM,GAAQ,EAAE,CAAC;IAErB,iCAAiC;IACjC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAErC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;QACjB,IAAI,KAAK,GAAQ,IAAI,CAAC;QAEtB,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3D,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aACtC;YAAC,OAAO,KAAK,EAAE;gBACZ,2BAA2B;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAAE,MAAM,KAAK,CAAC;iBAAE;gBACjE,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SAEJ;aAAM;YACH,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACZ,2BAA2B;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAAE,MAAM,KAAK,CAAC;iBAAE;gBACjE,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SACJ;QAED,IAAI,KAAK,IAAI,SAAS,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;IACL,CAAC,CAAC,CAAC;IAEH,4DAA4D;IAC5D,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,KAAK;QAC3C,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;QAC7B,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAAE;YACtC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;SACjB;QACD,OAAO,KAAK,CAAC;IACjB,CAAC,EAAgC,EAAG,CAAC,CAAC;IAEtC,yCAAyC;IACzC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAY,EAAE,KAAa;QACvC,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;QAC3B,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAAE,OAAO;SAAE;QAEjD,IAAI,IAAI,KAAK,QAAQ,EAAE;YAAE,IAAI,GAAG,SAAS,CAAC;SAAE;QAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;YAAE,OAAO;SAAE;QAErC,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,IAAI,KAAK,YAAY,KAAK,EAAE;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;gBAChC,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,cAAQ,MAAM,KAAK,CAAC,CAAC,CAAC;aAC9B,CAAC,CAAC;SACN;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;SACxB;IACL,CAAC,CAAC,CAAC;4BAEM,CAAC;QACN,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,KAAK,YAAY,KAAK,EAAE;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE;gBAC7B,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,cAAQ,MAAM,KAAK,CAAC,CAAC,CAAC;aAC9B,CAAC,CAAC;SACN;;IAPL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE;gBAA7B,CAAC;KAQT;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAnFD,wBAmFC;AAGD;IAAgC,8BAAK;IAIjC,oBAAY,KAAY,EAAE,MAAc,EAAE,SAAiB;QAA3D,iBAOC;QANG,IAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;QACnE,IAAM,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACjD,QAAA,kBAAM,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAC;QAEzC,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACzB,CAAC;IAED,iCAAY,GAAZ;QACI,+EAA+E;QAC/E,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAE/C,IAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7B;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc,EAAE,KAAiB;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;SACnD;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAExB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YACrB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC,CAAC,GAAG,GAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5G,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAAE;QAEnE,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;YAEtC,sDAAsD;YACtD,wDAAwD;YACxD,yDAAyD;YACzD,sDAAsD;YACtD,4DAA4D;YAC5D,IAAI,KAAK,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;gBAClC,MAAM,CAAC,UAAU,CAAC,0BAA0B,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBACxE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;oBAC3B,KAAK,EAAE,KAAK;iBACf,CAAC,CAAC;aACN;SACJ;QACD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,0BAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAAE;QAEhF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5D,CAAC;IACL,iBAAC;AAAD,CAAC,AAlED,CAAgC,sBAAK,GAkEpC;AAlEY,gCAAU"}