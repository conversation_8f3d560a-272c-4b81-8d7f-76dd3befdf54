"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnonymousCoder = void 0;
var abstract_coder_1 = require("./abstract-coder");
// Clones the functionality of an existing Coder, but without a localName
var AnonymousCoder = /** @class */ (function (_super) {
    __extends(AnonymousCoder, _super);
    function AnonymousCoder(coder) {
        var _this = _super.call(this, coder.name, coder.type, undefined, coder.dynamic) || this;
        _this.coder = coder;
        return _this;
    }
    AnonymousCoder.prototype.defaultValue = function () {
        return this.coder.defaultValue();
    };
    AnonymousCoder.prototype.encode = function (writer, value) {
        return this.coder.encode(writer, value);
    };
    AnonymousCoder.prototype.decode = function (reader) {
        return this.coder.decode(reader);
    };
    return AnonymousCoder;
}(abstract_coder_1.Coder));
exports.AnonymousCoder = AnonymousCoder;
//# sourceMappingURL=anonymous.js.map