{"_ethers.alias": {"random.js": "browser-random.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/random": "./lib/browser-random.js"}, "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}, "description": "Random utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers", "random"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/random", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/random", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xb91a9e0289e2dcbc8a1521cc785465161f4ae4aad01aea6ddf3a1a3f2c29a4da", "types": "./lib/index.d.ts", "version": "5.7.0"}