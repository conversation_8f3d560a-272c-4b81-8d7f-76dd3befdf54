Random Value Utilities
======================

This sub-module is part of the [ethers project](https://github.com/ethers-io/ethers.js).

It contains functions to assist with random numbers.

For more information, see the [documentation](https://docs.ethers.io/v5/api/utils/bytes/#byte-manipulation--random-bytes).


Importing
---------

Most users will prefer to use the [umbrella package](https://www.npmjs.com/package/ethers),
but for those with more specific needs, individual components can be imported.

```javascript
const {

    shuffled,

    randomBytes

} = require("@ethersproject/random");
```


License
-------

MIT License
