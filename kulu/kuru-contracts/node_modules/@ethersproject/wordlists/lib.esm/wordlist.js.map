{"version": 3, "file": "wordlist.js", "sourceRoot": "", "sources": ["../src.ts/wordlist.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,iCAAiC;AACjC,MAAM,cAAc,GAAG,KAAK,CAAC;AAE7B,OAAO,EAAE,EAAE,EAAE,MAAM,qBAAqB,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,CAAC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAE1C,MAAM,OAAgB,QAAQ;IAG1B,YAAY,MAAc;QACtB,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC3C,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAKD,+BAA+B;IAC/B,KAAK,CAAC,QAAgB;QAClB,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IAC9C,CAAC;IAED,+BAA+B;IAC/B,IAAI,CAAC,KAAoB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,QAAkB;QAC3B,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,wBAAwB;YACxB,IAAI,CAAC,KAAK,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;gBAAE,OAAO,IAAI,CAAC;aAAE;YACvD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpB;QACD,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,IAAc,EAAE,IAAa;QACzC,IAAI,CAAC,IAAI,EAAE;YAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;SAAE;QAElC,wBAAwB;QACxB,IAAI,cAAc,EAAE;YAChB,IAAI;gBACA,MAAM,SAAS,GAAI,MAAc,CAAA;gBACjC,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE;oBAClD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;wBACnC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;qBAC5D;iBACJ;aACJ;YAAC,OAAO,KAAK,EAAE,GAAG;SACtB;IACL,CAAC;CAEJ"}