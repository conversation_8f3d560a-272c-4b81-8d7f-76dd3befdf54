{"version": 3, "file": "browser-sha2.js", "sourceRoot": "", "sources": ["../src.ts/browser-sha2.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;AAEb,oDAA2B;AAC3B,qCAAqC;AAErC,8CAA2D;AAE3D,iCAA6C;AAE7C,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,SAAgB,SAAS,CAAC,IAAe;IACrC,OAAO,IAAI,GAAG,CAAC,iBAAI,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,CAAC;AAFD,8BAEC;AAED,SAAgB,MAAM,CAAC,IAAe;IAClC,OAAO,IAAI,GAAG,CAAC,iBAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,CAAC;AAFD,wBAEC;AAED,SAAgB,MAAM,CAAC,IAAe;IAClC,OAAO,IAAI,GAAG,CAAC,iBAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,CAAC;AAFD,wBAEC;AAED,SAAgB,WAAW,CAAC,SAA6B,EAAE,GAAc,EAAE,IAAe;IACtF,IAAI,CAAC,0BAAkB,CAAC,SAAS,CAAC,EAAE;QAChC,MAAM,CAAC,UAAU,CAAC,wBAAwB,GAAG,SAAS,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACzF,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;KACN;IAED,OAAO,IAAI,GAAG,iBAAI,CAAC,IAAI,CAAO,iBAAK,CAAC,SAAS,CAAC,EAAE,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxG,CAAC;AATD,kCASC"}