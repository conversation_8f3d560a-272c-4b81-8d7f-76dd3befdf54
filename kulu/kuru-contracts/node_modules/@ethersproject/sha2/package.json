{"_ethers.alias": {"sha2.js": "browser-sha2.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/sha2": "./lib/browser-sha2.js"}, "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}, "description": "The SHA2 family hash functions and HMAC functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/sha2", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/sha2", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xf4e98e42d47de50b1a899374350f5257d0ea9a604d59546ab0acbed6d89eceb6", "types": "./lib/index.d.ts", "version": "5.7.0"}