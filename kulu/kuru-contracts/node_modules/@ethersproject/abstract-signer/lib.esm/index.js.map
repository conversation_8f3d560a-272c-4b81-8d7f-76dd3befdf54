{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAKb,OAAO,EAAc,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAEvG,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,MAAM,sBAAsB,GAAkB;IAC1C,YAAY,EAAE,iBAAiB,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,sBAAsB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO;CAC3K,CAAC;AAEF,MAAM,aAAa,GAAG;IAClB,MAAM,CAAC,MAAM,CAAC,kBAAkB;IAChC,MAAM,CAAC,MAAM,CAAC,aAAa;IAC3B,MAAM,CAAC,MAAM,CAAC,uBAAuB;CACxC,CAAC;AAWD,CAAC;AAKD,CAAC;AAsBF,MAAM,OAAgB,MAAM;IA4BxB,mBAAmB;IACnB,8BAA8B;IAC9B;QACI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAGD,mBAAmB;IACnB,iCAAiC;IAE3B,UAAU,CAAC,QAAmB;;YAChC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAClC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;QACvE,CAAC;KAAA;IAEK,mBAAmB,CAAC,QAAmB;;YACzC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAC3C,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChF,CAAC;KAAA;IAED,6EAA6E;IACvE,WAAW,CAAC,WAA2C;;YACzD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACnC,MAAM,EAAE,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;YACvE,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC/C,CAAC;KAAA;IAED,kEAAkE;IAC5D,IAAI,CAAC,WAA2C,EAAE,QAAmB;;YACvE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5B,MAAM,EAAE,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;YACvE,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;KAAA;IAED,8EAA8E;IACxE,eAAe,CAAC,WAA2C;;YAC7D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACvC,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC;KAAA;IAEK,UAAU;;YACZ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YACjD,OAAO,OAAO,CAAC,OAAO,CAAC;QAC3B,CAAC;KAAA;IAEK,WAAW;;YACb,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACnC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC7C,CAAC;KAAA;IAEK,UAAU;;YACZ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAClC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC5C,CAAC;KAAA;IAGK,WAAW,CAAC,IAAY;;YAC1B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACnC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC;KAAA;IAID,4DAA4D;IAC5D,uCAAuC;IACvC,gCAAgC;IAChC,+BAA+B;IAC/B,+CAA+C;IAC/C,yDAAyD;IACzD,WAAW;IACX,kBAAkB;IAClB,yDAAyD;IACzD,gBAAgB,CAAC,WAA2C;QACxD,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;YAC3B,IAAI,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,MAAM,CAAC,kBAAkB,CAAC,2BAA2B,GAAG,GAAG,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aAC5F;SACJ;QAED,MAAM,EAAE,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QAEpC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;YACjB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;SAE/B;aAAM;YACH,qDAAqD;YACrD,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;gBAClB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxB,IAAI,CAAC,UAAU,EAAE;aACpB,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE;oBACrD,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;iBAClF;gBACD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;SACN;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,sEAAsE;IACtE,6EAA6E;IAC7E,yDAAyD;IACzD,sBAAsB;IACtB,EAAE;IACF,SAAS;IACT,uEAAuE;IACjE,mBAAmB,CAAC,WAA2C;;YAEjE,MAAM,EAAE,GAAmC,MAAM,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAA;YAEtG,IAAI,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE;gBACf,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAO,EAAE,EAAE,EAAE;oBAC7C,IAAI,EAAE,IAAI,IAAI,EAAE;wBAAE,OAAO,IAAI,CAAC;qBAAE;oBAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBAC3C,IAAI,OAAO,IAAI,IAAI,EAAE;wBACjB,MAAM,CAAC,kBAAkB,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;qBAChF;oBACD,OAAO,OAAO,CAAC;gBACnB,CAAC,CAAA,CAAC,CAAC;gBAEH,+DAA+D;gBAC/D,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,GAAI,CAAC,CAAC,CAAC;aAChC;YAED,2DAA2D;YAC3D,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC;YAChF,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,EAAE;gBACtD,MAAM,CAAC,kBAAkB,CAAC,8CAA8C,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aACzG;iBAAM,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,UAAU,EAAE;gBACvD,MAAM,CAAC,kBAAkB,CAAC,2EAA2E,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aACtI;YAED,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,CAAC,EAAE;gBACpG,sDAAsD;gBACtD,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;aAEf;iBAAM,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;gBACvC,0CAA0C;gBAE1C,4BAA4B;gBAC5B,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;oBAAE,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;iBAAE;aAEjE;iBAAM;gBAEH,8CAA8C;gBAC9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAExC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;oBACjB,kEAAkE;oBAElE,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC,oBAAoB,IAAI,IAAI,EAAE;wBACtE,iCAAiC;wBAEjC,4CAA4C;wBAC5C,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;wBAEZ,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;4BACrB,yDAAyD;4BACzD,yCAAyC;4BACzC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;4BAC7B,OAAO,EAAE,CAAC,QAAQ,CAAC;4BACnB,EAAE,CAAC,YAAY,GAAG,QAAQ,CAAC;4BAC3B,EAAE,CAAC,oBAAoB,GAAG,QAAQ,CAAC;yBAEtC;6BAAM;4BACH,4BAA4B;4BAC5B,IAAI,EAAE,CAAC,YAAY,IAAI,IAAI,EAAE;gCAAE,EAAE,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;6BAAE;4BACxE,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,EAAE;gCAAE,EAAE,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;6BAAE;yBACnG;qBAEJ;yBAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;wBACjC,sCAAsC;wBAEtC,oDAAoD;wBACpD,IAAI,UAAU,EAAE;4BACZ,MAAM,CAAC,UAAU,CAAC,mCAAmC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gCACxF,SAAS,EAAE,qBAAqB;6BACnC,CAAC,CAAC;yBACN;wBAED,4BAA4B;wBAC5B,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;4BAAE,EAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;yBAAE;wBAE5D,+CAA+C;wBAC/C,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;qBAEf;yBAAM;wBACH,4BAA4B;wBAC5B,MAAM,CAAC,UAAU,CAAC,mCAAmC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;4BACxF,SAAS,EAAE,mBAAmB;yBACjC,CAAC,CAAC;qBACN;iBAEJ;qBAAM,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;oBACtB,4BAA4B;oBAE5B,4BAA4B;oBAC5B,IAAI,EAAE,CAAC,YAAY,IAAI,IAAI,EAAE;wBAAE,EAAE,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;qBAAE;oBACxE,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,EAAE;wBAAE,EAAE,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;qBAAE;iBACnG;aACJ;YAED,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE;gBAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;aAAE;YAEzE,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACrB,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC/C,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACxC,MAAM,KAAK,CAAC;qBACf;oBAED,OAAO,MAAM,CAAC,UAAU,CAAC,2EAA2E,EAAE,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE;wBACzI,KAAK,EAAE,KAAK;wBACZ,EAAE,EAAE,EAAE;qBACT,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;YAED,IAAI,EAAE,CAAC,OAAO,IAAI,IAAI,EAAE;gBACpB,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;aAClC;iBAAM;gBACH,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;oBACrB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC3B,IAAI,CAAC,UAAU,EAAE;iBACpB,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBAChB,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;wBAC/C,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;qBACrF;oBACD,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC;aACN;YAED,OAAO,MAAM,iBAAiB,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;KAAA;IAGD,mBAAmB;IACnB,uCAAuC;IAEvC,cAAc,CAAC,SAAkB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7F,SAAS,EAAE,CAAC,SAAS,IAAI,gBAAgB,CAAC;aAAE,CAAC,CAAC;SACjD;IACL,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,KAAU;QACtB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;CACJ;AAED,MAAM,OAAO,UAAW,SAAQ,MAAM;IAGlC,YAAY,OAAe,EAAE,QAAmB;QAC5C,KAAK,EAAE,CAAC;QACR,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,UAAU;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,SAAiB;QACpC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,OAAuB;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAED,eAAe,CAAC,WAA2C;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,iBAAiB,CAAC,CAAC;IAChF,CAAC;IAED,cAAc,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B;QAC5G,OAAO,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,eAAe,CAAC,CAAC;IAC5E,CAAC;IAED,OAAO,CAAC,QAAkB;QACtB,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;CACJ"}