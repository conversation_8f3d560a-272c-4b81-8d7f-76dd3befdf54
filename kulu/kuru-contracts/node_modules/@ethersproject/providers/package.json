{"_ethers.alias": {"browser-net.d.ts": "", "browser-net.d.ts.map": "", "browser-net.js": "", "browser-net.js.map": "", "ipc-provider.js": "browser-ipc-provider.js", "ws.js": "browser-ws.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/ipc-provider": "./lib/browser-ipc-provider.js", "./lib/ws": "./lib/browser-ws.js", "net": "./lib/browser-net.js"}, "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0", "bech32": "1.1.4", "ws": "7.4.6"}, "description": "Ethereum Providers for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "60248f32491b13e914f801402d3041a37e4bf44a", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/providers", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/providers", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x7cd2809b100a1f50c6855245bf6ee7e848535447206f8fb83837445400204372", "types": "./lib/index.d.ts", "version": "5.7.2"}