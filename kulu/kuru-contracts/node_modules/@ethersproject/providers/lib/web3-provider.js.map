{"version": 3, "file": "web3-provider.js", "sourceRoot": "", "sources": ["../src.ts/web3-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAGb,wDAAqE;AAErE,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,yDAAsD;AAatD,IAAI,OAAO,GAAG,CAAC,CAAC;AAMhB,SAAS,sBAAsB,CAAC,QAA0B,EAAE,QAAwB;IAChF,IAAM,OAAO,GAAG,mBAAmB,CAAC;IAEpC,OAAO,UAAS,MAAc,EAAE,MAAkB;QAA3C,iBAgDN;QA/CG,IAAM,OAAO,GAAG;YACZ,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;YACd,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,EAAE,KAAK;SACjB,CAAC;QAEF,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,SAAS;gBACjB,OAAO,SAAA;gBACP,OAAO,EAAE,IAAA,qBAAQ,EAAC,OAAO,CAAC;gBAC1B,QAAQ,EAAE,KAAI;aACjB,CAAC,CAAC;YAEH,QAAQ,CAAC,OAAO,EAAE,UAAC,KAAK,EAAE,QAAQ;gBAE9B,IAAI,KAAK,EAAE;oBACP,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,OAAO,SAAA;wBACP,KAAK,OAAA;wBACL,OAAO,SAAA;wBACP,QAAQ,EAAE,KAAI;qBACjB,CAAC,CAAC;oBAEH,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBACxB;gBAED,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACf,MAAM,EAAE,UAAU;oBAClB,OAAO,SAAA;oBACP,OAAO,SAAA;oBACP,QAAQ,UAAA;oBACR,QAAQ,EAAE,KAAI;iBACjB,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,KAAK,EAAE;oBAChB,IAAM,OAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAC1C,OAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;oBAClC,OAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;oBACxC,OAAO,MAAM,CAAC,OAAK,CAAC,CAAC;iBACxB;gBAED,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC,CAAA;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,QAA0B;IACnD,OAAO,UAAS,MAAc,EAAE,MAAkB;QAA3C,iBAkCN;QAjCG,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,MAAM,GAAG,EAAG,CAAC;SAAE;QAErC,IAAM,OAAO,GAAG,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,IAAA,qBAAQ,EAAC,OAAO,CAAC;YAC1B,QAAQ,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ;YAC3C,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,gBAAgB;gBACzB,OAAO,SAAA;gBACP,QAAQ,UAAA;gBACR,QAAQ,EAAE,KAAI;aACjB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAEpB,CAAC,EAAE,UAAC,KAAK;YACL,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,gBAAgB;gBACzB,OAAO,SAAA;gBACP,KAAK,OAAA;gBACL,QAAQ,EAAE,KAAI;aACjB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC,CAAA;AACL,CAAC;AAED;IAAkC,gCAAe;IAI7C,sBAAY,QAA6C,EAAE,OAAoB;QAA/E,iBAuCC;QAtCG,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,GAAW,IAAI,CAAC;QACxB,IAAI,gBAAgB,GAAqB,IAAI,CAAC;QAC9C,IAAI,WAAW,GAAqB,IAAI,CAAC;QAEzC,IAAI,OAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;YACjC,IAAI,GAAG,UAAU,CAAC;YAClB,gBAAgB,GAAG,QAAQ,CAAC;SAE/B;aAAM;YACH,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC9B,IAAI,GAAG,UAAU,CAAC;aACrB;YAED,WAAW,GAAG,QAAQ,CAAC;YAEvB,IAAI,QAAQ,CAAC,OAAO,EAAE;gBAClB,IAAI,IAAI,KAAK,EAAE,EAAE;oBAAE,IAAI,GAAG,WAAW,CAAC;iBAAE;gBACxC,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aACpD;iBAAM,IAAI,QAAQ,CAAC,SAAS,EAAE;gBAC3B,gBAAgB,GAAG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1F;iBAAM,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACtB,gBAAgB,GAAG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aACrF;iBAAM;gBACH,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3E;YAED,IAAI,CAAC,IAAI,EAAE;gBAAE,IAAI,GAAG,UAAU,CAAC;aAAE;SACpC;QAED,QAAA,kBAAM,IAAI,EAAE,OAAO,CAAC,SAAC;QAErB,IAAA,2BAAc,EAAC,KAAI,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QAC3D,IAAA,2BAAc,EAAC,KAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;;IAClD,CAAC;IAED,2BAAI,GAAJ,UAAK,MAAc,EAAE,MAAkB;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IACL,mBAAC;AAAD,CAAC,AAhDD,CAAkC,mCAAe,GAgDhD;AAhDY,oCAAY"}