{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAEA,OAAO,EACH,KAAK,EACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACtB,MAAM,kCAAkC,CAAC;AAE1C,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE9D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEnF,OAAO,EAAE,eAAe,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAC/E,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAC/E,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,cAAc,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AACpF,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AAErE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAShI,iBAAS,kBAAkB,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,YAAY,CA4C7E;AAKD,OAAO,EAGH,QAAQ,EACR,YAAY,EAEZ,QAAQ,EAER,kBAAkB,EAKlB,gBAAgB,EAEhB,eAAe,EACf,wBAAwB,EACxB,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,uBAAuB,EACvB,eAAe,EACf,oBAAoB,EACpB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,YAAY,EACZ,iBAAiB,EAEjB,WAAW,EAMX,aAAa,EAMb,kBAAkB,EAClB,UAAU,EACV,mBAAmB,EACnB,sBAAsB,EACtB,mBAAmB,EAMnB,SAAS,EAMT,KAAK,EACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,EACR,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EAEnB,gBAAgB,EAChB,gBAAgB,EAEhB,sBAAsB,EAEtB,OAAO,EACP,UAAU,EAEV,WAAW,EACX,WAAW,EAEX,oBAAoB,EACvB,CAAC"}