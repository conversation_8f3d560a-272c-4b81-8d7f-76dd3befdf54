{"version": 3, "file": "formatter.d.ts", "sourceRoot": "", "sources": ["../src.ts/formatter.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAElG,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAIrD,OAAO,EAAE,UAAU,EAA4C,MAAM,6BAA6B,CAAC;AAMnG,oBAAY,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC;AAE7C,oBAAY,WAAW,GAAG;IAAE,CAAE,GAAG,EAAE,MAAM,GAAI,UAAU,CAAA;CAAE,CAAC;AAE1D,oBAAY,OAAO,GAAG;IAClB,WAAW,EAAE,WAAW,CAAC;IACzB,kBAAkB,EAAE,WAAW,CAAC;IAChC,OAAO,EAAE,WAAW,CAAC;IACrB,UAAU,EAAE,WAAW,CAAC;IACxB,KAAK,EAAE,WAAW,CAAC;IACnB,qBAAqB,EAAE,WAAW,CAAC;IACnC,MAAM,EAAE,WAAW,CAAC;IACpB,SAAS,EAAE,WAAW,CAAC;CAC1B,CAAC;AAEF,qBAAa,SAAS;IAClB,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;;IAM1B,iBAAiB,IAAI,OAAO;IAgJ5B,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,UAAU;IAM9C,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM;IAK3B,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM;IAMzB,SAAS,CAAC,KAAK,EAAE,GAAG,GAAG,SAAS;IAKhC,OAAO,CAAC,KAAK,EAAE,GAAG,GAAG,OAAO;IAU5B,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM;IAUzC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM;IAU1C,OAAO,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM;IAI3B,WAAW,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM;IAM/B,eAAe,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM;IAKnC,QAAQ,CAAC,QAAQ,EAAE,GAAG,GAAG,MAAM;IAmB/B,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM;IAS1C,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM;IAY9B,OAAO,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM;IAO3B,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,GAAG,KAAK;IAWtC,KAAK,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK;IAIxB,qBAAqB,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK;IAKxC,kBAAkB,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAInC,mBAAmB,CAAC,WAAW,EAAE,GAAG,GAAG,mBAAmB;IAqE1D,WAAW,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAI5B,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAI3B,OAAO,CAAC,KAAK,EAAE,GAAG,GAAG,kBAAkB;IA+BvC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAWvB,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAIvB,SAAS,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAI1B,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;QAAE,CAAE,IAAI,EAAE,MAAM,GAAI,UAAU,CAAA;KAAE,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG;IAgBxE,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,UAAU;IAQjE,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,GAAG,UAAU;IAQtE,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,GAAG,UAAU;CAajD;AAED,MAAM,WAAW,oBAAoB;IACjC,mBAAmB,IAAI,OAAO,CAAC;CAClC;AAED,wBAAgB,sBAAsB,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,oBAAoB,CAEhF;AAED,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,GAAG,GAAG,OAAO,CAEvD;AAID,wBAAgB,mBAAmB,SAgBlC"}