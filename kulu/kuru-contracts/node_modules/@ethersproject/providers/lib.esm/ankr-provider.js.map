{"version": 3, "file": "ankr-provider.js", "sourceRoot": "", "sources": ["../src.ts/ankr-provider.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAI7D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAGnC,MAAM,aAAa,GAAG,kEAAkE,CAAC;AAEzF,SAAS,OAAO,CAAC,IAAY;IACzB,QAAQ,IAAI,EAAE;QACV,KAAK,WAAW;YACZ,OAAO,mBAAmB,CAAC;QAC/B,KAAK,SAAS;YACV,OAAO,2BAA2B,CAAC;QACvC,KAAK,SAAS;YACV,OAAO,2BAA2B,CAAC;QACvC,KAAK,QAAQ;YACT,OAAO,0BAA0B,CAAC;QAEtC,KAAK,OAAO;YACR,OAAO,uBAAuB,CAAC;QAEnC,KAAK,UAAU;YACX,OAAO,wBAAwB,CAAC;KACvC;IACD,OAAO,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,OAAO,YAAa,SAAQ,kBAAkB;IAGhD,mBAAmB;QACf,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAW;QACxB,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,OAAO,aAAa,CAAC;SAAE;QAC7C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,OAAgB,EAAE,MAAW;QACvC,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,MAAM,GAAG,aAAa,CAAC;SAAE;QAC/C,MAAM,UAAU,GAAmB;YAC/B,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACnD,gBAAgB,EAAE,CAAC,OAAe,EAAE,GAAW,EAAE,EAAE;gBAC/C,IAAI,MAAM,CAAC,MAAM,KAAK,aAAa,EAAE;oBACjC,mBAAmB,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;SACJ,CAAC;QAEF,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;YAC9B,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC;YACrB,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAA;SAC7C;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ"}