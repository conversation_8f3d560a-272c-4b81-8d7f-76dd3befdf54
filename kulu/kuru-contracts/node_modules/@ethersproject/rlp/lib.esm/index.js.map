{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,gDAAgD;AAEhD,OAAO,EAAE,QAAQ,EAAa,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAEjF,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,SAAS,eAAe,CAAC,KAAa;IAClC,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,OAAO,KAAK,EAAE;QACV,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAC7B,KAAK,KAAK,CAAC,CAAC;KACf;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAgB,EAAE,MAAc,EAAE,MAAc;IACvE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7B,MAAM,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC9C;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,OAAO,CAAC,MAA2B;IACxC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,OAAO,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,UAAS,KAAK;YACzB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE;YACtB,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;YACtC,OAAO,OAAO,CAAC;SAClB;QAED,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAEjC;IAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;QACtB,MAAM,CAAC,kBAAkB,CAAC,8BAA8B,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAC/E;IAED,MAAM,IAAI,GAAkB,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACtC,OAAO,IAAI,CAAC;KAEf;SAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;KACf;IAED,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAErC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,MAAW;IAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACpC,CAAC;AAOD,SAAS,eAAe,CAAC,IAAgB,EAAE,MAAc,EAAE,WAAmB,EAAE,MAAc;IAC1F,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,OAAO,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE;QACtC,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAE3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE5B,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC;QAChC,IAAI,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;KACJ;IAED,OAAO,EAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC;AACpD,CAAC;AAED,+CAA+C;AAC/C,SAAS,OAAO,CAAC,IAAgB,EAAE,MAAc;IAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACnB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;KAC1E;IAED,iCAAiC;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;YACzC,MAAM,CAAC,UAAU,CAAC,8BAA8B,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SACxF;QAED,MAAM,MAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAClD,MAAM,CAAC,UAAU,CAAC,6BAA6B,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SACvF;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,YAAY,GAAG,MAAM,CAAC,CAAC;KAE1F;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;KAE5D;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;YACzC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,MAAM,MAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAClD,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;QAClG,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KAEnE;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAC1E;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACpE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KACpD;IACD,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AAC1D,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,IAAe;IAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAClC,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,EAAE;QACnC,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAC/D;IACD,OAAO,OAAO,CAAC,MAAM,CAAC;AAC1B,CAAC"}