{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,gDAAgD;AAEhD,8CAAiF;AAEjF,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,SAAS,eAAe,CAAC,KAAa;IAClC,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,OAAO,KAAK,EAAE;QACV,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAC7B,KAAK,KAAK,CAAC,CAAC;KACf;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAgB,EAAE,MAAc,EAAE,MAAc;IACvE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7B,MAAM,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC9C;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,OAAO,CAAC,MAA2B;IACxC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,SAAO,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,UAAS,KAAK;YACzB,SAAO,GAAG,SAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAI,SAAO,CAAC,MAAM,IAAI,EAAE,EAAE;YACtB,SAAO,CAAC,OAAO,CAAC,IAAI,GAAG,SAAO,CAAC,MAAM,CAAC,CAAA;YACtC,OAAO,SAAO,CAAC;SAClB;QAED,IAAM,QAAM,GAAG,eAAe,CAAC,SAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,QAAM,CAAC,OAAO,CAAC,IAAI,GAAG,QAAM,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,QAAM,CAAC,MAAM,CAAC,SAAO,CAAC,CAAC;KAEjC;IAED,IAAI,CAAC,IAAA,mBAAW,EAAC,MAAM,CAAC,EAAE;QACtB,MAAM,CAAC,kBAAkB,CAAC,8BAA8B,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAC/E;IAED,IAAM,IAAI,GAAkB,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC;IAEzE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACtC,OAAO,IAAI,CAAC;KAEf;SAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;KACf;IAED,IAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAErC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAED,SAAgB,MAAM,CAAC,MAAW;IAC9B,OAAO,IAAA,eAAO,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACpC,CAAC;AAFD,wBAEC;AAOD,SAAS,eAAe,CAAC,IAAgB,EAAE,MAAc,EAAE,WAAmB,EAAE,MAAc;IAC1F,IAAM,MAAM,GAAG,EAAE,CAAC;IAElB,OAAO,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE;QACtC,IAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAE3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE5B,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC;QAChC,IAAI,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;KACJ;IAED,OAAO,EAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC;AACpD,CAAC;AAED,+CAA+C;AAC/C,SAAS,OAAO,CAAC,IAAgB,EAAE,MAAc;IAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACnB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;KAC1E;IAED,iCAAiC;IACjC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QACtB,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;YACzC,MAAM,CAAC,UAAU,CAAC,8BAA8B,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SACxF;QAED,IAAM,QAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,QAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAClD,MAAM,CAAC,UAAU,CAAC,6BAA6B,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SACvF;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,YAAY,GAAG,QAAM,CAAC,CAAC;KAE1F;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAM,QAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,GAAG,QAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,QAAM,CAAC,CAAC;KAE5D;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACzC,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;YACzC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,IAAM,QAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;QACjE,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,QAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAClD,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAChF;QAED,IAAM,MAAM,GAAG,IAAA,eAAO,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,QAAM,CAAC,CAAC,CAAC;QAClG,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,YAAY,GAAG,QAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KAEnE;SAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAM,QAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,GAAG,QAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;SAC1E;QAED,IAAM,MAAM,GAAG,IAAA,eAAO,EAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,QAAM,CAAC,CAAC,CAAC;QACpE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,QAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;KACpD;IACD,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,IAAA,eAAO,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AAC1D,CAAC;AAED,SAAgB,MAAM,CAAC,IAAe;IAClC,IAAM,KAAK,GAAG,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC;IAC7B,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAClC,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,EAAE;QACnC,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAC/D;IACD,OAAO,OAAO,CAAC,MAAM,CAAC;AAC1B,CAAC;AAPD,wBAOC"}