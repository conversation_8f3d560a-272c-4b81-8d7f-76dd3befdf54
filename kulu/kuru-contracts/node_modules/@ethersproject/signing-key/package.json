{"_ethers.alias": {"elliptic.js": "browser-elliptic.js"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}, "description": "Elliptic curve library functions for the secp256k1 curve.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/signing-key", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/signing-key", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xff1f808724833f5688798be9713ffc54260b28d51d001eda774f11d6babc4f35", "types": "./lib/index.d.ts", "version": "5.7.0"}