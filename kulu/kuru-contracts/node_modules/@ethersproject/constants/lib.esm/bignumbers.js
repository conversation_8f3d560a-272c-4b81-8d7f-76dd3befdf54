import { BigNumber } from "@ethersproject/bignumber";
const NegativeOne = ( /*#__PURE__*/BigNumber.from(-1));
const Zero = ( /*#__PURE__*/BigNumber.from(0));
const One = ( /*#__PURE__*/BigNumber.from(1));
const Two = ( /*#__PURE__*/BigNumber.from(2));
const WeiPerEther = ( /*#__PURE__*/BigNumber.from("1000000000000000000"));
const MaxUint256 = ( /*#__PURE__*/BigNumber.from("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"));
const MinInt256 = ( /*#__PURE__*/BigNumber.from("-0x8000000000000000000000000000000000000000000000000000000000000000"));
const MaxInt256 = ( /*#__PURE__*/BigNumber.from("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"));
export { NegativeOne, Zero, One, Two, WeiPerEther, MaxUint256, MinInt256, MaxInt256, };
//# sourceMappingURL=bignumbers.js.map