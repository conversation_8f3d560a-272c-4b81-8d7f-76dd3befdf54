{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/properties": "^5.7.0"}, "description": "Base-X without <PERSON><PERSON><PERSON>.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/basex", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/basex", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xa3450b5ccf02165bd79e235cc6803751f8194a7680cfc165c62ff3ee3fa6def5", "types": "./lib/index.d.ts", "version": "5.7.0"}