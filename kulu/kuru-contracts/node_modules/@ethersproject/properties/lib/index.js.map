{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,SAAgB,cAAc,CAAuB,MAAS,EAAE,IAAO,EAAE,KAAW;IAChF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;QAChC,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,KAAK;KAClB,CAAC,CAAC;AACP,CAAC;AAND,wCAMC;AAED,yDAAyD;AACzD,SAAgB,SAAS,CAAI,IAAS,EAAE,GAAW;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;SAAE;QACpC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,OAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE;YAAE,MAAM;SAAE;QACtE,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;KAC5D;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAPD,8BAOC;AASD,SAAsB,iBAAiB,CAAI,MAA+B;;;;;;oBAChE,QAAQ,GAA2B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG;wBACjE,IAAM,KAAK,GAAG,MAAM,CAAsB,GAAG,CAAC,CAAC;wBAC/C,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAxB,CAAwB,CAAC,CAAC;oBACxE,CAAC,CAAC,CAAC;oBAEa,qBAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;oBAArC,OAAO,GAAG,SAA2B;oBAE3C,sBAAO,OAAO,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,MAAM;4BAChC,KAAK,CAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;4BAC5C,OAAO,KAAK,CAAC;wBACjB,CAAC,EAAK,EAAG,CAAC,EAAC;;;;CACd;AAZD,8CAYC;AAED,SAAgB,eAAe,CAAC,MAAW,EAAE,UAAyC;IAClF,IAAI,CAAC,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QACxC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;QAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,GAAG,GAAG,EAAE,cAAc,GAAG,GAAG,EAAE,MAAM,CAAC,CAAC;SAC1F;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAVD,0CAUC;AAED,SAAgB,WAAW,CAAI,MAAS;IACpC,IAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;QAAE,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;KAAE;IACxD,OAAO,MAAM,CAAC;AAClB,CAAC;AAJD,kCAIC;AAED,IAAM,MAAM,GAA+B,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AAEzH,SAAS,SAAS,CAAC,MAAW;IAE1B,gEAAgE;IAChE,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IAEvF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QACtD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAE/C,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,KAAK,GAAQ,IAAI,CAAC;YACtB,IAAI;gBACA,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B;YAAC,OAAO,KAAK,EAAE;gBACZ,yDAAyD;gBACzD,4DAA4D;gBAC5D,SAAS;aACZ;YAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO,KAAK,CAAC;aAAE;SAC3C;QAED,OAAO,IAAI,CAAC;KACf;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,qBAAoB,OAAM,CAAC,MAAM,CAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9F,CAAC;AAED,yEAAyE;AACzE,+CAA+C;AAC/C,SAAS,SAAS,CAAC,MAAW;IAE1B,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IAEzC,kDAAkD;IAClD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,QAAQ,CAAC,IAAI,CAAC,EAAd,CAAc,CAAC,CAAC,CAAC;KAC9D;IAED,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC7B,IAAM,MAAM,GAA6B,EAAE,CAAC;QAC5C,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACtB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,KAAK,SAAS,EAAE;gBAAE,SAAS;aAAE;YACtC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SAChD;QAED,OAAO,MAAM,CAAC;KACjB;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,qBAAoB,OAAM,CAAC,MAAM,CAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9F,CAAC;AAED,SAAgB,QAAQ,CAAI,MAAS;IACjC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAFD,4BAEC;AAED;IACI,qBAAY,IAAgC;QACxC,KAAK,IAAM,GAAG,IAAI,IAAI,EAAE;YACd,IAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;IACL,CAAC;IACL,kBAAC;AAAD,CAAC,AAND,IAMC;AANY,kCAAW"}