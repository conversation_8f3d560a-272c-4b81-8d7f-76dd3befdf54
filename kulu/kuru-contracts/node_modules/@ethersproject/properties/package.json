{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/logger": "^5.7.0"}, "description": "Properties utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/properties", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/properties", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"auto-build": "npm run build -- -w", "build": "tsc -p ./tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xae19a78858ce5d385611b26f4f7d576681f7352281e38f0916bba8f3fa04f5b5", "types": "./lib/index.d.ts", "version": "5.7.0"}