{"version": 3, "file": "bignumber.js", "sourceRoot": "", "sources": ["../src.ts/bignumber.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb;;;;;;GAMG;AAEH,OAAO,GAAG,MAAM,OAAO,CAAC;AACxB,IAAO,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAEnB,OAAO,EAAkB,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAErF,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,MAAM,iBAAiB,GAAG,EAAG,CAAC;AAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC;AAKlC,MAAM,UAAU,cAAc,CAAC,KAAU;IACrC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CACtB,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5B,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3D,WAAW,CAAC,KAAK,CAAC;QAClB,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC;QAC5B,OAAO,CAAC,KAAK,CAAC,CACjB,CAAC;AACN,CAAC;AAED,6CAA6C;AAC7C,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAEjC,MAAM,OAAO,SAAS;IAIlB,YAAY,gBAAqB,EAAE,GAAW;QAC1C,IAAI,gBAAgB,KAAK,iBAAiB,EAAE;YACxC,MAAM,CAAC,UAAU,CAAC,sDAAsD,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC3G,SAAS,EAAE,iBAAiB;aAC/B,CAAC,CAAC;SACN;QAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,QAAQ,CAAC,KAAa;QAClB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,KAAa;QAChB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,GAAG;QACC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACtB,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACZ,UAAU,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;YACf,UAAU,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;YACf,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;SACvC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;YACpC,UAAU,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;SAC/C;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,EAAE,CAAC,KAAmB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;YACpC,UAAU,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SAC9C;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;YACpC,UAAU,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;SAC/C;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,KAAa;QACd,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;YAChC,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;SACxC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,GAAG,CAAC,KAAa;QACb,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;YAChC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;SACvC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,GAAG,CAAC,KAAa;QACb,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;YAChC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;SACvC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,EAAE,CAAC,KAAmB;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,EAAE,CAAC,KAAmB;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,GAAG,CAAC,KAAmB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,EAAE,CAAC,KAAmB;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACvC,CAAC;IAEA,GAAG,CAAC,KAAmB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,UAAU;QACN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;IAC/B,CAAC;IAED,QAAQ;QACJ,IAAI;YACA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;SAChC;QAAC,OAAO,KAAK,EAAE;YACZ,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SACvD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,QAAQ;QACJ,IAAI;YACA,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAClC;QAAC,OAAO,CAAC,EAAE,GAAG;QAEf,OAAO,MAAM,CAAC,UAAU,CAAC,uCAAuC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACnG,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;SACzB,CAAC,CAAC;IACP,CAAC;IAED,QAAQ;QACJ,4EAA4E;QAC5E,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,oBAAoB,EAAE;oBACvB,oBAAoB,GAAG,IAAI,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;iBACxF;aACJ;iBAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,MAAM,CAAC,UAAU,CAAC,gFAAgF,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAG,CAAC,CAAC;aAC/I;iBAAM;gBACH,MAAM,CAAC,UAAU,CAAC,+CAA+C,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAG,CAAC,CAAC;aAC9G;SACJ;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,MAAM,CAAC,GAAY;QACf,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;IAC1D,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,KAAU;QAClB,IAAI,KAAK,YAAY,SAAS,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAEjD,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC5B,IAAI,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;gBACjC,OAAO,IAAI,SAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;aACzD;YAED,IAAI,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBAC3B,OAAO,IAAI,SAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACjE;YAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SAChF;QAED,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC5B,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,UAAU,CAAC,WAAW,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;aACpD;YAED,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACzC,UAAU,CAAC,UAAU,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;aACnD;YAED,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;SACxC;QAED,MAAM,QAAQ,GAAQ,KAAK,CAAC;QAE5B,IAAI,OAAM,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC/B,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC9C;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YACnB,OAAO,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC5C;QAED,IAAI,QAAQ,EAAE;YAEV,qCAAqC;YACrC,IAAI,QAAQ,CAAC,WAAW,EAAE;gBACtB,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACnC,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;oBAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAC9B;aAEJ;iBAAM;gBACH,6DAA6D;gBAC7D,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAExB,gBAAgB;gBAChB,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE;oBAC9C,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;iBACtB;gBAED,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;oBAC1B,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;wBACvE,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBAC9B;iBACJ;aACJ;SACJ;QAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAChF,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAU;QACzB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;CACJ;AAED,2BAA2B;AAC3B,SAAS,KAAK,CAAC,KAAkB;IAE7B,iCAAiC;IACjC,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;QAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;KACpC;IAED,0EAA0E;IAC1E,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,8BAA8B;QAC9B,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAE3B,sDAAsD;QACtD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAAE,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SAAE;QAEnF,uCAAuC;QACvC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,uBAAuB;QACvB,IAAI,KAAK,KAAK,MAAM,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAEvC,mBAAmB;QACnB,OAAO,GAAG,GAAG,KAAK,CAAC;KACtB;IAED,+BAA+B;IAC/B,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;QAAE,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;KAAE;IAE7D,iBAAiB;IACjB,IAAI,KAAK,KAAK,IAAI,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IAEtC,8BAA8B;IAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QAAE,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KAAE;IAE7D,sCAAsC;IACtC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;QACzD,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KACrC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,WAAW,CAAC,KAAS;IAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,IAAI,CAAC,KAAmB;IAC7B,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;IAChD,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChB,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KAC/C;IACD,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,UAAU,CAAC,KAAa,EAAE,SAAiB,EAAE,KAAW;IAC7D,MAAM,MAAM,GAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;IAC3D,IAAI,KAAK,IAAI,IAAI,EAAE;QAAE,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;KAAE;IAE5C,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACzE,CAAC;AAED,8BAA8B;AAC9B,MAAM,UAAU,WAAW,CAAC,KAAa;IACrC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC5C,CAAC;AAED,8BAA8B;AAC9B,MAAM,UAAU,WAAW,CAAC,KAAa;IACrC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC5C,CAAC"}