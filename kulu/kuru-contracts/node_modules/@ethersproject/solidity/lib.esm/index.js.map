{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC1E,OAAO,EAAE,SAAS,IAAI,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACtE,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACjD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACpD,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAEtD,MAAM,KAAK,GAAG,kEAAkE,CAAC;AAEjF,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAGnC,SAAS,KAAK,CAAC,IAAY,EAAE,KAAU,EAAE,OAAiB;IACtD,QAAO,IAAI,EAAE;QACT,KAAK,SAAS;YACV,IAAI,OAAO,EAAE;gBAAE,OAAO,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aAAE;YAC3C,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,OAAO;YACR,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,MAAM;YACP,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,OAAO,EAAE;gBAAE,OAAO,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aAAE;YAC3C,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC9B;IAED,IAAI,KAAK,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACrC,IAAI,KAAK,EAAE;QACP,mCAAmC;QACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAA;QAEtC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE;YACzF,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;SACjE;QAED,IAAI,OAAO,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC;SAAE;QAE5B,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;KACnC;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,EAAE;QACP,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE;YACtD,MAAM,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;SAChE;QACD,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,UAAU,KAAK,IAAI,EAAE;YACrC,MAAM,CAAC,kBAAkB,CAAC,qBAAsB,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;SAC3E;QACD,IAAI,OAAO,EAAE;YAAE,OAAO,QAAQ,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAAE;QACnE,OAAO,KAAK,CAAC;KAChB;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;YACvB,MAAM,CAAC,kBAAkB,CAAC,4BAA6B,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;SAClF;QACD,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,KAAK,CAAC,OAAO,CAAC,UAAS,KAAK;YACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;KACzB;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AAClE,CAAC;AAED,oBAAoB;AAEpB,MAAM,UAAU,IAAI,CAAC,KAA4B,EAAE,MAA0B;IACzE,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;QAC/B,MAAM,CAAC,kBAAkB,CAAC,oDAAoD,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;KACpG;IACD,MAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,KAAK,CAAC,OAAO,CAAC,UAAS,IAAI,EAAE,KAAK;QAC9B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,KAA4B,EAAE,MAA0B;IAC9E,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,KAA4B,EAAE,MAA0B;IAC3E,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC3C,CAAC"}