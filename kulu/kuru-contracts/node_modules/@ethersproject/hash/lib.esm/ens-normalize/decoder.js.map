{"version": 3, "file": "decoder.js", "sourceRoot": "", "sources": ["../../src.ts/ens-normalize/decoder.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AAKH,uEAAuE;AACvE,SAAS,IAAI,CAAC,KAAiB,EAAE,KAAc;IAC3C,IAAI,KAAK,IAAI,IAAI,EAAE;QAAE,KAAK,GAAG,CAAC,CAAC;KAAE;IACjC,MAAM,MAAM,GAAe,EAAE,CAAC;IAE9B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAE/B,MAAM,QAAQ,GAAG,UAAU,GAAe,EAAE,KAAa;QACrD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,GAAQ;YAChC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACjC,QAAQ,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;aAC5B;iBAAM;gBACJ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACvB,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,WAAW,CAAyE,KAAoB;IAC7G,MAAM,MAAM,GAA+B,EAAG,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;KAC/B;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC/C,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,SAAS,GAAG,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAE7D,6BAA6B;IAC7B,IAAI,YAAY,GAAG,GAAG,EAAE,CAAC;IACzB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,+BAA+B;IACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;QACtC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;KACzB;IAED,4DAA4D;IAC5D,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;IACjB,IAAI,WAAW,GAAG,GAAG,CAAC;IACtB,GAAG,IAAI,IAAI,CAAC;IAEZ,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,SAAS,QAAQ;QAChB,IAAI,UAAU,IAAI,CAAC,EAAE;YACpB,sCAAsC;YACtC,gCAAgC;YAChC,WAAW,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YAChD,UAAU,GAAG,CAAC,CAAC;SACf;QACD,OAAO,CAAC,WAAW,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,GAAG,EAAE,CAAC;IACb,MAAM,IAAI,GAAG,SAAA,CAAC,EAAE,CAAC,CAAA,CAAC;IAClB,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC;IACxB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;IACvB,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;IAEtB,gBAAgB;IAChB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAE,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC;IAEpE,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,qBAAqB;IACvC,OAAO,IAAI,EAAE;QACZ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACrE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,YAAY,CAAC;QACvB,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,gBAAgB;YACzC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrB,GAAG,GAAG,GAAG,CAAC;aACV;iBAAM;gBACN,KAAK,GAAG,GAAG,CAAC;aACZ;SACD;QACD,IAAI,KAAK,IAAI,CAAC;YAAE,MAAM,CAAC,2BAA2B;QAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAK,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;QAC1D,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7B,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,EAAE,CAAC;YAC/C,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YACpB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;SACxB;QACD,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE;YACrB,QAAQ,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC;YAC7E,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YACpB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;SACjC;QACD,GAAG,GAAG,CAAC,CAAC;QACR,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAClB;IACD,IAAI,MAAM,GAAG,YAAY,GAAG,CAAC,CAAC;IAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACtB,QAAQ,CAAC,GAAG,MAAM,EAAE;YACnB,KAAK,CAAC,CAAC,CAAC,OAAO,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YACtH,KAAK,CAAC,CAAC,CAAC,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YACrF,KAAK,CAAC,CAAC,CAAC,OAAO,MAAM,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;YAC7C,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACtB;IACF,CAAC,CAAC,CAAC;AACJ,CAAC;AAGD,oDAAoD;AACpD,MAAM,UAAU,YAAY,CAAC,CAAU;IACtC,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACvB,CAAC;AACD,MAAM,UAAU,uBAAuB,CAAC,KAAc;IACrD,OAAO,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,sCAAsC;AACtC,MAAM,UAAU,MAAM,CAAC,CAAS;IAC/B,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAE,IAAc;IAC7C,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;IAC9C,OAAO,CAAC,CAAC;AACV,CAAC;AAED,SAAS,cAAc,CAAC,CAAS,EAAE,IAAc;IAChD,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;IAC3D,OAAO,CAAC,CAAC;AACV,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAE,IAAc;IAC7C,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9D,OAAO,CAAC,CAAC;AACV,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAc,EAAE,MAA+B;IAC7E,IAAI,CAAC,GAAG,cAAc,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;IACf,IAAI,EAAE,GAAG,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACjC,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrB;KACJ;IACD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,oBAAoB;AACpB,qCAAqC;AACrC,mCAAmC;AACnC,MAAM,UAAU,eAAe,CAAC,IAAc;IAC7C,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,OAAO,IAAI,EAAE;QACZ,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM;QAClB,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACrC;IACD,OAAO,IAAI,EAAE;QACZ,IAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,GAAG,CAAC;YAAE,MAAM;QACjB,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KAC1C;IACD,OAAO,WAAW,CAAwB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAC,IAAc;IACxD,IAAI,CAAC,GAAG,EAAE,CAAC;IACX,OAAO,IAAI,EAAE;QACZ,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM;QAClB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACV;IACD,OAAO,CAAC,CAAC;AACV,CAAC;AAED,SAAS,eAAe,CAAC,CAAS,EAAE,CAAS,EAAE,IAAc;IACzD,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KACxD;IACD,OAAO,CAAC,CAAC;AACb,CAAC;AAGD,SAAS,iBAAiB,CAAC,CAAS,EAAE,IAAc;IACnD,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;IACpB,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;IAChB,IAAI,EAAE,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAI,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9C,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACzB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,qBAAqB;QACrB,4CAA4C;QAC5C,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,CAAS,EAAE,IAAc;IACxD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;IACnB,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACtC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC;AAeD,MAAM,UAAU,eAAe,CAAC,IAAc;IAC7C,IAAI,MAAM,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3D,OAAO,IAAI,EAAE,CAAC;IACd,SAAS,IAAI;QACZ,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,OAAO,IAAI,EAAE;YACZ,IAAI,IAAI,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC3C,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;gBAAE,MAAM;YAC5B,QAAQ,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC;SAClD;QACC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;QACxE,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;QAClB,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;QACrB,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAC,CAAC,CAAC;QACpB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACxB,IAAI,KAAK,CAAC,CAAC;QACX,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;QACrB,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC;QACtB,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;IAC9C,CAAC;AACF,CAAC"}