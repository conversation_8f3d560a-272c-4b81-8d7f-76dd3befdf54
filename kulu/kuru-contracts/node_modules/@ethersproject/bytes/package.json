{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/logger": "^5.7.0"}, "description": "Bytes utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/bytes", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/bytes", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"auto-build": "npm run build -- -w", "build": "tsc -p ./tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x5ce4e7b7716e28697fe07abcee74e4e8b4fa3b919b2807322ad6a282990db523", "types": "./lib/index.d.ts", "version": "5.7.0"}