{"_ethers.alias": {"pbkdf2.js": "browser-pbkdf2.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/pbkdf2": "./lib/browser-pbkdf2.js"}, "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/sha2": "^5.7.0"}, "description": "The PBKDF2 password-pbased key derivation function for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers", "pbkdf2"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/pbkdf2", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/pbkdf2", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x56b0994e0126486fe570d5ce41d58a0ad8e46ead8509d235b4cde119d7b4fe21", "types": "./lib/index.d.ts", "version": "5.7.0"}