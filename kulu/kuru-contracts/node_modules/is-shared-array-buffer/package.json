{"name": "is-shared-array-buffer", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS SharedArrayBuffer?", "license": "MIT", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only --", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-shared-array-buffer.git"}, "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "devDependencies": {"@ljharb/eslint-config": "^20.2.3", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.2.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "nyc": "^10.3.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "tape": "^5.5.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bind": "^1.0.2"}}