{"name": "ethereum-bloom-filters", "version": "1.0.10", "description": "Ability to test bloom filters for ethereum.", "main": "dist/index.js", "scripts": {"build": "tsc && npm run build-web-scripts", "watch": "tsc --watch", "test": "jest", "publish-sdk": "npm run build && npm publish --access public", "build-web-scripts": "tsc && gulp"}, "repository": {"type": "git", "url": "git+https://github.com/joshstevens19/ethereum-bloom-filters.git"}, "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/joshstevens19/ethereum-bloom-filters/issues"}, "homepage": "https://github.com/joshstevens19/ethereum-bloom-filters#readme", "keywords": ["ethereum", "blockchain", "blooms", "bloom", "bloom filters"], "dependencies": {"js-sha3": "^0.8.0"}, "devDependencies": {"@types/jest": "^24.0.18", "browserify": "^16.5.0", "gulp": "^4.0.2", "gulp-uglify-es": "^1.0.4", "jest": "^24.9.0", "ts-jest": "^24.1.0", "tslint": "^5.20.0", "typescript": "^3.6.4", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0"}}