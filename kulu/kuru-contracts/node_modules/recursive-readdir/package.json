{"author": "Jamison Dance <<EMAIL>> (http://jamison.dance.com/)", "name": "recursive-readdir", "description": "Get an array of all files in a directory and subdirectories.", "license": "MIT", "version": "2.2.3", "repository": {"type": "git", "url": "git://github.com/jergason/recursive-readdir.git"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "mocha test/"}, "keywords": ["directory", "lister"], "engines": {"node": ">=6.0.0"}, "dependencies": {"minimatch": "^3.0.5"}, "devDependencies": {"mocha": "6.1.4"}}