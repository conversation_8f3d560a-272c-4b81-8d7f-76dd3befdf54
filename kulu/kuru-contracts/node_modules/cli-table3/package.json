{"name": "cli-table3", "version": "0.5.1", "description": "Pretty unicode tables for the command line. Based on the original cli-table.", "main": "index.js", "types": "index.d.ts", "files": ["src/", "index.d.ts", "index.js"], "directories": {"test": "test"}, "dependencies": {"object-assign": "^4.1.0", "string-width": "^2.1.1"}, "devDependencies": {"ansi-256-colors": "^1.1.0", "cli-table": "^0.3.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.0", "jest": "^23.1.0", "jest-runner-eslint": "^0.6.0", "lerna-changelog": "^0.8.0", "prettier": "1.13.7"}, "optionalDependencies": {"colors": "^1.1.2"}, "scripts": {"changelog": "lerna-changelog", "docs": "node ./scripts/update-docs.js", "prettier": "prettier --write '{examples,lib,scripts,src,test}/**/*.js'", "test": "jest --color", "test:watch": "jest --color --watchAll --notify"}, "repository": {"type": "git", "url": "https://github.com/cli-table/cli-table3.git"}, "keywords": ["node", "command", "line", "cli", "table", "tables", "tabular", "unicode", "colors", "grid"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/cli-table/cli-table3/issues"}, "homepage": "https://github.com/cli-table/cli-table3", "engines": {"node": ">=6"}, "changelog": {"repo": "cli-table/cli-table3", "labels": {"breaking": ":boom: Breaking Change", "enhancement": ":rocket: Enhancement", "bug": ":bug: Bug Fix", "documentation": ":memo: Documentation", "internal": ":house: Internal"}}, "jest": {"projects": [{"displayName": "test", "testMatch": ["**/test/**/*-test(s)?.js"]}, {"runner": "jest-runner-es<PERSON>", "displayName": "lint", "testMatch": ["<rootDir>/examples/**/*.js", "<rootDir>/lib/**/*.js", "<rootDir>/scripts/**/*.js", "<rootDir>/src/**/*.js", "<rootDir>/test/**/*.js"]}]}, "prettier": {"printWidth": 120, "tabWidth": 2, "singleQuote": true, "trailingComma": "es5"}}