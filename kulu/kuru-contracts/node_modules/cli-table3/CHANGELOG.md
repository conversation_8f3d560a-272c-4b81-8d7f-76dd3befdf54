# Changelog

## v0.5.1 (2018-07-19)

#### :rocket: Enhancement
* [#21](https://github.com/cli-table/cli-table3/pull/21) Import type definition from `@types/cli-table2` ([@Turbo87](https://github.com/Turbo87))

#### Committers: 1
- <PERSON> ([Turbo87](https://github.com/Turbo87))


## v0.5.0 (2018-06-11)

#### :boom: Breaking Change
* [#2](https://github.com/cli-table/cli-table3/pull/2) Update Node version requirements. ([@Turbo87](https://github.com/Turbo87))

#### :memo: Documentation
* [#11](https://github.com/cli-table/cli-table3/pull/11) Update Documentation. ([@Turbo87](https://github.com/Turbo87))

#### :house: Internal
* [#16](https://github.com/cli-table/cli-table3/pull/16) Replace `kind-of` dependency with `typeof` and `Array.isArray()`. ([@Turbo87](https://github.com/Turbo87))
* [#15](https://github.com/cli-table/cli-table3/pull/15) Remove Gulp. ([@Turbo87](https://github.com/Turbo87))
* [#13](https://github.com/cli-table/cli-table3/pull/13) Use ES6 class syntax and `let/const`. ([@Turbo87](https://github.com/Turbo87))
* [#12](https://github.com/cli-table/cli-table3/pull/12) Add ESLint and Prettier. ([@Turbo87](https://github.com/Turbo87))
* [#10](https://github.com/cli-table/cli-table3/pull/10) chore: use yarn cache. ([@DanielRuf](https://github.com/DanielRuf))
* [#9](https://github.com/cli-table/cli-table3/pull/9) Use Jest for testing. ([@Turbo87](https://github.com/Turbo87))
* [#3](https://github.com/cli-table/cli-table3/pull/3) Add `yarn.lock` file. ([@Turbo87](https://github.com/Turbo87))
* [#1](https://github.com/cli-table/cli-table3/pull/1) Skip broken test. ([@Turbo87](https://github.com/Turbo87))

#### Committers: 2
- Daniel Ruf ([DanielRuf](https://github.com/DanielRuf))
- Tobias Bieniek ([Turbo87](https://github.com/Turbo87))


## v0.4.0 (2018-06-10)

First official release as `cli-table3`. Changes compares to `cli-table2` v0.2.0:

#### :rocket: Enhancement
* [#27](https://github.com/jamestalmage/cli-table2/pull/27) Remove "lodash" dependency. ([@Turbo87](https://github.com/Turbo87))

#### :bug: Bug Fix
* [#29](https://github.com/jamestalmage/cli-table2/pull/29) Fix wordWrap with colSpan. ([@mmurphy](https://github.com/mmurphy))
* [#24](https://github.com/jamestalmage/cli-table2/pull/24) Fixing the runtime error when content is truncated. ([@sthadeshwar](https://github.com/sthadeshwar))

#### :memo: Documentation
* [#41](https://github.com/jamestalmage/cli-table2/pull/41) Create LICENSE. ([@GantMan](https://github.com/GantMan))

#### :house: Internal
* [#26](https://github.com/jamestalmage/cli-table2/pull/26) package.json: Whitelist JS files ([@Turbo87](https://github.com/Turbo87))

#### Committers: 4
- Gant Laborde ([GantMan](https://github.com/GantMan))
- Martin Murphy ([mmurphy](https://github.com/mmurphy))
- Satyajit Thadeshwar ([sthadeshwar](https://github.com/sthadeshwar))
- Tobias Bieniek ([Turbo87](https://github.com/Turbo87))
