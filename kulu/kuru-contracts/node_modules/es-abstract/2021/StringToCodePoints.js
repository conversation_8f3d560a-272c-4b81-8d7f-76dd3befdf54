'use strict';

var GetIntrinsic = require('get-intrinsic');

var $TypeError = GetIntrinsic('%TypeError%');

var callBound = require('call-bind/callBound');

var $push = callBound('Array.prototype.push');

var CodePointAt = require('./CodePointAt');
var Type = require('./Type');

// https://262.ecma-international.org/12.0/#sec-stringtocodepoints

module.exports = function StringToCodePoints(string) {
	if (Type(string) !== 'String') {
		throw new $TypeError('Assertion failed: `string` must be a String');
	}
	var codePoints = [];
	var size = string.length;
	var position = 0;
	while (position < size) {
		var cp = CodePointAt(string, position);
		$push(codePoints, cp['[[CodePoint]]']);
		position += cp['[[CodeUnitCount]]'];
	}
	return codePoints;
};
