{"name": "object.assign", "version": "4.1.4", "author": "<PERSON>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "test": "npm run tests-only && npm run test:ses", "posttest": "aud --production", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "test:shim": "nyc node test/shimmed", "test:implementation": "nyc node test", "test:ses": "node test/ses-compat", "lint": "eslint .", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublishOnly": "safe-publish-latest && npm run build", "prepublish": "not-in-publish || npm run prepublishOnly"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.2.3", "@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "browserify": "^16.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has": "^1.0.3", "has-strict-mode": "^1.0.1", "is": "^3.3.0", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "ses": "^0.11.1", "tape": "^5.5.3"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}}