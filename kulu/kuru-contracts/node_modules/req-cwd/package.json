{"name": "req-cwd", "version": "2.0.0", "description": "Require a module like `require()` but from the current working directory", "license": "MIT", "repository": "sindresorhus/req-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "dependencies": {"req-from": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}