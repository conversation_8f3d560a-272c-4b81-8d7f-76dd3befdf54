{"name": "@assemblyscript/loader", "description": "A convenient loader for AssemblyScript modules.", "keywords": ["assemblyscript", "loader", "glue", "interop", "webassembly", "wasm"], "version": "0.19.10", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://assemblyscript.org", "repository": {"type": "git", "url": "https://github.com/AssemblyScript/assemblyscript.git", "directory": "lib/loader"}, "bugs": {"url": "https://github.com/AssemblyScript/assemblyscript/issues"}, "type": "module", "main": "index.js", "types": "index.d.ts", "exports": {"import": "./index.js", "require": "./umd/index.js"}, "scripts": {"asbuild": "npm run asbuild:default && npm run asbuild:legacy", "asbuild:default": "node ../../bin/asc tests/assembly/index.ts --binaryFile tests/build/default.wasm --exportRuntime", "asbuild:legacy": "node ../../bin/asc tests/assembly/index.ts --disable mutable-globals --binaryFile tests/build/legacy.wasm --exportRuntime", "build": "npx esm2umd loader index.js > umd/index.js", "test": "node tests && node tests/umd"}, "files": ["index.d.ts", "index.js", "package.json", "umd/index.d.ts", "umd/index.js", "umd/package.json", "README.md"], "devDependencies": {"esm2umd": "^0.1.2"}}