{"name": "assemblyscript", "description": "Definitely not a TypeScript to WebAssembly compiler.", "keywords": ["typescript", "webassembly", "compiler", "assemblyscript", "wasm"], "version": "0.19.10", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "jhwgh1968 <<EMAIL>>", "<PERSON> <jeffrey<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Surma <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "ncave <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Valeria Viana <PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <martin.f<PERSON><PERSON><PERSON>@vikinganalytics.se>", "forcepusher <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Chance <PERSON> <*****************>", "<PERSON> <peters<PERSON><PERSON><EMAIL>>", "ookangzheng <<EMAIL>>", "yjhm<PERSON>dy <<EMAIL>>", "bnbarak <<EMAIL>>", "<PERSON> <colin.eber<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "Roman F. <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Congcong Cai <<EMAIL>>"], "license": "Apache-2.0", "homepage": "https://assemblyscript.org", "repository": {"type": "git", "url": "https://github.com/AssemblyScript/assemblyscript.git"}, "bugs": {"url": "https://github.com/AssemblyScript/assemblyscript/issues"}, "dependencies": {"binaryen": "101.0.0-nightly.20210723", "long": "^4.0.0"}, "type": "commonjs", "main": "index.js", "types": "index.d.ts", "exports": {".": "./index.js", "./std/portable": "./std/portable/index.js", "./lib/loader": {"import": "./lib/loader/index.js", "require": "./lib/loader/umd/index.js"}, "./lib/rtrace": {"import": "./lib/rtrace/index.js", "require": "./lib/rtrace/umd/index.js"}, "./*": "./*.js", "./cli/asc": "./cli/asc.js", "./cli/transform": "./cli/transform.js", "./cli/util/options": "./cli/util/options.js", "./dist/assemblyscript": "./dist/assemblyscript.js", "./dist/asc": "./dist/asc.js"}, "bin": {"asc": "bin/asc", "asinit": "bin/asinit"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/assemblyscript"}, "files": ["lib/loader/index.d.ts", "lib/loader/index.js", "lib/loader/package.json", "lib/loader/umd/index.d.ts", "lib/loader/umd/index.js", "lib/loader/umd/package.json", "lib/loader/README.md", "lib/rtrace/index.d.ts", "lib/rtrace/index.js", "lib/rtrace/package.json", "lib/rtrace/umd/index.d.ts", "lib/rtrace/umd/index.js", "lib/rtrace/umd/package.json", "lib/rtrace/README.md", "bin/", "cli/", "dist/", "index.d.ts", "index.js", "LICENSE", "NOTICE", "package.json", "package-lock.json", "README.md", "std/", "tsconfig-base.json"]}