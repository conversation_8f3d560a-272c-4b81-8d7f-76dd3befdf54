{"version": {"category": "General", "description": "Prints just the compiler's version and exits.", "type": "b", "alias": "v"}, "help": {"category": "General", "description": "Prints this message and exits.", "type": "b", "alias": "h"}, "noColors": {"category": "General", "description": "Disables terminal colors.", "type": "b", "default": false}, "config": {"category": "General", "description": "Configuration file to apply. CLI arguments take precedence.", "type": "s", "cliOnly": true}, "target": {"category": "General", "description": "Target configuration to use. Defaults to 'release'.", "type": "s", "cliOnly": true}, "optimize": {"category": "Optimization", "description": ["Optimizes the module. Typical shorthands are:", "", " Default optimizations   -O", " Make a release build    -O --noAssert", " Make a debug build      --debug", " Optimize for speed      -Ospeed", " Optimize for size       -Osize", ""], "type": "b", "alias": "O"}, "optimizeLevel": {"category": "Optimization", "description": "How much to focus on optimizing code. [0-3]", "type": "i"}, "shrinkLevel": {"category": "Optimization", "description": "How much to focus on shrinking code size. [0-2, s=1, z=2]", "type": "i"}, "converge": {"category": "Optimization", "description": "Re-optimizes until no further improvements can be made.", "type": "b", "default": false}, "noAssert": {"category": "Optimization", "description": "Replaces assertions with just their value without trapping.", "type": "b", "default": false}, "outFile": {"category": "Output", "description": "Specifies the output file. File extension indicates format.", "type": "s", "alias": "o", "isPath": true}, "binaryFile": {"category": "Output", "description": "Specifies the binary output file (.wasm).", "type": "s", "alias": "b", "isPath": true}, "textFile": {"category": "Output", "description": "Specifies the text output file (.wat).", "type": "s", "alias": "t", "isPath": true}, "jsFile": {"category": "Output", "description": "Specifies the JavaScript (via wasm2js) output file (.js).", "type": "s", "alias": "j", "isPath": true}, "idlFile": {"category": "Output", "description": "Specifies the WebIDL output file (.webidl).", "type": "s", "alias": "i", "isPath": true}, "tsdFile": {"category": "Output", "description": "Specifies the TypeScript definition output file (.d.ts).", "type": "s", "alias": "d", "isPath": true}, "sourceMap": {"category": "Debugging", "description": ["Enables source map generation. Optionally takes the URL", "used to reference the source map from the binary file."], "type": "s"}, "debug": {"category": "Debugging", "description": "Enables debug information in emitted binaries.", "type": "b", "default": false}, "importMemory": {"category": "Features", "description": "Imports the memory from 'env.memory'.", "type": "b", "default": false}, "noExportMemory": {"category": "Features", "description": "Does not export the memory as 'memory'.", "type": "b", "default": false}, "initialMemory": {"category": "Features", "description": "Sets the initial memory size in pages.", "type": "i", "default": 0}, "maximumMemory": {"category": "Features", "description": "Sets the maximum memory size in pages.", "type": "i", "default": 0}, "sharedMemory": {"category": "Features", "description": "Declare memory as shared. Requires maximumMemory.", "type": "b", "default": false}, "importTable": {"category": "Features", "description": "Imports the function table from 'env.table'.", "type": "b", "default": false}, "exportTable": {"category": "Features", "description": "Exports the function table as 'table'.", "type": "b", "default": false}, "runtime": {"category": "Features", "description": ["Specifies the runtime variant to include in the program.", "", " incremental  TLSF + incremental GC (default)", " minimal      TLSF + lightweight GC invoked externally", " stub         Minimal runtime stub (never frees)", " ...          Path to a custom runtime implementation", ""], "type": "s", "default": "incremental"}, "exportRuntime": {"category": "Features", "description": "Exports the runtime helpers (__new, __collect etc.).", "type": "b", "default": false}, "stackSize": {"category": "Features", "description": ["Overrides the stack size. Only relevant for incremental GC", "or when using a custom runtime that requires stack space.", "Defaults to 0 without and to 16384 with incremental GC."], "default": 0, "type": "i"}, "explicitStart": {"category": "Features", "description": "Exports an explicit '_start' function to call.", "type": "b", "default": false}, "enable": {"category": "Features", "description": ["Enables WebAssembly features being disabled by default.", "", " nontrapping-f2i     Non-trapping float to integer ops.", " bulk-memory         Bulk memory operations.", " simd                SIMD types and operations.", " threads             Threading and atomic operations.", " reference-types     Reference types and operations.", " gc                  Garbage collection (WIP).", ""], "TODO_doesNothingYet": [" exception-handling  Exception handling.", " tail-calls          Tail call operations.", " multi-value         Multi value types.", " memory64            Memory64 operations."], "type": "S", "mutuallyExclusive": "disable"}, "disable": {"category": "Features", "description": ["Disables WebAssembly features being enabled by default.", "", " mutable-globals     Mutable global imports and exports.", " sign-extension      Sign-extension operations", ""], "type": "S", "mutuallyExclusive": "enable"}, "use": {"category": "Features", "description": ["Aliases a global object under another name, e.g., to switch", "the default 'Math' implementation used: --use Math=JSMath", "Can also be used to introduce an integer constant."], "type": "S", "alias": "u"}, "lowMemoryLimit": {"category": "Features", "description": "Enforces very low (<64k) memory constraints.", "default": 0, "type": "i"}, "memoryBase": {"category": "Linking", "description": "Sets the start offset of emitted memory segments.", "type": "i", "default": 0}, "tableBase": {"category": "Linking", "description": "Sets the start offset of emitted table elements.", "type": "i", "default": 0}, "transform": {"category": "API", "description": "Specifies the path to a custom transform to 'require'.", "type": "S", "isPath": true, "useNodeResolution": true}, "trapMode": {"category": "<PERSON><PERSON><PERSON>", "description": ["Sets the trap mode to use.", "", " allow  Allow trapping operations. This is the default.", " clamp  Replace trapping operations with clamping semantics.", " js     Replace trapping operations with JS semantics.", ""], "type": "s", "default": "allow"}, "runPasses": {"category": "<PERSON><PERSON><PERSON>", "description": ["Specifies additional <PERSON><PERSON><PERSON> passes to run after other", "optimizations, if any. See: Binaryen/src/passes/pass.cpp"], "type": "s"}, "noValidate": {"category": "<PERSON><PERSON><PERSON>", "description": "Skips validating the module using Binaryen.", "type": "b", "default": false}, "baseDir": {"description": "Specifies the base directory of input and output files.", "type": "s", "default": "."}, "extension": {"description": "Specifies an alternative file extension to use.", "type": "s", "cliOnly": true}, "noUnsafe": {"description": ["Disallows the use of unsafe features in user code.", "Does not affect library files and external modules."], "type": "b", "default": false}, "noEmit": {"description": "Performs compilation as usual but does not emit code.", "type": "b", "default": false}, "showConfig": {"description": "Print computed compiler options and exit.", "type": "b", "default": false}, "measure": {"description": "Prints measuring information on I/O and compile times.", "type": "b", "default": false}, "pedantic": {"description": "Make yourself sad for no good reason.", "type": "b", "default": false}, "lib": {"description": ["Adds one or multiple paths to custom library components and", "uses exports of all top-level files at this path as globals."], "type": "S", "isPath": true}, "path": {"description": ["Adds one or multiple paths to package resolution, similar", "to node_modules. Prefers an 'ascMain' entry in a package's", "package.json and falls back to an inner 'assembly/' folder."], "type": "S", "isPath": true}, "traceResolution": {"description": "Enables tracing of package resolution.", "type": "b", "default": false}, "listFiles": {"description": "Lists files to be compiled and exits.", "type": "b", "default": false}, "wasm": {"description": "Uses the specified Wasm binary of the compiler.", "type": "s"}, " ...": {"description": "Specifies node.js options (CLI only). See: node --help"}, "-Os": {"value": {"optimizeLevel": 0, "shrinkLevel": 1}}, "-Oz": {"value": {"optimizeLevel": 0, "shrinkLevel": 2}}, "-O0": {"value": {"optimizeLevel": 0, "shrinkLevel": 0}}, "-O1": {"value": {"optimizeLevel": 1, "shrinkLevel": 0}}, "-O2": {"value": {"optimizeLevel": 2, "shrinkLevel": 0}}, "-O3": {"value": {"optimizeLevel": 3, "shrinkLevel": 0}}, "-O0s": {"value": {"optimizeLevel": 0, "shrinkLevel": 1}}, "-O1s": {"value": {"optimizeLevel": 1, "shrinkLevel": 1}}, "-O2s": {"value": {"optimizeLevel": 2, "shrinkLevel": 1}}, "-O3s": {"value": {"optimizeLevel": 3, "shrinkLevel": 1}}, "-O0z": {"value": {"optimizeLevel": 0, "shrinkLevel": 2}}, "-O1z": {"value": {"optimizeLevel": 1, "shrinkLevel": 2}}, "-O2z": {"value": {"optimizeLevel": 2, "shrinkLevel": 2}}, "-O3z": {"value": {"optimizeLevel": 3, "shrinkLevel": 2}}, "-Ospeed": {"value": {"optimizeLevel": 3, "shrinkLevel": 0}}, "-Osize": {"value": {"optimizeLevel": 0, "shrinkLevel": 2, "converge": true}}}