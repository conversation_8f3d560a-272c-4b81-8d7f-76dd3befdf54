{"version": 3, "sources": ["webpack://asc/webpack/universalModuleDefinition", "webpack://asc/./asc.js", "webpack://asc/./shim/fs.js", "webpack://asc/./shim/path.js", "webpack://asc/./shim/process.js", "webpack://asc/./util/colors.js", "webpack://asc/./util/find.js", "webpack://asc/./util/mkdirp.js", "webpack://asc/./util/options.js", "webpack://asc/./util/utf8.js", "webpack://asc/../lib/loader/umd/index.js", "webpack://asc/../lib/rtrace/umd/index.js", "webpack://asc/external \"assemblyscript\"", "webpack://asc/external \"binaryen\"", "webpack://asc/webpack/bootstrap", "webpack://asc/webpack/runtime/global", "webpack://asc/webpack/startup"], "names": ["root", "factory", "exports", "module", "require", "e", "define", "amd", "self", "this", "__WEBPACK_EXTERNAL_MODULE__911__", "__WEBPACK_EXTERNAL_MODULE__525__", "fs", "path", "process", "utf8", "colorsUtil", "optionsUtil", "mkdirp", "find", "binaryen", "g", "dynrequire", "WIN", "platform", "EOL", "SEP", "toUpperSnakeCase", "str", "replace", "toUpperCase", "setupExtension", "ext", "startsWith", "ext_d", "re", "RegExp", "re_d", "re_except_d", "re_index", "defaultExtension", "__wrap", "ptrOrObj", "wrapperClass", "wrap", "assemblyscript", "__newString", "__getString", "__pin", "__unpin", "__collect", "Object", "defineProperty", "get", "ready", "removeAllListeners", "wasm<PERSON>rg", "argv", "findIndex", "arg", "binaryPath", "splice", "loader", "rtrace", "err", "info", "console", "log", "get<PERSON><PERSON>ory", "memory", "gcProfile", "length", "writeFileSync", "timestamp", "Date", "now", "JSON", "stringify", "join", "instantiateSync", "readFileSync", "install", "_start", "loadAssemblyScriptWasm", "register", "project", "skip<PERSON><PERSON>re", "compilerOptions", "target", "e_ts", "Error", "stack", "loadAssemblyScriptJS", "ptr", "_", "loadAssemblyScript", "isBundle", "version", "options", "libraryPrefix", "LIBRARY_PREFIX", "valueOf", "defaultOptimizeLevel", "defaultShrinkLevel", "libraryFiles", "libDir", "bundled", "files", "for<PERSON>ach", "file", "definitionFiles", "readDefinition", "name", "assembly", "portable", "compileString", "sources", "output", "create", "stdout", "createMemoryStream", "stderr", "keys", "key", "val", "opt", "type", "push", "Array", "isArray", "String", "main", "concat", "readFile", "prototype", "hasOwnProperty", "call", "writeFile", "contents", "listFiles", "callback", "bundleMinorVersion", "bundleMajorVersion", "bundlePatchVersion", "versionParts", "split", "parseInt", "readFileNode", "writeFileNode", "listFilesNode", "stats", "readTime", "readCount", "writeTime", "writeCount", "parseTime", "parseCount", "initializeTime", "initializeCount", "compileTime", "compileCount", "emitTime", "emitCount", "validateTime", "validateCount", "optimizeTime", "optimizeCount", "transformTime", "transformCount", "extension", "optionsResult", "parse", "opts", "arguments", "noColors", "supported", "from", "unknownOpts", "unknown", "write", "yellow", "trailingArgv", "trailing", "code", "red", "test", "baseDir", "normalize", "asconfigPath", "<PERSON><PERSON><PERSON>", "config", "asconfigFile", "basename", "asconfigDir", "dirname", "asconfig", "getAsconfig", "asconfigHasEntries", "entries", "help", "out", "color", "white", "cyan", "seenAsconfig", "Set", "add", "targets", "targetOptions", "merge", "generalOptions", "entry", "extends", "has", "addDefaults", "showConfig", "unique", "values", "program", "newOptions", "<PERSON><PERSON><PERSON><PERSON>", "setNoAssert", "noAssert", "setExportMemory", "noExportMemory", "setImportMemory", "importMemory", "setInitialMemory", "initialMemory", "setMaximumMemory", "maximumMemory", "setSharedMemory", "sharedMemory", "setImportTable", "importTable", "setExportTable", "exportTable", "setExplicitStart", "explicitStart", "setMemoryBase", "memoryBase", "setTableBase", "tableBase", "setSourceMap", "sourceMap", "setNoUnsafe", "noUnsafe", "setPedantic", "pedantic", "setLowMemoryLimit", "lowMemoryLimit", "setExportRuntime", "exportRuntime", "setBundleVersion", "stackSize", "runtime", "DEFAULT_STACK_SIZE", "setStackSize", "use", "aliases", "i", "k", "part", "p", "indexOf", "alias", "substring", "trim", "aliasPtr", "namePtr", "setGlobalAlias", "features", "disable", "flag", "disableFeature", "enable", "enableFeature", "optimizeLevel", "shrinkLevel", "optimize", "Math", "min", "max", "setOptimizeLevelHints", "newProgram", "transforms", "transform", "tsNodeRegistered", "transformArgs", "filename", "endsWith", "transpileOnly", "skipProject", "resolve", "paths", "cwd", "map", "classOrModule", "assign", "error", "applyTransform", "args", "transfromTime", "measure", "libPath", "includes", "textPtr", "pathPtr", "customLibDirs", "lib", "libFiles", "j", "l", "libText", "packageMains", "Map", "packageBases", "getFile", "internalPath", "<PERSON><PERSON><PERSON><PERSON>", "sourceText", "sourcePath", "plainName", "indexName", "match", "packageName", "isPackageRoot", "undefined", "filePath", "basePath", "traceResolution", "parts", "slice", "currentPath", "relative", "mainP<PERSON>", "jsonPath", "jsonText", "json", "ascMain", "set", "mainDir", "parseBacklog", "nextFile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numErrors", "checkDiagnostics", "reportDiagnostic", "message", "runtimeName", "runtimePath", "runtimeText", "isAbsolute", "parser", "s", "normalizedPath", "sort", "initializeProgram", "crash", "compile", "original", "<PERSON><PERSON><PERSON>", "wrapModule", "ref", "dispose", "noValidate", "<PERSON><PERSON><PERSON><PERSON>", "validate", "trapMode", "runPasses", "debugInfo", "debug", "converge", "pass", "last", "emitBinary", "next", "noEmit", "outFile", "textFile", "jsFile", "binaryFile", "hasStdout", "hasOutput", "tsdFile", "idlFile", "wasm", "sourceMapURL", "binary", "writeStdout", "sourceRoot", "index", "text", "getSource", "sourcesContent", "wastFormat", "emitText", "emitStackIR", "idl", "buildIDL", "tsd", "buildTSD", "js", "emitAsmjs", "printStats", "<PERSON><PERSON><PERSON>", "outputFilePath", "existsSync", "readdirSync", "filter", "used", "toString", "isObject", "location", "ex", "include", "diagnosticPtr", "nextDiagnostic", "formatDiagnostic", "isTTY", "diagnostic", "DiagnosticMessage", "range", "Range", "relatedRange", "rangeSource", "source", "Source", "relatedRangeSource", "category", "start", "end", "isError", "createStats", "fn", "hrtime", "times", "formatTime", "time", "toFixed", "format", "count", "len", "pad", "allocBuffer", "<PERSON><PERSON><PERSON>", "allocUnsafe", "Uint8Array", "stream", "chunk", "buffer", "reset", "<PERSON><PERSON><PERSON><PERSON>", "offset", "read", "stage", "BAR", "exit", "tscOptions", "alwaysStrict", "noImplicitAny", "noImplicitReturns", "noImplicitThis", "noEmitOnError", "strict<PERSON>ull<PERSON>hecks", "experimentalDecorators", "noLib", "types", "allowJs", "assertPath", "TypeError", "normalizeStringPosix", "allowAboveRoot", "res", "lastSegmentLength", "lastSlash", "dots", "charCodeAt", "lastSlashIndex", "lastIndexOf", "posix", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "trailingSeparator", "joined", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "_makeLong", "hasRoot", "matchedSlash", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "pathObject", "sep", "dir", "base", "_format", "ret", "delimiter", "win32", "umask", "previousTimestamp", "clocktime", "performanceNow", "performance", "seconds", "floor", "nanoseconds", "mozNow", "msNow", "oNow", "webkitNow", "getTime", "proc", "isCI", "env", "colors", "gray", "GRAY", "RESET", "RED", "green", "GREEN", "YELLOW", "blue", "BLUE", "magenta", "MAGENTA", "CYAN", "WHITE", "findFiles", "statSync", "isDirectory", "iname", "made", "mode", "mkdirSync", "err0", "stat", "err1", "sanitizeValue", "value", "Boolean", "trunc", "Number", "v", "propagateDefaults", "option", "default", "exec", "parseFloat", "indent", "padding", "eol", "sbCategories", "sbOther", "description", "sb", "noCategories", "line", "hasCategories", "currentOptions", "parentOptions", "parentBaseDir", "mergedOptions", "mutuallyExclusive", "isPath", "useNodeResolution", "cliOnly", "currentValue", "parentValue", "exclude", "defaultValue", "string", "c", "t", "fromCharCode", "apply", "c1", "c2", "instantiate", "instantiateStreaming", "demangle", "BIGINT", "BigUint64Array", "THIS", "Symbol", "utf16", "TextDecoder", "fatal", "getStringImpl", "Uint32Array", "wtf16", "Uint16Array", "decode", "off", "subarray", "preInstantiate", "imports", "extendedExports", "getString", "abort", "msg", "colm", "trace", "n", "seed", "F_NOEXPORTRUNTIME", "postInstantiate", "instance", "table", "__new", "__rtti_base", "getRttiCount", "arr", "getArrayInfo", "id", "U32", "getInfo", "getBase", "getValueAlign", "clz32", "get<PERSON>iew", "alignLog2", "signed", "float", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "BigInt64Array", "__get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "align", "buf", "getTypedArray", "Type", "getTypedArrayView", "bufPtr", "attachTypedArrayFunctions", "ctor", "bind", "U16", "__new<PERSON><PERSON>y", "result", "view", "__get<PERSON><PERSON>y", "input", "__get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8ClampedArray", "BYTES_PER_ELEMENT", "__instanceof", "baseId", "isResponse", "src", "Response", "isModule", "WebAssembly", "async", "extended", "Instance", "arrayBuffer", "setArgumentsLength", "internalName", "elem", "curr", "shift", "hash", "className", "classElem", "constructor", "thisValue", "writable", "getOwnPropertyNames", "getOwnPropertyDescriptor", "getter", "setter", "enumerable", "_default", "Rtrace", "TOTAL_OVERHEAD", "OBJECT_OVERHEAD", "BLOCK_OVERHEAD", "PTR_VIEW", "assert", "x", "trimStacktrace", "levels", "stackTraceLimit", "mmTagsToString", "gcColorToString", "onerror", "oninfo", "oncollect_", "oncollect", "shadow", "shadowStart", "blocks", "allocSites", "freedBlocks", "gcProfileStart", "allocCount", "resizeCount", "moveCount", "freeCount", "heapBase", "oninit", "onalloc", "onresize", "onmove", "onvisit", "onfree", "oninterrupt", "onyield", "onstore", "onload", "diff", "byteLength", "grow", "Memory", "initial", "PAGE_SIZE", "oldSize", "PTR_SIZE", "size", "errored", "isLoad", "isRT", "syncShadow", "mmInfo", "gcInfo", "gcInfo2", "rtId", "rtSize", "tags", "prev", "allocStack", "getBlockInfo", "<PERSON><PERSON><PERSON><PERSON>", "beforeInfo", "newSize", "unmarkShadow", "oldPtr", "newPtr", "oldInfo", "newInfo", "freeStack", "allocInfo", "delete", "total", "plot", "pause", "interruptStart", "bytes", "accessShadow", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "globalThis", "Function", "window"], "mappings": ";;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,YAAc,WAA+C,IAAM,OAAOA,QAAQ,kBAAqB,MAAMC,KAAhG,IACrB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,WAAY,kBAAmBL,GACb,iBAAZC,QACdA,QAAa,IAAID,EAAQG,QAAQ,YAAc,WAA+C,IAAM,OAAOA,QAAQ,kBAAqB,MAAMC,KAAhG,IAE9CL,EAAU,IAAIC,EAAQD,EAAe,SAAGA,EAAqB,gBAR/D,CASmB,oBAATQ,KAAuBA,KAAOC,MAAM,SAASC,EAAkCC,GACzF,M,2BCsBA,MAAMC,EAAK,EAAQ,KACbC,EAAO,EAAQ,KACfC,EAAU,EAAQ,KAClBC,EAAO,EAAQ,KACfC,EAAa,EAAQ,KACrBC,EAAc,EAAQ,KACtBC,EAAS,EAAQ,KACjBC,EAAO,EAAQ,KACfC,EAAW,EAAAC,EAAOD,WAAa,EAAAC,EAAOD,SAAW,EAAQ,MAEzDE,EACF,QAGEC,EAA2B,UAArBT,EAAQU,SACdC,EAAMF,EAAM,OAAS,KACrBG,EAAMH,EAAM,KAAS,IAE3B,SAASI,EAAiBC,GACxB,OAAOA,EAAIC,QAAQ,KAAM,KAAKC,cAIhC,SAASC,EAAeC,GAEtB,OADKA,EAAIC,WAAW,OAAMD,EAAM,IAAIA,KAC7B,CACLA,MACAE,MAAO,KAAKF,IACZG,GAAI,IAAIC,OAAO,KAAOJ,EAAM,KAC5BK,KAAM,IAAID,OAAO,SAAWJ,EAAM,KAClCM,YAAa,IAAIF,OAAO,eAAiBJ,EAAM,SAAWA,EAAM,KAChEO,SAAU,IAAIH,OAAO,yBAA2BJ,EAAM,MAI1D,MAAMQ,EAAmBT,EAAe,OA2ExC,SAASU,EAAOC,EAAUC,GACxB,MAAwB,iBAAbD,EACW,IAAbA,EAAiB,KAAOC,EAAaC,KAAKF,GAE5CA,EAGT,IAAIG,EAAgBC,EAAaC,EAAaC,EAAOC,EAASC,EA/E9DC,OAAOC,eAAelD,EAAS,QAAS,CACtCmD,IAAG,IAAYjC,EAASkC,QAKtBxC,EAAQyC,oBACVzC,EAAQyC,mBAAmB,qBA0E7B,WACE,MAAMC,EAAU1C,EAAQ2C,KAAKC,WAAUC,GAAc,UAAPA,IAC9C,IAAKH,EAAS,CACZ,IAAII,EAAa9C,EAAQ2C,KAAKD,EAAU,GACxC1C,EAAQ2C,KAAKI,OAAOL,EAAS,GAC7BX,EA5CJ,SAAgCe,GAC9B,MAAME,EAAS,EAAQ,KACjBC,EAAS,IAAK,OAAyC,QAAE,CAC7D,QAAQC,EAAKC,GACXC,QAAQC,IAAIH,EAAKC,IAEnBG,UAAS,IACAlE,EAAQmE,OAEjB,YACE,IAAIC,EAAYP,EAAOO,UACvB,GAAIA,GAAaA,EAAUC,QAAU3D,EAAG4D,cAAe,CACrD,IAAIC,EAAYC,KAAKC,MACrB/D,EAAG4D,cACD,qBAAqBC,SACrBG,KAAKC,UAAUP,IAEjB1D,EAAG4D,cACD,qBAAqBC,QACrB,sBAAsBH,EAAUQ,KAAK,aAK7C,IAAI,QAAE5E,GAAY4D,EAAOiB,gBAAgBnE,EAAGoE,aAAapB,GAAaG,EAAOkB,QAAQ,CAAE7D,cAEvF,OADIlB,EAAQgF,QAAQhF,EAAQgF,SACrBhF,EAkBYiF,CAAuBvB,GACxCd,EAAcD,EAAeC,YAC7BC,EAAcF,EAAeE,YAC7BC,EAAQH,EAAeG,MACvBC,EAAUJ,EAAeI,QACzBC,EAAYL,EAAeK,eAE3BL,EAlFJ,WACE,IAAI3C,EACJ,IAGEA,EAAU,EAAQ,KAClB,MAAOG,GACP,IACEH,EAAUoB,EAAW,6BACrB,MAAOjB,GACP,IACEiB,EAAW,WAAW8D,SAAS,CAC7BC,QAASxE,EAAKiE,KAAK,IAAW,KAAM,MAAO,iBAC3CQ,YAAY,EACZC,gBAAiB,CAAEC,OAAQ,YAE7BlE,EAAW,kBACXpB,EAAUoB,EAAW,UACrB,MAAOmE,GACP,IACEvF,EAAUoB,EAAW,oBACrB,MAAOjB,GACP,MAAMqF,MAAM,GAAGD,EAAKE,eAAetF,EAAEsF,YAK7C,OAAOzF,EAuDY0F,GACjB9C,EAAclB,GAAOA,EACrBmB,EAAc8C,GAAOA,EACrB7C,EAAQ6C,GAAOA,EACf5C,EAAU6C,MACV5C,EAAY4C,MAGhBC,GAGA7F,EAAQ8F,UAAW,EAGnB9F,EAAQ+F,QAAU/F,EAAQ8F,SAAW,UAAiB1E,EAAW,mBAAmB2E,QAGpF/F,EAAQgG,QAAU,EAAlB,KAGAhG,EAAQiG,cAAgBpD,EAAYF,EAAeuD,eAAeC,WAGlEnG,EAAQoG,qBAAuB,EAG/BpG,EAAQqG,mBAAqB,EAG7BrG,EAAQsG,aAAetG,EAAQ8F,SAAW,8rkvBAAiB,MACzD,MAAMS,EAAS5F,EAAKiE,KAAK,IAAW,KAAM,MAAO,YAC3C4B,EAAU,GAMhB,OALAvF,EACGwF,MAAMF,EAAQjE,EAAiBF,aAC/BsE,SAAQC,IACPH,EAAQG,EAAKhF,QAAQW,EAAiBL,GAAI,KAAOvB,EAAGoE,aAAanE,EAAKiE,KAAK2B,EAAQI,GAAO,WAEvFH,GARkD,GAY3DxG,EAAQ4G,gBAAkB5G,EAAQ8F,SAAW,m5lJAAqB,MAChE,MAAMe,EAAiBC,GAAQpG,EAAGoE,aAChCnE,EAAKiE,KAAK,IAAW,KAAM,MAAOkC,EAAM,QAAQxE,EAAiBN,SACjE,QAEF,MAAO,CACL+E,SAAUF,EAAe,YACzBG,SAAUH,EAAe,cAPqC,GAYlE7G,EAAQiH,cAAgB,CAACC,EAASlB,KACT,iBAAZkB,IAAsBA,EAAU,CAAE,CAAC,QAAQ5E,EAAiBR,OAAQoF,IAC/E,MAAMC,EAASlE,OAAOmE,OAAO,CAC3BC,OAAQC,IACRC,OAAQD,MAEV,IAAI/D,EAAO,CACT,eAAgB,SAChB,aAAc,QAqBhB,OAnBAN,OAAOuE,KAAKxB,GAAW,IAAIU,SAAQe,IACjC,IAAIC,EAAM1B,EAAQyB,GACdE,EAAM3H,EAAQgG,QAAQyB,GACtBE,GAAoB,MAAbA,EAAIC,KACTF,GAAKnE,EAAKsE,KAAK,KAAKJ,KAEpBK,MAAMC,QAAQL,GAChBA,EAAIhB,SAAQgB,IAASnE,EAAKsE,KAAK,KAAKJ,IAAOO,OAAON,OAE/CnE,EAAKsE,KAAK,KAAKJ,IAAOO,OAAON,OAGtC1H,EAAQiI,KAAK1E,EAAK2E,OAAOjF,OAAOuE,KAAKN,IAAW,CAC9CG,OAAQF,EAAOE,OACfE,OAAQJ,EAAOI,OACfY,SAAUrB,GAAQ7D,OAAOmF,UAAUC,eAAeC,KAAKpB,EAASJ,GAAQI,EAAQJ,GAAQ,KACxFyB,UAAW,CAACzB,EAAM0B,KAAerB,EAAOL,GAAQ0B,GAChDC,UAAW,IAAM,KAEZtB,GAITnH,EAAQiI,KAAO,SAAc1E,EAAMyC,EAAS0C,GACnB,mBAAZ1C,GACT0C,EAAW1C,EACXA,EAAU,IACAA,IACVA,EAAU,IAIZ,IAAI2C,EAAqB,EAAGC,EAAqB,EAAGC,EAAqB,EACzE,MAAMC,GAAgB9I,EAAQ+F,SAAW,IAAIgD,MAAM,KACvB,IAAxBD,EAAazE,SACfuE,EAAiD,EAA5BI,SAASF,EAAa,IAC3CH,EAAiD,EAA5BK,SAASF,EAAa,IAC3CD,EAAiD,EAA5BG,SAASF,EAAa,KAG7C,MAAMzB,EAASrB,EAAQqB,QAAUzG,EAAQyG,OACnCE,EAASvB,EAAQuB,QAAU3G,EAAQ2G,OACnCY,EAAWnC,EAAQmC,UAAYc,GAC/BV,EAAYvC,EAAQuC,WAAaW,GACjCT,EAAYzC,EAAQyC,WAAaU,GACjCC,EAAQpD,EAAQoD,OAqjCf,CACLC,SAAU,EACVC,UAAW,EACXC,UAAW,EACXC,WAAY,EACZC,UAAW,EACXC,WAAY,EACZC,eAAgB,EAChBC,gBAAiB,EACjBC,YAAa,EACbC,aAAc,EACdC,SAAU,EACVC,UAAW,EACXC,aAAc,EACdC,cAAe,EACfC,aAAc,EACdC,cAAe,EACfC,cAAe,EACfC,eAAgB,GAtkClB,IAAIC,EAAYjI,EAGhB,IAAK+E,EAAQ,MAAM7B,MAAM,sCACzB,IAAK+B,EAAQ,MAAM/B,MAAM,sCAGzB,MAAMgF,EAAgBzJ,EAAY0J,MAAMlH,EAAMvD,EAAQgG,SAAS,GAC/D,IAAI0E,EAAOF,EAAcxE,QACzBzC,EAAOiH,EAAcG,UAEjBD,EAAKE,SACP9J,EAAWuG,OAAOwD,UAClB/J,EAAWyG,OAAOsD,WAAY,GAE9B/J,EAAWuG,OAASvG,EAAWgK,KAAKzD,GACpCvG,EAAWyG,OAASzG,EAAWgK,KAAKvD,IAItC,MAAMwD,EAAcP,EAAcQ,QAC9BD,EAAY1G,QACd0G,EAAYrE,SAAQjD,IAClB8D,EAAO0D,MACL,GAAGnK,EAAWyG,OAAO2D,OAAO,8BAA8BzH,eAMhE,MAAM0H,EAAeX,EAAcY,SAkBnC,GAjBID,EAAa9G,QACfkD,EAAO0D,MACL,GAAGnK,EAAWyG,OAAO2D,OAAO,8CAA8CC,EAAavG,KAAK,OAAOrD,KAKlGmH,IAAUA,EAAW,SAAyB5E,GACjD,IAAIuH,EAAO,EAKX,OAJIvH,IACFyD,EAAO0D,MAAM,GAAGnK,EAAWyG,OAAO+D,IAAI,cAAcxH,EAAI2B,MAAM9D,QAAQ,YAAa,MAAMJ,KACzF8J,EAAO,GAEFA,IAILX,EAAK3E,QAEP,OADAsB,EAAO4D,MAAM,WAAWjL,EAAQ+F,UAAUxE,KACnCmH,EAAS,MAIlB,GAA8B,iBAAnBgC,EAAKH,UAAwB,CACtC,IAAI,yBAAyBgB,KAAKb,EAAKH,WAGrC,OAAO7B,EAASlD,MAAM,sBAAsBkF,EAAKH,cAFjDA,EAAY1I,EAAe6I,EAAKH,WAOpC,MAAMiB,EAAU7K,EAAK8K,UAAUf,EAAKc,SAAW,KAG/C,IAAIE,EAAe3K,EAAY4K,YAAYjB,EAAKkB,QAAU,gBAAiBJ,GACvEK,EAAelL,EAAKmL,SAASJ,GAC7BK,EAAcpL,EAAKqL,QAAQN,GAC3BO,EAAWC,EAAYL,EAAcE,EAAa5D,GAClDgE,EAAiC,MAAZF,GAAoBnE,MAAMC,QAAQkE,EAASG,UAAYH,EAASG,QAAQ/H,OAGjG,GAAIqG,EAAK2B,OAAU9I,EAAKc,SAAW8H,EAAqB,CACtD,IAAIG,EAAM5B,EAAK2B,KAAOhF,EAASE,EAC3BgF,EAAQ7B,EAAK2B,KAAOvL,EAAWuG,OAASvG,EAAWyG,OAevD,OAdA+E,EAAIrB,MAAM,CACRsB,EAAMC,MAAM,UACZ,KAAOD,EAAME,KAAK,OAAS,6BAC3B,GACAF,EAAMC,MAAM,YACZ,KAAOD,EAAME,KAAK,OAAS,SAAWlC,EAAUzI,IAChD,KAAOyK,EAAME,KAAK,OAAS,SAAWlC,EAAUzI,IAAM,8BACtD,KAAOyK,EAAME,KAAK,OAAS,UAAYlC,EAAUzI,IAAM,UAAYyI,EAAUzI,IAAM,sBACnF,KAAOyK,EAAME,KAAK,OAAS,2CAC3B,GACAF,EAAMC,MAAM,YACZtE,OACAnH,EAAYsL,KAAKrM,EAAQgG,QAAS,GAAIzE,IACtCqD,KAAKrD,GAAOA,GACPmH,EAAS,MAIlB,IAAKhI,EAAGoE,aAAc,CACpB,GAAIqD,IAAac,GAAgB,MAAMzD,MAAM,wCAC7C,GAAI+C,IAAcW,GAAe,MAAM1D,MAAM,yCAC7C,GAAIiD,IAAcU,GAAe,MAAM3D,MAAM,yCAI/C,MAAMkH,EAAe,IAAIC,IACzBD,EAAaE,IAAIlB,GACjB,MAAMpG,EAASoF,EAAKpF,QAAU,UAC9B,KAAO2G,GAAU,CAEf,GAAIA,EAASY,QAAS,CACpB,MAAMC,EAAgBb,EAASY,QAAQvH,GACnCwH,IACFpC,EAAO3J,EAAYgM,MAAM/M,EAAQgG,QAAS0E,EAAMoC,EAAef,IAInE,MAAMiB,EAAiBf,EAASjG,QAMhC,GALIgH,IACFtC,EAAO3J,EAAYgM,MAAM/M,EAAQgG,QAAS0E,EAAMsC,EAAgBjB,IAI9DE,EAASG,QACX,IAAK,IAAIa,KAAShB,EAASG,QACzB7I,EAAKsE,KAAK9G,EAAY4K,YAAYsB,EAAOlB,IAK7C,IAAIE,EAASiB,QAQX,MAJA,GAHAxB,EAAe3K,EAAY4K,YAAYM,EAASiB,QAASnB,GAAa,GACtEF,EAAelL,EAAKmL,SAASJ,GAC7BK,EAAcpL,EAAKqL,QAAQN,GACvBgB,EAAaS,IAAIzB,GAAe,MACpCgB,EAAaE,IAAIlB,GACjBO,EAAWC,EAAYL,EAAcE,EAAa5D,GAUtD,GAHApH,EAAYqM,YAAYpN,EAAQgG,QAAS0E,GAGrCA,EAAK2C,WAKP,OAJA9F,EAAO0D,MAAMvG,KAAKC,UAAU,CAC1BqB,QAAS0E,EACT0B,QAAS7I,GACR,KAAM,IACFmF,EAAS,MAIlB,SAAS4E,GAAOC,GACd,MAAO,IAAI,IAAIZ,IAAIY,IAIrB,IAAIC,GACJ,MAAMnI,GAAkBvC,EAAMH,EAAe8K,cAmC7C,GAlCA9K,EAAe+K,UAAUrI,GAAiB,GAC1C1C,EAAegL,YAAYtI,GAAiBqF,EAAKkD,UACjDjL,EAAekL,gBAAgBxI,IAAkBqF,EAAKoD,gBACtDnL,EAAeoL,gBAAgB1I,GAAiBqF,EAAKsD,cACrDrL,EAAesL,iBAAiB5I,GAAiBqF,EAAKwD,gBAAkB,GACxEvL,EAAewL,iBAAiB9I,GAAiBqF,EAAK0D,gBAAkB,GACxEzL,EAAe0L,gBAAgBhJ,GAAiBqF,EAAK4D,cACrD3L,EAAe4L,eAAelJ,GAAiBqF,EAAK8D,aACpD7L,EAAe8L,eAAepJ,GAAiBqF,EAAKgE,aACpD/L,EAAegM,iBAAiBtJ,GAAiBqF,EAAKkE,eACtDjM,EAAekM,cAAcxJ,GAAiBqF,EAAKoE,aAAe,GAClEnM,EAAeoM,aAAa1J,GAAiBqF,EAAKsE,YAAc,GAChErM,EAAesM,aAAa5J,GAAmC,MAAlBqF,EAAKwE,WAClDvM,EAAewM,YAAY9J,GAAiBqF,EAAK0E,UACjDzM,EAAe0M,YAAYhK,GAAiBqF,EAAK4E,UACjD3M,EAAe4M,kBAAkBlK,GAAiBqF,EAAK8E,iBAAmB,GAC1E7M,EAAe8M,iBAAiBpK,GAAiBqF,EAAKgF,eACtD/M,EAAegN,iBAAiBtK,GAAiBuD,EAAoBD,EAAoBE,GACpF6B,EAAKkF,WAA6B,eAAhBlF,EAAKmF,UAC1BnF,EAAKkF,UAAYjN,EAAemN,oBAElCnN,EAAeoN,aAAa1K,GAAiBqF,EAAKkF,WAGlDlH,EAAW,SAAUA,GACnB,OAAO,SAAyB5E,GAI9B,OAHAf,EAAQsC,IACJmI,IAASzK,EAAQyK,IACrBxK,IACO0F,EAAS5E,IALT,CAOR4E,GAGCgC,EAAKsF,IAAK,CACZ,IAAIC,EAAUvF,EAAKsF,IACnB,IAAK,IAAIE,EAAI,EAAGC,EAAIF,EAAQ5L,OAAQ6L,EAAIC,IAAKD,EAAG,CAC9C,IAAIE,EAAOH,EAAQC,GACfG,EAAID,EAAKE,QAAQ,KACrB,GAAID,EAAI,EAAG,OAAO3H,EAASlD,MAAM,iBAAiB4K,mBAClD,IAAIG,EAAQH,EAAKI,UAAU,EAAGH,GAAGI,OAC7B3J,EAAOsJ,EAAKI,UAAUH,EAAI,GAAGI,OACjC,IAAKF,EAAMlM,OACT,OAAOqE,EAASlD,MAAM,iBAAiB4K,mBAEzC,CACE,IAAIM,EAAW5N,EAAMF,EAAY2N,IAC7BI,EAAU/N,EAAYkE,GAC1BnE,EAAeiO,eAAevL,GAAiBqL,EAAUC,GACzD5N,EAAQ2N,KAMd,IAAIG,GACJ,GAAiC,OAA5BA,GAAWnG,EAAKoG,SAAkB,CACb,iBAAbD,KAAuBA,GAAWA,GAAS9H,MAAM,MAC5D,IAAK,IAAImH,EAAI,EAAGC,EAAIU,GAASxM,OAAQ6L,EAAIC,IAAKD,EAAG,CAC/C,IAAIpJ,EAAO+J,GAASX,GAAGO,OACnBM,EAAOpO,EAAe,WAAWlB,EAAiBqF,MACtD,IAAKiK,EAAM,OAAOrI,EAASlD,MAAM,YAAYsB,mBAC7CnE,EAAeqO,eAAe3L,GAAiB0L,IAKnD,GAAgC,OAA3BF,GAAWnG,EAAKuG,QAAiB,CACZ,iBAAbJ,KAAuBA,GAAWA,GAAS9H,MAAM,MAC5D,IAAK,IAAImH,EAAI,EAAGC,EAAIU,GAASxM,OAAQ6L,EAAIC,IAAKD,EAAG,CAC/C,IAAIpJ,EAAO+J,GAASX,GAAGO,OACnBM,EAAOpO,EAAe,WAAWlB,EAAiBqF,MACtD,IAAKiK,EAAM,OAAOrI,EAASlD,MAAM,YAAYsB,mBAC7CnE,EAAeuO,cAAc7L,GAAiB0L,IAKlD,IAAII,GAAgB,EAChBC,GAAc,EACd1G,EAAK2G,WACPF,GAAgBnR,EAAQoG,qBACxBgL,GAAcpR,EAAQqG,oBAEU,iBAAvBqE,EAAKyG,gBAA4BA,GAAgBzG,EAAKyG,eACjC,iBAArBzG,EAAK0G,cAA0BA,GAAc1G,EAAK0G,aAC7DD,GAAgBG,KAAKC,IAAID,KAAKE,IAAIL,GAAe,GAAI,GACrDC,GAAcE,KAAKC,IAAID,KAAKE,IAAIJ,GAAa,GAAI,GACjDzO,EAAe8O,sBAAsBpM,GAAiB8L,GAAeC,IAGrE5D,GAAU1K,EAAMH,EAAe+O,WAAWrM,KAI1C,IAAIsM,GAAa,GAMjB,GAJI7J,MAAMC,QAAQ/B,EAAQ2L,aACxBA,GAAW9J,QAAQ7B,EAAQ2L,YAGzBjH,EAAKkH,UAAW,CAClB,IAAIC,GAAmB,EACnBC,EAAgBxE,GAAO5C,EAAKkH,WAChC,IAAK,IAAI1B,EAAI,EAAGC,EAAI2B,EAAczN,OAAQ6L,EAAIC,IAAKD,EAAG,CACpD,IAAI6B,EAAWD,EAAc5B,GAAGO,QAC3BoB,GAAoBE,EAASC,SAAS,SACzC5Q,EAAW,WAAW8D,SAAS,CAC7B+M,eAAe,EACfC,aAAa,EACb7M,gBAAiB,CAAEC,OAAQ,YAE7BuM,GAAmB,GAErB,IACEF,GAAW9J,KAAKzG,EAAWA,EAAW+Q,QAAQJ,EAAU,CACtDK,MAAO,CAAC5G,EAAS5K,EAAQyR,WAE3B,MAAOlS,GACP,OAAOuI,EAASvI,KAMtB,IACEwR,GAAaA,GAAWW,KAAIC,GAEG,mBAAlBA,EACFA,GAETtP,OAAOuP,OAAOD,EAAcnK,UAAW,CACrCoF,WACAhC,UACAnE,SACAE,SACAtD,IAAKD,QAAQyO,MACbtK,WACAI,YACAE,cAEK,IAAI8J,KAEb,MAAOpS,GACP,OAAOuI,EAASvI,GAGlB,SAASuS,GAAe5L,KAAS6L,GAC/B,IAAK,IAAIzC,EAAI,EAAGC,EAAIwB,GAAWtN,OAAQ6L,EAAIC,IAAKD,EAAG,CACjD,IAAI0B,EAAYD,GAAWzB,GAC3B,GAA+B,mBAApB0B,EAAU9K,GACnB,IACEsC,EAAMkB,iBACNlB,EAAMwJ,eAAiBC,GAAQ,KAC7BjB,EAAU9K,MAAS6L,MAErB,MAAOxS,GACP,OAAOA,IAOf8C,OAAOuE,KAAKxH,EAAQsG,cAAcI,SAAQoM,IACpCA,EAAQC,SAAS,OACrB3J,EAAMM,aACNN,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUlQ,EAAMF,EAAY5C,EAAQsG,aAAawM,KACjDG,EAAUrQ,EAAY5C,EAAQiG,cAAgB6M,EAAUvI,EAAUzI,KACtEa,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,GAChDlQ,EAAQiQ,UAGZ,IAAIE,GAAgB,GACpB,GAAIxI,EAAKyI,IAAK,CACZ,IAAIA,EAAMzI,EAAKyI,IACI,iBAARA,IAAkBA,EAAMA,EAAIpK,MAAM,MAC7CmK,GAAcrL,QAAQsL,EAAIb,KAAIjC,GAAKA,EAAEI,UACrCyC,GAAgB5F,GAAO4F,IACvB,IAAK,IAAIhD,EAAI,EAAGC,EAAI+C,GAAc7O,OAAQ6L,EAAIC,IAAKD,EAAG,CACpD,IACIkD,EADA7M,EAAS2M,GAAchD,GAEvB3J,EAAOyL,SAASzH,EAAUzI,MAC5BsR,EAAW,CAAEzS,EAAKmL,SAASvF,IAC3BA,EAAS5F,EAAKqL,QAAQzF,IAEtB6M,EAAW3K,EAAUlC,EAAQiF,IAAY,GAE3C,IAAK,IAAI6H,EAAI,EAAGC,EAAIF,EAAS/O,OAAQgP,EAAIC,IAAKD,EAAG,CAC/C,IAAIP,EAAUM,EAASC,GACnBE,EAAUpL,EAAS2K,EAASvM,GAChC,GAAe,MAAXgN,EACF,OAAO7K,EAASlD,MAAM,iBAAiBsN,kBAEzC1J,EAAMM,aACN1J,EAAQsG,aAAawM,EAAQnR,QAAQ4I,EAAUtI,GAAI,KAAOsR,EAC1DnK,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUlQ,EAAMF,EAAY2Q,IAC5BN,EAAUrQ,EAAY5C,EAAQiG,cAAgB6M,GAClDnQ,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,GAChDlQ,EAAQiQ,QAKhBtI,EAAK/J,KAAO+J,EAAK/J,MAAQ,GAGzB,MAAM6S,GAAe,IAAIC,IACnBC,GAAe,IAAID,IAGzB,SAASE,GAAQC,EAAcC,GAC7B,IAAIC,EAAa,KACbC,EAAa,KAEjB,MAAM9N,EAAgBjG,EAAQiG,cACxBK,EAAetG,EAAQsG,aAG7B,GAAKsN,EAAa7R,WAAWkE,GAUtB,CACL,MAAM+N,EAAYJ,EAAapD,UAAUvK,EAAc5B,QACjD4P,EAAY,GAAGD,UACrB,GAAI/Q,OAAOmF,UAAUC,eAAeC,KAAKhC,EAAc0N,GACrDF,EAAaxN,EAAa0N,GAC1BD,EAAa9N,EAAgB+N,EAAYzJ,EAAUzI,SAC9C,GAAImB,OAAOmF,UAAUC,eAAeC,KAAKhC,EAAc2N,GAC5DH,EAAaxN,EAAa2N,GAC1BF,EAAa9N,EAAgBgO,EAAY1J,EAAUzI,QAC9C,CACL,IAAK,MAAMyE,KAAU2M,GAAe,CAClC,GAAkE,OAA7DY,EAAa3L,EAAS6L,EAAYzJ,EAAUzI,IAAKyE,IAAkB,CACtEwN,EAAa9N,EAAgB+N,EAAYzJ,EAAUzI,IACnD,MAEA,GAAkE,OAA7DgS,EAAa3L,EAAS8L,EAAY1J,EAAUzI,IAAKyE,IAAkB,CACtEwN,EAAa9N,EAAgBgO,EAAY1J,EAAUzI,IACnD,OAIN,GAAkB,MAAdgS,EAAoB,CACtB,MAAMI,EAAQN,EAAaM,MAAM,0CACjC,GAAIA,EAAO,CACT,MAAMC,EAAcD,EAAM,GACpBE,OAA6BC,IAAbH,EAAM,GACtBI,EAAWF,EAAgB,QAAUF,EAAM,GAC3CK,EAAWb,GAAavG,IAAI0G,GAC9BH,GAAavQ,IAAI0Q,GACjB,IAEAnJ,EAAK8J,iBACPjN,EAAO0D,MAAM,wBAAwBkJ,YAAsBG,mBAA0BC,KAAYhT,KAEnG,MAAM6Q,EAAQ,GACRqC,EAAQ9T,EAAKwR,QAAQ3G,EAAS+I,GAAUxL,MAAMvH,GACpD,IAAK,IAAI0O,EAAIuE,EAAMpQ,OAAQ8L,EAAI9O,EAAM,EAAI,EAAG6O,GAAKC,IAAKD,EAC/B,iBAAjBuE,EAAMvE,EAAI,IACZkC,EAAMvK,KAAK,GAAG4M,EAAMC,MAAM,EAAGxE,GAAGtL,KAAKpD,KAAOA,iBAGhD,IAAK,MAAMmT,KAAevC,EAAMlK,UAAUwC,EAAK/J,MAAM2R,KAAIjC,GAAK1P,EAAKiU,SAASpJ,EAAS6E,KAAK,CACpF3F,EAAK8J,iBACPjN,EAAO0D,MAAM,QAAQtK,EAAKiE,KAAK+P,EAAaR,KAAe5S,KAE7D,IAAIsT,EAAW,WACf,GAAIrB,GAAarG,IAAIgH,GACnBU,EAAWrB,GAAarQ,IAAIgR,OACvB,CACL,IAAIW,EAAWnU,EAAKiE,KAAK+P,EAAaR,EAAa,gBAC/CY,EAAW5M,EAAS2M,EAAUtJ,GAClC,GAAgB,MAAZuJ,EACF,IACE,IAAIC,EAAOtQ,KAAK+F,MAAMsK,GACM,iBAAjBC,EAAKC,UACdJ,EAAWG,EAAKC,QAAQtT,QAAQ4I,EAAUlI,SAAU,IACpDmR,GAAa0B,IAAIf,EAAaU,IAEhC,MAAO1U,KAGb,MAAMgV,EAAUxU,EAAKiE,KAAK+P,EAAaR,EAAaU,GAC9Cb,EAAYM,EAClB,GAAuF,OAAlFR,EAAa3L,EAASxH,EAAKiE,KAAKuQ,EAASnB,EAAYzJ,EAAUzI,KAAM0J,IAAmB,CAC3FuI,EAAa,GAAG9N,IAAgBkO,KAAeH,IAAYzJ,EAAUzI,MACrE4R,GAAawB,IAAInB,EAAWpS,QAAQ4I,EAAUtI,GAAI,IAAKtB,EAAKiE,KAAK+P,EAAaR,IAC1EzJ,EAAK8J,iBACPjN,EAAO0D,MAAM,QAAQtK,EAAKiE,KAAKuQ,EAASnB,EAAYzJ,EAAUzI,OAAOP,KAEvE,MACK,IAAK6S,EAAe,CACzB,MAAMH,EAAY,GAAGK,UACrB,GAAwF,QAAnFR,EAAa3L,EAASxH,EAAKiE,KAAKuQ,EAASlB,EAAY1J,EAAUzI,KAAM0J,IAAoB,CAC5FuI,EAAa,GAAG9N,IAAgBkO,KAAeF,IAAY1J,EAAUzI,MACrE4R,GAAawB,IAAInB,EAAWpS,QAAQ4I,EAAUtI,GAAI,IAAKtB,EAAKiE,KAAK+P,EAAaR,IAC1EzJ,EAAK8J,iBACPjN,EAAO0D,MAAM,QAAQtK,EAAKiE,KAAKuQ,EAASlB,EAAY1J,EAAUzI,OAAOP,KAEvE,iBAvFuE,OAA9EuS,EAAa3L,EAAS4L,EAAaH,EAAerJ,EAAUzI,IAAK0J,KAC0B,OAAzFsI,EAAa3L,EAAS4L,EAAaH,EAAe,SAAWrJ,EAAUzI,IAAK0J,MAE/EuI,EAAaH,EAAerJ,EAAUzI,IACtCgS,EAAa3L,EAASyL,EAAerJ,EAAUvI,MAAOwJ,IA4F5D,OAAkB,MAAdsI,EAA2B,KACxB,CAAEA,aAAYC,cAIvB,SAASqB,KAEP,IADA,IAAIxB,EACIA,EAAe/Q,EAAYF,EAAe0S,SAAS7H,MAAY,CACrE,IAAI7G,EAAOgN,GAAQC,EAAcjR,EAAe2S,YAAY9H,GAASoG,IACjEjN,GACFyC,EAAMM,aACNN,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUlQ,EAAMF,EAAY+D,EAAKmN,aACjCb,EAAUrQ,EAAY+D,EAAKoN,YAC/BpR,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,GAChDlQ,EAAQiQ,OAGV5J,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUpQ,EAAY,MACtBqQ,EAAUrQ,EAAYgR,EAAerJ,EAAUzI,KACnDa,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,MAItD,IAAIsC,EAAYC,EAAiBhI,GAASjG,EAAQvB,EAAQyP,kBAC1D,GAAIF,EAAW,CACb,MAAMzR,EAAM0B,MAAM,GAAG+P,oBAErB,OADAzR,EAAI2B,MAAQ3B,EAAI4R,QACThN,EAAS5E,IAKpB,CACE,IAAI6R,EAAc3N,OAAO0C,EAAKmF,SAC1B+F,EAAc,YAAYD,IAC1BE,EAAc7V,EAAQsG,aAAasP,GACvC,GAAmB,MAAfC,GAGF,GAFAD,EAAcD,EACdE,EAAc1N,EAASyN,EAAcrL,EAAUzI,IAAK0J,GACjC,MAAfqK,EAAqB,OAAOnN,EAASlD,MAAM,YAAYmQ,uBAE3DC,EAAc,QAAQA,IAExBxM,EAAMM,aACNN,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUlQ,EAAMF,EAAYiT,IAC5B5C,EAAUrQ,EAAYgT,EAAcrL,EAAUzI,KAClDa,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,GAChDlQ,EAAQiQ,MAKZ,IAAK,IAAI9C,EAAI,EAAGC,EAAI5M,EAAKc,OAAQ6L,EAAIC,IAAKD,EAAG,CAC3C,MAAM6B,EAAWxO,EAAK2M,GACtB,IAAI6D,EAAa/L,OAAO+J,GACrBpQ,QAAQ,MAAO,KACfA,QAAQ4I,EAAUtI,GAAI,IACtBN,QAAQ,SAAU,IAGrBoS,EAAapT,EAAKmV,WAAW/B,GACzBpT,EAAKiU,SAASpJ,EAASuI,GAAYpS,QAAQ,MAAO,KAClDoS,EAGJ,IAAID,EAAa3L,EAAS4L,EAAaxJ,EAAUzI,IAAK0J,GACtD,GAAkB,MAAdsI,EAAoB,CACtB,MAAMnT,EAAO,GAAGoT,UAAmBxJ,EAAUzI,MAC7CgS,EAAa3L,EAASxH,EAAM6K,GACV,MAAdsI,EAAoBC,EAAapT,EAChCoT,GAAcxJ,EAAUzI,SAE7BiS,GAAcxJ,EAAUzI,IAG1BsH,EAAMM,aACNN,EAAMK,WAAaoJ,GAAQ,KACzB,IAAIG,EAAUlQ,EAAMF,EAAYkR,IAC5Bb,EAAUrQ,EAAYmR,GAC1BpR,EAAe8H,MAAM+C,GAASwF,EAASC,GAAS,GAChDlQ,EAAQiQ,MAKZ,CACE,IAAI3H,EAAO+J,KACX,GAAI/J,EAAM,OAAOA,EAInB,CACE,IAAIoH,EAAQC,GAAe,aAAclF,GAAQuI,QACjD,GAAItD,EAAO,OAAO/J,EAAS+J,GAI7B,CACE,IAAIpH,EAAO+J,KACX,GAAI/J,EAAM,OAAOA,EAInB,GAAIX,EAAKjC,UAGP,OADAlB,EAAO0D,MAAMuC,GAAQtG,QAAQoL,KAAI0D,GAAKA,EAAEC,iBAAgBC,OAAOtR,KAAKrD,GAAOA,GACpEmH,EAAS,MAIlBU,EAAMQ,kBACNR,EAAMO,gBAAkBkJ,GAAQ,KAC9B,IACElQ,EAAewT,kBAAkB3I,IACjC,MAAOrN,GACPiW,EAAM,aAAcjW,OAKxB,CACE,IAAIsS,EAAQC,GAAe,kBAAmBlF,IAC9C,GAAIiF,EAAO,OAAO/J,EAAS+J,GAG7B,IAAIxS,GACJmJ,EAAMU,eACNV,EAAMS,aAAegJ,GAAQ,KAC3B,IACE5S,GAAS0C,EAAe0T,QAAQ7I,IAChC,MAAOrN,GACPiW,EAAM,UAAWjW,GAInB,GAAsB,iBAAXF,GAAqB,CAC9B,MAAMqW,EAAW3T,EAAe4T,OAAO7T,KAAKzC,KAC5CA,GAASiB,EAASsV,WAAWF,EAASG,MAC/BpF,SAAW,YAAYsB,GAC5B2D,EAASjF,YAAYsB,QAElB,CACL,MAAM2D,EAAWrW,IACjBA,GAASiB,EAASsV,WAAWvW,GAAOwW,MAC7BpF,SAAW,YAAYsB,GAC5B2D,EAASjF,YAAYsB,QAI3B,IAAI4C,GAAYC,EAAiBhI,GAASjG,EAAQvB,EAAQyP,kBAC1D,GAAIF,GAAW,CACTtV,IAAQA,GAAOyW,UACnB,MAAM5S,EAAM0B,MAAM,GAAG+P,uBAErB,OADAzR,EAAI2B,MAAQ3B,EAAI4R,QACThN,EAAS5E,GAIlB,CACE,IAAI2O,EAAQC,GAAe,eAAgBzS,IAC3C,GAAIwS,EAAO,OAAO/J,EAAS+J,GAI7B,IAAK/H,EAAKiM,WAAY,CAEpB,IAAIC,EAIJ,GALAxN,EAAMc,gBAENd,EAAMa,cAAgB4I,GAAQ,KAC5B+D,EAAU3W,GAAO4W,eAEdD,EAEH,OADA3W,GAAOyW,UACAhO,EAASlD,MAAM,mBAK1B,GAAsB,UAAlBkF,EAAKoM,UAA0C,OAAlBpM,EAAKoM,SACpC1N,EAAMgB,gBACNhB,EAAMe,cAAgB0I,GAAQ,KAC5B,IACE5S,GAAO8W,UAAU,CAAC,aAAarM,EAAKoM,aACpC,MAAO3W,GACPiW,EAAM,YAAajW,YAGlB,GAAsB,UAAlBuK,EAAKoM,SAEd,OADA7W,GAAOyW,UACAhO,EAASlD,MAAM,0BAIxB,MAAMwR,GAAYtM,EAAKuM,MACjBC,GAAWxM,EAAKwM,SAChBH,GAAY,GA+DlB,GA9DIrM,EAAKqM,YACuB,iBAAnBrM,EAAKqM,YACdrM,EAAKqM,UAAYrM,EAAKqM,UAAUhO,MAAM,MAEpC2B,EAAKqM,UAAU1S,QACjBqG,EAAKqM,UAAUrQ,SAAQyQ,IAChBJ,GAAUhE,SAASoE,EAAOA,EAAK1G,SAClCsG,GAAUlP,KAAKsP,OAMvB/N,EAAMe,cAAgB0I,GAAQ,KAC5BzJ,EAAMgB,gBACN,IACEnK,GAAOoR,SAASF,GAAeC,GAAa4F,IAC5C,MAAO7W,GACPiW,EAAM,WAAYjW,GAEpB,IACEF,GAAO8W,UAAUA,IACjB,MAAO5W,GACPiW,EAAM,YAAajW,GAErB,GAAI+W,GAAU,CACZ,IAAIE,EACJ,IACEA,EAAOnX,GAAOoX,aACd,MAAOlX,GACPiW,EAAM,wBAAyBjW,GAEjC,OAAG,CACDiJ,EAAMgB,gBACN,IACEnK,GAAOoR,SAASF,GAAeC,GAAa4F,IAC5C,MAAO7W,GACPiW,EAAM,sBAAuBjW,GAE/B,IACEF,GAAO8W,UAAUA,IACjB,MAAO5W,GACPiW,EAAM,uBAAwBjW,GAEhC,IAAImX,EACJ,IACEA,EAAOrX,GAAOoX,aACd,MAAOlX,GACPiW,EAAM,wBAAyBjW,GAEjC,GAAImX,EAAKjT,QAAU+S,EAAK/S,OAAQ,CAC1BiT,EAAKjT,OAAS+S,EAAK/S,QACrBkD,EAAO0D,MAAM,iCAAiC1J,KAEhD,MAEF6V,EAAOE,QAMR5M,EAAK6M,OAAQ,CACI,MAAhB7M,EAAK8M,UACc,MAAjB9M,EAAK+M,UAAoB,WAAWlM,KAAKb,EAAK8M,SAChD9M,EAAK+M,SAAW/M,EAAK8M,QACG,MAAf9M,EAAKgN,QAAkB,QAAQnM,KAAKb,EAAK8M,SAClD9M,EAAKgN,OAAShN,EAAK8M,QACS,MAAnB9M,EAAKiN,aACdjN,EAAKiN,WAAajN,EAAK8M,UAI3B,IAAII,GAAY,EACZC,EAA6B,MAAjBnN,EAAK+M,UACc,MAAnB/M,EAAKiN,YACU,MAAfjN,EAAKgN,QACW,MAAhBhN,EAAKoN,SACW,MAAhBpN,EAAKqN,QAGrB,GAAuB,MAAnBrN,EAAKiN,WAAoB,CAC3B,IAOIK,EAPAlM,EAAWnL,EAAKmL,SAASpB,EAAKiN,YAC9BM,EAAiC,MAAlBvN,EAAKwE,UACpBxE,EAAKwE,UAAU7K,OACbqG,EAAKwE,UACL,KAAKpD,QACP,KAoBJ,GAjBA1C,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEmF,EAAO/X,GAAOoX,WAAWY,GACzB,MAAO9X,GACPiW,EAAM,aAAcjW,OAIpBuK,EAAKiN,WAAWtT,OAClBkE,EAAUmC,EAAKiN,WAAYK,EAAKE,OAAQ1M,IAExC2M,GAAYH,EAAKE,QACjBN,GAAY,GAIQ,IAAlBI,EAAK9I,UACP,GAAIxE,EAAKiN,WAAWtT,OAAQ,CAC1B,IAAIiO,EAAM5N,KAAK+F,MAAMuN,EAAK9I,WAC1BoD,EAAI8F,WAAa,KAAKtM,IACtB,IAAItD,EAAW,GACf8J,EAAIpL,QAAQR,SAAQ,CAACI,EAAMuR,KACzB,IAAIC,EAAO3V,EAAe4V,UAAU/K,GAAS5K,EAAYkE,EAAKnF,QAAQ4I,EAAUtI,GAAI,MACpF,GAAY,MAARqW,EAAc,OAAO5P,EAASlD,MAAM,mBAAmBsB,kBAC3D0B,EAAS6P,GAASC,KAEpBhG,EAAIkG,eAAiBhQ,EACrBD,EAAU5H,EAAKiE,KACbjE,EAAKqL,QAAQtB,EAAKiN,YAClBhX,EAAKmL,SAASmM,IACdtW,QAAQ,QAAS,IAAK+C,KAAKC,UAAU2N,GAAM9G,QAE7CjE,EAAO0D,MAAM,+CAA+C1J,KAMlE,GAAqB,MAAjBmJ,EAAK+M,WAAqBI,EAAW,CACvC,IAAIvL,EACJ,GAAqB,MAAjB5B,EAAK+M,UAAoB/M,EAAK+M,SAASpT,OAAQ,CAGjD,IAAIoU,EAAa/N,EAAK+M,SAASzF,SAAS,SACxC5I,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEvG,EAAMmM,EACFxY,GAAOyY,WACPzY,GAAO0Y,aAAY,GACvB,MAAOxY,GACPiW,EAAM,WAAYjW,OAGtBoI,EAAUmC,EAAK+M,SAAUnL,EAAKd,QACpBoM,IACVxO,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEvG,EAAMrM,GAAO0Y,aAAY,GACzB,MAAOxY,GACPiW,EAAM,WAAYjW,OAGtBgY,GAAY7L,IAKhB,GAAoB,MAAhB5B,EAAKqN,QAAiB,CACxB,IAAIa,EACAlO,EAAKqN,QAAQ1T,QACf+E,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACE+F,EAAMjW,EAAekW,SAASrL,IAC9B,MAAOrN,GACPiW,EAAM,WAAYjW,OAGtBoI,EAAUmC,EAAKqN,QAASlV,EAAY+V,GAAMpN,IAChCoM,IACVxO,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACE+F,EAAMjW,EAAekW,SAASrL,IAC9B,MAAOrN,GACPiW,EAAM,WAAYjW,OAGtBgY,GAAYtV,EAAY+V,IACxBhB,GAAY,GAKhB,GAAoB,MAAhBlN,EAAKoN,QAAiB,CACxB,IAAIgB,EACApO,EAAKoN,QAAQzT,QACf+E,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEiG,EAAMnW,EAAeoW,SAASvL,IAC9B,MAAOrN,GACPiW,EAAM,WAAYjW,OAGtBoI,EAAUmC,EAAKoN,QAASjV,EAAYiW,GAAMtN,IAChCoM,IACVxO,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEiG,EAAMnW,EAAeoW,SAASvL,IAC9B,MAAOrN,GACPiW,EAAM,WAAYjW,OAGtBgY,GAAYtV,EAAYiW,IACxBlB,GAAY,GAKhB,GAAmB,MAAflN,EAAKgN,OAAgB,CACvB,IAAIsB,EACAtO,EAAKgN,OAAOrT,QACd+E,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEmG,EAAK/Y,GAAOgZ,YACZ,MAAO9Y,GACPiW,EAAM,SAAUjW,OAGpBoI,EAAUmC,EAAKgN,OAAQsB,EAAIxN,IACjBoM,IACVxO,EAAMY,YACNZ,EAAMW,UAAY8I,GAAQ,KACxB,IACEmG,EAAK/Y,GAAOgZ,YACZ,MAAO9Y,GACPiW,EAAM,SAAUjW,OAGpBgY,GAAYa,KAUlB,OALA/Y,GAAOyW,UACHhM,EAAKmI,SACPqG,EAAW9P,EAAO7B,GAGbmB,EAAS,MAEhB,SAASO,GAAa8I,EAAUvG,GAC9B,IAAI1E,EAAOnG,EAAKwR,QAAQ3G,EAASuG,GACjC,IACE,IAAIuG,EAKJ,OAJAlP,EAAME,YACNF,EAAMC,UAAYwJ,GAAQ,KACxByF,EAAO5X,EAAGoE,aAAagC,EAAM,WAExBwR,EACP,MAAOnY,GACP,OAAO,MAIX,SAAS+I,GAAc6I,EAAUvJ,EAAUgD,GACzC,IASE,OARApC,EAAMI,aACNJ,EAAMG,WAAasJ,GAAQ,KACzB,MAAMsG,EAAUxY,EAAKwR,QAAQ3G,EAAS7K,EAAKqL,QAAQ+F,IACnDA,EAAWpR,EAAKmL,SAASiG,GACzB,MAAMqH,EAAiBzY,EAAKiE,KAAKuU,EAASpH,GACrCrR,EAAG2Y,WAAWF,IAAUnY,EAAOmY,GACpCzY,EAAG4D,cAAc8U,EAAgB5Q,OAE5B,EACP,MAAOrI,GACP,OAAO,GAIX,SAASgJ,GAAc6C,EAASR,GAC9B,IAAI/E,EACJ,IAME,OALA2C,EAAME,YACNF,EAAMC,UAAYwJ,GAAQ,KACxBpM,EAAQ/F,EAAG4Y,YAAY3Y,EAAKiE,KAAK4G,EAASQ,IACvCuN,QAAO5S,GAAQ4D,EAAUnI,YAAYmJ,KAAK5E,QAExCF,EACP,MAAOtG,GACP,OAAO,MAIX,SAASgY,GAAY3P,GACd2P,GAAYqB,OACfpQ,EAAMI,aACN2O,GAAYqB,MAAO,GAErBpQ,EAAMG,WAAasJ,GAAQ,KACzBxL,EAAO4D,MAAMzC,QAKnB,MAAMiR,EAAWxW,OAAOmF,UAAUqR,SAElC,SAASC,EAASjW,GAChB,MAA8B,oBAAvBgW,EAASnR,KAAK7E,GAGvB,SAASyI,EAAYvF,EAAM6E,EAASrD,GAClC,MAAMK,EAAWL,EAASxB,EAAM6E,GAC1BmO,EAAWhZ,EAAKiE,KAAK4G,EAAS7E,GACpC,IAAK6B,EAAU,OAAO,KAGtB,IAAIoD,EACJ,IACEA,EAASlH,KAAK+F,MAAMjC,GACpB,MAAMoR,GACN,MAAM,IAAIpU,MAAM,+BAA+BmU,KAIjD,GAAI/N,EAAO5F,UAAY0T,EAAS9N,EAAO5F,SACrC,MAAM,IAAIR,MAAM,sCAAsCmU,KAGxD,GAAI/N,EAAOiO,UAAY/R,MAAMC,QAAQ6D,EAAOiO,SAC1C,MAAM,IAAIrU,MAAM,qCAAqCmU,KAGvD,GAAI/N,EAAOiB,QAAS,CAClB,IAAK6M,EAAS9N,EAAOiB,SACnB,MAAM,IAAIrH,MAAM,sCAAsCmU,KAExD,MAAM9M,EAAU5J,OAAOuE,KAAKoE,EAAOiB,SACnC,IAAK,IAAIqD,EAAI,EAAGA,EAAIrD,EAAQxI,OAAQ6L,IAAK,CACvC,MAAM5K,EAASuH,EAAQqD,GACvB,IAAKwJ,EAAS9N,EAAOiB,QAAQvH,IAC3B,MAAM,IAAIE,MAAM,oBAAoBF,uBAA4BqU,MAKtE,GAAI/N,EAAOsB,SAAqC,iBAAnBtB,EAAOsB,QAClC,MAAM,IAAI1H,MAAM,qCAAqCmU,KAGvD,OAAO/N,EAMT,SAAS4J,EAAiBhI,EAASjG,EAAQkO,GAEzC,IADA,IAAIF,EAAY,IACb,CACD,IAAIuE,EAAgBnX,EAAeoX,eAAevM,GAClD,IAAKsM,EAAe,MAQpB,GAPAhX,EAAMgX,GACFvS,GACFA,EAAO0D,MACLpI,EAAYF,EAAeqX,iBAAiBF,EAAevS,EAAO0S,OAAO,IACzE1Y,EAAMA,GAGNkU,EAAkB,CACpB,MAAMyE,EAAa3X,EAAOuX,EAAenX,EAAewX,mBAClDC,EAAQ7X,EAAO2X,EAAWE,MAAOzX,EAAe0X,OAChDC,EAAe/X,EAAO2X,EAAWI,aAAc3X,EAAe0X,OAC9DE,EAAcH,EAAQ7X,EAAO6X,EAAMI,OAAQ7X,EAAe8X,QAAU,KACpEC,EAAqBJ,EAAe/X,EAAO+X,EAAaE,OAAQ7X,EAAe8X,QAAU,KAE/FhF,EAAiB,CACfC,QAAS7S,EAAYqX,EAAWxE,SAChCrK,KAAM6O,EAAW7O,KACjBsP,SAAUT,EAAWS,SACrBP,MAAOA,EAAQ,CACbQ,MAAOR,EAAMQ,MACbC,IAAKT,EAAMS,IACXL,OAAQD,EAAc,CACpBtE,eAAgBpT,EAAY0X,EAAYtE,iBACtC,MACF,KACJqE,aAAcA,EAAe,CAC3BM,MAAON,EAAaM,MACpBC,IAAKP,EAAaO,IAClBL,OAAQE,EAAqB,CAC3BzE,eAAgBpT,EAAY6X,EAAmBzE,iBAC7C,MACF,OAGJtT,EAAemY,QAAQhB,MAAkBvE,EAC7CxS,EAAQ+W,GAEV,OAAOvE,EAMT,SAASwF,IACP,MAAO,CACL1R,SAAU,EACVC,UAAW,EACXC,UAAW,EACXC,WAAY,EACZC,UAAW,EACXC,WAAY,EACZC,eAAgB,EAChBC,gBAAiB,EACjBC,YAAa,EACbC,aAAc,EACdC,SAAU,EACVC,UAAW,EACXC,aAAc,EACdC,cAAe,EACfC,aAAc,EACdC,cAAe,EACfC,cAAe,EACfC,eAAgB,GAOpB,SAASuI,EAAQmI,GACf,MAAMJ,EAAQha,EAAQqa,SACtBD,IACA,MAAME,EAAQta,EAAQqa,OAAOL,GAC7B,OAAkB,IAAXM,EAAM,GAAWA,EAAM,GAWhC,SAASC,EAAWC,GAClB,OAAOA,EAAO,IAAIA,EAAO,KAAKC,QAAQ,QAAU,MAMlD,SAASnC,EAAW9P,EAAOjC,GACzB,MAAMmU,EAAS,CAACF,EAAMG,IAAU,GAdlC,SAAa7Z,EAAK8Z,GAChB,KAAO9Z,EAAI2C,OAASmX,GAAK9Z,EAAM,IAAIA,IACnC,OAAOA,EAY4B+Z,CAAIN,EAAWC,GAAO,UAAUG,KAClEpU,GAAUvG,EAAQyG,QAAQ4D,MAAM,CAC/B,gBAAkBqQ,EAAOlS,EAAMC,SAAUD,EAAME,WAC/C,gBAAkBgS,EAAOlS,EAAMG,UAAWH,EAAMI,YAChD,gBAAkB8R,EAAOlS,EAAMK,UAAWL,EAAMM,YAChD,gBAAkB4R,EAAOlS,EAAMO,eAAgBP,EAAMQ,iBACrD,gBAAkB0R,EAAOlS,EAAMS,YAAaT,EAAMU,cAClD,gBAAkBwR,EAAOlS,EAAMW,SAAUX,EAAMY,WAC/C,gBAAkBsR,EAAOlS,EAAMa,aAAcb,EAAMc,eACnD,gBAAkBoR,EAAOlS,EAAMe,aAAcf,EAAMgB,eACnD,gBAAkBkR,EAAOlS,EAAMiB,cAAejB,EAAMkB,gBACpD,IACA1F,KAAKrD,GAAOA,GAhHhBvB,EAAQkM,YAAcA,EAgDtBlM,EAAQwV,iBAAmBA,EA0B3BxV,EAAQ+a,YAAcA,EAUtB/a,EAAQ6S,QAAUA,EAYlB7S,EAAQmb,WAAaA,EAmBrBnb,EAAQkZ,WAAaA,EAErB,IAAIwC,OAAgC,IAAX,EAAAva,GAA0B,EAAAA,EAAOwa,OACtD,EAAAxa,EAAOwa,OAAOC,aAAe,CAACJ,GAAO,IAAI,EAAAra,EAAOwa,OAAOH,IACvDA,GAAO,IAAIK,WAAWL,GAG1B,SAASlU,EAAmB0T,GAC1B,IAAIc,EAAS,GA6Bb,OA5BAA,EAAO7Q,MAAQ,SAAS8Q,GAEtB,GADIf,GAAIA,EAAGe,GACU,iBAAVA,EAAoB,CAC7B,IAAIC,EAASN,EAAY7a,EAAKwD,OAAO0X,IACrClb,EAAKoK,MAAM8Q,EAAOC,EAAQ,GAC1BD,EAAQC,EAEVzb,KAAKsH,KAAKkU,IAEZD,EAAOG,MAAQ,WACbH,EAAOzX,OAAS,GAElByX,EAAOI,SAAW,WAEhB,IADA,IAAIC,EAAS,EAAGjM,EAAI,EAAGC,EAAI5P,KAAK8D,OACzB6L,EAAIC,GAAGgM,GAAU5b,KAAK2P,KAAK7L,OAClC,IAAI2X,EAASN,EAAYS,GAEzB,IADAA,EAASjM,EAAI,EACNA,EAAIC,GACT6L,EAAO9G,IAAI3U,KAAK2P,GAAIiM,GACpBA,GAAU5b,KAAK2P,GAAG7L,SAChB6L,EAEJ,OAAO8L,GAETF,EAAOrC,SAAW,WAChB,IAAIuC,EAASzb,KAAK2b,WAClB,OAAOrb,EAAKub,KAAKJ,EAAQ,EAAGA,EAAO3X,SAE9ByX,EAsBT,SAAS1F,EAAMiG,EAAOlc,GACpB,MAAMmc,EAAMxb,EAAWwK,IAAI,MAC3BtH,QAAQyO,MAAM,CACZlR,EACA+a,EAAK,0DAA2DD,EAAO,OAAQ9a,EAC/E+a,EAAK/a,EACL+a,EAAK,uDAAwD/a,EAC7D+a,EAAK/a,EACLpB,EAAEsF,MAAM9D,QAAQ,MAAO2a,GAAM/a,EAC7B+a,EAAK/a,EACL+a,EAAK,8EAA+E/a,EACpF+a,EAAK,6EAA8E/a,EACnF+a,EAAK/a,EACL+a,EAAK,8EAA+E/a,EACpF+a,EAAK,8EAA+E/a,EACpF+a,EAAK/a,EACL+a,EAAK,aAAc/a,GACnBqD,KAAK,KACPhE,EAAQ2b,KAAK,GArCfvc,EAAQsH,mBAAqBA,EAG7BtH,EAAQwc,WAAa,CACnBC,cAAc,EACdC,eAAe,EACfC,mBAAmB,EACnBC,gBAAgB,EAChBC,eAAe,EACfC,kBAAkB,EAClBC,wBAAwB,EACxBzX,OAAQ,SACRrF,OAAQ,WACR+c,OAAO,EACPC,MAAO,GACPC,SAAS,I,i2RC17CXjd,EAAOD,QAAU,I,cCAjB,MAAMY,EAAU,EAAQ,KA0BxB,SAASuc,EAAWxc,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAIyc,UAAU,mCAAqC1Y,KAAKC,UAAUhE,IAK5E,SAAS0c,EAAqB1c,EAAM2c,GAMlC,IALA,IAIIjS,EAJAkS,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAEFxN,EAAI,EAAGA,GAAKvP,EAAK0D,SAAU6L,EAAG,CACrC,GAAIA,EAAIvP,EAAK0D,OACXgH,EAAO1K,EAAKgd,WAAWzN,OACpB,IAAa,KAAT7E,EACP,MAEAA,EAAO,GACT,GAAa,KAATA,EAAmB,CACrB,GAAIoS,IAAcvN,EAAI,GAAc,IAATwN,QAEpB,GAAID,IAAcvN,EAAI,GAAc,IAATwN,EAAY,CAC5C,GAAIH,EAAIlZ,OAAS,GAA2B,IAAtBmZ,GAA8D,KAAnCD,EAAII,WAAWJ,EAAIlZ,OAAS,IAAgD,KAAnCkZ,EAAII,WAAWJ,EAAIlZ,OAAS,GACpH,GAAIkZ,EAAIlZ,OAAS,EAAG,CAClB,IAAIuZ,EAAiBL,EAAIM,YAAY,KACrC,GAAID,IAAmBL,EAAIlZ,OAAS,EAAG,EACb,IAApBuZ,GACFL,EAAM,GACNC,EAAoB,GAGpBA,GADAD,EAAMA,EAAI7I,MAAM,EAAGkJ,IACKvZ,OAAS,EAAIkZ,EAAIM,YAAY,KAEvDJ,EAAYvN,EACZwN,EAAO,EACP,eAEG,GAAmB,IAAfH,EAAIlZ,QAA+B,IAAfkZ,EAAIlZ,OAAc,CAC/CkZ,EAAM,GACNC,EAAoB,EACpBC,EAAYvN,EACZwN,EAAO,EACP,SAGAJ,IACEC,EAAIlZ,OAAS,EACfkZ,GAAO,MAEPA,EAAM,KACRC,EAAoB,QAGlBD,EAAIlZ,OAAS,EACfkZ,GAAO,IAAM5c,EAAK+T,MAAM+I,EAAY,EAAGvN,GAEvCqN,EAAM5c,EAAK+T,MAAM+I,EAAY,EAAGvN,GAClCsN,EAAoBtN,EAAIuN,EAAY,EAEtCA,EAAYvN,EACZwN,EAAO,OACW,KAATrS,IAAyB,IAAVqS,IACtBA,EAEFA,GAAQ,EAGZ,OAAOH,EAeT,IAAIO,EAAQ,CAEV3L,QAAS,WAKP,IAJA,IAEIE,EAFA0L,EAAe,GACfC,GAAmB,EAGd9N,EAAIvF,UAAUtG,OAAS,EAAG6L,IAAM,IAAM8N,EAAkB9N,IAAK,CACpE,IAAIvP,EACAuP,GAAK,EACPvP,EAAOgK,UAAUuF,SAELmE,IAARhC,IACFA,EAAMzR,EAAQyR,OAChB1R,EAAO0R,GAGT8K,EAAWxc,GAGS,IAAhBA,EAAK0D,SAIT0Z,EAAepd,EAAO,IAAMod,EAC5BC,EAA0C,KAAvBrd,EAAKgd,WAAW,IASrC,OAFAI,EAAeV,EAAqBU,GAAeC,GAE/CA,EACED,EAAa1Z,OAAS,EACjB,IAAM0Z,EAEN,IACAA,EAAa1Z,OAAS,EACxB0Z,EAEA,KAIXtS,UAAW,SAAmB9K,GAG5B,GAFAwc,EAAWxc,GAES,IAAhBA,EAAK0D,OAAc,MAAO,IAE9B,IAAIyR,EAAoC,KAAvBnV,EAAKgd,WAAW,GAC7BM,EAAyD,KAArCtd,EAAKgd,WAAWhd,EAAK0D,OAAS,GAQtD,OAHoB,KAFpB1D,EAAO0c,EAAqB1c,GAAOmV,IAE1BzR,QAAiByR,IAAYnV,EAAO,KACzCA,EAAK0D,OAAS,GAAK4Z,IAAmBtd,GAAQ,KAE9CmV,EAAmB,IAAMnV,EACtBA,GAGTmV,WAAY,SAAoBnV,GAE9B,OADAwc,EAAWxc,GACJA,EAAK0D,OAAS,GAA4B,KAAvB1D,EAAKgd,WAAW,IAG5C/Y,KAAM,WACJ,GAAyB,IAArB+F,UAAUtG,OACZ,MAAO,IAET,IADA,IAAI6Z,EACKhO,EAAI,EAAGA,EAAIvF,UAAUtG,SAAU6L,EAAG,CACzC,IAAIzM,EAAMkH,UAAUuF,GACpBiN,EAAW1Z,GACPA,EAAIY,OAAS,SACAgQ,IAAX6J,EACFA,EAASza,EAETya,GAAU,IAAMza,GAGtB,YAAe4Q,IAAX6J,EACK,IACFJ,EAAMrS,UAAUyS,IAGzBtJ,SAAU,SAAkB9J,EAAMqT,GAIhC,GAHAhB,EAAWrS,GACXqS,EAAWgB,GAEPrT,IAASqT,EAAI,MAAO,GAKxB,IAHArT,EAAOgT,EAAM3L,QAAQrH,OACrBqT,EAAKL,EAAM3L,QAAQgM,IAEF,MAAO,GAExB,GAAa,MAATrT,EAAc,OAAOqT,EAIzB,IADA,IAAIC,EAAY,EACTA,EAAYtT,EAAKzG,QACa,KAA/ByG,EAAK6S,WAAWS,KADYA,GASlC,IALA,IAAIC,EAAUvT,EAAKzG,OACfia,EAAUD,EAAUD,EAGpBG,EAAU,EACPA,EAAUJ,EAAG9Z,QACa,KAA3B8Z,EAAGR,WAAWY,KADUA,GAW9B,IAPA,IACIC,EADQL,EAAG9Z,OACKka,EAGhBla,EAASia,EAAUE,EAAQF,EAAUE,EACrCC,GAAiB,EACjBvO,EAAI,EACDA,GAAK7L,IAAU6L,EAAG,CACvB,GAAIA,IAAM7L,EAAQ,CAChB,GAAIma,EAAQna,EAAQ,CAClB,GAAmC,KAA/B8Z,EAAGR,WAAWY,EAAUrO,GAG1B,OAAOiO,EAAGzJ,MAAM6J,EAAUrO,EAAI,GACzB,GAAU,IAANA,EAGT,OAAOiO,EAAGzJ,MAAM6J,EAAUrO,QAEnBoO,EAAUja,IACoB,KAAnCyG,EAAK6S,WAAWS,EAAYlO,GAG9BuO,EAAgBvO,EACD,IAANA,IAGTuO,EAAgB,IAGpB,MAEF,IAAIC,EAAW5T,EAAK6S,WAAWS,EAAYlO,GAE3C,GAAIwO,IADSP,EAAGR,WAAWY,EAAUrO,GAEnC,MACoB,KAAbwO,IACPD,EAAgBvO,GAGpB,IAAI5D,EAAM,GAGV,IAAK4D,EAAIkO,EAAYK,EAAgB,EAAGvO,GAAKmO,IAAWnO,EAClDA,IAAMmO,GAAkC,KAAvBvT,EAAK6S,WAAWzN,KAChB,IAAf5D,EAAIjI,OACNiI,GAAO,KAEPA,GAAO,OAMb,OAAIA,EAAIjI,OAAS,EACRiI,EAAM6R,EAAGzJ,MAAM6J,EAAUE,IAEhCF,GAAWE,EACoB,KAA3BN,EAAGR,WAAWY,MACdA,EACGJ,EAAGzJ,MAAM6J,KAIpBI,UAAW,SAAmBhe,GAC5B,OAAOA,GAGTqL,QAAS,SAAiBrL,GAExB,GADAwc,EAAWxc,GACS,IAAhBA,EAAK0D,OAAc,MAAO,IAK9B,IAJA,IAAIgH,EAAO1K,EAAKgd,WAAW,GACvBiB,EAAmB,KAATvT,EACVwP,GAAO,EACPgE,GAAe,EACV3O,EAAIvP,EAAK0D,OAAS,EAAG6L,GAAK,IAAKA,EAEtC,GAAa,MADb7E,EAAO1K,EAAKgd,WAAWzN,KAErB,IAAK2O,EAAc,CACjBhE,EAAM3K,EACN,YAIF2O,GAAe,EAInB,OAAa,IAAThE,EAAmB+D,EAAU,IAAM,IACnCA,GAAmB,IAAR/D,EAAkB,KAC1Bla,EAAK+T,MAAM,EAAGmG,IAGvB/O,SAAU,SAAkBnL,EAAMmB,GAChC,QAAYuS,IAARvS,GAAoC,iBAARA,EAAkB,MAAM,IAAIsb,UAAU,mCACtED,EAAWxc,GAEX,IAGIuP,EAHA0K,EAAQ,EACRC,GAAO,EACPgE,GAAe,EAGnB,QAAYxK,IAARvS,GAAqBA,EAAIuC,OAAS,GAAKvC,EAAIuC,QAAU1D,EAAK0D,OAAQ,CACpE,GAAIvC,EAAIuC,SAAW1D,EAAK0D,QAAUvC,IAAQnB,EAAM,MAAO,GACvD,IAAIme,EAAShd,EAAIuC,OAAS,EACtB0a,GAAoB,EACxB,IAAK7O,EAAIvP,EAAK0D,OAAS,EAAG6L,GAAK,IAAKA,EAAG,CACrC,IAAI7E,EAAO1K,EAAKgd,WAAWzN,GAC3B,GAAa,KAAT7E,GAGF,IAAKwT,EAAc,CACjBjE,EAAQ1K,EAAI,EACZ,YAGwB,IAAtB6O,IAGFF,GAAe,EACfE,EAAmB7O,EAAI,GAErB4O,GAAU,IAERzT,IAASvJ,EAAI6b,WAAWmB,IACR,KAAZA,IAGJjE,EAAM3K,IAKR4O,GAAU,EACVjE,EAAMkE,IAOd,OADInE,IAAUC,EAAKA,EAAMkE,GAAmC,IAATlE,IAAYA,EAAMla,EAAK0D,QACnE1D,EAAK+T,MAAMkG,EAAOC,GAEzB,IAAK3K,EAAIvP,EAAK0D,OAAS,EAAG6L,GAAK,IAAKA,EAClC,GAA2B,KAAvBvP,EAAKgd,WAAWzN,IAGlB,IAAK2O,EAAc,CACjBjE,EAAQ1K,EAAI,EACZ,YAEgB,IAAT2K,IAGTgE,GAAe,EACfhE,EAAM3K,EAAI,GAId,OAAa,IAAT2K,EAAmB,GAChBla,EAAK+T,MAAMkG,EAAOC,IAI7BmE,QAAS,SAAiBre,GACxBwc,EAAWxc,GAQX,IAPA,IAAIse,GAAY,EACZC,EAAY,EACZrE,GAAO,EACPgE,GAAe,EAGfM,EAAc,EACTjP,EAAIvP,EAAK0D,OAAS,EAAG6L,GAAK,IAAKA,EAAG,CACzC,IAAI7E,EAAO1K,EAAKgd,WAAWzN,GAC3B,GAAa,KAAT7E,GASS,IAATwP,IAGFgE,GAAe,EACfhE,EAAM3K,EAAI,GAEC,KAAT7E,GAEgB,IAAd4T,EACFA,EAAW/O,EACY,IAAhBiP,IACPA,EAAc,IACO,IAAdF,IAGTE,GAAe,QArBf,IAAKN,EAAc,CACjBK,EAAYhP,EAAI,EAChB,OAuBN,OAAkB,IAAd+O,IAA4B,IAATpE,GAEH,IAAhBsE,GAEgB,IAAhBA,GAAqBF,IAAapE,EAAM,GAAKoE,IAAaC,EAAY,EACjE,GAEFve,EAAK+T,MAAMuK,EAAUpE,IAG9BS,OAAQ,SAAgB8D,GACtB,GAAmB,OAAfA,GAA6C,iBAAfA,EAChC,MAAM,IAAIhC,UAAU,0EAA4EgC,GAElG,OAzVJ,SAAiBC,EAAKD,GACpB,IAAIE,EAAMF,EAAWE,KAAOF,EAAWtf,KACnCyf,EAAOH,EAAWG,OAASH,EAAWtY,MAAQ,KAAOsY,EAAWtd,KAAO,IAC3E,OAAKwd,EAGDA,IAAQF,EAAWtf,KACdwf,EAAMC,EAERD,EAAMD,EAAME,EALVA,EAqVAC,CAAQ,IAAKJ,IAGtB3U,MAAO,SAAe9J,GACpBwc,EAAWxc,GAEX,IAAI8e,EAAM,CAAE3f,KAAM,GAAIwf,IAAK,GAAIC,KAAM,GAAIzd,IAAK,GAAIgF,KAAM,IACxD,GAAoB,IAAhBnG,EAAK0D,OAAc,OAAOob,EAC9B,IAEI7E,EAFAvP,EAAO1K,EAAKgd,WAAW,GACvB7H,EAAsB,KAATzK,EAEbyK,GACF2J,EAAI3f,KAAO,IACX8a,EAAQ,GAERA,EAAQ,EAaV,IAXA,IAAIqE,GAAY,EACZC,EAAY,EACZrE,GAAO,EACPgE,GAAe,EACf3O,EAAIvP,EAAK0D,OAAS,EAIlB8a,EAAc,EAGXjP,GAAK0K,IAAS1K,EAEnB,GAAa,MADb7E,EAAO1K,EAAKgd,WAAWzN,KAUV,IAAT2K,IAGFgE,GAAe,EACfhE,EAAM3K,EAAI,GAEC,KAAT7E,GAEgB,IAAd4T,EAAiBA,EAAW/O,EAA2B,IAAhBiP,IAAmBA,EAAc,IACrD,IAAdF,IAGTE,GAAe,QAlBf,IAAKN,EAAc,CACjBK,EAAYhP,EAAI,EAChB,MAyCN,OArBkB,IAAd+O,IAA4B,IAATpE,GAEP,IAAhBsE,GAEgB,IAAhBA,GAAqBF,IAAapE,EAAM,GAAKoE,IAAaC,EAAY,GACvD,IAATrE,IACiC4E,EAAIF,KAAOE,EAAI3Y,KAAhC,IAAdoY,GAAmBpJ,EAAkCnV,EAAK+T,MAAM,EAAGmG,GAAgCla,EAAK+T,MAAMwK,EAAWrE,KAG7G,IAAdqE,GAAmBpJ,GACrB2J,EAAI3Y,KAAOnG,EAAK+T,MAAM,EAAGuK,GACzBQ,EAAIF,KAAO5e,EAAK+T,MAAM,EAAGmG,KAEzB4E,EAAI3Y,KAAOnG,EAAK+T,MAAMwK,EAAWD,GACjCQ,EAAIF,KAAO5e,EAAK+T,MAAMwK,EAAWrE,IAEnC4E,EAAI3d,IAAMnB,EAAK+T,MAAMuK,EAAUpE,IAG7BqE,EAAY,EAAGO,EAAIH,IAAM3e,EAAK+T,MAAM,EAAGwK,EAAY,GAAYpJ,IAAY2J,EAAIH,IAAM,KAElFG,GAGTJ,IAAK,IACLK,UAAW,IACXC,MAAO,KACP7B,MAAO,MAGTA,EAAMA,MAAQA,EAEd7d,EAAOD,QAAU8d,G,cClhBjB7d,EAAOD,QAAU,CACfsB,SAAU,QACV+Q,IAAG,IACM,IAETuN,MAAK,IACI,EAET3E,OA0CF,SAAgB4E,GACd,IAAIC,EAAYC,EAAezX,KAAK0X,GAChCC,EAAU3O,KAAK4O,MAAkB,KAAZJ,GACrBK,EAAc7O,KAAK4O,MAAkB,IAAZJ,EAA4B,IAAVG,GAC3CJ,IACFI,GAAWJ,EAAkB,IAC7BM,GAAeN,EAAkB,IACf,IAChBI,IACAE,GAAe,MAGnB,MAAO,CAAEF,EAASE,IArDlB5c,KAAM,GACN,KAAK8H,EAAO,GACV,MAAM7F,MAAM,QAAQ6F,OA8BxB,IAAI2U,EAAc,EAAA7e,EAAO6e,aAAe,GACpCD,EACFC,EAAYvb,KACZub,EAAYI,QACZJ,EAAYK,OACZL,EAAYM,MACZN,EAAYO,WACZ,WAAY,OAAO,IAAK/b,MAAQgc,Y,YC3ClC,IAAIC,EAA0B,oBAAZ7f,SAA2BA,SAAW,GACpD8f,EAAOD,EAAKE,KAAO,OAAQF,EAAKE,IAEpC,SAAS7V,EAAKgR,EAAQyD,GACpB,IAAIqB,EAASrB,GAAQ,GAUrB,OATAqB,EAAO/V,UAAaiR,KAAYA,EAAO7B,OAAUyG,EACjDE,EAAOC,KAAOvI,GAAQsI,EAAO/V,UAAY7K,EAAQ8gB,KAAOxI,EAAOtY,EAAQ+gB,MAAQzI,EAC/EsI,EAAOtV,IAAMgN,GAAQsI,EAAO/V,UAAY7K,EAAQghB,IAAM1I,EAAOtY,EAAQ+gB,MAAQzI,EAC7EsI,EAAOK,MAAQ3I,GAAQsI,EAAO/V,UAAY7K,EAAQkhB,MAAQ5I,EAAOtY,EAAQ+gB,MAAQzI,EACjFsI,EAAO1V,OAASoN,GAAQsI,EAAO/V,UAAY7K,EAAQmhB,OAAS7I,EAAOtY,EAAQ+gB,MAAQzI,EACnFsI,EAAOQ,KAAO9I,GAAQsI,EAAO/V,UAAY7K,EAAQqhB,KAAO/I,EAAOtY,EAAQ+gB,MAAQzI,EAC/EsI,EAAOU,QAAUhJ,GAAQsI,EAAO/V,UAAY7K,EAAQuhB,QAAUjJ,EAAOtY,EAAQ+gB,MAAQzI,EACrFsI,EAAOnU,KAAO6L,GAAQsI,EAAO/V,UAAY7K,EAAQwhB,KAAOlJ,EAAOtY,EAAQ+gB,MAAQzI,EAC/EsI,EAAOpU,MAAQ8L,GAAQsI,EAAO/V,UAAY7K,EAAQyhB,MAAQnJ,EAAOtY,EAAQ+gB,MAAQzI,EAC1EsI,EAGT5gB,EAAQqH,OAASyD,EAAK2V,EAAKpZ,OAAQrH,GACnCA,EAAQuH,OAASuD,EAAK2V,EAAKlZ,QAC3BvH,EAAQ8K,KAAOA,EAEf9K,EAAQ8gB,KAAO,QACf9gB,EAAQghB,IAAM,QACdhhB,EAAQkhB,MAAQ,QAChBlhB,EAAQmhB,OAAS,QACjBnhB,EAAQqhB,KAAO,QACfrhB,EAAQuhB,QAAU,QAClBvhB,EAAQwhB,KAAO,QACfxhB,EAAQyhB,MAAQ,QAChBzhB,EAAQ+gB,MAAQ,Q,cC7BhB,MAAMrgB,EAAK,EAAQ,KACbC,EAAO,EAAQ,KAcrBX,EAAQyG,MAZR,SAASib,EAAU1V,EAASuN,GAC1B,IAAIjN,EAAM,GAQV,OAPA5L,EAAG4Y,YAAYtN,GAAStF,SAAQI,IAC1BpG,EAAGihB,SAAShhB,EAAKiE,KAAKoH,EAASlF,IAAO8a,cACxCF,EAAU/gB,EAAKiE,KAAKoH,EAASlF,GAAOyS,GAAQ7S,SAAQmb,GAASvV,EAAIzE,KAAKf,EAAO,IAAM+a,MACzEtI,GAA4B,mBAAXA,EAAuCA,EAAOhO,KAAKzE,GAA3ByS,EAAOzS,KAC1DwF,EAAIzE,KAAKf,MAGNwF,I,cCST,MAAM3L,EAAO,EAAQ,KACfD,EAAK,EAAQ,KACbE,EAAU,EAAQ,KAExBX,EAAOD,QAAU,SAASgB,EAAOqP,EAAG3F,EAAMoX,GACnCpX,GAAwB,iBAATA,IAClBA,EAAO,CAAEqX,KAAMrX,IAEjB,IAAIqX,EAAOrX,EAAKqX,UACH1N,IAAT0N,IACFA,EAAO,KAAUnhB,EAAQgf,SAEtBkC,IAAMA,EAAO,MAClBzR,EAAI1P,EAAKwR,QAAQ9B,GACjB,IACE3P,EAAGshB,UAAU3R,EAAG0R,GAChBD,EAAOA,GAAQzR,EACf,MAAO4R,GACP,OAAQA,EAAK5W,MACX,IAAK,SACHyW,EAAO9gB,EAAOL,EAAKqL,QAAQqE,GAAI3F,EAAMoX,GACrC9gB,EAAOqP,EAAG3F,EAAMoX,GAChB,MACF,QACE,IAAII,EACJ,IACEA,EAAOxhB,EAAGihB,SAAStR,GACnB,MAAO8R,GACP,MAAMF,EAER,IAAKC,EAAKN,cAAe,MAAMK,GAIrC,OAAOH,I,cCvDT,MAAMnhB,EAAO,EAAQ,KACfG,EAAa,EAAQ,KA2I3B,SAASshB,EAAcC,EAAOza,GAC5B,GAAa,MAATya,EACF,OAAQza,GACN,UAAKyM,EACL,IAAK,IAAK,OAAOiO,QAAQD,GACzB,IAAK,IAAK,OAAO/Q,KAAKiR,MAAMF,IAAU,EACtC,IAAK,IAAK,OAAOG,OAAOH,IAAU,EAClC,IAAK,IACH,OAAc,IAAVA,EAAuB,GACpBra,OAAOqa,GAEhB,IAAK,IAEH,OADKva,MAAMC,QAAQsa,KAAQA,EAAQ,CAAEA,IAC9BA,EAAM/P,KAAImQ,GAAKnR,KAAKiR,MAAME,IAAM,IAEzC,IAAK,IAEH,OADK3a,MAAMC,QAAQsa,KAAQA,EAAQ,CAAEA,IAC9BA,EAAM/P,KAAImQ,GAAKD,OAAOC,IAAM,IAErC,IAAK,IAEH,OADK3a,MAAMC,QAAQsa,KAAQA,EAAQ,CAAEA,IAC9BA,EAAM/P,IAAItK,SAvEzBhI,EAAQyK,MA5ER,SAAelH,EAAMqI,EAAQ8W,GAAoB,GAC/C,IAAI1c,EAAU,GACVgF,EAAU,GACV2H,EAAO,GACPvH,EAAW,GAGX6E,EAAU,GACdhN,OAAOuE,KAAKoE,GAAQlF,SAAQe,IAC1B,IAAIA,EAAI1F,WAAW,KAAnB,CACA,IAAI4gB,EAAS/W,EAAOnE,GACA,MAAhBkb,EAAOpS,QACmB,iBAAjBoS,EAAOpS,MAAoBN,EAAQ0S,EAAOpS,OAAS9I,EACrDK,MAAMC,QAAQ4a,EAAOpS,QAAQoS,EAAOpS,MAAM7J,SAAQ6J,GAASN,EAAQM,GAAS9I,KAEnFib,GAAuC,MAAlBC,EAAOC,UAAiB5c,EAAQyB,GAAOkb,EAAOC,aAIzE,IAAK,IAAI1S,EAAI,EAAGC,GAAK5M,EAAOA,EAAKmR,SAASrQ,OAAQ6L,EAAIC,IAAKD,EAAG,CAC5D,IAAIzM,EAAMF,EAAK2M,GACf,GAAW,MAAPzM,EAAa,GAAIyM,EAAG,MACxB,IAAoEyS,EAAQlb,EAAxEyM,EAAQ,6CAA6C2O,KAAKpf,GAC9D,GAAIyQ,EACEtI,EAAOnI,GAAMkf,EAAS/W,EAAOnE,EAAMhE,GAClB,MAAZyQ,EAAM,IACbyO,EAAS/W,EAAOnE,EAAMwI,EAAQiE,EAAM,GAAG1D,UAAU,KAC7CmS,GAAsB,MAAZzO,EAAM,KAAY3Q,EAAK2M,KAAOgE,EAAM,KAC7B,MAAZA,EAAM,KACfyO,EAAS/W,EAAOnE,EAAMyM,EAAM,GAAG1D,UAAU,IACrCmS,GAAsB,MAAZzO,EAAM,KAAY3Q,EAAK2M,KAAOgE,EAAM,SAE/C,CACL,GAAyB,IAArBzQ,EAAIka,WAAW,GACd,CAAEhL,EAAK9K,KAAKpE,GAAM,SADMkf,EAAS/W,EAAOnE,EAAMhE,GAGrD,GAAIkf,EACF,GAAIA,EAAON,MAETpf,OAAOuE,KAAKmb,EAAON,OAAO3b,SAAQyJ,GAAKnK,EAAQmK,GAAKwS,EAAON,MAAMlS,UAC5D,GAAmB,MAAfwS,EAAO/a,MAAgC,MAAhB+a,EAAO/a,KAEvC5B,EAAQyB,IAAO,OAEf,GAAIyI,EAAI,EAAI3M,EAAKc,QAAuC,IAA7Bd,EAAK2M,EAAI,GAAGyN,WAAW,GAEhD,OAAQgF,EAAO/a,MACb,IAAK,IAAK5B,EAAQyB,GAAOuB,SAASzF,IAAO2M,GAAI,IAAK,MAClD,IAAK,IAAKlK,EAAQyB,IAAQzB,EAAQyB,IAAQ,IAAIS,OAAOc,SAASzF,IAAO2M,GAAI,KAAM,MAC/E,IAAK,IAAKlK,EAAQyB,GAAOqb,WAAWvf,IAAO2M,IAAK,MAChD,IAAK,IAAKlK,EAAQyB,IAAQzB,EAAQyB,IAAQ,IAAIS,OAAO4a,WAAWvf,IAAO2M,KAAM,MAC7E,IAAK,IAAKlK,EAAQyB,GAAOO,OAAOzE,IAAO2M,IAAK,MAC5C,IAAK,IAAKlK,EAAQyB,IAAQzB,EAAQyB,IAAQ,IAAIS,OAAO3E,IAAO2M,GAAGnH,MAAM,MAAO,MAC5E,QAASiC,EAAQnD,KAAKpE,KAAQyM,OAIhC,OAAQyS,EAAO/a,MACb,IAAK,IACL,IAAK,IAAK5B,EAAQyB,GAAOkb,EAAOC,SAAW,EAAG,MAC9C,IAAK,IAAK5c,EAAQyB,GAAOkb,EAAOC,SAAW,GAAI,MAC/C,IAAK,IACL,IAAK,IACL,IAAK,IAAK5c,EAAQyB,GAAOkb,EAAOC,SAAW,GAAI,MAC/C,QAAS5X,EAAQnD,KAAKpE,QAIvBuH,EAAQnD,KAAKpE,GAEtB,KAAOyM,EAAIC,GAAG/E,EAASvD,KAAKtE,EAAK2M,MAGjC,OAFIwS,GAAmBtV,EAAYxB,EAAQ5F,GAEpC,CAAEA,UAASgF,UAASL,UAAWgI,EAAMvH,aAkD9CpL,EAAQqM,KA5CR,SAAcT,EAAQ5F,GACfA,IAASA,EAAU,IACxB,IAAI+c,EAAS/c,EAAQ+c,QAAU,EAC3BC,EAAUhd,EAAQgd,SAAW,GAC7BC,EAAMjd,EAAQid,KAAO,KACrBC,EAAe,GACfC,EAAU,GACdlgB,OAAOuE,KAAKoE,GAAQlF,SAAQe,IAC1B,IAAIkb,EAAS/W,EAAOnE,GACpB,GAA0B,MAAtBkb,EAAOS,YAAX,CAEA,IADA,IAKIC,EALA/K,EAAO,GACJA,EAAKjU,OAAS0e,GAAQzK,GAAQ,IAGrC,IAFAA,GAAQ,KAAO7Q,EACXkb,EAAOpS,QAAO+H,GAAQ,MAAQqK,EAAOpS,OAClC+H,EAAKjU,OAAS2e,GAAS1K,GAAQ,KAEjCtS,EAAQsd,cAAgBX,EAAOhI,UAC5B0I,EAAKH,EAAaP,EAAOhI,aAC7BuI,EAAaP,EAAOhI,UAAY0I,EAAK,IAGvCA,EAAKF,EAEHrb,MAAMC,QAAQ4a,EAAOS,aACvBC,EAAGxb,KAAKyQ,EAAOqK,EAAOS,YAAY,GAAKT,EAAOS,YAAY1O,MAAM,GAAGpC,KAAIiR,IACrE,IAAK,IAAIrT,EAAI,EAAGA,EAAI8S,IAAW9S,EAAGqT,EAAO,IAAMA,EAC/C,OAAON,EAAMM,KACZ3e,KAAK,KACHye,EAAGxb,KAAKyQ,EAAOqK,EAAOS,iBAE/B,IAAIC,EAAK,GACLG,GAAgB,EAUpB,OATAvgB,OAAOuE,KAAK0b,GAAcxc,SAAQiU,IAChC6I,GAAgB,EAChBH,EAAGxb,KAAKob,EAAM,IAAMniB,EAAW+f,KAAKlG,GAAYsI,GAChDI,EAAGxb,KAAKqb,EAAavI,GAAU/V,KAAKqe,OAElCO,GACFH,EAAGxb,KAAKob,EAAM,IAAMniB,EAAW+f,KAAK,SAAWoC,GAEjDI,EAAGxb,KAAKsb,EAAQve,KAAKqe,IACdI,EAAGze,KAAKqe,IAkGjBjjB,EAAQ+M,MA/DR,SAAenB,EAAQ6X,EAAgBC,EAAeC,GACpD,MAAMC,EAAgB,GACtB,IAAK,MAAOnc,GAAK,KAAEG,EAAI,kBAAEic,EAAiB,OAAEC,EAAM,kBAAEC,EAAiB,QAAEC,MAAc/gB,OAAOmJ,QAAQR,GAAS,CAC3G,IAAIqY,EAAe7B,EAAcqB,EAAehc,GAAMG,GAClDsc,EAAc9B,EAAcsB,EAAcjc,GAAMG,GACpD,GAAoB,MAAhBqc,GACF,GAAmB,MAAfC,EAAqB,CAEvB,GAAIF,EAAS,SACb,GAAIlc,MAAMC,QAAQmc,GAAc,CAC9B,IAAIC,EACAL,IACFI,EAAcA,EAAY5R,KAAI+P,GAAS1W,EAAY0W,EAAOsB,EAAeI,MAElD,MAArBF,IAA8BM,EAAUV,EAAeI,IACzDD,EAAcnc,GAAOyc,EAAY3K,QAAO8I,IAAU8B,EAAQpR,SAASsP,KAEnEuB,EAAcnc,GAAOyc,EAAYxP,aAG/BoP,IACFI,EAAcvY,EAAYuY,EAAaP,EAAeI,IAExDH,EAAcnc,GAAOyc,QAGpB,GAAmB,MAAfA,EAELpc,MAAMC,QAAQkc,GAChBL,EAAcnc,GAAOwc,EAAavP,QAElCkP,EAAcnc,GAAOwc,OAIvB,GAAInc,MAAMC,QAAQkc,GAAe,CAC/B,GAAID,EAAS,CACXJ,EAAcnc,GAAOwc,EAAavP,QAClC,SAEF,IAAIyP,EACAL,IACFI,EAAcA,EAAY5R,KAAI+P,GAAS1W,EAAY0W,EAAOsB,EAAeI,MAElD,MAArBF,IAA8BM,EAAUV,EAAeI,IACzDD,EAAcnc,GAAO,IAChBwc,KACAC,EAAY3K,QAAO8I,IAAU4B,EAAalR,SAASsP,KAAW8B,EAAQpR,SAASsP,MAGpFuB,EAAcnc,GAAO,IAChBwc,KACAC,EAAY3K,QAAO8I,IAAU4B,EAAalR,SAASsP,WAI1DuB,EAAcnc,GAAOwc,EAI3B,OAAOL,GAKT,MAAMxiB,EACF,QAIJ,SAASuK,EAAY0E,EAAG7E,EAASuY,GAAoB,GACnD,OAAIpjB,EAAKmV,WAAWzF,GAAWA,EAC3B0T,IAAsB1T,EAAEtO,WAAW,KAC9BX,EAAW+Q,QAAQ9B,EAAG,CAAE+B,MAAO,CAAE5G,KAEnC7K,EAAKiE,KAAK4G,EAAS6E,GAM5B,SAASjD,EAAYxB,EAAQ5F,GAC3B,IAAK,MAAOyB,GAAOmb,QAASwB,MAAmBnhB,OAAOmJ,QAAQR,GACxC,MAAhB5F,EAAQyB,IAAgC,MAAhB2c,IAC1Bpe,EAAQyB,GAAO2c,GANrBpkB,EAAQ2L,YAAcA,EAWtB3L,EAAQoN,YAAcA,G,YC3PtB,IAAIvM,EAAOb,EAOXa,EAAKwD,OAAS,SAAqBggB,GAGjC,IAFA,IAAI7I,EAAM,EACN8I,EAAI,EACCpU,EAAI,EAAGoD,EAAI+Q,EAAOhgB,OAAQ6L,EAAIoD,IAAKpD,GAC1CoU,EAAID,EAAO1G,WAAWzN,IACd,IACNsL,GAAO,EACA8I,EAAI,KACX9I,GAAO,EACiB,QAAZ,MAAJ8I,IAAkE,QAAZ,MAA3BD,EAAO1G,WAAWzN,EAAI,OACvDA,EACFsL,GAAO,GAEPA,GAAO,EAEX,OAAOA,GAUT3a,EAAKub,KAAO,SAAmBJ,EAAQpB,EAAOC,GAE5C,GADUA,EAAMD,EACN,EACR,MAAO,GAKT,IAJA,IAGI2J,EAHA9P,EAAQ,KACRsH,EAAQ,GACR7L,EAAI,EAED0K,EAAQC,IACb0J,EAAIvI,EAAOpB,MACH,IACNmB,EAAM7L,KAAOqU,EACNA,EAAI,KAAOA,EAAI,IACtBxI,EAAM7L,MAAY,GAAJqU,IAAW,EAAsB,GAAlBvI,EAAOpB,KAC7B2J,EAAI,KAAOA,EAAI,KACtBA,IAAU,EAAJA,IAAU,IAAwB,GAAlBvI,EAAOpB,OAAkB,IAAwB,GAAlBoB,EAAOpB,OAAkB,EAAsB,GAAlBoB,EAAOpB,MAAiB,MAC1GmB,EAAM7L,KAAO,OAAUqU,GAAK,IAC5BxI,EAAM7L,KAAO,OAAc,KAAJqU,IAEvBxI,EAAM7L,MAAY,GAAJqU,IAAW,IAAwB,GAAlBvI,EAAOpB,OAAkB,EAAsB,GAAlBoB,EAAOpB,KACjE1K,EAAI,QACLuE,IAAUA,EAAQ,KAAK5M,KAAKG,OAAOwc,aAAaC,MAAMzc,OAAQ+T,IAC/D7L,EAAI,GAGR,OAAIuE,GACEvE,GACFuE,EAAM5M,KAAKG,OAAOwc,aAAaC,MAAMzc,OAAQ+T,EAAMrH,MAAM,EAAGxE,KACvDuE,EAAM7P,KAAK,KAEboD,OAAOwc,aAAaC,MAAMzc,OAAQ+T,EAAMrH,MAAM,EAAGxE,KAU1DrP,EAAKoK,MAAQ,SAAoBoZ,EAAQrI,EAAQG,GAI/C,IAHA,IACIuI,EACAC,EAFA/J,EAAQuB,EAGHjM,EAAI,EAAGA,EAAImU,EAAOhgB,SAAU6L,GACnCwU,EAAKL,EAAO1G,WAAWzN,IACd,IACP8L,EAAOG,KAAYuI,EACVA,EAAK,MACd1I,EAAOG,KAAYuI,GAAM,EAAI,IAC7B1I,EAAOG,KAAiB,GAALuI,EAAU,KACF,QAAZ,MAALA,IAA0E,QAAZ,OAAjCC,EAAKN,EAAO1G,WAAWzN,EAAI,MAClEwU,EAAK,QAAiB,KAALA,IAAgB,KAAY,KAALC,KACtCzU,EACF8L,EAAOG,KAAYuI,GAAM,GAAK,IAC9B1I,EAAOG,KAAYuI,GAAM,GAAK,GAAK,IACnC1I,EAAOG,KAAYuI,GAAM,EAAI,GAAK,IAClC1I,EAAOG,KAAiB,GAALuI,EAAU,MAE7B1I,EAAOG,KAAYuI,GAAM,GAAK,IAC9B1I,EAAOG,KAAYuI,GAAM,EAAI,GAAK,IAClC1I,EAAOG,KAAiB,GAALuI,EAAU,KAGjC,OAAOvI,EAASvB,I,YC5GlB,MACIhX,EAAS,SAAU5D,GACrB,aAEAiD,OAAOC,eAAelD,EAAS,aAAc,CAC3CqiB,OAAO,IAETriB,EAAQ4kB,YAAcA,EACtB5kB,EAAQ6E,gBAAkBA,EAC1B7E,EAAQ6kB,qBAAuBA,EAC/B7kB,EAAQ8kB,SAAWA,EACnB9kB,EAAQ4iB,aAAU,EAElB,MA+BMmC,EAAmC,oBAAnBC,eAChBC,EAAOC,SAKPC,EAAQ,IAAIC,YAAY,WAAY,CACxCC,OAAO,IAKT,SAASC,EAActJ,EAAQrW,GAC7B,IAAI6V,EAAM,IAAI+J,YAAYvJ,GAAQrW,GA3ChB,IA2CsC,KAAO,EAC/D,MAAM6f,EAAQ,IAAIC,YAAYzJ,EAAQrW,EAAK6V,GAC3C,GAAIA,GAbmB,IAaM,OAAOxT,OAAOwc,gBAAgBgB,GAE3D,IACE,OAAOL,EAAMO,OAAOF,GACpB,MACA,IAAI9jB,EAAM,GACNikB,EAAM,EAEV,KAAOnK,EAAMmK,EAnBQ,MAoBnBjkB,GAAOsG,OAAOwc,gBAAgBgB,EAAMI,SAASD,EAAKA,GApB/B,OAuBrB,OAAOjkB,EAAMsG,OAAOwc,gBAAgBgB,EAAMI,SAASD,KAMvD,SAASE,EAAeC,GACtB,MAAMC,EAAkB,GAExB,SAASC,EAAU7hB,EAAQwB,GACzB,OAAKxB,EACEmhB,EAAcnhB,EAAO6X,OAAQrW,GADhB,gBAKtB,MAAMgb,EAAMmF,EAAQnF,IAAMmF,EAAQnF,KAAO,GAgBzC,OAdAA,EAAIsF,MAAQtF,EAAIsF,OAAS,SAAeC,EAAKvf,EAAM4c,EAAM4C,GACvD,MAAMhiB,EAAS4hB,EAAgB5hB,QAAUwc,EAAIxc,OAE7C,MAAMqB,MAAM,UAAUwgB,EAAU7hB,EAAQ+hB,SAAWF,EAAU7hB,EAAQwC,MAAS4c,KAAQ4C,MAGxFxF,EAAIyF,MAAQzF,EAAIyF,OAAS,SAAeF,EAAKG,KAAM1T,GACjD,MAAMxO,EAAS4hB,EAAgB5hB,QAAUwc,EAAIxc,OAC7CH,QAAQC,IAAI,UAAU+hB,EAAU7hB,EAAQ+hB,KAAOG,EAAI,IAAM,KAAK1T,EAAK+B,MAAM,EAAG2R,GAAGzhB,KAAK,UAGtF+b,EAAI2F,KAAO3F,EAAI2F,MAAQ9hB,KAAKC,IAC5BqhB,EAAQxU,KAAOwU,EAAQxU,MAAQA,KAC/BwU,EAAQthB,KAAOshB,EAAQthB,MAAQA,KACxBuhB,EAGT,MAEMQ,EAAoB,WACxB,MAAM/gB,MAHkB,sDAQ1B,SAASghB,EAAgBT,EAAiBU,GACxC,MAAMzmB,EAAUymB,EAASzmB,QACnBmE,EAASnE,EAAQmE,OACjBuiB,EAAQ1mB,EAAQ0mB,MAEhBC,EAAQ3mB,EAAQ2mB,OAASJ,EAEzBzjB,EAAQ9C,EAAQ8C,OAASyjB,EAEzBxjB,EAAU/C,EAAQ+C,SAAWwjB,EAE7BvjB,EAAYhD,EAAQgD,WAAaujB,EAEjCK,EAAc5mB,EAAQ4mB,YACtBC,EAAeD,EAAc,SAAUE,GAC3C,OAAOA,EAAIF,IAAgB,IACzBL,EAgBJ,SAASQ,EAAaC,GACpB,MAAMjjB,EAVR,SAAiBijB,GACf,MAAMC,EAAM,IAAI1B,YAAYphB,EAAO6X,QAEnC,IAAKgL,KAAQ,IADCH,EAAaI,GACD,MAAMzhB,MAAM,eAAewhB,KACrD,OAAOC,GAAKL,EAAc,IAAM,GAAU,EAALI,GAMxBE,CAAQF,GACrB,KAAa,EAAPjjB,GAAiD,MAAMyB,MAAM,iBAAiBwhB,YAAajjB,KACjG,OAAOA,EAKT,SAASojB,EAAQH,GACf,MAAMC,EAAM,IAAI1B,YAAYphB,EAAO6X,QAEnC,IAAKgL,KAAQ,IADCH,EAAaI,GACD,MAAMzhB,MAAM,eAAewhB,KACrD,OAAOC,GAAKL,EAAc,IAAM,GAAU,EAALI,EAAS,GAKhD,SAASI,EAAcrjB,GACrB,OAAO,GAAKuN,KAAK+V,MAAMtjB,IA1IF,EA0I8B,IAqCrD,SAASujB,EAAQC,EAAWC,EAAQC,GAClC,MAAMzL,EAAS7X,EAAO6X,OAEtB,GAAIyL,EACF,OAAQF,GACN,KAAK,EACH,OAAO,IAAIG,aAAa1L,GAE1B,KAAK,EACH,OAAO,IAAI2L,aAAa3L,QAG5B,OAAQuL,GACN,KAAK,EACH,OAAO,IAAKC,EAASI,UAAY/L,YAAYG,GAE/C,KAAK,EACH,OAAO,IAAKwL,EAASK,WAAapC,aAAazJ,GAEjD,KAAK,EACH,OAAO,IAAKwL,EAASM,WAAavC,aAAavJ,GAEjD,KAAK,EACH,OAAO,IAAKwL,EAASO,cAAgB/C,gBAAgBhJ,GAI3D,MAAMxW,MAAM,sBAAsB+hB,KAgDpC,SAASS,EAAelB,GACtB,MAAMG,EAAM,IAAI1B,YAAYphB,EAAO6X,QAE7BjY,EAAOgjB,EADFE,EAAIH,GAxQD,IAwQqB,IAE7BmB,EAAQb,EAAcrjB,GAC5B,IAAImkB,EAlQY,EAkQNnkB,EAAqB+iB,EAAMG,EAAIH,EAjPJ,IAiP+C,GACpF,MAAMziB,EApQI,EAoQKN,EAAekjB,EAAIH,EA/OV,KA+OwC,GAAKG,EAAIiB,GA3QzD,IA2Q+E,KAAOD,EACtG,OAAOX,EAAQW,EA/PA,KA+POlkB,EA9PR,KA8P2BA,GAAkB6hB,SAASsC,KAASD,EAAOC,EAAM7jB,GA6B5F,SAAS8jB,EAAcC,EAAMb,EAAW5hB,GACtC,OAAO,IAAIyiB,EAAKC,EAAkBD,EAAMb,EAAW5hB,IAKrD,SAAS0iB,EAAkBD,EAAMb,EAAW5hB,GAC1C,MAAMqW,EAAS7X,EAAO6X,OAChBiL,EAAM,IAAI1B,YAAYvJ,GACtBsM,EAASrB,EAAIthB,EAzRkB,IAyRyB,GAC9D,OAAO,IAAIyiB,EAAKpM,EAAQsM,EAAQrB,EAAIqB,GAnTpB,IAmT6C,KAAOf,GAKtE,SAASgB,EAA0BC,EAAM1hB,EAAMmhB,GAC7ClC,EAAgB,QAAQjf,KAAUqhB,EAAcM,KAAK,KAAMD,EAAMP,GACjElC,EAAgB,QAAQjf,SAAcuhB,EAAkBI,KAAK,KAAMD,EAAMP,GAkC3E,OAxOAlC,EAAgBY,MAAQA,EACxBZ,EAAgBjjB,MAAQA,EACxBijB,EAAgBhjB,QAAUA,EAC1BgjB,EAAgB/iB,UAAYA,EAqD5B+iB,EAAgBnjB,YAbhB,SAAqBlB,GACnB,GAAW,MAAPA,EAAa,OAAO,EACxB,MAAM2C,EAAS3C,EAAI2C,OAEbsB,EAAMghB,EAAMtiB,GAAU,EAhKd,GAkKRqkB,EAAM,IAAIjD,YAAYthB,EAAO6X,QAEnC,IAAK,IAAI9L,EAAI,EAAGG,EAAI1K,IAAQ,EAAGuK,EAAI7L,IAAU6L,EAAGwY,EAAIrY,EAAIH,GAAKxO,EAAIic,WAAWzN,GAE5E,OAAOvK,GAcTogB,EAAgBljB,YARhB,SAAqB8C,GACnB,IAAKA,EAAK,OAAO,KACjB,MAAMqW,EAAS7X,EAAO6X,OAEtB,GAhLc,IA+KH,IAAIuJ,YAAYvJ,GAAQrW,GAnLrB,IAmLyC,GACjC,MAAMH,MAAM,iBAAiBG,KACnD,OAAO2f,EAActJ,EAAQrW,IA8E/BogB,EAAgB4C,WAxChB,SAAoB3B,EAAIzZ,GACtB,MAAMxJ,EAAOgjB,EAAaC,GACpBiB,EAAQb,EAAcrjB,GACtBM,EAASkJ,EAAOlJ,OAEhB6jB,EAAMvB,EAAMtiB,GAAU4jB,EAvNZ,EAuNmBlkB,EAAqBijB,EA7NrC,GA+NnB,IAAI4B,EAEJ,GA3NgB,EA2NZ7kB,EACF6kB,EAASV,MACJ,CACLplB,EAAMolB,GAEN,MAAMpB,EAAMH,EAjOJ,EAiOU5iB,EA3ML,GAFU,GA6M6CijB,GAEpEjkB,EAAQmlB,GAER,MAAMjB,EAAM,IAAI1B,YAAYphB,EAAO6X,QACnCiL,EAAIH,EArN4B,IAqNY,GAAKoB,EACjDjB,EAAIH,EArN+B,IAqNY,GAAKoB,EACpDjB,EAAIH,EArNgC,IAqNY,GAAKziB,GAAU4jB,EAxOvD,EAyOJlkB,IAAckjB,EAAIH,EApNA,KAoN8B,GAAKziB,GACzDukB,EAAS9B,EAGX,MAAM+B,EAAOvB,EAAQW,EAvON,KAuOalkB,EAtOd,KAsOiCA,GAE/C,GAtOgB,MAsOZA,EACF,IAAK,IAAImM,EAAI,EAAGA,EAAI7L,IAAU6L,EAAG,CAC/B,MAAMmS,EAAQ9U,EAAO2C,GACrB2Y,GAAMX,IAAQD,GAAS/X,GAAKmS,OAG9BwG,EAAK3T,IAAI3H,EAAQ2a,IAAQD,GAG3B,OAAOW,GAgBT7C,EAAgBiC,eAAiBA,EAcjCjC,EAAgB+C,WAXhB,SAAoBhC,GAClB,MAAMiC,EAAQf,EAAelB,GAEvBtL,EAAMuN,EAAM1kB,OACZiI,EAAM,IAAIxE,MAAM0T,GAEtB,IAAK,IAAItL,EAAI,EAAGA,EAAIsL,EAAKtL,IAAK5D,EAAI4D,GAAK6Y,EAAM7Y,GAE7C,OAAO5D,GAYTyZ,EAAgBiD,iBANhB,SAA0BrjB,GACxB,MAAMqW,EAAS7X,EAAO6X,OAChB3X,EAAS,IAAIkhB,YAAYvJ,GAAQrW,GAlSvB,IAkS6C,GAC7D,OAAOqW,EAAOtH,MAAM/O,EAAKA,EAAMtB,IA0BjC,CAACujB,UAAW/L,WAAYoN,kBAAmBpB,WAAYpC,YAAaqC,WAAYvC,YAAamC,aAAcC,cAAcjhB,SAAQ8hB,IAC/HD,EAA0BC,EAAMA,EAAK1hB,KAAM,GAAKwK,KAAK+V,MAAMmB,EAAKU,uBAG9DnE,GACF,CAACC,eAAgB+C,eAAerhB,SAAQ8hB,IACtCD,EAA0BC,EAAMA,EAAK1hB,KAAK4N,MAAM,GAAI,MAoBxDqR,EAAgBoD,aAdhB,SAAsBxjB,EAAKyjB,GACzB,MAAMnC,EAAM,IAAI1B,YAAYphB,EAAO6X,QACnC,IAAIgL,EAAKC,EAAIthB,GA5UC,IA4UmB,GAEjC,GAAIqhB,GAAMH,EAAaI,GACrB,EAAG,CACD,GAAID,GAAMoC,EAAQ,OAAO,EACzBpC,EAAKG,EAAQH,SACNA,GAGX,OAAO,GAKTjB,EAAgB5hB,OAAS4hB,EAAgB5hB,QAAUA,EACnD4hB,EAAgBW,MAAQX,EAAgBW,OAASA,EAE1C5B,EAAS9kB,EAAS+lB,GAG3B,SAASsD,EAAWC,GAClB,MAA2B,oBAAbC,UAA4BD,aAAeC,SAG3D,SAASC,EAASF,GAChB,OAAOA,aAAeG,YAAYlT,OAKpCmT,eAAe9E,EAAYpK,EAAQsL,EAAU,IAC3C,GAAIuD,EAAW7O,QAAeA,GAAS,OAAOqK,EAAqBrK,EAAQsL,GAC3E,MAAM7lB,EAASupB,EAAShP,GAAUA,QAAeiP,YAAYpT,QAAQmE,GAC/DmP,EAAW9D,EAAeC,GAC1BW,QAAiBgD,YAAY7E,YAAY3kB,EAAQ6lB,GAEvD,MAAO,CACL7lB,SACAwmB,WACAzmB,QAJcwmB,EAAgBmD,EAAUlD,IAU5C,SAAS5hB,EAAgB2V,EAAQsL,EAAU,IACzC,MAAM7lB,EAASupB,EAAShP,GAAUA,EAAS,IAAIiP,YAAYlT,OAAOiE,GAC5DmP,EAAW9D,EAAeC,GAC1BW,EAAW,IAAIgD,YAAYG,SAAS3pB,EAAQ6lB,GAElD,MAAO,CACL7lB,SACAwmB,WACAzmB,QAJcwmB,EAAgBmD,EAAUlD,IAU5CiD,eAAe7E,EAAqBrK,EAAQsL,EAAU,IACpD,IAAK2D,YAAY5E,qBACf,OAAOD,EAAYyE,EAAW7O,QAAeA,GAAUA,EAAOqP,cAAgBrP,EAAQsL,GAGxF,MAAM6D,EAAW9D,EAAeC,GAC1B8C,QAAea,YAAY5E,qBAAqBrK,EAAQsL,GACxD9lB,EAAUwmB,EAAgBmD,EAAUf,EAAOnC,UACjD,MAAO,IAAKmC,EACV5oB,WAMJ,SAAS8kB,EAAS9kB,EAAS+lB,EAAkB,IAC3C,MAAM+D,EAAqB9pB,EAA2B,kBAAIqE,IACxDrE,EAA2B,kBAAEqiB,MAAQhe,GACnCrE,EAA8B,sBAAKA,EAAmB,WAAK,SAI/D,IAAK,IAAI+pB,KAAgB/pB,EAAS,CAChC,IAAKiD,OAAOmF,UAAUC,eAAeC,KAAKtI,EAAS+pB,GAAe,SAClE,MAAMC,EAAOhqB,EAAQ+pB,GACrB,IAAItV,EAAQsV,EAAahhB,MAAM,KAC3BkhB,EAAOlE,EAEX,KAAOtR,EAAMpQ,OAAS,GAAG,CACvB,IAAI+L,EAAOqE,EAAMyV,QACZjnB,OAAOmF,UAAUC,eAAeC,KAAK2hB,EAAM7Z,KAAO6Z,EAAK7Z,GAAQ,IACpE6Z,EAAOA,EAAK7Z,GAGd,IAAItJ,EAAO2N,EAAM,GACb0V,EAAOrjB,EAAKwJ,QAAQ,KAExB,GAAI6Z,GAAQ,EAAG,CACb,MAAMC,EAAYtjB,EAAK0J,UAAU,EAAG2Z,GAC9BE,EAAYJ,EAAKG,GAEvB,QAAyB,IAAdC,IAA8BA,EAAUjiB,UAAW,CAC5D,MAAMogB,EAAO,YAAa7V,GACxB,OAAO6V,EAAK9lB,KAAK8lB,EAAKpgB,UAAUkiB,YAAY,KAAM3X,KAGpD6V,EAAKpgB,UAAY,CACf,UACE,OAAO7H,KAAK0kB,KAKhBuD,EAAK9lB,KAAO,SAAU6nB,GACpB,OAAOtnB,OAAOmE,OAAOohB,EAAKpgB,UAAW,CACnC,CAAC6c,GAAO,CACN5C,MAAOkI,EACPC,UAAU,MAKZH,GAAWpnB,OAAOwnB,oBAAoBJ,GAAW3jB,SAAQI,GAAQ7D,OAAOC,eAAeslB,EAAM1hB,EAAM7D,OAAOynB,yBAAyBL,EAAWvjB,MAClJmjB,EAAKG,GAAa5B,EAMpB,GAHA1hB,EAAOA,EAAK0J,UAAU2Z,EAAO,GAC7BF,EAAOA,EAAKG,GAAWhiB,UAEnB,cAAcmD,KAAKzE,IACrB,IAAK7D,OAAOmF,UAAUC,eAAeC,KAAK2hB,EAAMnjB,EAAOA,EAAK0J,UAAU,IAAK,CACzE,IAAIma,EAAS3qB,EAAQ+pB,EAAapoB,QAAQ,OAAQ,SAC9CipB,EAAS5qB,EAAQ+pB,EAAapoB,QAAQ,OAAQ,SAClDsB,OAAOC,eAAe+mB,EAAMnjB,EAAM,CAChC,MACE,OAAO6jB,EAAOpqB,KAAK0kB,KAGrB,IAAI5C,GACFuI,EAAOrqB,KAAK0kB,GAAO5C,IAGrBwI,YAAY,SAIH,gBAAT/jB,GACDmjB,EAAKnjB,GAAQ,IAAI6L,KAChBmX,EAAmBnX,EAAKtO,QACjB2lB,KAAQrX,KACd2D,SAAW0T,GAGbC,EAAKnjB,GAAQ,YAAa6L,GAGzB,OADAmX,EAAmBnX,EAAKtO,QACjB2lB,EAAKzpB,KAAK0kB,MAAUtS,KAC1B2D,SAAW0T,MAId,cAAcze,KAAKzE,GAChB7D,OAAOmF,UAAUC,eAAeC,KAAK2hB,EAAMnjB,EAAOA,EAAK0J,UAAU,KACpEvN,OAAOC,eAAe+mB,EAAMnjB,EAAM,CAChC3D,IAAKnD,EAAQ+pB,EAAapoB,QAAQ,OAAQ,SAC1CuT,IAAKlV,EAAQ+pB,EAAapoB,QAAQ,OAAQ,SAC1CkpB,YAAY,IAGS,mBAATb,GAAuBA,IAASF,GAC/CG,EAAKnjB,GAAQ,IAAI6L,KAChBmX,EAAmBnX,EAAKtO,QACjB2lB,KAAQrX,KACd2D,SAAW0T,EAEdC,EAAKnjB,GAAQkjB,EAKnB,OAAOjE,EAGT,IAAI+E,EAAW,CACblG,cACA/f,kBACAggB,uBACAC,YAGF,OADA9kB,EAAQ4iB,QAAUkI,EACX9qB,EArhBI,CAshBV,SACqF,KAA7B,EAAF,WAAe,OAAO4D,GAAS,QAAjC,OAAiC,c,YCxhBxF,MACIC,EAAS,SAAU7D,GACrB,aAEAiD,OAAOC,eAAelD,EAAS,aAAc,CAC3CqiB,OAAO,IAETriB,EAAQ4iB,QAAU5iB,EAAQ+qB,OAAS/qB,EAAQgrB,eAAiBhrB,EAAQirB,gBAAkBjrB,EAAQkrB,oBAAiB,EAE/G,MAOMC,EAAW5F,YAEjBvlB,EAAQkrB,eAJS,EAMjBlrB,EAAQirB,gBADgB,GAKxB,SAASG,EAAOC,GACd,IAAKA,EAAG,MAAM7lB,MAAM,oBACpB,OAAO6lB,EAKT,SAASC,EAAe7lB,EAAO8lB,GAC7B,OAAO9lB,EAAMsD,MAAM,SAAS2L,MAAM,EAAI6W,GAVxCvrB,EAAQgrB,eADeE,GAQvB1lB,MAAMgmB,gBAAkB,GAMxB,MAAMvQ,EAAgC,oBAAhB+E,aAA+BA,YAAYvb,IAAMub,YAAYvb,IAAyB,oBAAZ7D,SAA2BA,QAAQqa,OAAS,KAC1I,IAAIsJ,EAAI3jB,QAAQqa,SAChB,OAAc,IAAPsJ,EAAE,GAAWA,EAAE,GAAK,KACzB/f,KAAKC,IACHgnB,EAAiB,CAAC,GAAI,OAAQ,WAAY,iBAC1CC,EAAkB,CAAC,cAAe,cAAe,OAAQ,WAE/D,MAAMX,EACJ,YAAY/kB,GACVzF,KAAKyF,QAAUA,GAAW,GAE1BzF,KAAKorB,QAAUprB,KAAKyF,QAAQ2lB,SAAW,aAIvCprB,KAAKqrB,OAASrrB,KAAKyF,QAAQ4lB,QAAU,aAIrCrrB,KAAKsrB,WAAatrB,KAAKyF,QAAQ8lB,WAAa,aAI5CvrB,KAAK4D,OAAS,KACd5D,KAAKwrB,OAAS,KACdxrB,KAAKyrB,YAAc,WACnBzrB,KAAK0rB,OAAS,IAAIxY,IAClBlT,KAAK2rB,WAAa,IAAIzY,IACtBlT,KAAK4rB,YAAc,IAAI1Y,IACvBlT,KAAK6rB,eAAiB,EACtB7rB,KAAK6D,UAAY,GACjB7D,KAAK8rB,WAAa,EAClB9rB,KAAK+rB,YAAc,EACnB/rB,KAAKgsB,UAAY,EACjBhsB,KAAKisB,UAAY,EACjBjsB,KAAKksB,SAAW,WAGlB,QAAQ3G,GAeN,OAdKA,IAASA,EAAU,IACxBA,EAAQjiB,OAASZ,OAAOuP,OAAOsT,EAAQjiB,QAAU,GAAI,CACnD6oB,OAAQnsB,KAAKmsB,OAAOjE,KAAKloB,MACzBosB,QAASpsB,KAAKosB,QAAQlE,KAAKloB,MAC3BqsB,SAAUrsB,KAAKqsB,SAASnE,KAAKloB,MAC7BssB,OAAQtsB,KAAKssB,OAAOpE,KAAKloB,MACzBusB,QAASvsB,KAAKusB,QAAQrE,KAAKloB,MAC3BwsB,OAAQxsB,KAAKwsB,OAAOtE,KAAKloB,MACzBysB,YAAazsB,KAAKysB,YAAYvE,KAAKloB,MACnC0sB,QAAS1sB,KAAK0sB,QAAQxE,KAAKloB,MAC3BurB,UAAWvrB,KAAKurB,UAAUrD,KAAKloB,MAC/B2sB,QAAS3sB,KAAK2sB,QAAQzE,KAAKloB,MAC3B4sB,OAAQ5sB,KAAK4sB,OAAO1E,KAAKloB,QAEpBulB,EAKT,aACE,GAAKvlB,KAAK4D,OAKH,CACL,IAAIipB,EAAO7sB,KAAK4D,OAAO6X,OAAOqR,WAAa9sB,KAAKwrB,OAAO/P,OAAOqR,WAC1DD,EAAO,GAAG7sB,KAAKwrB,OAAOuB,KAAKF,IAAS,SANxC7sB,KAAK4D,OAASinB,EAAO7qB,KAAKyF,QAAQ9B,aAClC3D,KAAKwrB,OAAS,IAAItC,YAAY8D,OAAO,CACnCC,SAAUjtB,KAAK4D,OAAO6X,OAAOqR,WAtFnBI,OAsF4C,SAxFvC,KAkGrB,WAAW1pB,EAAM2pB,EAAU,GACzBtC,EAAO7qB,KAAKwrB,QAAUxrB,KAAKwrB,OAAOsB,YAAc9sB,KAAK4D,OAAOkpB,YAC5DjC,EAAiC,IA9FpBuC,EA8FL5pB,EAAK6pB,OAET7pB,EAAK4B,IAAMpF,KAAKyrB,cAClBzrB,KAAKyrB,YAAcjoB,EAAK4B,KAG1B,IAAI6V,EAAMzX,EAAK6pB,OAtGG,EAuGd/E,EAAO,IAAIsC,EAAS5qB,KAAKwrB,OAAO/P,OAAQjY,EAAK4B,IAAK6V,GAClDqS,GAAU,EACVjT,EAAQ8S,IAzGM,EA2GlB,IAAK,IAAIxd,EAAI,EAAGA,EAAI0K,IAAS1K,EACvB2Y,EAAK3Y,IAAMnM,EAAK4B,KAAQkoB,IAC1BttB,KAAKorB,QAAQnmB,MAAM,2BAA6BqjB,EAAK3Y,GAAK,OAASnM,EAAK4B,KAAM5B,GAC9E8pB,GAAU,GAIdA,GAAU,EAEV,IAAK,IAAI3d,EAAI0K,EAAO1K,EAAIsL,IAAOtL,EACd,GAAX2Y,EAAK3Y,IAAY2d,IACnBttB,KAAKorB,QAAQnmB,MAAM,iCAAmCqjB,EAAK3Y,GAAK,SAAUnM,GAC1E8pB,GAAU,GAGZhF,EAAK3Y,GAAKnM,EAAK4B,IAMnB,aAAa5B,EAAM2pB,EAAU3pB,EAAK6pB,MAChCxC,EAAO7qB,KAAKwrB,QAAUxrB,KAAKwrB,OAAOsB,YAAc9sB,KAAK4D,OAAOkpB,YAC5D,IAAI7R,EAAMkS,IAlIQ,EAmId7E,EAAO,IAAIsC,EAAS5qB,KAAKwrB,OAAO/P,OAAQjY,EAAK4B,IAAK6V,GAClDqS,GAAU,EACVjT,EAAQ,EAER8S,GAAW3pB,EAAK6pB,OAClBxC,EAAOsC,EAAU3pB,EAAK6pB,MACtBhT,EAAQ7W,EAAK6pB,OAzIG,GA4IlB,IAAK,IAAI1d,EAAI,EAAGA,EAAIsL,IAAOtL,EACrB2Y,EAAK3Y,IAAMnM,EAAK4B,KAAQkoB,IAC1BttB,KAAKorB,QAAQnmB,MAAM,2BAA6BqjB,EAAK3Y,GAAK,OAASnM,EAAK4B,KAAM5B,GAC9E8pB,GAAU,GAGR3d,GAAK0K,IAAOiO,EAAK3Y,GAAK,GAM9B,aAAavK,EAAKioB,EAAME,EAAQC,GAE9B,IADAxtB,KAAKytB,eACDroB,EAAMpF,KAAKyrB,gBAEF,GADD,IAAIzG,YAAYhlB,KAAKwrB,OAAO/P,QAAc,EAANrW,EAAiB,GAAG,KAG/DooB,GAAM,CACT,IAAItoB,EAAQ6lB,GAAe,IAAI9lB,OAAQC,MAAO,GAC9ClF,KAAKorB,QAAQ,IAAInmB,MAAM,QAAUsoB,EAAS,OAAS,SAAW,EAAIF,EAAO,eAAiBjoB,EAAM,KAAOF,EAAMb,KAAK,SAMtH,aAAae,GACX,MAAOsoB,EAAQC,EAAQC,EAASC,EAAMC,GAAU,IAAI9I,YAAYhlB,KAAK4D,OAAO6X,OAAQrW,EAAK,GACnFioB,GAAgB,EAATK,EACb,MAAO,CACLtoB,MACAioB,KA1KW,EA0KYA,EAEvBK,OAAQ,CACNK,KAAM7C,EAAwB,EAATwC,GACrBL,KAAMA,GAGRM,OAAQ,CACN3hB,MAAOmf,EAAyB,EAATwC,GACvB5W,MAAe,EAAT4W,EACNK,KAAMJ,GAERC,OACAC,UAMJ,aACE,OAAO/L,QAAQ/hB,KAAK8rB,YAAc9rB,KAAK+rB,aAAe/rB,KAAKgsB,WAAahsB,KAAKisB,WAK/E,QACE,GAAIjsB,KAAKqrB,OACP,IAAK,IAAKjmB,EAAK5B,KAASxD,KAAK0rB,OAC3B1rB,KAAKqrB,OAAO,QAAUjmB,EAAM,KAAO5B,EAAKyqB,WAAW5pB,KAAK,OAI5D,OAAOrE,KAAK0rB,OAAO2B,KAIrB,OAAOnB,GACLlsB,KAAKksB,SAAWA,EAChBlsB,KAAK6rB,eAAiB,EACtB7rB,KAAK6D,UAAUC,OAAS,EACxB9D,KAAKqrB,OAAO,iBAAmBa,GAGjC,QAAQ9mB,GACNpF,KAAKytB,eACHztB,KAAK8rB,WACP,IAAItoB,EAAOxD,KAAKkuB,aAAa9oB,GAE7B,GAAIpF,KAAK0rB,OAAO9e,IAAIxH,GAClBpF,KAAKorB,QAAQnmB,MAAM,oBAAsBG,GAAM5B,OAC1C,CACLxD,KAAKqrB,OAAO,SAAWjmB,EAAM,MAAQA,EAAM5B,EAAK6pB,OAChDrtB,KAAKmuB,WAAW3qB,GAChB,IAAIyqB,EAAalD,GAAe,IAAI9lB,OAAQC,MAAO,GAEnDlF,KAAK0rB,OAAO/W,IAAIvP,EAAK1C,OAAOuP,OAAOzO,EAAM,CACvCyqB,iBAKN,SAAS7oB,EAAK+nB,GACZntB,KAAKytB,eACHztB,KAAK+rB,YACP,MAAMvoB,EAAOxD,KAAKkuB,aAAa9oB,GAE/B,GAAKpF,KAAK0rB,OAAO9e,IAAIxH,GAEd,CACL,MAAMgpB,EAAapuB,KAAK0rB,OAAO9oB,IAAIwC,GAE/BgpB,EAAWf,MAAQF,GACrBntB,KAAKorB,QAAQnmB,MAAM,8BAA8BG,MAAQgpB,EAAWf,WAAWF,MAAa3pB,GAG9F,MAAM6qB,EAAU7qB,EAAK6pB,KACrBrtB,KAAKqrB,OAAO,UAAYjmB,EAAM,MAAQA,EAAMipB,GAAW,KAAOlB,EAAU,KAAOkB,EAAU,KACzFruB,KAAK0rB,OAAO/W,IAAIvP,EAAK1C,OAAOuP,OAAOzO,EAAM,CACvCyqB,WAAYG,EAAWH,cAGrBI,EAAUlB,EACZntB,KAAKmuB,WAAW3qB,EAAM2pB,GACbkB,EAAUlB,GACnBntB,KAAKsuB,aAAa9qB,EAAM2pB,QAjB1BntB,KAAKorB,QAAQnmB,MAAM,oBAAsBG,GAAM5B,GAsBnD,OAAO+qB,EAAQC,GACbxuB,KAAKytB,eACHztB,KAAKgsB,UACP,IAAIyC,EAAUzuB,KAAKkuB,aAAaK,GAC5BG,EAAU1uB,KAAKkuB,aAAaM,GAEhC,GAAKxuB,KAAK0rB,OAAO9e,IAAI2hB,GAGnB,GAAKvuB,KAAK0rB,OAAO9e,IAAI4hB,GAEd,CACL,MAAMJ,EAAapuB,KAAK0rB,OAAO9oB,IAAI2rB,GAC7BpB,EAAUsB,EAAQpB,KAClBgB,EAAUK,EAAQrB,KAEpBe,EAAWf,MAAQF,GACrBntB,KAAKorB,QAAQnmB,MAAM,4BAA4BspB,MAAWH,EAAWf,WAAWF,MAAasB,GAG/FzuB,KAAKqrB,OAAO,QAAUkD,EAAS,MAAQA,EAASpB,GAAW,OAASqB,EAAS,MAAQA,EAASH,SAV9FruB,KAAKorB,QAAQnmB,MAAM,wBAA0BupB,GAASE,QAHxD1uB,KAAKorB,QAAQnmB,MAAM,wBAA0BspB,GAASE,GAkB1D,QAAQrpB,GAEN,GAAIA,EAAMpF,KAAKksB,WAAalsB,KAAK0rB,OAAO9e,IAAIxH,GAAM,CAChD,IAAI7B,EAAM0B,MAAM,mBAAqBG,GACjC5B,EAAOxD,KAAK4rB,YAAYhpB,IAAIwC,GAQhC,OANI5B,IACFD,EAAI2B,OAAS,sBAAwB1B,EAAKyqB,WAAW5pB,KAAK,MAC1Dd,EAAI2B,OAAS,kBAAoB1B,EAAKmrB,UAAUtqB,KAAK,OAGvDrE,KAAKorB,QAAQ7nB,EAAK,OACX,EAGT,OAAO,EAGT,OAAO6B,GACLpF,KAAKytB,eACHztB,KAAKisB,UACP,IAAIzoB,EAAOxD,KAAKkuB,aAAa9oB,GAE7B,GAAKpF,KAAK0rB,OAAO9e,IAAIxH,GAEd,CACL,MAAMqpB,EAAUzuB,KAAK0rB,OAAO9oB,IAAIwC,GAE5B5B,EAAK6pB,MAAQoB,EAAQpB,MACvBrtB,KAAKorB,QAAQnmB,MAAM,4BAA4BG,MAAQqpB,EAAQpB,WAAW7pB,EAAK6pB,SAAU7pB,GAG3FxD,KAAKqrB,OAAO,QAAUjmB,EAAM,MAAQA,EAAM5B,EAAK6pB,OAC/CrtB,KAAKsuB,aAAa9qB,GAClB,MAAMorB,EAAY5uB,KAAK0rB,OAAO9oB,IAAIwC,GAClCpF,KAAK0rB,OAAOmD,OAAOzpB,GACnB,MAAM6oB,EAAaW,EAAUX,WACvBU,EAAY5D,GAAe,IAAI9lB,OAAQC,MAAO,GAGpDlF,KAAK4rB,YAAYjX,IAAIvP,EAAK,CACxB6oB,aACAU,mBAlBF3uB,KAAKorB,QAAQnmB,MAAM,kBAAoBG,GAAM5B,GAuBjD,UAAUsrB,GACR9uB,KAAKqrB,OAAO,cAAcyD,KAC1B9uB,KAAK+uB,KAAKD,GACV9uB,KAAKsrB,aAIP,KAAKwD,EAAOE,EAAQ,GACbhvB,KAAK6rB,iBAAgB7rB,KAAK6rB,eAAiB5nB,KAAKC,OACrDlE,KAAK6D,UAAUyD,KAAK,CAACrD,KAAKC,MAAQlE,KAAK6rB,eAAgBiD,EAAOE,IAGhE,YAAYF,GACV9uB,KAAKivB,eAAiBvU,IACtB1a,KAAK+uB,KAAKD,GAGZ,QAAQA,GACN,IAAIE,EAAQtU,IAAW1a,KAAKivB,eACxBD,GAAS,GAAGvrB,QAAQC,IAAI,mBAAqBsrB,EAAMlU,QAAQ,GAAK,MACpE9a,KAAK+uB,KAAKD,EAAOE,GAInB,QAAQ5pB,EAAKwW,EAAQsT,EAAO1B,GAE1B,OADAxtB,KAAKmvB,aAAa/pB,EAAMwW,EAAQsT,GAAO,EAAO1B,GACvCpoB,EAGT,OAAOA,EAAKwW,EAAQsT,EAAO1B,GAEzB,OADAxtB,KAAKmvB,aAAa/pB,EAAMwW,EAAQsT,GAAO,EAAM1B,GACtCpoB,GAKX3F,EAAQ+qB,OAASA,EACjB,IAAID,EAAW,CACbC,UAGF,OADA/qB,EAAQ4iB,QAAUkI,EACX9qB,EAjYI,CAkYV,SACqF,KAA7B,EAAF,WAAe,OAAO6D,GAAS,QAAjC,OAAiC,c,qBCpYxF,QAA+C,IAArCpD,EAAkD,CAAE,IAAIN,EAAI,IAAIqF,MAAM,uCAAqE,MAA7BrF,EAAEkL,KAAO,mBAA0BlL,EAE3JF,EAAOD,QAAUS,G,qBCFjBR,EAAOD,QAAUQ,ICCbmvB,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBxb,IAAjByb,EACH,OAAOA,EAAa9vB,QAGrB,IAAIC,EAAS0vB,EAAyBE,GAAY,CAGjD7vB,QAAS,IAOV,OAHA+vB,EAAoBF,GAAU5vB,EAAQA,EAAOD,QAAS4vB,GAG/C3vB,EAAOD,Q,OCrBf4vB,EAAoBzuB,EAAI,WACvB,GAA0B,iBAAf6uB,WAAyB,OAAOA,WAC3C,IACC,OAAOzvB,MAAQ,IAAI0vB,SAAS,cAAb,GACd,MAAO9vB,GACR,GAAsB,iBAAX+vB,OAAqB,OAAOA,QALjB,GCGEN,EAAoB,M", "file": "asc.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"binaryen\"), (function webpackLoadOptionalExternalModule() { try { return require(\"assemblyscript\"); } catch(e) {} }()));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"binaryen\", \"assemblyscript\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"asc\"] = factory(require(\"binaryen\"), (function webpackLoadOptionalExternalModule() { try { return require(\"assemblyscript\"); } catch(e) {} }()));\n\telse\n\t\troot[\"asc\"] = factory(root[\"binaryen\"], root[\"assemblyscript\"]);\n})(typeof self !== 'undefined' ? self : this, function(__WEBPACK_EXTERNAL_MODULE__911__, __WEBPACK_EXTERNAL_MODULE__525__) {\nreturn ", "/**\n * @license\n * Copyright 2020 <PERSON> / The AssemblyScript Authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * @fileoverview Compiler frontend for node.js\n *\n * Uses the low-level API exported from src/index.ts so it works with the compiler compiled to\n * JavaScript as well as the compiler compiled to WebAssembly (eventually). Runs the sources\n * directly through ts-node if distribution files are not present.\n *\n * Can also be packaged as a bundle suitable for in-browser use with the standard library injected\n * in the build step. See dist/asc.js for the bundle and webpack.config.js for building details.\n */\n\n/* global BUNDLE_VERSION, BUNDLE_LIBRARY, BUNDLE_DEFINITIONS */\n\nconst fs = require(\"fs\");\nconst path = require(\"path\");\nconst process = require(\"process\"); // ensure shim\nconst utf8 = require(\"./util/utf8\");\nconst colorsUtil = require(\"./util/colors\");\nconst optionsUtil = require(\"./util/options\");\nconst mkdirp = require(\"./util/mkdirp\");\nconst find = require(\"./util/find\");\nconst binaryen = global.binaryen || (global.binaryen = require(\"binaryen\"));\n\nconst dynrequire = typeof __webpack_require__ === \"function\"\n  ? __non_webpack_require__\n  : require;\n\nconst WIN = process.platform === \"win32\";\nconst EOL = WIN ? \"\\r\\n\" : \"\\n\";\nconst SEP = WIN ? \"\\\\\"   : \"/\";\n\nfunction toUpperSnakeCase(str) {\n  return str.replace(/-/g, \"_\").toUpperCase();\n}\n\n// Sets up an extension with its definition counterpart and relevant regexes.\nfunction setupExtension(ext) {\n  if (!ext.startsWith(\".\")) ext = `.${ext}`;\n  return {\n    ext,\n    ext_d: `.d${ext}`,\n    re: new RegExp(\"\\\\\" + ext + \"$\"),\n    re_d: new RegExp(\"\\\\.d\\\\\" + ext + \"$\"),\n    re_except_d: new RegExp(\"^(?!.*\\\\.d\\\\\" + ext + \"$).*\\\\\" + ext + \"$\"),\n    re_index: new RegExp(\"(?:^|[\\\\\\\\\\\\/])index\\\\\" + ext + \"$\")\n  };\n}\n\nconst defaultExtension = setupExtension(\".ts\");\n\n// Proxy Binaryen's ready event\nObject.defineProperty(exports, \"ready\", {\n  get() { return binaryen.ready; }\n});\n\n// Emscripten adds an `uncaughtException` listener to Binaryen that results in an additional\n// useless code fragment on top of an actual error. suppress this:\nif (process.removeAllListeners) {\n  process.removeAllListeners(\"uncaughtException\");\n}\n\n// Use distribution files if present, otherwise run the sources directly.\nfunction loadAssemblyScriptJS() {\n  var exports;\n  try {\n    // note that this case will always trigger in recent node.js versions for typical installs\n    // see: https://nodejs.org/api/packages.html#packages_self_referencing_a_package_using_its_name\n    exports = require(\"assemblyscript\");\n  } catch (e) {\n    try { // `asc` on the command line (unnecessary in recent node)\n      exports = dynrequire(\"../dist/assemblyscript.js\");\n    } catch (e) {\n      try { // `asc` on the command line without dist files (unnecessary in recent node)\n        dynrequire(\"ts-node\").register({\n          project: path.join(__dirname, \"..\", \"src\", \"tsconfig.json\"),\n          skipIgnore: true,\n          compilerOptions: { target: \"ES2016\" }\n        });\n        dynrequire(\"../src/glue/js\");\n        exports = dynrequire(\"../src\");\n      } catch (e_ts) {\n        try { // `require(\"dist/asc.js\")` in explicit browser tests\n          exports = dynrequire(\"./assemblyscript\");\n        } catch (e) {\n          throw Error(`${e_ts.stack}\\n---\\n${e.stack}`);\n        }\n      }\n    }\n  }\n  return exports;\n}\n\n// Loads the specified bootstrapped Wasm binary of the compiler.\nfunction loadAssemblyScriptWasm(binaryPath) {\n  const loader = require(\"../lib/loader/umd/index\");\n  const rtrace = new (require(\"../lib/rtrace/umd/index\").Rtrace)({\n    onerror(err, info) {\n      console.log(err, info);\n    },\n    getMemory() {\n      return exports.memory;\n    },\n    oncollect() {\n      var gcProfile = rtrace.gcProfile;\n      if (gcProfile && gcProfile.length && fs.writeFileSync) {\n        let timestamp = Date.now();\n        fs.writeFileSync(\n          `rtrace-gc-profile-${timestamp}.json`,\n          JSON.stringify(gcProfile)\n        );\n        fs.writeFileSync(\n          `rtrace-gc-profile-${timestamp}.csv`,\n          `time,memory,pause\\n${gcProfile.join(\"\\n\")}`\n        );\n      }\n    }\n  });\n  var { exports } = loader.instantiateSync(fs.readFileSync(binaryPath), rtrace.install({ binaryen }));\n  if (exports._start) exports._start();\n  return exports;\n}\n\n/** Ensures that an object is a wrapper class instead of just a pointer. */\nfunction __wrap(ptrOrObj, wrapperClass) {\n  if (typeof ptrOrObj === \"number\") {\n    return ptrOrObj === 0 ? null : wrapperClass.wrap(ptrOrObj);\n  }\n  return ptrOrObj;\n}\n\nvar assemblyscript, __newString, __getString, __pin, __unpin, __collect;\n\nfunction loadAssemblyScript() {\n  const wasmArg = process.argv.findIndex(arg => arg == \"--wasm\");\n  if (~wasmArg) {\n    let binaryPath = process.argv[wasmArg + 1];\n    process.argv.splice(wasmArg, 2);\n    assemblyscript = loadAssemblyScriptWasm(binaryPath);\n    __newString = assemblyscript.__newString;\n    __getString = assemblyscript.__getString;\n    __pin = assemblyscript.__pin;\n    __unpin = assemblyscript.__unpin;\n    __collect = assemblyscript.__collect;\n  } else {\n    assemblyscript = loadAssemblyScriptJS();\n    __newString = str => str;\n    __getString = ptr => ptr;\n    __pin = ptr => ptr;\n    __unpin = _ => undefined;\n    __collect = _ => undefined;\n  }\n}\nloadAssemblyScript();\n\n/** Whether this is a webpack bundle or not. */\nexports.isBundle = typeof BUNDLE_VERSION === \"string\";\n\n/** AssemblyScript version. */\nexports.version = exports.isBundle ? BUNDLE_VERSION : dynrequire(\"../package.json\").version;\n\n/** Available CLI options. */\nexports.options = require(\"./asc.json\");\n\n/** Prefix used for library files. */\nexports.libraryPrefix = __getString(assemblyscript.LIBRARY_PREFIX.valueOf());\n\n/** Default Binaryen optimization level. */\nexports.defaultOptimizeLevel = 3;\n\n/** Default Binaryen shrink level. */\nexports.defaultShrinkLevel = 0;\n\n/** Bundled library files. */\nexports.libraryFiles = exports.isBundle ? BUNDLE_LIBRARY : (() => { // set up if not a bundle\n  const libDir = path.join(__dirname, \"..\", \"std\", \"assembly\");\n  const bundled = {};\n  find\n    .files(libDir, defaultExtension.re_except_d)\n    .forEach(file => {\n      bundled[file.replace(defaultExtension.re, \"\")] = fs.readFileSync(path.join(libDir, file), \"utf8\");\n    });\n  return bundled;\n})();\n\n/** Bundled definition files. */\nexports.definitionFiles = exports.isBundle ? BUNDLE_DEFINITIONS : (() => { // set up if not a bundle\n  const readDefinition = name => fs.readFileSync(\n    path.join(__dirname, \"..\", \"std\", name, `index${defaultExtension.ext_d}`),\n    \"utf8\"\n  );\n  return {\n    assembly: readDefinition(\"assembly\"),\n    portable: readDefinition(\"portable\")\n  };\n})();\n\n/** Convenience function that parses and compiles source strings directly. */\nexports.compileString = (sources, options) => {\n  if (typeof sources === \"string\") sources = { [`input${defaultExtension.ext}`]: sources };\n  const output = Object.create({\n    stdout: createMemoryStream(),\n    stderr: createMemoryStream()\n  });\n  var argv = [\n    \"--binaryFile\", \"binary\",\n    \"--textFile\", \"text\",\n  ];\n  Object.keys(options || {}).forEach(key => {\n    var val = options[key];\n    var opt = exports.options[key];\n    if (opt && opt.type === \"b\") {\n      if (val) argv.push(`--${key}`);\n    } else {\n      if (Array.isArray(val)) {\n        val.forEach(val => { argv.push(`--${key}`, String(val)); });\n      }\n      else argv.push(`--${key}`, String(val));\n    }\n  });\n  exports.main(argv.concat(Object.keys(sources)), {\n    stdout: output.stdout,\n    stderr: output.stderr,\n    readFile: name => Object.prototype.hasOwnProperty.call(sources, name) ? sources[name] : null,\n    writeFile: (name, contents) => { output[name] = contents; },\n    listFiles: () => []\n  });\n  return output;\n};\n\n/** Runs the command line utility using the specified arguments array. */\nexports.main = function main(argv, options, callback) {\n  if (typeof options === \"function\") {\n    callback = options;\n    options = {};\n  } else if (!options) {\n    options = {};\n  }\n\n  // Bundle semantic version\n  let bundleMinorVersion = 0, bundleMajorVersion = 0, bundlePatchVersion = 0;\n  const versionParts = (exports.version || \"\").split(\".\");\n  if (versionParts.length === 3) {\n    bundleMajorVersion = parseInt(versionParts[0]) | 0;\n    bundleMinorVersion = parseInt(versionParts[1]) | 0;\n    bundlePatchVersion = parseInt(versionParts[2]) | 0;\n  }\n\n  const stdout = options.stdout || process.stdout;\n  const stderr = options.stderr || process.stderr;\n  const readFile = options.readFile || readFileNode;\n  const writeFile = options.writeFile || writeFileNode;\n  const listFiles = options.listFiles || listFilesNode;\n  const stats = options.stats || createStats();\n  let extension = defaultExtension;\n\n  // Output must be specified if not present in the environment\n  if (!stdout) throw Error(\"'options.stdout' must be specified\");\n  if (!stderr) throw Error(\"'options.stderr' must be specified\");\n\n  // Parse command line options but do not populate option defaults yet\n  const optionsResult = optionsUtil.parse(argv, exports.options, false);\n  let opts = optionsResult.options;\n  argv = optionsResult.arguments;\n\n  if (opts.noColors) {\n    colorsUtil.stdout.supported =\n    colorsUtil.stderr.supported = false;\n  } else {\n    colorsUtil.stdout = colorsUtil.from(stdout);\n    colorsUtil.stderr = colorsUtil.from(stderr);\n  }\n\n  // Check for unknown options\n  const unknownOpts = optionsResult.unknown;\n  if (unknownOpts.length) {\n    unknownOpts.forEach(arg => {\n      stderr.write(\n        `${colorsUtil.stderr.yellow(\"WARNING \")}Unknown option '${arg}'%{EOL}`\n      );\n    });\n  }\n\n  // Check for trailing arguments\n  const trailingArgv = optionsResult.trailing;\n  if (trailingArgv.length) {\n    stderr.write(\n      `${colorsUtil.stderr.yellow(\"WARNING \")}Unsupported trailing arguments: ${trailingArgv.join(\" \")}${EOL}`\n    );\n  }\n\n  // Use default callback if none is provided\n  if (!callback) callback = function defaultCallback(err) {\n    var code = 0;\n    if (err) {\n      stderr.write(`${colorsUtil.stderr.red(\"FAILURE \")}${err.stack.replace(/^ERROR: /i, \"\")}${EOL}`);\n      code = 1;\n    }\n    return code;\n  };\n\n  // Just print the version if requested\n  if (opts.version) {\n    stdout.write(`Version ${exports.version}${EOL}`);\n    return callback(null);\n  }\n\n  // Use another extension if requested\n  if (typeof opts.extension === \"string\") {\n    if (/^\\.?[0-9a-zA-Z]{1,14}$/.test(opts.extension)) {\n      extension = setupExtension(opts.extension);\n    } else {\n      return callback(Error(`Invalid extension: ${opts.extension}`));\n    }\n  }\n\n  // Set up base directory\n  const baseDir = path.normalize(opts.baseDir || \".\");\n\n  // Check if a config file is present\n  let asconfigPath = optionsUtil.resolvePath(opts.config || \"asconfig.json\", baseDir);\n  let asconfigFile = path.basename(asconfigPath);\n  let asconfigDir = path.dirname(asconfigPath);\n  let asconfig = getAsconfig(asconfigFile, asconfigDir, readFile);\n  let asconfigHasEntries = asconfig != null && Array.isArray(asconfig.entries) && asconfig.entries.length;\n\n  // Print the help message if requested or no source files are provided\n  if (opts.help || (!argv.length && !asconfigHasEntries)) {\n    var out = opts.help ? stdout : stderr;\n    var color = opts.help ? colorsUtil.stdout : colorsUtil.stderr;\n    out.write([\n      color.white(\"SYNTAX\"),\n      \"  \" + color.cyan(\"asc\") + \" [entryFile ...] [options]\",\n      \"\",\n      color.white(\"EXAMPLES\"),\n      \"  \" + color.cyan(\"asc\") + \" hello\" + extension.ext,\n      \"  \" + color.cyan(\"asc\") + \" hello\" + extension.ext + \" -b hello.wasm -t hello.wat\",\n      \"  \" + color.cyan(\"asc\") + \" hello1\" + extension.ext + \" hello2\" + extension.ext + \" -b -O > hello.wasm\",\n      \"  \" + color.cyan(\"asc\") + \" --config asconfig.json --target release\",\n      \"\",\n      color.white(\"OPTIONS\"),\n    ].concat(\n      optionsUtil.help(exports.options, 24, EOL)\n    ).join(EOL) + EOL);\n    return callback(null);\n  }\n\n  // I/O must be specified if not present in the environment\n  if (!fs.readFileSync) {\n    if (readFile === readFileNode)   throw Error(\"'options.readFile' must be specified\");\n    if (writeFile === writeFileNode) throw Error(\"'options.writeFile' must be specified\");\n    if (listFiles === listFilesNode) throw Error(\"'options.listFiles' must be specified\");\n  }\n\n  // Load additional options from asconfig.json\n  const seenAsconfig = new Set();\n  seenAsconfig.add(asconfigPath);\n  const target = opts.target || \"release\";\n  while (asconfig) {\n    // Merge target first\n    if (asconfig.targets) {\n      const targetOptions = asconfig.targets[target];\n      if (targetOptions) {\n        opts = optionsUtil.merge(exports.options, opts, targetOptions, asconfigDir);\n      }\n    }\n    // Merge general options\n    const generalOptions = asconfig.options;\n    if (generalOptions) {\n      opts = optionsUtil.merge(exports.options, opts, generalOptions, asconfigDir);\n    }\n\n    // Append entries\n    if (asconfig.entries) {\n      for (let entry of asconfig.entries) {\n        argv.push(optionsUtil.resolvePath(entry, asconfigDir));\n      }\n    }\n\n    // Look up extended asconfig and repeat\n    if (asconfig.extends) {\n      asconfigPath = optionsUtil.resolvePath(asconfig.extends, asconfigDir, true);\n      asconfigFile = path.basename(asconfigPath);\n      asconfigDir = path.dirname(asconfigPath);\n      if (seenAsconfig.has(asconfigPath)) break;\n      seenAsconfig.add(asconfigPath);\n      asconfig = getAsconfig(asconfigFile, asconfigDir, readFile);\n    } else {\n      break;\n    }\n  }\n\n  // Populate option defaults once user-defined options are set\n  optionsUtil.addDefaults(exports.options, opts);\n\n  // If showConfig print options and exit\n  if (opts.showConfig) {\n    stderr.write(JSON.stringify({\n      options: opts,\n      entries: argv\n    }, null, 2));\n    return callback(null);\n  }\n\n  // create a unique set of values\n  function unique(values) {\n    return [...new Set(values)];\n  }\n\n  // Set up options\n  var program;\n  const compilerOptions = __pin(assemblyscript.newOptions());\n  assemblyscript.setTarget(compilerOptions, 0);\n  assemblyscript.setNoAssert(compilerOptions, opts.noAssert);\n  assemblyscript.setExportMemory(compilerOptions, !opts.noExportMemory);\n  assemblyscript.setImportMemory(compilerOptions, opts.importMemory);\n  assemblyscript.setInitialMemory(compilerOptions, opts.initialMemory >>> 0);\n  assemblyscript.setMaximumMemory(compilerOptions, opts.maximumMemory >>> 0);\n  assemblyscript.setSharedMemory(compilerOptions, opts.sharedMemory);\n  assemblyscript.setImportTable(compilerOptions, opts.importTable);\n  assemblyscript.setExportTable(compilerOptions, opts.exportTable);\n  assemblyscript.setExplicitStart(compilerOptions, opts.explicitStart);\n  assemblyscript.setMemoryBase(compilerOptions, opts.memoryBase >>> 0);\n  assemblyscript.setTableBase(compilerOptions, opts.tableBase >>> 0);\n  assemblyscript.setSourceMap(compilerOptions, opts.sourceMap != null);\n  assemblyscript.setNoUnsafe(compilerOptions, opts.noUnsafe);\n  assemblyscript.setPedantic(compilerOptions, opts.pedantic);\n  assemblyscript.setLowMemoryLimit(compilerOptions, opts.lowMemoryLimit >>> 0);\n  assemblyscript.setExportRuntime(compilerOptions, opts.exportRuntime);\n  assemblyscript.setBundleVersion(compilerOptions, bundleMajorVersion, bundleMinorVersion, bundlePatchVersion);\n  if (!opts.stackSize && opts.runtime == \"incremental\") {\n    opts.stackSize = assemblyscript.DEFAULT_STACK_SIZE;\n  }\n  assemblyscript.setStackSize(compilerOptions, opts.stackSize);\n\n  // Instrument callback to perform GC\n  callback = (function(callback) {\n    return function wrappedCallback(err) {\n      __unpin(compilerOptions);\n      if (program) __unpin(program);\n      __collect();\n      return callback(err);\n    };\n  })(callback);\n\n  // Add or override aliases if specified\n  if (opts.use) {\n    let aliases = opts.use;\n    for (let i = 0, k = aliases.length; i < k; ++i) {\n      let part = aliases[i];\n      let p = part.indexOf(\"=\");\n      if (p < 0) return callback(Error(`Global alias '${part}' is invalid.`));\n      let alias = part.substring(0, p).trim();\n      let name = part.substring(p + 1).trim();\n      if (!alias.length) {\n        return callback(Error(`Global alias '${part}' is invalid.`));\n      }\n      {\n        let aliasPtr = __pin(__newString(alias));\n        let namePtr = __newString(name);\n        assemblyscript.setGlobalAlias(compilerOptions, aliasPtr, namePtr);\n        __unpin(aliasPtr);\n      }\n    }\n  }\n\n  // Disable default features if specified\n  var features;\n  if ((features = opts.disable) != null) {\n    if (typeof features === \"string\") features = features.split(\",\");\n    for (let i = 0, k = features.length; i < k; ++i) {\n      let name = features[i].trim();\n      let flag = assemblyscript[`FEATURE_${toUpperSnakeCase(name)}`];\n      if (!flag) return callback(Error(`Feature '${name}' is unknown.`));\n      assemblyscript.disableFeature(compilerOptions, flag);\n    }\n  }\n\n  // Enable experimental features if specified\n  if ((features = opts.enable) != null) {\n    if (typeof features === \"string\") features = features.split(\",\");\n    for (let i = 0, k = features.length; i < k; ++i) {\n      let name = features[i].trim();\n      let flag = assemblyscript[`FEATURE_${toUpperSnakeCase(name)}`];\n      if (!flag) return callback(Error(`Feature '${name}' is unknown.`));\n      assemblyscript.enableFeature(compilerOptions, flag);\n    }\n  }\n\n  // Set up optimization levels\n  var optimizeLevel = 0;\n  var shrinkLevel = 0;\n  if (opts.optimize) {\n    optimizeLevel = exports.defaultOptimizeLevel;\n    shrinkLevel = exports.defaultShrinkLevel;\n  }\n  if (typeof opts.optimizeLevel === \"number\") optimizeLevel = opts.optimizeLevel;\n  if (typeof opts.shrinkLevel === \"number\") shrinkLevel = opts.shrinkLevel;\n  optimizeLevel = Math.min(Math.max(optimizeLevel, 0), 3);\n  shrinkLevel = Math.min(Math.max(shrinkLevel, 0), 2);\n  assemblyscript.setOptimizeLevelHints(compilerOptions, optimizeLevel, shrinkLevel);\n\n  // Initialize the program\n  program = __pin(assemblyscript.newProgram(compilerOptions));\n\n  // Collect transforms *constructors* from the `--transform` CLI flag as well\n  // as the `transform` option into the `transforms` array.\n  let transforms = [];\n  // `transform` option from `main()`\n  if (Array.isArray(options.transforms)) {\n    transforms.push(...options.transforms);\n  }\n  // `--transform` CLI flag\n  if (opts.transform) {\n    let tsNodeRegistered = false;\n    let transformArgs = unique(opts.transform);\n    for (let i = 0, k = transformArgs.length; i < k; ++i) {\n      let filename = transformArgs[i].trim();\n      if (!tsNodeRegistered && filename.endsWith(\".ts\")) { // ts-node requires .ts specifically\n        dynrequire(\"ts-node\").register({\n          transpileOnly: true,\n          skipProject: true,\n          compilerOptions: { target: \"ES2016\" }\n        });\n        tsNodeRegistered = true;\n      }\n      try {\n        transforms.push(dynrequire(dynrequire.resolve(filename, {\n          paths: [baseDir, process.cwd()]\n        })));\n      } catch (e) {\n        return callback(e);\n      }\n    }\n  }\n\n  // Fix up the prototype of the transforms’ constructors and instantiate them.\n  try {\n    transforms = transforms.map(classOrModule => {\n      // Except if it’s a legacy module, just pass it through.\n      if (typeof classOrModule !== \"function\") {\n        return classOrModule;\n      }\n      Object.assign(classOrModule.prototype, {\n        program,\n        baseDir,\n        stdout,\n        stderr,\n        log: console.error,\n        readFile,\n        writeFile,\n        listFiles\n      });\n      return new classOrModule();\n    });\n  } catch (e) {\n    return callback(e);\n  }\n\n  function applyTransform(name, ...args) {\n    for (let i = 0, k = transforms.length; i < k; ++i) {\n      let transform = transforms[i];\n      if (typeof transform[name] === \"function\") {\n        try {\n          stats.transformCount++;\n          stats.transfromTime += measure(() => {\n            transform[name](...args);\n          });\n        } catch (e) {\n          return e;\n        }\n      }\n    }\n  }\n\n  // Parse library files\n  Object.keys(exports.libraryFiles).forEach(libPath => {\n    if (libPath.includes(\"/\")) return; // in sub-directory: imported on demand\n    stats.parseCount++;\n    stats.parseTime += measure(() => {\n      let textPtr = __pin(__newString(exports.libraryFiles[libPath]));\n      let pathPtr = __newString(exports.libraryPrefix + libPath + extension.ext);\n      assemblyscript.parse(program, textPtr, pathPtr, false);\n      __unpin(textPtr);\n    });\n  });\n  let customLibDirs = [];\n  if (opts.lib) {\n    let lib = opts.lib;\n    if (typeof lib === \"string\") lib = lib.split(\",\");\n    customLibDirs.push(...lib.map(p => p.trim()));\n    customLibDirs = unique(customLibDirs); // `lib` and `customLibDirs` may include duplicates\n    for (let i = 0, k = customLibDirs.length; i < k; ++i) { // custom\n      let libDir = customLibDirs[i];\n      let libFiles;\n      if (libDir.endsWith(extension.ext)) {\n        libFiles = [ path.basename(libDir) ];\n        libDir = path.dirname(libDir);\n      } else {\n        libFiles = listFiles(libDir, baseDir) || [];\n      }\n      for (let j = 0, l = libFiles.length; j < l; ++j) {\n        let libPath = libFiles[j];\n        let libText = readFile(libPath, libDir);\n        if (libText == null) {\n          return callback(Error(`Library file '${libPath}' not found.`));\n        }\n        stats.parseCount++;\n        exports.libraryFiles[libPath.replace(extension.re, \"\")] = libText;\n        stats.parseTime += measure(() => {\n          let textPtr = __pin(__newString(libText));\n          let pathPtr = __newString(exports.libraryPrefix + libPath);\n          assemblyscript.parse(program, textPtr, pathPtr, false);\n          __unpin(textPtr);\n        });\n      }\n    }\n  }\n  opts.path = opts.path || [];\n\n  // Maps package names to parent directory\n  const packageMains = new Map();\n  const packageBases = new Map();\n\n  // Gets the file matching the specified source path, imported at the given dependee path\n  function getFile(internalPath, dependeePath) {\n    var sourceText = null; // text reported back to the compiler\n    var sourcePath = null; // path reported back to the compiler\n\n    const libraryPrefix = exports.libraryPrefix;\n    const libraryFiles = exports.libraryFiles;\n\n    // Try file.ext, file/index.ext, file.d.ext\n    if (!internalPath.startsWith(libraryPrefix)) {\n      if ((sourceText = readFile(sourcePath = internalPath + extension.ext, baseDir)) == null) {\n        if ((sourceText = readFile(sourcePath = internalPath + \"/index\" + extension.ext, baseDir)) == null) {\n          // portable d.ext: uses the .js file next to it in JS or becomes an import in Wasm\n          sourcePath = internalPath + extension.ext;\n          sourceText = readFile(internalPath + extension.ext_d, baseDir);\n        }\n      }\n\n    // Search library in this order: stdlib, custom lib dirs, paths\n    } else {\n      const plainName = internalPath.substring(libraryPrefix.length);\n      const indexName = `${plainName}/index`;\n      if (Object.prototype.hasOwnProperty.call(libraryFiles, plainName)) {\n        sourceText = libraryFiles[plainName];\n        sourcePath = libraryPrefix + plainName + extension.ext;\n      } else if (Object.prototype.hasOwnProperty.call(libraryFiles, indexName)) {\n        sourceText = libraryFiles[indexName];\n        sourcePath = libraryPrefix + indexName + extension.ext;\n      } else { // custom lib dirs\n        for (const libDir of customLibDirs) {\n          if ((sourceText = readFile(plainName + extension.ext, libDir)) != null) {\n            sourcePath = libraryPrefix + plainName + extension.ext;\n            break;\n          } else {\n            if ((sourceText = readFile(indexName + extension.ext, libDir)) != null) {\n              sourcePath = libraryPrefix + indexName + extension.ext;\n              break;\n            }\n          }\n        }\n        if (sourceText == null) { // paths\n          const match = internalPath.match(/^~lib\\/((?:@[^/]+\\/)?[^/]+)(?:\\/(.+))?/); // ~lib/(pkg)/(path), ~lib/(@org/pkg)/(path)\n          if (match) {\n            const packageName = match[1];\n            const isPackageRoot = match[2] === undefined;\n            const filePath = isPackageRoot ? \"index\" : match[2];\n            const basePath = packageBases.has(dependeePath)\n              ? packageBases.get(dependeePath)\n              : \".\";\n\n            if (opts.traceResolution) {\n              stderr.write(`Looking for package '${packageName}' file '${filePath}' relative to '${basePath}'${EOL}`);\n            }\n            const paths = [];\n            const parts = path.resolve(baseDir, basePath).split(SEP);\n            for (let i = parts.length, k = WIN ? 1 : 0; i >= k; --i) {\n              if (parts[i - 1] !== \"node_modules\") {\n                paths.push(`${parts.slice(0, i).join(SEP)}${SEP}node_modules`);\n              }\n            }\n            for (const currentPath of paths.concat(...opts.path).map(p => path.relative(baseDir, p))) {\n              if (opts.traceResolution) {\n                stderr.write(`  in ${path.join(currentPath, packageName)}${EOL}`);\n              }\n              let mainPath = \"assembly\";\n              if (packageMains.has(packageName)) { // use cached\n                mainPath = packageMains.get(packageName);\n              } else { // evaluate package.json\n                let jsonPath = path.join(currentPath, packageName, \"package.json\");\n                let jsonText = readFile(jsonPath, baseDir);\n                if (jsonText != null) {\n                  try {\n                    let json = JSON.parse(jsonText);\n                    if (typeof json.ascMain === \"string\") {\n                      mainPath = json.ascMain.replace(extension.re_index, \"\");\n                      packageMains.set(packageName, mainPath);\n                    }\n                  } catch (e) { /* nop */ }\n                }\n              }\n              const mainDir = path.join(currentPath, packageName, mainPath);\n              const plainName = filePath;\n              if ((sourceText = readFile(path.join(mainDir, plainName + extension.ext), baseDir)) != null) {\n                sourcePath = `${libraryPrefix}${packageName}/${plainName}${extension.ext}`;\n                packageBases.set(sourcePath.replace(extension.re, \"\"), path.join(currentPath, packageName));\n                if (opts.traceResolution) {\n                  stderr.write(`  -> ${path.join(mainDir, plainName + extension.ext)}${EOL}`);\n                }\n                break;\n              } else if (!isPackageRoot) {\n                const indexName = `${filePath}/index`;\n                if ((sourceText = readFile(path.join(mainDir, indexName + extension.ext), baseDir)) !== null) {\n                  sourcePath = `${libraryPrefix}${packageName}/${indexName}${extension.ext}`;\n                  packageBases.set(sourcePath.replace(extension.re, \"\"), path.join(currentPath, packageName));\n                  if (opts.traceResolution) {\n                    stderr.write(`  -> ${path.join(mainDir, indexName + extension.ext)}${EOL}`);\n                  }\n                  break;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    // No such file\n    if (sourceText == null) return null;\n    return { sourceText, sourcePath };\n  }\n\n  // Parses the backlog of imported files after including entry files\n  function parseBacklog() {\n    var internalPath;\n    while ((internalPath = __getString(assemblyscript.nextFile(program)))) {\n      let file = getFile(internalPath, assemblyscript.getDependee(program, internalPath));\n      if (file) {\n        stats.parseCount++;\n        stats.parseTime += measure(() => {\n          let textPtr = __pin(__newString(file.sourceText));\n          let pathPtr = __newString(file.sourcePath);\n          assemblyscript.parse(program, textPtr, pathPtr, false);\n          __unpin(textPtr);\n        });\n      } else {\n        stats.parseTime += measure(() => {\n          let textPtr = __newString(null); // no need to pin\n          let pathPtr = __newString(internalPath + extension.ext);\n          assemblyscript.parse(program, textPtr, pathPtr, false);\n        });\n      }\n    }\n    var numErrors = checkDiagnostics(program, stderr, options.reportDiagnostic);\n    if (numErrors) {\n      const err = Error(`${numErrors} parse error(s)`);\n      err.stack = err.message; // omit stack\n      return callback(err);\n    }\n  }\n\n  // Include runtime before entry files so its setup runs first\n  {\n    let runtimeName = String(opts.runtime);\n    let runtimePath = `rt/index-${runtimeName}`;\n    let runtimeText = exports.libraryFiles[runtimePath];\n    if (runtimeText == null) {\n      runtimePath = runtimeName;\n      runtimeText = readFile(runtimePath + extension.ext, baseDir);\n      if (runtimeText == null) return callback(Error(`Runtime '${runtimeName}' not found.`));\n    } else {\n      runtimePath = `~lib/${runtimePath}`;\n    }\n    stats.parseCount++;\n    stats.parseTime += measure(() => {\n      let textPtr = __pin(__newString(runtimeText));\n      let pathPtr = __newString(runtimePath + extension.ext);\n      assemblyscript.parse(program, textPtr, pathPtr, true);\n      __unpin(textPtr);\n    });\n  }\n\n  // Include entry files\n  for (let i = 0, k = argv.length; i < k; ++i) {\n    const filename = argv[i];\n    let sourcePath = String(filename)\n      .replace(/\\\\/g, \"/\")\n      .replace(extension.re, \"\")\n      .replace(/[\\\\/]$/, \"\");\n\n    // Setting the path to relative path\n    sourcePath = path.isAbsolute(sourcePath)\n      ? path.relative(baseDir, sourcePath).replace(/\\\\/g, \"/\")\n      : sourcePath;\n\n    // Try entryPath.ext, then entryPath/index.ext\n    let sourceText = readFile(sourcePath + extension.ext, baseDir);\n    if (sourceText == null) {\n      const path = `${sourcePath}/index${extension.ext}`;\n      sourceText = readFile(path, baseDir);\n      if (sourceText != null) sourcePath = path;\n      else sourcePath += extension.ext;\n    } else {\n      sourcePath += extension.ext;\n    }\n\n    stats.parseCount++;\n    stats.parseTime += measure(() => {\n      let textPtr = __pin(__newString(sourceText));\n      let pathPtr = __newString(sourcePath);\n      assemblyscript.parse(program, textPtr, pathPtr, true);\n      __unpin(textPtr);\n    });\n  }\n\n  // Parse entry files\n  {\n    let code = parseBacklog();\n    if (code) return code;\n  }\n\n  // Call afterParse transform hook\n  {\n    let error = applyTransform(\"afterParse\", program.parser);\n    if (error) return callback(error);\n  }\n\n  // Parse additional files, if any\n  {\n    let code = parseBacklog();\n    if (code) return code;\n  }\n\n  // Print files and exit if listFiles\n  if (opts.listFiles) {\n    // FIXME: not a proper C-like API\n    stderr.write(program.sources.map(s => s.normalizedPath).sort().join(EOL) + EOL);\n    return callback(null);\n  }\n\n  // Pre-emptively initialize the program\n  stats.initializeCount++;\n  stats.initializeTime += measure(() => {\n    try {\n      assemblyscript.initializeProgram(program);\n    } catch (e) {\n      crash(\"initialize\", e);\n    }\n  });\n\n  // Call afterInitialize transform hook\n  {\n    let error = applyTransform(\"afterInitialize\", program);\n    if (error) return callback(error);\n  }\n\n  var module;\n  stats.compileCount++;\n  stats.compileTime += measure(() => {\n    try {\n      module = assemblyscript.compile(program);\n    } catch (e) {\n      crash(\"compile\", e);\n    }\n    // From here on we are going to use Binaryen.js, except that we keep pass\n    // order as defined in the compiler.\n    if (typeof module === \"number\") { // Wasm\n      const original = assemblyscript.Module.wrap(module);\n      module = binaryen.wrapModule(original.ref);\n      module.optimize = function(...args) {\n        original.optimize(...args);\n      };\n    } else { // JS\n      const original = module;\n      module = binaryen.wrapModule(module.ref);\n      module.optimize = function(...args) {\n        original.optimize(...args);\n      };\n    }\n  });\n  var numErrors = checkDiagnostics(program, stderr, options.reportDiagnostic);\n  if (numErrors) {\n    if (module) module.dispose();\n    const err = Error(`${numErrors} compile error(s)`);\n    err.stack = err.message; // omit stack\n    return callback(err);\n  }\n\n  // Call afterCompile transform hook\n  {\n    let error = applyTransform(\"afterCompile\", module);\n    if (error) return callback(error);\n  }\n\n  // Validate the module if requested\n  if (!opts.noValidate) {\n    stats.validateCount++;\n    let isValid;\n    stats.validateTime += measure(() => {\n      isValid = module.validate();\n    });\n    if (!isValid) {\n      module.dispose();\n      return callback(Error(\"validate error\"));\n    }\n  }\n\n  // Set Binaryen-specific options\n  if (opts.trapMode === \"clamp\" || opts.trapMode === \"js\") {\n    stats.optimizeCount++;\n    stats.optimizeTime += measure(() => {\n      try {\n        module.runPasses([`trap-mode-${opts.trapMode}`]);\n      } catch (e) {\n        crash(\"runPasses\", e);\n      }\n    });\n  } else if (opts.trapMode !== \"allow\") {\n    module.dispose();\n    return callback(Error(\"Unsupported trap mode\"));\n  }\n\n  // Optimize the module\n  const debugInfo = opts.debug;\n  const converge = opts.converge;\n  const runPasses = [];\n  if (opts.runPasses) {\n    if (typeof opts.runPasses === \"string\") {\n      opts.runPasses = opts.runPasses.split(\",\");\n    }\n    if (opts.runPasses.length) {\n      opts.runPasses.forEach(pass => {\n        if (!runPasses.includes(pass = pass.trim())) {\n          runPasses.push(pass);\n        }\n      });\n    }\n  }\n\n  stats.optimizeTime += measure(() => {\n    stats.optimizeCount++;\n    try {\n      module.optimize(optimizeLevel, shrinkLevel, debugInfo);\n    } catch (e) {\n      crash(\"optimize\", e);\n    }\n    try {\n      module.runPasses(runPasses);\n    } catch (e) {\n      crash(\"runPasses\", e);\n    }\n    if (converge) {\n      let last;\n      try {\n        last = module.emitBinary();\n      } catch (e) {\n        crash(\"emitBinary (converge)\", e);\n      }\n      do {\n        stats.optimizeCount++;\n        try {\n          module.optimize(optimizeLevel, shrinkLevel, debugInfo);\n        } catch (e) {\n          crash(\"optimize (converge)\", e);\n        }\n        try {\n          module.runPasses(runPasses);\n        } catch (e) {\n          crash(\"runPasses (converge)\", e);\n        }\n        let next;\n        try {\n          next = module.emitBinary();\n        } catch (e) {\n          crash(\"emitBinary (converge)\", e);\n        }\n        if (next.length >= last.length) {\n          if (next.length > last.length) {\n            stderr.write(`Last converge was suboptimial.${EOL}`);\n          }\n          break;\n        }\n        last = next;\n      } while (true);\n    }\n  });\n\n  // Prepare output\n  if (!opts.noEmit) {\n    if (opts.outFile != null) {\n      if (opts.textFile == null && /\\.was?t$/.test(opts.outFile)) {\n        opts.textFile = opts.outFile;\n      } else if (opts.jsFile == null && /\\.js$/.test(opts.outFile)) {\n        opts.jsFile = opts.outFile;\n      } else if (opts.binaryFile == null) {\n        opts.binaryFile = opts.outFile;\n      }\n    }\n\n    let hasStdout = false;\n    let hasOutput = opts.textFile != null\n                 || opts.binaryFile != null\n                 || opts.jsFile != null\n                 || opts.tsdFile != null\n                 || opts.idlFile != null;\n\n    // Write binary\n    if (opts.binaryFile != null) {\n      let basename = path.basename(opts.binaryFile);\n      let sourceMapURL = opts.sourceMap != null\n        ? opts.sourceMap.length\n          ? opts.sourceMap\n          : `./${basename}.map`\n        : null;\n\n      let wasm;\n      stats.emitCount++;\n      stats.emitTime += measure(() => {\n        try {\n          wasm = module.emitBinary(sourceMapURL);\n        } catch (e) {\n          crash(\"emitBinary\", e);\n        }\n      });\n\n      if (opts.binaryFile.length) {\n        writeFile(opts.binaryFile, wasm.binary, baseDir);\n      } else {\n        writeStdout(wasm.binary);\n        hasStdout = true;\n      }\n\n      // Post-process source map\n      if (wasm.sourceMap != \"\") {\n        if (opts.binaryFile.length) {\n          let map = JSON.parse(wasm.sourceMap);\n          map.sourceRoot = `./${basename}`;\n          let contents = [];\n          map.sources.forEach((name, index) => {\n            let text = assemblyscript.getSource(program, __newString(name.replace(extension.re, \"\")));\n            if (text == null) return callback(Error(`Source of file '${name}' not found.`));\n            contents[index] = text;\n          });\n          map.sourcesContent = contents;\n          writeFile(path.join(\n            path.dirname(opts.binaryFile),\n            path.basename(sourceMapURL)\n          ).replace(/^\\.\\//, \"\"), JSON.stringify(map), baseDir);\n        } else {\n          stderr.write(`Skipped source map (stdout already occupied)${EOL}`);\n        }\n      }\n    }\n\n    // Write text (also fallback)\n    if (opts.textFile != null || !hasOutput) {\n      let out;\n      if (opts.textFile != null && opts.textFile.length) {\n        // use superset text format when extension is `.wast`.\n        // Otherwise use official stack IR format (wat).\n        let wastFormat = opts.textFile.endsWith(\".wast\");\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            out = wastFormat\n              ? module.emitText()\n              : module.emitStackIR(true);\n          } catch (e) {\n            crash(\"emitText\", e);\n          }\n        });\n        writeFile(opts.textFile, out, baseDir);\n      } else if (!hasStdout) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            out = module.emitStackIR(true);\n          } catch (e) {\n            crash(\"emitText\", e);\n          }\n        });\n        writeStdout(out);\n      }\n    }\n\n    // Write WebIDL\n    if (opts.idlFile != null) {\n      let idl;\n      if (opts.idlFile.length) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            idl = assemblyscript.buildIDL(program);\n          } catch (e) {\n            crash(\"buildIDL\", e);\n          }\n        });\n        writeFile(opts.idlFile, __getString(idl), baseDir);\n      } else if (!hasStdout) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            idl = assemblyscript.buildIDL(program);\n          } catch (e) {\n            crash(\"buildIDL\", e);\n          }\n        });\n        writeStdout(__getString(idl));\n        hasStdout = true;\n      }\n    }\n\n    // Write TypeScript definition\n    if (opts.tsdFile != null) {\n      let tsd;\n      if (opts.tsdFile.length) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            tsd = assemblyscript.buildTSD(program);\n          } catch (e) {\n            crash(\"buildTSD\", e);\n          }\n        });\n        writeFile(opts.tsdFile, __getString(tsd), baseDir);\n      } else if (!hasStdout) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            tsd = assemblyscript.buildTSD(program);\n          } catch (e) {\n            crash(\"buildTSD\", e);\n          }\n        });\n        writeStdout(__getString(tsd));\n        hasStdout = true;\n      }\n    }\n\n    // Write JS (modifies the binary, so must be last)\n    if (opts.jsFile != null) {\n      let js;\n      if (opts.jsFile.length) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            js = module.emitAsmjs();\n          } catch (e) {\n            crash(\"emitJS\", e);\n          }\n        });\n        writeFile(opts.jsFile, js, baseDir);\n      } else if (!hasStdout) {\n        stats.emitCount++;\n        stats.emitTime += measure(() => {\n          try {\n            js = module.emitAsmjs();\n          } catch (e) {\n            crash(\"emitJS\", e);\n          }\n        });\n        writeStdout(js);\n      }\n    }\n  }\n\n  module.dispose();\n  if (opts.measure) {\n    printStats(stats, stderr);\n  }\n\n  return callback(null);\n\n  function readFileNode(filename, baseDir) {\n    let name = path.resolve(baseDir, filename);\n    try {\n      let text;\n      stats.readCount++;\n      stats.readTime += measure(() => {\n        text = fs.readFileSync(name, \"utf8\");\n      });\n      return text;\n    } catch (e) {\n      return null;\n    }\n  }\n\n  function writeFileNode(filename, contents, baseDir) {\n    try {\n      stats.writeCount++;\n      stats.writeTime += measure(() => {\n        const dirPath = path.resolve(baseDir, path.dirname(filename));\n        filename = path.basename(filename);\n        const outputFilePath = path.join(dirPath, filename);\n        if (!fs.existsSync(dirPath)) mkdirp(dirPath);\n        fs.writeFileSync(outputFilePath, contents);\n      });\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  function listFilesNode(dirname, baseDir) {\n    var files;\n    try {\n      stats.readCount++;\n      stats.readTime += measure(() => {\n        files = fs.readdirSync(path.join(baseDir, dirname))\n          .filter(file => extension.re_except_d.test(file));\n      });\n      return files;\n    } catch (e) {\n      return null;\n    }\n  }\n\n  function writeStdout(contents) {\n    if (!writeStdout.used) {\n      stats.writeCount++;\n      writeStdout.used = true;\n    }\n    stats.writeTime += measure(() => {\n      stdout.write(contents);\n    });\n  }\n};\n\nconst toString = Object.prototype.toString;\n\nfunction isObject(arg) {\n  return toString.call(arg) === \"[object Object]\";\n}\n\nfunction getAsconfig(file, baseDir, readFile) {\n  const contents = readFile(file, baseDir);\n  const location = path.join(baseDir, file);\n  if (!contents) return null;\n\n  // obtain the configuration\n  let config;\n  try {\n    config = JSON.parse(contents);\n  } catch(ex) {\n    throw new Error(`Asconfig is not valid json: ${location}`);\n  }\n\n  // validate asconfig shape\n  if (config.options && !isObject(config.options)) {\n    throw new Error(`Asconfig.options is not an object: ${location}`);\n  }\n\n  if (config.include && !Array.isArray(config.include)) {\n    throw new Error(`Asconfig.include is not an array: ${location}`);\n  }\n\n  if (config.targets) {\n    if (!isObject(config.targets)) {\n      throw new Error(`Asconfig.targets is not an object: ${location}`);\n    }\n    const targets = Object.keys(config.targets);\n    for (let i = 0; i < targets.length; i++) {\n      const target = targets[i];\n      if (!isObject(config.targets[target])) {\n        throw new Error(`Asconfig.targets.${target} is not an object: ${location}`);\n      }\n    }\n  }\n\n  if (config.extends && typeof config.extends !== \"string\") {\n    throw new Error(`Asconfig.extends is not a string: ${location}`);\n  }\n\n  return config;\n}\n\nexports.getAsconfig = getAsconfig;\n\n/** Checks diagnostics emitted so far for errors. */\nfunction checkDiagnostics(program, stderr, reportDiagnostic) {\n  var numErrors = 0;\n  do {\n    let diagnosticPtr = assemblyscript.nextDiagnostic(program);\n    if (!diagnosticPtr) break;\n    __pin(diagnosticPtr);\n    if (stderr) {\n      stderr.write(\n        __getString(assemblyscript.formatDiagnostic(diagnosticPtr, stderr.isTTY, true)) +\n        EOL + EOL\n      );\n    }\n    if (reportDiagnostic) {\n      const diagnostic = __wrap(diagnosticPtr, assemblyscript.DiagnosticMessage);\n      const range = __wrap(diagnostic.range, assemblyscript.Range);\n      const relatedRange = __wrap(diagnostic.relatedRange, assemblyscript.Range);\n      const rangeSource = range ? __wrap(range.source, assemblyscript.Source) : null;\n      const relatedRangeSource = relatedRange ? __wrap(relatedRange.source, assemblyscript.Source) : null;\n\n      reportDiagnostic({\n        message: __getString(diagnostic.message),\n        code: diagnostic.code,\n        category: diagnostic.category,\n        range: range ? {\n          start: range.start,\n          end: range.end,\n          source: rangeSource ? {\n            normalizedPath: __getString(rangeSource.normalizedPath)\n          } : null,\n        } : null,\n        relatedRange: relatedRange ? {\n          start: relatedRange.start,\n          end: relatedRange.end,\n          source: relatedRangeSource ? {\n            normalizedPath: __getString(relatedRangeSource.normalizedPath)\n          } : null\n        } : null\n      });\n    }\n    if (assemblyscript.isError(diagnosticPtr)) ++numErrors;\n    __unpin(diagnosticPtr);\n  } while (true);\n  return numErrors;\n}\n\nexports.checkDiagnostics = checkDiagnostics;\n\n/** Creates an empty set of stats. */\nfunction createStats() {\n  return {\n    readTime: 0,\n    readCount: 0,\n    writeTime: 0,\n    writeCount: 0,\n    parseTime: 0,\n    parseCount: 0,\n    initializeTime: 0,\n    initializeCount: 0,\n    compileTime: 0,\n    compileCount: 0,\n    emitTime: 0,\n    emitCount: 0,\n    validateTime: 0,\n    validateCount: 0,\n    optimizeTime: 0,\n    optimizeCount: 0,\n    transformTime: 0,\n    transformCount: 0\n  };\n}\n\nexports.createStats = createStats;\n\n/** Measures the execution time of the specified function.  */\nfunction measure(fn) {\n  const start = process.hrtime();\n  fn();\n  const times = process.hrtime(start);\n  return times[0] * 1e9 + times[1];\n}\n\nexports.measure = measure;\n\nfunction pad(str, len) {\n  while (str.length < len) str = ` ${str}`;\n  return str;\n}\n\n/** Formats a high resolution time to a human readable string. */\nfunction formatTime(time) {\n  return time ? `${(time / 1e6).toFixed(3)} ms` : \"n/a\";\n}\n\nexports.formatTime = formatTime;\n\n/** Formats and prints out the contents of a set of stats. */\nfunction printStats(stats, output) {\n  const format = (time, count) => `${pad(formatTime(time), 12)}  n=${count}`;\n  (output || process.stdout).write([\n    \"I/O Read   : \" + format(stats.readTime, stats.readCount),\n    \"I/O Write  : \" + format(stats.writeTime, stats.writeCount),\n    \"Parse      : \" + format(stats.parseTime, stats.parseCount),\n    \"Initialize : \" + format(stats.initializeTime, stats.initializeCount),\n    \"Compile    : \" + format(stats.compileTime, stats.compileCount),\n    \"Emit       : \" + format(stats.emitTime, stats.emitCount),\n    \"Validate   : \" + format(stats.validateTime, stats.validateCount),\n    \"Optimize   : \" + format(stats.optimizeTime, stats.optimizeCount),\n    \"Transform  : \" + format(stats.transformTime, stats.transformCount),\n    \"\"\n  ].join(EOL) + EOL);\n}\n\nexports.printStats = printStats;\n\nvar allocBuffer = typeof global !== \"undefined\" && global.Buffer\n  ? global.Buffer.allocUnsafe || (len => new global.Buffer(len))\n  : len => new Uint8Array(len);\n\n/** Creates a memory stream that can be used in place of stdout/stderr. */\nfunction createMemoryStream(fn) {\n  var stream = [];\n  stream.write = function(chunk) {\n    if (fn) fn(chunk);\n    if (typeof chunk === \"string\") {\n      let buffer = allocBuffer(utf8.length(chunk));\n      utf8.write(chunk, buffer, 0);\n      chunk = buffer;\n    }\n    this.push(chunk);\n  };\n  stream.reset = function() {\n    stream.length = 0;\n  };\n  stream.toBuffer = function() {\n    var offset = 0, i = 0, k = this.length;\n    while (i < k) offset += this[i++].length;\n    var buffer = allocBuffer(offset);\n    offset = i = 0;\n    while (i < k) {\n      buffer.set(this[i], offset);\n      offset += this[i].length;\n      ++i;\n    }\n    return buffer;\n  };\n  stream.toString = function() {\n    var buffer = this.toBuffer();\n    return utf8.read(buffer, 0, buffer.length);\n  };\n  return stream;\n}\n\nexports.createMemoryStream = createMemoryStream;\n\n/** Compatible TypeScript compiler options for syntax highlighting etc. */\nexports.tscOptions = {\n  alwaysStrict: true,\n  noImplicitAny: true,\n  noImplicitReturns: true,\n  noImplicitThis: true,\n  noEmitOnError: true,\n  strictNullChecks: true,\n  experimentalDecorators: true,\n  target: \"esnext\",\n  module: \"commonjs\",\n  noLib: true,\n  types: [],\n  allowJs: false\n};\n\n// Gracefully handle crashes\nfunction crash(stage, e) {\n  const BAR = colorsUtil.red(\"▌ \");\n  console.error([\n    EOL,\n    BAR, \"Whoops, the AssemblyScript compiler has crashed during \", stage, \" :-(\", EOL,\n    BAR, EOL,\n    BAR, \"Here is a stack trace that may or may not be useful:\", EOL,\n    BAR, EOL,\n    e.stack.replace(/^/mg, BAR), EOL,\n    BAR, EOL,\n    BAR, \"If it refers to the dist files, try to 'npm install source-map-support' and\", EOL,\n    BAR, \"run again, which should then show the actual code location in the sources.\", EOL,\n    BAR, EOL,\n    BAR, \"If you see where the error is, feel free to send us a pull request. If not,\", EOL,\n    BAR, \"please let us know: https://github.com/AssemblyScript/assemblyscript/issues\", EOL,\n    BAR, EOL,\n    BAR, \"Thank you!\", EOL\n  ].join(\"\"));\n  process.exit(1);\n}\n", "module.exports = {};\n", "const process = require(\"process\"); // ensure shim\n\n// https://github.com/browserify/path-browserify v1.0.1\n//\n// Copyright (c) 2013 <PERSON>\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 || res.charCodeAt(res.length - 2) !== 46) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    if (from === \".\") return to; // FIX for 'odule.ts' (see issue #1398)\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n        if (!matchedSlash) {\n          end = i;\n          break;\n        }\n      } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            start = i + 1;\n            break;\n          }\n        } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            start = i + 1;\n            break;\n          }\n        } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1)\n          startDot = i;\n        else if (preDotState !== 1)\n          preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46) {\n        // If this is our first dot, mark it as the start of our extension\n        if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "module.exports = {\n  platform: \"linux\",\n  cwd() {\n    return \".\";\n  },\n  umask() {\n    return 0;\n  },\n  hrtime,\n  argv: [],\n  exit(code = 0) {\n    throw Error(`exit ${code}`);\n  }\n};\n\n// https://github.com/kumavis/browser-process-hrtime v1.0.0\n//\n// Copyright 2014 kumavis\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//\n// 1. Redistributions of source code must retain the above copyright notice,\n// this list of conditions and the following disclaimer.\n//\n// 2. Redistributions in binary form must reproduce the above copyright notice,\n// this list of conditions and the following disclaimer in the documentation\n// and/or other materials provided with the distribution.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n// POSSIBILITY OF SUCH DAMAGE.\n\nvar performance = global.performance || {};\nvar performanceNow =\n  performance.now        ||\n  performance.mozNow     ||\n  performance.msNow      ||\n  performance.oNow       ||\n  performance.webkitNow  ||\n  function(){ return (new Date()).getTime(); };\n\nfunction hrtime(previousTimestamp) {\n  var clocktime = performanceNow.call(performance);\n  var seconds = Math.floor(clocktime * 1e-3);\n  var nanoseconds = Math.floor(clocktime * 1e6 - seconds * 1e9);\n  if (previousTimestamp) {\n    seconds -= previousTimestamp[0];\n    nanoseconds -= previousTimestamp[1];\n    if (nanoseconds < 0) {\n      seconds--;\n      nanoseconds += 1e9;\n    }\n  }\n  return [ seconds, nanoseconds ];\n}\n", "/**\n * @fileoverview Terminal colors utility.\n * @license Apache-2.0\n */\n\nvar proc = typeof process !== \"undefined\" && process || {};\nvar isCI = proc.env && \"CI\" in proc.env; // doesn't work when bundled because 'process' is a mock\n\nfunction from(stream, base) {\n  var colors = base || {};\n  colors.supported = (stream && !!stream.isTTY) || isCI;\n  colors.gray = text => colors.supported ? exports.GRAY + text + exports.RESET : text;\n  colors.red = text => colors.supported ? exports.RED + text + exports.RESET : text;\n  colors.green = text => colors.supported ? exports.GREEN + text + exports.RESET : text;\n  colors.yellow = text => colors.supported ? exports.YELLOW + text + exports.RESET : text;\n  colors.blue = text => colors.supported ? exports.BLUE + text + exports.RESET : text;\n  colors.magenta = text => colors.supported ? exports.MAGENTA + text + exports.RESET : text;\n  colors.cyan = text => colors.supported ? exports.CYAN + text + exports.RESET : text;\n  colors.white = text => colors.supported ? exports.WHITE + text + exports.RESET : text;\n  return colors;\n}\n\nexports.stdout = from(proc.stdout, exports);\nexports.stderr = from(proc.stderr);\nexports.from = from;\n\nexports.GRAY = \"\\u001b[90m\";\nexports.RED = \"\\u001b[91m\";\nexports.GREEN = \"\\u001b[92m\";\nexports.YELLOW = \"\\u001b[93m\";\nexports.BLUE = \"\\u001b[94m\";\nexports.MAGENTA = \"\\u001b[95m\";\nexports.CYAN = \"\\u001b[96m\";\nexports.WHITE = \"\\u001b[97m\";\nexports.RESET = \"\\u001b[0m\";\n", "/**\n * @fileoverview File finding utility.\n * @license Apache-2.0\n */\n\nconst fs = require(\"fs\");\nconst path = require(\"path\");\n\nfunction findFiles(dirname, filter) {\n  var out = [];\n  fs.readdirSync(dirname).forEach(name => {\n    if (fs.statSync(path.join(dirname, name)).isDirectory()) {\n      findFiles(path.join(dirname, name), filter).forEach(iname => out.push(name + \"/\" + iname));\n    } else if (!filter || typeof filter === \"function\" ? filter(name) : filter.test(name)) {\n      out.push(name);\n    }\n  });\n  return out;\n}\n\nexports.files = findFiles;\n", "/**\n * @fileoverview Recursive mkdir.\n * @license\n * Copyright 2010 <PERSON> (<EMAIL>)\n *\n * This project is free software released under the MIT/X11 license:\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nconst path = require(\"path\");\nconst fs = require(\"fs\");\nconst process = require(\"process\"); // ensure shim\n\nmodule.exports = function mkdirp(p, opts, made) {\n  if (!opts || typeof opts !== \"object\") {\n    opts = { mode: opts };\n  }\n  var mode = opts.mode;\n  if (mode === undefined) {\n    mode = 0o777 & (~process.umask());\n  }\n  if (!made) made = null;\n  p = path.resolve(p);\n  try {\n    fs.mkdirSync(p, mode);\n    made = made || p;\n  } catch (err0) {\n    switch (err0.code) {\n      case \"ENOENT\":\n        made = mkdirp(path.dirname(p), opts, made);\n        mkdirp(p, opts, made);\n        break;\n      default:\n        var stat;\n        try {\n          stat = fs.statSync(p);\n        } catch (err1) {\n          throw err0;\n        }\n        if (!stat.isDirectory()) throw err0;\n        break;\n    }\n  }\n  return made;\n};\n", "/**\n * @fileoverview Command line options utility.\n * @license Apache-2.0\n */\n\nconst path = require(\"path\");\nconst colorsUtil = require(\"./colors\");\n\n// type | meaning\n// -----|---------------\n// b    | boolean\n// i    | integer\n// f    | float\n// s    | string\n// I    | integer array\n// F    | float array\n// S    | string array\n\n/** Parses the specified command line arguments according to the given configuration. */\nfunction parse(argv, config, propagateDefaults = true) {\n  var options = {};\n  var unknown = [];\n  var args = [];\n  var trailing = [];\n\n  // make an alias map and initialize defaults\n  var aliases = {};\n  Object.keys(config).forEach(key => {\n    if (key.startsWith(\" \")) return;\n    var option = config[key];\n    if (option.alias != null) {\n      if (typeof option.alias === \"string\") aliases[option.alias] = key;\n      else if (Array.isArray(option.alias)) option.alias.forEach(alias => aliases[alias] = key);\n    }\n    if (propagateDefaults && option.default != null) options[key] = option.default;\n  });\n\n  // iterate over argv\n  for (var i = 0, k = (argv = argv.slice()).length; i < k; ++i) {\n    let arg = argv[i];\n    if (arg == \"--\") { ++i; break; }\n    let match = /^(?:(-\\w)(?:=(.*))?|(--\\w{2,})(?:=(.*))?)$/.exec(arg), option, key;\n    if (match) {\n      if (config[arg]) option = config[key = arg]; // exact\n      else if (match[1] != null) { // alias\n        option = config[key = aliases[match[1].substring(1)]];\n        if (option && match[2] != null) argv[i--] = match[2];\n      } else if (match[3] != null) { // full\n        option = config[key = match[3].substring(2)];\n        if (option && match[4] != null) argv[i--] = match[4];\n      }\n    } else {\n      if (arg.charCodeAt(0) == 45) option = config[key = arg]; // exact\n      else { args.push(arg); continue; } // argument\n    }\n    if (option) {\n      if (option.value) {\n        // alias setting fixed values\n        Object.keys(option.value).forEach(k => options[k] = option.value[k]);\n      } else if (option.type == null || option.type === \"b\") {\n        // boolean flag not taking a value\n        options[key] = true;\n      } else {\n        if (i + 1 < argv.length && argv[i + 1].charCodeAt(0) != 45) {\n          // non-boolean with given value\n          switch (option.type) {\n            case \"i\": options[key] = parseInt(argv[++i], 10); break;\n            case \"I\": options[key] = (options[key] || []).concat(parseInt(argv[++i], 10)); break;\n            case \"f\": options[key] = parseFloat(argv[++i]); break;\n            case \"F\": options[key] = (options[key] || []).concat(parseFloat(argv[++i])); break;\n            case \"s\": options[key] = String(argv[++i]); break;\n            case \"S\": options[key] = (options[key] || []).concat(argv[++i].split(\",\")); break;\n            default: unknown.push(arg); --i;\n          }\n        } else {\n          // non-boolean with omitted value\n          switch (option.type) {\n            case \"i\":\n            case \"f\": options[key] = option.default || 0; break;\n            case \"s\": options[key] = option.default || \"\"; break;\n            case \"I\":\n            case \"F\":\n            case \"S\": options[key] = option.default || []; break;\n            default: unknown.push(arg);\n          }\n        }\n      }\n    } else unknown.push(arg);\n  }\n  while (i < k) trailing.push(argv[i++]); // trailing\n  if (propagateDefaults) addDefaults(config, options);\n\n  return { options, unknown, arguments: args, trailing };\n}\n\nexports.parse = parse;\n\n/** Generates the help text for the specified configuration. */\nfunction help(config, options) {\n  if (!options) options = {};\n  var indent = options.indent || 2;\n  var padding = options.padding || 24;\n  var eol = options.eol || \"\\n\";\n  var sbCategories = {};\n  var sbOther = [];\n  Object.keys(config).forEach(key => {\n    var option = config[key];\n    if (option.description == null) return;\n    var text = \"\";\n    while (text.length < indent) text += \" \";\n    text += \"--\" + key;\n    if (option.alias) text += \", -\" + option.alias;\n    while (text.length < padding) text += \" \";\n    var sb;\n    if (!options.noCategories && option.category) {\n      if (!(sb = sbCategories[option.category])) {\n        sbCategories[option.category] = sb = [];\n      }\n    } else {\n      sb = sbOther;\n    }\n    if (Array.isArray(option.description)) {\n      sb.push(text + option.description[0] + option.description.slice(1).map(line => {\n        for (let i = 0; i < padding; ++i) line = \" \" + line;\n        return eol + line;\n      }).join(\"\"));\n    } else sb.push(text + option.description);\n  });\n  var sb = [];\n  var hasCategories = false;\n  Object.keys(sbCategories).forEach(category => {\n    hasCategories = true;\n    sb.push(eol + \" \" + colorsUtil.gray(category) + eol);\n    sb.push(sbCategories[category].join(eol));\n  });\n  if (hasCategories) {\n    sb.push(eol + \" \" + colorsUtil.gray(\"Other\") + eol);\n  }\n  sb.push(sbOther.join(eol));\n  return sb.join(eol);\n}\n\nexports.help = help;\n\n/** Sanitizes an option value to be a valid value of the option's type. */\nfunction sanitizeValue(value, type) {\n  if (value != null) {\n    switch (type) {\n      case undefined:\n      case \"b\": return Boolean(value);\n      case \"i\": return Math.trunc(value) || 0;\n      case \"f\": return Number(value) || 0;\n      case \"s\": {\n        if (value === true) return \"\";\n        return String(value);\n      }\n      case \"I\": {\n        if (!Array.isArray(value)) value = [ value ];\n        return value.map(v => Math.trunc(v) || 0);\n      }\n      case \"F\": {\n        if (!Array.isArray(value)) value = [ value ];\n        return value.map(v => Number(v) || 0);\n      }\n      case \"S\": {\n        if (!Array.isArray(value)) value = [ value ];\n        return value.map(String);\n      }\n    }\n  }\n  return undefined;\n}\n\n/** Merges two sets of options into one, preferring the current over the parent set. */\nfunction merge(config, currentOptions, parentOptions, parentBaseDir) {\n  const mergedOptions = {};\n  for (const [key, { type, mutuallyExclusive, isPath, useNodeResolution, cliOnly }] of Object.entries(config)) {\n    let currentValue = sanitizeValue(currentOptions[key], type);\n    let parentValue = sanitizeValue(parentOptions[key], type);\n    if (currentValue == null) {\n      if (parentValue != null) {\n        // only parent value present\n        if (cliOnly) continue;\n        if (Array.isArray(parentValue)) {\n          let exclude;\n          if (isPath) {\n            parentValue = parentValue.map(value => resolvePath(value, parentBaseDir, useNodeResolution));\n          }\n          if (mutuallyExclusive != null && (exclude = currentOptions[mutuallyExclusive])) {\n            mergedOptions[key] = parentValue.filter(value => !exclude.includes(value));\n          } else {\n            mergedOptions[key] = parentValue.slice();\n          }\n        } else {\n          if (isPath) {\n            parentValue = resolvePath(parentValue, parentBaseDir, useNodeResolution);\n          }\n          mergedOptions[key] = parentValue;\n        }\n      }\n    } else if (parentValue == null) {\n      // only current value present\n      if (Array.isArray(currentValue)) {\n        mergedOptions[key] = currentValue.slice();\n      } else {\n        mergedOptions[key] = currentValue;\n      }\n    } else {\n      // both current and parent values present\n      if (Array.isArray(currentValue)) {\n        if (cliOnly) {\n          mergedOptions[key] = currentValue.slice();\n          continue;\n        }\n        let exclude;\n        if (isPath) {\n          parentValue = parentValue.map(value => resolvePath(value, parentBaseDir, useNodeResolution));\n        }\n        if (mutuallyExclusive != null && (exclude = currentOptions[mutuallyExclusive])) {\n          mergedOptions[key] = [\n            ...currentValue,\n            ...parentValue.filter(value => !currentValue.includes(value) && !exclude.includes(value))\n          ];\n        } else {\n          mergedOptions[key] = [\n            ...currentValue,\n            ...parentValue.filter(value => !currentValue.includes(value)) // dedup\n          ];\n        }\n      } else {\n        mergedOptions[key] = currentValue;\n      }\n    }\n  }\n  return mergedOptions;\n}\n\nexports.merge = merge;\n\nconst dynrequire = typeof __webpack_require__ === \"function\"\n  ? __non_webpack_require__\n  : require;\n\n/** Resolves a single possibly relative path. Keeps absolute paths, otherwise prepends baseDir. */\nfunction resolvePath(p, baseDir, useNodeResolution = false) {\n  if (path.isAbsolute(p)) return p;\n  if (useNodeResolution && !p.startsWith(\".\")) {\n    return dynrequire.resolve(p, { paths: [ baseDir ] });\n  }\n  return path.join(baseDir, p);\n}\n\nexports.resolvePath = resolvePath;\n\n/** Populates default values on a parsed options result. */\nfunction addDefaults(config, options) {\n  for (const [key, { default: defaultValue }] of Object.entries(config)) {\n    if (options[key] == null && defaultValue != null) {\n      options[key] = defaultValue;\n    }\n  }\n}\n\nexports.addDefaults = addDefaults;\n", "/**\n * @fileoverview UTF8 utility.\n * @license Apache-2.0\n */\n\n// @protobufjs/utf8\n\n/**\n * A minimal UTF8 implementation for number arrays.\n * @memberof util\n * @namespace\n */\nvar utf8 = exports;\n\n/**\n * Calculates the UTF8 byte length of a string.\n * @param {string} string String\n * @returns {number} Byte length\n */\nutf8.length = function utf8_length(string) {\n  var len = 0,\n      c = 0;\n  for (var i = 0, l = string.length; i < l; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128)\n      len += 1;\n    else if (c < 2048)\n      len += 2;\n    else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\n      ++i;\n      len += 4;\n    } else\n      len += 3;\n  }\n  return len;\n};\n\n/**\n * Reads UTF8 bytes as a string.\n * @param {Uint8Array} buffer Source buffer\n * @param {number} start Source start\n * @param {number} end Source end\n * @returns {string} String read\n */\nutf8.read = function utf8_read(buffer, start, end) {\n  var len = end - start;\n  if (len < 1)\n    return \"\";\n  var parts = null,\n      chunk = [],\n      i = 0, // char offset\n      t;     // temporary\n  while (start < end) {\n    t = buffer[start++];\n    if (t < 128)\n      chunk[i++] = t;\n    else if (t > 191 && t < 224)\n      chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\n    else if (t > 239 && t < 365) {\n      t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\n      chunk[i++] = 0xD800 + (t >> 10);\n      chunk[i++] = 0xDC00 + (t & 1023);\n    } else\n      chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\n    if (i > 8191) {\n      (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\n      i = 0;\n    }\n  }\n  if (parts) {\n    if (i)\n      parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\n    return parts.join(\"\");\n  }\n  return String.fromCharCode.apply(String, chunk.slice(0, i));\n};\n\n/**\n * Writes a string as UTF8 bytes.\n * @param {string} string Source string\n * @param {Uint8Array} buffer Destination buffer\n * @param {number} offset Destination offset\n * @returns {number} Bytes written\n */\nutf8.write = function utf8_write(string, buffer, offset) {\n  var start = offset,\n      c1, // character 1\n      c2; // character 2\n  for (var i = 0; i < string.length; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = c1 >> 6 | 192;\n      buffer[offset++] = c1 & 63 | 128;\n    } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\n      c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\n      ++i;\n      buffer[offset++] = c1 >> 18 | 240;\n      buffer[offset++] = c1 >> 12 & 63 | 128;\n      buffer[offset++] = c1 >> 6 & 63 | 128;\n      buffer[offset++] = c1 & 63 | 128;\n    } else {\n      buffer[offset++] = c1 >> 12 | 224;\n      buffer[offset++] = c1 >> 6 & 63 | 128;\n      buffer[offset++] = c1 & 63 | 128;\n    }\n  }\n  return offset - start;\n};\n", "// GENERATED FILE. DO NOT EDIT.\nvar loader = (function(exports) {\n  \"use strict\";\n  \n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.instantiate = instantiate;\n  exports.instantiateSync = instantiateSync;\n  exports.instantiateStreaming = instantiateStreaming;\n  exports.demangle = demangle;\n  exports.default = void 0;\n  // Runtime header offsets\n  const ID_OFFSET = -8;\n  const SIZE_OFFSET = -4; // Runtime ids\n  \n  const ARRAYBUFFER_ID = 0;\n  const STRING_ID = 1; // const ARRAYBUFFERVIEW_ID = 2;\n  // Runtime type information\n  \n  const ARRAYBUFFERVIEW = 1 << 0;\n  const ARRAY = 1 << 1;\n  const STATICARRAY = 1 << 2; // const SET = 1 << 3;\n  // const MAP = 1 << 4;\n  \n  const VAL_ALIGN_OFFSET = 6; // const VAL_ALIGN = 1 << VAL_ALIGN_OFFSET;\n  \n  const VAL_SIGNED = 1 << 11;\n  const VAL_FLOAT = 1 << 12; // const VAL_NULLABLE = 1 << 13;\n  \n  const VAL_MANAGED = 1 << 14; // const KEY_ALIGN_OFFSET = 15;\n  // const KEY_ALIGN = 1 << KEY_ALIGN_OFFSET;\n  // const KEY_SIGNED = 1 << 20;\n  // const KEY_FLOAT = 1 << 21;\n  // const KEY_NULLABLE = 1 << 22;\n  // const KEY_MANAGED = 1 << 23;\n  // Array(BufferView) layout\n  \n  const ARRAYBUFFERVIEW_BUFFER_OFFSET = 0;\n  const ARRAYBUFFERVIEW_DATASTART_OFFSET = 4;\n  const ARRAYBUFFERVIEW_DATALENGTH_OFFSET = 8;\n  const ARRAYBUFFERVIEW_SIZE = 12;\n  const ARRAY_LENGTH_OFFSET = 12;\n  const ARRAY_SIZE = 16;\n  const BIGINT = typeof BigUint64Array !== \"undefined\";\n  const THIS = Symbol();\n  const STRING_SMALLSIZE = 192; // break-even point in V8\n  \n  const STRING_CHUNKSIZE = 1024; // mitigate stack overflow\n  \n  const utf16 = new TextDecoder(\"utf-16le\", {\n    fatal: true\n  }); // != wtf16\n  \n  /** Gets a string from memory. */\n  \n  function getStringImpl(buffer, ptr) {\n    let len = new Uint32Array(buffer)[ptr + SIZE_OFFSET >>> 2] >>> 1;\n    const wtf16 = new Uint16Array(buffer, ptr, len);\n    if (len <= STRING_SMALLSIZE) return String.fromCharCode(...wtf16);\n  \n    try {\n      return utf16.decode(wtf16);\n    } catch {\n      let str = \"\",\n          off = 0;\n  \n      while (len - off > STRING_CHUNKSIZE) {\n        str += String.fromCharCode(...wtf16.subarray(off, off += STRING_CHUNKSIZE));\n      }\n  \n      return str + String.fromCharCode(...wtf16.subarray(off));\n    }\n  }\n  /** Prepares the base module prior to instantiation. */\n  \n  \n  function preInstantiate(imports) {\n    const extendedExports = {};\n  \n    function getString(memory, ptr) {\n      if (!memory) return \"<yet unknown>\";\n      return getStringImpl(memory.buffer, ptr);\n    } // add common imports used by stdlib for convenience\n  \n  \n    const env = imports.env = imports.env || {};\n  \n    env.abort = env.abort || function abort(msg, file, line, colm) {\n      const memory = extendedExports.memory || env.memory; // prefer exported, otherwise try imported\n  \n      throw Error(`abort: ${getString(memory, msg)} at ${getString(memory, file)}:${line}:${colm}`);\n    };\n  \n    env.trace = env.trace || function trace(msg, n, ...args) {\n      const memory = extendedExports.memory || env.memory;\n      console.log(`trace: ${getString(memory, msg)}${n ? \" \" : \"\"}${args.slice(0, n).join(\", \")}`);\n    };\n  \n    env.seed = env.seed || Date.now;\n    imports.Math = imports.Math || Math;\n    imports.Date = imports.Date || Date;\n    return extendedExports;\n  }\n  \n  const E_NOEXPORTRUNTIME = \"Operation requires compiling with --exportRuntime\";\n  \n  const F_NOEXPORTRUNTIME = function () {\n    throw Error(E_NOEXPORTRUNTIME);\n  };\n  /** Prepares the final module once instantiation is complete. */\n  \n  \n  function postInstantiate(extendedExports, instance) {\n    const exports = instance.exports;\n    const memory = exports.memory;\n    const table = exports.table;\n  \n    const __new = exports.__new || F_NOEXPORTRUNTIME;\n  \n    const __pin = exports.__pin || F_NOEXPORTRUNTIME;\n  \n    const __unpin = exports.__unpin || F_NOEXPORTRUNTIME;\n  \n    const __collect = exports.__collect || F_NOEXPORTRUNTIME;\n  \n    const __rtti_base = exports.__rtti_base;\n    const getRttiCount = __rtti_base ? function (arr) {\n      return arr[__rtti_base >>> 2];\n    } : F_NOEXPORTRUNTIME;\n    extendedExports.__new = __new;\n    extendedExports.__pin = __pin;\n    extendedExports.__unpin = __unpin;\n    extendedExports.__collect = __collect;\n    /** Gets the runtime type info for the given id. */\n  \n    function getInfo(id) {\n      const U32 = new Uint32Array(memory.buffer);\n      const count = getRttiCount(U32);\n      if ((id >>>= 0) >= count) throw Error(`invalid id: ${id}`);\n      return U32[(__rtti_base + 4 >>> 2) + id * 2];\n    }\n    /** Gets and validate runtime type info for the given id for array like objects */\n  \n  \n    function getArrayInfo(id) {\n      const info = getInfo(id);\n      if (!(info & (ARRAYBUFFERVIEW | ARRAY | STATICARRAY))) throw Error(`not an array: ${id}, flags=${info}`);\n      return info;\n    }\n    /** Gets the runtime base id for the given id. */\n  \n  \n    function getBase(id) {\n      const U32 = new Uint32Array(memory.buffer);\n      const count = getRttiCount(U32);\n      if ((id >>>= 0) >= count) throw Error(`invalid id: ${id}`);\n      return U32[(__rtti_base + 4 >>> 2) + id * 2 + 1];\n    }\n    /** Gets the runtime alignment of a collection's values. */\n  \n  \n    function getValueAlign(info) {\n      return 31 - Math.clz32(info >>> VAL_ALIGN_OFFSET & 31); // -1 if none\n    }\n    /** Gets the runtime alignment of a collection's keys. */\n    // function getKeyAlign(info) {\n    //   return 31 - Math.clz32((info >>> KEY_ALIGN_OFFSET) & 31); // -1 if none\n    // }\n  \n    /** Allocates a new string in the module's memory and returns its pointer. */\n  \n  \n    function __newString(str) {\n      if (str == null) return 0;\n      const length = str.length;\n  \n      const ptr = __new(length << 1, STRING_ID);\n  \n      const U16 = new Uint16Array(memory.buffer);\n  \n      for (var i = 0, p = ptr >>> 1; i < length; ++i) U16[p + i] = str.charCodeAt(i);\n  \n      return ptr;\n    }\n  \n    extendedExports.__newString = __newString;\n    /** Reads a string from the module's memory by its pointer. */\n  \n    function __getString(ptr) {\n      if (!ptr) return null;\n      const buffer = memory.buffer;\n      const id = new Uint32Array(buffer)[ptr + ID_OFFSET >>> 2];\n      if (id !== STRING_ID) throw Error(`not a string: ${ptr}`);\n      return getStringImpl(buffer, ptr);\n    }\n  \n    extendedExports.__getString = __getString;\n    /** Gets the view matching the specified alignment, signedness and floatness. */\n  \n    function getView(alignLog2, signed, float) {\n      const buffer = memory.buffer;\n  \n      if (float) {\n        switch (alignLog2) {\n          case 2:\n            return new Float32Array(buffer);\n  \n          case 3:\n            return new Float64Array(buffer);\n        }\n      } else {\n        switch (alignLog2) {\n          case 0:\n            return new (signed ? Int8Array : Uint8Array)(buffer);\n  \n          case 1:\n            return new (signed ? Int16Array : Uint16Array)(buffer);\n  \n          case 2:\n            return new (signed ? Int32Array : Uint32Array)(buffer);\n  \n          case 3:\n            return new (signed ? BigInt64Array : BigUint64Array)(buffer);\n        }\n      }\n  \n      throw Error(`unsupported align: ${alignLog2}`);\n    }\n    /** Allocates a new array in the module's memory and returns its pointer. */\n  \n  \n    function __newArray(id, values) {\n      const info = getArrayInfo(id);\n      const align = getValueAlign(info);\n      const length = values.length;\n  \n      const buf = __new(length << align, info & STATICARRAY ? id : ARRAYBUFFER_ID);\n  \n      let result;\n  \n      if (info & STATICARRAY) {\n        result = buf;\n      } else {\n        __pin(buf);\n  \n        const arr = __new(info & ARRAY ? ARRAY_SIZE : ARRAYBUFFERVIEW_SIZE, id);\n  \n        __unpin(buf);\n  \n        const U32 = new Uint32Array(memory.buffer);\n        U32[arr + ARRAYBUFFERVIEW_BUFFER_OFFSET >>> 2] = buf;\n        U32[arr + ARRAYBUFFERVIEW_DATASTART_OFFSET >>> 2] = buf;\n        U32[arr + ARRAYBUFFERVIEW_DATALENGTH_OFFSET >>> 2] = length << align;\n        if (info & ARRAY) U32[arr + ARRAY_LENGTH_OFFSET >>> 2] = length;\n        result = arr;\n      }\n  \n      const view = getView(align, info & VAL_SIGNED, info & VAL_FLOAT);\n  \n      if (info & VAL_MANAGED) {\n        for (let i = 0; i < length; ++i) {\n          const value = values[i];\n          view[(buf >>> align) + i] = value;\n        }\n      } else {\n        view.set(values, buf >>> align);\n      }\n  \n      return result;\n    }\n  \n    extendedExports.__newArray = __newArray;\n    /** Gets a live view on an array's values in the module's memory. Infers the array type from RTTI. */\n  \n    function __getArrayView(arr) {\n      const U32 = new Uint32Array(memory.buffer);\n      const id = U32[arr + ID_OFFSET >>> 2];\n      const info = getArrayInfo(id);\n      const align = getValueAlign(info);\n      let buf = info & STATICARRAY ? arr : U32[arr + ARRAYBUFFERVIEW_DATASTART_OFFSET >>> 2];\n      const length = info & ARRAY ? U32[arr + ARRAY_LENGTH_OFFSET >>> 2] : U32[buf + SIZE_OFFSET >>> 2] >>> align;\n      return getView(align, info & VAL_SIGNED, info & VAL_FLOAT).subarray(buf >>>= align, buf + length);\n    }\n  \n    extendedExports.__getArrayView = __getArrayView;\n    /** Copies an array's values from the module's memory. Infers the array type from RTTI. */\n  \n    function __getArray(arr) {\n      const input = __getArrayView(arr);\n  \n      const len = input.length;\n      const out = new Array(len);\n  \n      for (let i = 0; i < len; i++) out[i] = input[i];\n  \n      return out;\n    }\n  \n    extendedExports.__getArray = __getArray;\n    /** Copies an ArrayBuffer's value from the module's memory. */\n  \n    function __getArrayBuffer(ptr) {\n      const buffer = memory.buffer;\n      const length = new Uint32Array(buffer)[ptr + SIZE_OFFSET >>> 2];\n      return buffer.slice(ptr, ptr + length);\n    }\n  \n    extendedExports.__getArrayBuffer = __getArrayBuffer;\n    /** Copies a typed array's values from the module's memory. */\n  \n    function getTypedArray(Type, alignLog2, ptr) {\n      return new Type(getTypedArrayView(Type, alignLog2, ptr));\n    }\n    /** Gets a live view on a typed array's values in the module's memory. */\n  \n  \n    function getTypedArrayView(Type, alignLog2, ptr) {\n      const buffer = memory.buffer;\n      const U32 = new Uint32Array(buffer);\n      const bufPtr = U32[ptr + ARRAYBUFFERVIEW_DATASTART_OFFSET >>> 2];\n      return new Type(buffer, bufPtr, U32[bufPtr + SIZE_OFFSET >>> 2] >>> alignLog2);\n    }\n    /** Attach a set of get TypedArray and View functions to the exports. */\n  \n  \n    function attachTypedArrayFunctions(ctor, name, align) {\n      extendedExports[`__get${name}`] = getTypedArray.bind(null, ctor, align);\n      extendedExports[`__get${name}View`] = getTypedArrayView.bind(null, ctor, align);\n    }\n  \n    [Int8Array, Uint8Array, Uint8ClampedArray, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array].forEach(ctor => {\n      attachTypedArrayFunctions(ctor, ctor.name, 31 - Math.clz32(ctor.BYTES_PER_ELEMENT));\n    });\n  \n    if (BIGINT) {\n      [BigUint64Array, BigInt64Array].forEach(ctor => {\n        attachTypedArrayFunctions(ctor, ctor.name.slice(3), 3);\n      });\n    }\n    /** Tests whether an object is an instance of the class represented by the specified base id. */\n  \n  \n    function __instanceof(ptr, baseId) {\n      const U32 = new Uint32Array(memory.buffer);\n      let id = U32[ptr + ID_OFFSET >>> 2];\n  \n      if (id <= getRttiCount(U32)) {\n        do {\n          if (id == baseId) return true;\n          id = getBase(id);\n        } while (id);\n      }\n  \n      return false;\n    }\n  \n    extendedExports.__instanceof = __instanceof; // Pull basic exports to extendedExports so code in preInstantiate can use them\n  \n    extendedExports.memory = extendedExports.memory || memory;\n    extendedExports.table = extendedExports.table || table; // Demangle exports and provide the usual utility on the prototype\n  \n    return demangle(exports, extendedExports);\n  }\n  \n  function isResponse(src) {\n    return typeof Response !== \"undefined\" && src instanceof Response;\n  }\n  \n  function isModule(src) {\n    return src instanceof WebAssembly.Module;\n  }\n  /** Asynchronously instantiates an AssemblyScript module from anything that can be instantiated. */\n  \n  \n  async function instantiate(source, imports = {}) {\n    if (isResponse(source = await source)) return instantiateStreaming(source, imports);\n    const module = isModule(source) ? source : await WebAssembly.compile(source);\n    const extended = preInstantiate(imports);\n    const instance = await WebAssembly.instantiate(module, imports);\n    const exports = postInstantiate(extended, instance);\n    return {\n      module,\n      instance,\n      exports\n    };\n  }\n  /** Synchronously instantiates an AssemblyScript module from a WebAssembly.Module or binary buffer. */\n  \n  \n  function instantiateSync(source, imports = {}) {\n    const module = isModule(source) ? source : new WebAssembly.Module(source);\n    const extended = preInstantiate(imports);\n    const instance = new WebAssembly.Instance(module, imports);\n    const exports = postInstantiate(extended, instance);\n    return {\n      module,\n      instance,\n      exports\n    };\n  }\n  /** Asynchronously instantiates an AssemblyScript module from a response, i.e. as obtained by `fetch`. */\n  \n  \n  async function instantiateStreaming(source, imports = {}) {\n    if (!WebAssembly.instantiateStreaming) {\n      return instantiate(isResponse(source = await source) ? source.arrayBuffer() : source, imports);\n    }\n  \n    const extended = preInstantiate(imports);\n    const result = await WebAssembly.instantiateStreaming(source, imports);\n    const exports = postInstantiate(extended, result.instance);\n    return { ...result,\n      exports\n    };\n  }\n  /** Demangles an AssemblyScript module's exports to a friendly object structure. */\n  \n  \n  function demangle(exports, extendedExports = {}) {\n    const setArgumentsLength = exports[\"__argumentsLength\"] ? length => {\n      exports[\"__argumentsLength\"].value = length;\n    } : exports[\"__setArgumentsLength\"] || exports[\"__setargc\"] || (() => {\n      /* nop */\n    });\n  \n    for (let internalName in exports) {\n      if (!Object.prototype.hasOwnProperty.call(exports, internalName)) continue;\n      const elem = exports[internalName];\n      let parts = internalName.split(\".\");\n      let curr = extendedExports;\n  \n      while (parts.length > 1) {\n        let part = parts.shift();\n        if (!Object.prototype.hasOwnProperty.call(curr, part)) curr[part] = {};\n        curr = curr[part];\n      }\n  \n      let name = parts[0];\n      let hash = name.indexOf(\"#\");\n  \n      if (hash >= 0) {\n        const className = name.substring(0, hash);\n        const classElem = curr[className];\n  \n        if (typeof classElem === \"undefined\" || !classElem.prototype) {\n          const ctor = function (...args) {\n            return ctor.wrap(ctor.prototype.constructor(0, ...args));\n          };\n  \n          ctor.prototype = {\n            valueOf() {\n              return this[THIS];\n            }\n  \n          };\n  \n          ctor.wrap = function (thisValue) {\n            return Object.create(ctor.prototype, {\n              [THIS]: {\n                value: thisValue,\n                writable: false\n              }\n            });\n          };\n  \n          if (classElem) Object.getOwnPropertyNames(classElem).forEach(name => Object.defineProperty(ctor, name, Object.getOwnPropertyDescriptor(classElem, name)));\n          curr[className] = ctor;\n        }\n  \n        name = name.substring(hash + 1);\n        curr = curr[className].prototype;\n  \n        if (/^(get|set):/.test(name)) {\n          if (!Object.prototype.hasOwnProperty.call(curr, name = name.substring(4))) {\n            let getter = exports[internalName.replace(\"set:\", \"get:\")];\n            let setter = exports[internalName.replace(\"get:\", \"set:\")];\n            Object.defineProperty(curr, name, {\n              get() {\n                return getter(this[THIS]);\n              },\n  \n              set(value) {\n                setter(this[THIS], value);\n              },\n  \n              enumerable: true\n            });\n          }\n        } else {\n          if (name === 'constructor') {\n            (curr[name] = (...args) => {\n              setArgumentsLength(args.length);\n              return elem(...args);\n            }).original = elem;\n          } else {\n            // instance method\n            (curr[name] = function (...args) {\n              // !\n              setArgumentsLength(args.length);\n              return elem(this[THIS], ...args);\n            }).original = elem;\n          }\n        }\n      } else {\n        if (/^(get|set):/.test(name)) {\n          if (!Object.prototype.hasOwnProperty.call(curr, name = name.substring(4))) {\n            Object.defineProperty(curr, name, {\n              get: exports[internalName.replace(\"set:\", \"get:\")],\n              set: exports[internalName.replace(\"get:\", \"set:\")],\n              enumerable: true\n            });\n          }\n        } else if (typeof elem === \"function\" && elem !== setArgumentsLength) {\n          (curr[name] = (...args) => {\n            setArgumentsLength(args.length);\n            return elem(...args);\n          }).original = elem;\n        } else {\n          curr[name] = elem;\n        }\n      }\n    }\n  \n    return extendedExports;\n  }\n  \n  var _default = {\n    instantiate,\n    instantiateSync,\n    instantiateStreaming,\n    demangle\n  };\n  exports.default = _default;\n  return exports;\n})({});\nif (typeof define === 'function' && define.amd) define([], function() { return loader; });\nelse if (typeof module === 'object' && typeof exports==='object') module.exports = loader;\n", "// GENERATED FILE. DO NOT EDIT.\nvar rtrace = (function(exports) {\n  \"use strict\";\n  \n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.Rtrace = exports.TOTAL_OVERHEAD = exports.OBJECT_OVERHEAD = exports.BLOCK_OVERHEAD = void 0;\n  // WebAssembly pages are 65536 kb\n  const PAGE_SIZE_BITS = 16;\n  const PAGE_SIZE = 1 << PAGE_SIZE_BITS;\n  const PAGE_MASK = PAGE_SIZE - 1; // Wasm32 pointer size is 4 bytes\n  \n  const PTR_SIZE_BITS = 2;\n  const PTR_SIZE = 1 << PTR_SIZE_BITS;\n  const PTR_MASK = PTR_SIZE - 1;\n  const PTR_VIEW = Uint32Array;\n  const BLOCK_OVERHEAD = PTR_SIZE;\n  exports.BLOCK_OVERHEAD = BLOCK_OVERHEAD;\n  const OBJECT_OVERHEAD = 16;\n  exports.OBJECT_OVERHEAD = OBJECT_OVERHEAD;\n  const TOTAL_OVERHEAD = BLOCK_OVERHEAD + OBJECT_OVERHEAD;\n  exports.TOTAL_OVERHEAD = TOTAL_OVERHEAD;\n  \n  function assert(x) {\n    if (!x) throw Error(\"assertion failed\");\n    return x;\n  }\n  \n  Error.stackTraceLimit = 15;\n  \n  function trimStacktrace(stack, levels) {\n    return stack.split(/\\r?\\n/).slice(1 + levels);\n  }\n  \n  const hrtime = typeof performance !== \"undefined\" && performance.now ? performance.now : typeof process !== \"undefined\" && process.hrtime ? () => {\n    let t = process.hrtime();\n    return t[0] * 1e3 + t[1] / 1e6;\n  } : Date.now;\n  const mmTagsToString = [\"\", \"FREE\", \"LEFTFREE\", \"FREE+LEFTFREE\"];\n  const gcColorToString = [\"BLACK/WHITE\", \"WHITE/BLACK\", \"GRAY\", \"INVALID\"];\n  \n  class Rtrace {\n    constructor(options) {\n      this.options = options || {};\n  \n      this.onerror = this.options.onerror || function () {\n        /* nop */\n      };\n  \n      this.oninfo = this.options.oninfo || function () {\n        /* nop */\n      };\n  \n      this.oncollect_ = this.options.oncollect || function () {\n        /* nop */\n      };\n  \n      this.memory = null;\n      this.shadow = null;\n      this.shadowStart = 0x100000000;\n      this.blocks = new Map();\n      this.allocSites = new Map();\n      this.freedBlocks = new Map();\n      this.gcProfileStart = 0;\n      this.gcProfile = [];\n      this.allocCount = 0;\n      this.resizeCount = 0;\n      this.moveCount = 0;\n      this.freeCount = 0;\n      this.heapBase = 0x100000000;\n    }\n  \n    install(imports) {\n      if (!imports) imports = {};\n      imports.rtrace = Object.assign(imports.rtrace || {}, {\n        oninit: this.oninit.bind(this),\n        onalloc: this.onalloc.bind(this),\n        onresize: this.onresize.bind(this),\n        onmove: this.onmove.bind(this),\n        onvisit: this.onvisit.bind(this),\n        onfree: this.onfree.bind(this),\n        oninterrupt: this.oninterrupt.bind(this),\n        onyield: this.onyield.bind(this),\n        oncollect: this.oncollect.bind(this),\n        onstore: this.onstore.bind(this),\n        onload: this.onload.bind(this)\n      });\n      return imports;\n    }\n    /** Synchronizes the shadow memory with the module's memory. */\n  \n  \n    syncShadow() {\n      if (!this.memory) {\n        this.memory = assert(this.options.getMemory());\n        this.shadow = new WebAssembly.Memory({\n          initial: (this.memory.buffer.byteLength + PAGE_MASK & ~PAGE_MASK) >>> PAGE_SIZE_BITS\n        });\n      } else {\n        var diff = this.memory.buffer.byteLength - this.shadow.buffer.byteLength;\n        if (diff > 0) this.shadow.grow(diff >>> 16);\n      }\n    }\n    /** Marks a block's presence in shadow memory. */\n  \n  \n    markShadow(info, oldSize = 0) {\n      assert(this.shadow && this.shadow.byteLength == this.memory.byteLength);\n      assert((info.size & PTR_MASK) == 0);\n  \n      if (info.ptr < this.shadowStart) {\n        this.shadowStart = info.ptr;\n      }\n  \n      var len = info.size >>> PTR_SIZE_BITS;\n      var view = new PTR_VIEW(this.shadow.buffer, info.ptr, len);\n      var errored = false;\n      var start = oldSize >>> PTR_SIZE_BITS;\n  \n      for (let i = 0; i < start; ++i) {\n        if (view[i] != info.ptr && !errored) {\n          this.onerror(Error(\"shadow region mismatch: \" + view[i] + \" != \" + info.ptr), info);\n          errored = true;\n        }\n      }\n  \n      errored = false;\n  \n      for (let i = start; i < len; ++i) {\n        if (view[i] != 0 && !errored) {\n          this.onerror(Error(\"shadow region already in use: \" + view[i] + \" != 0\"), info);\n          errored = true;\n        }\n  \n        view[i] = info.ptr;\n      }\n    }\n    /** Unmarks a block's presence in shadow memory. */\n  \n  \n    unmarkShadow(info, oldSize = info.size) {\n      assert(this.shadow && this.shadow.byteLength == this.memory.byteLength);\n      var len = oldSize >>> PTR_SIZE_BITS;\n      var view = new PTR_VIEW(this.shadow.buffer, info.ptr, len);\n      var errored = false;\n      var start = 0;\n  \n      if (oldSize != info.size) {\n        assert(oldSize > info.size);\n        start = info.size >>> PTR_SIZE_BITS;\n      }\n  \n      for (let i = 0; i < len; ++i) {\n        if (view[i] != info.ptr && !errored) {\n          this.onerror(Error(\"shadow region mismatch: \" + view[i] + \" != \" + info.ptr), info);\n          errored = true;\n        }\n  \n        if (i >= start) view[i] = 0;\n      }\n    }\n    /** Performs an access to shadow memory. */\n  \n  \n    accessShadow(ptr, size, isLoad, isRT) {\n      this.syncShadow();\n      if (ptr < this.shadowStart) return;\n      var value = new Uint32Array(this.shadow.buffer, ptr & ~PTR_MASK, 1)[0];\n      if (value != 0) return;\n  \n      if (!isRT) {\n        let stack = trimStacktrace(new Error().stack, 2);\n        this.onerror(new Error(\"OOB \" + (isLoad ? \"load\" : \"store\") + 8 * size + \" at address \" + ptr + \"\\n\" + stack.join(\"\\n\")));\n      }\n    }\n    /** Obtains information about a block. */\n  \n  \n    getBlockInfo(ptr) {\n      const [mmInfo, gcInfo, gcInfo2, rtId, rtSize] = new Uint32Array(this.memory.buffer, ptr, 5);\n      const size = mmInfo & ~3;\n      return {\n        ptr,\n        size: BLOCK_OVERHEAD + size,\n        // total incl. overhead\n        mmInfo: {\n          tags: mmTagsToString[mmInfo & 3],\n          size: size // as stored excl. overhead\n  \n        },\n        gcInfo: {\n          color: gcColorToString[gcInfo & 3],\n          next: gcInfo & ~3,\n          prev: gcInfo2\n        },\n        rtId,\n        rtSize\n      };\n    }\n    /** Checks if rtrace is active, i.e. at least one event has occurred. */\n  \n  \n    get active() {\n      return Boolean(this.allocCount || this.resizeCount || this.moveCount || this.freeCount);\n    }\n    /** Checks if there are any leaks and emits them via `oninfo`. Returns the number of live blocks. */\n  \n  \n    check() {\n      if (this.oninfo) {\n        for (let [ptr, info] of this.blocks) {\n          this.oninfo(\"LIVE \" + ptr + \"\\n\" + info.allocStack.join(\"\\n\"));\n        }\n      }\n  \n      return this.blocks.size;\n    } // Runtime instrumentation\n  \n  \n    oninit(heapBase) {\n      this.heapBase = heapBase;\n      this.gcProfileStart = 0;\n      this.gcProfile.length = 0;\n      this.oninfo(\"INIT heapBase=\" + heapBase);\n    }\n  \n    onalloc(ptr) {\n      this.syncShadow();\n      ++this.allocCount;\n      var info = this.getBlockInfo(ptr);\n  \n      if (this.blocks.has(ptr)) {\n        this.onerror(Error(\"duplicate alloc: \" + ptr), info);\n      } else {\n        this.oninfo(\"ALLOC \" + ptr + \"..\" + (ptr + info.size));\n        this.markShadow(info);\n        let allocStack = trimStacktrace(new Error().stack, 1); // strip onalloc\n  \n        this.blocks.set(ptr, Object.assign(info, {\n          allocStack\n        }));\n      }\n    }\n  \n    onresize(ptr, oldSize) {\n      this.syncShadow();\n      ++this.resizeCount;\n      const info = this.getBlockInfo(ptr);\n  \n      if (!this.blocks.has(ptr)) {\n        this.onerror(Error(\"orphaned resize: \" + ptr), info);\n      } else {\n        const beforeInfo = this.blocks.get(ptr);\n  \n        if (beforeInfo.size != oldSize) {\n          this.onerror(Error(`size mismatch upon resize: ${ptr} (${beforeInfo.size} != ${oldSize})`), info);\n        }\n  \n        const newSize = info.size;\n        this.oninfo(\"RESIZE \" + ptr + \"..\" + (ptr + newSize) + \" (\" + oldSize + \"->\" + newSize + \")\");\n        this.blocks.set(ptr, Object.assign(info, {\n          allocStack: beforeInfo.allocStack\n        }));\n  \n        if (newSize > oldSize) {\n          this.markShadow(info, oldSize);\n        } else if (newSize < oldSize) {\n          this.unmarkShadow(info, oldSize);\n        }\n      }\n    }\n  \n    onmove(oldPtr, newPtr) {\n      this.syncShadow();\n      ++this.moveCount;\n      var oldInfo = this.getBlockInfo(oldPtr);\n      var newInfo = this.getBlockInfo(newPtr);\n  \n      if (!this.blocks.has(oldPtr)) {\n        this.onerror(Error(\"orphaned move (old): \" + oldPtr), oldInfo);\n      } else {\n        if (!this.blocks.has(newPtr)) {\n          this.onerror(Error(\"orphaned move (new): \" + newPtr), newInfo);\n        } else {\n          const beforeInfo = this.blocks.get(oldPtr);\n          const oldSize = oldInfo.size;\n          const newSize = newInfo.size;\n  \n          if (beforeInfo.size != oldSize) {\n            this.onerror(Error(`size mismatch upon move: ${oldPtr} (${beforeInfo.size} != ${oldSize})`), oldInfo);\n          }\n  \n          this.oninfo(\"MOVE \" + oldPtr + \"..\" + (oldPtr + oldSize) + \" -> \" + newPtr + \"..\" + (newPtr + newSize)); // calls new alloc before and old free after\n        }\n      }\n    }\n  \n    onvisit(ptr) {\n      // Indicates that a block has been freed but it still visited by the GC\n      if (ptr > this.heapBase && !this.blocks.has(ptr)) {\n        let err = Error(\"orphaned visit: \" + ptr);\n        let info = this.freedBlocks.get(ptr);\n  \n        if (info) {\n          err.stack += \"\\n^ allocated at:\\n\" + info.allocStack.join(\"\\n\");\n          err.stack += \"\\n^ freed at:\\n\" + info.freeStack.join(\"\\n\");\n        }\n  \n        this.onerror(err, null);\n        return false;\n      }\n  \n      return true;\n    }\n  \n    onfree(ptr) {\n      this.syncShadow();\n      ++this.freeCount;\n      var info = this.getBlockInfo(ptr);\n  \n      if (!this.blocks.has(ptr)) {\n        this.onerror(Error(\"orphaned free: \" + ptr), info);\n      } else {\n        const oldInfo = this.blocks.get(ptr);\n  \n        if (info.size != oldInfo.size) {\n          this.onerror(Error(`size mismatch upon free: ${ptr} (${oldInfo.size} != ${info.size})`), info);\n        }\n  \n        this.oninfo(\"FREE \" + ptr + \"..\" + (ptr + info.size));\n        this.unmarkShadow(info);\n        const allocInfo = this.blocks.get(ptr);\n        this.blocks.delete(ptr);\n        const allocStack = allocInfo.allocStack;\n        const freeStack = trimStacktrace(new Error().stack, 1); // strip onfree\n        // (not much) TODO: Maintaining these is essentially a memory leak\n  \n        this.freedBlocks.set(ptr, {\n          allocStack,\n          freeStack\n        });\n      }\n    }\n  \n    oncollect(total) {\n      this.oninfo(`COLLECT at ${total}`);\n      this.plot(total);\n      this.oncollect_();\n    } // GC profiling\n  \n  \n    plot(total, pause = 0) {\n      if (!this.gcProfileStart) this.gcProfileStart = Date.now();\n      this.gcProfile.push([Date.now() - this.gcProfileStart, total, pause]);\n    }\n  \n    oninterrupt(total) {\n      this.interruptStart = hrtime();\n      this.plot(total);\n    }\n  \n    onyield(total) {\n      var pause = hrtime() - this.interruptStart;\n      if (pause >= 1) console.log(\"interrupted for \" + pause.toFixed(1) + \"ms\");\n      this.plot(total, pause);\n    } // Memory instrumentation\n  \n  \n    onstore(ptr, offset, bytes, isRT) {\n      this.accessShadow(ptr + offset, bytes, false, isRT);\n      return ptr;\n    }\n  \n    onload(ptr, offset, bytes, isRT) {\n      this.accessShadow(ptr + offset, bytes, true, isRT);\n      return ptr;\n    }\n  \n  }\n  \n  exports.Rtrace = Rtrace;\n  var _default = {\n    Rtrace\n  };\n  exports.default = _default;\n  return exports;\n})({});\nif (typeof define === 'function' && define.amd) define([], function() { return rtrace; });\nelse if (typeof module === 'object' && typeof exports==='object') module.exports = rtrace;\n", "if(typeof __WEBPACK_EXTERNAL_MODULE__525__ === 'undefined') { var e = new Error(\"Cannot find module 'assemblyscript'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__525__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__911__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(469);\n"], "sourceRoot": ""}