#!/usr/bin/env node

const tailArgs = process.argv.indexOf("--");
if (~tailArgs) {
  require("child_process").spawnSync(
    process.argv[0],
    process.argv.slice(tailArgs + 1).concat(
      process.argv.slice(1, tailArgs)
    ),
    { stdio: "inherit" }
  );
  return;
}

try { require("source-map-support").install(); } catch (e) {}

const asc = module.exports = require("../cli/asc.js");
if (/\basc$/.test(process.argv[1])) {
  asc.ready.then(() => process.exitCode = asc.main(process.argv.slice(2)));
}
