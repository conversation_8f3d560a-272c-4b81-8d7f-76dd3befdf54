The following authors have all licensed their contributions to AssemblyScript
under the licensing terms detailed in LICENSE:

* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <alang<PERSON><EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* jhwgh1968 <<EMAIL>>
* <PERSON> <jeffrey<PERSON><PERSON>@gmail.com>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* Surma <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* ncave <<EMAIL>>
* <PERSON> <<EMAIL>>
* Maël Nison <<EMAIL>>
* Valeria Viana Gusmao <<EMAIL>>
* Gabor Greif <<EMAIL>>
* Martin Fredriksson <<EMAIL>>
* forcepusher <<EMAIL>>
* Piotr Oleś <<EMAIL>>
* Saúl Cabrera <<EMAIL>>
* Chance Snow <*****************>
* Peter Salomonsen <<EMAIL>>
* ookangzheng <<EMAIL>>
* yjhmelody <<EMAIL>>
* bnbarak <<EMAIL>>
* Colin Eberhardt <<EMAIL>>
* Ryan Pivovar <<EMAIL>>
* Roman F. <<EMAIL>>
* Joe Pea <<EMAIL>>
* Felipe Gasper <<EMAIL>>
* Congcong Cai <<EMAIL>>

Portions of this software are derived from third-party works licensed under
the following terms:

* TypeScript: https://github.com/Microsoft/TypeScript

  Copyright (c) Microsoft Corporation
  Apache License, Version 2.0 (https://opensource.org/licenses/Apache-2.0)

* Binaryen: https://github.com/WebAssembly/binaryen

  Copyright (c) WebAssembly Community Group participants
  Apache License, Version 2.0 (https://opensource.org/licenses/Apache-2.0)

* musl libc: http://www.musl-libc.org

  Copyright (c) Rich Felker, et al.
  The MIT License (https://opensource.org/licenses/MIT)

* V8: https://developers.google.com/v8/

  Copyright (c) the V8 project authors
  The 3-Clause BSD License (https://opensource.org/licenses/BSD-3-Clause)

* Arm Optimized Routines: https://github.com/ARM-software/optimized-routines

  Copyright (c) Arm Limited
  The MIT License (https://opensource.org/licenses/MIT)
