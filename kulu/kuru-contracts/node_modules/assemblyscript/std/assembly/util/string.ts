import { itoa32, utoa32, itoa64, utoa64, dtoa, itoa_buffered, dtoa_buffered, MAX_DOUBLE_LENGTH } from "./number";
import { ipow32 } from "../math";

// All tables are stored as two staged lookup tables (static tries)
// because the full range of Unicode symbols can't be efficiently
// represented as-is in memory (see Unicode spec ch 5, p.196):
// https://www.unicode.org/versions/Unicode12.0.0/ch05.pdf
// Tables have been generated using these forked musl tools:
// https://github.com/MaxGraey/musl-chartable-tools/tree/case-ignorable

// Lookup table to check if a character is alphanumeric or not
// See: https://git.musl-libc.org/cgit/musl/tree/src/ctype/alpha.h
// size: 3904 bytes
// @ts-ignore
@inline @lazy const ALPHA_TABLE = memory.data<u8>([
  18,17,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,17,34,35,36,17,37,38,39,40,
  41,42,43,44,17,45,46,47,16,16,48,16,16,16,16,16,16,16,49,50,51,16,52,53,16,16,
  17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,54,
  17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,
  17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,
  17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,
  17,17,17,55,17,17,17,17,56,17,57,58,59,60,61,62,17,17,17,17,17,17,17,17,17,17,
  17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,17,
  17,17,17,17,17,17,17,63,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,17,64,65,17,66,67,
  68,69,70,71,72,73,74,17,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,
  93,94,16,95,96,97,98,17,17,17,99,100,101,16,16,16,16,16,16,16,16,16,16,17,17,
  17,17,102,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,17,17,103,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,17,17,104,105,16,16,106,107,17,17,17,17,17,17,17,17,17,17,17,17,17,
  17,17,17,17,17,17,17,17,17,17,108,17,17,17,17,109,110,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  17,111,112,16,16,16,16,16,16,16,16,16,113,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,114,115,116,117,16,16,16,16,16,16,16,16,118,
  119,120,16,16,16,16,16,121,122,16,16,16,16,123,16,16,124,16,16,16,16,16,16,16,
  16,16,125,16,16,16,
  16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,0,0,0,0,0,0,0,0,254,255,255,7,254,
  255,255,7,0,0,0,0,0,4,32,4,255,255,127,255,255,255,127,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,195,255,3,0,31,80,0,0,0,0,0,0,0,0,0,0,32,0,0,0,0,0,223,188,64,215,255,255,
  251,255,255,255,255,255,255,255,255,255,191,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,3,252,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,254,255,255,255,127,2,255,255,255,
  255,255,1,0,0,0,0,255,191,182,0,255,255,255,135,7,0,0,0,255,7,255,255,255,255,
  255,255,255,254,255,195,255,255,255,255,255,255,255,255,255,255,255,255,239,
  31,254,225,255,
  159,0,0,255,255,255,255,255,255,0,224,255,255,255,255,255,255,255,255,255,255,
  255,255,3,0,255,255,255,255,255,7,48,4,255,255,255,252,255,31,0,0,255,255,255,
  1,255,7,0,0,0,0,0,0,255,255,223,255,255,0,240,255,248,3,255,255,255,255,255,
  255,255,255,255,239,255,223,225,255,207,255,254,255,239,159,249,255,255,253,
  197,227,159,89,128,176,207,255,3,16,238,135,249,255,255,253,109,195,135,25,2,
  94,192,255,63,0,238,191,251,255,255,253,237,227,191,27,1,0,207,255,0,30,238,
  159,249,255,255,253,237,227,159,25,192,176,207,255,2,0,236,199,61,214,24,199,
  255,195,199,29,129,0,192,255,0,0,239,223,253,255,255,253,255,227,223,29,96,7,
  207,255,0,0,239,223,253,255,255,253,239,227,223,29,96,64,207,255,6,0,255,223,
  253,255,255,255,255,231,223,93,240,128,207,255,0,252,238,255,127,252,255,255,
  251,47,127,128,95,255,192,255,12,0,254,255,255,255,255,127,255,7,63,32,255,3,
  0,0,0,0,214,247,255,255,175,255,255,59,95,32,255,243,0,0,0,
  0,1,0,0,0,255,3,0,0,255,254,255,255,255,31,254,255,3,255,255,254,255,255,255,
  31,0,0,0,0,0,0,0,0,255,255,255,255,255,255,127,249,255,3,255,255,255,255,255,
  255,255,255,255,63,255,255,255,255,191,32,255,255,255,255,255,247,255,255,255,
  255,255,255,255,255,255,61,127,61,255,255,255,255,255,61,255,255,255,255,61,
  127,61,255,127,255,255,255,255,255,255,255,61,255,255,255,255,255,255,255,255,
  7,0,0,0,0,255,255,0,0,255,255,255,255,255,255,255,255,255,255,63,63,254,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,159,255,255,254,255,255,7,255,255,255,255,255,255,255,255,
  255,199,255,1,255,223,15,0,255,255,15,0,255,255,15,0,255,223,13,0,255,255,255,
  255,255,255,207,255,255,1,128,16,255,3,0,0,0,0,255,3,255,255,255,255,255,255,
  255,255,255,255,255,1,255,255,255,255,255,7,255,255,255,255,255,255,255,255,
  63,
  0,255,255,255,127,255,15,255,1,192,255,255,255,255,63,31,0,255,255,255,255,
  255,15,255,255,255,3,255,3,0,0,0,0,255,255,255,15,255,255,255,255,255,255,255,
  127,254,255,31,0,255,3,255,3,128,0,0,128,1,0,0,0,0,0,0,0,255,255,255,255,255,
  255,239,255,239,15,255,3,0,0,0,0,255,255,255,255,255,243,255,255,255,255,255,
  255,191,255,3,0,255,255,255,255,255,255,127,0,255,227,255,255,255,255,255,63,
  255,1,255,255,255,255,255,231,0,0,0,0,0,222,111,4,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,0,0,0,0,
  128,255,31,0,255,255,63,63,255,255,255,255,63,63,255,170,255,255,255,63,255,
  255,255,255,255,255,223,95,220,31,207,15,255,31,220,31,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,2,128,0,0,255,31,0,0,0,0,0,0,0,0,0,0,0,0,132,252,47,62,80,189,255,243,
  224,67,0,0,255,255,255,255,255,1,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,255,255,255,255,255,255,3,0,
  0,255,255,255,255,255,127,255,255,255,255,255,127,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,31,120,12,0,255,255,255,255,191,32,255,
  255,255,255,255,255,255,128,0,0,255,255,127,0,127,127,127,127,127,127,127,127,
  255,255,255,255,0,0,0,0,0,128,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,224,0,0,0,254,3,62,31,254,255,255,255,255,255,255,255,255,255,127,224,254,
  255,255,255,255,255,255,255,255,255,255,247,224,255,255,255,255,255,254,255,
  255,255,255,255,255,255,255,255,255,127,0,0,255,255,255,255,0,0,0,0,0,0,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,
  31,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,31,0,0,
  0,0,0,0,0,0,255,255,255,255,255,63,255,31,255,255,255,15,0,0,255,255,255,255,
  255,127,240,143,255,255,255,255,255,255,255,255,255,255,255,255,255,255,0,0,0,
  0,128,255,252,255,255,255,255,255,255,255,255,255,255,255,255,249,255,255,255,
  255,255,255,252,7,0,0,0,0,224,255,191,255,255,255,255,0,0,0,255,255,255,255,
  255,255,15,0,255,255,255,255,255,255,255,255,47,0,255,3,0,0,252,232,255,255,
  255,255,255,7,255,255,255,255,7,0,255,255,255,31,255,255,255,255,255,255,247,
  255,0,128,255,3,255,255,255,127,255,255,255,255,255,255,127,0,255,63,255,3,
  255,255,127,252,255,255,255,255,255,255,255,127,5,0,0,56,255,255,60,0,126,126,
  126,0,127,127,255,255,255,255,255,247,255,3,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,7,255,3,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,15,0,255,255,127,248,255,255,255,255,
  255,
  15,255,255,255,255,255,255,255,255,255,255,255,255,255,63,255,255,255,255,255,
  255,255,255,255,255,255,255,255,3,0,0,0,0,127,0,248,224,255,253,127,95,219,
  255,255,255,255,255,255,255,255,255,255,255,255,255,3,0,0,0,248,255,255,255,
  255,255,255,255,255,255,255,255,255,63,0,0,255,255,255,255,255,255,255,255,
  252,255,255,255,255,255,255,0,0,0,0,0,255,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,223,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,31,0,0,255,3,
  254,255,255,7,254,255,255,7,192,255,255,255,255,255,255,255,255,255,255,127,
  252,252,252,28,0,0,0,0,255,239,255,255,127,255,255,183,255,63,255,63,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,7,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,31,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,255,255,255,31,255,255,255,255,255,255,1,0,0,0,0,
  0,255,255,255,255,0,224,255,255,255,7,255,255,255,255,255,7,255,255,255,63,
  255,255,255,255,15,255,62,0,0,0,0,0,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,63,255,3,255,255,255,255,15,255,255,255,
  255,15,255,255,255,255,255,0,255,255,255,255,255,255,15,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,255,255,255,255,255,255,127,0,255,255,63,0,255,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,63,253,255,255,255,255,191,145,255,255,63,0,255,255,
  127,0,255,255,255,127,0,0,0,0,0,0,0,0,255,255,55,0,255,255,63,0,255,255,255,3,
  0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,192,0,0,0,0,0,0,0,0,111,240,239,
  254,255,255,63,0,0,0,0,0,255,255,255,31,255,255,255,31,0,0,0,0,255,254,255,
  255,31,0,0,0,255,255,255,255,255,255,63,0,255,255,63,0,255,255,7,0,255,255,3,
  0,0,0,0,0,0,0,0,0,0,0,0,
  0,255,255,255,255,255,255,255,255,255,1,0,0,0,0,0,0,255,255,255,255,255,255,7,
  0,255,255,255,255,255,255,7,0,255,255,255,255,255,0,255,3,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,
  255,27,3,0,0,0,0,0,0,0,0,0,255,255,255,31,128,0,255,255,63,0,0,0,0,0,0,0,0,0,
  0,0,0,0,255,255,31,0,0,0,255,255,127,0,255,255,255,255,255,255,255,255,63,0,0,
  0,192,255,0,0,252,255,255,255,255,255,255,1,0,0,255,255,255,1,255,3,255,255,
  255,255,255,255,199,255,240,0,255,255,255,255,71,0,255,255,255,255,255,255,
  255,255,30,192,255,23,0,0,0,0,255,255,251,255,255,255,159,64,0,0,0,0,0,0,0,0,
  127,189,255,191,255,1,255,255,255,255,255,255,255,1,255,3,239,159,249,255,255,
  253,237,227,159,25,129,224,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,255,255,255,255,255,255,255,255,187,7,255,131,3,0,0,0,255,255,255,255,255,
  255,255,255,179,0,255,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,
  255,255,255,63,127,0,0,0,63,0,0,0,0,255,255,255,255,255,255,255,127,17,0,255,
  3,0,0,0,0,255,255,255,255,255,255,63,1,255,3,0,0,0,0,0,0,255,255,255,231,255,
  7,255,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,
  255,255,1,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,3,0,128,
  127,242,111,255,255,255,191,153,7,0,255,3,0,0,0,0,0,0,0,0,255,252,255,255,255,
  255,255,252,26,0,0,0,255,255,255,255,255,255,231,127,0,0,255,255,255,255,255,
  255,255,255,255,32,0,0,0,0,255,255,255,255,255,255,255,1,255,253,255,255,255,
  255,127,127,1,0,255,3,0,0,252,255,255,255,252,255,255,254,127,0,0,0,0,0,0,0,0,
  0,127,251,255,255,255,255,127,180,203,0,255,3,191,253,255,255,255,127,123,1,
  255,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,255,255,127,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,
  0,0,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,3,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,255,255,255,
  255,127,0,0,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,255,255,255,255,255,127,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,255,255,255,255,255,255,255,255,127,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,
  0,255,255,255,255,255,255,255,1,255,255,255,127,255,3,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,63,0,0,255,255,255,255,255,255,0,0,15,0,255,3,248,255,255,224,255,
  255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,
  255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,135,
  255,255,255,255,255,255,255,128,255,255,0,0,0,0,0,0,0,0,11,0,3,0,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,0,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,63,0,0,0,0,0,
  255,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,
  127,0,0,0,0,0,0,7,0,240,0,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,15,255,255,255,255,255,
  255,255,255,255,255,255,255,255,7,255,31,255,1,255,67,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,223,255,255,255,255,255,255,255,255,
  223,100,222,255,235,239,255,255,255,255,255,255,255,191,231,223,223,255,255,
  255,123,95,252,253,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,63,255,255,255,253,255,255,247,255,255,255,
  247,255,255,223,255,255,255,223,255,255,127,255,255,255,127,255,255,255,253,
  255,255,255,253,255,255,247,207,255,255,255,255,255,255,127,255,255,249,219,7,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,31,
  128,63,255,67,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,15,255,
  3,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,31,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,143,8,
  255,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,239,255,255,255,150,254,247,10,
  132,234,150,170,150,247,247,94,255,251,255,15,238,251,255,15,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,255,255,255,3,255,255,255,3,255,255,255,3,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,3
]);

// size: 1568 bytes (compressed to ~1380 bytes after binaryen)
// @ts-ignore: decorator
@lazy @inline const CASED = memory.data<u8>([
  18,19,20,21,22,23,16,16,16,16,16,16,16,16,16,16,
  24,16,16,25,16,16,16,16,16,16,16,16,26,27,17,28,
  29,30,16,16,31,16,16,16,16,16,16,16,32,33,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,34,35,16,16,16,36,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,37,16,16,16,38,
  16,16,16,16,39,16,16,16,16,16,16,16,40,16,16,16,
  16,16,16,16,16,16,16,16,41,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,42,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,43,44,45,46,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,47,16,16,16,16,16,16,
  16,48,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  0,0,0,0,0,0,0,0,254,255,255,7,254,255,255,7,0,0,0,0,0,4,32,4,
  255,255,127,255,255,255,127,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,247,240,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,239,255,255,255,255,1,3,0,0,0,31,0,0,0,
  0,0,0,0,0,0,0,0,32,0,0,0,0,0,207,188,64,215,255,255,251,255,255,255,
  255,255,255,255,255,255,191,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  3,252,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,254,255,
  255,255,127,0,255,255,255,255,255,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,
  191,32,255,255,255,255,255,231,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,255,255,255,255,255,255,255,255,255,255,63,63,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,255,1,255,255,255,255,255,231,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  0,0,0,0,0,0,0,0,255,255,63,63,255,255,255,255,63,63,255,170,255,255,255,63,
  255,255,255,255,255,255,223,95,220,31,207,15,255,31,220,31,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,2,128,0,0,255,31,0,0,0,0,0,0,0,0,0,0,0,0,
  132,252,47,62,80,189,31,242,224,67,0,0,255,255,255,255,24,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,192,255,255,255,255,255,255,3,0,0,255,255,255,255,255,127,255,255,
  255,255,255,127,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,31,120,12,0,
  255,255,255,255,191,32,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,63,0,0,
  255,255,255,63,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,252,255,255,255,
  255,255,255,255,255,255,255,255,255,120,255,255,255,255,255,255,252,7,0,0,0,0,96,7,
  0,0,0,0,0,0,255,255,255,255,255,247,255,1,255,255,255,255,255,255,255,255,255,255,
  0,0,0,0,0,0,0,0,127,0,248,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,254,255,255,7,
  254,255,255,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,255,255,
  255,255,15,255,255,255,255,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,7,0,255,255,255,255,255,255,7,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,0,0,0,0,
  0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,255,223,255,255,255,255,255,
  255,255,255,223,100,222,255,235,239,255,255,255,255,255,255,255,191,231,223,223,255,255,255,123,
  95,252,253,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,63,255,255,255,
  253,255,255,247,255,255,255,247,255,255,223,255,255,255,223,255,255,127,255,255,255,127,255,255,
  255,253,255,255,255,253,255,255,247,15,0,0,0,0,0,0,255,255,255,255,255,255,255,255,
  15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,255,255,255,3,255,255,255,3,255,255,255,3,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0
]);

// size: 2976 bytes (compressed to ~2050 bytes after binaryen)
// @ts-ignore: decorator
@lazy @inline const CASE_IGNORABLES = memory.data<u8>([
  18,16,19,20,21,22,23,24,25,26,27,28,29,30,31,32,
  33,16,16,34,16,16,16,35,36,37,38,39,40,41,16,42,
  43,16,16,16,16,16,16,16,16,16,16,16,44,45,46,16,
  47,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  48,16,16,16,49,16,50,51,52,53,54,55,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,56,16,16,57,58,
  16,59,60,61,16,16,16,16,16,16,62,16,16,63,64,65,
  66,67,68,69,70,71,72,73,74,75,76,16,77,78,79,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,80,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,81,82,16,16,16,83,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,84,16,16,16,
  16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
  16,85,86,16,16,16,16,16,16,16,87,16,16,16,16,16,
  88,89,90,16,16,16,16,16,91,92,16,16,16,16,16,16,
  16,16,16,93,16,16,16,16,16,16,16,16,16,16,16,16,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  0,0,0,0,128,64,0,4,0,0,0,64,1,0,0,0,0,0,0,0,0,161,144,1,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,
  255,255,255,255,255,255,48,4,176,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,248,3,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,130,0,0,0,0,
  0,0,254,255,255,255,255,191,182,0,0,0,0,0,16,0,63,0,255,23,0,0,0,0,
  1,248,255,255,0,0,1,0,0,0,0,0,0,0,0,0,0,0,192,191,255,61,0,0,
  0,128,2,0,0,0,255,255,255,7,0,0,0,0,0,0,0,0,0,0,192,255,1,0,
  0,0,0,0,0,248,63,36,0,0,192,255,255,63,0,0,0,0,0,14,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,248,255,255,255,255,255,7,0,0,0,0,0,0,20,
  254,33,254,0,12,0,2,0,2,0,0,0,0,0,0,16,30,32,0,0,12,0,0,64,
  6,0,0,0,0,0,0,16,134,57,2,0,0,0,35,0,6,0,0,0,0,0,0,16,
  190,33,0,0,12,0,0,252,2,0,0,0,0,0,0,144,30,32,96,0,12,0,0,0,
  4,0,0,0,0,0,0,0,1,32,0,0,0,0,0,0,17,0,0,0,0,0,0,192,
  193,61,96,0,12,0,0,0,2,0,0,0,0,0,0,144,64,48,0,0,12,0,0,0,
  3,0,0,0,0,0,0,24,30,32,0,0,12,0,0,0,2,0,0,0,0,0,0,0,
  0,4,92,0,0,0,0,0,0,0,0,0,0,0,242,7,192,127,0,0,0,0,0,0,
  0,0,0,0,0,0,242,31,64,63,0,0,0,0,0,0,0,0,0,3,0,0,160,2,
  0,0,0,0,0,0,254,127,223,224,255,254,255,255,255,31,64,0,0,0,0,0,0,0,
  0,0,0,0,0,224,253,102,0,0,0,195,1,0,30,0,100,32,0,32,0,0,0,0,
  0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,224,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,28,0,0,0,28,0,
  0,0,12,0,0,0,12,0,0,0,0,0,0,0,176,63,64,254,143,32,0,0,0,0,
  0,120,0,0,0,0,0,0,8,0,0,0,0,0,0,0,96,0,0,0,0,2,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,135,1,4,14,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,128,9,0,0,0,0,
  0,0,64,127,229,31,248,159,0,0,0,0,128,0,255,255,1,0,0,0,0,0,0,0,
  15,0,0,0,0,0,208,23,4,0,0,0,0,248,15,0,3,0,0,0,60,59,0,0,
  0,0,0,0,64,163,3,0,0,0,0,0,0,240,207,0,0,0,0,0,0,0,0,63,
  0,0,0,0,0,0,0,0,0,0,247,255,253,33,16,3,0,0,0,0,0,240,255,255,
  255,255,255,255,255,7,0,1,0,0,0,248,255,255,255,255,255,255,255,255,255,255,255,251,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,160,
  3,224,0,224,0,224,0,96,0,248,0,3,144,124,0,0,0,0,0,0,223,255,2,128,
  0,0,255,31,0,0,0,0,0,0,255,255,255,255,1,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,48,0,0,0,0,0,0,0,0,0,0,0,0,0,128,3,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,128,0,128,0,0,0,0,0,0,0,0,
  0,0,0,0,255,255,255,255,0,0,0,0,0,128,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,32,0,0,0,0,60,62,8,
  0,0,0,0,0,0,0,0,0,0,0,126,0,0,0,0,0,0,0,0,0,0,0,112,
  0,0,32,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,63,0,16,0,0,0,0,0,0,
  0,0,0,0,0,128,247,191,0,0,0,240,0,0,0,0,0,0,0,0,0,0,3,0,
  255,255,255,255,3,0,0,0,0,0,0,0,0,0,1,0,0,7,0,0,0,0,0,0,
  0,0,0,0,0,0,0,3,68,8,0,0,96,16,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,48,0,0,0,255,255,3,128,0,0,0,0,192,63,0,0,
  128,255,3,0,0,0,0,0,7,0,0,0,0,0,200,51,0,128,0,0,96,0,0,0,
  0,0,0,0,0,126,102,0,8,16,0,0,0,0,1,16,0,0,0,0,0,0,157,193,
  2,0,0,32,0,48,88,0,0,0,0,0,0,0,0,0,0,0,0,248,0,14,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,32,33,0,0,0,0,0,64,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,252,255,3,0,0,0,0,0,0,0,
  255,255,8,0,255,255,0,0,0,0,36,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,128,128,64,0,4,0,0,0,64,1,0,0,0,0,0,1,0,
  0,0,0,192,0,0,0,0,0,0,0,0,8,0,0,14,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,32,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,7,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,110,240,0,0,0,0,0,135,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,0,0,0,
  0,0,0,0,240,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,24,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  192,255,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  2,0,0,0,0,0,0,255,127,0,0,0,0,0,0,128,3,0,0,0,0,0,120,38,
  0,32,0,0,0,0,0,0,7,0,0,0,128,239,31,0,0,0,0,0,0,0,8,0,
  3,0,0,0,0,0,192,127,0,158,0,0,0,0,0,0,0,0,0,0,0,128,211,64,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,128,248,7,0,0,
  3,0,0,0,0,0,0,24,1,0,0,0,192,31,31,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,92,0,0,64,0,0,0,0,
  0,0,0,0,0,0,248,133,13,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,60,176,1,0,0,48,0,0,0,0,
  0,0,0,0,0,0,248,167,1,0,0,0,0,0,0,0,0,0,0,0,0,40,191,0,
  0,0,0,0,0,0,0,0,0,0,0,224,188,15,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,128,255,6,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,88,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,240,12,1,0,0,0,254,7,0,0,0,0,248,121,128,0,126,14,0,0,0,0,
  0,252,127,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,127,191,
  0,0,0,0,0,0,0,0,0,0,252,255,255,252,109,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,126,180,191,0,0,0,0,0,0,0,0,0,163,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,0,0,0,0,0,0,0,255,1,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,31,0,0,0,0,0,0,0,127,0,15,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,128,0,0,0,0,0,0,0,128,255,255,0,0,0,0,0,0,0,0,27,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,15,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,128,3,248,255,
  231,15,0,0,0,60,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  28,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  255,255,255,255,255,255,127,248,255,255,255,255,255,31,32,0,16,0,0,248,254,255,0,0,
  0,0,0,0,0,0,0,0,127,255,255,249,219,7,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,63,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,240,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,127,0,0,0,0,0,0,0,0,0,0,0,0,0,
  240,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
  0,0,0,0,0,0,0,248
]);

// @ts-ignore: decorator
@lazy @inline const LOWER127 = memory.data<u8>([
  0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,
  16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,
  32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,
  48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,
  64,
  97,98,99,100,101,102,103,104,105,106,107,108,109,
  110,111,112,113,114,115,116,117,118,119,120,121,122,
  91,92,93,94,95,96,
  97,98,99,100,101,102,103,104,105,106,107,108,109,
  110,111,112,113,114,115,116,117,118,119,120,121,122,
  123,124,125,126,127
]);

// @ts-ignore: decorator
@lazy @inline const UPPER127 = memory.data<u8>([
  0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,
  16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,
  32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,
  48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,
  64,
  65,66,67,68,69,70,71,72,73,74,75,76,77,
  78,79,80,81,82,83,84,85,86,87,88,89,90,
  91,92,93,94,95,96,
  65,66,67,68,69,70,71,72,73,74,75,76,77,
  78,79,80,81,82,83,84,85,86,87,88,89,90,
  123,124,125,126,127
]);

// 23 * 8 = 184 bytes
// @ts-ignore: decorator
@lazy @inline const POWERS10 = memory.data<f64>([
  1e00, 1e01, 1e02, 1e03, 1e04, 1e05, 1e06, 1e07, 1e08, 1e09,
  1e10, 1e11, 1e12, 1e13, 1e14, 1e15, 1e16, 1e17, 1e18, 1e19,
  1e20, 1e21, 1e22
]);

// @ts-ignore: decorator
@inline
export const enum CharCode {
  PERCENT = 0x25,
  PLUS = 0x2B,
  MINUS = 0x2D,
  DOT = 0x2E,
  _0 = 0x30,
  _1 = 0x31,
  _2 = 0x32,
  _3 = 0x33,
  _4 = 0x34,
  _5 = 0x35,
  _6 = 0x36,
  _7 = 0x37,
  _8 = 0x38,
  _9 = 0x39,
  A = 0x41,
  B = 0x42,
  E = 0x45,
  I = 0x49,
  N = 0x4E,
  O = 0x4F,
  X = 0x58,
  Z = 0x5A,
  a = 0x61,
  b = 0x62,
  e = 0x65,
  n = 0x6E,
  o = 0x6F,
  u = 0x75,
  x = 0x78,
  z = 0x7A
}

// @ts-ignore: decorator
@inline
export function isAscii(c: u32): bool {
  return !(c >> 7);
}

// @ts-ignore: decorator
@inline
export function isLower8(c: u32): bool {
  return c - CharCode.a < 26;
}

// @ts-ignore: decorator
@inline
export function isUpper8(c: u32): bool {
  return c - CharCode.A < 26;
}

export function isSpace(c: u32): bool {
  if (c < 0x1680) { // < <LS> (1)
    // <SP>, <TAB>, <LF>, <VT>, <FF>, <CR> and <NBSP>
    // (c == 0x20 || c == 0xA0) was optimized to (c | 0x80) == 0xA0
    // @ts-ignore: cast
    return ((c | 0x80) == 0xA0) | (c - 0x09 <= 0x0D - 0x09);
  }
  if (c - 0x2000 <= 0x200A - 0x2000) return true;
  switch (c) {
    case 0x1680: // <LS> (1)
    case 0x2028: // <LS> (2)
    case 0x2029: // <PS>
    case 0x202F: // <NNS>
    case 0x205F: // <MMSP>
    case 0x3000: // <IS>
    case 0xFEFF: return true; // <ZWNBSP>
  }
  return false;
}

export function isAlpha(c: u32): bool {
  if (isAscii(c)) return (c | 32) - CharCode.a < 26;
  if (c < 0x20000) {
    // @ts-ignore: cast
    return stagedBinaryLookup(ALPHA_TABLE, c);
  }
  return c < 0x2FFFE;
}

// @ts-ignore: decorator
@inline
export function isCased(c: u32): bool {
  // @ts-ignore: cast
  return c < 0x1F18A && stagedBinaryLookup(CASED, c);
}

// @ts-ignore: decorator
@inline
export function isCaseIgnorable(c: u32): bool {
  // @ts-ignore: cast
  return c < 0xE01F0 && stagedBinaryLookup(CASE_IGNORABLES, c);
}

// @ts-ignore: decorator
@inline
export function isFinalSigma(buffer: usize, index: isize, len: isize): bool {
  const lookaheadLimit = 30; // max lookahead limit
  var found = false;
  var pos = index;
  var minPos = max(0, pos - lookaheadLimit);
  while (pos > minPos) {
    let c = codePointBefore(buffer, pos);
    if (!isCaseIgnorable(c)) {
      if (isCased(c)) {
        found = true;
      } else {
        return false;
      }
    }
    pos -= isize(c >= 0x10000) + 1;
  }
  if (!found) return false;
  pos = index + 1;
  var maxPos = min(pos + lookaheadLimit, len);
  while (pos < maxPos) {
    let c = <u32>load<u16>(buffer + (pos << 1));
    if (u32((c & 0xFC00) == 0xD800) & u32(pos + 1 != len)) {
      let c1 = <u32>load<u16>(buffer + (pos << 1), 2);
      if ((c1 & 0xFC00) == 0xDC00) {
        c = (c - 0xD800 << 10) + (c1 - 0xDC00) + 0x10000;
      }
    }
    if (!isCaseIgnorable(c)) {
      return !isCased(c);
    }
    pos += isize(c >= 0x10000) + 1;
  }
  return true;
}

// @ts-ignore: decorator
@inline
function codePointBefore(buffer: usize, index: isize): i32 {
  if (index <= 0) return -1;
  var c = <u32>load<u16>(buffer + (index - 1 << 1));
  if (u32((c & 0xFC00) == 0xDC00) & u32(index - 2 >= 0)) {
    let c1 = <u32>load<u16>(buffer + (index - 2 << 1));
    if ((c1 & 0xFC00) == 0xD800) {
      return ((c1 & 0x3FF) << 10) + (c & 0x3FF) + 0x10000;
    }
  }
  return (c & 0xF800) == 0xD800 ? 0xFFFD : c;
}

// Search routine for two-staged lookup tables
function stagedBinaryLookup(table: usize, c: u32): bool {
  return <bool>((load<u8>(table + (<u32>load<u8>(table + (c >>> 8)) << 5) + ((c & 255) >> 3)) >>> (c & 7)) & 1);
}

export function compareImpl(str1: string, index1: usize, str2: string, index2: usize, len: usize): i32 {
  var ptr1 = changetype<usize>(str1) + (index1 << 1);
  var ptr2 = changetype<usize>(str2) + (index2 << 1);
  if (ASC_SHRINK_LEVEL < 2) {
    if (len >= 4 && !((ptr1 & 7) | (ptr2 & 7))) {
      do {
        if (load<u64>(ptr1) != load<u64>(ptr2)) break;
        ptr1 += 8;
        ptr2 += 8;
        len  -= 4;
      } while (len >= 4);
    }
  }
  while (len--) {
    let a = <i32>load<u16>(ptr1);
    let b = <i32>load<u16>(ptr2);
    if (a != b) return a - b;
    ptr1 += 2;
    ptr2 += 2;
  }
  return 0;
}

// @ts-ignore: decorator
@inline
export function toLower8(c: u32): u32 {
  if (ASC_SHRINK_LEVEL > 0) {
    return c | u32(isUpper8(c)) << 5;
  } else {
    return <u32>load<u8>(LOWER127 + c);
  }
}

// @ts-ignore: decorator
@inline
export function toUpper8(c: u32): u32 {
  if (ASC_SHRINK_LEVEL > 0) {
    return c & ~(u32(isLower8(c)) << 5);
  } else {
    return <u32>load<u8>(UPPER127 + c);
  }
}

/** Parses a string to an integer (usually), using the specified radix. */
export function strtol<T>(str: string, radix: i32 = 0): T {
  var len = str.length;
  if (!len) {
    if (isFloat<T>()) {
      // @ts-ignore: cast
      return <T>NaN;
    } else {
      // @ts-ignore: cast
      return <T>0;
    }
  }

  var ptr = changetype<usize>(str) /* + HEAD -> offset */;
  var code = <u32>load<u16>(ptr);

  // trim white spaces
  while (isSpace(code)) {
    code = <u32>load<u16>(ptr += 2);
    --len;
  }
  // determine sign
  // @ts-ignore
  var sign: T = 1;
  if (code == CharCode.MINUS || code == CharCode.PLUS) {
    if (!--len) {
      if (isFloat<T>()) {
        // @ts-ignore: cast
        return <T>NaN;
      } else {
        // @ts-ignore: cast
        return <T>0;
      }
    }
    if (code == CharCode.MINUS) {
      // @ts-ignore: type
      sign = -1;
    }
    code = <u32>load<u16>(ptr += 2);
  }

  // See https://tc39.es/ecma262/#sec-parseint-string-radix
  if (radix) {
    if (radix < 2 || radix > 36) {
      if (isFloat<T>()) {
        // @ts-ignore: cast
        return <T>NaN;
      } else {
        // @ts-ignore: cast
        return <T>0;
      }
    }
    // handle case as parseInt("0xFF", 16) by spec
    if (radix == 16) {
      if (
        len > 2 &&
        code == CharCode._0 &&
        (<u32>load<u16>(ptr, 2) | 32) == CharCode.x
      ) {
        ptr += 4; len -= 2;
      }
    }
  } else {
    // determine radix by literal prefix
    if (code == CharCode._0 && len > 2) {
      switch (<u32>load<u16>(ptr, 2) | 32) {
        case CharCode.b: {
          ptr += 4; len -= 2;
          radix = 2;
          break;
        }
        case CharCode.o: {
          ptr += 4; len -= 2;
          radix = 8;
          break;
        }
        case CharCode.x: {
          ptr += 4; len -= 2;
          radix = 16;
          break;
        }
      }
    }
    if (!radix) radix = 10;
  }

  // calculate value
  // @ts-ignore: type
  var num: T = 0;
  while (len--) {
    code = <u32>load<u16>(ptr);
    if (code - CharCode._0 < 10) {
      code -= CharCode._0;
    } else if (code - CharCode.A <= <u32>(CharCode.Z - CharCode.A)) {
      code -= CharCode.A - 10;
    } else if (code - CharCode.a <= <u32>(CharCode.z - CharCode.a)) {
      code -= CharCode.a - 10;
    }
    if (code >= <u32>radix) {
      if (!num) {
        if (isFloat<T>()) {
          // @ts-ignore: cast
          return <T>NaN;
        } else {
          // @ts-ignore: cast
          return <T>0;
        }
      }
      break;
    }
    // @ts-ignore: type
    num = num * radix + code;
    ptr += 2;
  }
  // @ts-ignore: type
  return sign * num;
}

export function strtod(str: string): f64 {
  var len = str.length;
  if (!len) return NaN;

  var ptr  = changetype<usize>(str);
  var code = <u32>load<u16>(ptr);

  var sign = 1.0;
  // skip white spaces
  while (len && isSpace(code)) {
    code = <u32>load<u16>(ptr += 2);
    --len;
  }
  if (!len) return NaN;

  // try parse '-' or '+'
  if (code == CharCode.MINUS) {
    if (!--len) return NaN;
    code = <u32>load<u16>(ptr += 2);
    sign = -1;
  } else if (code == CharCode.PLUS) {
    if (!--len) return NaN;
    code = <u32>load<u16>(ptr += 2);
  }

  // try parse Infinity
  if (len >= 8 && code == CharCode.I) {
    if (
      load<u64>(ptr, 0) == 0x690066006E0049 && // ifnI
      load<u64>(ptr, 8) == 0x7900740069006E    // ytin
    ) {
      return Infinity * sign;
    }
    return NaN;
  }
  // validate next symbol
  if (code != CharCode.DOT && <u32>(code - CharCode._0) >= 10) {
    return NaN;
  }
  var savedPtr = ptr;
  // skip zeros
  while (code == CharCode._0) {
    code = <u32>load<u16>(ptr += 2);
    --len;
  }
  if (len <= 0) return 0;
  const capacity = 19; // int(64 * 0.3010)
  var pointed = false;
  var consumed = 0;
  var position = 0;
  var x: u64 = 0;
  if (code == CharCode.DOT) {
    let noDigits = !(savedPtr - ptr);
    ptr += 2; --len;
    if (!len && noDigits) return NaN;
    for (pointed = true; (code = <u32>load<u16>(ptr)) == CharCode._0; --position, ptr += 2) --len;
    if (len <= 0) return 0;
    if (!position && noDigits && code - CharCode._0 >= 10) return NaN;
  }
  for (let digit = code - CharCode._0; digit < 10 || (code == CharCode.DOT && !pointed); digit = code - CharCode._0) {
    if (digit < 10) {
      x = consumed < capacity ? 10 * x + digit : x | u64(!!digit);
      ++consumed;
    } else {
      position = consumed;
      pointed = true;
    }
    if (!--len) break;
    code = <u32>load<u16>(ptr += 2);
  }

  if (!pointed) position = consumed;
  return copysign<f64>(scientific(x, position - min(capacity, consumed) + parseExp(ptr, len)), sign);
}

export function joinBooleanArray(dataStart: usize, length: i32, separator: string): string {
  var lastIndex = length - 1;
  if (lastIndex < 0) return "";
  if (!lastIndex) return select("true", "false", load<bool>(dataStart));

  var sepLen = separator.length;
  var valueLen = 5; // max possible length of element len("false")
  var estLen = (valueLen + sepLen) * lastIndex + valueLen;
  var result = changetype<string>(__new(estLen << 1, idof<string>()));
  var offset = 0;
  var value: bool;
  for (let i = 0; i < lastIndex; ++i) {
    value = load<bool>(dataStart + i);
    valueLen = 4 + i32(!value);
    memory.copy(
      changetype<usize>(result) + (<usize>offset << 1),
      changetype<usize>(select("true", "false", value)),
      <usize>valueLen << 1
    );
    offset += valueLen;
    if (sepLen) {
      memory.copy(
        changetype<usize>(result) + (<usize>offset << 1),
        changetype<usize>(separator),
        <usize>sepLen << 1
      );
      offset += sepLen;
    }
  }
  value = load<bool>(dataStart + <usize>lastIndex);
  valueLen = 4 + i32(!value);
  memory.copy(
    changetype<usize>(result) + (<usize>offset << 1),
    changetype<usize>(select("true", "false", value)),
    valueLen << 1
  );
  offset += valueLen;

  if (estLen > offset) return result.substring(0, offset);
  return result;
}

export function joinIntegerArray<T>(dataStart: usize, length: i32, separator: string): string {
  var lastIndex = length - 1;
  if (lastIndex < 0) return "";
  if (!lastIndex) {
    let value = load<T>(dataStart);
    if (isSigned<T>()) {
      if (sizeof<T>() <= 4) {
        // @ts-ignore: type
        return changetype<string>(itoa32(<i32>value, 10));
      } else {
        // @ts-ignore: type
        return changetype<string>(itoa64(<i32>value, 10));
      }
    } else {
      if (sizeof<T>() <= 4) {
        // @ts-ignore: type
        return changetype<string>(utoa32(<u32>value, 10));
      } else {
        // @ts-ignore: type
        return changetype<string>(utoa64(<u64>value, 10));
      }
    }
  }

  var sepLen = separator.length;
  const valueLen = (sizeof<T>() <= 4 ? 10 : 20) + i32(isSigned<T>());
  var estLen = (valueLen + sepLen) * lastIndex + valueLen;
  var result = changetype<string>(__new(estLen << 1, idof<string>()));
  var offset = 0;
  var value: T;
  for (let i = 0; i < lastIndex; ++i) {
    value = load<T>(dataStart + (<usize>i << alignof<T>()));
    // @ts-ignore: type
    offset += itoa_buffered<T>(changetype<usize>(result) + (<usize>offset << 1), value);
    if (sepLen) {
      memory.copy(
        changetype<usize>(result) + (<usize>offset << 1),
        changetype<usize>(separator),
        <usize>sepLen << 1
      );
      offset += sepLen;
    }
  }
  value = load<T>(dataStart + (<usize>lastIndex << alignof<T>()));
  // @ts-ignore: type
  offset += itoa_buffered<T>(changetype<usize>(result) + (<usize>offset << 1), value);
  if (estLen > offset) return result.substring(0, offset);
  return result;
}

export function joinFloatArray<T>(dataStart: usize, length: i32, separator: string): string {
  var lastIndex = length - 1;
  if (lastIndex < 0) return "";
  if (!lastIndex) {
    return changetype<string>(dtoa(
      // @ts-ignore: type
      load<T>(dataStart))
    );
  }

  const valueLen = MAX_DOUBLE_LENGTH;
  var sepLen = separator.length;
  var estLen = (valueLen + sepLen) * lastIndex + valueLen;
  var result = changetype<string>(__new(estLen << 1, idof<string>()));
  var offset = 0;
  var value: T;
  for (let i = 0; i < lastIndex; ++i) {
    value = load<T>(dataStart + (<usize>i << alignof<T>()));
    // @ts-ignore: type
    offset += dtoa_buffered(changetype<usize>(result) + (<usize>offset << 1), value);
    if (sepLen) {
      memory.copy(
        changetype<usize>(result) + (<usize>offset << 1),
        changetype<usize>(separator),
        <usize>sepLen << 1
      );
      offset += sepLen;
    }
  }
  value = load<T>(dataStart + (<usize>lastIndex << alignof<T>()));
  // @ts-ignore: type
  offset += dtoa_buffered(changetype<usize>(result) + (<usize>offset << 1), value);
  if (estLen > offset) return result.substring(0, offset);
  return result;
}

export function joinStringArray(dataStart: usize, length: i32, separator: string): string {
  var lastIndex = length - 1;
  if (lastIndex < 0) return "";
  if (!lastIndex) {
    // @ts-ignore: type
    return load<string>(dataStart) || "";
  }
  var estLen = 0;
  var value: string;
  for (let i = 0; i < length; ++i) {
    value = load<string>(dataStart + (<usize>i << alignof<string>()));
    // @ts-ignore: type
    if (value !== null) estLen += value.length;
  }
  var offset = 0;
  var sepLen = separator.length;
  var result = changetype<string>(__new((estLen + sepLen * lastIndex) << 1, idof<string>()));
  for (let i = 0; i < lastIndex; ++i) {
    value = load<string>(dataStart + (<usize>i << alignof<string>()));
    if (value !== null) {
      let valueLen = value.length;
      memory.copy(
        changetype<usize>(result) + (<usize>offset << 1),
        changetype<usize>(value),
        <usize>valueLen << 1
      );
      offset += valueLen;
    }
    if (sepLen) {
      memory.copy(
        changetype<usize>(result) + (<usize>offset << 1),
        changetype<usize>(separator),
        <usize>sepLen << 1
      );
      offset += sepLen;
    }
  }
  value = load<string>(dataStart + (<usize>lastIndex << alignof<string>()));
  if (value !== null) {
    memory.copy(
      changetype<usize>(result) + (<usize>offset << 1),
      changetype<usize>(value),
      <usize>value.length << 1
    );
  }
  return result;
}

export function joinReferenceArray<T>(dataStart: usize, length: i32, separator: string): string {
  var lastIndex = length - 1;
  if (lastIndex < 0) return "";
  var value: T;
  if (!lastIndex) {
    value = load<T>(dataStart);
    // @ts-ignore: type
    return value !== null ? value.toString() : "";
  }
  var result = "";
  var sepLen = separator.length;
  for (let i = 0; i < lastIndex; ++i) {
    value = load<T>(dataStart + (<usize>i << alignof<T>()));
    // @ts-ignore: type
    if (value !== null) result += value.toString();
    if (sepLen) result += separator;
  }
  value = load<T>(dataStart + (<usize>lastIndex << alignof<T>()));
  // @ts-ignore: type
  if (value !== null) result += value.toString();
  return result;
}

// @ts-ignore: decorator
@inline
function scientific(significand: u64, exp: i32): f64 {
  if (!significand || exp < -342) return 0;
  if (exp > 308) return Infinity;
  // Try use fast path
  // Use fast path for string-to-double conversion if possible
  // see http://www.exploringbinary.com/fast-path-decimal-to-floating-point-conversion
  // Simple integer
  var significandf = <f64>significand;
  if (!exp) return significandf;
  if (exp > 22 && exp <= 22 + 15) {
    significandf *= pow10(exp - 22);
    exp = 22;
  }
  if (significand <= 9007199254740991 && abs(exp) <= 22) {
    if (exp > 0) return significandf * pow10(exp);
    return significandf / pow10(-exp);
  } else if (exp < 0) {
    return scaledown(significand, exp);
  } else {
    return scaleup(significand, exp);
  }
}

// Adopted from metallic lib:
// https://github.com/jdh8/metallic/blob/master/src/stdlib/parse/scientific.h
// @ts-ignore: decorator
@inline
function scaledown(significand: u64, exp: i32): f64 {
  const denom: u64 = 6103515625; // 1e14 * 0x1p-14
  const scale = reinterpret<f64>(0x3F06849B86A12B9B); // 1e-14 * 0x1p32

  var shift = clz(significand);
  significand <<= shift;
  shift = exp - shift;

  for (; exp <= -14; exp += 14) {
    let q = significand / denom;
    let r = significand % denom;
    let s = clz(q);
    significand = (q << s) + <u64>nearest(scale * <f64>(r << (s - 18)));
    shift -= s;
  }
  var b = <u64>ipow32(5, -exp);
  var q = significand / b;
  var r = significand % b;
  var s = clz(q);
  significand = (q << s) + <u64>(reinterpret<f64>(reinterpret<u64>(<f64>r) + (s << 52)) / <f64>b);
  shift -= s;

  return NativeMath.scalbn(<f64>significand, <i32>shift);
}

// Adopted from metallic lib:
// https://github.com/jdh8/metallic/blob/master/src/stdlib/parse/scientific.h
// @ts-ignore: decorator
@inline
function scaleup(significand: u64, exp: i32): f64 {
  const coeff: u32 = 1220703125; // 1e13 * 0x1p-13;
  var shift = ctz(significand);
  significand >>= shift;
  shift += exp;

  __fixmulShift = shift;
  for (; exp >= 13; exp -= 13) {
    significand = fixmul(significand, coeff);
  }
  significand = fixmul(significand, <u32>ipow32(5, exp));
  shift = __fixmulShift;
  return NativeMath.scalbn(<f64>significand, <i32>shift);
}

// Adopted from metallic lib:
// https://github.com/jdh8/metallic/blob/master/src/stdlib/parse/scientific.h
// @ts-ignore: decorator
@inline
function parseExp(ptr: usize, len: i32): i32 {
  var sign = 1, magnitude = 0;
  var code = <u32>load<u16>(ptr);
  // check code is 'e' or 'E'
  if ((code | 32) != CharCode.e) return 0;

  if (!--len) return 0;
  code = <u32>load<u16>(ptr += 2);
  if (code == CharCode.MINUS) {
    if (!--len) return 0;
    code = <u32>load<u16>(ptr += 2);
    sign = -1;
  } else if (code == CharCode.PLUS) {
    if (!--len) return 0;
    code = <u32>load<u16>(ptr += 2);
  }
  // skip zeros
  while (code == CharCode._0) {
    if (!--len) return 0;
    code = <u32>load<u16>(ptr += 2);
  }
  for (let digit: u32 = code - CharCode._0; len && digit < 10; digit = code - CharCode._0) {
    if (magnitude >= 3200) return sign * 3200;
    magnitude = 10 * magnitude + digit;
    code = <u32>load<u16>(ptr += 2);
    --len;
  }
  return sign * magnitude;
}

// @ts-ignore: decorator
@lazy var __fixmulShift: u64 = 0;

// Adopted from metallic lib:
// https://github.com/jdh8/metallic/blob/master/src/stdlib/parse/scientific.h
// @ts-ignore: decorator
@inline
function fixmul(a: u64, b: u32): u64 {
  var low  = (a & 0xFFFFFFFF) * b;
  var high = (a >> 32) * b + (low >> 32);
  var overflow = <u32>(high >> 32);
  var space = clz(overflow);
  var revspace: u64 = 32 - space;
  __fixmulShift += revspace;
  return (high << space | (low & 0xFFFFFFFF) >> revspace) + (low << space >> 31 & 1);
}

// @ts-ignore: decorator
@inline
function pow10(n: i32): f64 {
  // argument `n` should bounds in [0, 22] range
  return load<f64>(POWERS10 + (n << alignof<f64>()));
}
