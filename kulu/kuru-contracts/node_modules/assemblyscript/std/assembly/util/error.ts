// Common error messages for use across the standard library. Keeping error messages compact
// and reusing them where possible ensures minimal static data in binaries.

// @ts-ignore: decorator
@lazy @inline
export const E_INDEXOUTOFRANGE: string = "Index out of range";

// @ts-ignore: decorator
@lazy @inline
export const E_VALUEOUTOFRANGE: string = "Value out of range";

// @ts-ignore: decorator
@lazy @inline
export const E_INVALIDLENGTH: string = "Invalid length";

// @ts-ignore: decorator
@lazy @inline
export const E_ILLEGALGENTYPE: string = "Illegal generic type";

// @ts-ignore: decorator
@lazy @inline
export const E_EMPTYARRAY: string = "Array is empty";

// @ts-ignore: decorator
@lazy @inline
export const E_HOLEYARRAY: string = "Element type must be nullable if array is holey";

// @ts-ignore: decorator
@lazy @inline
export const E_NOTIMPLEMENTED: string = "Not implemented";

// @ts-ignore: decorator
@lazy @inline
export const E_KEYNOTFOUND: string = "Key does not exist";

// @ts-ignore: decorator
@lazy @inline
export const E_ALLOCATION_TOO_LARGE: string = "Allocation too large";

// @ts-ignore: decorator
@lazy @inline
export const E_ALREADY_PINNED: string = "Object already pinned";

// @ts-ignore: decorator
@lazy @inline
export const E_NOT_PINNED: string = "Object is not pinned";

// @ts-ignore: decorator
@lazy @inline
export const E_URI_MALFORMED: string = "URI malformed";

// @ts-ignore: decorator
@lazy @inline
export const E_INVALIDDATE: string = "Invalid Date";

// @ts-ignore: decorator
@lazy @inline
export const E_UNPAIRED_SURROGATE: string = "Unpaired surrogate";
