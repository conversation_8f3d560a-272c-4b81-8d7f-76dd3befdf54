// Total tables size: ~5 kb (usually compressed to ~4 kb)
// See: https://git.musl-libc.org/cgit/musl/tree/src/ctype/casemap.h

// @ts-ignore: decorator
@lazy @inline const TAB = memory.data<u8>([
  7, 8, 9, 10, 11, 12, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  13, 6, 6, 14, 6, 6, 6, 6, 6, 6, 6, 6, 15, 16, 17, 18,
  6, 19, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 20, 21, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 22, 23, 6, 6, 6, 24, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 25,
  6, 6, 6, 6, 26, 6, 6, 6, 6, 6, 6, 6, 27, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 28, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 29, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 30, 6, 6, 6, 6, 6, 6,
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36,
  43, 43, 43, 43, 43, 43, 43, 43, 1, 0, 84, 86, 86, 86, 86, 86,
  86, 86, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 43, 43, 43, 43, 43, 43,
  43, 7, 43, 43, 91, 86, 86, 86, 86, 86, 86, 86, 74, 86, 86, 5,
  49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80,
  36, 80, 121, 49, 80, 49, 80, 49, 56, 80, 49, 80, 49, 80, 49, 80,
  49, 80, 49, 80, 49, 80, 49, 80, 78, 49, 2, 78, 13, 13, 78, 3,
  78, 0, 36, 110, 0, 78, 49, 38, 110, 81, 78, 36, 80, 78, 57, 20,
  129, 27, 29, 29, 83, 49, 80, 49, 80, 13, 49, 80, 49, 80, 49, 80,
  27, 83, 36, 80, 49, 2, 92, 123, 92, 123, 92, 123, 92, 123, 92, 123,
  20, 121, 92, 123, 92, 123, 92, 45, 43, 73, 3, 72, 3, 120, 92, 123,
  20, 0, 150, 10, 1, 43, 40, 6, 6, 0, 42, 6, 42, 42, 43, 7,
  187, 181, 43, 30, 0, 43, 7, 43, 43, 43, 1, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 1, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 42, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 205, 70, 205, 43, 0, 37, 43, 7, 1, 6, 1, 85, 86, 86, 86,
  86, 86, 85, 86, 86, 2, 36, 129, 129, 129, 129, 129, 21, 129, 129, 129,
  0, 0, 43, 0, 178, 209, 178, 209, 178, 209, 178, 209, 0, 0, 205, 204,
  1, 0, 215, 215, 215, 215, 215, 131, 129, 129, 129, 129, 129, 129, 129, 129,
  129, 129, 172, 172, 172, 172, 172, 172, 172, 172, 172, 172, 28, 0, 0, 0,
  0, 0, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 2, 0, 0,
  49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80,
  49, 80, 78, 49, 80, 49, 80, 78, 49, 80, 49, 80, 49, 80, 49, 80,
  49, 80, 49, 80, 49, 80, 49, 2, 135, 166, 135, 166, 135, 166, 135, 166,
  135, 166, 135, 166, 135, 166, 135, 166, 42, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 0, 0, 0, 84, 86, 86, 86, 86, 86, 86, 86,
  86, 86, 86, 86, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 84, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86,
  12, 0, 12, 42, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 7, 42, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 86, 86, 108, 129, 21, 0, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 7, 108, 3, 65, 43, 43, 86, 86, 86, 86, 86, 86,
  86, 86, 86, 86, 86, 86, 86, 86, 44, 86, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 1,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 12, 108, 0, 0, 0, 0, 0, 6,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37,
  6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37,
  6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37,
  6, 37, 6, 37, 6, 37, 6, 37, 86, 122, 158, 38, 6, 37, 6, 37,
  6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 37,
  6, 37, 6, 37, 6, 37, 6, 37, 6, 37, 6, 1, 43, 43, 79, 86,
  86, 44, 43, 127, 86, 86, 57, 43, 43, 85, 86, 86, 43, 43, 79, 86,
  86, 44, 43, 127, 86, 86, 129, 55, 117, 91, 123, 92, 43, 43, 79, 86,
  86, 2, 172, 4, 0, 0, 57, 43, 43, 85, 86, 86, 43, 43, 79, 86,
  86, 44, 43, 43, 86, 86, 50, 19, 129, 87, 0, 111, 129, 126, 201, 215,
  126, 45, 129, 129, 14, 126, 57, 127, 111, 87, 0, 129, 129, 126, 21, 0,
  126, 3, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 7, 43,
  36, 43, 151, 43, 43, 43, 43, 43, 43, 43, 43, 43, 42, 43, 43, 43,
  43, 43, 86, 86, 86, 86, 86, 128, 129, 129, 129, 129, 57, 187, 42, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 1, 129, 129, 129, 129, 129, 129, 129, 129,
  129, 129, 129, 129, 129, 129, 129, 201, 172, 172, 172, 172, 172, 172, 172, 172,
  172, 172, 172, 172, 172, 172, 172, 208, 13, 0, 78, 49, 2, 180, 193, 193,
  215, 215, 36, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80,
  49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80, 49, 80,
  49, 80, 49, 80, 215, 215, 83, 193, 71, 212, 215, 215, 215, 5, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 7, 1, 0, 1, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 49, 80, 49, 80, 49, 80,
  49, 80, 49, 80, 49, 80, 49, 80, 13, 0, 0, 0, 0, 0, 36, 80,
  49, 80, 49, 80, 49, 80, 49, 80, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 121, 92, 123, 92, 123, 79, 123, 92, 123, 92, 123,
  92, 123, 92, 123, 92, 123, 92, 123, 92, 123, 92, 123, 92, 123, 92, 45,
  43, 43, 121, 20, 92, 123, 92, 45, 121, 42, 92, 39, 92, 123, 92, 123,
  92, 123, 164, 0, 10, 180, 92, 123, 92, 123, 79, 3, 120, 56, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 79, 45, 43, 43, 1,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 42, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 43, 43, 43, 43, 43, 43, 43, 43, 7, 0, 72, 86, 86, 86, 86,
  86, 86, 86, 86, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 85, 86, 86, 86, 86, 86, 86,
  86, 86, 86, 86, 86, 86, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 36, 43, 43, 43, 43, 43, 43, 43, 43, 43,
  43, 43, 7, 0, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 43, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 7, 0, 0,
  0, 0, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86,
  86, 86, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43, 43,
  43, 43, 43, 43, 43, 43, 43, 43, 86, 86, 86, 86, 86, 86, 86, 86,
  86, 86, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 42, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 86, 86,
  86, 86, 86, 86, 86, 86, 86, 86, 14, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43, 85,
  86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 14, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0
]);

// @ts-ignore: decorator
@lazy @inline const RULES = memory.data<i32>([
  0x0, 0x2001, -0x2000, 0x1dbf00, 0x2e700, 0x7900,
  0x2402, 0x101, -0x100, 0x0, 0x201, -0x200,
  -0xc6ff, -0xe800, -0x78ff, -0x12c00, 0xc300, 0xd201,
  0xce01, 0xcd01, 0x4f01, 0xca01, 0xcb01, 0xcf01,
  0x6100, 0xd301, 0xd101, 0xa300, 0xd501, 0x8200,
  0xd601, 0xda01, 0xd901, 0xdb01, 0x3800, 0x3,
  -0x4f00, -0x60ff, -0x37ff, 0x242802, 0x0, 0x101,
  -0x100, -0xcd00, -0xda00, -0x81ff, 0x2a2b01, -0xa2ff,
  0x2a2801, 0x2a3f00, -0xc2ff, 0x4501, 0x4701, 0x2a1f00,
  0x2a1c00, 0x2a1e00, -0xd200, -0xce00, -0xca00, -0xcb00,
  0xa54f00, 0xa54b00, -0xcf00, 0xa52800, 0xa54400, -0xd100,
  -0xd300, 0x29f700, 0xa54100, 0x29fd00, -0xd500, -0xd600,
  0x29e700, 0xa54300, 0xa52a00, -0x4500, -0xd900, -0x4700,
  -0xdb00, 0xa51500, 0xa51200, 0x4c2402, 0x0, 0x2001,
  -0x2000, 0x101, -0x100, 0x5400, 0x7401, 0x2601,
  0x2501, 0x4001, 0x3f01, -0x2600, -0x2500, -0x1f00,
  -0x4000, -0x3f00, 0x801, -0x3e00, -0x3900, -0x2f00,
  -0x3600, -0x800, -0x5600, -0x5000, 0x700, -0x7400,
  -0x3bff, -0x6000, -0x6ff, 0x701a02, 0x101, -0x100,
  0x2001, -0x2000, 0x5001, 0xf01, -0xf00, 0x0,
  0x3001, -0x3000, 0x101, -0x100, 0x0, 0xbc000,
  0x1c6001, 0x0, 0x97d001, 0x801, -0x800, 0x8a0502,
  0x0, -0xbbfff, -0x186200, 0x89c200, -0x182500, -0x186e00,
  -0x186d00, -0x186400, -0x186300, -0x185c00, 0x0, 0x8a3800,
  0x8a0400, 0xee600, 0x101, -0x100, 0x0, -0x3b00,
  -0x1dbeff, 0x8f1d02, 0x800, -0x7ff, 0x0, 0x5600,
  -0x55ff, 0x4a00, 0x6400, 0x8000, 0x7000, 0x7e00,
  0x900, -0x49ff, -0x8ff, -0x1c2500, -0x63ff, -0x6fff,
  -0x7fff, -0x7dff, 0xac0502, 0x0, 0x1001, -0x1000,
  0x1c01, 0x101, -0x1d5cff, -0x20beff, -0x2045ff, -0x1c00,
  0xb10b02, 0x101, -0x100, 0x3001, -0x3000, 0x0,
  -0x29f6ff, -0xee5ff, -0x29e6ff, -0x2a2b00, -0x2a2800, -0x2a1bff,
  -0x29fcff, -0x2a1eff, -0x2a1dff, -0x2a3eff, 0x0, -0x1c6000,
  0x0, 0x101, -0x100, 0xbc0c02, 0x0, 0x101,
  -0x100, -0xa543ff, 0x3a001, -0x8a03ff, -0xa527ff, 0x3000,
  -0xa54eff, -0xa54aff, -0xa540ff, -0xa511ff, -0xa529ff, -0xa514ff,
  -0x2fff, -0xa542ff, -0x8a37ff, 0x0, -0x97d000, -0x3a000,
  0x0, 0x2001, -0x2000, 0x0, 0x2801, -0x2800,
  0x0, 0x4001, -0x4000, 0x0, 0x2001, -0x2000,
  0x0, 0x2001, -0x2000, 0x0, 0x2201, -0x2200
]);

// @ts-ignore: decorator
@lazy @inline const RULE_BASES = memory.data<u8>([
  0, 6, 39, 81, 111, 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  124, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 131, 142, 146, 151,
  0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 196, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 198, 201, 0, 0, 0, 219, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 222,
  0, 0, 0, 0, 225, 0, 0, 0, 0, 0, 0, 0, 228, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 231, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 234, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 237, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
]);

// @ts-ignore: decorator
@lazy @inline const EXCEPTIONS = memory.data<u8>([
  48, 12,  49, 13,  120, 14,  127, 15,
  128, 16,  129, 17,  134, 18,  137, 19,
  138, 19,  142, 20,  143, 21,  144, 22,
  147, 19,  148, 23,  149, 24,  150, 25,
  151, 26,  154, 27,  156, 25,  157, 28,
  158, 29,  159, 30,  166, 31,  169, 31,
  174, 31,  177, 32,  178, 32,  183, 33,
  191, 34,  197, 35,  200, 35,  203, 35,
  221, 36,  242, 35,  246, 37,  247, 38,
  32, 45,  58, 46,  61, 47,  62, 48,
  63, 49,  64, 49,  67, 50,  68, 51,
  69, 52,  80, 53,  81, 54,  82, 55,
  83, 56,  84, 57,  89, 58,  91, 59,
  92, 60,  97, 61,  99, 62,  101, 63,
  102, 64,  104, 65,  105, 66,  106, 64,
  107, 67,  108, 68,  111, 66,  113, 69,
  114, 70,  117, 71,  125, 72,  130, 73,
  135, 74,  137, 75,  138, 76,  139, 76,
  140, 77,  146, 78,  157, 79,  158, 80,
  69, 87,  123, 29,  124, 29,  125, 29,
  127, 88,  134, 89,  136, 90,  137, 90,
  138, 90,  140, 91,  142, 92,  143, 92,
  172, 93,  173, 94,  174, 94,  175, 94,
  194, 95,  204, 96,  205, 97,  206, 97,
  207, 98,  208, 99,  209, 100,  213, 101,
  214, 102,  215, 103,  240, 104,  241, 105,
  242, 106,  243, 107,  244, 108,  245, 109,
  249, 110,  253, 45,  254, 45,  255, 45,
  80, 105,  81, 105,  82, 105,  83, 105,
  84, 105,  85, 105,  86, 105,  87, 105,
  88, 105,  89, 105,  90, 105,  91, 105,
  92, 105,  93, 105,  94, 105,  95, 105,
  130, 0,  131, 0,  132, 0,  133, 0,
  134, 0,  135, 0,  136, 0,  137, 0,
  192, 117,  207, 118,  128, 137,  129, 138,
  130, 139,  133, 140,  134, 141,  112, 157,
  113, 157,  118, 158,  119, 158,  120, 159,
  121, 159,  122, 160,  123, 160,  124, 161,
  125, 161,  179, 162,  186, 163,  187, 163,
  188, 164,  190, 165,  195, 162,  204, 164,
  218, 166,  219, 166,  229, 106,  234, 167,
  235, 167,  236, 110,  243, 162,  248, 168,
  249, 168,  250, 169,  251, 169,  252, 164,
  38, 176,  42, 177,  43, 178,  78,  179,
  132,  8,  98, 186,  99, 187,  100, 188,
  101, 189,  102, 190,  109, 191,  110, 192,
  111, 193,  112, 194,  126, 195,  127, 195,
  125, 207,  141, 208,  148, 209,  171, 210,
  172, 211,  173, 212,  176, 213,  177, 214,
  178, 215,  196, 216,  197, 217,  198, 218
]);

/* Special Case Mappings
 * See: https://unicode.org/Public/UNIDATA/SpecialCasing.txt
 */

/*
@lazy @inline
const SPECIALS_LOWER: StaticArray<u16> = [
  0x0130,  0x0069, 0x0307, 0x0000,
];
*/

// @ts-ignore: decorator
@lazy @inlne
export const SPECIALS_UPPER: StaticArray<u16> = [
  // String#toUpperCase needs .length
  0x00DF,  0x0053, 0x0053, 0x0000,
  0x0149,  0x02BC, 0x004E, 0x0000,
  0x01F0,  0x004A, 0x030C, 0x0000,
  0x0390,  0x0399, 0x0308, 0x0301,
  0x03B0,  0x03A5, 0x0308, 0x0301,
  0x0587,  0x0535, 0x0552, 0x0000,
  0x1E96,  0x0048, 0x0331, 0x0000,
  0x1E97,  0x0054, 0x0308, 0x0000,
  0x1E98,  0x0057, 0x030A, 0x0000,
  0x1E99,  0x0059, 0x030A, 0x0000,
  0x1E9A,  0x0041, 0x02BE, 0x0000,
  0x1F50,  0x03A5, 0x0313, 0x0000,
  0x1F52,  0x03A5, 0x0313, 0x0300,
  0x1F54,  0x03A5, 0x0313, 0x0301,
  0x1F56,  0x03A5, 0x0313, 0x0342,
  0x1F80,  0x1F08, 0x0399, 0x0000,
  0x1F81,  0x1F09, 0x0399, 0x0000,
  0x1F82,  0x1F0A, 0x0399, 0x0000,
  0x1F83,  0x1F0B, 0x0399, 0x0000,
  0x1F84,  0x1F0C, 0x0399, 0x0000,
  0x1F85,  0x1F0D, 0x0399, 0x0000,
  0x1F86,  0x1F0E, 0x0399, 0x0000,
  0x1F87,  0x1F0F, 0x0399, 0x0000,
  0x1F88,  0x1F08, 0x0399, 0x0000,
  0x1F89,  0x1F09, 0x0399, 0x0000,
  0x1F8A,  0x1F0A, 0x0399, 0x0000,
  0x1F8B,  0x1F0B, 0x0399, 0x0000,
  0x1F8C,  0x1F0C, 0x0399, 0x0000,
  0x1F8D,  0x1F0D, 0x0399, 0x0000,
  0x1F8E,  0x1F0E, 0x0399, 0x0000,
  0x1F8F,  0x1F0F, 0x0399, 0x0000,
  0x1F90,  0x1F28, 0x0399, 0x0000,
  0x1F91,  0x1F29, 0x0399, 0x0000,
  0x1F92,  0x1F2A, 0x0399, 0x0000,
  0x1F93,  0x1F2B, 0x0399, 0x0000,
  0x1F94,  0x1F2C, 0x0399, 0x0000,
  0x1F95,  0x1F2D, 0x0399, 0x0000,
  0x1F96,  0x1F2E, 0x0399, 0x0000,
  0x1F97,  0x1F2F, 0x0399, 0x0000,
  0x1F98,  0x1F28, 0x0399, 0x0000,
  0x1F99,  0x1F29, 0x0399, 0x0000,
  0x1F9A,  0x1F2A, 0x0399, 0x0000,
  0x1F9B,  0x1F2B, 0x0399, 0x0000,
  0x1F9C,  0x1F2C, 0x0399, 0x0000,
  0x1F9D,  0x1F2D, 0x0399, 0x0000,
  0x1F9E,  0x1F2E, 0x0399, 0x0000,
  0x1F9F,  0x1F2F, 0x0399, 0x0000,
  0x1FA0,  0x1F68, 0x0399, 0x0000,
  0x1FA1,  0x1F69, 0x0399, 0x0000,
  0x1FA2,  0x1F6A, 0x0399, 0x0000,
  0x1FA3,  0x1F6B, 0x0399, 0x0000,
  0x1FA4,  0x1F6C, 0x0399, 0x0000,
  0x1FA5,  0x1F6D, 0x0399, 0x0000,
  0x1FA6,  0x1F6E, 0x0399, 0x0000,
  0x1FA7,  0x1F6F, 0x0399, 0x0000,
  0x1FA8,  0x1F68, 0x0399, 0x0000,
  0x1FA9,  0x1F69, 0x0399, 0x0000,
  0x1FAA,  0x1F6A, 0x0399, 0x0000,
  0x1FAB,  0x1F6B, 0x0399, 0x0000,
  0x1FAC,  0x1F6C, 0x0399, 0x0000,
  0x1FAD,  0x1F6D, 0x0399, 0x0000,
  0x1FAE,  0x1F6E, 0x0399, 0x0000,
  0x1FAF,  0x1F6F, 0x0399, 0x0000,
  0x1FB2,  0x1FBA, 0x0399, 0x0000,
  0x1FB3,  0x0391, 0x0399, 0x0000,
  0x1FB4,  0x0386, 0x0399, 0x0000,
  0x1FB6,  0x0391, 0x0342, 0x0000,
  0x1FB7,  0x0391, 0x0342, 0x0399,
  0x1FBC,  0x0391, 0x0399, 0x0000,
  0x1FC2,  0x1FCA, 0x0399, 0x0000,
  0x1FC3,  0x0397, 0x0399, 0x0000,
  0x1FC4,  0x0389, 0x0399, 0x0000,
  0x1FC6,  0x0397, 0x0342, 0x0000,
  0x1FC7,  0x0397, 0x0342, 0x0399,
  0x1FCC,  0x0397, 0x0399, 0x0000,
  0x1FD2,  0x0399, 0x0308, 0x0300,
  0x1FD3,  0x0399, 0x0308, 0x0301,
  0x1FD6,  0x0399, 0x0342, 0x0000,
  0x1FD7,  0x0399, 0x0308, 0x0342,
  0x1FE2,  0x03A5, 0x0308, 0x0300,
  0x1FE3,  0x03A5, 0x0308, 0x0301,
  0x1FE4,  0x03A1, 0x0313, 0x0000,
  0x1FE6,  0x03A5, 0x0342, 0x0000,
  0x1FE7,  0x03A5, 0x0308, 0x0342,
  0x1FF2,  0x1FFA, 0x0399, 0x0000,
  0x1FF3,  0x03A9, 0x0399, 0x0000,
  0x1FF4,  0x038F, 0x0399, 0x0000,
  0x1FF6,  0x03A9, 0x0342, 0x0000,
  0x1FF7,  0x03A9, 0x0342, 0x0399,
  0x1FFC,  0x03A9, 0x0399, 0x0000,
  0xFB00,  0x0046, 0x0046, 0x0000,
  0xFB01,  0x0046, 0x0049, 0x0000,
  0xFB02,  0x0046, 0x004C, 0x0000,
  0xFB03,  0x0046, 0x0046, 0x0049,
  0xFB04,  0x0046, 0x0046, 0x004C,
  0xFB05,  0x0053, 0x0054, 0x0000,
  0xFB06,  0x0053, 0x0054, 0x0000,
  0xFB13,  0x0544, 0x0546, 0x0000,
  0xFB14,  0x0544, 0x0535, 0x0000,
  0xFB15,  0x0544, 0x053B, 0x0000,
  0xFB16,  0x054E, 0x0546, 0x0000,
  0xFB17,  0x0544, 0x053D, 0x0000
];

// @ts-ignore: decorator
@lazy @inline const MT = memory.data<i32>([
  2048, 342, 57
]);

// Special binary search routine for Special Casing Tables
// @ts-ignore: decorator
@inline
export function bsearch(key: u32, ptr: usize, max: i32): i32 {
  var min = 0;
  while (min <= max) {
    let mid = (min + max) >>> 3 << 2;
    let cmp = load<u16>(ptr + (mid << alignof<u16>())) - key;
    if (cmp == 0) return mid; // found
    else if (cmp >>> 31) min = mid + 4; // < 0
    else max = mid - 4; // > 0
  }
  return -1; // not found
}

// See: https://git.musl-libc.org/cgit/musl/tree/src/ctype/towctrans.c
export function casemap(c: u32, dir: i32): i32 {
  // if (c >= 0x20000) return c;
  var c0 = c as i32;
  var b = c >> 8;
  c &= 255;

  var x = c / 3;
  var y = c % 3;

  /* lookup entry in two-level base-6 table */
  // v = tab[(tab[b] as i32) * 86 + x] as u32;
  var v = <usize>load<u8>(TAB + <usize>load<u8>(TAB + b) * 86 + x);
  // v = (v * mt[y] >> 11) % 6;
  v = (v * load<i32>(MT + (y << alignof<i32>())) >> 11) % 6;
  /* use the bit vector out of the tables as an index into
   * a block-specific set of rules and decode the rule into
   * a type and a case-mapping delta. */
  // r = rules[(ruleBases[b] as u32) + v];
  var r = load<i32>(RULES + ((<usize>load<u8>(RULE_BASES + b) + v) << alignof<i32>()));
  var rt: u32 = r & 255;
  var rd: i32 = r >> 8;
  /* rules 0/1 are simple lower/upper case with a delta.
   * apply according to desired mapping direction. */
  if (rt < 2) return c0 + (rd & -(rt ^ dir));
  /* binary search. endpoints of the binary search for
   * this block are stored in the rule delta field. */
  var xn: u32 = rd & 0xff;
  var xb: u32 = rd >>> 8;
  while (xn) {
    let h = xn >> 1;
    // let t = exceptions[(xb + h) * 2 + 0] as u32;
    let t = <u32>load<u8>(EXCEPTIONS + (xb + h) * 2, 0);
    if (t == c) {
      // r = rules[exceptions[(xb + h) * 2 + 1]];
      r = load<i32>(RULES + <usize>(load<u8>(EXCEPTIONS + (xb + h) * 2, 1) << alignof<i32>()));
      rt = r & 255;
      rd = r >> 8;
      if (rt < 2) return c0 + (rd & -(rt ^ dir));
      /* Hard-coded for the four exceptional titlecase */
      return c0 + 1 - (dir << 1); // (dir ? -1 : 1);
    } else if (t > c) {
      xn = h;
    } else {
      xb += h;
      xn -= h;
    }
  }
  return c0;
}
