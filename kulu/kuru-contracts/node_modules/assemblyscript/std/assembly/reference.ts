@unmanaged
abstract class Ref {
}

@final @unmanaged
export abstract class Fun<PERSON><PERSON> extends Ref {
}

@final @unmanaged
export abstract class Externref extends Ref {
}

@final @unmanaged
export abstract class Anyref extends Ref {
}

@final @unmanaged
export abstract class Eqref extends Ref {
}

@final @unmanaged
export abstract class I31ref extends Ref {
}

@final @unmanaged
export abstract class Dataref extends Ref {
}
