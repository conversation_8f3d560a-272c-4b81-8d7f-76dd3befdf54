export declare function get(target: externref, propertyKey: externref/* , receiver: externref */): externref;
export declare function has(target: externref, propertyKey: externref): bool;
export declare function set(target: externref, propertyKey: externref, value: externref/* , receiver: externref */): externref;
export declare function apply(target: externref, thisArgument: externref, argumentsList: externref): externref;
