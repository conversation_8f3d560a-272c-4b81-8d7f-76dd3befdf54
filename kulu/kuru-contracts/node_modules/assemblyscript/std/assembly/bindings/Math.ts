export declare const E: f64;
export declare const LN2: f64;
export declare const LN10: f64;
export declare const LOG2E: f64;
export declare const LOG10E: f64;
export declare const PI: f64;
export declare const SQRT1_2: f64;
export declare const SQRT2: f64;

export declare function abs(x: f64): f64;
export declare function acos(x: f64): f64;
export declare function acosh(x: f64): f64;
export declare function asin(x: f64): f64;
export declare function asinh(x: f64): f64;
export declare function atan(x: f64): f64;
export declare function atan2(y: f64, x: f64): f64;
export declare function atanh(x: f64): f64;
export declare function cbrt(x: f64): f64;
export declare function ceil(x: f64): f64;
export declare function clz32(x: f64): f64;
export declare function cos(x: f64): f64;
export declare function cosh(x: f64): f64;
export declare function exp(x: f64): f64;
export declare function expm1(x: f64): f64;
export declare function floor(x: f64): f64;
export declare function fround(x: f64): f32;
export declare function hypot(value1: f64, value2: f64): f64; // TODO: rest
export declare function imul(a: f64, b: f64): f64;
export declare function log(x: f64): f64;
export declare function log10(x: f64): f64;
export declare function log1p(x: f64): f64;
export declare function log2(x: f64): f64;
export declare function max(value1: f64, value2: f64): f64; // TODO: rest
export declare function min(value1: f64, value2: f64): f64; // TODO: rest
export declare function pow(base: f64, exponent: f64): f64;
export declare function random(): f64;
export declare function round(x: f64): f64;
export declare function sign(x: f64): f64;
export declare function sin(x: f64): f64;
export declare function sinh(x: f64): f64;
export declare function sqrt(x: f64): f64;
export declare function tan(x: f64): f64;
export declare function tanh(x: f64): f64;
export declare function trunc(x: f64): f64;
