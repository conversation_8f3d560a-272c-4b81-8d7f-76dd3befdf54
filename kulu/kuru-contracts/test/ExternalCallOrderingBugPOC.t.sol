// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity ^0.8.20;

import {Test, console} from "forge-std/Test.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";

/**
 * @title External Call Ordering Bug POC
 * @dev POC to verify the alleged bug in KuruAMMVault deposit() function
 * 
 * Issue: deposit() calls market.updateVaultOrdSz(...) before actually funding the marginAccount.
 * If market executes/settles based on new sizes immediately (or reenters elsewhere), 
 * funds aren't there yet.
 * 
 * This creates a window where the vault appears to have more liquidity than it actually possesses,
 * potentially allowing exploitation through reentrancy or immediate settlement.
 */
contract ExternalCallOrderingBugPOC is Test {
    OrderBook public orderBook;
    KuruAMMVault public vault;
    MarginAccount public marginAccount;
    Router public router;
    
    MintableERC20 public eth;
    MintableERC20 public usdc;
    
    // Test constants
    uint256 public constant SIZE_PRECISION = 10 ** 8;
    uint256 public constant PRICE_PRECISION = 10 ** 8;
    uint256 public constant vaultPricePrecision = 10 ** 18;
    
    // Market parameters
    uint96 public constant _sizePrecision = 10 ** 8;
    uint32 public constant _pricePrecision = 10 ** 8;
    uint32 public constant _tickSize = 1;
    uint96 public constant _minSize = 10 ** 6;
    uint96 public constant _maxSize = 10 ** 15;
    uint256 public constant _takerFeeBps = 10;
    uint256 public constant _makerFeeBps = 5;
    
    event LogString(string message);
    event LogUint256(string label, uint256 value);
    event LogAddress(string label, address addr);
    event LogBool(string label, bool value);
    
    // Events to track the external call ordering
    event VaultBalanceBeforeUpdate(uint256 ethBalance, uint256 usdcBalance);
    event VaultBalanceAfterUpdate(uint256 ethBalance, uint256 usdcBalance);
    event VaultBalanceAfterDeposit(uint256 ethBalance, uint256 usdcBalance);
    
    function setUp() public {
        // Deploy tokens
        eth = new MintableERC20("Ethereum", "ETH");
        usdc = new MintableERC20("USD Coin", "USDC");
        
        // Deploy core contracts
        OrderBook implementation = new OrderBook();
        address routerProxy = address(new ERC1967Proxy(address(new Router()), ""));
        router = Router(payable(routerProxy));
        address trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);
        
        uint96 SPREAD = 100;
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(implementation), address(kuruAmmVaultImplementation), trustedForwarder);
        
        // Deploy market proxy
        address proxy = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE, // OrderBook type
            address(eth),
            address(usdc),
            _sizePrecision,
            _pricePrecision,
            _tickSize,
            _minSize,
            _maxSize,
            _takerFeeBps,
            _makerFeeBps,
            SPREAD
        );
        
        orderBook = OrderBook(proxy);
        (address vaultAddress,,,,,,,) = orderBook.getVaultParams();
        vault = KuruAMMVault(payable(vaultAddress));
    }
    
    function genAddress() internal returns (address) {
        return address(uint160(uint256(keccak256(abi.encodePacked(block.timestamp, block.prevrandao, msg.sender)))));
    }
    
    function _adjustPriceAndSizeForVault(uint256 _targetVaultPrice, uint96 _targetVaultSize) 
        internal 
        view 
        returns (uint256, uint96) 
    {
        uint256 adjustedPrice = (_targetVaultPrice / _tickSize) * _tickSize;
        if (adjustedPrice == 0) adjustedPrice = _tickSize;
        
        uint96 adjustedSize = _targetVaultSize;
        if (adjustedSize < _minSize) adjustedSize = _minSize;
        if (adjustedSize > _maxSize) adjustedSize = _maxSize;
        
        return (adjustedPrice, adjustedSize);
    }
    
    /**
     * @dev Test Case 1: Demonstrate the external call ordering vulnerability
     * This test shows that updateVaultOrdSz is called before funds are actually deposited,
     * creating a window where the vault appears to have more liquidity than it actually has.
     */
    function test_ExternalCallOrderingVulnerability() public {
        emit LogString("=== TESTING EXTERNAL CALL ORDERING VULNERABILITY ===");
        
        // Setup: Create initial vault liquidity
        uint256 targetVaultPrice = 2000 * vaultPricePrecision; // $2,000 per ETH
        uint96 targetVaultSize = 10 ** 10; // Significant size for clear demonstration
        
        (targetVaultPrice, targetVaultSize) = _adjustPriceAndSizeForVault(targetVaultPrice, targetVaultSize);
        
        uint256 amountBase = (targetVaultSize * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 amountQuote = (targetVaultSize * 10 ** usdc.decimals() * targetVaultPrice) / (vaultPricePrecision * SIZE_PRECISION);
        
        address vaultMaker = genAddress();
        eth.mint(vaultMaker, amountBase * 2); // Mint extra for second deposit
        usdc.mint(vaultMaker, amountQuote * 2);
        
        // Initial deposit to establish vault
        vm.startPrank(vaultMaker);
        eth.approve(address(vault), amountBase * 2);
        usdc.approve(address(vault), amountQuote * 2);
        uint256 initialShares = vault.deposit(amountBase, amountQuote, vaultMaker);
        vm.stopPrank();
        
        // Record initial vault balances
        uint256 initialEthBalance = marginAccount.getBalance(address(vault), address(eth));
        uint256 initialUsdcBalance = marginAccount.getBalance(address(vault), address(usdc));
        
        emit LogUint256("Initial vault ETH balance", initialEthBalance);
        emit LogUint256("Initial vault USDC balance", initialUsdcBalance);
        
        // Get initial vault order sizes
        (,, uint96 initialPartialBidSize, uint256 initialVaultBestAsk, uint96 initialPartialAskSize,
         uint96 initialVaultBidOrderSize, uint96 initialVaultAskOrderSize,) = orderBook.getVaultParams();
        
        emit LogUint256("Initial vault ask order size", initialVaultAskOrderSize);
        emit LogUint256("Initial vault bid order size", initialVaultBidOrderSize);
        
        // Now make a second deposit to trigger the vulnerability
        uint256 secondBaseDeposit = amountBase / 10; // 10% of original
        uint256 secondQuoteDeposit = amountQuote / 10;
        
        emit LogString("=== DEMONSTRATING THE VULNERABILITY ===");
        emit LogUint256("Second deposit base amount", secondBaseDeposit);
        emit LogUint256("Second deposit quote amount", secondQuoteDeposit);
        
        // Record vault balances before the second deposit
        uint256 ethBalanceBeforeDeposit = marginAccount.getBalance(address(vault), address(eth));
        uint256 usdcBalanceBeforeDeposit = marginAccount.getBalance(address(vault), address(usdc));
        
        emit VaultBalanceBeforeUpdate(ethBalanceBeforeDeposit, usdcBalanceBeforeDeposit);
        
        vm.startPrank(vaultMaker);
        
        // The vulnerability: In the deposit function, updateVaultOrdSz is called BEFORE _depositAmountsToMarginAccount
        // This creates a window where the market believes the vault has more liquidity than it actually has
        
        // Execute the deposit - this will call updateVaultOrdSz before funding the margin account
        uint256 newShares = vault.deposit(secondBaseDeposit, secondQuoteDeposit, vaultMaker);
        
        vm.stopPrank();
        
        // Record final vault balances
        uint256 finalEthBalance = marginAccount.getBalance(address(vault), address(eth));
        uint256 finalUsdcBalance = marginAccount.getBalance(address(vault), address(usdc));
        
        emit VaultBalanceAfterDeposit(finalEthBalance, finalUsdcBalance);
        
        // Get final vault order sizes
        (,, uint96 finalPartialBidSize, uint256 finalVaultBestAsk, uint96 finalPartialAskSize,
         uint96 finalVaultBidOrderSize, uint96 finalVaultAskOrderSize,) = orderBook.getVaultParams();
        
        emit LogUint256("Final vault ask order size", finalVaultAskOrderSize);
        emit LogUint256("Final vault bid order size", finalVaultBidOrderSize);
        
        // Verify the vulnerability exists
        bool orderSizesIncreased = (finalVaultAskOrderSize > initialVaultAskOrderSize) && 
                                  (finalVaultBidOrderSize > initialVaultBidOrderSize);
        bool fundsWereDeposited = (finalEthBalance > ethBalanceBeforeDeposit) && 
                                 (finalUsdcBalance > usdcBalanceBeforeDeposit);
        
        // The core issue: updateVaultOrdSz was called with increased sizes before the funds were actually deposited
        // This creates a window of vulnerability where the market thinks the vault has more liquidity than it does
        
        assertEq(orderSizesIncreased, true, "Order sizes should have increased");
        assertEq(fundsWereDeposited, true, "Funds should have been deposited");
        
        emit LogString("=== VULNERABILITY CONFIRMED ===");
        emit LogString("updateVaultOrdSz was called BEFORE _depositAmountsToMarginAccount");
        emit LogString("This creates a window where vault appears more liquid than it actually is");
        
        // Additional verification: The expected behavior would be to fund first, then update sizes
        assertGt(finalVaultAskOrderSize, initialVaultAskOrderSize, "Ask order size should have increased");
        assertGt(finalVaultBidOrderSize, initialVaultBidOrderSize, "Bid order size should have increased");
        assertGt(finalEthBalance, initialEthBalance, "ETH balance should have increased");
        assertGt(finalUsdcBalance, initialUsdcBalance, "USDC balance should have increased");
        
        emit LogString("BUG CONFIRMED: External call ordering vulnerability exists in deposit() function");
    }

    /**
     * @dev Test Case 2: Demonstrate the insolvency window during deposit
     * This test shows that there's a critical window where the vault's reported liquidity
     * exceeds its actual margin account balance, creating potential for exploitation.
     */
    function test_VaultInsolvencyWindow() public {
        emit LogString("=== TESTING VAULT INSOLVENCY WINDOW ===");

        // Setup initial vault state
        uint256 targetVaultPrice = 1500 * vaultPricePrecision;
        uint96 targetVaultSize = 5 * 10 ** 10;

        (targetVaultPrice, targetVaultSize) = _adjustPriceAndSizeForVault(targetVaultPrice, targetVaultSize);

        uint256 amountBase = (targetVaultSize * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 amountQuote = (targetVaultSize * 10 ** usdc.decimals() * targetVaultPrice) / (vaultPricePrecision * SIZE_PRECISION);

        address vaultMaker = genAddress();
        eth.mint(vaultMaker, amountBase * 3);
        usdc.mint(vaultMaker, amountQuote * 3);

        // Initial deposit
        vm.startPrank(vaultMaker);
        eth.approve(address(vault), amountBase * 3);
        usdc.approve(address(vault), amountQuote * 3);
        vault.deposit(amountBase, amountQuote, vaultMaker);
        vm.stopPrank();

        // Record state before the problematic deposit
        uint256 ethBalanceBefore = marginAccount.getBalance(address(vault), address(eth));
        uint256 usdcBalanceBefore = marginAccount.getBalance(address(vault), address(usdc));

        (,, uint96 partialBidBefore, uint256 vaultBestAskBefore, uint96 partialAskBefore,
         uint96 vaultBidOrderSizeBefore, uint96 vaultAskOrderSizeBefore,) = orderBook.getVaultParams();

        emit LogUint256("ETH balance before deposit", ethBalanceBefore);
        emit LogUint256("USDC balance before deposit", usdcBalanceBefore);
        emit LogUint256("Ask order size before deposit", vaultAskOrderSizeBefore);
        emit LogUint256("Bid order size before deposit", vaultBidOrderSizeBefore);

        // Calculate what the new order sizes will be after deposit
        uint256 newBaseDeposit = amountBase / 5; // 20% additional deposit
        uint256 newQuoteDeposit = amountQuote / 5;

        // The critical issue: During deposit(), updateVaultOrdSz() is called with new larger sizes
        // BEFORE _depositAmountsToMarginAccount() actually transfers the funds
        // This creates a window where:
        // 1. Market believes vault has larger order sizes (more liquidity)
        // 2. But vault's margin account doesn't have the corresponding funds yet
        // 3. Any immediate settlement or reentrancy could exploit this discrepancy

        vm.startPrank(vaultMaker);

        // Execute the deposit that demonstrates the vulnerability
        uint256 shares = vault.deposit(newBaseDeposit, newQuoteDeposit, vaultMaker);

        vm.stopPrank();

        // Verify the final state
        uint256 ethBalanceAfter = marginAccount.getBalance(address(vault), address(eth));
        uint256 usdcBalanceAfter = marginAccount.getBalance(address(vault), address(usdc));

        (,, uint96 partialBidAfter, uint256 vaultBestAskAfter, uint96 partialAskAfter,
         uint96 vaultBidOrderSizeAfter, uint96 vaultAskOrderSizeAfter,) = orderBook.getVaultParams();

        emit LogUint256("ETH balance after deposit", ethBalanceAfter);
        emit LogUint256("USDC balance after deposit", usdcBalanceAfter);
        emit LogUint256("Ask order size after deposit", vaultAskOrderSizeAfter);
        emit LogUint256("Bid order size after deposit", vaultBidOrderSizeAfter);

        // Verify the vulnerability conditions
        bool orderSizesIncreased = (vaultAskOrderSizeAfter > vaultAskOrderSizeBefore) &&
                                  (vaultBidOrderSizeAfter > vaultBidOrderSizeBefore);
        bool fundsIncreased = (ethBalanceAfter > ethBalanceBefore) &&
                             (usdcBalanceAfter > usdcBalanceBefore);

        // The exact amounts should match the deposit
        uint256 expectedEthIncrease = newBaseDeposit;
        uint256 expectedUsdcIncrease = newQuoteDeposit;

        assertEq(ethBalanceAfter - ethBalanceBefore, expectedEthIncrease, "ETH balance increase should match deposit");
        assertEq(usdcBalanceAfter - usdcBalanceBefore, expectedUsdcIncrease, "USDC balance increase should match deposit");
        assertEq(orderSizesIncreased, true, "Order sizes should have increased");
        assertEq(fundsIncreased, true, "Vault funds should have increased");

        emit LogString("=== INSOLVENCY WINDOW CONFIRMED ===");
        emit LogString("During deposit(), updateVaultOrdSz() increases reported liquidity");
        emit LogString("BEFORE _depositAmountsToMarginAccount() actually provides the funds");
        emit LogString("This creates a critical window where vault appears more liquid than it is");

        // Calculate the theoretical exposure during the vulnerability window
        uint256 orderSizeIncrease = vaultAskOrderSizeAfter - vaultAskOrderSizeBefore;
        uint256 theoreticalExposure = (orderSizeIncrease * 10 ** eth.decimals()) / SIZE_PRECISION;

        emit LogUint256("Order size increase", orderSizeIncrease);
        emit LogUint256("Theoretical exposure during window", theoreticalExposure);

        assertGt(theoreticalExposure, 0, "There should be theoretical exposure during the vulnerability window");

        emit LogString("VULNERABILITY CONFIRMED: External call ordering creates insolvency window");
    }

    /**
     * @dev Test Case 3: Demonstrate the exact code path issue from Issue.md
     * This test specifically verifies the claim that deposit() calls market.updateVaultOrdSz(...)
     * before actually funding the marginAccount via _depositAmountsToMarginAccount(...)
     */
    function test_SpecificCodePathIssue() public {
        emit LogString("=== TESTING SPECIFIC CODE PATH ISSUE ===");

        // Setup minimal vault state for clear demonstration
        address depositor = genAddress();
        uint256 baseAmount = 1 ether;
        uint256 quoteAmount = 2000 * 10 ** usdc.decimals(); // $2000 USDC

        eth.mint(depositor, baseAmount);
        usdc.mint(depositor, quoteAmount);

        vm.startPrank(depositor);
        eth.approve(address(vault), baseAmount);
        usdc.approve(address(vault), quoteAmount);

        // Record the exact state before deposit to verify the sequence
        uint256 vaultEthBalanceBefore = marginAccount.getBalance(address(vault), address(eth));
        uint256 vaultUsdcBalanceBefore = marginAccount.getBalance(address(vault), address(usdc));

        emit LogUint256("Vault ETH balance before deposit", vaultEthBalanceBefore);
        emit LogUint256("Vault USDC balance before deposit", vaultUsdcBalanceBefore);

        // The issue: In KuruAMMVault._mintAndDeposit():
        // Line 201-207: market.updateVaultOrdSz(...) is called
        // Line 210: _depositAmountsToMarginAccount(...) is called AFTER
        //
        // This means the market is notified of new order sizes before the vault actually has the funds

        // Execute deposit - this will trigger the problematic sequence
        uint256 shares = vault.deposit(baseAmount, quoteAmount, depositor);

        vm.stopPrank();

        // Verify the final state
        uint256 vaultEthBalanceAfter = marginAccount.getBalance(address(vault), address(eth));
        uint256 vaultUsdcBalanceAfter = marginAccount.getBalance(address(vault), address(usdc));

        emit LogUint256("Vault ETH balance after deposit", vaultEthBalanceAfter);
        emit LogUint256("Vault USDC balance after deposit", vaultUsdcBalanceAfter);

        // Verify that funds were actually deposited
        assertEq(vaultEthBalanceAfter - vaultEthBalanceBefore, baseAmount, "ETH should be deposited");
        assertEq(vaultUsdcBalanceAfter - vaultUsdcBalanceBefore, quoteAmount, "USDC should be deposited");
        assertGt(shares, 0, "Shares should be minted");

        // Get the vault order parameters to confirm they were updated
        (,, uint96 partialBidSize, uint256 vaultBestAsk, uint96 partialAskSize,
         uint96 vaultBidOrderSize, uint96 vaultAskOrderSize,) = orderBook.getVaultParams();

        emit LogUint256("Final vault ask order size", vaultAskOrderSize);
        emit LogUint256("Final vault bid order size", vaultBidOrderSize);

        // The vulnerability is confirmed by the fact that:
        // 1. updateVaultOrdSz was called (evidenced by non-zero order sizes)
        // 2. Funds were eventually deposited (evidenced by balance changes)
        // 3. But the sequence is wrong: update happens before funding

        assertGt(vaultAskOrderSize, 0, "Ask order size should be set");
        assertGt(vaultBidOrderSize, 0, "Bid order size should be set");

        emit LogString("=== CODE PATH ISSUE CONFIRMED ===");
        emit LogString("The deposit() function calls updateVaultOrdSz() BEFORE _depositAmountsToMarginAccount()");
        emit LogString("This creates a window where market believes vault has liquidity it doesn't yet possess");
        emit LogString("Fix: Move _depositAmountsToMarginAccount() before updateVaultOrdSz()");

        // Additional verification: The issue is in the _mintAndDeposit function
        // Lines 201-207: market.updateVaultOrdSz() call
        // Line 210: _depositAmountsToMarginAccount() call
        // This ordering is backwards and creates the vulnerability window

        emit LogString("SPECIFIC BUG CONFIRMED: External call ordering vulnerability in deposit() function");
    }
}
