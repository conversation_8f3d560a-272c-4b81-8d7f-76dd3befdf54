// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {RouterErrors, OrderBookErrors} from "../contracts/libraries/Errors.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

/**
 * @title FeeValidationBugPOC
 * @dev Proof of Concept test to verify the alleged bug in Issue.md regarding fee validation
 * 
 * Bug Claims:
 * 1. Router.deployProxy() doesn't validate makerFeeBps < takerFeeBps despite NatSpec saying it should
 * 2. This could lead to arithmetic underflow in OrderBook fee calculations
 * 3. Economic incentives could be broken when makerFeeBps >= takerFeeBps
 */
contract FeeValidationBugPOC is Test {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;

    Router router;
    MarginAccount marginAccount;
    MintableERC20 tokenA;
    MintableERC20 tokenB;
    address trustedForwarder;

    function setUp() public {
        // Deploy test tokens
        tokenA = new MintableERC20("TokenA", "TKA");
        tokenB = new MintableERC20("TokenB", "TKB");

        // Deploy Router
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));

        // Deploy MarginAccount
        trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);

        // Deploy OrderBook and KuruAMMVault implementations
        OrderBook implementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        
        // Initialize Router
        router.initialize(
            address(this), 
            address(marginAccount), 
            address(implementation), 
            address(kuruAmmVaultImplementation), 
            trustedForwarder
        );
    }

    /**
     * @dev Test Case 1: Verify Router allows makerFeeBps == takerFeeBps (violates NatSpec)
     * Expected: Router should allow this (bug confirmed)
     * OrderBook should allow this (allows <= instead of strict <)
     */
    function test_RouterAllowsEqualFees() public {
        uint256 makerFeeBps = 100; // 1%
        uint256 takerFeeBps = 100; // 1% (equal to maker fee)

        // This should succeed in Router (demonstrating the bug)
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        // Verify market was deployed successfully
        assertTrue(deployedMarket != address(0), "Market should be deployed despite equal fees");
        
        // Verify the market is registered in verifiedMarket
        (uint32 pricePrecision,,,,,,,,,,) = router.verifiedMarket(deployedMarket);
        assertEq(pricePrecision, PRICE_PRECISION, "Market should be verified");
        
        // Verify OrderBook was initialized with equal fees (OrderBook allows <=)
        OrderBook orderBook = OrderBook(deployedMarket);
        (,,,,,,,,,uint256 storedTakerFee, uint256 storedMakerFee) = orderBook.getMarketParams();
        assertEq(storedTakerFee, takerFeeBps, "Taker fee should be stored correctly");
        assertEq(storedMakerFee, makerFeeBps, "Maker fee should be stored correctly");
    }

    /**
     * @dev Test Case 2: Verify Router allows makerFeeBps > takerFeeBps (violates NatSpec)
     * Expected: Router should allow this (bug confirmed)
     * OrderBook should reject this (validates makerFeeBps <= takerFeeBps)
     */
    function test_RouterAllowsButOrderBookRejectsMakerGreaterThanTaker() public {
        uint256 makerFeeBps = 200; // 2%
        uint256 takerFeeBps = 100; // 1% (less than maker fee)

        // Router should allow this call to proceed (demonstrating the Router bug)
        // But OrderBook.initialize should revert
        vm.expectRevert(OrderBookErrors.MarketFeeError.selector);
        router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );
    }

    /**
     * @dev Test Case 3: Verify proper fee validation (makerFeeBps < takerFeeBps)
     * Expected: Both Router and OrderBook should allow this
     */
    function test_ProperFeeValidationWorks() public {
        uint256 makerFeeBps = 50;  // 0.5%
        uint256 takerFeeBps = 100; // 1% (greater than maker fee)

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        // Verify market was deployed successfully
        assertTrue(deployedMarket != address(0), "Market should be deployed with proper fees");
        
        // Verify fees are stored correctly
        OrderBook orderBook = OrderBook(deployedMarket);
        (,,,,,,,,,uint256 storedTakerFee, uint256 storedMakerFee) = orderBook.getMarketParams();
        assertEq(storedTakerFee, takerFeeBps, "Taker fee should be stored correctly");
        assertEq(storedMakerFee, makerFeeBps, "Maker fee should be stored correctly");
        assertTrue(storedMakerFee < storedTakerFee, "Maker fee should be less than taker fee");
    }

    /**
     * @dev Test Case 4: Boundary test with zero fees
     * Expected: Should work (0 <= 0 is valid)
     */
    function test_ZeroFeesWork() public {
        uint256 makerFeeBps = 0;
        uint256 takerFeeBps = 0;

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        assertTrue(deployedMarket != address(0), "Market should be deployed with zero fees");
    }

    /**
     * @dev Test Case 5: Test maximum valid fees (just under 100%)
     * Expected: Should work if both are under BPS_MULTIPLIER (10000)
     */
    function test_MaximumValidFees() public {
        uint256 makerFeeBps = 9999;  // 99.99%
        uint256 takerFeeBps = 9999;  // 99.99%

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        assertTrue(deployedMarket != address(0), "Market should be deployed with maximum valid fees");
    }

    /**
     * @dev Test Case 6: Test fees at 100% (should fail in OrderBook)
     * Expected: OrderBook should reject takerFeeBps >= BPS_MULTIPLIER (10000)
     */
    function test_HundredPercentFeesRejected() public {
        uint256 makerFeeBps = 10000;  // 100%
        uint256 takerFeeBps = 10000;  // 100%

        vm.expectRevert(OrderBookErrors.MarketFeeError.selector);
        router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );
    }
}
