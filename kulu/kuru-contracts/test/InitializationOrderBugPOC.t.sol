// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {Router} from "../contracts/Router.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {IMarginAccount} from "../contracts/interfaces/IMarginAccount.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

/**
 * @title InitializationOrderBugPOC
 * @dev Proof of Concept test demonstrating the initialization order bug described in Issue.md
 * 
 * The bug: Router.deployProxy() calls MarginAccount.updateMarkets() BEFORE initializing the OrderBook proxy.
 * This means if updateMarkets tries to read any state from the market, it will read zeroed storage.
 */
contract InitializationOrderBugPOC is Test {
    Router router;
    OrderBook orderBookImplementation;
    KuruAMMVault kuruAmmVaultImplementation;
    MockMarginAccountWithValidation mockMarginAccount;
    MintableERC20 baseToken;
    MintableERC20 quoteToken;
    
    address constant TRUSTED_FORWARDER = address(0x123);
    uint96 constant SIZE_PRECISION = 10**10;
    uint32 constant PRICE_PRECISION = 10**2;
    uint96 constant SPREAD = 100;
    
    function setUp() public {
        // Deploy tokens
        baseToken = new MintableERC20("Base", "BASE");
        quoteToken = new MintableERC20("Quote", "QUOTE");
        
        // Deploy implementations
        orderBookImplementation = new OrderBook();
        Router routerImplementation = new Router();
        kuruAmmVaultImplementation = new KuruAMMVault();
        
        // Deploy Router proxy
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));
        
        // Deploy mock MarginAccount that validates market state during updateMarkets
        mockMarginAccount = new MockMarginAccountWithValidation();
        mockMarginAccount = MockMarginAccountWithValidation(
            payable(address(new ERC1967Proxy(address(mockMarginAccount), "")))
        );
        mockMarginAccount.initialize(address(this), address(router), address(router), TRUSTED_FORWARDER);
        
        // Initialize Router
        router.initialize(
            address(this),
            address(mockMarginAccount),
            address(orderBookImplementation),
            address(kuruAmmVaultImplementation),
            TRUSTED_FORWARDER
        );
    }
    
    /**
     * @dev Test that demonstrates the bug: updateMarkets is called before initialization
     * This test shows that if MarginAccount.updateMarkets tries to read market state,
     * it will fail because the proxy hasn't been initialized yet.
     */
    function testInitializationOrderBug_UpdateMarketsCalledBeforeInitialization() public {
        // Configure the mock to validate that the market's owner is the router
        mockMarginAccount.setValidationMode(true);
        mockMarginAccount.setExpectedOwner(address(router));
        
        // This should revert because updateMarkets is called before the OrderBook proxy is initialized
        // The proxy exists but its storage is zeroed, so getMarketParams() will return default values
        vm.expectRevert("Market not properly initialized or owner mismatch");
        
        router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(baseToken),
            address(quoteToken),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10**5, // minSize
            10**15, // maxSize
            100, // takerFeeBps
            50, // makerFeeBps
            SPREAD
        );
    }
    
    /**
     * @dev Test that shows the fix works: if we disable validation, deployment succeeds
     * This simulates the current MarginAccount implementation that doesn't validate market state
     */
    function testCurrentImplementationWorks_NoValidation() public {
        // Disable validation (simulates current MarginAccount behavior)
        mockMarginAccount.setValidationMode(false);
        
        // This should succeed because updateMarkets doesn't try to read market state
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(baseToken),
            address(quoteToken),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10**5, // minSize
            10**15, // maxSize
            100, // takerFeeBps
            50, // makerFeeBps
            SPREAD
        );
        
        assertTrue(deployedMarket != address(0), "Market should be deployed successfully");
        assertTrue(mockMarginAccount.verifiedMarket(deployedMarket), "Market should be verified");
    }
    
    /**
     * @dev Test demonstrating that reading from an uninitialized proxy returns zero values
     */
    function testUninitializedProxyReturnsZeroValues() public {
        // Deploy a proxy without initializing it
        address uninitializedProxy = Create2.deploy(
            0,
            keccak256("test"),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(orderBookImplementation, bytes("")))
        );
        
        // Try to read market parameters from the uninitialized proxy
        IOrderBook uninitializedOrderBook = IOrderBook(uninitializedProxy);
        
        (
            uint32 pricePrecision,
            uint96 sizePrecision,
            address baseAsset,
            uint256 baseAssetDecimals,
            address quoteAsset,
            uint256 quoteAssetDecimals,
            uint32 tickSize,
            uint96 minSize,
            uint96 maxSize,
            uint256 takerFeeBps,
            uint256 makerFeeBps
        ) = uninitializedOrderBook.getMarketParams();
        
        // All values should be zero/default because the proxy hasn't been initialized
        assertEq(pricePrecision, 0, "Price precision should be 0 for uninitialized proxy");
        assertEq(sizePrecision, 0, "Size precision should be 0 for uninitialized proxy");
        assertEq(baseAsset, address(0), "Base asset should be address(0) for uninitialized proxy");
        assertEq(baseAssetDecimals, 0, "Base asset decimals should be 0 for uninitialized proxy");
        assertEq(quoteAsset, address(0), "Quote asset should be address(0) for uninitialized proxy");
        assertEq(quoteAssetDecimals, 0, "Quote asset decimals should be 0 for uninitialized proxy");
        assertEq(tickSize, 0, "Tick size should be 0 for uninitialized proxy");
        assertEq(minSize, 0, "Min size should be 0 for uninitialized proxy");
        assertEq(maxSize, 0, "Max size should be 0 for uninitialized proxy");
        assertEq(takerFeeBps, 0, "Taker fee should be 0 for uninitialized proxy");
        assertEq(makerFeeBps, 0, "Maker fee should be 0 for uninitialized proxy");
    }
}

/**
 * @dev Mock MarginAccount that validates market state during updateMarkets
 * This simulates a future version of MarginAccount that might perform validation
 */
contract MockMarginAccountWithValidation {
    bool public validationEnabled;
    address public expectedOwner;
    mapping(address => bool) public verifiedMarket;
    address public routerContractAddress;
    bool public protocolPaused;
    address public owner;
    address public trustedForwarder;

    modifier onlyRouter() {
        require(msg.sender == routerContractAddress, "OnlyRouterAllowed");
        _;
    }

    modifier protocolActive() {
        require(!protocolPaused, "ProtocolPaused");
        _;
    }

    function initialize(address _owner, address _router, address _feeCollector, address _trustedForwarder) external {
        owner = _owner;
        routerContractAddress = _router;
        trustedForwarder = _trustedForwarder;
        protocolPaused = false;
    }

    function setValidationMode(bool _enabled) external {
        validationEnabled = _enabled;
    }

    function setExpectedOwner(address _expectedOwner) external {
        expectedOwner = _expectedOwner;
    }

    /**
     * @dev Mock updateMarkets that validates market state during registration
     * This simulates the scenario described in Issue.md where updateMarkets
     * tries to validate the market's configuration before registering it
     */
    function updateMarkets(address _marketAddress) external onlyRouter protocolActive {
        if (validationEnabled) {
            // Try to read market parameters to validate the market
            // This will fail if called before the market proxy is initialized
            IOrderBook market = IOrderBook(_marketAddress);

            (
                uint32 pricePrecision,
                ,
                address baseAsset,
                ,
                address quoteAsset,
                ,
                ,
                ,
                ,
                ,
            ) = market.getMarketParams();

            // Validate that the market has been properly initialized
            // An uninitialized proxy will return zero values
            require(
                pricePrecision > 0 && baseAsset != address(0) && quoteAsset != address(0),
                "Market not properly initialized or owner mismatch"
            );
        }

        // Register the market
        verifiedMarket[_marketAddress] = true;
    }
}
